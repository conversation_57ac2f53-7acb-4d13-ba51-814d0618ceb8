import{J,u as E,r as m,cp as Je,s as I,aW as ot,q as N,aX as ge,a0 as z,C as G,L as l,j as r,a as e,a6 as ve,bD as dt,O as Ie,ca as k,cq as et,c7 as tt,P as ne,d as te,cr as qe,b1 as mt,F,m as Ee,b as me,cs as Ye,ct as at,a4 as j,a5 as ae,cu as ut,T as vt,n as ft,ab as be,N as ht,G as re,bS as we,U as Nt,A as Fe,K as le,cv as st,t as X,y as nt,I as Ve,x as Be,V as _e,ak as Pe,an as Pt,ao as gt,aF as At,cw as St,v as pt,o as Xe,aR as It,aU as Et,bN as Tt,cf as Rt,bV as Ct,cg as Ot,ag as Lt,cx as Mt,ae as wt,aa as bt,b0 as Gt}from"./index-99e0aeb1.js";import{S as _t}from"./Switch-4e23059b.js";import{T as it}from"./Textarea-88ad0a75.js";import{formatNumber as kt}from"./Tiktok-16e483f1.js";import"./Slider-9dfab8ac.js";const ee=J({host:"",participants:[],viewers:0});function Dt(){var Re,Ce,Oe,Le;const a=E(me);E(R);const t=E(H),n=E(ee),[o,v]=m.useState(!1),[s,c]=m.useState(!1),[u,f]=m.useState(null),[P,T]=m.useState(""),d=m.useRef(null),i=m.useRef(null),h=m.useRef(null),[S,M]=m.useState(null),[g,L]=m.useState(null),[O,p]=m.useState(null),[w,U]=m.useState([]),[x,V]=m.useState([]),[oe,Q]=m.useState([]);m.useEffect(()=>{d.current.scrollTop=d.current.scrollHeight},[oe]),m.useEffect(()=>{const A=a.rtc?new Je({config:a.rtc}):new Je;return A.on("open",C=>M({peer:A,id:C})),A.on("call",C=>{C.on("close",()=>{V(_=>_.filter(B=>B.connectionId!==C.connectionId))}),V(_=>[..._,C])}),I("info","Created peer"),()=>{try{for(let C of x)C.close()}catch{I("error","Failed to close calls")}try{A.destroy()}catch{I("error","Failed to destroy peer")}}},[]),m.useEffect(()=>{if(!O||h!=null&&h.current||!S)return;I("info","Creating game render");let A=new ot(O);h.current=A,A.resizeByAspect(3/4);const C=window.innerWidth;C>1280&&A.setQuality(1280/C);let _;if(g||o){I("info","Host is true, creating call"),o?N("Instagram",{action:"setLive",username:t.username,verified:t.verified,id:S.id}):g&&N("Instagram",{action:"joinLive",username:g,streamId:S.id}).then(q=>{if(L(null),!q)return I("error","Failed to join live");c(!0)});const B=O.captureStream(24);_=q=>{I("info","Answering call",q),q.answer(B)},S.peer.on("call",_)}return()=>{_&&S.peer.off("call",_),h.current&&h.current.destroy(),h.current=null}},[O,S]);const fe=A=>{A.forEach(C=>{if(C.username===t.username)return I("info","Skipping self");I("info","Adding participant",C);const _=document.createElement("canvas"),B=_.captureStream(0),q=S.peer.call(C.id,B);V(Se=>[...Se,q]),q.on("close",_.remove),q.on("error",_.remove),q.on("stream",Se=>{N("Instagram",{action:"addCall",id:q.connectionId,username:C.username}),U(ke=>[...ke,{stream:Se,username:C.username}])})})},he=A=>{A.forEach(C=>{I("info","Removing participant",C),C.username===t.username&&c(!1),U(_=>_.filter(B=>B.username!==C.username))})};m.useEffect(()=>{const A=n.participants;if(!A||!S)return I("warning","No participants");const C=A.filter(B=>!w.find(q=>q.username===B.username));C.length&&fe(C),I("info","Added",C);const _=w.filter(B=>!A.find(q=>q.username===B.username));_.length&&he(_),I("info","Removed",_)},[n.participants,S]),m.useEffect(()=>{const A=t.username===(n==null?void 0:n.host);return v(A),ge.ShouldClose.set(!1),A&&U(C=>[...C,{username:n.host}]),()=>{ge.ShouldClose.set(!0)}},[]);const Ne=()=>{var A;G.PopUp.set({title:o?l("APPS.INSTAGRAM.END_LIVE_POPUP.TITLE"):l("APPS.INSTAGRAM.LEAVE_LIVE_POPUP.TITLE"),description:l("APPS.INSTAGRAM.END_LIVE_POPUP.DESCRIPTION").format({action:(A=o?l("APPS.INSTAGRAM.END_LIVE_POPUP.END"):l("APPS.INSTAGRAM.END_LIVE_POPUP.LEAVE"))==null?void 0:A.toLowerCase()}),buttons:[{title:l("APPS.INSTAGRAM.END_LIVE_POPUP.CANCEL")},{title:o?l("APPS.INSTAGRAM.END_LIVE_POPUP.END"):l("APPS.INSTAGRAM.END_LIVE_POPUP.LEAVE"),color:"red",cb:()=>{de(o)}}]})},Te=()=>{N("Instagram",{action:"sendLiveMessage",data:{user:{username:t.username,avatar:t.avatar,verified:t.verified},live:n.host,content:P}}),T("")},de=(A,C)=>{if(o)N("Instagram",{action:"endLive"},!0).then(()=>R.set("feed"));else if(s&&N("Instagram",{action:"endLive"}).then(()=>R.set("feed")),N("Instagram",{action:"stopViewing",username:n.host}),A)ge.ShouldClose.set(!0),ee.patch({ended:!0});else{if(C===!1)return;R.set("feed")}};return z("instagram:addMessage",A=>{A.live===n.host&&Q(C=>[...C,A])}),z("instagram:updateViewers",A=>{A.username===(n==null?void 0:n.host)&&ee.patch({viewers:A.viewers})}),z("instagram:setLive",A=>{ee.set(A)}),z("instagram:liveEnded",A=>{A===(n==null?void 0:n.host)&&de(!0)}),z("instagram:stopLive",()=>R.set("feed")),z("instagram:endCall",A=>{const C=x.find(_=>_.connectionId===A);C&&(C.close(),V(_=>_.filter(B=>B.connectionId!==A)))}),z("instagram:invitedLive",A=>{A===(n==null?void 0:n.host)&&G.PopUp.set({title:l("APPS.INSTAGRAM.INVITE_LIVE_POPUP.TITLE"),description:l("APPS.INSTAGRAM.INVITE_LIVE_POPUP.DESCRIPTION").format({username:A}),buttons:[{title:l("APPS.INSTAGRAM.INVITE_LIVE_POPUP.NO")},{title:l("APPS.INSTAGRAM.INVITE_LIVE_POPUP.YES"),cb:()=>{L(A),U(C=>[...C,{username:t.username}])}}]})}),z("instagram:joinedLive",A=>{A.host===(n==null?void 0:n.host)&&ee.patch({participants:[...n.participants,A]})}),z("instagram:leftLive",A=>{A.host===(n==null?void 0:n.host)&&(I("info","left live",A),ee.patch({participants:n.participants.filter(C=>C.username!==A.participant)}))}),r("div",{className:"instagram-live-container",children:[e("div",{className:"instagram-streams","data-streams":w.length.toString(),children:w.map((A,C)=>A.username===t.username?r("div",{className:"instagram-stream",children:[e("canvas",{ref:_=>{I("info","setting canvas ref",_),p(_)}}),!o&&e(ve,{onClick:()=>{G.PopUp.set({title:l("APPS.INSTAGRAM.LEAVE_LIVE_POPUP.TITLE"),description:l("APPS.INSTAGRAM.LEAVE_LIVE_POPUP.DESCRIPTION"),buttons:[{title:l("APPS.INSTAGRAM.REMOVE_LIVE_POPUP.CANCEL")},{title:l("APPS.INSTAGRAM.LEAVE_LIVE_POPUP.LEAVE"),color:"red",cb:()=>{N("Instagram",{action:"endLive"}),c(!1)}}]})}})]}):e(Ut,{host:o,stream:A.stream,username:A.username},`stream-${C}`))}),n.ended&&r("div",{className:"instagram-stream-ended",children:[e(dt,{onClick:()=>R.set("feed")}),e("div",{className:"title",children:l("APPS.INSTAGRAM.LIVE_ENDED")})]}),e(Ie,{children:u&&e(Ft,{username:n.host,type:u,host:u==="participants"?o||s:o,endLive:de,close:()=>f(null)})}),r("div",{className:"instagram-live",children:[r("div",{className:"live-header",children:[r("div",{className:"profile",onClick:()=>{w.length>1?f("participants"):!o&&!s&&(de(),y(n.host))},children:[e("div",{className:"profile-picture",children:e(k,{className:"avatar",avatar:(Ce=(Re=n==null?void 0:n.participants)==null?void 0:Re[0])==null?void 0:Ce.avatar})}),r("div",{className:"name",children:[e(yt,{usernames:n.participants.map(A=>A.username)}),((Le=(Oe=n==null?void 0:n.participants)==null?void 0:Oe[0])==null?void 0:Le.verified)&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})}),e(et,{})]})]}),r("div",{className:"stats",children:[e("div",{className:"live",children:l("APPS.INSTAGRAM.LIVE")}),r("div",{className:"viewers",onClick:()=>f("viewers"),children:[e(tt,{}),n.viewers]}),e(ve,{className:"x",onClick:()=>{o||s?Ne():de(!1)}})]})]}),r("div",{className:"comment-section",children:[e("div",{className:"comment-feed",ref:d,children:oe.map((A,C)=>{const _=A.user.avatar??`./assets/img/avatar-placeholder-${ne.Settings.value.display.theme}.svg`;return r("div",{className:"comment",children:[e(k,{avatar:_}),r("div",{className:"info",children:[r("div",{className:"name",children:[A.user.username,A.user.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]}),e("div",{className:"comment-text",children:A.content})]})]},C)})}),r("div",{className:"comment-input",children:[e(te,{type:"text",ref:i,placeholder:l("APPS.INSTAGRAM.COMMENT_PLACEHOLDER"),onChange:A=>T(A.target.value),onKeyDown:A=>{A.key==="Enter"&&P.replace(/ /g,"").length>0&&(Te(),i.current.value="")}}),e(qe,{onClick:()=>{P.replace(/ /g,"").length>0&&(Te(),i.current.value="")}}),(o||s)&&e(mt,{onClick:()=>N("Instagram",{action:"flipCamera"})})]})]})]})]})}const Ut=a=>{const t=m.useRef(null);return m.useEffect(()=>{if(!(t!=null&&t.current))return I("warning","No video ref");t.current.srcObject=a.stream,t.current.play().catch(()=>I("error","Failed to play video stream"))},[a==null?void 0:a.stream]),r("div",{className:"instagram-stream",children:[a.host&&e(ve,{onClick:()=>{G.PopUp.set({title:l("APPS.INSTAGRAM.REMOVE_LIVE_POPUP.TITLE").format({username:a.username}),description:l("APPS.INSTAGRAM.REMOVE_LIVE_POPUP.DESCRIPTION").format({username:a.username}),buttons:[{title:l("APPS.INSTAGRAM.REMOVE_LIVE_POPUP.CANCEL")},{title:l("APPS.INSTAGRAM.REMOVE_LIVE_POPUP.REMOVE"),color:"red",cb:()=>{N("Instagram",{action:"removeLive",username:a.username}).then(()=>{})}}]})}}),e("video",{autoPlay:!0,ref:t})]})},yt=a=>{let t=a.usernames.map((n,o)=>n).join(", ");return t.length>9&&(t=t.substring(0,9)+"..",t[t.length-3]===","&&(t=t.substring(0,t.length-3)+"..")),e(F,{children:t})},Ft=a=>{const[t,n]=m.useState([]);return m.useEffect(()=>{var o;if(a.type==="participants"){n((o=ee.value)==null?void 0:o.participants);return}N("Instagram",{action:"getLiveViewers",username:a.username}).then(v=>{v&&n(v)})},[]),r(Ee.div,{className:"story-modal","data-type":"live",initial:{y:600},animate:{y:0},exit:{y:600},transition:{duration:.35,ease:"easeInOut"},children:[r("div",{className:"story-modal-header",children:[e("div",{className:"close-border",onClick:()=>a.close()}),e("div",{className:"title",children:a.type==="viewers"?l("APPS.INSTAGRAM.WHOS_WATCHING"):l("APPS.INSTAGRAM.IN_THIS_ROOM")})]}),e("div",{className:"story-modal-content",children:e("div",{className:"viewers",children:t.map((o,v)=>e(Vt,{type:a.type,user:o,host:a.host,endLive:a.endLive},v))})})]})},Vt=({user:a,host:t,type:n,endLive:o})=>{var u;const v=(u=E(ee))==null?void 0:u.participants,[s,c]=m.useState(!1);return r("div",{className:"item",children:[r("div",{className:"user",onClick:()=>{t||(o(!1,!1),y(a.username))},children:[e("div",{className:"profile-picture",children:e(k,{className:"avatar",avatar:a.avatar})}),r("div",{className:"user-details",children:[r("div",{className:"username",children:[a.username,a.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]}),e("div",{className:"name",children:a.name})]})]}),t&&n==="viewers"&&(v.find(f=>f.username===a.username)?e("div",{className:"remove",onClick:f=>{f.stopPropagation(),G.PopUp.set({title:l("APPS.INSTAGRAM.REMOVE_LIVE_POPUP.TITLE").format({username:a.username}),description:l("APPS.INSTAGRAM.REMOVE_LIVE_POPUP.DESCRIPTION").format({username:a.username}),buttons:[{title:l("APPS.INSTAGRAM.REMOVE_LIVE_POPUP.CANCEL")},{title:l("APPS.INSTAGRAM.REMOVE_LIVE_POPUP.REMOVE"),color:"red",cb:()=>{N("Instagram",{action:"removeLive",username:a.username}).then(()=>{})}}]})},children:l("APPS.INSTAGRAM.REMOVE")}):e("div",{className:"invite",onClick:f=>{f.stopPropagation(),!s&&N("Instagram",{action:"inviteLive",username:a.username},()=>{c(!0)})},children:s?l("APPS.INSTAGRAM.INVITED"):l("APPS.INSTAGRAM.INVITE")}))]})};function rt(a){const t=E(ne.Settings);return e(F,{children:a.liked?e(Ye,{onClick:a.onClick,color:"#ed4956"}):e(at,{onClick:a.onClick,color:t.display.theme==="dark"?"#ffffff":"#262626"})})}function ct(a){const t=E(H);E(R),E(ce);const[n,o]=m.useState(a.data),[v,s]=m.useState(!1),c=m.useRef(null),[u,f]=m.useState(0),[P,T]=m.useState(!1),d=E(Nt),i=E(Fe),h={pos:{startLeft:0,startX:0},onMouseDown:g=>{re("Instagram")&&(h.pos={startLeft:c.current.scrollLeft,startX:g.clientX},c.current.style.userSelect="none",document.addEventListener("mouseup",h.onMouseUp),document.addEventListener("mousemove",h.onMove))},onMove:g=>{const L=(g.clientX-h.pos.startX)/we();c.current.scrollLeft=h.pos.startLeft-L;const O=c.current.getBoundingClientRect();(O.left*we()-5>g.clientX||g.clientX>O.right*we()-5)&&h.onMouseUp()},onMouseUp:()=>{c.current.style.removeProperty("user-select"),document.removeEventListener("mouseup",h.onMouseUp),document.removeEventListener("mousemove",h.onMove);const g=JSON.parse(n.media),L=c.current.clientWidth;let O=u;const p=c.current.scrollLeft-h.pos.startLeft;p>L/2&&O<g.length-1?O++:p<-L/2&&O>0&&O--,S(O)}},S=g=>{c.current.scrollTo({left:g*c.current.offsetWidth,behavior:"smooth"}),f(g)};z("instagram:updatePostData",g=>{if(!g||g.postId!==n.id)return I("warning","instagram:updatePostData","Post ID's do not match");o(L=>({...L,[g.data]:g.increment?L[g.data]+1:L[g.data]-1}))});const M=()=>{G.ContextMenu.set({buttons:[{title:l("APPS.INSTAGRAM.SHARE_TO_STORY.TITLE"),cb:()=>{var g;N("Instagram",{action:"addToStory",media:(g=JSON.parse(n.media))==null?void 0:g[u],metadata:{post:{id:n.id,username:n.username}}},Math.random().toString(36).substring(7)).then(L=>{if(!L)return I("error","Failed to add story");se.set([{username:t.username,avatar:t.avatar,verified:t.verified,seen:0,id:L},...se.value.filter(O=>O.username!==t.username)]),G.PopUp.set({title:l("APPS.INSTAGRAM.SHARE_TO_STORY.SUCCESS"),description:l("APPS.INSTAGRAM.SHARE_TO_STORY.DESCRIPTION"),buttons:[{title:l("APPS.INSTAGRAM.OK")}]})})}}]})};return r("div",{className:"post",id:a.last?"last":"","data-id":n.id,"data-active":a.active,children:[r("div",{className:"post-header",children:[e(k,{avatar:n.avatar,className:"profile-picture",onClick:()=>y(n.username)}),r("div",{className:"post-info",children:[r("div",{className:"name",onClick:()=>y(n.username),children:[n.username,n.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]}),n.location&&e("div",{className:"location",children:n.location})]})]}),r("div",{className:"post-body",children:[P&&e("div",{className:"popup-heart","data-visible":P,children:e(Ye,{})}),e("div",{className:"image-container",ref:c,onMouseDown:h.onMouseDown,children:JSON.parse(n.media).map((g,L)=>{var O;return e("div",{className:"image",onClick:()=>s(!0),children:j(g)?e(xt,{attachment:g,active:a.active&&!(d!=null&&d.visible)&&((O=i==null?void 0:i.active)==null?void 0:O.name)==="Instagram"}):e(ae,{src:g,blur:!v&&(n==null?void 0:n.username)!==(t==null?void 0:t.username)})},L)})}),r("div",{className:"actions",children:[r("div",{className:"like-comment",children:[e(rt,{liked:n.liked,onClick:()=>{n.id&&N("Instagram",{action:"toggleLike",data:{postId:n.id,toggle:!n.liked,isComment:!1}},!n.liked).then(g=>{o(L=>({...L,liked:g}))})}}),e(ut,{onClick:()=>$e({id:n.id,username:n.username,avatar:n.avatar,verified:n.verified,caption:n.caption,timestamp:n.timestamp})}),(n.username===t.username||t.isAdmin)&&e(vt,{onClick:()=>{G.PopUp.set({title:l("APPS.INSTAGRAM.DELETE_POST_POPUP.TITLE"),description:l("APPS.INSTAGRAM.DELETE_POST_POPUP.DESCRIPTION"),buttons:[{title:l("APPS.INSTAGRAM.DELETE_POST_POPUP.CANCEL")},{title:l("APPS.INSTAGRAM.DELETE_POST_POPUP.PROCEED"),color:"red",cb:()=>{N("Instagram",{action:"deletePost",id:n.id},!0).then(g=>{if(!g)return I("error","Instagram","Failed to delete post");R.set("feed")})}}]})}})]}),JSON.parse(n.media).length>1&&e("div",{className:"scroll-dots",children:JSON.parse(n.media).map((g,L)=>e("div",{className:`dot ${u==L?"active":""}`,onClick:()=>S(L)},L))}),e("div",{className:"like-comment",children:e(ft,{onClick:M})})]}),r("div",{className:"liked-by",onClick:()=>{ce.set({title:l("APPS.INSTAGRAM.LIKED_BY"),postId:n.id})},children:[n.like_count," ",l("APPS.INSTAGRAM.LIKES")]}),r("div",{className:"caption",children:[e("span",{className:"user",onClick:g=>{g.stopPropagation(),y(n.username)},children:n.username}),be(n.caption)]}),e("div",{className:"comments",onClick:g=>{g.stopPropagation(),$e({id:n.id,username:n.username,avatar:n.avatar,verified:n.verified,caption:n.caption,timestamp:n.timestamp})},children:n.comment_count>0?l("APPS.INSTAGRAM.VIEW_ALL_COMMENTS").format({count:n.comment_count}):l("APPS.INSTAGRAM.FIRST_COMMENT")}),e("div",{className:"date",children:ht(n.timestamp)})]})]})}const xt=a=>{var s;const[t,n]=m.useState(a.active),o=E(ne.Settings),v=m.useRef(null);return m.useEffect(()=>{var c,u,f,P;n(a.active),!(!v.current||((c=o==null?void 0:o.sound)==null?void 0:c.volume)===void 0)&&a.active&&(v.current.volume=((u=o==null?void 0:o.sound)==null?void 0:u.volume)!==void 0?o.sound.volume:.5,v.current.currentTime=0,a.active?(f=v.current)==null||f.play().catch(T=>{}):(P=v.current)==null||P.pause().catch(T=>{}))},[a.active]),m.useEffect(()=>{var c,u;!v.current||((c=o==null?void 0:o.sound)==null?void 0:c.volume)===void 0||a.active&&(v.current.volume=((u=o==null?void 0:o.sound)==null?void 0:u.volume)!==void 0?o.sound.volume:.5)},[(s=o==null?void 0:o.sound)==null?void 0:s.volume]),e("video",{ref:v,src:a.attachment,autoPlay:!0,loop:!0,muted:!a.active,onLoadedMetadata:c=>{var u;a.active&&(c.currentTarget.volume=((u=o==null?void 0:o.sound)==null?void 0:u.volume)!==void 0?o.sound.volume:.5)}})},W={account:{username:"lb",name:"LB",avatar:"https://docs.lbscripts.com/images/icons/icon.png",bio:`Official LB account
https://lbphone.com`,verified:!0},accounts:{lb:{username:"lb",name:"LB",avatar:"https://docs.lbscripts.com/images/icons/icon.png",bio:`Official LB account
https://lbphone.com`,verified:!0,following:2,followers:13,posts:2,followingList:["loaf","breze"],followerList:["loaf","breze"]},breze:{username:"breze",name:"Breze",bio:`Hey, its breze 
https://lbphone.com`,verified:!0,following:2,followers:1,posts:2,followingList:["lb","loaf"],followerList:["lb","loaf"]},loaf:{username:"loaf",name:"Loaf Scripts",bio:`Loaf Scritps official account 
https://lbphone.com`,verified:!0,following:2,followers:1,posts:2,followingList:["lb","breze"],followerList:["lb","breze"]}},posts:[{id:"1",username:"breze",name:"Breze",verified:!0,caption:"sunsets are the best",media:'["https://r2.fivemanage.com/images/GRBUI6C4orbD.webp", "https://r2.fivemanage.com/images/rafgjPwSYnn2.png"]',location:"Sandy Shores",like_count:2,comment_count:0,timestamp:Date.now()-1e3*60*60*24*1},{id:"2",username:"lb",name:"LB",verified:!0,caption:"dump",media:'["https://r2.fivemanage.com/images/upTPndukc2PD.webp", "https://r2.fivemanage.com/images/X4pfDFiSFvhi.webp"]',location:"Los Santos",like_count:8,comment_count:2,timestamp:Date.now()-1e3*60*60*24*2}],stories:[{id:"1",username:"lb",avatar:"https://docs.lbscripts.com/images/icons/icon.png",verified:!0,seen:0,media:[{id:"2",image:"https://r2.fivemanage.com/images/oRKcdeTA8oBy.png",timestamp:Date.now()-1e3*60*60*4,seen:!1,views:2,viewers:[{username:"Breze"},{username:"Loaf"}]}]},{id:"2",username:"breze",verified:!0,seen:0,media:[{id:"1",metadata:{post:{id:132,src:"https://townsquare.media/site/812/files/2024/03/attachment-future-metro-boomin-we-dont-trust-you-photo.jpg?w=1080&q=75",username:"tordinationen"}},timestamp:Date.now()-1e3*60*60*2,seen:!1},{id:"2",image:"https://r2.fivemanage.com/images/ZsmnyJOA2rc5.png",timestamp:Date.now()-1e3*60*60*3,seen:!1},{id:"3",image:"https://r2.fivemanage.com/video/REZeHruJivNL.mp4",timestamp:Date.now()-1e3*60*60*4,seen:!1}]},{id:"3",username:"loaf",verified:!0,seen:1,media:[{id:"1",image:"https://r2.fivemanage.com/images/X4pfDFiSFvhi.webp",timestamp:Date.now()-1e3*60*60*3,seen:!0},{id:"2",image:"https://r2.fivemanage.com/images/upTPndukc2PD.webp",timestamp:Date.now()-1e3*60*60*4,seen:!0}]}],comments:[{postId:"2",user:{username:"breze",verified:!0},comment:{id:"1",content:"nice",timestamp:Date.now()-1e3*60*60*24,likes:0}},{postId:"2",user:{username:"loaf",verified:!0},comment:{id:"2",content:"haha, nice picture",timestamp:Date.now()-1e3*60*60*22,likes:1,liked:!0}}],messages:[{id:"1",username:"breze",name:"Breze",verified:!0,content:"hey",messages:[{id:"1",sender:"breze",content:"hey",timestamp:Date.now()-1e3*60*60*4}],timestamp:Date.now()-1e3*60*60*4},{id:"2",username:"loaf",name:"Loaf Scripts",verified:!1,content:"yoo, your last post was fire",messages:[{id:"3",sender:"loaf",content:"yoo, your last post was fire",timestamp:Date.now()-1e3*60*60*8}],timestamp:Date.now()-1e3*60*60*8}],notifications:[{type:"like_photo",username:"loaf",name:"Loaf Scripts",postId:"1",photo:"https://r2.fivemanage.com/images/upTPndukc2PD.webp",timestamp:Date.now()-1e3*60*60*2},{type:"follow",username:"loaf",name:"Loaf Scripts",isFollowing:!0,timestamp:Date.now()-1e3*60*60*24*3}]};function Ht(){const a=E(R),t=E(H),n=E(se),[o,v]=m.useState([]),[s,c]=m.useState([]),[u,f]=m.useState(null);m.useEffect(()=>{re("Instagram")&&N("Instagram",{action:"getPosts",filters:{following:!0},page:0},W.posts).then(i=>{if(!i)return I("error","Failed to fetch posts");v(i)})},[]),m.useEffect(()=>{re("Instagram")&&a==="feed"&&(N("Instagram",{action:"getLives"}).then(i=>{i&&c(i)}),N("Instagram",{action:"getStories"},W.stories).then(i=>{if(!i)return I("warning","Failed to fetch stories");se.set([...i.sort((h,S)=>{if(h.seen===S.seen)return 0;if(h.seen===1)return 1;if(S.seen===1)return-1})])}))},[a]),new IntersectionObserver(P,{rootMargin:"20px",threshold:.5});function P(i){i.forEach(h=>{if(h.isIntersecting){let M=h.target.getAttribute("data-id");f(M)}})}const{handleScroll:T}=le({fetchData:i=>N("Instagram",{action:"getPosts",filters:{following:!0},page:i}),onDataFetched:i=>v([...o,...i]),perPage:25});m.useEffect(()=>{const i=document.querySelector(".stories");return i==null||i.addEventListener("wheel",h=>{h.preventDefault(),i.scrollLeft+=h.deltaY}),()=>{i==null||i.removeEventListener("wheel",h=>{h.preventDefault(),i.scrollLeft+=h.deltaY})}},[]),z("instagram:updateLives",i=>{i&&c(i)});const d=()=>{G.PopUp.set({title:l("APPS.INSTAGRAM.GO_LIVE_POPUP.TITLE"),description:l("APPS.INSTAGRAM.GO_LIVE_POPUP.DESCRIPTION"),buttons:[{title:l("APPS.INSTAGRAM.GO_LIVE_POPUP.CANCEL")},{title:l("APPS.INSTAGRAM.GO_LIVE_POPUP.PROCEED"),color:"red",cb:()=>{N("Instagram",{action:"goLive",user:{username:t.username,avatar:t==null?void 0:t.avatar,verified:t.verified}},!0).then(i=>{if(!i)return I("error","Failed to go live");ee.set({host:t.username,participants:[{username:t.username,name:t.username,avatar:t==null?void 0:t.avatar,verified:t.verified}],viewers:0}),R.set("live")})}}]})};return r(F,{children:[r("div",{className:"instagram-header feed-header",children:[r("div",{className:"name",children:[t==null?void 0:t.username,(t==null?void 0:t.verified)&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]}),e(qe,{onClick:()=>R.set("dmList")})]}),r("div",{className:"feed",onScroll:T,children:[e("div",{className:"stories-wrapper",children:r("div",{className:"stories",children:[r("div",{className:"story self",onClick:()=>{G.ContextMenu.set({buttons:[{title:l("APPS.INSTAGRAM.GO_LIVE"),cb:()=>d()},{title:l("APPS.INSTAGRAM.CREATE_STORY"),cb:()=>{var i,h,S;G.Gallery.set({includeVideos:!0,allowExternal:(S=(h=(i=me)==null?void 0:i.value)==null?void 0:h.AllowExternal)==null?void 0:S.InstaPic,onSelect:M=>{let g=Array.isArray(M)?M[0].src:M.src;N("Instagram",{action:"addToStory",media:g},Math.random().toString(36).substring(7)).then(L=>{if(!L)return I("error","Failed to add story");se.set([{username:t.username,avatar:t.avatar,verified:t.verified,seen:0,id:L},...n.filter(O=>O.username!==t.username)])})}})}}]})},children:[r("div",{className:"profile-picture",children:[e(k,{className:"avatar",avatar:t==null?void 0:t.avatar}),e("div",{className:"plus",children:e("i",{className:"far fa-plus"})})]}),e("div",{className:"name",children:l("APPS.INSTAGRAM.YOUR_STORY")})]}),Object.keys(s).map(i=>{var h;return r("div",{className:"story",onClick:()=>{var S;N("Instagram",{action:"viewLive",username:((S=s[i])==null?void 0:S.participant)??i}).then(M=>{var O,p,w,U;if(!M)return I("error","Failed to view live, perhaps it ended?");let g=(O=s[i])!=null&&O.participant?s[(p=s[i])==null?void 0:p.participant]:s[i],L=(g==null?void 0:g.participants)??[];L.unshift({username:((w=s[i])==null?void 0:w.participant)??i,name:g.name,avatar:g.avatar,verified:g.verified,id:g.id}),ee.set({host:((U=s[i])==null?void 0:U.participant)??i,participants:L,viewers:M}),R.set("live")})},children:[r("div",{className:"profile-picture",children:[e(k,{className:"avatar",avatar:(h=s[i])==null?void 0:h.avatar}),e("img",{className:"circle live",src:"./assets/img/icons/instagram/LiveCircle.svg"})]}),e("div",{className:"name",children:i})]},i)}),n==null?void 0:n.map(i=>r("div",{className:"story",onClick:h=>{let[S,M]=st(h);ye(i,null,{x:S,y:M})},children:[r("div",{className:"profile-picture",children:[e(k,{className:"avatar",avatar:i.avatar}),e("img",{className:"circle","data-seen":i.seen===1,src:"./assets/img/icons/instagram/StoryCircle.svg"})]}),e("div",{className:"name",children:i.username})]},i.username))]})}),(o==null?void 0:o.length)===0&&e("div",{className:"no-posts",children:l("APPS.INSTAGRAM.EMPTY_FEED")}),o==null?void 0:o.map(i=>e(ct,{data:i,active:u===i.id},i.id))]})]})}function Qe(){const a=E(ne.Settings),[t,n]=m.useState(""),[o,v]=m.useState(""),[s,c]=m.useState(""),[u,f]=m.useState("login"),P=()=>{N("Instagram",{action:"logIn",username:o,password:s},{success:!!W.accounts[o],account:W.accounts[o]}).then(d=>{if(d.success&&d.account)N("isAdmin",null,!1).then(i=>{X.APPS.INSTAGRAM.account.set(d.account),H.set({...d.account,isAdmin:i}),R.set("feed"),ie.set(!0)});else{if(d.error)return G.PopUp.set({title:l("APPS.INSTAGRAM.ERROR"),description:l(`APPS.INSTAGRAM.${d.error}`),buttons:[{title:l("APPS.INSTAGRAM.OK")}]});I("error","Instagram LogIn Error",d.error)}})},T=()=>{let d=o==null?void 0:o.toLowerCase().replace(/\s/g,""),i=d.match(me.value.UsernameFilter);if(!i||(i==null?void 0:i[0])!==d||d.length<3||d.length>15){I("warning","Username did not match regex"),G.PopUp.set({title:l("APPS.INSTAGRAM.ERROR"),description:l("APPS.INSTAGRAM.USERNAME_NOT_ALLOWED"),buttons:[{title:l("APPS.INSTAGRAM.OK")}]});return}N("Instagram",{action:"createAccount",name:t,username:d,password:s},{success:!W.accounts[d]}).then(h=>{if(h.success)N("isAdmin").then(S=>{let M={name:t,username:d,posts:0,followers:0,following:0};X.APPS.INSTAGRAM.account.set(M),H.set({...M,isAdmin:S}),R.set("feed"),ie.set(!0)});else{if(h.error)return G.PopUp.set({title:l("APPS.INSTAGRAM.ERROR"),description:l(`APPS.INSTAGRAM.${h.error}`),buttons:[{title:l("APPS.INSTAGRAM.OK")}]});I("error","couldn't create account",h)}})};return m.useEffect(()=>{n(""),v(""),c("")},[u]),e("div",{className:"log-in",children:r(F,{children:[e("div",{className:"wrapper",children:u==="login"?r(F,{children:[e("img",{src:"./assets/img/icons/instagram/logo.png","data-theme":a.display.theme}),r("div",{className:"form",children:[e(te,{placeholder:l("APPS.INSTAGRAM.USERNAME_PLACEHOLDER"),type:"text",className:"username",onChange:d=>v(d.target.value.replace(/\s/g,"")),maxLength:15},1),e(te,{placeholder:l("APPS.INSTAGRAM.PASSWORD_PLACEHOLDER"),type:"password",onChange:d=>c(d.target.value),maxLength:50},3),e("div",{className:"button",onClick:P,children:l("APPS.INSTAGRAM.LOG_IN")})]})]}):r(F,{children:[e("img",{src:"./assets/img/icons/instagram/logo.png","data-theme":a.display.theme}),r("div",{className:"form",children:[e(te,{placeholder:l("APPS.INSTAGRAM.NAME_PLACEHOLDER"),type:"text",onChange:d=>n(d.target.value),maxLength:20},2),e(te,{placeholder:l("APPS.INSTAGRAM.USERNAME_PLACEHOLDER"),type:"text",className:"username",onChange:d=>v(d.target.value.replace(/\s/g,"")),maxLength:15},4),e(te,{placeholder:l("APPS.INSTAGRAM.PASSWORD_PLACEHOLDER"),type:"password",onChange:d=>c(d.target.value),maxLength:50}),e("div",{className:"button",onClick:T,children:l("APPS.INSTAGRAM.CREATE_ACCOUNT")})]})]})}),e("div",{className:"footer",children:u==="login"?r(F,{children:[l("APPS.INSTAGRAM.NOT_HAVE_ACCOUNT")," ",e("span",{onClick:()=>f("createAccount"),children:l("APPS.INSTAGRAM.CREATE_ACCOUNT")})]}):r(F,{children:[l("APPS.INSTAGRAM.HAVE_ACCOUNT")," ",e("span",{onClick:()=>f("login"),children:l("APPS.INSTAGRAM.LOG_IN")})]})})]})})}const He=J(!1);function Wt(){var s,c,u,f,P;const a=E(Fe),[t,n]=m.useState(null),o=E(He);m.useEffect(()=>{re("Instagram")&&N("Instagram",{action:"getNotifications"},W.notifications).then(T=>{if(!T)return I("warning","Failed to fetch notifications, or no results?");n(T)})},[a==null?void 0:a.active]);const{handleScroll:v}=le({fetchData:T=>N("Instagram",{action:"getNotifications",page:T}),onDataFetched:T=>{T.length>0&&n(d=>({notifications:[...d.notifications,...T[0].notifications]}))},perPage:25});return r("div",{className:"notifications-container",children:[e(Ie,{children:o&&e($t,{})}),e("div",{className:"notifications-header",children:l("APPS.INSTAGRAM.ACTIVITY")}),r("div",{className:"notifications-body",onScroll:v,children:[((s=t==null?void 0:t.requests)==null?void 0:s.total)>0&&r("div",{className:"follow-requests",onClick:()=>He.set(!0),children:[r("div",{className:"item-data",children:[e("div",{className:"profile-pictures","data-multiple":t.requests.total>1,children:t.requests.recent.map((T,d)=>e(k,{avatar:T.avatar},d))}),r("div",{className:"item-body",children:[e("div",{className:"title",children:l("APPS.INSTAGRAM.FOLLOW_REQUESTS")}),e("div",{className:"subtitle",children:t.requests.total>1?l("APPS.INSTAGRAM.ANDOTHERS").format({username:(c=t.requests.recent[0])==null?void 0:c.username,amount:((u=t.requests)==null?void 0:u.total)-1}):(f=t.requests.recent[0])==null?void 0:f.username})]})]}),r("div",{className:"actions",children:[e("div",{className:"blue-dot"}),e(nt,{})]})]}),(P=t==null?void 0:t.notifications)==null?void 0:P.map((T,d)=>{let i=d===t.notifications.length-1?"last":"";return e(qt,{notification:T,last:i},d)})]})]})}const $t=()=>{const[a,t]=m.useState(""),[n,o]=m.useState([]),v=[{class:"follow",label:l("APPS.INSTAGRAM.CONFIRM"),action:!0},{class:"following",label:l("APPS.INSTAGRAM.DELETE"),action:!1}];m.useEffect(()=>{N("Instagram",{action:"getFollowRequests",page:0},[]).then(c=>{if(!c)return I("warning","Failed to fetch follow requests");I("info","Fetched follow requests",c),o(c)})},[]);const{handleScroll:s}=le({fetchData:c=>N("Instagram",{action:"getFollowRequests",page:c}),onDataFetched:c=>o(c),perPage:15});return r(Ee.div,{initial:{opacity:.5,x:150},animate:{opacity:1,x:0},exit:{opacity:.5,x:150},transition:{duration:.2,ease:"easeInOut"},className:"requests-container",children:[r("div",{className:"requests-header",children:[e("div",{className:"icon",children:e(Ve,{onClick:()=>He.set(!1)})}),e("div",{className:"title",children:l("APPS.INSTAGRAM.FOLLOW_REQUESTS")}),e("span",{})]}),r("div",{className:"requests-body",children:[e(Be,{placeholder:l("SEARCH"),onChange:c=>t(c.target.value)}),e("div",{className:"items",onScroll:s,children:n.filter(c=>{var u,f;return((u=c.username)==null?void 0:u.toLowerCase().includes(a==null?void 0:a.toLowerCase()))||((f=c.name)==null?void 0:f.toLowerCase().includes(a==null?void 0:a.toLowerCase()))}).map((c,u)=>r("div",{className:"item",children:[r("div",{className:"user",onClick:()=>y(c.username),children:[e(k,{className:"avatar",avatar:c.avatar}),r("div",{className:"user-info",children:[r("div",{className:"username",children:[c.username,c.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]}),e("div",{className:"name",children:c.name})]})]}),e("div",{className:"buttons",children:v.map((f,P)=>e("div",{className:_e("button",f.class),onClick:T=>{T.stopPropagation(),N("Instagram",{action:"handleFollowRequest",username:c.username,accept:f.action},!0).then(d=>{if(!d)return I("warning","Failed to handle follow request");o(i=>i.filter(h=>h.username!==c.username))})},children:f.label},P))})]},u))})]})]})},qt=({notification:a,last:t})=>{const n=E(H),[o,v]=m.useState(a.isFollowing);switch(a.type){case"like_photo":return r("div",{className:"notification-item",id:t,onClick:()=>Ue(a.postId),children:[r("div",{className:"notification-body",children:[e(k,{className:"avatar",avatar:a.avatar,onClick:c=>{c.stopPropagation(),y(a.username)}}),r("div",{className:"content",children:[e("span",{children:a.username})," ",l("APPS.INSTAGRAM.LIKED_PHOTO"),e("span",{className:"date",children:Pe(a.timestamp)})]})]}),j(a.photo)?e("video",{className:"post-preview",src:a.photo,muted:!0,controls:!1}):e(ae,{className:"post-preview",src:a.photo})]});case"like_comment":return e("div",{className:"notification-item",id:t,onClick:()=>Ue(a.postId,!0),children:r("div",{className:"notification-body",children:[e(k,{className:"avatar",avatar:a.avatar,onClick:c=>{c.stopPropagation(),y(a.username)}}),r("div",{className:"content",children:[e("span",{children:a.username})," ",l("APPS.INSTAGRAM.LIKED_COMMENT").format({comment:a.comment}),e("span",{className:"date",children:Pe(a.timestamp)})]})]})});case"comment":return r("div",{className:"notification-item",id:t,onClick:()=>Ue(a.postId,!0),children:[r("div",{className:"notification-body",children:[e(k,{className:"avatar",avatar:a.avatar,onClick:c=>{c.stopPropagation(),y(a.username)}}),r("div",{className:"content",children:[e("span",{children:a.username})," ",l("APPS.INSTAGRAM.COMMENTED").format({comment:a.comment}),e("span",{className:"date",children:Pe(a.timestamp)})]})]}),j(a.photo)?e("video",{className:"post-preview",src:a.photo,muted:!0,controls:!1}):e("img",{className:"post-preview",src:a.photo})]});case"follow":return r("div",{className:"notification-item",id:t,onClick:()=>y(a.username),children:[r("div",{className:"notification-body",children:[e(k,{className:"avatar",avatar:a.avatar,onClick:c=>{c.stopPropagation(),y(a.username)}}),r("div",{className:"content",children:[e("span",{children:a.username})," ",l("APPS.INSTAGRAM.FOLLOWED"),e("span",{className:"date",children:Pe(a.timestamp)})]})]}),e("div",{className:_e("button",o?"following":"follow"),onClick:c=>{c.stopPropagation(),n.username!==a.username&&(o?G.PopUp.set({title:l("APPS.INSTAGRAM.UNFOLLOW_POPUP.TITLE"),description:l("APPS.INSTAGRAM.UNFOLLOW_POPUP.DESCRIPTION").format({name:a.username}),buttons:[{title:l("APPS.INSTAGRAM.UNFOLLOW_POPUP.CANCEL")},{title:l("APPS.INSTAGRAM.UNFOLLOW_POPUP.PROCEED"),color:"red",cb:()=>{N("Instagram",{action:"toggleFollow",data:{username:a.username,following:!o}},!0).then(()=>{v(u=>!u)})}}]}):N("Instagram",{action:"toggleFollow",data:{username:a.username,following:!o}},!0).then(()=>{v(u=>!u)}))},children:o?l("APPS.INSTAGRAM.FOLLOWING"):l("APPS.INSTAGRAM.FOLLOW")})]})}};function Yt(){var h,S,M,g,L,O;const a=E(H),t=E(Z),n=E(Y),[o,v]=m.useState(!1),[s,c]=m.useState(!1),[u,f]=m.useState(null);m.useEffect(()=>{re("Instagram")&&t.username&&(v(a.username===t.username),N("Instagram",{action:"getPosts",filters:{profile:!0,username:t.username},page:0}).then(p=>{p&&f(p)}))},[t==null?void 0:t.username]);const{handleScroll:P}=le({fetchData:p=>N("Instagram",{action:"getPosts",filters:{profile:!0,username:t.username},page:p}),onDataFetched:p=>f([...u,...p]),perPage:15}),T=()=>{if(!t.name)return I("warning","Name is required, not saving");N("Instagram",{action:"updateProfile",data:{name:t.name,bio:t.bio,avatar:t.avatar,private:t.private}},!0).then(p=>{if(!p)return I("warning","Failed to update profile");X.APPS.INSTAGRAM.account.set({...a,name:t.name,bio:t.bio,avatar:t.avatar,private:t.private}),c(!1),I("info","Successfully updated profile")})};z("instagram:updateProfileData",p=>{p.username===t.username&&Z.set({...t,[p.data]:p.increment?t[p.data]+1:t[p.data]-1})});const d=m.useRef(!0);m.useEffect(()=>{if(d.current){d.current=!1;return}N("Instagram",{action:"toggleFollow",data:{username:t.username,following:t.isFollowing}},!0)},[t.isFollowing]);const i=()=>{N("AccountSwitcher",{action:"getAccounts",app:"Instagram"}).then(p=>{var x;if(!p)return I("info","No accounts found");let w=a.isAdmin,U=(x=p==null?void 0:p.filter(V=>V!==t.username))==null?void 0:x.map(V=>({title:V,cb:()=>{N("AccountSwitcher",{action:"switch",app:"Instagram",account:V}).then(oe=>{N("Instagram",{action:"isLoggedIn"}).then(Q=>{Q?(X.APPS.INSTAGRAM.account.set(Q),H.set({...Q,isAdmin:w}),R.set("feed"),ie.set(!0)):X.APPS.INSTAGRAM.account.set(null)})})}}));G.ContextMenu.set({buttons:[{title:a.username},...U,{title:l("APPS.INSTAGRAM.ADD_ACCOUNT"),cb:()=>ie.set(!1)}]})})};return e(F,{children:t&&r("div",{className:"profile",children:[e(Ie,{children:s&&r(Ee.div,{className:"edit-profile",initial:{y:750,opacity:.75},animate:{y:0,opacity:1},exit:{y:750,opacity:.75},transition:{duration:.3,ease:"easeInOut"},children:[r("div",{className:"edit-profile-header",children:[e("div",{className:"cancel",onClick:()=>c(!1),children:l("CANCEL")}),e("div",{className:"title",children:l("APPS.INSTAGRAM.EDIT_PROFILE")}),e("div",{className:"save",onClick:()=>T(),children:l("APPS.INSTAGRAM.DONE")})]}),r("div",{className:"edit-profile-body",children:[r("div",{className:"profile-picture",onClick:()=>{var p,w,U;G.Gallery.set({allowExternal:(U=(w=(p=me)==null?void 0:p.value)==null?void 0:w.AllowExternal)==null?void 0:U.InstaPic,onSelect:x=>{let V=Array.isArray(x)?x[0].src:x.src;Z.set({...t,avatar:V}),H.set({...a,avatar:V})}})},children:[e(k,{avatar:t.avatar}),e("span",{children:l("APPS.INSTAGRAM.CHANGE_PICTURE")})]}),r("div",{className:"profile-items",children:[r("div",{className:"item",children:[e("div",{className:"title",children:l("APPS.INSTAGRAM.NAME")}),e(te,{placeholder:l("APPS.INSTAGRAM.NAME_PLACEHOLDER"),onChange:p=>{Z.set({...t,name:p.target.value})},maxLength:30,defaultValue:t.name})]}),r("div",{className:"item",children:[e("div",{className:"title",children:l("APPS.INSTAGRAM.BIO")}),e(it,{placeholder:l("APPS.INSTAGRAM.BIO"),onChange:p=>{Z.set({...t,bio:p.target.value})},maxLength:100,defaultValue:t.bio??""})]}),r("div",{className:"item",children:[e("div",{className:"title",children:l("APPS.INSTAGRAM.PRIVATE")}),e("div",{className:"switch-wrapper",children:e(_t,{checked:t.private,onChange:()=>Z.set({...t,private:!t.private})})})]})]}),r("div",{className:"buttons",children:[e("div",{className:"button blue",onClick:()=>{G.PopUp.set({title:l("APPS.INSTAGRAM.SIGN_OUT_POPUP.TITLE"),description:l("APPS.INSTAGRAM.SIGN_OUT_POPUP.DESCRIPTION"),buttons:[{title:l("APPS.INSTAGRAM.SIGN_OUT_POPUP.CANCEL")},{title:l("APPS.INSTAGRAM.SIGN_OUT_POPUP.PROCEED"),color:"red",cb:()=>{N("Instagram",{action:"signOut"},!0).then(p=>{if(!p)return I("error","Failed to sign out");ie.set(null),H.set(null),X.APPS.INSTAGRAM.account.set(null)})}}]})},children:l("APPS.INSTAGRAM.SIGN_OUT")}),((M=(S=(h=me)==null?void 0:h.value)==null?void 0:S.ChangePassword)==null?void 0:M.InstaPic)&&e("div",{className:"button red",onClick:()=>Pt("Instagram",()=>{}),children:l("APPS.INSTAGRAM.CHANGE_PASSWORD")}),((O=(L=(g=me)==null?void 0:g.value)==null?void 0:L.DeleteAccount)==null?void 0:O.InstaPic)&&e("div",{className:"button red",onClick:()=>{gt("Instagram",()=>{ie.set(null),H.set(null),X.APPS.INSTAGRAM.account.set(null)})},children:l("APPS.INSTAGRAM.DELETE_ACCOUNT")})]})]})]})}),o?e("div",{className:"instagram-header",children:r("div",{className:"name",style:{cursor:"pointer"},onClick:()=>i(),children:[t.username??"Unknown",t.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})}),e(et,{})]})}):r("div",{className:"profile-header",children:[e("i",{className:"fas fa-chevron-left",onClick:()=>{n!=null&&n.cb?n.cb():R.set("feed")}}),r("div",{className:"name",children:[t.username??"Unknown",t.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]}),e("span",{})]}),r("div",{className:"profile-body",children:[r("div",{className:"profile-wrapper",children:[r("div",{className:"top",children:[r("div",{className:"profile-picture",children:[e(k,{avatar:t.avatar,className:"avatar"}),t.isLive&&(!t.private||t.isFollowing||!o)?e("img",{onClick:()=>{N("Instagram",{action:"getLives"}).then(p=>{var U;let w=t.username;N("Instagram",{action:"viewLive",username:((U=p[w])==null?void 0:U.participant)??w}).then(x=>{var Q,fe,he,Ne;if(!x)return I("error","Failed to view live, perhaps it ended?");let V=(Q=p[w])!=null&&Q.participant?p[(fe=p[w])==null?void 0:fe.participant]:p[w],oe=(V==null?void 0:V.participants)??[];oe.unshift({username:((he=p[w])==null?void 0:he.participant)??w,name:V.name,avatar:V.avatar,verified:V.verified,id:V.id}),ee.set({host:((Ne=p[w])==null?void 0:Ne.participant)??w,participants:oe,viewers:x}),R.set("live")})})},className:"circle live",src:"./assets/img/icons/instagram/LiveCircle.svg"}):null,t.hasStory&&!t.isLive&&(!t.private||t.isFollowing||o)?e("img",{className:"circle","data-seen":t.seenStory===1,src:"./assets/img/icons/instagram/StoryCircle.svg",onClick:p=>{let[w,U]=st(p);ye({username:t.username,avatar:t.avatar,verified:t.verified},null,{x:w,y:U})}}):null]}),r("div",{className:"stats",children:[r("div",{children:[e("div",{className:"value",children:t.posts}),e("div",{className:"label",children:t.posts===1?l("APPS.INSTAGRAM.POST"):l("APPS.INSTAGRAM.POSTS")})]}),r("div",{"data-disabled":!t.isFollowing&&t.private&&!o,onClick:()=>{!t.isFollowing&&t.private&&!o||ce.set({title:l("APPS.INSTAGRAM.FOLLOWERS"),username:t.username})},children:[e("div",{className:"value",children:t.followers}),e("div",{className:"label",children:t.followers===1?l("APPS.INSTAGRAM.FOLLOWER"):l("APPS.INSTAGRAM.FOLLOWERS")})]}),r("div",{"data-disabled":!t.isFollowing&&t.private&&!o,onClick:()=>{!t.isFollowing&&t.private&&!o||ce.set({title:l("APPS.INSTAGRAM.FOLLOWING"),username:t.username})},children:[e("div",{className:"value",children:t.following}),e("div",{className:"label",children:l("APPS.INSTAGRAM.FOLLOWING")})]})]})]}),e("div",{className:"name",children:t.name}),e("div",{className:"description",children:be(t.bio)??""}),e("div",{className:"buttons",children:o?o&&e("div",{className:"button edit",onClick:()=>c(!0),children:l("APPS.INSTAGRAM.EDIT_PROFILE")}):r(F,{children:[e("div",{className:_e("button",t.requested||t.isFollowing?"following":"follow"),onClick:()=>{t.isFollowing?G.PopUp.set({title:l("APPS.INSTAGRAM.UNFOLLOW_POPUP.TITLE"),description:l("APPS.INSTAGRAM.UNFOLLOW_POPUP.DESCRIPTION").format({name:t.username}),buttons:[{title:l("APPS.INSTAGRAM.UNFOLLOW_POPUP.CANCEL")},{title:l("APPS.INSTAGRAM.UNFOLLOW_POPUP.PROCEED"),color:"red",cb:()=>{Z.set({...t,isFollowing:!1})}}]}):t.private?N("Instagram",{action:"toggleFollow",data:{username:t.username,following:!0}},!0).then(p=>{if(!p)return I("error","Failed to follow");Z.set({...t,requested:!t.requested})}):Z.set({...t,isFollowing:!0})},children:t.isFollowing?l("APPS.INSTAGRAM.FOLLOWING"):t.requested?l("APPS.INSTAGRAM.REQUESTED"):l("APPS.INSTAGRAM.FOLLOW")}),!t.private||t.isFollowing?e("div",{className:"button following",onClick:()=>{ue.set({username:t.username,name:t.name,verified:t.verified,avatar:t.avatar}),R.set("dm")},children:l("APPS.INSTAGRAM.MESSAGE")}):null]})})]}),t.private&&!t.isFollowing&&!o?r("div",{className:"private-account",children:[e("div",{className:"icon",children:e(At,{})}),e("div",{className:"title",children:l("APPS.INSTAGRAM.PRIVATE_ACCOUNT")}),e("div",{className:"description",children:l("APPS.INSTAGRAM.PRIVATE_ACCOUNT_DESCRIPTION")})]}):e("div",{className:"posts",onScroll:P,children:u&&u.map((p,w)=>{let U=JSON.parse(p.media)[0];return e("div",{onClick:()=>{Y.set({cb:()=>{Z.set(t),R.set("profile"),Ge.set(null),Y.set(null)}}),Ge.set(p),R.set("post")},children:j(U)?e("video",{src:U,muted:!0,autoPlay:!0,loop:!0}):e(ae,{src:U,blur:t.username!==a.username})},w)})})]})]})})}function Bt(){const[a,t]=m.useState(""),[n,o]=m.useState(""),[v,s]=m.useState([]),[c,u]=m.useState([]);m.useEffect(()=>{re("Instagram")&&N("Instagram",{action:"getPosts",page:0},W.posts).then(P=>{if(!P)return I("warning","No posts received in getPosts");u(P)})},[]),m.useEffect(()=>{const P=setTimeout(()=>t(n),500);return()=>clearTimeout(P)},[n]),m.useEffect(()=>{a.length>0&&N("Instagram",{action:"search",query:a},Object.keys(W.accounts).map(P=>W.accounts[P])).then(P=>{P&&s(P)})},[a]);const{handleScroll:f}=le({fetchData:P=>N("Instagram",{action:"getPosts",page:P}),onDataFetched:P=>u([...c,...P]),perPage:15});return r("div",{className:"search-container",children:[e("div",{className:"search-header",children:e(Be,{placeholder:l("SEARCH"),onChange:P=>o(P.target.value)})}),e("div",{className:"search-body",onScroll:f,children:a.length>0?e("div",{className:"search-results",children:v.map(P=>r("div",{className:"item",onClick:()=>y(P.username),children:[e("div",{className:"profile-picture",children:e(k,{avatar:P.avatar})}),r("div",{className:"user",children:[r("div",{className:"username",children:[P.username,P.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]}),e("div",{className:"name",children:P.name})]})]}))}):e("div",{className:"feed-grid",children:c.map((P,T)=>{let i=Math.floor(T/3)%2==0,h=!1;h=T%3==(i?1:0),c[T+1]||(h=!1);let S=JSON.parse(P.media)[0];return e("div",{className:_e("grid-post",h&&"big"),onClick:()=>{Y.set({cb:()=>{R.set("search"),Y.set(null)}}),Ge.set(P),R.set("post")},children:j(S)?e("video",{src:S,autoPlay:!0,loop:!0,muted:!0}):e(ae,{src:S,blur:!0})},T)})})})]})}function zt(){const a=E(H);E(R);const t=E(Y),n=E(De),[o,v]=m.useState(""),s=m.useRef(null);m.useState(0),m.useState(!1),m.useState(!1);const c=()=>{o.length!==0&&N("Instagram",{action:"postComment",data:{postId:n.post.id,comment:o}},Math.random().toString(36).substring(7)).then(f=>{if(!f)return I("error","Failed to post comment, no ");v(""),s.current.value="",De.set({...n,post:n.post,comments:[{user:{username:a.username,avatar:a.avatar,verified:a.verified},comment:{id:f,content:o,timestamp:new Date().getTime(),likes:0,liked:!1}},...n.comments]})})},{handleScroll:u}=le({fetchData:f=>N("Instagram",{action:"getComments",postId:n.post.id,page:f}),onDataFetched:f=>De.set({...n,comments:[...n.comments,...f]}),perPage:25});return r("div",{className:"comments-container",children:[r("div",{className:"comments-header",children:[e(Ve,{onClick:()=>{t&&(t!=null&&t.cb)?t.cb():R.set("feed")}}),e("div",{className:"title",children:l("APPS.INSTAGRAM.COMMENTS")}),e("span",{})]}),r("div",{className:"comments-body",onScroll:u,children:[r("div",{className:"post-info",children:[e("div",{className:"profile-picture",children:e(k,{avatar:n.post.avatar})}),r("div",{className:"post-data",children:[r("div",{className:"caption",children:[r("span",{className:"username",children:[n.post.username,n.post.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]}),be(n.post.caption)]}),e("div",{className:"actions",children:e("div",{className:"date",children:lt(n.post.timestamp)})})]})]}),n.comments.map((f,P)=>e(Kt,{data:f},P))]}),r("div",{className:"add-comment",children:[e("div",{className:"profile-picture",children:e(k,{avatar:a.avatar})}),r("div",{className:"input-container",children:[e(te,{type:"text",ref:s,placeholder:l("APPS.INSTAGRAM.ADD_COMMENT_AS").format({name:a.username}),onKeyPress:f=>{f.key==="Enter"&&c()},onChange:f=>v(f.target.value)}),e("div",{className:"send",onClick:c,children:l("APPS.INSTAGRAM.POST")})]})]})]})}const Kt=({data:a})=>{var s;const[t,n]=m.useState(a.comment.liked),[o,v]=m.useState(a.comment.likes);return z("instagram:updateCommentLikes",c=>{!c||c.commentId!==a.comment.id||v(u=>u+(c.increment?1:-1))}),r("div",{className:"comment",children:[r("div",{className:"user",children:[e("div",{className:"profile-picture",onClick:()=>y(a.user.username),children:e(k,{avatar:a.user.avatar})}),r("div",{className:"comment-data",children:[r("div",{className:"caption",children:[r("span",{className:"username",onClick:()=>y(a.user.username),children:[a.user.username,a.user.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]}),a.comment.content]}),r("div",{className:"actions",children:[e("div",{className:"date",children:lt(a.comment.timestamp)}),r("div",{className:"likes",children:[o," ",(s=l("APPS.INSTAGRAM.LIKES"))==null?void 0:s.toLowerCase()]})]})]})]}),e(rt,{liked:t,onClick:()=>{a.comment.id&&N("Instagram",{action:"toggleLike",data:{postId:a.comment.id,toggle:!t,isComment:!0}},!t).then(c=>n(c))}})]})},lt=a=>{const t=new Date(a),o=new Date().getTime()-t.getTime(),v=Math.floor(o/(1e3*60*60*24)),s=Math.floor(o/(1e3*60*60)),c=Math.floor(o/(1e3*60));let u="";return v<1?s<1?u=`${c}m`:u=`${s}h`:v<30?u=`${v}d`:u=`${t.getDate()}/${t.getMonth()+1}/${t.getFullYear()}`,u};function jt(){E(R);const[a,t]=m.useState(0),[n,o]=m.useState([]),[v,s]=m.useState(""),[c,u]=m.useState(null);return m.useEffect(()=>{var f,P,T;a===0&&G.Gallery.set({allowExternal:(T=(P=(f=me)==null?void 0:f.value)==null?void 0:P.AllowExternal)==null?void 0:T.InstaPic,multiSelect:!0,includeVideos:!0,onCancel:()=>R.set("feed"),onSelect:d=>{Array.isArray(d)?d.length>0&&o(d.map(i=>i.src)):d&&o([d.src]),t(1)}})},[a]),e("div",{className:"newpost-container","data-camera":!1,children:a===1&&r(F,{children:[r("div",{className:"newpost-header",children:[e(Ve,{onClick:()=>t(0)}),e("div",{className:"title",children:l("APPS.INSTAGRAM.NEW_POST")}),e("div",{className:"next",onClick:()=>{N("Instagram",{action:"newPost",data:{caption:v,location:c,images:n}},!0).then(f=>{if(!f)return I("warning","Failed to post");R.set("feed")})},children:l("APPS.INSTAGRAM.POST")})]}),e("div",{className:"newpost-body",children:r("div",{className:"newpost-content",children:[r("div",{className:"caption",children:[e("div",{className:"attachments",style:{width:`calc(4rem + ${n.length*20}px)`},children:n.slice(0,5).map((f,P)=>e("div",{className:"attachment",style:{left:`${P*20}px`,top:`${P*5}px`,zIndex:P+1},children:j(f)?e(Jt,{src:f}):e(ae,{src:f,alt:"image"})},P))}),e("div",{className:"text-area",children:e(it,{onChange:f=>s(f.target.value),placeholder:l("APPS.INSTAGRAM.CAPTION_PLACEHOLDER")})})]}),e("div",{className:"item",onClick:()=>{G.PopUp.set({title:l("APPS.INSTAGRAM.CHOOSE_LOCATION.TITLE"),description:l("APPS.INSTAGRAM.CHOOSE_LOCATION.DESCRIPTION"),input:{type:"text",maxCharacters:30,onChange:f=>u(f)},buttons:[{title:l("APPS.INSTAGRAM.CHOOSE_LOCATION.CANCEL")},{title:l("APPS.INSTAGRAM.CHOOSE_LOCATION.PROCEED")}]})},children:c?r(F,{children:[r("div",{className:"label",children:[e(St,{}),c]}),e(ve,{onClick:f=>{f.stopPropagation(),u(null)}})]}):r(F,{children:[e("div",{className:"label",children:l("APPS.INSTAGRAM.LOCATION")}),e(nt,{})]})})]})})]})})}const Jt=({src:a})=>{const[t,n]=m.useState(null);return r(F,{children:[e("video",{src:a,crossOrigin:"anonymous",controls:!1,onMouseOver:v=>v.currentTarget.play(),onMouseOut:v=>v.currentTarget.pause(),onLoadedMetadata:v=>n(v.currentTarget.duration)}),t&&e("div",{className:"video-duration",children:(v=>{const s=Math.floor(v/60),c=pt(v-s*60,0);return`${s}:${c<10?"0"+c:c}`})(t)})]})};function Xt(){const a=E(Y),t=E(Ge);return e(F,{children:t&&e("div",{className:"viewpost-container",children:r(F,{children:[r("div",{className:"viewpost-header",children:[e(Ve,{onClick:()=>{a&&(a!=null&&a.cb)?a.cb():R.set(a.view)}}),e("div",{className:"name",children:t.username??"Unknown"}),e("span",{})]}),e("div",{className:"profile-body",children:e(ct,{data:t})})]})})})}function Qt(){E(ne.Settings);const a=E(H),t=E(ue),n=E(Y),[o,v]=m.useState([]),s=m.useRef(null),c=m.useRef(0),[u,f]=m.useState({content:"",attachments:[]});m.useEffect(()=>{var d;v([]),N("Instagram",{action:"getMessages",page:0,username:t.username},((d=W.messages.find(i=>i.username===t.username))==null?void 0:d.messages)??[]).then(i=>{i&&v(i.reverse())})},[]),z("instagram:newMessage",d=>{if(t.username!==d.sender||d.sender===a.username)return;v([...o,d]);let i=document.querySelector(".dm-body-container");i.scrollTop=i.scrollHeight});const P=()=>{u.content.length===0&&u.attachments.length===0||N("Instagram",{action:"sendMessage",username:t.username,message:{content:u.content,attachments:u.attachments.length>0?u.attachments:null}},!0).then(d=>{if(!d)return;s.current.value="",v([...o,{sender:a.username,recipient:t.username,content:u.content,attachments:u.attachments,timestamp:Date.now()}]),f({content:"",attachments:[]});let i=document.querySelector(".dm-body-container");i.scrollTop=i.scrollHeight})},{handleScroll:T}=le({fetchData:d=>N("Instagram",{action:"getMessages",username:t.username,page:d}),onDataFetched:d=>{let i=document.querySelector(".dm-body-container");c.current=i.scrollHeight,v([...d.reverse(),...o])},isReversed:!0,perPage:25});return m.useEffect(()=>{let d=document.querySelector(".dm-body-container");const i=d.scrollHeight;d.scrollTop+=i-c.current,d.scroll},[o]),r("div",{className:"dm-container",children:[r("div",{className:"dm-header",children:[e("i",{className:"far fa-chevron-left",onClick:()=>{n&&n.cb?(n.cb(),Y.reset()):R.set("dmList")}}),r("div",{className:"profile",onClick:d=>{d.stopPropagation(),Y.set({cb:()=>{ue.set(t),R.set("dm")}}),y(t.username)},children:[e("div",{className:"avatar",children:e(k,{avatar:t.avatar})}),r("div",{className:"username",children:[t.name??t.username,t.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]})]})]}),e("div",{className:"dm-body-container",onScroll:T,children:e("div",{className:"dm-body",children:o.map((d,i)=>{var L;let h,S=d.sender===a.username?"self":"other",M=((L=o[i+1])==null?void 0:L.sender)===a.username?"self":"other",g=We(d.content);return o[i+1]?h=Math.abs(d.timestamp-o[i+1].timestamp)/36e5:M=void 0,r("div",{className:`message ${S}`,children:[S=="other"?r("div",{className:"message-with-pfp",children:[e("div",{"data-story-reply":g?"true":"false",className:_e("profile-picture",S!==M?"show":"hide"),onClick:O=>{O.stopPropagation(),Y.set({cb:()=>{ue.set(t),R.set("dm")}}),y(t.username)},children:e(k,{avatar:t.avatar})}),e("div",{className:"message-content",children:g?r("div",{className:"story-reply",children:[e("div",{className:"title",children:l("APPS.INSTAGRAM.REPLIED_TO_YOUR_STORY")}),j(g.src)?e("video",{src:g.src,controls:!1,loop:!0,autoPlay:!0,muted:!0}):e(ae,{src:g.src}),e("div",{className:"content",children:g.content})]}):r(F,{children:[d.content&&e("div",{className:"content",children:be(d.content)}),d.attachments&&e("div",{className:"attatchments",children:d.attachments.map((O,p)=>j(O)?e("video",{src:O,controls:!1,loop:!0,autoPlay:!0,muted:!0,onClick:w=>G.FullscreenImage.set(O)},p):e(ae,{src:O,blur:!0,onClick:()=>G.FullscreenImage.set(O)},p))})]})})]}):e(F,{children:g?r("div",{className:"story-reply",children:[j(g.src)?e("video",{src:g.src,controls:!1,loop:!0,autoPlay:!0,muted:!0}):e(ae,{src:g.src}),e("div",{className:"content",children:g.content})]}):r(F,{children:[d.content&&e("div",{className:"content",children:be(d.content)}),d.attachments&&e("div",{className:"attatchments",children:d.attachments.map((O,p)=>j(O)?e("video",{src:O,controls:!1,loop:!0,muted:!0,autoPlay:!0,onClick:w=>{G.FullscreenImage.set(O)}},p):e(ae,{src:O,onClick:()=>{G.FullscreenImage.set(O)}},p))})]})}),o[i+1]&&h>6?e("div",{className:"date",children:Xe(d.timestamp)}):S!==M&&e("div",{className:"date",children:Xe(d.timestamp)})]},i)})})}),e("div",{className:"attachments",children:u.attachments.map((d,i)=>r("div",{className:"attachment",children:[j(d)?e("video",{src:d,muted:!0,controls:!1,loop:!0,autoPlay:!0}):e(ae,{src:d}),e(ve,{onClick:()=>{f({...u,attachments:u.attachments.filter((h,S)=>S!==i)})}})]},i))}),r("div",{className:"dm-footer",children:[e(te,{type:"text",ref:s,onChange:d=>{f({...u,content:d.target.value})},onKeyDown:d=>{d.key==="Enter"&&P()},placeholder:l("APPS.INSTAGRAM.MESSAGE_PLACEHOLDER")}),u.content.length>0?e("span",{onClick:()=>P(),className:"send",children:l("APPS.INSTAGRAM.SEND")}):e(It,{onClick:()=>{var d,i,h;u.attachments.length<3&&G.Gallery.set({includeVideos:!0,allowExternal:(h=(i=(d=me)==null?void 0:d.value)==null?void 0:i.AllowExternal)==null?void 0:h.InstaPic,onSelect:S=>f({...u,attachments:[...u.attachments,Array.isArray(S)?S[0].src:S.src]})})}})]})]})}const We=a=>{if(!(a.includes("<!REPLIED_STORY-DATA=")&&a.includes("!>")))return null;const n=a.match(/src="([^"]+)"/),o=a.match(/content:"([^"]+)"/);return!n||!o?null:{src:n[1],content:o[1]}},Ze=J(0);function Zt(){const a=E(H),[t,n]=m.useState(!1),[o,v]=m.useState(""),[s,c]=m.useState(""),[u,f]=m.useState([]),[P,T]=m.useState([]);m.useEffect(()=>{re("Instagram")&&N("Instagram",{action:"getRecentMessages",page:0},W.messages).then(i=>{i&&T(i)})},[]);const{handleScroll:d}=le({fetchData:i=>N("Instagram",{action:"getRecentMessages",page:i}),onDataFetched:i=>T([...P,...i]),perPage:25});return m.useEffect(()=>{const i=setTimeout(()=>v(s),500);return()=>clearTimeout(i)},[s]),m.useEffect(()=>{o.length>0?t?N("Instagram",{action:"search",query:o},W.messages.filter(i=>i.username!==a.username)).then(i=>{i&&f(i)}):f(P.filter(i=>{var h;return i.username.includes(o)||((h=i==null?void 0:i.name)==null?void 0:h.includes(o))&&i.username!==a.username})):f([])},[o]),m.useEffect(()=>{v(""),f([])},[t]),e("div",{className:"dm-list",children:t?r("div",{className:"slide up",children:[r("div",{className:"dm-list-header new",children:[e("i",{className:"far fa-chevron-left",onClick:()=>{n(!1),f([])}}),e("div",{className:"title",children:l("APPS.INSTAGRAM.NEW_MESSAGE")}),e("span",{})]}),r("div",{className:"dm-list-body ",children:[r("div",{className:"input",children:[e("span",{children:l("APPS.INSTAGRAM.TO")}),e(te,{type:"text",onChange:i=>c(i.target.value),placeholder:l("SEARCH")})]}),u.length>0&&e("div",{className:"items",children:u.map((i,h)=>r("div",{className:"item",onClick:()=>{n(!1),ue.set(i),R.set("dm")},children:[e("div",{className:"avatar",onClick:S=>{S.stopPropagation(),Y.set({cb:()=>{R.set("dmList"),Y.reset()}}),y(i.username)},children:e(k,{avatar:i.avatar})}),r("div",{className:"info",children:[r("div",{className:"username",children:[i.username,i.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]}),e("div",{className:"last-message",children:i.name})]})]},h))})]})]}):r(F,{children:[r("div",{className:"dm-list-header",children:[r("div",{className:"top",children:[e("i",{className:"far fa-chevron-left",onClick:()=>R.set("feed")}),e("i",{className:"far fa-edit",onClick:()=>n(!0)})]}),e(Be,{placeholder:l("SEARCH"),onChange:i=>c(i.target.value)})]}),e("div",{className:"dm-list-body",onScroll:d,children:e("div",{className:"items",children:o.length>0&&!t?u.map((i,h)=>{var S,M;if(((S=i.content)==null?void 0:S.length)===0&&(i.content=l("APPS.INSTAGRAM.SENT_A_PHOTO")),(M=i.content)!=null&&M.includes("<!REPLIED_STORY-DATA=")){let g=We(i.content);i.content=l("APPS.INSTAGRAM.REPLIED_TO_YOUR_STORY_CONTENT").format({content:g.content})}return r("div",{className:"item",onClick:()=>{i.unread&&Ze.set(Ze.value-1),n(!1),ue.set(i),R.set("dm")},children:[e("div",{className:"avatar",onClick:g=>{g.stopPropagation(),Y.set({cb:()=>{R.set("dmList"),Y.reset()}}),y(i.username)},children:e(k,{avatar:i.avatar})}),r("div",{className:"info",children:[r("div",{className:"username",children:[i.name,i.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]}),r("div",{className:"last-message",children:[i.content.length>40?i.content.substring(0,40)+"...":i.content," • ",Pe(i.timestamp)]})]})]},h)}):P.map((i,h)=>{var S;if(i.content.length===0&&(i.content=l("APPS.INSTAGRAM.SENT_A_PHOTO")),(S=i.content)!=null&&S.includes("<!REPLIED_STORY-DATA=")){let M=We(i.content);i.content=l("APPS.INSTAGRAM.REPLIED_TO_YOUR_STORY_CONTENT").format({content:M.content})}return r("div",{className:"item",onClick:()=>{n(!1),ue.set(i),R.set("dm")},children:[e("div",{className:"avatar",onClick:M=>{M.stopPropagation(),Y.set({cb:()=>{R.set("dmList"),Y.reset()}}),y(i.username)},children:e(k,{avatar:i.avatar})}),r("div",{className:"info",children:[r("div",{className:"username",children:[i.name,i.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]}),i.content.length>40?i.content.substring(0,40)+"...":i.content," • ",Pe(i.timestamp)]})]},h)})})})]})})}function ea(){var oe,Q,fe,he,Ne,Te,de,Re,Ce,Oe,Le,A,C,_,B,q,Se,ke,ze,Ke,je;const a=E(ne.Settings),t=E(ge.Visible),n=E(Fe),o=E(H),v=E(se),s=E(Ae),[c,u]=m.useState(0),[f,P]=m.useState(""),[T,d]=m.useState(!1),[i,h]=m.useState({x:0,y:0}),S=m.useRef(null),[M,g]=m.useState(0),[L,O]=m.useState(null);m.useEffect(()=>{if(s){h(s.origin);for(let b=0;b<s.media.length;b++)if(!s.media[b].seen)return u(b)}},[]),m.useEffect(()=>{var b,D,$;(D=(b=s==null?void 0:s.media[c])==null?void 0:b.metadata)!=null&&D.post&&Et(($=s==null?void 0:s.media[c])==null?void 0:$.image).then(K=>{if(!K)return I("warning","Failed to get average color");O(`
                    linear-gradient(0deg,
                        ${K.darkmuted} 0%,
                        ${K.darkvibrant} 100%
                    )
                `)})},[c]),m.useEffect(()=>{var b,D;if(!(!S.current||((b=a==null?void 0:a.sound)==null?void 0:b.volume)===void 0)){if(!t||!re("Instagram")){S.current.pause();return}(D=S==null?void 0:S.current)!=null&&D.paused&&S.current.play(),S.current.volume=a.sound.volume}},[(oe=a==null?void 0:a.sound)==null?void 0:oe.volume,t,n==null?void 0:n.active]);const p=b=>{if(T)return d(!1);const D=b.target.getBoundingClientRect(),$=b.clientX-D.left*we();let K=s.media[c];if(K.seen||N("Instagram",{action:"viewedStory",id:K.id}),$<D.width*we()/2)if(c>0)u(c-1);else{let pe=v.findIndex(xe=>xe.username===s.username),Me=v[pe-1];Me&&ye(Me)}else if(c<s.media.length-1)u(c+1);else{let pe=v.findIndex(xe=>xe.username===s.username),Me=v[pe+1];Me?(U(s.username),ye(Me),u(0)):x()}},w=()=>{var D,$;if(!f||f==="")return;let b=`<!REPLIED_STORY-DATA='{src="${($=(D=s==null?void 0:s.media)==null?void 0:D[c])==null?void 0:$.image}", content:"${f}"}'!>`;N("Instagram",{action:"sendMessage",username:s.username,message:{content:b}},!0).then(K=>{if(!K)return I("error","Failed to send message");ue.set({username:s.username,name:s.name,verified:s.verified,avatar:s.avatar}),R.set("dm"),x()})},U=b=>{var D;s.media.length===c+1&&se.set((D=se.value)==null?void 0:D.map($=>($.username===b&&($.seen=1),$)))},x=b=>{b!==!1&&U(s.username),Ae.set(null)},V=()=>{var b,D;(D=(b=s==null?void 0:s.media[c])==null?void 0:b.metadata)!=null&&D.post&&G.ContextMenu.set({buttons:[{title:l("APPS.INSTAGRAM.VIEW_POST"),cb:()=>{var $,K,pe;if(Ue((pe=(K=($=s==null?void 0:s.media[c])==null?void 0:$.metadata)==null?void 0:K.post)==null?void 0:pe.id),s!=null&&s.media[c].seen)return x();N("Instagram",{action:"viewedStory",id:s==null?void 0:s.media[c].id},!0).then(()=>x())}}]})};return(Q=s==null?void 0:s.media)!=null&&Q[c]?r(Ee.div,{className:"instagram-story",initial:{scale:0,transformOrigin:s!=null&&s.origin?`${((fe=s==null?void 0:s.origin)==null?void 0:fe.x)??0}px ${((he=s==null?void 0:s.origin)==null?void 0:he.y)??0}px`:"50% 50%"},animate:{scale:1,transformOrigin:s!=null&&s.origin?`${((Ne=s==null?void 0:s.origin)==null?void 0:Ne.x)??0}px ${((Te=s==null?void 0:s.origin)==null?void 0:Te.y)??0}px`:"50% 50%"},exit:{scale:0,transformOrigin:i?`${(i==null?void 0:i.x)??0}px ${(i==null?void 0:i.y)??0}px`:"50% 50%"},transition:{duration:.25,ease:"easeInOut"},children:[(Ce=(Re=(de=s==null?void 0:s.media)==null?void 0:de[c])==null?void 0:Re.metadata)!=null&&Ce.post?e("div",{className:"background-image",onClick:p,style:{background:L??"rgb(20,20,20)"}}):j((Le=(Oe=s==null?void 0:s.media)==null?void 0:Oe[c])==null?void 0:Le.image)?e("video",{ref:S,className:"background-image",onClick:p,muted:!0,autoPlay:!0,loop:!0,src:(C=(A=s==null?void 0:s.media)==null?void 0:A[c])==null?void 0:C.image,onTimeUpdate:()=>g(S.current.currentTime/S.current.duration)}):e("div",{className:"background-image",onClick:p,style:{backgroundImage:`url(${s==null?void 0:s.media[c].image})`}}),e(Ie,{children:T&&e(ta,{activeImage:c,close:()=>d(!1)})}),r("div",{className:"story-wrapper",children:[r("div",{className:"story-header",children:[e("div",{className:"story-steps",children:s==null?void 0:s.media.map((b,D)=>{var K;let $=j((K=s==null?void 0:s.media[D])==null?void 0:K.image);return e("div",{style:$&&c===D?{background:`linear-gradient(to right, rgba(255,255,255,0.8) ${M*100}%, rgba(255,255,255,0.3) ${M*100}%)`}:void 0,className:c===D?"active":"",onClick:()=>u(D)},D)})}),r("div",{className:"profile-details",children:[r("div",{className:"profile",onClick:()=>{s!=null&&s.media[c].seen||N("Instagram",{action:"viewedStory",id:s==null?void 0:s.media[c].id},!0).then(()=>x()),x(),y(s.username)},children:[e("div",{className:"profile-picture",children:e(k,{className:"avatar",avatar:s==null?void 0:s.avatar})}),r("div",{className:"name",children:[s==null?void 0:s.username,!1]}),e("div",{className:"time",children:Pe(s==null?void 0:s.media[c].timestamp)})]}),e("div",{className:"stats",children:e(ve,{onClick:()=>{if(s!=null&&s.media[c].seen)return x();N("Instagram",{action:"viewedStory",id:s==null?void 0:s.media[c].id},!0).then(()=>x())}})})]})]}),((B=(_=s==null?void 0:s.media[c])==null?void 0:_.metadata)==null?void 0:B.post)&&e("div",{className:"story-post",children:r("div",{className:"post-wrapper",onClick:V,children:[j((q=s==null?void 0:s.media[c])==null?void 0:q.image)?e("video",{src:(Se=s==null?void 0:s.media[c])==null?void 0:Se.image,muted:!0}):e(ae,{src:(ke=s==null?void 0:s.media[c])==null?void 0:ke.image}),r("div",{className:"post-username",children:["@",(je=(Ke=(ze=s==null?void 0:s.media[c])==null?void 0:ze.metadata)==null?void 0:Ke.post)==null?void 0:je.username]})]})}),(s==null?void 0:s.username)===o.username?r("div",{className:"story-footer","data-stats":!0,children:[r("div",{className:"seen-by",onClick:()=>d(!0),children:[(s==null?void 0:s.media[c].viewers)&&e("div",{className:"images",children:s==null?void 0:s.media[c].viewers.map((b,D)=>e(k,{avatar:b.avatar},D))}),l("APPS.INSTAGRAM.SEEN_BY").format({count:kt(s==null?void 0:s.media[c].views)??0})]}),e(ve,{onClick:()=>{G.PopUp.set({title:l("APPS.INSTAGRAM.DELETE_STORY.TITLE"),description:l("APPS.INSTAGRAM.DELETE_STORY.DESCRIPTION"),buttons:[{title:l("APPS.INSTAGRAM.DELETE_STORY.CANCEL")},{title:l("APPS.INSTAGRAM.DELETE_STORY.PROCEED"),color:"red",cb:()=>{N("Instagram",{action:"removeFromStory",id:s==null?void 0:s.media[c].id},!0).then(b=>{var D;if(!b)return I("error","Failed to remove image from story");I("info","Removed image from story"),Ae.set(null),s.media.length===1&&se.set((D=se.value)==null?void 0:D.filter($=>$.username!==s.username))})}}]})}})]}):r("div",{className:"story-footer",children:[e("div",{className:"input",children:e(te,{type:"text",placeholder:l("APPS.INSTAGRAM.SEND_A_MESSAGE"),onChange:b=>P(b.target.value),onKeyDown:b=>{b.key==="Enter"&&w()}})}),e(qe,{onClick:()=>w()})]})]})]}):null}const ta=a=>{var s;const t=E(Ae),[n,o]=m.useState([]);m.useEffect(()=>{var c,u;(a==null?void 0:a.activeImage)!==void 0&&N("Instagram",{action:"getViewers",id:t==null?void 0:t.media[a.activeImage].id,page:0},(u=(c=W.stories.find(f=>f.username===t.username))==null?void 0:c.media.find(f=>f.id===t.media[a.activeImage].id))==null?void 0:u.viewers).then(f=>{if(!f)return I("warning","Failed to fetch viewers data");o(f)})},[]);const{handleScroll:v}=le({fetchData:c=>N("Instagram",{action:"getViewers",id:t==null?void 0:t.media[a.activeImage].id,page:c}),onDataFetched:c=>o(u=>[...u,...c]),perPage:15});return r(Ee.div,{className:"story-modal",initial:{y:600},animate:{y:0},exit:{y:600},transition:{duration:.35,ease:"easeInOut"},children:[r("div",{className:"story-modal-header",children:[r("div",{className:"views",children:[e(tt,{})," ",e("div",{className:"views",children:((s=t==null?void 0:t.media[a==null?void 0:a.activeImage])==null?void 0:s.views)??0})]}),e(ve,{className:"close",onClick:a==null?void 0:a.close})]}),r("div",{className:"story-modal-content",children:[e("div",{className:"title",children:l("APPS.INSTAGRAM.STORY_VIEWERS")}),e("div",{className:"viewers",onScroll:v,children:n.map((c,u)=>e("div",{className:"item",children:r("div",{className:"user",onClick:()=>{y(c.username),Ae.set(null)},children:[e("div",{className:"profile-picture",children:e(k,{className:"avatar",avatar:c.avatar})}),r("div",{className:"user-details",children:[r("div",{className:"username",children:[c.username,c.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]}),e("div",{className:"name",children:c.name})]})]})},u))})]})]})};function aa(){const a=E(R),t=E(H);return e("div",{className:"instagram-footer",children:r(F,{children:[[{value:"feed",activeIcon:e(Tt,{}),icon:e(Rt,{})},{value:"search",activeIcon:e(Ct,{}),icon:e(Ot,{})},{value:"newPost",activeIcon:e(Lt,{}),icon:e(Mt,{})},{value:"notifications",activeIcon:e(Ye,{}),icon:e(at,{})}].map((o,v)=>e("div",{onClick:()=>R.set(o.value),children:a===o.value?o.activeIcon:o.icon},v)),e(k,{avatar:t.avatar,className:"profile",onClick:()=>y(t.username)})]})})}function sa(){const a=E(ce),[t,n]=m.useState([]);let o={[l("APPS.INSTAGRAM.FOLLOWING")]:"getFollowing",[l("APPS.INSTAGRAM.FOLLOWERS")]:"getFollowers",[l("APPS.INSTAGRAM.LIKED_BY")]:"getLikes"};const v=(c,u)=>{var f,P;if(c!==l("APPS.INSTAGRAM.LIKED_BY")){let T=c===l("APPS.INSTAGRAM.FOLLOWING")?(f=W.accounts[u])==null?void 0:f.followingList:((P=W.accounts[u])==null?void 0:P.followerList)??[];return T==null?void 0:T.map(d=>W.accounts[d])}};m.useEffect(()=>{re("Instagram")&&N("Instagram",{action:o[a.title],data:{postId:a.postId,username:a.username,page:0}},v(a.title,a.username)).then(c=>{if(!c)return I("info","No data returned from instagram event");n(c)})},[]);const{handleScroll:s}=le({fetchData:c=>N("Instagram",{action:o[a.title],data:{postId:a.postId,username:a.username,page:c}}),onDataFetched:c=>n([...t,...c]),perPage:20});return e(F,{children:a&&t&&r(Ee.div,{...wt,className:"userpanel-container",children:[r("div",{className:"userpanel-header",children:[e("i",{className:"far fa-chevron-left",onClick:()=>ce.set(null)}),e("div",{className:"userpanel-title",children:a.title}),e("span",{})]}),e("div",{className:"userpanel-body",onScroll:s,children:e("div",{className:"items",children:t.map((c,u)=>e(na,{data:c,allData:a},u))})})]})})}const na=a=>{const t=E(H);let n=a.data;const[o,v]=m.useState(n.isFollowing===1);return r("div",{className:"item",onClick:()=>{ce.set(a.allData),y(n.username)},children:[r("div",{className:"user",children:[e("div",{className:"profile-picture",children:e(k,{avatar:n.avatar})}),r("div",{className:"user-details",children:[r("div",{className:"username",children:[n.username,n.verified&&e("span",{className:"verified",children:e("img",{src:"./assets/img/icons/instagram/Verified.svg"})})]}),e("div",{className:"name",children:n.name})]})]}),t.username!==n.username&&e("div",{className:"action",onClick:s=>{s.stopPropagation(),n.username!==t.username&&(o?G.PopUp.set({title:l("APPS.INSTAGRAM.UNFOLLOW_POPUP.TITLE"),description:l("APPS.INSTAGRAM.UNFOLLOW_POPUP.DESCRIPTION").format({name:n.username}),buttons:[{title:l("APPS.INSTAGRAM.UNFOLLOW_POPUP.CANCEL")},{title:l("APPS.INSTAGRAM.UNFOLLOW_POPUP.PROCEED"),color:"red",cb:()=>{N("Instagram",{action:"toggleFollow",data:{username:n.username,following:!o}},!0).then(()=>{v(c=>!c)})}}]}):N("Instagram",{action:"toggleFollow",data:{username:n.username,following:!o}},!0).then(()=>{v(c=>!c)}))},children:o?e("div",{className:"button following",children:l("APPS.INSTAGRAM.FOLLOWING")}):e("div",{className:"button follow",children:l("APPS.INSTAGRAM.FOLLOW")})})]})},R=J("feed"),H=J(null),ie=J(!1),se=J([]),Ae=J(null),Z=J(null),De=J(null),Ge=J(null),ue=J(null),Y=J(null),ce=J(null);function da(){var d;const a=E(ne.Settings),t=(d=E(Fe))==null?void 0:d.active,n=E(ne.Styles.TextColor),o=E(R),v=E(H),s=E(ie),c=E(Ae),u=E(ce),[f,P]=m.useState(!0);m.useEffect(()=>{var i,h;t&&((i=t==null?void 0:t.data)==null?void 0:i.view)==="profile"&&y((h=t==null?void 0:t.data)==null?void 0:h.username)},[t]),m.useEffect(()=>{N("isAdmin",null,!1).then(i=>{if(X.APPS.INSTAGRAM.account.value){H.set({...X.APPS.INSTAGRAM.account.value,isAdmin:i}),ie.set(!0),P(!1);return}else if(X.APPS.INSTAGRAM.account.value===!1)return P(!1);N("Instagram",{action:"isLoggedIn"},W.account).then(h=>{h?(X.APPS.INSTAGRAM.account.set(h),H.set({...h,isAdmin:i}),ie.set(!0),P(!1)):(P(!1),X.APPS.INSTAGRAM.account.set(null))})})},[]),m.useEffect(()=>{if(re("Instagram"))return setTimeout(()=>{o==="live"||o==="story"?(ne.Styles.TextColor.set("#fafafaed"),o=="live"&&G.IndicatorVisible.set(!1)):(ne.Styles.TextColor.set(a.display.theme==="dark"?"#fafafaed":"#000000"),G.IndicatorVisible.set(!0)),o==="dm"?ge.ReceiveAppNotifications.set(!1):ge.ReceiveAppNotifications.set(!0)},250),()=>{ge.ReceiveAppNotifications.set(!0)}},[o]);const T={feed:e(Ht,{}),search:e(Bt,{}),notifications:e(Wt,{}),profile:e(Yt,{}),live:e(Dt,{}),logIn:e(Qe,{}),comments:e(zt,{}),post:e(Xt,{}),newPost:e(jt,{}),dmList:e(Zt,{}),dm:e(Qt,{})};return z("instagram:logout",i=>{if(!i)return I("warning","No username provided to logout");if(!s)return I("info","User is not logged in, cannot log out");if(i!==(v==null?void 0:v.username))return I("warning","Username provided does not match current logged in username, not logging out");H.set(null),ie.set(!1),R.set("logIn")}),e("div",{className:"instagram-container",children:f||!bt()?e("div",{className:"loading",children:e(Gt,{size:40,lineWeight:5,speed:2,color:n})}):e("div",{className:"instagram-wrapper",children:s?r(F,{children:[e(Ie,{children:c&&e(ea,{})}),e(Ie,{children:u&&e(sa,{})}),T[o],o!=="live"&&o!=="logIn"&&o!=="newPost"&&o!=="comments"&&!c&&e(aa,{})]}):e(Qe,{})})})}const Ue=(a,t)=>{a&&N("Instagram",{action:"getPost",id:a},W.posts.find(n=>n.id===a)).then(n=>{if(!n)return I("warning","Failed to fetch post data");if(t)return $e(n);Ge.set(n),R.set("post")})},y=a=>{var t;a&&N("Instagram",{action:"getProfile",username:a},(t=W.accounts)==null?void 0:t[a]).then(n=>{if(!n)return I("warning","Failed to fetch profile data");Z.set(n),R.set("profile"),ce.value&&ce.reset()})},$e=a=>{a.id&&N("Instagram",{action:"getComments",postId:a.id,page:0},W.comments.filter(t=>t.postId===a.id)).then(t=>{t&&(De.set({post:a,comments:t}),R.set("comments"))})},ye=(a,t,n)=>{var o;!a&&!(a!=null&&a.username)||((t==null?void 0:t.length)==0&&(t=void 0),N("Instagram",{action:"getStory",username:a.username},(o=W.stories.find(v=>v.username===a.username))==null?void 0:o.media).then(v=>{if(!v||v.length===0)return I("warning","Failed to fetch story data");Ae.set({username:a.username,avatar:a.avatar,verified:a.verified,media:v,queue:t,origin:n})}))};export{H as Account,De as Comments,R as CurrentView,ue as Dm,Y as History,ie as LoggedIn,Ge as Post,Z as Profile,se as Stories,Ae as Story,ce as UserPanel,da as default,$e as fetchAndSetComments,Ue as fetchAndSetPost,y as fetchAndSetProfile,ye as fetchAndSetStory};
