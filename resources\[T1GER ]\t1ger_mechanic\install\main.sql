DROP TABLE IF EXISTS `t1ger_mechanic`;
CREATE TABLE `t1ger_mechanic` (
	`id` INT(11) NOT NULL AUTO_INCREMENT,
	`name` VARCHAR(100) NOT NULL, -- Name of the shop
	`job` LONGTEXT NOT NULL DEFAULT ('[]'), -- Shop job settings
	`blip` LONGTEXT NOT NULL DEFAULT ('[]'), -- Blip for the shop
	`owner` VARCHAR(100) DEFAULT NULL, -- Identifier of the owner
	`account` INT(11) NOT NULL DEFAULT 0, -- Shop account
	`employees` LONGTEXT NOT NULL DEFAULT ('[]'), -- Employee data
	`markers` LONGTEXT NOT NULL DEFAULT ('[]'), -- Markers indexed by class
	`billing` LONGTEXT NOT NULL DEFAULT ('[]'), -- Billing
	`for_sale` TINYINT(1) NOT NULL DEFAULT 0, -- 1 = Listed for sale, 0 = Not for sale
	`sale_price` INT(11) DEFAULT NULL, -- Price set for the shop sale
	PRIMARY KEY (`id`)
);

DROP TABLE IF EXISTS `t1ger_carlifts`;
CREATE TABLE `t1ger_carlifts` (
	`id` INT(11) NOT NULL AUTO_INCREMENT,
	`coords` LONGTEXT NOT NULL,
	`rotation` LONGTEXT NOT NULL,
	PRIMARY KEY (`id`)
);

DROP TABLE IF EXISTS `t1ger_vehicledata`;
CREATE TABLE `t1ger_vehicledata` (
    `plate` VARCHAR(50) PRIMARY KEY,
    `mileage` FLOAT DEFAULT 0,
    `service_parts` LONGTEXT NOT NULL,
    `core_parts` LONGTEXT NOT NULL
);

DROP TABLE IF EXISTS `t1ger_servicehistory`;
CREATE TABLE `t1ger_servicehistory` (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `plate` VARCHAR(50) NOT NULL,
    `mileage` FLOAT DEFAULT 0,
    `part` VARCHAR(100) NOT NULL,
    `mechanic` VARCHAR(100) NOT NULL,
    `shop` VARCHAR(100) NOT NULL,
    `date` VARCHAR(10) NOT NULL,
	PRIMARY KEY (`id`)
);