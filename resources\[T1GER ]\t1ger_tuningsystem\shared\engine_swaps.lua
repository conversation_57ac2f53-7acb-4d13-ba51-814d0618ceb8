Config.EngineSwaps = {

    DefaultIcon = 'gear', -- default icon for listed engines in menu

	Hoist = { -- Hoist Configuration:
		model = 'imp_prop_engine_hoist_02a', -- hoist prop
        removeItem = true, -- set to false and item will not be removed on use. If true, item will be removed on use and re-added, when collecting the hoist.
        keybind = 47, -- keybind to handle hoist (place on ground)
        playSound = true, -- sound effects when attaching/detaching an engine to/from the hoist.
        smoothness = 150, -- number of counts the engine will go/up down (the higher the more smooth)
        delay = 10, -- delay in hoist handle (how fast should engine go(up down))
		boneId = 28422, -- boneId on player

        textUi = { -- textUi when carrying hoist:
            string = '[G] - Detach Hoist', -- string in textUi
            icon = 'location-crosshairs', -- icon 
            position = 'right-center', -- position
            style = {borderRadius = 0, backgroundColor = '#48BB78', color = 'white'} -- css
        },

		anim = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49}, -- carry animation
        offset = {
            ['carry'] = {pos = vector3(0.0, -0.5, -1.3), rot = vector3(-195.0, -180.0, 180.0)}, -- offset for carry animation with player
            ['engine'] = {pos = vector3(-0.2, -0.-1.10, 1.2), rot = vector3(0.0, 0.0, 0.0)}, -- offset for hoist hook attach point for engine
        },

        target = { -- do not change index[''] strings , do not change target.name. Only change icon, label and distance. 
            ['carry'] = {name = 'tuningsystem:target:carryHoist', icon = 'fa-solid fa-up-down-left-right', label = 'Carry Hoist', distance =  2.5}, -- carry target
            ['extract'] = {name = 'tuningsystem:target:extractEngine', icon = 'fa-solid fa-arrow-up-long', label = 'Extract Engine', distance =  2.0}, -- extract target
            ['attach'] = {name = 'tuningsystem:target:attachEngine', icon = 'fa-solid fa-link', label = 'Attach Engine', distance =  2.0}, -- attach target
            ['detach'] = {name = 'tuningsystem:target:detachEngine', icon = 'fa-solid fa-link-slash', label = 'Detach Engine', distance =  2.0}, -- detach target
            ['remove'] = {name = 'tuningsystem:target:removeHoist', icon = 'fa-solid fa-hand', label = 'Collect Hoist', distance =  2.0}, -- remove target
        }
	},

    -- target created on extracted engine to dump/delete the old engine prop:
    ExtractTarget = {
        label = 'Dump Engine', -- target label
        icon = 'fa-solid fa-trash-can', --target icon
        distance = 2.0 -- target distance
    },

    -- target created on vehicle at engien bone pos:
    VehicleTarget = { -- do not change index[''] strings , do not change target.name. Only change icon, label and distance. 
        ['access'] = {name = 'tuningsystem:target:accessVehicleEngine', icon = 'fa-solid fa-screwdriver', label = 'Access Engine Bay', distance =  5.0},
        ['complete'] = {name = 'tuningsystem:target:completeSwap', icon = 'fa-solid fa-screwdriver', label = 'Complete Engine Swap', distance =  5.0}
    },

    -- animation to access engien bay/complete engine swap
    RepairAnimation = {dict = 'mini@repair', name = 'fixing_a_player', blendIn = 2.0, blendOut = 2.0, duration = 3000, flags = 1},

    -- settings for point when extracting engine / installing new engine on the vehicle: 
    VehiclePoint = {
        distance = 5, -- distance for nearby function
        marker = {
            type = 20, -- type for the marker
            scale = vector3(0.25, 0.25, 0.25), -- scale for the marker
            color = { -- do not change the index[''] string names!
                ['default'] = vector4(194, 80, 80, 200), -- rgba color for default marker at engine on the vehicle
                ['success'] = vector4(63, 195, 128, 200), -- rgba color for marker and engine object outline when distance <= precision (success)
            }
        },
        precision = 0.15, -- distance from hoist hook to detach marker. The smaller the more precise player has to be before placing the hoist.
    },

    -- settings for point when attaching new engine to the hoist:
    EnginePoint = {
        distance = 5, -- distance for nearby function
        marker = {
            type = 30, -- type for the markers
            scale = vector3(0.1, 1.0, 1.2), -- scale for the markers
            color = { -- do not change the index[''] string names!
                ['engine'] = vector4(194, 80, 80, 200), -- rgba color for engine marker
                ['hoist'] = vector4(20, 20, 20, 200), -- rgba color for hoist marker
                ['success'] = vector4(63, 195, 128, 200), -- rgba color for engine marker when distance <= precision (success)
            }
        },
        precision = 0.15, -- distance from hoist marker to engine marker. The smaller the more precise player has to be before placing the hoist.
    }
}

-- the index number represent the vehicle class!
Config.Engines = { -- set icon to nil to use defaualt, else set your own icon.
    ['0'] = { -- Compacts Class
        {label = 'Stock Engine', stock = true, price = 1000, icon = nil}, -- requires 'gameName' in vehicles.meta to be specified!
        {label = 'Kanjo Engine', soundName = 'kanjo', price = 2500, icon = nil}, -- Dinka Kanjo
        {label = 'Club Engine', soundName = 'club', price = 3500, icon = nil}, -- BF Club
        {label = 'Issi Engine', soundName = 'issi3', price = 4500, icon = nil}, -- Weeny Issy Classic
    },
    ['1'] = { -- Sedans Class
        {label = 'Stock Engine', stock = true, price = 1000, icon = nil}, -- requires 'gameName' in vehicles.meta to be specified!
        {label = 'Fugitive Engine', soundName = 'fugitive', price = 4500, icon = nil}, -- Cheval Fugitive
        {label = 'Tailgater S Engine', soundName = 'tailgater2', price = 6000, icon = nil}, -- Obey Tailgater S
        {label = 'Schafter V12 Engine', soundName = 'schafter5', price = 7500, icon = nil}, -- Benefactor Schafter V12 (Armored)
    },
    ['2'] = { -- SUVs Class
        {label = 'Stock Engine', stock = true, price = 1000, icon = nil}, -- requires 'gameName' in vehicles.meta to be specified!
        {label = 'Baller ST-D Engine', soundName = 'baller8', price = 6000, icon = nil}, -- Gallivanter Baller ST-D
        {label = 'Contender Engine', soundName = 'contender', price = 7000, icon = nil}, -- Vapid Contender
        {label = 'Toros Engine', soundName = 'toros', price = 8000, icon = nil}, -- Pegassi Toros
        {label = 'Rebla GTS Engine', soundName = 'rebla', price = 10000, icon = nil}, -- Ubermacht Rebla GTS
    },
    ['3'] = { -- Coupes Class
        {label = 'Stock Engine', stock = true, price = 1000, icon = nil}, -- requires 'gameName' in vehicles.meta to be specified!
        {label = 'Sentinel XS Engine', soundName = 'sentinel', price = 4000, icon = nil}, -- Ubermacht Sentinel XS
        {label = 'Cognoscenti Engine', soundName = 'cogcabrio', price = 5000, icon = nil}, -- Cognoscenti Cabrio
        {label = 'Previon Engine', soundName = 'previon', price = 6000, icon = nil}, -- Karin Previon
    },
    ['4'] = { -- Muscle Class
        {label = 'Stock Engine', stock = true, price = 1000, icon = nil}, -- requires 'gameName' in vehicles.meta to be specified!
        {label = 'Blade Engine', soundName = 'blade', price = 4000, icon = nil}, -- Vapid Blade 
        {label = 'Yosemite Engine', soundName = 'yosemite', price = 5000, icon = nil}, -- Declasse Yosemite
        {label = 'Gauntlet Hellfire Engine', soundName = 'gauntlet4', price = 6000, icon = nil}, -- Bravado Gauntlet Hellfire
        {label = 'Dominator GT Engine', soundName = 'dominator9', price = 7000, icon = nil}, -- Vapid Dominator GT
        {label = 'BlackFin Engine', soundName = 'coquette3', price = 10000, icon = nil}, -- Invetero Coquette BlackFin
    },
    ['5'] = { -- Sports Classics Class
        {label = 'Stock Engine', stock = true, price = 1000, icon = nil}, -- requires 'gameName' in vehicles.meta to be specified!
        {label = 'Stirling GT Engine', soundName = 'feltzer3', price = 10000, icon = nil}, -- Benefactor Stirling GT
        {label = 'Casco Engine', soundName = 'casco', price = 8000, icon = nil}, -- Lampadatti Casco
        {label = 'Ardent Engine', soundName = 'ardent', price = 6000, icon = nil}, -- Ocelot Ardent
        
    },
    ['6'] = { -- Sports Class
        {label = 'Stock Engine', stock = true, price = 1000, icon = nil}, -- requires 'gameName' in vehicles.meta to be specified!
        {label = 'Drafter Engine', soundName = 'drafter', price = 6000, icon = nil}, -- Obey 8F Drafter
        {label = 'Comet S2 Engine', soundName = 'comet7', price = 7000, icon = nil}, -- Pfister Comet S2 Cabrio
        {label = 'Slagen GT Engine', soundName = 'schlagen', price = 8500, icon = nil}, -- Benefactor Schalgen GT
        {label = 'Schafter V12 Engine', soundName = 'schafter3', price = 9500, icon = nil}, -- Benefactor Schafter V12
    },
    ['7'] = { -- Super Class
        {label = 'Stock Engine', stock = true, price = 1000, icon = nil}, -- requires 'gameName' in vehicles.meta to be specified!
        {label = 'Turismo R Engine', soundName = 'turismor', price = 12500, icon = nil}, -- Grotti Turismo R
        {label = 'Zorrusso Engine', soundName = 'zorrusso', price = 15000, icon = nil}, -- Pegassi Zorrusso
        {label = 'XA-21 Engine', soundName = 'xa21', price = 18000, icon = nil}, -- Ocelat XA-21
    },
    ['8'] = { -- Motorcycles Class
        {label = 'Stock Engine', stock = true, price = 1000, icon = nil}, -- requires 'gameName' in vehicles.meta to be specified!
        {label = 'Sanctus Engine', soundName = 'sanctus', price = 4500, icon = nil}, -- LCC Sanctus
        {label = 'Gargoyle Engine', soundName = 'gargoyle', price = 6000, icon = nil}, -- Western Gargoyle
        {label = 'FCR 1000 Engine', soundName = 'fcr', price = 7500, icon = nil}, -- Pegassi FCR 1000
        {label = 'Shinobi Engine', soundName = 'shinobi', price = 8500, icon = nil}, -- Nagasaki Shinobi 
    },
    ['9'] = { -- Off Roads
        {label = 'Stock Engine', stock = true, price = 1000, icon = nil}, -- requires 'gameName' in vehicles.meta to be specified!
    },
    ['12'] = { -- Vans
        {label = 'Stock Engine', stock = true, price = 1000, icon = nil}, -- requires 'gameName' in vehicles.meta to be specified!
    },
}