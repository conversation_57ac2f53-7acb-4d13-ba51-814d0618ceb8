---Returns enabled repair types
---@param types table config table with repairTypes
---@return table repairTypes returns enabled repair types
local function GetEnabledRepairTypes(types)
    local repairTypes = {}
    for repairType, data in pairs(types) do
        if data.enable then
            repairTypes[#repairTypes+1] = tostring(repairType)
        end
    end
    return repairTypes
end

--- Checks if player is near the mission ped entity
---@param entity number ped entity handle
---@return boolean IsNearMissionPed `true` if near mission ped. `false` otherwise
local function IsNearMissionPed(entity)
    if IsEntityValid(entity) and GetEntityType(entity) ~= 0 then
        local driver = GetPedInVehicleSeat(entity, -1)
        if driver and driver ~= 0 then 
            if curMission.ped and DoesEntityExist(curMission.ped) and curMission.ped == driver then
                local pedCoords = GetEntityCoords(curMission.ped)
                if #(coords - pedCoords) < 1.4 then
                    return true
                end
            end
        end
    end
    return false
end

---Returns if player is near the fuel tank of the vehicle
---@param entity vehicle the vehicle entity handle
---@return boolean IsNearFuelTank `true` if near fuel tank. `false` otherwise
local function IsNearFuelTank(entity)
    local bones = {"petrolcap", "petroltank", "petroltank_l", "hub_lr", "engine"}

    --- Gets vehicle fuel bone index
    ---@param vehicle entity the handle of the vehicle entity
    ---@return integer|nil boneIndex the index of the found bone or nil
    local function GetVehicleFuelTankBoneIndex(vehicle)
        -- loop bones and return boneIndex
        for i = 1, #bones do
            local boneIndex = GetEntityBoneIndexByName(vehicle, bones[i])

            if boneIndex ~= -1 then
                return boneIndex
            end
        end
    end

    -- get bone index:
    local boneIndex = GetVehicleFuelTankBoneIndex(entity)
    if not boneIndex or boneIndex == -1 then
        print("this vehicle does not have a fuel bone. Please remove from your config...")
        return false
    end

    -- get fuel cap position:
	local fuelcapPosition = GetWorldPositionOfEntityBone(entity, boneIndex)

    -- compare distance:
    if fuelcapPosition and #(coords - fuelcapPosition) < 1.1 then
        curMission.repairPosition = fuelcapPosition
        return true
    end

    return false
end

---Returns if player is near the battery of the vehicle
---@param entity vehicle the vehicle entity handle
---@return boolean IsNearBattery `true` if near battery. `false` otherwise
local function IsNearBattery(entity)
    -- get vehicle model dimensions
    local min, max = GetModelDimensions(GetEntityModel(entity))

    -- bonnet position:
    local batteryPosition = GetOffsetFromEntityInWorldCoords(entity, 0.0, max.y + 0.10, 0.0)

    -- compare distance:
    if #(coords - batteryPosition) < 1.1 then
        curMission.repairPosition = GetEntityCoords(entity)
        return true 
    end

    return false
end

---Returns if player is near the wheel/tire of the vehicle
---@param entity vehicle the vehicle entity handle
---@param wheelData table table containing the specific wheel data
---@return boolean IsNearTire `true` if near battery. `false` otherwise
local function IsNearTire(entity, wheelData)
    -- get bone index:
    local boneIndex = GetEntityBoneIndexByName(entity, wheelData.bone)
    if not boneIndex or boneIndex == -1 then
        print("this vehicle does not have a wheel bone. Please remove from your config...")
        return false
    end

    -- get tire position:
	local tirePosition = GetWorldPositionOfEntityBone(entity, boneIndex)

    -- compare distance:
    if tirePosition and #(coords - tirePosition) < 1.1 then
        curMission.repairPosition = tirePosition
        return true
    end

    return false
end

--- Checks if player is near the mission vehicle entity
---@param entity number vehicle entity handle
---@return boolean IsNearMissionVehicle `true` if near mission vehicle. `false` otherwise
local function IsNearMissionVehicle(entity)
    if IsEntityValid(entity) and GetEntityType(entity) ~= 0 then
        if curMission and curMission.repairType then
            if curMission.repairType == "tire" then
                if curMission.randomWheel and IsNearTire(entity, curMission.randomWheel) then 
                    return true
                end
            elseif curMission.repairType == "battery" then
                if IsNearBattery(entity) then 
                    return true
                end
            elseif curMission.repairType == "fuel" then
                if IsNearFuelTank(entity) then 
                    return true
                end
            end
        end
    end
    return false
end

---Creates target on the mission vehicle
---@param missionVehicle entity vehicle handle
---@param missionType string type of mission
local function CreateMissionVehicleTarget(missionVehicle, missionType)
    local anims = {
        fuel = {dict = "timetable@gardener@filling_can", clip = "gar_ig_5_filling_can", flag = 1, blendIn = 2.0, blendOut = 8.0},
        battery = {dict = "mini@repair", clip = "fixing_a_player", flag = 1, blendIn = 1.0, blendOut = -1.0},
        tire = {dict = "anim@amb@clubhouse@tutorial@bkr_tut_ig3@", clip = "machinic_loop_mechandplayer", flag = 1, blendIn = 8.0, blendOut = -2.0}
    }

    local targetOptions = {
        {
            name = "t1ger_mechanic:"..missionType..":vehicle",
            icon = "fa-solid fa-toolbox",
            label = locale("target.roadsiderepair_"..missionType),
            canInteract = IsNearMissionVehicle,
            distance = 2.0,
            onSelect = function(entity)
                if not curMission.repairPosition then
                    TaskTurnPedToFaceEntity(player, entity, 1000)
                else
                    TaskTurnPedToFaceCoord(player, curMission.repairPosition.x, curMission.repairPosition.y, curMission.repairPosition.z, 1000)
                end
                Wait(1000)

                if missionType == "battery" then
                    SetVehicleDoorOpen(entity, 4, 0, 0)
                end

                local success = ProgressBar({
                    duration = Config.Missions.Types["roadsiderepair"].duration,
                    label = locale("progressbar.roadsiderepair_"..missionType),
                    useWhileDead = false,
                    canCancel = true,
                    anim = anims[missionType],
                    disable = {
                        move = true,
                        combat = true
                    }
                })

                if not success then return end

                -- remove item:
                TriggerServerEvent("t1ger_mechanic:server:removeItem", curMission.repairItem, 1)

                -- fix:
                if missionType == "tire" then
                    SetVehicleTyreFixed(entity, curMission.randomWheel.index)
                    SetVehicleFixed(entity)
                elseif missionType == "battery" then
                    SetVehicleEngineOn(entity, true, true, false)
                    SetVehicleDoorShut(entity, 4, 1, 1)
                elseif missionType == "fuel" then
                    _API.SetVehicleFuel(entity, 100.0)
                    curMission.repaired = true
                end
                _API.Target.RemoveLocalEntity(entity, {names = {"t1ger_mechanic:"..missionType..":vehicle"}, labels = {locale("target.roadsiderepair_"..missionType)}})
                curMission.repaired = true

                -- notification
                _API.ShowNotification(locale("notification.roadsiderepair_fixed"), "inform", {})
            end
        },
        {
            name = "t1ger_mechanic:"..missionType..":ped",
            icon = "fa-solid fa-comment",
            label = locale("target.missions_ped_interact"),
            canInteract = IsNearMissionPed,
            distance = 1.4,
            onSelect = function(entity)
                TaskTurnPedToFaceEntity(player, entity, -1)
                Wait(1000)

                if not curMission.repaired then
                    _API.ShowNotification(locale("notification.roadsiderepair_cannot_pay"), "inform", {})
                    ClearPedTasks(player)
                else
                    if curMission.collected then return end
                    curMission.collected = true
                    -- play anim on player:
                    local taskAnim = {dict = "mp_common", clip = "givetake2_a", flag = 49, blendIn = 4.0, blendOut = -4.0}
                    lib.requestAnimDict(taskAnim.dict)
                    TaskPlayAnim(player, taskAnim.dict, taskAnim.clip, taskAnim.blendIn, taskAnim.blendOut, -1, taskAnim.flag, 1, 0, 0, 0)
                    Wait(2000)

                    -- remove target:
                    _API.Target.RemoveLocalEntity(curMission.vehicle, {names = {"t1ger_mechanic:"..missionType..":ped"}, labels = {locale("target.missions_ped_interact")}})

                    -- give money:
                    local vehicleCoords = GetEntityCoords(entity)
                    TriggerServerEvent("t1ger_mechanic:server:missionPayout", curMission.type, curMission.index, vehicleCoords)
                    ClearPedTasks(player)

                    -- remove blip:
                    if DoesBlipExist(curMission.blip) then 
                        RemoveBlip(curMission.blip)
                    end

                    -- cleanup:
                    Wait(500)
                    SetVehicleCanBeUsedByFleeingPeds(curMission.vehicle, true)
                    TaskVehicleDriveWander(curMission.ped, curMission.vehicle, 80.0, 786603)
                    Wait(3000)
                    TaskSmartFleePed(curMission.ped, player, 40.0, 20000)
                    Wait(4000)
                    curMission.cancel = true
                end
            end
        }
    }

    -- create target:
    _API.Target.AddLocalEntity(missionVehicle, targetOptions)
end

--- RoadsideRepair mission
function RoadsideRepair()
    local mission = Config.Missions.Types[curMission.type]
    local location = Config.Missions.Locations[curMission.type][curMission.index]
    local complete = false

    -- create blip for npc vehicle:
    curMission.blip = CreateMissionBlip(location.pos, mission.blip)

    -- get enabled repair types:
    local repairTypes = GetEnabledRepairTypes(mission.types)
    if not repairTypes or next(repairTypes) == nil then
        return error("[RoadsideRepair] No enabled repair types. Fix your Config.Missions!")
    end

    -- randomize repair type:
    math.randomseed(GetGameTimer())
    curMission.repairType = repairTypes[math.random(#repairTypes)]

    -- required item:
    curMission.repairItem = mission.types[curMission.repairType].item
    local itemLabel = _API.Inventory.GetItemLabel(curMission.repairItem)

    -- advanced notification:
    if Config.Missions.UseAdvancedNotify then
        local notifyIcon = Config.Missions.NotifyIcon
        local title, subtitle, message = locale("advanced_notification.roadsiderepair_title"), mission.types[curMission.repairType].subtitle, locale("advanced_notification.roadsiderepair_message")
        _API.ShowAdvancedNotification(notifyIcon, notifyIcon, 6, title, false, subtitle, message)
    else
        local string = locale("advanced_notification.roadsiderepair_title").."\n\n"..mission.types[curMission.repairType].subtitle.."\n\n"..locale("advanced_notification.roadsiderepair_message")
        -- Calculate dynamic duration (e.g., 50ms per character)
        local perChar = 60
        local minDuration = 3000
        local maxDuration = 10000
        local dynamicDuration = math.min(maxDuration, math.max(minDuration, #string * perChar))
        _API.ShowNotification(string, "inform", {duration = dynamicDuration})
    end

    --- loop
    while not complete do
        Wait(1)

        if Config.Missions.Locations[curMission.type][curMission.index].inUse then

            local missionDist = #(coords - vector3(location.pos.x, location.pos.y, location.pos.z))

            -- create mission npc vehicle:
            if missionDist < 100.0 and not curMission.vehicle then
				curMission.vehicle = CreateMissionVehicle(location.pos)
				SetEntityAsMissionEntity(curMission.vehicle, true, true)

                -- set vehicle effects:
                if curMission.repairType == "tire" then
                    curMission.randomWheel = BurstRandomTire(curMission.vehicle)
                elseif curMission.repairType == "battery" then
                    SetVehicleEngineOn(curMission.vehicle, false, true, true)
                elseif curMission.repairType == "fuel" then
                    _API.SetVehicleFuel(curMission.vehicle, 0.0)
                end
            end

            -- create mission npc ped:
            if missionDist < 90.0 and curMission.vehicle and DoesEntityExist(curMission.vehicle) and not curMission.ped then
				curMission.ped = CreateMissionPedInsideVehicle(curMission.vehicle)
				SetEntityAsMissionEntity(curMission.ped, true, true)

                -- create target:
                CreateMissionVehicleTarget(curMission.vehicle, curMission.repairType)
            end

        end

        if curMission.cancel then
            complete = true
        end

    end
    
    -- clean up entities and blip
    if DoesEntityExist(curMission.vehicle) then DeleteVehicle(curMission.vehicle) end
    if DoesEntityExist(curMission.ped) then DeleteEntity(curMission.ped) end
    if DoesBlipExist(curMission.blip) then RemoveBlip(curMission.blip) end
    
    -- sync inUse state:
    TriggerServerEvent("t1ger_mechanic:server:missionInUse", curMission.type, curMission.index, false)

    curMission = {}
end

---Bursts a random tire on the vehicle and returns the affected random wheel data
---@param vehicle entity the entity handle
---@return table randomWheel returns the found random wheel data
function BurstRandomTire(vehicle)
    -- get number of vehicle wheels:
    local numWheels = tostring(GetVehicleNumberOfWheels(vehicle))

    -- vehicle wheels data:
    local vehicleWheels = _API.VehicleWheels[numWheels]

    -- Convert keys to a simple array to pick a random one
    local keys = {}
    for k in pairs(vehicleWheels) do
        table.insert(keys, k)
    end

    -- Pick a random wheel
    math.randomseed(GetGameTimer())
    local randomKey = keys[math.random(1, #keys)]
    local randomWheel = vehicleWheels[randomKey]
    
    -- burst tire
    SetVehicleTyreBurst(vehicle, randomWheel.index, true, 1000.0)

    return randomWheel
end