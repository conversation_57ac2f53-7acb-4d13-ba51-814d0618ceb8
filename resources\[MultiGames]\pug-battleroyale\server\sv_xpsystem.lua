local function AwaitQuery(query, params)
    local p = promise.new()
    MySQL.query(query, params, function(result)
        p:resolve(result)
    end)
    return Citizen.Await(p)
end

local function AwaitUpdate(query, params)
    local p = promise.new()
    MySQL.update(query, params, function(result)
        p:resolve(result)
    end)
    return Citizen.Await(p)
end

-- Ensure XP row exists for citizenid
local function EnsureXPRow(citizenid)
    local result = AwaitQuery('SELECT 1 FROM pug_battleroyale_rankstats WHERE citizenid = ? LIMIT 1', { citizenid })
    if not result or not result[1] then
        AwaitUpdate('INSERT INTO pug_battleroyale_rankstats (citizenid, xp, unlocks) VALUES (?, 0, 0)', { citizenid })
    end
end

-- Get a player's XP
function GetPlayerXP(citizenid)
    local result = AwaitQuery('SELECT xp FROM pug_battleroyale_rankstats WHERE citizenid = ?', { citizenid })
    if result and result[1] then
        return result[1].xp or 0
    end
    return 0
end

-- Add XP to a player
function AddPlayerXP(playerSrc, amount)
    local _, _, citizenid = GetCharacterInfo(playerSrc)
    if not citizenid then return end

    EnsureXPRow(citizenid)
    AwaitUpdate('UPDATE pug_battleroyale_rankstats SET xp = xp + ? WHERE citizenid = ?', { amount, citizenid })

    TriggerClientEvent("Pug:client:BattleRoyaleNotify", playerSrc, ("You gained %d XP!"):format(amount), "success")
end

-- Add XP for a kill
function AwardKillXP(playerSrc)
    local amount = math.random(Config.PlayerXP.PerKill.Min, Config.PlayerXP.PerKill.Max)
    AddPlayerXP(playerSrc, amount)
end

-- Add XP for surviving
function AwardSurviveXP(playerSrc)
    local amount = math.random(Config.PlayerXP.PerSurviving.Min, Config.PlayerXP.PerSurviving.Max)
    AddPlayerXP(playerSrc, amount)
end

-- Add XP for placement win
function AwardPlacementXP(playerSrc, place)
    local min, max = 0, 0
    if place == 1 then
        min, max = Config.PlayerXP.PerWin.FirstPlaceMin, Config.PlayerXP.PerWin.FirstPlaceMax
    elseif place == 2 then
        min, max = Config.PlayerXP.PerWin.SecondPlaceMin, Config.PlayerXP.PerWin.SecondPlaceMax
    elseif place == 3 then
        min, max = Config.PlayerXP.PerWin.ThirdPlaceMin, Config.PlayerXP.PerWin.ThirdPlaceMax
    else
        return
    end

    local amount = math.random(min, max)
    AddPlayerXP(playerSrc, amount)
end

-- Get a player's unlocks
function GetPlayerUnlocks(citizenid)
    local result = AwaitQuery('SELECT unlocks FROM pug_battleroyale_rankstats WHERE citizenid = ?', { citizenid })
    if result and result[1] then
        return result[1].unlocks or 0
    end
    return 0
end

-- Set a player's unlocks
function SetPlayerUnlocks(playerSrc, amount)
    local _, _, citizenid = GetCharacterInfo(playerSrc)
    if not citizenid then return end

    EnsureXPRow(citizenid)
    AwaitUpdate('UPDATE pug_battleroyale_rankstats SET unlocks = ? WHERE citizenid = ?', { amount, citizenid })

    TriggerClientEvent("Pug:client:BattleRoyaleNotify", playerSrc, ("You now have %d unlocks!"):format(amount), "info")
end