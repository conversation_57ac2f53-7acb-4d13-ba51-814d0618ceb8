import{u as v,r as I,G as W,q as u,s as N,a as e,F as O,j as i,L as t,m as A,c1 as Ee,k as J,bm as pe,C as g,c2 as ue,ax as he,bQ as ve,b as _,K as Ce,a0 as w,I as Ie,a5 as X,f as y,a2 as Ne,d as ge,bI as $,a7 as be,aa as re,v as te,V as fe,ab as ne,ac as Ae,A as B,o as q,P as D,x as Oe,e as se,g as Re,h as le,E as ie,J as Y,i as de,bl as me,c3 as Le,c4 as oe,O as Se,aB as Te,S as Pe,a1 as ye,y as De,c5 as Me,b0 as Ve}from"./index-99e0aeb1.js";import{S as ce}from"./Switch-4e23059b.js";const U={companies:[{job:"police",name:"Police",icon:"https://cdn-icons-png.flaticon.com/512/7211/7211100.png",canCall:!0,canMessage:!0,bossRanks:["boss","lieutenant"],open:!0,location:{name:"Mission Row",coords:{x:428.9,y:-984.5}}},{job:"ambulance",name:"Ambulance",icon:"https://cdn-icons-png.flaticon.com/128/1032/1032989.png",canCall:!0,canMessage:!0,bossRanks:["boss","doctor"],open:!0,location:{name:"Pillbox",coords:{x:304.2,y:-587}}},{job:"mechanic",name:"Mechanic",icon:"https://cdn-icons-png.flaticon.com/128/10281/10281554.png",canCall:!0,canMessage:!0,bossRanks:["boss","worker"],open:!1,location:{name:"LS Customs",coords:{x:-336.6,y:-134.3}}},{job:"taxi",name:"Taxi",icon:"https://cdn-icons-png.flaticon.com/128/433/433449.png",canCall:!0,canMessage:!0,bossRanks:["boss","driver"],open:!1,location:{name:"Taxi HQ",coords:{x:984.2,y:-219}}}],company:{job:"police",duty:!0,receiveCalls:!0,isBoss:!0,balance:1e6,employees:[{id:1,name:"Breze",grade:1,gradeLabel:"Officer",canInteract:!0,online:!0},{id:2,name:"Loaf Scripts",grade:2,gradeLabel:"Sergeant",canInteract:!0,online:!0},{id:3,name:"Kash",grade:3,gradeLabel:"Lieutenant",canInteract:!0,online:!1}],grades:[{label:"Officer",grade:1},{label:"Sergeant",grade:2},{label:"Lieutenant",grade:3},{label:"Captain",grade:4}]},messageList:[{id:"1",lastMessage:"Is anyone available to help me?",sender:"Breze",number:"**********",company:{name:"Police",icon:"https://cdn-icons-png.flaticon.com/512/7211/7211100.png"},timestamp:Date.now()-1e3*60*60*2},{id:"2",lastMessage:"Hello, how can i help you?",sender:"Loaf Scripts",number:"**********",company:{name:"Police",icon:"https://cdn-icons-png.flaticon.com/512/7211/7211100.png"},timestamp:Date.now()-1e3*60*60*4},{id:"3",lastMessage:"Hey, i need backup at the bank",sender:"Sally",number:"**********",company:{name:"Police",icon:"https://cdn-icons-png.flaticon.com/512/7211/7211100.png"},timestamp:Date.now()-1e3*60*60*24*2}],messages:{1:[{id:"1",sender:"**********",content:"Is anyone available to help me?",timestamp:Date.now()-1e3*60*60*2},{id:"2",sender:"**********",content:"Hey, a unit is on its way to your location",timestamp:Date.now()-1e3*60*60*24*2},{id:"3",sender:"**********",content:"i'd like to report a crime, i'm at the pier",timestamp:Date.now()-1e3*60*60*24*2}],2:[{id:"1",sender:"**********",content:"Hey, can you help me?",timestamp:Date.now()-1e3*60*60*4},{id:"2",sender:"**********",content:"I need a unit to my location",timestamp:Date.now()-1e3*60*60*24*5},{id:"3",sender:"**********",content:"I need backup at the bank",timestamp:Date.now()-1e3*60*60*24*9}],3:[{id:"1",sender:"**********",content:"is there any way you have any job positions available?",timestamp:Date.now()-1e3*60*60*24*2},{id:"2",sender:"**********",content:"hello?",timestamp:Date.now()-1e3*60*60*24*4},{id:"3",sender:"**********",content:"hey, i have a question",timestamp:Date.now()-1e3*60*60*24*5}]},employees:[{firstname:"John",lastname:"Doe",grade:1,gradeLabel:"Officer",number:"**********",online:!0},{firstname:"Loaf",lastname:"Scripts",grade:2,gradeLabel:"Sergeant",number:"**********",online:!0},{firstname:"Kash",grade:3,gradeLabel:"Lieutenant",number:"**********",online:!1},{firstname:"Sally",grade:4,gradeLabel:"Captain",number:"**********",online:!0}]};function we(){var S,E,o,p,h,f,s,P,T,k,K,z,Q;const a=v(_),n=v(C),[l,m]=I.useState(null);I.useEffect(()=>{W("Services")&&u("Services",{action:"getCompany"},U.company).then(d=>{if(!d)return N("warning","Failed to fetch company data");C.set(d)})},[]);const c=()=>{g.PopUp.set({title:t("APPS.SERVICES.DEPOSIT_POPUP.TITLE"),description:t("APPS.SERVICES.DEPOSIT_POPUP.DESCRIPTION"),input:{placeholder:t("APPS.SERVICES.DEPOSIT_POPUP.AMOUNT"),onChange:d=>{m(d)},type:"number"},buttons:[{title:t("APPS.SERVICES.DEPOSIT_POPUP.CANCEL")},{title:t("APPS.SERVICES.DEPOSIT_POPUP.PROCEED"),cb:()=>{m(d=>{/^\d+$/.test(d)&&u("Services",{action:"depositMoney",amount:parseInt(d)},parseInt(d)+n.balance).then(b=>{if(typeof b!="number")return N("warning","Balance is not a number, not updating");C.patch({balance:b})})})}}]})},r=()=>{g.PopUp.set({title:t("APPS.SERVICES.WITHDRAW_POPUP.TITLE"),description:t("APPS.SERVICES.WITHDRAW_POPUP.DESCRIPTION"),input:{placeholder:t("APPS.SERVICES.WITHDRAW_POPUP.AMOUNT"),onChange:d=>{m(d)},type:"number"},buttons:[{title:t("APPS.SERVICES.WITHDRAW_POPUP.CANCEL")},{title:t("APPS.SERVICES.WITHDRAW_POPUP.PROCEED"),cb:()=>{m(d=>{if(/^\d+$/.test(d)){if(n.balance-parseInt(d)<0){g.PopUp.set({title:t("APPS.SERVICES.WITHDRAW_POPUP.TITLE"),description:t("APPS.SERVICES.WITHDRAW_POPUP.NOT_ENOUGH_MONEY").format({amount:d}),buttons:[{title:t("APPS.SERVICES.WITHDRAW_POPUP.PROCEED")}]});return}u("Services",{action:"withdrawMoney",amount:parseInt(d)},n.balance-parseInt(d)).then(b=>{if(typeof b!="number")return N("warning","Balance is not a number, not updating");C.patch({balance:b})})}})}}]})};return e(O,{children:n!=null&&n.job?i("div",{className:"actions-container",children:[e("div",{className:"category-title",children:t("APPS.SERVICES.SETTINGS")}),e("section",{children:i(O,{children:[n.duty!==void 0&&((E=(S=a==null?void 0:a.Companies)==null?void 0:S.Management)==null?void 0:E.Duty)!==!1&&i(A.div,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},exit:{opacity:0,scale:.9},className:"item",children:[i("div",{className:"item-data",children:[e(Ee,{className:"orange"}),i("div",{className:"item-info",children:[e("div",{className:"title",children:t("APPS.SERVICES.DUTY")}),e("div",{className:"description",children:t("APPS.SERVICES.DUTY_DESCRIPTION")})]})]}),e("div",{className:"item-value",children:e(ce,{checked:n.duty,onChange:()=>{u("Services",{action:"toggleDuty",duty:!n.duty},!0).then(()=>{var d;return C.patch({duty:!((d=C.value)!=null&&d.duty)})})}})})]}),(n.duty===void 0||n.duty===!0)&&i(A.div,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},exit:{opacity:0,scale:.9},className:"item",children:[i("div",{className:"item-data",children:[e(J,{className:"green"}),i("div",{className:"item-info",children:[e("div",{className:"title",children:t("APPS.SERVICES.JOB_CALLS")}),i("div",{className:"description",children:[" ",t("APPS.SERVICES.JOB_CALLS_DESCRIPTION").format({toggle:n.receiveCalls?t("APPS.SERVICES.DISABLE"):t("APPS.SERVICES.ENABLE")})]})]})]}),e("div",{className:"item-value",children:e(ce,{checked:n.receiveCalls,onChange:()=>{u("Services",{action:"toggleCalls"},!n.receiveCalls).then(d=>{C.patch({receiveCalls:d})})}})})]})]})}),n.isBoss&&i(O,{children:[e("div",{className:"category-title",children:t("APPS.SERVICES.ACTIONS")}),i("section",{children:[i(A.div,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},exit:{opacity:0,scale:.9},className:"item",children:[i("div",{className:"item-data",children:[e(pe,{className:"green"}),i("div",{className:"item-info",children:[e("div",{className:"title",children:t("APPS.SERVICES.BALANCE")}),e("div",{className:"description",children:"Current balance of the company"})]})]}),e("div",{className:"item-value",children:a.CurrencyFormat.replace("%s",(o=n.balance)==null?void 0:o.toLocaleString())})]}),(((h=(p=a==null?void 0:a.Companies)==null?void 0:p.Management)==null?void 0:h.Deposit)!==!1||((s=(f=a==null?void 0:a.Companies)==null?void 0:f.Management)==null?void 0:s.Withdraw)!==!1)&&i("div",{className:"item",children:[((T=(P=a==null?void 0:a.Companies)==null?void 0:P.Management)==null?void 0:T.Deposit)!==!1&&e("div",{className:"button",onClick:c,children:t("APPS.SERVICES.DEPOSIT_POPUP.TITLE")}),((K=(k=a==null?void 0:a.Companies)==null?void 0:k.Management)==null?void 0:K.Withdraw)!==!1&&e("div",{className:"button",onClick:r,children:t("APPS.SERVICES.WITHDRAW_POPUP.TITLE")})]})]}),e("div",{className:"category-title",children:t("APPS.SERVICES.MANAGE_EMPLOYEES")}),i("section",{children:[((Q=(z=a==null?void 0:a.Companies)==null?void 0:z.Management)==null?void 0:Q.Hire)!==!1&&i(A.div,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},exit:{opacity:0,scale:.9},className:"item",onClick:()=>{g.PopUp.set({title:t("APPS.SERVICES.HIRE_POPUP.TITLE"),description:t("APPS.SERVICES.HIRE_POPUP.DESCRIPTION"),input:{type:"number",placeholder:t("APPS.SERVICES.HIRE_POPUP.PLACEHOLDER"),onChange:d=>{m(d)}},buttons:[{title:t("APPS.SERVICES.HIRE_POPUP.CANCEL")},{title:t("APPS.SERVICES.HIRE_POPUP.PROCEED"),cb:()=>{m(d=>{if(/^\d+$/.test(d))return u("Services",{action:"hireEmployee",source:parseInt(d)},{id:d,name:"Joe Doe"}).then(b=>{var x,V;b&&C.patch({employees:[...(x=C.value)==null?void 0:x.employees,{id:b.id,name:b.name,grade:0,gradeLabel:(V=C.value)==null?void 0:V.grades[0].label,canInteract:!0}]})}),""})}}]})},children:[i("div",{className:"item-data",children:[e(ue,{className:"blue"}),i("div",{className:"item-info",children:[e("div",{className:"title",children:t("APPS.SERVICES.HIRE")}),e("div",{className:"description",children:t("APPS.SERVICES.HIRE_POPUP.DESCRIPTION")})]})]}),e("div",{className:"item-value",children:e(he,{})})]}),n.employees.map((d,b)=>i(A.div,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},exit:{opacity:0,scale:.9},className:"item",onClick:()=>{var V,Z,ee,ae;if(!d.canInteract||a.Companies.Management.Fire===!1&&a.Companies.Management.Promote===!1)return;const x=[((Z=(V=a==null?void 0:a.Companies)==null?void 0:V.Management)==null?void 0:Z.Promote)!==!1&&{title:t("APPS.SERVICES.SET_GRADE"),cb:()=>{setTimeout(()=>{g.ContextMenu.set({buttons:[...n.grades.map(R=>({title:R.label,cb:()=>{u("Services",{action:"setGrade",employee:d.id,grade:R.grade},!0).then(H=>{var j;if(!H)return N("warning","Failed to set grade");C.patch({employees:(j=C.value)==null?void 0:j.employees.map(F=>(F.id===d.id&&(F.grade=R.grade,F.gradeLabel=R.label),F))})})}}))]})},300)}},((ae=(ee=a==null?void 0:a.Companies)==null?void 0:ee.Management)==null?void 0:ae.Fire)!==!1&&{title:t("APPS.SERVICES.FIRE_POPUP.TITLE"),color:"red",cb:()=>{g.PopUp.set({title:t("APPS.SERVICES.FIRE_POPUP.TITLE"),description:t("APPS.SERVICES.FIRE_POPUP.DESCRIPTION").format({name:d.name}),buttons:[{title:t("APPS.SERVICES.FIRE_POPUP.CANCEL")},{title:t("APPS.SERVICES.FIRE_POPUP.PROCEED"),color:"red",cb:()=>{u("Services",{action:"fireEmployee",employee:d.id},!0).then(R=>{var H;if(!R)return N("warning","Failed to fire employee");C.patch({employees:(H=C.value)==null?void 0:H.employees.filter(j=>j.id!==d.id)})})}}]})}}];g.ContextMenu.set({buttons:x.filter(R=>R)})},children:[i("div",{className:"item-data",children:[e(ve,{}),i("div",{className:"item-info",children:[e("div",{className:"title",children:d.name}),i("div",{className:"description",children:[" ",d.gradeLabel]})]})]}),e("div",{className:"item-value",children:e("div",{className:"status","data-online":d.online})})]},b))]})]})]}):e("div",{className:"no-job",children:t("APPS.SERVICES.UNEMPLOYED")})})}function _e(){const a=v(D.Settings),n=v(D.PhoneNumber),l=v(L),[m,c]=I.useState([]),r=I.useRef(null),[S,E]=I.useState({content:"",attachments:[]});I.useEffect(()=>{W("Services")&&u("Services",{action:"getMessages",page:0,id:l.id,company:l.job},U.messages[l.id]).then(s=>{if(s&&s.length>0){c([...s.reverse()]);let P=document.querySelector(".chat-container");P.scrollTop=P.scrollHeight}})},[]);const o=()=>{if(S.content.length>0){let s={sender:n,id:l.id,company:l.job,content:S.content,timestamp:new Date};if(!re())return c(P=>[...P,{...s,delivered:!1}]);u("Services",{action:"sendMessage",...s},!0).then(P=>{P||N("error","Failed to send message"),c(T=>[...T,{...s,delivered:P}]),E({content:"",attachments:[]}),r.current.value=""})}},p=()=>{g.PopUp.set({title:t("APPS.MESSAGES.SEND_LOCATION_POPUP.TITLE"),description:t("APPS.MESSAGES.SEND_LOCATION_POPUP.TEXT"),buttons:[{title:t("APPS.MESSAGES.SEND_LOCATION_POPUP.CANCEL")},{title:t("APPS.MESSAGES.SEND_LOCATION_POPUP.SEND"),cb:()=>{u("Maps",{action:"getCurrentLocation"},{x:"0",y:"0"}).then(s=>{if(!s)return N("error","Failed to get location");let P={sender:n,content:`<!SENT-LOCATION-X=${te(s.x,2)}Y=${te(s.y,2)}!>`,attachments:[],id:l.id,company:l.job,timestamp:new Date};u("Services",{action:"sendMessage",...P},!0).then(T=>{c(k=>[...k,T?P:{...P,delivered:!1}])})})}}]})},{handleScroll:h}=Ce({fetchData:s=>u("Services",{action:"getMessages",id:l.id,company:l.job,page:s}),onDataFetched:s=>c([...s.reverse(),...m]),isReversed:!0,perPage:25});w("services:newMessage",s=>{if(l.id!==s.channelId||s.sender===n)return;c([...m,{...s,timestamp:new Date}]);let P=document.querySelector(".chat-container");P.scrollTop=P.scrollHeight},{waitUntilService:!0});const f=s=>{if(s)return/<!SENT-LOCATION-X=(-?\d*\.?\d*)Y=(-?\d*\.?\d*)!>/.test(s)};return i(O,{children:[i("div",{className:"chat-header",children:[e("div",{className:"back",onClick:()=>{M.set("messages"),L.reset()},children:e(Ie,{})}),i("div",{className:"user",children:[e(X,{src:l.icon??`./assets/img/avatar-placeholder-${a.display.theme}.svg`,className:"avatar"}),e("div",{className:"name",children:l.company??y(l.number)})]}),e(Ne,{className:"hidden"})]}),e("div",{className:"chat-container",onScroll:h,children:e("div",{className:"chat-body",children:m.map((s,P)=>e(Ue,{index:P,messages:m,message:s,func:{isLocation:f}}))})}),e("div",{className:"chat-bottom",children:i("div",{className:"input",children:[e(ge,{placeholder:t("APPS.MESSAGES.PLACEHOLDER"),ref:r,value:S.content,onChange:s=>{E({content:s.target.value,attachments:S.attachments})},onKeyDown:s=>{s.key=="Enter"&&o()}}),i("div",{className:"buttons",children:[e("div",{className:"location",children:e($,{onClick:p})}),S.content.length>0&&e("div",{className:"send",onClick:o,children:e(be,{})})]})]})})]})}const Ue=({messages:a,message:n,index:l,func:m})=>{var f;const c=v(D.PhoneNumber),r=v(L);let S,E,o,p=n.sender===c?"self":"other",h=((f=a[l+1])==null?void 0:f.sender)===c?"self":"other";if(a[l+1]?E=Math.abs(n.timestamp-a[l+1].timestamp)/36e5:h=void 0,m.isLocation(n.content)){let s=n.content.match(/X=(-?\d*\.?\d*)Y/)[1],P=n.content.match(/Y=(-?\d*\.?\d*)!>/)[1];o={x:s,y:P}}return i("div",{className:fe("message",p),"data-id":n.id,children:[S,n.delivered===!1?i("div",{className:"content-wrapper",children:[e("div",{className:"content",children:ne(n.content)}),e(Ae,{})]}):o?i("div",{className:"location",onClick:()=>{B.patch({active:{name:"Maps",data:{location:[o.y,o.x],name:`${y(n.sender)}'s location`,icon:r.icon}}})},children:[e("div",{className:"img",style:{backgroundImage:'url("https://img.gta5-mods.com/q95/images/mirror-park-garden/2b72f9-20160428154103_1.jpg")'}}),p==="other"&&i("div",{className:"sender",children:[y(n.sender)," ",t("APPS.MESSAGES.SENT_LOCATION")]})]}):n.content&&e("div",{className:"content",children:ne(n.content)}),n.delivered===!1?e("div",{className:"status",children:t("APPS.MESSAGES.NOT_DELIVERED")}):a[l+1]&&E>6?e("div",{className:"date",children:q(n.timestamp)}):p!==h&&e("div",{className:"date",children:q(n.timestamp)})]},l)},G=Y(null);function ke(){var S,E;const a=v(G),[n,l]=I.useState("all"),[m,c]=I.useState(""),r=o=>{if(!o)return N("error","No number provided");g.PopUp.set({title:t("APPS.SERVICES.CALL_POPUP.TITLE"),description:t("APPS.SERVICES.CALL_POPUP.DESCRIPTION").format({name:y(o)}),buttons:[{title:t("APPS.SERVICES.CALL_POPUP.CANCEL")},{title:t("APPS.SERVICES.CALL_POPUP.PROCEED"),cb:()=>{let p=ie(o);de({...p,number:o})}}]})};return i(A.div,{initial:{opacity:0,y:250},animate:{opacity:1,y:0},exit:{opacity:0,y:250},transition:{duration:.2,ease:"easeInOut"},className:"employee-list",children:[i("div",{className:"employee-list-header",children:[e("div",{className:"close",onClick:()=>G.set(null)}),e("div",{className:"selector",children:["all","available"].map((o,p)=>e("div",{className:"option","data-active":n===o,onClick:()=>l(o),children:t(`APPS.SERVICES.${o.toUpperCase()}`)},p))}),e(Oe,{placeholder:t("APPS.SERVICES.SEARCH_EMPLOYEES"),onChange:o=>c(o.target.value)})]}),e("div",{className:"employee-list-body",children:(E=(S=a==null?void 0:a.filter(o=>{var p;return(n==="all"?!0:o.online)&&((p=se(o.firstname,o.lastname))==null?void 0:p.toLowerCase().includes(m==null?void 0:m.toLowerCase()))}))==null?void 0:S.sort((o,p)=>p.grade-o.grade))==null?void 0:E.map((o,p)=>i(A.div,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},viewport:{once:!0},className:"employee-item",children:[i("div",{className:"user",children:[e("div",{className:"avatar",children:Re(o.firstname,o.lastname)}),i("div",{className:"info",children:[se(o.firstname,o.lastname),e("span",{children:o.gradeLabel})]})]}),e("div",{className:"buttons",children:i(O,{children:[e(J,{className:o.online&&o.number?"green":"red",onClick:()=>r(o.number)}),e(le,{className:o.online&&o.number?"green":"red",onClick:()=>{if(!o.number)return N("error","No number provided");let h=ie(o.number);B.patch({active:{name:"Messages",data:{number:o.number,name:h==null?void 0:h.name,avatar:h==null?void 0:h.avatar,view:"messages"}}})}})]})})]},p))})]})}function xe(){var m;const[a,n]=I.useState([]),l=(m=v(B))==null?void 0:m.active;return I.useEffect(()=>{W("Services")&&u("Services",{action:"getCompanies"},U.companies).then(c=>{if(!c)return N("warning","Failed to fetch companies");n(c)})},[l]),w("services:updateOpen",c=>{if(!c)return;const{job:r,open:S}=c;n(E=>{const o=E.findIndex(p=>p.job===r);return o===-1?E:(E[o].open=S,[...E])})}),e(O,{children:e("div",{className:"companies-list",children:a.sort((c,r)=>c.open&&!r.open?-1:!c.open&&r.open?1:0).map(c=>e(He,{company:c},c.name))})})}const He=({company:a})=>{var o,p,h,f;const n=v(_),l=()=>{u("Services",{action:"getEmployees",company:a.job},U.employees).then(s=>{if(!s)return N("warning","You do not have permission to view employees.");G.set(s)})},[m,c]=I.useState(!1),r=s=>{var P;!((P=s==null?void 0:s.location)!=null&&P.coords)||!s.name||B.patch({active:{name:"Maps",data:{location:[s.location.coords.y,s.location.coords.x],name:s.name,icon:s.icon}}})},S=(s,P)=>{n.Companies.Enabled&&g.PopUp.set({title:t("APPS.SERVICES.CALL_POPUP.TITLE"),description:t("APPS.SERVICES.CALL_POPUP.DESCRIPTION").format({name:Te(s)}),buttons:[{title:t("APPS.SERVICES.CALL_POPUP.CANCEL")},{title:t("APPS.SERVICES.CALL_POPUP.PROCEED"),cb:()=>{de({company:s,companylabel:P})}}]})},E=s=>{u("Services",{action:"getChannelId",company:s.job},"1").then(P=>{if(!P)return N("error","Failed to get channel id");L.set({icon:s.icon,company:s.name,job:s.job,id:P}),M.set("chat")})};return i("div",{className:"item",onMouseEnter:()=>c(!0),onMouseLeave:()=>c(!1),"data-open":a.open,onClick:l,children:[i("div",{className:"info",children:[a.icon&&e("div",{className:"icon",children:e(X,{src:a.icon})}),i("div",{className:"company-details",children:[e("div",{className:"title",children:a.name}),i("div",{className:"location",children:[e(me,{}),(o=a.location)==null?void 0:o.name]})]})]}),a.open?i("div",{className:"actions",children:[a.customIcon&&e("div",{className:"custom-icon",onClick:s=>{s.stopPropagation(),u("Services",{action:"customIconClick",company:a.job})},children:Le[a.customIcon]()}),((p=a.location)==null?void 0:p.coords)&&e($,{className:"orange",onClick:s=>{s.stopPropagation(),r(a)}}),((h=n.Companies)==null?void 0:h.Enabled)!==!1&&a.canCall!==!1&&e(J,{className:"green",onClick:s=>{s.stopPropagation(),S(a.job,a.name)}}),a.canMessage!==!1&&e(oe,{className:"green",onClick:s=>{s.stopPropagation(),E(a)}})]}):i(O,{children:[i("div",{className:"actions",onMouseEnter:()=>c(!1),onMouseLeave:()=>c(!0),children:[((f=a.location)==null?void 0:f.coords)&&e($,{className:"orange",onClick:s=>{s.stopPropagation(),r(a)}}),a.canMessage!==!1&&n.Companies.MessageOffline&&e(oe,{className:"green",onClick:s=>{s.stopPropagation(),E(a)}})]}),e(Se,{children:m&&e(A.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2,ease:"easeInOut"},className:"closed",children:t("APPS.SERVICES.CLOSED")})})]})]},a.name)};function je(){const a=v(D.PhoneNumber),[n,l]=I.useState({personal:[],job:[]}),[m,c]=I.useState("personal");return I.useEffect(()=>{W("Services")&&u("Services",{action:"getRecentMessages",page:0},U.messageList).then(r=>{if(!r)return N("warning","No recent messages found");l({personal:[...r.filter(S=>S.number===a)],job:[...r.filter(S=>S.number!==a)]})})},[]),w("services:channelDeleted",r=>{if(!r)return N("error","No channel ID provided");l({personal:n.personal.filter(S=>S.id!==r),job:n.job.filter(S=>S.id!==r)})}),e(O,{children:i("div",{className:"company-messages-container",children:[e("div",{className:"company-messages-header",children:e("div",{className:"selector",children:["personal","job"].map((r,S)=>e("div",{className:"option","data-active":m===r,onClick:()=>c(r),children:t(`APPS.SERVICES.${r.toUpperCase()}`)},S))})}),e(A.section,{...Pe(m==="personal"?"right":"left",m,.2),children:(n==null?void 0:n[m])&&(n==null?void 0:n[m].map((r,S)=>e(Fe,{channel:r,type:m},S)))})]})})}const Fe=({channel:a,type:n})=>{var r,S;const l=v(D.Settings),m=ye(()=>{var E;(E=_)!=null&&E.value.Companies.DeleteConversations&&n!=="personal"&&g.ContextMenu.set({buttons:[{title:t("APPS.MESSAGES.DELETE_CONVERSATION.TITLE"),color:"red",cb:()=>{setTimeout(()=>{g.PopUp.set({title:t("APPS.MESSAGES.DELETE_CONVERSATION.TITLE"),description:t("APPS.MESSAGES.DELETE_CONVERSATION.DESCRIPTION"),buttons:[{title:t("APPS.MESSAGES.DELETE_CONVERSATION.CANCEL")},{title:t("APPS.MESSAGES.DELETE_CONVERSATION.DELETE"),color:"red",cb:()=>{u("Services",{action:"deleteChannel",id:a.id},!0).then(o=>{if(!o)return N("error","Failed to delete channel, deleteChannel returned false")})}}]})},100)}}]})}),c=E=>{if(E)return/<!SENT-LOCATION-X=(-?\d*\.?\d*)Y=(-?\d*\.?\d*)!>/.test(E)};return i("div",{...m,className:"item",onClick:()=>{var E,o,p;L.set({...L==null?void 0:L.value,id:a.id,icon:n==="personal"&&((E=a==null?void 0:a.company)!=null&&E.icon)?a.company.icon:`./assets/img/avatar-placeholder-${l.display.theme}.svg`,company:n==="personal"&&((o=a==null?void 0:a.company)!=null&&o.name)?a.company.name:y(a.number),job:(p=a.company)==null?void 0:p.job,number:a.number}),M.set("chat")},children:[n==="personal"&&((r=a==null?void 0:a.company)!=null&&r.icon)?e(X,{src:a.company.icon}):e("img",{src:`./assets/img/avatar-placeholder-${l.display.theme}.svg`}),i("div",{className:"info",children:[i("div",{className:"info-header",children:[e("div",{className:"user",children:n==="personal"&&((S=a==null?void 0:a.company)!=null&&S.name)?a.company.name:y(a.number)}),i("div",{className:"right",children:[e("div",{className:"time",children:q(a.timestamp)}),e(De,{})]})]}),e("div",{className:"message",children:c(a.lastMessage)?t("APPS.MESSAGES.SENT_LOCATION_SHORT"):a.lastMessage})]})]})};function Ge(){const a=v(_),n=v(M),[l,m]=I.useState([{icon:e(me,{}),title:t("APPS.SERVICES.COMPANIES"),value:"companies"},{icon:e(le,{}),title:t("APPS.SERVICES.MESSAGES"),value:"messages"}]);return I.useEffect(()=>{var c,r;((r=(c=a==null?void 0:a.Companies)==null?void 0:c.Management)==null?void 0:r.Enabled)!==!1&&m([...l,{icon:e(Me,{}),title:t("APPS.SERVICES.ACTIONS"),value:"actions"}])},[]),e("div",{className:"companies-footer",children:l.map((c,r)=>i("div",{className:"item","data-active":n===c.value,onClick:()=>M.set(c.value),children:[c.icon,c.title]},r))})}const M=Y("companies"),L=Y(null),C=Y(null);function Ye(){const a=v(M),n=v(G),l=v(D.Styles.TextColor),m={companies:e(xe,{}),actions:e(we,{}),messages:e(je,{}),chat:e(_e,{})};let c={actions:t("APPS.SERVICES.ACTIONS"),messages:t("APPS.SERVICES.MESSAGES"),companies:t("APPS.SERVICES.COMPANIES")};return w("services:setCompany",r=>C.set(r)),w("services:setDuty",r=>C.patch({duty:r})),e("div",{className:"companies-container","data-view":a,children:!re()&&!_.value.Companies.AllowNoService?e("div",{className:"loading",children:e(Ve,{size:40,lineWeight:5,speed:2,color:l})}):i(O,{children:[e(Se,{children:n&&e(ke,{})}),i(A.div,{...Pe(a==="chat"?"left":"right",a,.2),className:"companies-wrapper",children:[a!=="chat"&&e("div",{className:"companies-header",children:e("div",{className:"title",children:c[a]})}),e("div",{className:"companies-body",children:m[a]})]}),a!=="chat"&&e(Ge,{})]})})}export{C as CompanyData,L as Data,M as View,Ye as default};
