local nitrous_install = {}
local can_refill, isRefilling, refill_num = false, false, nil
local usingNitrousBottle = false
local usingPurgeDye, dyeColor = false, nil
local nos_keybinds = {}
local purgePtfx = {}

local GetVehicleNitrousProperties = function(vehicleEntity)
    if not vehicleEntity or not DoesEntityExist(vehicleEntity) then
        return
    end
    local ent = Entity(vehicleEntity).state
    local props = ent['tuningsystem:vehicleNitrousProperties']
    return props
end
exports('GetVehicleNitrousProperties', GetVehicleNitrousProperties)

local SetVehicleNitrousProperties = function(vehicle, props)
    if vehicle == nil or not DoesEntityExist(vehicle) then
        return
    end
    local ent = Entity(vehicle).state
    ent:set('tuningsystem:vehicleNitrousProperties', props, true)
end
exports('SetVehicleNitrousProperties', SetVehicleNitrousProperties)

local IsInVehicle = function()
    local vehicle = GetVehiclePedIsIn(player, false)
    if vehicle == nil or vehicle == 0 then 
        return false
    else
        return vehicle
    end
end

local IsDriver = function(vehicle)
    return GetPedInVehicleSeat(vehicle, -1) == player
end

local InstallKit = function(vehicle, kit_type)
    local cfg = kit_type == 'nitrous_kit' and Config.Nitrous.Install.NitrousKit or kit_type == 'burst_kit' and Config.Nitrous.Install.BurstKit
    -- remove target:
    Lib.RemoveLocalEntity(vehicle, {'tuningsystem:target:install_'..kit_type}, cfg.target.label)

    -- face engine:
    local engineCoords = GetVehicleEngineCoords(vehicle)
    TaskTurnPedToFaceCoord(player, engineCoords.x, engineCoords.y, engineCoords.z, -1)
    Wait(1000)
    
    -- open hood if exists:
    if GetIsDoorValid(vehicle, 4) and (GetVehicleDoorAngleRatio(vehicle, 4) <= 0.05) then
        local anim = { dict = 'anim@heists@fleeca_bank@scope_out@return_case', name = 'trevor_action', rot = vector3(0.0, 0.0, GetEntityHeading(player)), blendIn = 2.0, blendOut = 2.0, duration = 1000, flags = 49, time = 0.25 }
        Lib.LoadAnim(anim.dict)
        TaskPlayAnimAdvanced(player, anim.dict, anim.name, coords.x, coords.y, coords.z, anim.rot.x, anim.rot.y, anim.rot.z, anim.blendIn, anim.blendOut, anim.duration, anim.flags, anim.time, 0, 0)
        Wait(200)
        SetVehicleDoorOpen(vehicle, 4, false, false)
        Wait(1000)
    end

    -- carry anim:
    TaskPlayAnim(player, cfg.anim.dict, cfg.anim.name, cfg.anim.blendIn, cfg.anim.blendOut, cfg.anim.duration, cfg.anim.flags, 0, 0, 0, 0)
    Wait(250)

    -- repair animation:
    local anim = {dict = 'mini@repair', name = 'fixing_a_player', blendIn = 2.0, blendOut = 2.0, duration = 5000, flags = 1}
    Lib.LoadAnim(anim.dict)
    TaskPlayAnim(player, anim.dict, anim.name, anim.blendIn, anim.blendOut, 2000, anim.flags, 0, 0, 0, 0)
    Wait(1700)

    -- delete object:
    DeleteEntity(nitrous_install.object)
    nitrous_install.carrying = false
    ClearPedTasks(player)
    TaskPlayAnim(player, anim.dict, anim.name, anim.blendIn, anim.blendOut, anim.duration, anim.flags, 0, 0, 0, 0)

    -- final repair animation:
    Wait(anim.duration - 300)
    RepairSound()
    Wait(300)
    ClearPedTasks(player)

    -- apply kit to vehicle:
    local nitrous = GetVehicleNitrousProperties(vehicle)
    if kit_type == 'nitrous_kit' then 
        local shots = nitrous_install.kit_size * 10
        local burst = Config.Nitrous.DefaultThreshold
        local color = {r = 1.0, g = 1.0, b = 1.0}
        -- check if alaready has nitrous:
        if nitrous ~= nil then
            if nitrous.shots > shots then
                shots = shots
            else
                shots = nitrous.shots
            end
            burst = nitrous.burst
            color = nitrous.color
        end
        -- reset nitrous shots if cfg enabled:
        if cfg.resetShots == true then
            shots = 0
        end
        -- sync props:
        local nitrous_props = {size = nitrous_install.kit_size, shots = shots, burst = burst, color = color} 
        SetVehicleNitrousProperties(vehicle, nitrous_props)
    elseif kit_type == 'burst_kit' then
        -- sync props:
        nitrous.burst = nitrous_install.burstKit.threshold
        SetVehicleNitrousProperties(vehicle, nitrous)
    end

    -- close hood:
    if GetIsDoorValid(vehicle, 4) then
        Wait(500)
        local anim = { dict = 'anim@heists@fleeca_bank@scope_out@return_case', name = 'trevor_action', rot = vector3(0.0, 0.0, GetEntityHeading(player)), blendIn = 2.0, blendOut = 2.0, duration = 1000, flags = 49, time = 0.25 }
        Lib.LoadAnim(anim.dict)
        TaskPlayAnimAdvanced(player, anim.dict, anim.name, coords.x, coords.y, coords.z, anim.rot.x, anim.rot.y, anim.rot.z, anim.blendIn, anim.blendOut, anim.duration, anim.flags, anim.time, 0, 0)
        Wait(1000)
        SetVehicleDoorShut(vehicle, 4, false)
        Wait(1000)
    end

    -- notification:
    if kit_type == 'nitrous_kit' then 
        Core.Notification({title = '', message = Lang['nitrous_kit_install_success'], type = 'success'}) 
    elseif kit_type == 'burst_kit' then
        Core.Notification({title = '', message = Lang['burst_kit_install_success'], type = 'success'}) 
    end

    -- unlock vehicle:
    SetVehicleUndriveable(nitrous_install.vehicle, false)

    nitrous_install = {}
end

local CreateVehicleEngineTarget = function(vehicle, target, kit_type)

    local CanInteract = function(entity)
        if Lib.IsEntityValid(entity) and GetEntityType(entity) ~= 0 then 
            if IsNearVehicleEngine(entity) then
                if (nitrous_install.object ~= nil and DoesEntityExist(nitrous_install.object)) and (nitrous_install.carrying ~= nil and nitrous_install.carrying == true) then 
                    return true
                end
            end
        end
        return false
    end

    Lib.AddLocalEntity(vehicle, {
        options = {
            {
                name = target.name, icon = target.icon, label = target.label, type = 'client', canInteract = CanInteract, distance =  target.distance,
                onSelect = function(data)
                    InstallKit(vehicle, kit_type)
                end
            }
        },
        distance = 5.0,
        canInteract = CanInteract,
    })
end

KitInstallHandle = function(kit_type)
    local cfg = kit_type == 'nitrous_kit' and Config.Nitrous.Install.NitrousKit or kit_type == 'burst_kit' and Config.Nitrous.Install.BurstKit

    local CreateKitObject = function()
        -- load anim:
        Lib.LoadAnim(cfg.anim.dict)
        -- play anim:
        TaskPlayAnim(player, cfg.anim.dict, cfg.anim.name, cfg.anim.blendIn, cfg.anim.blendOut, cfg.anim.duration, cfg.anim.flags, 0, 0, 0, 0)
        Wait(250)
        -- create object:
        nitrous_install.object = CreateObjectProp(cfg.prop, vector3(coords.x, coords.y, coords.z))
        -- attach object to player:
        local offset = cfg.offset
        AttachEntityToEntity(nitrous_install.object, player, GetPedBoneIndex(player, cfg.anim.boneId), offset.pos.x, offset.pos.y, offset.pos.z, offset.rot.x, offset.rot.y, offset.rot.z, true, true, false, true, 2, true)
    end

    -- leave vehicle
    SetVehicleUndriveable(nitrous_install.vehicle, true)
    TaskLeaveVehicle(player, nitrous_install.vehicle, 64)
    Wait(1500)

    -- anim & object:
    CreateKitObject()
    nitrous_install.carrying = true

    -- create target on the vehicle engine:
    local target = {
        name = 'tuningsystem:target:install_'..kit_type,
        icon = cfg.target.icon,
        label = cfg.target.label,
        distance =  cfg.target.distance
    }
    CreateVehicleEngineTarget(nitrous_install.vehicle, target, kit_type)
end

OrderNitrousKit = function(shopId, vehicleEntity)

    if nitrous_install.inProgress ~= nil and nitrous_install.inProgress == true then 
        return Core.Notification({title = '', message = Lang['ongoing_kit_install'], type = 'error'})
    end

    local input = lib.inputDialog(Lang['input_title_order_nitrous_kit'], {
        {type = 'number', label = Lang['input_label_unit_price'], icon = Config.Nitrous.MenuIcons.unitPrice, default = Config.Nitrous.UnitPrice, disabled = true, description = Lang['input_desc_unit_price']:format(GetFormattedPrice(Config.Nitrous.UnitPrice))},
        {type = 'slider', label = Lang['input_label_select_size'], icon = Config.Nitrous.MenuIcons.selectSize, required = true, min = 1, max = Config.Nitrous.MaxSize, step = 1},
    })

    if not input then
        return lib.showContext('nitrous_main_menu')
    end
    
    local kitSize = input[2]

    local totalPrice = (kitSize * input[1])

    local vehicle = IsInVehicle()
    if not vehicle then 
        return Core.Notification({title = '', message = Lang['must_be_inside_veh'], type = 'inform'})
    end

    if vehicle ~= vehicleEntity then 
        return Core.Notification({title = '', message = Lang['not_inside_correct_vehicle'], type = 'error'})
    end

    -- Alert Dialog:
    local alert = lib.alertDialog({
        header = Lang['alert_header_nitrous_kit']:format(kitSize, GetFormattedPrice(totalPrice)),
        content = Lang['alert_content_nitrous_kit']:format(kitSize, GetFormattedPrice(totalPrice)),
        centered = true,
        cancel = true
    })

    if alert == 'confirm' then
        local account = lib.callback.await('tuningsystem:shop:getAccount', false, shopId)
        if account >= totalPrice then
            -- remove account money:
            TriggerServerEvent('tuningsystem:server:removeAccountMoney', shopId, totalPrice)
            nitrous_install.kit_size = kitSize
            nitrous_install.shopId = shopId
            nitrous_install.inProgress = true
            nitrous_install.vehicle = vehicle or vehicleEntity
            KitInstallHandle('nitrous_kit')
        else
            Core.Notification({title = '', message = Lang['nitrous_insufficient_funds'], type = 'error'})
            return lib.showContext('nitrous_main_menu')
        end
    else
        return lib.showContext('nitrous_main_menu')
    end
end

OrderBurstKit = function(shopId, vehicleEntity, data)
    if nitrous_install.inProgress ~= nil and nitrous_install.inProgress == true then 
        return Core.Notification({title = '', message = Lang['ongoing_kit_install'], type = 'error'})
    end

    local vehicle = IsInVehicle()
    if not vehicle then 
        return Core.Notification({title = '', message = Lang['must_be_inside_veh'], type = 'inform'})
    end

    if vehicle ~= vehicleEntity then 
        return Core.Notification({title = '', message = Lang['not_inside_correct_vehicle'], type = 'error'})
    end

    -- Alert Dialog:
    local alert = lib.alertDialog({
        header = Lang['alert_header_burst_kit']:format(data.label, GetFormattedPrice(data.price)),
        content = Lang['alert_content_burst_kit']:format(data.label, data.threshold, GetFormattedPrice(data.price)),
        centered = true,
        cancel = true
    })

    if alert == 'confirm' then
        local account = lib.callback.await('tuningsystem:shop:getAccount', false, shopId)
        if account >= data.price then
            -- remove account money:
            TriggerServerEvent('tuningsystem:server:removeAccountMoney', shopId, data.price)
            nitrous_install.burstKit = data
            nitrous_install.shopId = shopId
            nitrous_install.inProgress = true
            nitrous_install.vehicle = vehicle or vehicleEntity
            KitInstallHandle('burst_kit')
        else
            Core.Notification({title = '', message = Lang['nitrous_insufficient_funds'], type = 'error'})
            return lib.showContext('burst_upgrade_kits')
        end
    else
        return lib.showContext('burst_upgrade_kits')
    end

end

RegisterBurstUpgradeKitsOptions = function(shopId, vehicle)
    local nitrous = GetVehicleNitrousProperties(vehicle)

    -- Function to get option setup:
    local GetBurstKitOption = function(title, icon, price, threshold, description, readOnly)
        return {
            title = title, icon = icon, description = description, readOnly = readOnly, args = { label = title, price = price, threshold = threshold },
            onSelect = function(args)
                OrderBurstKit(shopId, vehicle, args)
            end
        }
    end

    local defaultBurstKit = false
    if nitrous.burst == Config.Nitrous.DefaultThreshold then
        defaultBurstKit = true
    end

    local menuOptions = {}

    -- menu option for stock burst kit:
    if defaultBurstKit == false then
        menuOptions[#menuOptions+1] = GetBurstKitOption(Config.Nitrous.DefaultBurst.label, Config.Nitrous.DefaultBurst.icon, Config.Nitrous.DefaultBurst.price, Config.Nitrous.DefaultThreshold, Config.Nitrous.DefaultBurst.description, false)
    end

    -- fetch and create configured burst kits:
    for i = 1, #Config.Nitrous.BurstKits do
        local installed = false
        if nitrous.burst == Config.Nitrous.BurstKits[i].threshold then
            installed = true
        end
        menuOptions[#menuOptions+1] = GetBurstKitOption(Config.Nitrous.BurstKits[i].label, Config.Nitrous.BurstKits[i].icon, Config.Nitrous.BurstKits[i].price, Config.Nitrous.BurstKits[i].threshold, Config.Nitrous.BurstKits[i].description, installed)
    end

    -- register context menu:
    lib.registerContext({
        id = 'burst_upgrade_kits',
        title = Lang['title_burst_upg_kits'],
        menu = 'nitrous_main_menu',
        options = menuOptions
    })
end

-- NITROUS MAIN MENU:
RegisterNetEvent('tuningsystem:client:nitrousMainMenu', function(data)
    local menuOptions = {}

    -- CURRENT NITROUS KIT:
    local nitrous = GetVehicleNitrousProperties(data.vehicle)
    if nitrous ~= nil then 
        menuOptions[#menuOptions + 1] = {
            title = Lang['title_current_nos_kit'],
            description = Lang['desc_current_nos_kit'],
            icon = Config.Nitrous.MenuIcons.currentNosKit,
            readOnly = true,
            metadata = {
                {label = Lang['meta_nos_size'], value = nitrous.size..Config.Nitrous.Weight},
                {label = Lang['meta_nos_burst'], value = nitrous.burst},
                {label = Lang['meta_nos_shots'], value = nitrous.shots},
                {label = Lang['meta_nos_color'], value = json.encode(nitrous.color)},
            },
        }
    end

    -- NITROUS KIT OPTION:
    menuOptions[#menuOptions + 1] = {
        title = Lang['title_nitrous_kits'],
        icon = Config.Nitrous.MenuIcons.nitrousKit,
        description = Lang['desc_nitrous_kits'],
        onSelect = function()
            OrderNitrousKit(data.shopId, data.vehicle)
        end
    }

    if nitrous ~= nil then
        RegisterBurstUpgradeKitsOptions(data.shopId, data.vehicle) -- register burst upgrade kits sub-menu
        -- BURST UPGRADE KITS OPTION:
        menuOptions[#menuOptions + 1] = {
            title = Lang['title_burst_upg_kits'],
            icon = Config.Nitrous.MenuIcons.burstKit,
            arrow = true,
            menu = 'burst_upgrade_kits',
            description = Lang['desc_burst_kits'],
        }
    end
    
    lib.registerContext({
        id = 'nitrous_main_menu',
        title = Lang['title_nos_main_menu'],
        menu = 'tuning_bay_main_menu',
        options = menuOptions
    })
    lib.showContext('nitrous_main_menu')
end)

local refill_points = {}
SetupRefillPoints = function()
    local function CreateBlip(pos, data)
        local blip = AddBlipForCoord(pos.x, pos.y, pos.z)
        SetBlipSprite(blip, data.sprite)
        SetBlipDisplay(blip, data.display)
        SetBlipScale(blip, data.scale)
        SetBlipColour(blip, data.color)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(data.name)
        EndTextCommandSetBlipName(blip)
        return blip
    end

    if not IsPlayerTuner() then
        return 
    end
    
    for k,v in ipairs(Config.Nitrous.Refill.points) do
        refill_points[k] = {} 

        if v.blip == true then
            refill_points[k].blip = CreateBlip(v.pos, Config.Nitrous.Refill.blip)
        end

        refill_points[k].point = lib.points.new({
            num = k,
            coords = v.pos,
            distance = Config.Nitrous.Refill.distance,
            size = v.size,

            onEnter = function(point)
                can_refill = true
                refill_num = point.num
                if Config.Debug then 
                    print("point enter ("..point.num..") | can refill: ", can_refill, " | size: "..point.size)
                end
            end,
    
            onExit = function(point)
                can_refill = false
                refill_num = nil
                if Config.Debug then 
                    print("point enter ("..point.num..") | can refill: ", can_refill, " | size: "..point.size)
                end
            end,
        })
    end
end

ReloadRefillPoints = function()
    if refill_points ~= nil and next(refill_points) then
        for k,v in pairs(refill_points) do
            if refill_points[k].blip ~= nil and DoesBlipExist(refill_points[k].blip) then
                RemoveBlip(refill_points[k].blip)
            end
            if refill_points[k].point ~= nil then 
                refill_points[k].point.remove(refill_points[k].point)
            end 
        end
    end
    SetupRefillPoints()
end

RegisterNetEvent('tuningsystem:client:updateRefillPoint')
AddEventHandler('tuningsystem:client:updateRefillPoint', function(num, data)
    -- update client cfg
    Config.Nitrous.Refill.points[num] = data
    -- update point:
    refill_points[num].point.size = Config.Nitrous.Refill.points[num].size
end)

RegisterNetEvent('tuningsystem:client:refillNitrousBottle')
AddEventHandler('tuningsystem:client:refillNitrousBottle', function(itemId, itemData)
    local isTunerJob, shopId = IsTunerJob(Core.GetJob().name)

    if not isTunerJob then
        return Core.Notification({title = '', message = Lang['need_tuner_job'], type = 'inform'}) 
    end

    if isRefilling == true then 
        return Core.Notification({title = '', message = Lang['is_refilling_nos_bottle'], type = 'inform'})
    end

    if can_refill == false then 
        return Core.Notification({title = '', message = Lang['must_be_near_nos_gas_tank'], type = 'inform'})
    end

    if Config.Nitrous.Refill.points[refill_num].size <= 0 then
        return Core.Notification({title = '', message = Lang['refill_nos_gas_tank_empty'], type = 'inform'})
    end

    isRefilling = true
    
    local success = nil
    if Config.Nitrous.Refill.skillcheck.enable then
        success = SkillCheck(Config.Nitrous.Refill.skillcheck.difficulty, Config.Nitrous.Refill.skillcheck.inputs)
    else
        success = true
    end
    if success then
        if lib.progressBar({
            duration = Config.Nitrous.Refill.duration,
            label = Config.Nitrous.Refill.label,
            useWhileDead = false,
            canCancel = true,
            anim = {
                dict = Config.Nitrous.Refill.anim.dict,
                clip = Config.Nitrous.Refill.anim.name,
                flag = Config.Nitrous.Refill.anim.flag,
                blendIn = Config.Nitrous.Refill.anim.blendIn,
                blendOut = Config.Nitrous.Refill.anim.blendOut
            },
            disable = {
                move = true,
                combat = true
            }
        }) then
            TriggerServerEvent('tuningsystem:server:refillNitrousBottle', refill_num)
        end
    end

    isRefilling = false

end)

RegisterNetEvent('tuningsystem:client:useNitrousBottle')
AddEventHandler('tuningsystem:client:useNitrousBottle', function(itemId, itemData)

    if usingNitrousBottle == true then 
        return Core.Notification({title = '', message = Lang['is_using_nos_bottle'], type = 'inform'})
    end

    local vehicle = IsInVehicle()
    if not vehicle then 
        return Core.Notification({title = '', message = Lang['must_be_inside_veh'], type = 'inform'})
    end

    if IsDriver(vehicle) and GetEntitySpeed(vehicle) > 0 then 
        return Core.Notification({title = '', message = Lang['cannot_drive_load_nos_bottle'], type = 'inform'})
    end

    local nitrous = GetVehicleNitrousProperties(vehicle)
    if nitrous == nil then
        return Core.Notification({title = '', message = Lang['vehicle_no_nitrous_kit'], type = 'inform'})
    end

    if nitrous.shots >= (nitrous.size * 10) then
        return Core.Notification({title = '', message = Lang['vehicle_nitrous_kit_full'], type = 'inform'})
    end

    usingNitrousBottle = true
    TriggerServerEvent('t1ger_lib:server:removeItem', itemData.name, 1)

    if IsDriver(vehicle) then
        FreezeEntityPosition(vehicle, true)
    end
    
    local success = nil
    if Config.Nitrous.Bottle.skillcheck.enable then
        success = SkillCheck(Config.Nitrous.Bottle.skillcheck.difficulty, Config.Nitrous.Bottle.skillcheck.inputs)
    else
        success = true
    end
    if success then
        if lib.progressBar({
            duration = Config.Nitrous.Bottle.duration,
            label = Config.Nitrous.Bottle.label,
            useWhileDead = false,
            canCancel = true,
            disable = {
                move = true,
                combat = true
            }
        }) then
            nitrous.shots = nitrous.shots + 10
            if nitrous.shots > (nitrous.size * 10) then 
                nitrous.shots = (nitrous.size * 10)
            end
            SetVehicleNitrousProperties(vehicle, nitrous)
            Core.Notification({title = '', message = Lang['vehicle_used_nos_bottle']:format(nitrous.shots, (nitrous.size * 10)), type = 'success'})
        end
    end

    TriggerServerEvent('t1ger_lib:server:addItem', Config.Items['kits'][5].name, 1)

    if IsDriver(vehicle) then
        FreezeEntityPosition(vehicle, false)
    end

    usingNitrousBottle = false
end)

local TriggerNitrousEffects = function(entity, mode, toggle)
    if not entity or not DoesEntityExist(entity) then return end
    local ent = Entity(entity).state
    if mode == 'burst' then 
        ent:set('tuningsystem:toggleBurstEffects', toggle, true)
    elseif mode == 'purge' then
        ent:set('tuningsystem:togglePurgeEffects', toggle, true)
    end
end

local function InitializeNitrousData(vehicle)
    local ent = Entity(vehicle).state

    -- Check if the nitrous data is already set for the vehicle
    if not ent['tuningsystem:nitrousData'] then
        local data = {
            plate = GetVehicleNumberPlateText(vehicle), -- Vehicle plate for unique identification
            efficiency = {
                value = Config.Nitrous.Efficiency.noPurge, -- Initial efficiency (no purge)
                threshold = 0,
                active = false,
                timeLeft = 0,
                cooldown = false
            }
        }

        -- Set the nitrous data to the vehicle's state bag (syncing across players)
        ent:set('tuningsystem:nitrousData', data, true) -- 'true' enables sync across clients
        return data
    else
        local data = ent['tuningsystem:nitrousData']
        if data.efficiency ~= nil and data.efficiency.timeLeft ~= nil then 
            if data.efficiency.timeLeft <= 0 and data.efficiency.active == true then
                data.efficiency.active = false
                ent:set('tuningsystem:nitrousData', data, true) -- 'true' enables sync across clients
            end
        end
        return ent['tuningsystem:nitrousData']
    end

end

local IsNitrousCooldownActive = function(vehicle)
    local nitrousData = Entity(vehicle).state['tuningsystem:nitrousData']
    if nitrousData.efficiency.cooldown then
        return true 
    else
        return false
    end
    return false
end

local CalculateEfficiencyValue = function(threshold, burstkit)
    local value = Config.Nitrous.Efficiency.purge.min + ((threshold / burstkit) * (Config.Nitrous.Efficiency.purge.max - Config.Nitrous.Efficiency.purge.min))
    return value
end

local GetEfficiencyValue = function(vehicle)
    local nitrousData = Entity(vehicle).state['tuningsystem:nitrousData']
    return nitrousData.efficiency.value
end

local function IsDrivingVehicle()
    if not cache.vehicle then return end
	local vehicle = cache.vehicle

    Wait(1500)

    local nitrous = GetVehicleNitrousProperties(vehicle)
    if nitrous == nil or next(nitrous) == nil then 
        return 
    end

    -- get entity statebag:
    local ent = Entity(vehicle).state

    -- get nitrous data:
    local nitrous_data = InitializeNitrousData(vehicle)
    if Config.Debug then
        print(json.encode(nitrous_data, {indent = true}))
    end

    -- thread:
	while cache.seat == -1 do
		Wait(1000)
		if not DoesEntityExist(vehicle) then return end
        -- get nitrous data:
        local ent = Entity(vehicle).state
        nitrous_data = ent['tuningsystem:nitrousData']

        if Config.Debug then
            print("NOS Cooldown: ", nitrous_data.efficiency.cooldown)
        end

        if nitrous_data.efficiency.timeLeft > 0 then
            nitrous_data.efficiency.timeLeft = (nitrous_data.efficiency.timeLeft - 1)

            if Config.Debug then 
                print("Efficiency Window - Time Left: "..nitrous_data.efficiency.timeLeft)
            end

            -- if timer expires:
            if nitrous_data.efficiency.timeLeft <= 0 then
                nitrous_data.efficiency.timeLeft = 0
                nitrous_data.efficiency.value = Config.Nitrous.Efficiency.windowExpiry
                nitrous_data.efficiency.active = false
                nitrous_data.efficiency.threshold = 0
                -- start cooldown if not active:
                if not nitrous_data.efficiency.cooldown then 
                    -- activiate cooldown:
                    nitrous_data.efficiency.cooldown = true
                    -- sync cooldown to server:
                    TriggerServerEvent('tuningsystem:server:nitrousCooldown', GetVehicleNumberPlateText(vehicle), NetworkGetNetworkIdFromEntity(vehicle))
                end
            end
            ent:set('tuningsystem:nitrousData', nitrous_data, true) -- 'true' enables sync across clients
        end

	end

    -- Set the nitrous data to the vehicle's state bag (syncing across players)
    ent:set('tuningsystem:nitrousData', nitrous_data, true) -- 'true' enables sync across clients
end

-- check if driver when script (re)starts:
if cache.seat == -1 then
    Wait(2000)
    CreateThread(IsDrivingVehicle)
end

-- is seats changed:
lib.onCache('seat', function(seat)
    -- if driver:
	if seat == -1 then
		SetTimeout(0, IsDrivingVehicle)
	end
end)

local IsVehicleStationary = function(vehicle)
    local speed = GetEntitySpeed(vehicle)
    return speed < 0.1
end


local VehiclePurgeHandle = function(vehicle, threshold, nitrous)
    if nitrous ~= nil then
        -- show purge threshold message:
        if Config.Nitrous.Purge.notify.showHeldDuration == true then
            Core.Notification({title = '', message = Lang['nos_purge_threshold']:format(threshold), type = 'inform'})
        end
        -- remove nos shots:
        nitrous.shots = (nitrous.shots - 1)
        -- restart particle fx if threshold is held for 8 seconds or more
        if threshold == 8 or threshold == 18 or threshold == 28 or threshold == 38 then
            TriggerNitrousEffects(vehicle, 'purge', false)
            TriggerNitrousEffects(vehicle, 'purge', true)
        end
        -- is shots empty:
        if nitrous.shots <= 0 then
            nitrous.shots = 0
            Core.Notification({title = '', message = Lang['vehicle_nos_kit_empty'], type = 'inform'}) 
            TriggerNitrousEffects(vehicle, 'purge', false)
        end
        SetVehicleNitrousProperties(vehicle, nitrous)
    end
end

local ApplyGasLeak = function(vehicle, nitrous)
    local shots = nitrous.shots
    math.randomseed(GetGameTimer())
    local removePercent = math.random(Config.Nitrous.Purge.gasLeak.remove.min, Config.Nitrous.Purge.gasLeak.remove.max)
    -- Calculate the number of shots to remove based on percentage
    local shotsToRemove = math.floor(nitrous.shots * (removePercent / 100))
    nitrous.shots = math.max(0, nitrous.shots - shotsToRemove)
    -- Update the state bag with the new value
    SetVehicleNitrousProperties(vehicle, nitrous)

    -- nitrous leak ptfx:
    local ent = Entity(vehicle).state
    ent:set('tuningsystem:nitrousLeak', true, true) -- 'true' enables sync across clients

    -- Notify the player of the gas leak and how many shots were removed
    Core.Notification({title = '', message = Lang['nos_gas_leak'], type = 'error'})
end

-- Purge Keybind:
nos_keybinds.purge = lib.addKeybind({
    name = 'tuningsystem_nos_purge',
    description = Config.Nitrous.Keybinds.purge.description,
    defaultKey = Config.Nitrous.Keybinds.purge.defaultKey,
    onPressed = function(self)
        -- vehicle & driver checks:
        local vehicle = IsInVehicle()
        if not vehicle or not IsDriver(vehicle) or not DoesEntityExist(vehicle) then
            return
        end

        -- has nitrous kit:
        local nitrous = GetVehicleNitrousProperties(vehicle)
        if nitrous == nil or next(nitrous) == nil then 
            return
        end
        
        -- check if stationary:
        if not IsVehicleStationary(vehicle) then 
            return Core.Notification({title = '', message = Lang['vehicle_purge_not_stationary'], type = 'inform'})
        end

        -- has available nitrous gas:
        if nitrous.shots <= 0 then
            return Core.Notification({title = '', message = Lang['vehicle_nos_kit_empty'], type = 'inform'}) 
        end

        -- check engine damage:
        if GetVehicleEngineHealth(vehicle) <= Config.Nitrous.Burst.requiredEngineHealth then 
            return Core.Notification({title = '', message = Lang['nos_burst_activate_fail_engine'], type = 'inform'}) 
        end

        -- check for nitrous cooldown;
        if IsNitrousCooldownActive(vehicle) then
            -- Purge during cooldown, possible gas leak
            math.randomseed(GetGameTimer())
            if math.random(100) <= Config.Nitrous.Purge.gasLeak.chance then
                ApplyGasLeak(vehicle, nitrous)
            else
                Core.Notification({title = '', message = Lang['nos_purge_cooldown'], type = 'inform'})
            end
            return
        end

        FreezeEntityPosition(vehicle, true)

        self.isPressed = true
        self.threshold = 0
        self.vehicle = vehicle
        self.nitrous = nitrous
        self.timer = 0

        TriggerNitrousEffects(vehicle, 'purge', true)

        CreateThread(function()
            while self.isPressed and IsInVehicle() == vehicle and IsDriver(vehicle) and DoesEntityExist(vehicle) and self.vehicle == vehicle do
                Wait(0)
                self.timer = self.timer + 1
                if self.timer > 60 then
                    self.nitrous = GetVehicleNitrousProperties(self.vehicle)
                    if self.isPressed and self.nitrous?.shots > 0 then
                        self.threshold = self.threshold + 1
                        -- update purge handle:
                        VehiclePurgeHandle(self.vehicle, self.threshold, self.nitrous)
                    end
                    self.timer = 0
                end  
            end
        end)
        
    end,
    onReleased = function(self)
        self.isPressed = false
        
        -- check vehicle:
        local vehicle = IsInVehicle()
        if not vehicle or not IsDriver(vehicle) or not DoesEntityExist(vehicle) then 
            return
        end
        if not self.threshold then return end
        self.nitrous = GetVehicleNitrousProperties(vehicle)

        -- has button been held for at least a second:
        if self.threshold > 0 then
            local ent = Entity(vehicle).state
            -- get updated nitrous data:
            local nitrousData = InitializeNitrousData(vehicle)

            if not nitrousData.efficiency.cooldown then
                if not nitrousData.efficiency.active then 
                    nitrousData.efficiency.active = true
                    nitrousData.efficiency.timeLeft = (Config.Nitrous.Purge.window * 60)
                end
    
                -- update current purged threshold:
                nitrousData.efficiency.threshold = (nitrousData.efficiency.threshold + self.threshold)
                -- held threshold is bigger than burst kit:
                if nitrousData.efficiency.threshold >= self.nitrous.burst then
                    --Core.Notification({title = '', message = 'You are wasting NOS shots. NOS is fully purged!', type = 'inform'})
                    nitrousData.efficiency.threshold = self.nitrous.burst
                end

                if Config.Debug then
                    print('Purge Threshold Value: '..nitrousData.efficiency.threshold)
                end

                -- show shots consumption message:
                if Config.Nitrous.Purge.notify.showConsumption == true then
                    Core.Notification({title = '', message = Lang['nos_shots_consumed']:format(self.threshold, self.nitrous.shots, (self.nitrous.size * 10)), type = 'inform'})
                end

                -- calculate efficiency value:
                local purgeEfficiency = math.floor(CalculateEfficiencyValue(nitrousData.efficiency.threshold, self.nitrous.burst))
                nitrousData.efficiency.value = purgeEfficiency
                -- show efficiency message:
                if Config.Nitrous.Purge.notify.showEfficiency == true then
                    Core.Notification({title = '', message = Lang['nos_purge_efficiency']:format(purgeEfficiency), type = 'success'}) 
                end

            else
                Core.Notification({title = '', message = Lang['nos_purge_aborted'], type = 'inform'}) 
            end
            
            ent:set('tuningsystem:nitrousData', nitrousData, true) -- 'true' enables sync across clients
        end

        -- reset button stats:
        self.timer = 0
        self.threshold = 0

        -- toggle of purge effects & unfreeze vehicle:
        TriggerNitrousEffects(vehicle, 'purge', false)
        FreezeEntityPosition(vehicle, false)
    end
})

SpeedBoost = function(vehicle, maxSpeed, efficiency, threshold, nitrous)
    if nitrous.shots <= 0 then return end

    -- current vehicle speed:
    local currentSpeed = GetEntitySpeed(vehicle)
    if currentSpeed <= 0 then return end

    -- convert to km/h:
    local kmh = (currentSpeed * 3.6)

    -- speed ratio to avoid exceeding max vehicle speed:
    local speedRatio = (maxSpeed / currentSpeed)

    -- multiplier to boost with:
    local multiplier = (Config.Nitrous.Burst.baseTorqueMultiplier * speedRatio)

    -- Convert efficiency from percentage (integer) to decimal
    local efficiencyDecimal = efficiency / 100

    -- Add efficiency on top of the base multiplier
    local finalMultiplier = multiplier + (multiplier * efficiencyDecimal)

    -- Cap the multiplier at low speeds (<= 10 km/h)
    if kmh <= 10 then
        finalMultiplier = math.min(finalMultiplier, 1.3 + (1.3 * efficiencyDecimal)) -- Scale low-speed cap with efficiency
    end

    -- check for dyno performance:
    local dynoProperties = exports[GetCurrentResourceName()]:GetVehicleDynoProperties(vehicle)
    if dynoProperties ~= nil or (type(dynoProperties) == 'table' and next(dynoProperties) ~= nil) then
        finalMultiplier = finalMultiplier + dynoProperties.torque
    end

    -- Apply Torque Power:
    SetVehicleCheatPowerIncrease(vehicle, finalMultiplier)
end

VehicleBurstHandle = function(vehicle, threshold, nitrous, maxSpeed)
    if nitrous ~= nil then
        -- show burst threshold message:
        if Config.Nitrous.Burst.notify.showHeldDuration == true then
            Core.Notification({title = '', message = Lang['nos_burst_threshold']:format(threshold), type = 'inform'})
        end
        -- remove nos shots:
        nitrous.shots = (nitrous.shots - 1)
        -- is shots empty:
        if nitrous.shots <= 0 then
            nitrous.shots = 0
            Core.Notification({title = '', message = Lang['vehicle_nos_kit_empty'], type = 'inform'})
            TriggerNitrousEffects(vehicle, 'burst', false)
        end
        -- update nitrous props:
        SetVehicleNitrousProperties(vehicle, nitrous)

        -- damage engine if threshold > nitrous.burst size
        if threshold > nitrous.burst then
            math.randomseed(GetGameTimer())
            if math.random(100) <= Config.Nitrous.Burst.engineDamage.threshold.chance then
                SetVehicleEngineHealth(vehicle, Config.Nitrous.Burst.engineDamage.threshold.engineHealth)
                Core.Notification({title = '', message = Lang['nos_burst_engine_damage'], type = 'error'})
                TriggerNitrousEffects(vehicle, 'burst', false)
            end
        end
    end
end

ActivateNitrousBurstCooldown = function(vehicle, threshold)
    if Config.Nitrous.Burst.cooldown == false then return end
    local plate = GetVehicleNumberPlateText(vehicle)
    local ent = Entity(vehicle).state
    ent:set('tuningsystem:nitrousCooldown', true, true)
    SetTimeout(threshold * 1000, function()
        if DoesEntityExist(vehicle) then
            ent = Entity(vehicle).state
            ent:set('tuningsystem:nitrousCooldown', false, true)
        end
    end)
end

local IsNitrousBurstCooldownActive = function(vehicle)
    local ent = Entity(vehicle).state
    return ent['tuningsystem:nitrousCooldown']
end

-- Burst Keybind:
nos_keybinds.burst = lib.addKeybind({
    name = 'tuningsystem_nos_burst',
    description = Config.Nitrous.Keybinds.burst.description,
    defaultKey = Config.Nitrous.Keybinds.burst.defaultKey,
    onPressed = function(self)
        -- vehicle & driver checks:
        local vehicle = IsInVehicle()
        if not vehicle or not IsDriver(vehicle) or not DoesEntityExist(vehicle) then
            return
        end

        -- has nitrous kit:
        local nitrous = GetVehicleNitrousProperties(vehicle)
        if nitrous == nil or next(nitrous) == nil then 
            return
        end

        -- has available nitrous gas:
        if nitrous.shots <= 0 then
            return Core.Notification({title = '', message = Lang['vehicle_nos_kit_empty'], type = 'inform'}) 
        end

        -- check engine damage:
        if GetVehicleEngineHealth(vehicle) <= Config.Nitrous.Burst.requiredEngineHealth then 
            return Core.Notification({title = '', message = Lang['nos_burst_activate_fail_engine'], type = 'inform'}) 
        end

        -- check for nitrous cooldown:
        if Config.Nitrous.Burst.cooldown == true and IsNitrousBurstCooldownActive(vehicle) then 
            return Core.Notification({title = '', message = Lang['nos_burst_cooldown'], type = 'inform'}) 
        end

        self.isPressed = true
        self.threshold = 0
        self.vehicle = vehicle
        self.nitrous = nitrous
        self.timer = 0
        self.model = GetEntityModel(vehicle)
        self.maxSpeed = GetVehicleModelMaxSpeed(self.model)
        self.efficiencyValue = GetEfficiencyValue(vehicle)

        TriggerNitrousEffects(vehicle, 'burst', true)

        CreateThread(function()
            while self.isPressed and IsInVehicle() == vehicle and IsDriver(vehicle) and DoesEntityExist(vehicle) and self.vehicle == vehicle do
                Wait(1)
                self.timer = self.timer + 1

                -- Check for conditions to stop the burst
                if self.nitrous.shots <= 0 or GetVehicleEngineHealth(vehicle) <= Config.Nitrous.Burst.requiredEngineHealth then
                    -- Stop the burst:
                    self.isPressed = false
                    break  -- Break the loop and stop the burst
                end

                if self.timer > 60 then
                    self.nitrous = GetVehicleNitrousProperties(self.vehicle)
                    if self.isPressed and self.nitrous?.shots > 0 then
                        self.threshold = self.threshold + 1
                        -- update burst handle:
                        VehicleBurstHandle(self.vehicle, self.threshold, self.nitrous, self.maxSpeed)
                    end
                    self.timer = 0
                end

                SpeedBoost(self.vehicle, self.maxSpeed, self.efficiencyValue, self.threshold, self.nitrous)
            end
        end)
        
    end,
    onReleased = function(self)
        self.isPressed = false
        
        -- check vehicle:
        local vehicle = IsInVehicle()
        if not vehicle or not IsDriver(vehicle) or not DoesEntityExist(vehicle) then 
            return
        end
        if not self.threshold then return end

        self.nitrous = GetVehicleNitrousProperties(vehicle)

        -- has button been held for at least a second:
        if self.threshold > 0 then
            -- Check if nitrous cooldown is enabled and not already active, then activate cooldown
            if Config.Nitrous.Burst.cooldown == true and not IsNitrousBurstCooldownActive(vehicle) then 
                ActivateNitrousBurstCooldown(vehicle, self.threshold)
            end
            
            -- Get damageType based on efficiency
            local damageType = nil
            if self.efficiencyValue == Config.Nitrous.Efficiency.noPurge then 
                damageType = 'noPurge'
            elseif self.efficiencyValue == Config.Nitrous.Efficiency.windowExpiry then 
                damageType = 'windowExpiry'
            end

            -- Calculate chance and if applicable, set engine health
            if damageType ~= nil then 
                math.randomseed(GetGameTimer())

                -- Check if engine damage should occur based on chance
                if math.random(100) <= Config.Nitrous.Burst.engineDamage[damageType].chance then
                    SetVehicleEngineHealth(vehicle, Config.Nitrous.Burst.engineDamage[damageType].engineHealth)
                    Core.Notification({title = '', message = Lang['nos_chance_engine_damage'], type = 'error'})
                end

            end
        end

        -- reset button stats:
        self.timer = 0
        self.threshold = 0

        -- toggle off burst effects:
        TriggerNitrousEffects(vehicle, 'burst', false)
    end
})

-- function to start burst effects:
local ToggleBurstEffects = function(vehicle, enable)

    local SetNitrousActive = function(entity, toggle)
        SetVehicleNitroEnabled(entity, toggle)
        EnableVehicleExhaustPops(entity, not toggle)
        SetVehicleBoostActive(entity, toggle)
    end
    
    -- activate nitrous on vehicle:
    SetNitrousActive(vehicle, enable)

    -- vehicle checks:
    local veh = IsInVehicle()
    if not veh or not DoesEntityExist(veh) then
        return
    end

    -- veh comparison check:
    if (veh ~= vehicle) or (NetworkGetNetworkIdFromEntity(veh) ~= NetworkGetNetworkIdFromEntity(vehicle)) then
        return
    end
    
    -- screen effects:
    if enable then
        -- request ptfx asset
        lib.requestNamedPtfxAsset('veh_xs_vehicle_mods')
        -- screen effects:
        SetTimecycleModifier('rply_motionblur')
        ShakeGameplayCam('SKY_DIVING_SHAKE', 0.40)
    else
        -- reset power:
        SetVehicleCheatPowerIncrease(vehicle, 1.0)
        -- stop screen effects:
        StopGameplayCamShaking(true)
        SetTransitionTimecycleModifier('default', 0.40)
    end
end

-- Statebag Change Handler for Burst Effects:
AddStateBagChangeHandler('tuningsystem:toggleBurstEffects' --[[key filter]], nil --[[bag filter]], function(bagName, key, value, _unused, replicated)
	Wait(0)
	local entity = GetEntityFromStateBagName(bagName)
	if entity == 0 or not DoesEntityExist(entity) then return end
    if value == nil or replicated then return end
    
    -- code here:
    ToggleBurstEffects(entity, value)
end)

local TogglePurgeEffects = function(vehicle, enable)
    local plate = GetVehicleNumberPlateText(vehicle)
    
    local PurgeParticleFx = function(vehicle, asset, particle, boneName, offset, color)
        local ptfx = {}
        -- left side bonnet:
        UseParticleFxAssetNextCall(asset)
        if boneName == 'bonnet' then 
            ptfx.left = StartParticleFxLoopedOnEntity(particle, vehicle, offset.x - 0.6, offset.y + 0.08, offset.z - 0.08, 20.0, -15.0, 0.0, Config.Nitrous.Purge.ptfx_size, false, false, false)
        elseif boneName == 'engine' then 
            ptfx.left = StartParticleFxLoopedOnEntity(particle, vehicle, offset.x - 0.6, offset.y - 0.3, offset.z + 0.3, 20.0, -15.0, 0.0, Config.Nitrous.Purge.ptfx_size, false, false, false)
        end

        -- right side bonnet:
        UseParticleFxAssetNextCall(asset)
        if boneName == 'bonnet' then 
            ptfx.right = StartParticleFxLoopedOnEntity(particle, vehicle, offset.x + 0.6, offset.y + 0.08, offset.z - 0.08, 20.0, 15.0, 0.0, Config.Nitrous.Purge.ptfx_size, false, false, false)
        elseif boneName == 'engine' then 
            ptfx.right = StartParticleFxLoopedOnEntity(particle, vehicle, offset.x + 0.6, offset.y - 0.3, offset.z + 0.3, 20.0, 15.0, 0.0, Config.Nitrous.Purge.ptfx_size, false, false, false)
        end

        local nitrous = GetVehicleNitrousProperties(vehicle)

        -- set color:
        for index,ptfxHandle in pairs(ptfx) do
            SetParticleFxLoopedAlpha(ptfxHandle, 1.0)
            SetParticleFxLoopedColour(ptfxHandle, nitrous.color.r, nitrous.color.g, nitrous.color.b, true)
        end

        return ptfx
    end

    if enable then
        -- get bone index of bonnet:
        local boneName = 'bonnet'
        local boneIndex = GetEntityBoneIndexByName(vehicle, boneName)
    
        -- if not has bonnet bone:
        if boneIndex == -1 then
            boneName = 'engine'
            boneIndex = GetEntityBoneIndexByName(vehicle, boneName)
        end
    
        -- get bone pos and offset:
        local bonePos = GetWorldPositionOfEntityBone(vehicle, boneIndex)
        local offset = GetOffsetFromEntityGivenWorldCoords(vehicle, bonePos.x, bonePos.y, bonePos.z)
        
        -- store particles:
        purgePtfx[tostring(plate)] = PurgeParticleFx(vehicle, 'core', 'exp_sht_steam', boneName, offset)
    else
        -- stop particles:
        if purgePtfx[tostring(plate)] ~= nil then
            if purgePtfx[tostring(plate)].left ~= nil then
                StopParticleFxLooped(purgePtfx[tostring(plate)].left, 1)
            end
            if purgePtfx[tostring(plate)].right then
                StopParticleFxLooped(purgePtfx[tostring(plate)].right, 1)
            end
            purgePtfx[tostring(plate)] = nil
        end
    end
end

-- Statebag Change Handler for Purge Effects:
AddStateBagChangeHandler('tuningsystem:togglePurgeEffects' --[[key filter]], nil --[[bag filter]], function(bagName, key, value, _unused, replicated)
	Wait(0)
	local entity = GetEntityFromStateBagName(bagName)
	if entity == 0 or not DoesEntityExist(entity) then return end
    if value == nil or replicated then return end

    -- code here:
    TogglePurgeEffects(entity, value)
end)

-- Statebag Change Handler for Nitrous Leak:
AddStateBagChangeHandler('tuningsystem:nitrousLeak' --[[key filter]], nil --[[bag filter]], function(bagName, key, value, _unused, replicated)
	Wait(0)
	local entity = GetEntityFromStateBagName(bagName)
	if entity == 0 or not DoesEntityExist(entity) then return end
    if value == nil then return end

    -- start ptfx:
    lib.requestNamedPtfxAsset('core')
    UseParticleFxAssetNextCall('core')
    local ptfx = StartParticleFxLoopedOnEntityBone('ent_sht_steam', entity, 0.0, 0.0, -0.2, 0.0, -180.0, 0.0, GetEntityBoneIndexByName(entity, 'engine'), 0.25, false, false, false)

    -- stop ptfx:
    Wait(Config.Nitrous.Purge.gasLeak.duration)
    StopParticleFxLooped(ptfx, 0)
end)

-- Instal Purge Dye function:
InstallPurgeDye = function(vehicle)
    local cfg = Config.Nitrous.DyeInstall
    local installed = false
    
    -- check item:
    local hasItem, itemCount = lib.callback.await('tuningsystem:server:hasItem', false, Config.Items['kits'][6].name, 1)
    if not hasItem then
        return Core.Notification({title = '', message = Lang['you_dont_have_x_item']:format(Config.Items['kits'][6].label, 1), type = 'inform'}) 
    end

    -- remove target:
    Lib.RemoveLocalEntity(vehicle, {'tuningsystem:target:install_purgeDye'}, cfg.target.label)
    
    -- lock vehicle:
    SetVehicleUndriveable(vehicle, true)

    -- face engine:
    local engineCoords = GetVehicleEngineCoords(vehicle)
    TaskTurnPedToFaceCoord(player, engineCoords.x, engineCoords.y, engineCoords.z, -1)
    Wait(1000)

    -- open hood if exists:
    if GetIsDoorValid(vehicle, 4) and (GetVehicleDoorAngleRatio(vehicle, 4) <= 0.05) then
        local anim = { dict = 'anim@heists@fleeca_bank@scope_out@return_case', name = 'trevor_action', rot = vector3(0.0, 0.0, GetEntityHeading(player)), blendIn = 2.0, blendOut = 2.0, duration = 1000, flags = 49, time = 0.25 }
        Lib.LoadAnim(anim.dict)
        TaskPlayAnimAdvanced(player, anim.dict, anim.name, coords.x, coords.y, coords.z, anim.rot.x, anim.rot.y, anim.rot.z, anim.blendIn, anim.blendOut, anim.duration, anim.flags, anim.time, 0, 0)
        Wait(200)
        SetVehicleDoorOpen(vehicle, 4, false, false)
        Wait(1000)
    end

    -- repair animation:
    local anim = {dict = 'mini@repair', name = 'fixing_a_player', blendIn = 2.0, blendOut = 2.0, duration = cfg.duration, flags = 1}
    Lib.LoadAnim(anim.dict)
    TaskPlayAnim(player, anim.dict, anim.name, anim.blendIn, anim.blendOut, anim.duration, anim.flags, 0, 0, 0, 0)
    TriggerServerEvent('t1ger_lib:server:removeItem', Config.Items['kits'][6].name, 1)
    Wait(anim.duration - 300)
    RepairSound()
    installed = true
    Wait(300)
    ClearPedTasks(player)

    -- update nitrous properties:
    if installed then
        local nitrous = GetVehicleNitrousProperties(vehicle)
        nitrous.color = dyeColor
        SetVehicleNitrousProperties(vehicle, nitrous)
    end
    
    -- notification:
    if installed then 
        Core.Notification({title = '', message = Lang['injected_purge_dye_success'], type = 'success'}) 
    end

    -- unlock vehicle:
    SetVehicleUndriveable(vehicle, false)

    -- close hood:
    if GetIsDoorValid(vehicle, 4) then
        Wait(100)
        local anim = { dict = 'anim@heists@fleeca_bank@scope_out@return_case', name = 'trevor_action', rot = vector3(0.0, 0.0, GetEntityHeading(player)), blendIn = 2.0, blendOut = 2.0, duration = 1000, flags = 49, time = 0.25 }
        Lib.LoadAnim(anim.dict)
        TaskPlayAnimAdvanced(player, anim.dict, anim.name, coords.x, coords.y, coords.z, anim.rot.x, anim.rot.y, anim.rot.z, anim.blendIn, anim.blendOut, anim.duration, anim.flags, anim.time, 0, 0)
        Wait(1000)
        SetVehicleDoorShut(vehicle, 4, false)
    end

    dyeColor = nil
    usingPurgeDye = false
end

RegisterNetEvent('tuningsystem:client:useNitrousPurgeDye')
AddEventHandler('tuningsystem:client:useNitrousPurgeDye', function(itemId, itemData)
    if usingPurgeDye == true then 
        return Core.Notification({title = '', message = Lang['is_using_purge_dye'], type = 'inform'})
    end

    -- get vehicle:
    local vehicle, closestDist = GetVehiclePedIsIn(player, false), 0
    if vehicle == nil or vehicle == 0 then 
        vehicle, closestDist = Lib.GetClosestVehicle(coords, 5.0, false)
    end

    -- check if vehicle exist:
    if vehicle == nil or not DoesEntityExist(vehicle) then 
        return Core.Notification({
            title = '',
            message = Lang['no_vehicle_nearby'],
            type = 'inform'
        })
    end

    -- has nitrous kit:
    local nitrous = GetVehicleNitrousProperties(vehicle)
    if nitrous == nil or next(nitrous) == nil then 
        return Core.Notification({
            title = '',
            message = Lang['closest_veh_no_nitrous_kit'],
            type = 'inform'
        })
    end

    -- input dialog:
    local input = lib.inputDialog(Lang['input_title_rgb_color'], {
        {type = 'color', label = Lang['input_label_rgb_color'], default = 'rgb(255, 255, 255)', format = 'rgb', required = true}
    })

    -- check input:
    if not input or not input[1] then
        return 
    end

    -- convert rgb:
    local rgb = lib.math.tovector(input[1], 0, 255, true)
    local color = {r = lib.math.round((rgb.x/255), 3), g = lib.math.round((rgb.y/255), 3), b = lib.math.round((rgb.z/255), 3)}
    dyeColor = color

    -- start:
    usingPurgeDye = true

    -- can interact function:
    local CanInteract = function(entity)
        if Lib.IsEntityValid(entity) and GetEntityType(entity) ~= 0 then 
            if IsNearVehicleEngine(entity) then
                if usingPurgeDye and dyeColor ~= nil then 
                    return true
                end
            end
        end
        return false
    end

    -- create target on the vehicle engine:
    local cfg = Config.Nitrous.DyeInstall
    Lib.AddLocalEntity(vehicle, {
        options = {
            {
                name = 'tuningsystem:target:install_purgeDye', icon = cfg.target.icon, label = cfg.target.label, type = 'client', canInteract = CanInteract, distance = cfg.target.distance,
                onSelect = function(data)
                    InstallPurgeDye(vehicle)
                end
            }
        },
        distance = cfg.target.distance,
        canInteract = CanInteract,
    })

    Core.Notification({
        title = '',
        message = Lang['access_eng_bay_to_inject_purge_dye'],
        type = 'inform'
    })

end)
