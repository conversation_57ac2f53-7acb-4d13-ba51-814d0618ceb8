player, coords = nil, {}

Citizen.CreateThread(function()
    while true do
        player = PlayerPedId()
        coords = GetEntityCoords(player)
        Wait(500)
    end
end)

RegisterNetEvent("t1ger_mechanic:client:playerLoaded", function()
    Wait(1000)
    TriggerServerEvent("t1ger_mechanic:server:playerLoaded")
    if Config.Debug then 
        print("Player Loaded:", json.encode(_API.Player:GetJob(), {indent = true}))
        print("isAdmin: ", _API.Player.isAdmin)
    end
    CreateRepairStations()
end)

RegisterNetEvent("t1ger_mechanic:client:onJobUpdate", function(job, lastJob)
    if Config.Debug then
        print("Player Job updated:", json.encode(job, {indent = true}))
    end

    -- Check if player has a mechanic job
    local isPlayerMechanic, shopId = IsPlayerMechanic()

    if isPlayerMechanic then
        print("Player has mechanic job for shop ID:", shopId)

        -- Check if player is already hired in a mechanic shop
        local isEmployee, hiredShopId = IsPlayerEmployee()

        if isEmployee then
            -- If already hired in the same shop, update job grade
            if hiredShopId == shopId then
                TriggerServerEvent("t1ger_mechanic:server:updateEmployee", shopId, job.grade, false)
            else
                -- Player is hired in another shop, remove from old shop first
                TriggerServerEvent("t1ger_mechanic:server:removeEmployee", hiredShopId, false)
                Wait(500)
                TriggerServerEvent("t1ger_mechanic:server:addEmployee", shopId, job.grade, false)
            end
        else
            -- Player is not an employee anywhere, add them directly
            TriggerServerEvent("t1ger_mechanic:server:addEmployee", shopId, job.grade, false)
        end
    end

    -- Tell server about updated job for mechanic tracking
    TriggerServerEvent("t1ger_mechanic:server:onJobUpdate", job.name, job.onDuty)

    -- Reload shop markers after job update to reflect changes
    LoadAllMarkers()

    -- Refresh repair stations:
    RefreshRepairStations()
end)

--- Callback for skillcheck
--- @param difficulty table
--- @param inputs table
--- @return boolean?
function SkillCheck(difficulty, inputs)
    local success = lib.skillCheck(difficulty, inputs) 
    return success
end

--- Callback for progress bar
--- @param options table
--- @return boolean?
function ProgressBar(options)
    local success = lib.progressBar(options)
    return success
end

---Check if entity is valid
---@param entity number handle for entity
function IsEntityValid(entity)
    if not entity or entity == -1 then
        return false
    end
    return DoesEntityExist(entity)
end

---Draws 3D text on given coords
---@param x number
---@param y number
---@param z number
---@param text string
function Draw3DText(x, y, z, text)
    local boolean, _x, _y = GetScreenCoordFromWorldCoord(x, y, z)
    SetTextScale(0.32, 0.32); SetTextFont(4); SetTextProportional(1)
    SetTextColour(255, 255, 255, 255)
    SetTextEntry("STRING"); SetTextCentre(1); AddTextComponentString(text)
    DrawText(_x, _y)
    local factor = (string.len(text) / 500)
    DrawRect(_x, (_y + 0.0125), (0.015 + factor), 0.03, 0, 0, 0, 80)
end

--- Fixes vehicle deformation after inspection is complete.
--- @param vehicle number The vehicle entity handle
function FixVehicleDeformation(vehicle)
    -- replace the following export with whatever deformation script export you have.
    -- Currently supports this one: https://github.com/Kiminaze/VehicleDeformation

    local hasDeformationScript = GetResourceState("VehicleDeformation") == "started"
    if hasDeformationScript then
        exports["VehicleDeformation"]:FixVehicleDeformation(vehicle)
    end
end

--- Creates a prop/object and returns the entity handle
--- @param model string The prop/object model name
--- @param pos vector3 The coords to spawn the prop/model at
--- @return integer
function CreateProp(model, pos)
    if Config.ServerSideProps then
        -- create emote object on server
        local netId = lib.callback.await("t1ger_mechanic:server:createNetworkedObj", false, model, pos)
        if type(netId) ~= "number" and netId <= 0 then 
            return print("[t1ger_mechanic:client:propEmote] Could not create object")
        end

        -- Wait for netId to exist
        while not NetworkDoesNetworkIdExist(netId) do
            Wait(1)
        end
        SetNetworkIdExistsOnAllMachines(netId, true)

        -- request control of netId
        NetworkRequestControlOfNetworkId(netId)
        while not NetworkHasControlOfNetworkId(netId) do
            NetworkRequestControlOfNetworkId(netId)
            Wait(1)
        end

        -- get entity from network id
        local prop = NetworkGetEntityFromNetworkId(netId)
        
        -- ensure prop exists:
        while not DoesEntityExist(prop) do
            Wait(1)
        end

        return prop
    else
        -- request model
        lib.requestModel(model)

        -- create prop
        local prop = CreateObjectNoOffset(GetHashKey(model), pos.x, pos.y, pos.z, true, false, false)

        -- ensure prop exists:
        while not DoesEntityExist(prop) do 
            Wait(10)
        end

        return prop
    end
end