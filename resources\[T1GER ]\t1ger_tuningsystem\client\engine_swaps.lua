local engine_swap = {}
local curEngine = {}
local curHoist = {}
local curVehicle = {}

EngineSwapAlertDialog = function(shopId, vehicle, args)
    if curEngine ~= nil and next(curEngine) ~= nil or curVehicle ~= nil and next(curVehicle) ~= nil then
        Core.Notification({title = '', message = Lang['engine_swap_already_have_task'], type = 'error'})
        return lib.showContext('tuning_bay_main_menu')
    end

    local alert = lib.alertDialog({
        header = Lang['alert_header_engine_swap_order']:format(args.label, GetFormattedPrice(args.price)),
        content = Lang['alert_content_engine_swap_order']:format(args.label, GetFormattedPrice(args.price)),
        centered = true,
        cancel = true
    })
    if alert == 'confirm' then
        local account = lib.callback.await('tuningsystem:shop:getAccount', false, shopId)
        if account >= args.price then
            engine_swap = args
            engine_swap.shopId = shopId
            engine_swap.pos = Config.Shops[shopId].delivery
            TriggerServerEvent('tuningsystem:server:removeAccountMoney', shopId, args.price)
            curVehicle = CreateVehicleSwapClass(vehicle)
            Core.Notification({title = '', message = Lang['engine_swap_order_placed']:format(args.price, args.label), type = 'success'})
            local isOpen, currentText = lib.isTextUIOpen()
            if isOpen then
                lib.hideTextUI() 
            end
            Wait(1000)
            Core.Notification({title = '', message = Lang['engine_swap_prepare_vehicle'], type = 'inform'})
        else
            Core.Notification({title = '', message = Lang['engine_swap_insufficient_funds'], type = 'error'})
            lib.showContext('tuning_bay_main_menu')
        end
    else
        lib.showContext('tuning_bay_main_menu')
    end
end

CreateVehicleSwapClass = function(vehicle)
    local self = {}

    self.entity = vehicle
    self.modelHash = GetEntityModel(self.entity)
    self.d1,self.d2 = GetModelDimensions(self.modelHash)

    self.engineSwapped = false

    self.engine = {
        coords = nil,
        entity = nil,
        attached = false
    }

    self.target = Config.EngineSwaps.VehicleTarget

    -- get engine coords:
    self.getEngineCoords = function()
        local boneIndex = GetEntityBoneIndexByName(self.entity, 'engine')
        if boneIndex and boneIndex > 0 then
            return GetWorldPositionOfEntityBone(self.entity, boneIndex)
        else
            return GetOffsetFromEntityInWorldCoords(self.entity, 0.0, (self.d2.y - 1.0), 0.0)
        end
    end

    -- prepare engine for extraction:
    self.prepareEngine = function(vehicle)
        if GetIsDoorValid(vehicle, 4) and (GetVehicleDoorAngleRatio(vehicle, 4) <= 0.05 or (not IsVehicleDoorDamaged(vehicle, 4) and not IsVehicleDoorFullyOpen(vehicle, 4))) then
            local anim = { dict = 'anim@heists@fleeca_bank@scope_out@return_case', name = 'trevor_action', rot = vector3(0.0, 0.0, GetEntityHeading(player)), blendIn = 2.0, blendOut = 2.0, duration = 1000, flags = 49, time = 0.25 }
            Lib.LoadAnim(anim.dict)
            TaskPlayAnimAdvanced(player, anim.dict, anim.name, coords.x, coords.y, coords.z, anim.rot.x, anim.rot.y, anim.rot.z, anim.blendIn, anim.blendOut, anim.duration, anim.flags, anim.time, 0, 0)
            Wait(200)
            SetVehicleDoorOpen(vehicle, 4, false, false)
            Wait(1000)
        end
        local anim = Config.EngineSwaps.RepairAnimation
        Lib.LoadAnim(anim.dict)
        TaskPlayAnim(player, anim.dict, anim.name, anim.blendIn, anim.blendOut, anim.duration, anim.flags, 0, 0, 0, 0)
        Wait(anim.duration)
        ClearPedTasks(player)
        Wait(500)
        SetVehicleDoorBroken(vehicle, 4, true)
        FreezeEntityPosition(vehicle, true)
    
        self.hoistPlaced = false

        Core.Notification({title = '', message = Lang['engine_swap_use_hoist_extract'], type = 'inform'})
    end

    -- complete engine to finish swap:
    self.completeEngine = function(vehicle)
        
        local anim = Config.EngineSwaps.RepairAnimation
        Lib.LoadAnim(anim.dict)
        TaskPlayAnim(player, anim.dict, anim.name, anim.blendIn, anim.blendOut, anim.duration, anim.flags, 0, 0, 0, 0)
        Wait(anim.duration)
        ClearPedTasks(player)
        Wait(500)
        
        if GetIsDoorValid(vehicle, 4) then
            RepairSound()
            SetVehicleFixed(vehicle)
            SetVehicleDoorOpen(vehicle, 4, false, true)
            SetVehicleOnGroundProperly(vehicle)
            FreezeEntityPosition(vehicle, false)

            local anim = { dict = 'anim@heists@fleeca_bank@scope_out@return_case', name = 'trevor_action', rot = vector3(0.0, 0.0, GetEntityHeading(player)), blendIn = 2.0, blendOut = 2.0, duration = 1000, flags = 49, time = 0.25 }
            Lib.LoadAnim(anim.dict)
            TaskPlayAnimAdvanced(player, anim.dict, anim.name, coords.x, coords.y, coords.z, anim.rot.x, anim.rot.y, anim.rot.z, anim.blendIn, anim.blendOut, anim.duration, anim.flags, anim.time, 0, 0)
            Wait(1000)
            curVehicle.setEngineSwapped()
            SetVehicleDoorShut(vehicle, 4, false)
            Wait(1000)
        end
    end

    -- create target to access engine bay
    self.createEngineTarget = function()
        local CanPrepare = function(entity)
            if Lib.IsEntityValid(entity) and GetEntityType(entity) ~= 0 then 
                if IsNearVehicleEngine(entity) then
                    if curHoist ~= nil and (curHoist.carrying ~= nil and curHoist.carrying == true) then 
                        return false
                    end
                    if curEngine.installed == nil or (curEngine.installed ~= nil and curEngine.installed == false) then
                        return true
                    end
                end
            end
            return false
        end
        local CanComplete = function(entity)
            if Lib.IsEntityValid(entity) and GetEntityType(entity) ~= 0 then 
                if IsNearVehicleEngine(entity) then
                    if curHoist ~= nil and (curHoist.carrying ~= nil and curHoist.carrying == true) then 
                        return false
                    end
                    if curEngine.installed ~= nil and curEngine.installed == true then
                        return true
                    end
                end
            end
            return false
        end
        Lib.AddLocalEntity(self.entity, {
            options = {
                {
                    name = self.target['access'].name, icon = self.target['access'].icon, label = self.target['access'].label, type = 'client', canInteract = CanPrepare, distance =  self.target['access'].distance,
                    onSelect = function(data)
                        Lib.RemoveLocalEntity(self.entity, {self.target['access'].name}, self.target['access'].label)
                        self.engine.coords = self.getEngineCoords()
                        TaskTurnPedToFaceCoord(player, self.engine.coords.x, self.engine.coords.y, self.engine.coords.z, -1)
                        Wait(1000)
                        self.prepareEngine(self.entity)
                        self.createVehicleEnginePoint()
                    end
                },
                {
                    name = self.target['complete'].name, icon = self.target['complete'].icon, label = self.target['complete'].label, type = 'client', canInteract = CanComplete, distance =  self.target['complete'].distance,
                    onSelect = function(data)
                        if curHoist.entity ~= nil and DoesEntityExist(curHoist.entity) then
                            return Core.Notification({title = '', message = Lang['engine_swap_must_collect_hoist'], type = 'inform'})
                        end
                        Lib.RemoveLocalEntity(self.entity, {self.target['complete'].name}, self.target['complete'].label)
                        self.engine.coords = self.getEngineCoords()
                        TaskTurnPedToFaceCoord(player, self.engine.coords.x, self.engine.coords.y, self.engine.coords.z, -1)
                        Wait(1000)
                        self.completeEngine(self.entity)
                    end
                }
            },
            distance = 5.0,
            canInteract = CanInteract,
        })
    end
    self.createEngineTarget()

    self.createEngineObject = function(pos)
        local engineCoords = self.getEngineCoords()
        self.engine.entity = CreateObjectProp('prop_car_engine_01', vector3(pos.x, pos.y, (engineCoords.z - 0.3)))
        FreezeEntityPosition(self.engine.entity, true)
        SetEntityAsMissionEntity(self.engine.entity, true, true)
        self.engine.modelHash = GetEntityModel(self.engine.entity)
        self.engine.d1, self.engine.d2 = GetModelDimensions(self.engine.modelHash)
        return self.engine.entity
    end

    self.setEngineExtracted = function()
        self.point.remove(self.point)
        self.engine.attached = true
    end

    self.setEngineSwapped = function()
        local netId = NetworkGetNetworkIdFromEntity(self.entity)
        TriggerServerEvent('tuningsystem:server:setEngineSound', netId, engine_swap.soundName, engine_swap.default)

        self.reset()
    end

    -- vehicle engine interaction point
    self.createVehicleEnginePoint = function()
        self.engine.coords = self.getEngineCoords()

        self.point = lib.points.new({
            coords = vector3(self.engine.coords.x, self.engine.coords.y, self.engine.coords.z + 1.0),
            distance = Config.EngineSwaps.VehiclePoint.distance,
            marker = Config.EngineSwaps.VehiclePoint.marker,
            updateColor = Config.EngineSwaps.VehiclePoint.marker.color['default'],
            precision = Config.EngineSwaps.VehiclePoint.precision,

            nearby = function(point)
                if (curHoist.carrying ~= nil and curHoist.carrying == true) then
                    if (self.hoistPlaced == false or (self.hoistPlaced == true and curHoist.carrying == true)) then  
                        -- extraction:
                        if (self.engine.attached ~= nil and self.engine.attached == false) then
                            -- vehicle engine marker:
                            DrawMarker(point.marker.type, point.coords.x, point.coords.y, point.coords.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, point.marker.scale.x, point.marker.scale.y, point.marker.scale.z, point.updateColor.x, point.updateColor.y, point.updateColor.z, point.updateColor.w, true, true, 2, true, nil, nil, false)
                            local hookCoords = curHoist.getHookCoords()
                            local distance = Vdist(point.coords.x, point.coords.y, 0.0, hookCoords.x, hookCoords.y, 0.0)
                            if distance <= point.precision then
                                point.updateColor = point.marker.color['success']
                                SetEntityDrawOutline(curHoist.entity, true)
                                SetEntityDrawOutlineColor(point.marker.color['success'].x, point.marker.color['success'].y, point.marker.color['success'].z, point.marker.color['success'].w)
                                if IsControlJustPressed(0, curHoist.handleKeybind) then
                                    SetEntityDrawOutline(curHoist.entity, false)
                                    curHoist.place()
                                    self.hoistPlaced = true
                                end
                            else
                                point.updateColor = point.marker.color['default']
                                SetEntityDrawOutline(curHoist.entity, false)
                                self.hoistPlaced = false
                            end
                        end
                        -- installation:
                        if (curHoist.attached ~= nil and curHoist.attached == true) and (curEngine.attached ~= nil and curEngine.attached == true) then
                            -- vehicle engine marker:
                            DrawMarker(point.marker.type, point.coords.x, point.coords.y, point.coords.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, point.marker.scale.x, point.marker.scale.y, point.marker.scale.z, point.updateColor.x, point.updateColor.y, point.updateColor.z, point.updateColor.w, true, true, 2, true, nil, nil, false)
                            -- hoist marker:
                            local hookCoords = curHoist.getHookCoords()
                            local distance = Vdist(point.coords.x, point.coords.y, 0.0, hookCoords.x, hookCoords.y, 0.0)
                            if distance <= point.precision then
                                point.updateColor = point.marker.color['success']
                                SetEntityDrawOutline(curEngine.entity, true)
                                SetEntityDrawOutlineColor(point.marker.color['success'].x, point.marker.color['success'].y, point.marker.color['success'].z, point.marker.color['success'].w)
                                if IsControlJustPressed(0, curHoist.handleKeybind) then
                                    SetEntityDrawOutline(curEngine.entity, false)
                                    curHoist.place()
                                    curEngine.hoistPlaced = true
                                end
                            else
                                point.updateColor = point.marker.color['default']
                                SetEntityDrawOutline(curEngine.entity, false)
                                curEngine.hoistPlaced = false
                            end
                        end
                    end
                end
            end,
        })

    end

    self.reset = function()
        engine_swap = {}
        curEngine = {}
        curHoist = {}
        curVehicle = {}
    end

    return self
end

CreateHoistClass = function(itemData)
    local self = {}

    self.item = itemData -- useable item data
    self.prop = Config.EngineSwaps.Hoist.model -- prop for hoist object
    self.boneId = Config.EngineSwaps.Hoist.boneId -- boneId for player to attach hoist to
    self.attachOffset = Config.EngineSwaps.Hoist.offset['engine'] -- offset to attach the engine on the hoist hook
    self.carryOffset = Config.EngineSwaps.Hoist.offset['carry'] -- offset to attach the hoist to the player

    self.attached = false
    self.carrying = false -- boolean to check if hoist is being carried
    self.inUse = false

    self.handleKeybind = Config.EngineSwaps.Hoist.keybind
    self.textUi = Config.EngineSwaps.Hoist.textUi

    self.target = Config.EngineSwaps.Hoist.target

    -- create hoist object:
    self.createObject = function(pos)
        if self.entity == nil or not DoesEntityExist(self.entity) then
            self.entity, self.netId = CreateObjectProp(self.prop, pos)
            if Config.EngineSwaps.Hoist.removeItem == true then
                TriggerServerEvent('t1ger_lib:server:removeItem', self.item.name, 1)
            end
            self.modelHash = GetEntityModel(self.entity)
            self.d1,self.d2 = GetModelDimensions(self.modelHash)
            self.createTarget()
        end
    end

    -- delete hoist object:
    self.deleteObject = function()
        if self.entity ~= nil and DoesEntityExist(self.entity) then
            NetworkFadeOutEntity(self.entity, false, true)
            if Config.EngineSwaps.Hoist.removeItem == true then
                TriggerServerEvent('t1ger_lib:server:addItem', self.item.name, 1)
            end
            DeleteEntity(self.entity)
        end
    end

    -- carry handle:
    self.carryHandle = function()
        while self.carrying do
            Wait(5)
            local isOpen, currentText = lib.isTextUIOpen()
            if not isOpen or (isOpen and currentText ~= self.textUi.string) then
                lib.showTextUI(self.textUi.string, {icon = self.textUi.icon, position = self.textUi.position, style = self.textUi.style})
            end
            if IsControlJustReleased(0, self.handleKeybind) then
                self.place()
            end
        end

        if self.carrying == false and lib.isTextUIOpen() then 
            lib.hideTextUI()
        end
    end

    -- carry hoist object:
    self.carry = function(createObj)
        if self.carrying == true then 
            return Core.Notification({title = '', message = Lang['engine_swap_already_carrying_hoist'], type = 'inform'})
        end
        if self.inUse == true then 
            return Core.Notification({title = '', message = Lang['engine_swap_already_hoist_in_use'], type = 'inform'})
        end
        if createObj == nil and (self.entity == nil or not DoesEntityExist(self.entity)) then 
            return print("the f*ck you want to carry bro? Entity does not exist...")
        end
        -- Carry Animation:
        local anim = Config.EngineSwaps.Hoist.anim
        Lib.LoadAnim(anim.dict)
        TaskPlayAnim(player, anim.dict, anim.name, anim.blendIn, anim.blendOut, anim.duration, anim.flags, 0, 0, 0, 0)
        Wait(250)
        -- if create object:
        if createObj ~= nil and createObj == true then
            self.createObject(GetEntityCoords(player))
        end
        -- Attach Hoist:
        AttachEntityToEntity(self.entity, player, GetPedBoneIndex(player, self.boneId), self.carryOffset.pos.x, self.carryOffset.pos.y, self.carryOffset.pos.z, self.carryOffset.rot.x, self.carryOffset.rot.y, self.carryOffset.rot.z, true, true, false, true, 2, 1)
        self.carrying = true
        -- Thread while carrying?
        self.carryHandle()
    end

    -- place hoist object:
    self.place = function()
        self.carrying = false
        lib.hideTextUI()
        DetachEntity(self.entity)
        PlaceObjectOnGroundProperly(self.entity)
        FreezeEntityPosition(self.entity, true)
        ClearPedTasks(player)
    end

    -- target for hoist entity:
    self.createTarget = function()
        local CanCarryHoist = function(entity)
            if Lib.IsEntityValid(entity) and GetEntityType(entity) ~= 0 then 
                if IsNearObjectProp(entity) then
                    if self.carrying == false then 
                        if curEngine.installed == nil or (curEngine.installed ~= nil and curEngine.installed == false) then
                            return true
                        end
                    end
                end
            end
            return false
        end
        local CanExtractEngine = function(entity)
            if Lib.IsEntityValid(entity) and GetEntityType(entity) ~= 0 then 
                if IsNearObjectProp(entity) then
                    if self.carrying == false then
                        if (curVehicle.hoistPlaced ~= nil and curVehicle.hoistPlaced == true) then
                            return true
                        end
                    end
                end
            end
            return false
        end
        local CanAttachEngine = function(entity)
            if Lib.IsEntityValid(entity) and GetEntityType(entity) ~= 0 then 
                if IsNearObjectProp(entity) then
                    if self.carrying == false then
                        if (curEngine.canAttach ~= nil and curEngine.canAttach == true) and (curEngine.attached ~= nil and curEngine.attached == false) then
                            return true
                        end
                    end
                end
            end
            return false
        end
        local CanDetachEngine = function(entity)
            if Lib.IsEntityValid(entity) and GetEntityType(entity) ~= 0 then 
                if IsNearObjectProp(entity) then
                    if self.carrying == false then
                        if (curEngine.hoistPlaced ~= nil and curEngine.hoistPlaced == true) and (curEngine.attached ~= nil and curEngine.attached == true) then
                            return true
                        end
                    end
                end
            end
            return false
        end
        local CanRemoveHoist = function(entity)
            if Lib.IsEntityValid(entity) and GetEntityType(entity) ~= 0 then 
                if IsNearObjectProp(entity) then
                    if self.carrying == false then
                        if curEngine.installed ~= nil and curEngine.installed == true then
                            return true
                        end
                    end
                end
            end
            return false
        end
        Lib.AddLocalEntity(self.entity, {
            options = {
                {
                    name = self.target['carry'].name, icon = self.target['carry'].icon, label = self.target['carry'].label, type = 'client', canInteract = CanCarryHoist, distance =  self.target['carry'].distance,
                    onSelect = function(data)
                        if CanCarryHoist(self.entity) then
                            self.carry()
                        end
                    end
                },
                {
                    name = self.target['extract'].name, icon = self.target['extract'].icon, label = self.target['extract'].label, type = 'client', canInteract = CanExtractEngine, distance =  self.target['extract'].distance,
                    onSelect = function(data)
                        if CanExtractEngine(self.entity) then
                            self.extractVehicleEngine()
                        end
                    end
                },
                {
                    name = self.target['attach'].name, icon = self.target['attach'].icon, label = self.target['attach'].label, type = 'client', canInteract = CanAttachEngine, distance =  self.target['attach'].distance,
                    onSelect = function(data)
                        if CanAttachEngine(self.entity) then
                            self.attachEngine()
                        end
                    end
                },
                {
                    name = self.target['detach'].name, icon = self.target['detach'].icon, label = self.target['detach'].label, type = 'client', canInteract = CanDetachEngine, distance =  self.target['detach'].distance,
                    onSelect = function(data)
                        if CanDetachEngine(self.entity) then
                            self.detachEngine()
                        end
                    end
                },
                {
                    name = self.target['remove'].name, icon = self.target['remove'].icon, label = self.target['remove'].label, type = 'client', canInteract = CanRemoveHoist, distance =  self.target['remove'].distance,
                    onSelect = function(data)
                        if CanRemoveHoist(self.entity) then
                            self.deleteObject()
                        end
                    end
                }
            },
            distance = 2.0,
            canInteract = IsNearObjectProp,
        })
    end

    -- attach engine to hoist:
    self.attachHandle = function(engineEntity, dMax)
        self.inUse = true
        -- calculate travel distance:
        local hookCoords = self.getHookCoords()
        local engineCoords = GetOffsetFromEntityInWorldCoords(engineEntity, 0.0, (dMax.y - 0.6), dMax.z - 0.25)
        local height = Vdist(0.0, 0.0, hookCoords.z, 0.0, 0.0, engineCoords.z)

        -- calculate rotation distance:
        local hoistRotation = GetEntityRotation(self.entity)
        local engineRotation = GetEntityRotation(engineEntity)
        local rotationDiff = Vdist(0.0, 0.0, hoistRotation[3], 0.0, 0.0, engineRotation[3])

        if Config.EngineSwaps.Hoist.playSound == true then
            PlaySoundFromEntity(GetSoundId(), 'Hook_Engage', curHoist.entity, 'DLC_IE_Steal_Cargobob_Sounds', true, 0)
        end

        for i = 1, Config.EngineSwaps.Hoist.smoothness do
            local currentCoords = GetEntityCoords(engineEntity)
            local currentRotation = GetEntityRotation(engineEntity)
            
            if hoistRotation[3] > 0 then
                SetEntityRotation(engineEntity, currentRotation[1], currentRotation[2], currentRotation[3] + (rotationDiff/Config.EngineSwaps.Hoist.smoothness), 2, true)
            else
                SetEntityRotation(engineEntity, currentRotation[1], currentRotation[2], currentRotation[3] - (rotationDiff/Config.EngineSwaps.Hoist.smoothness), 2, true)
            end

            SetEntityCoordsNoOffset(engineEntity, currentCoords.x, currentCoords.y, currentCoords.z + (height/Config.EngineSwaps.Hoist.smoothness), true, false, false)
            Wait(Config.EngineSwaps.Hoist.delay)
        end

        AttachEntityToEntity(engineEntity, self.entity, 20, self.attachOffset.pos.x, self.attachOffset.pos.y, self.attachOffset.pos.z, self.attachOffset.rot.x, self.attachOffset.rot.y, self.attachOffset.rot.z, true, true, true, false, false, true)
        self.attached = true
        self.inUse = false
    end

    -- detach engine from hoist:
    self.detachHandle = function(engineEntity)
        self.inUse = true
        local hookCoords = self.getHookCoords()
        local height = Vdist(0.0, 0.0, hookCoords.z, 0.0, 0.0, coords.z - 0.9)
        DetachEntity(engineEntity, true, false)
        FreezeEntityPosition(engineEntity, true)
        if Config.EngineSwaps.Hoist.playSound == true then
            PlaySoundFromEntity(GetSoundId(), 'Hook_Engage', curHoist.entity, 'DLC_IE_Steal_Cargobob_Sounds', true, 0)
        end
        for i = 1, Config.EngineSwaps.Hoist.smoothness do
            local currentCoords = GetEntityCoords(engineEntity)
            SetEntityCoordsNoOffset(engineEntity, currentCoords.x, currentCoords.y, currentCoords.z - (height/Config.EngineSwaps.Hoist.smoothness), true, false, false)
            Wait(Config.EngineSwaps.Hoist.delay)
        end

        self.attached = false
        self.inUse = false
    end
    
    -- get hoist hook coords:
    self.getHookCoords = function()
        return GetOffsetFromEntityInWorldCoords(self.entity, (0.0 - 0.08), (self.d1.y + 0.1), 1.5)
    end

    -- extract vehicle engine:
    self.extractVehicleEngine = function()
        Lib.RemoveLocalEntity(self.entity, {self.target['extract'].name}, self.target['extract'].label)

        local hookCoords = self.getHookCoords()
        curVehicle.engine.entity = curVehicle.createEngineObject(hookCoords)

        self.attachHandle(curVehicle.engine.entity, curVehicle.engine.d2)
        curVehicle.setEngineExtracted()

        -- create dump target:
        Lib.AddLocalEntity(curVehicle.engine.entity, {
            options = {
                {
                    name = 'tuningsystem:target:dumpEngine', icon = Config.EngineSwaps.ExtractTarget.icon, label = Config.EngineSwaps.ExtractTarget.label, type = 'client', canInteract = IsNearObjectProp, distance =  Config.EngineSwaps.ExtractTarget.distance,
                    onSelect = function(data)
                        Lib.RemoveLocalEntity(curVehicle.engine.entity, {'tuningsystem:target:dumpEngine'}, Config.EngineSwaps.ExtractTarget.label)
                        -- Delete Entity:
                        NetworkFadeOutEntity(curVehicle.engine.entity, false, true)
                        -- Create New Engine Class:
                        curEngine = CreateEngineClass()
                        Core.Notification({title = Lang['engine_swap_order_arrived']:format(engine_swap.label), message = msg, type = 'inform'})
                        Wait(2000)
                        DeleteEntity(curVehicle.engine.entity)
                        curEngine.createPoint()
                    end
                }
            },
            distance = Config.EngineSwaps.ExtractTarget.distance,
            canInteract = IsNearObjectProp,
        })
    end

    self.attachEngine = function()
        Lib.RemoveLocalEntity(self.entity, {self.target['attach'].name}, self.target['attach'].label)
        self.attachHandle(curEngine.entity, curEngine.d2)
        curEngine.setAttached()
    end

    self.detachEngine = function()
        Lib.RemoveLocalEntity(self.entity, {self.target['detach'].name}, self.target['detach'].label)
        self.detachHandle(curEngine.entity)
        curEngine.setInstalled()
    end

    return self
end

CreateEngineClass = function()
    local self = {}

    self.canAttach = false
    self.attached = false
    self.installed = false

    self.createObject = function()
        self.entity, self.netId = CreateObjectProp('prop_car_engine_01', engine_swap.pos)
        PlaceObjectOnGroundProperly(self.entity)
        FreezeEntityPosition(self.entity, true)
        SetEntityAsMissionEntity(self.entity, true, true)

        self.modelHash = GetEntityModel(self.entity)
        self.d1,self.d2 = GetModelDimensions(self.modelHash)
    end
    self.createObject()

    self.setAttached = function()
        self.attached = true
        self.point.remove(self.point)
        curVehicle.createVehicleEnginePoint()
    end

    self.setInstalled = function()
        self.attached = false
        self.installed = true
        self.point.remove(self.point)
        DeleteEntity(self.entity)
    end

    self.getAttachPoint = function()
        return GetOffsetFromEntityInWorldCoords(self.entity, 0.0, (self.d2.y - 0.6), 1.5)
    end

    self.createPoint = function()
        local attachPoint = self.getAttachPoint()

        self.point = lib.points.new({
            coords = attachPoint,
            distance = Config.EngineSwaps.EnginePoint.distance,
            marker = Config.EngineSwaps.EnginePoint.marker,
            updateColor = Config.EngineSwaps.EnginePoint.marker.color['engine'],
            precision = Config.EngineSwaps.EnginePoint.precision,

            nearby = function(point)
                if not self.attached then 
                    if (curHoist.carrying ~= nil and curHoist.carrying == true) then
                        if (self.canAttach == false or (self.canAttach == true and curHoist.carrying == true)) then
                            -- engine marker:
                            DrawMarker(point.marker.type, point.coords.x, point.coords.y, point.coords.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, point.marker.scale.x, point.marker.scale.y, point.marker.scale.z, point.updateColor.x, point.updateColor.y, point.updateColor.z, point.updateColor.w, true, true, 2, true, nil, nil, false)
                            local hookCoords = curHoist.getHookCoords()
                            -- hoist marker:
                            DrawMarker(point.marker.type, hookCoords.x, hookCoords.y, hookCoords.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, point.marker.scale.x, point.marker.scale.y, point.marker.scale.z, point.marker.color['hoist'].x, point.marker.color['hoist'].y, point.marker.color['hoist'].z, point.marker.color['hoist'].w, false, false, 2, false, nil, nil, false)

                            local distance = Vdist(point.coords.x, point.coords.y, 0.0, hookCoords.x, hookCoords.y, 0.0)
                            if distance <= point.precision then
                                point.updateColor = point.marker.color['success']
                                if IsControlJustPressed(0, curHoist.handleKeybind) then
                                    curHoist.place()
                                    self.canAttach = true
                                end
                            else
                                point.updateColor = point.marker.color['engine']
                                self.canAttach = false
                            end
                        end
                    end
                end
            end,
        })
    end

    return self
end

RegisterNetEvent('tuningsystem:client:useEngineHoist')
AddEventHandler('tuningsystem:client:useEngineHoist', function(itemId, itemData)
    local isTunerJob, shopId = IsTunerJob(Core.GetJob().name)

    if not isTunerJob then
        return Core.Notification({title = '', message = Lang['need_tuner_job'], type = 'inform'}) 
    end

    if curHoist ~= nil and next(curHoist) ~= nil then
        return Core.Notification({title = '', message = Lang['engine_swap_only_one_hoist'], type = 'inform'})
    end

    if engine_swap == nil or (engine_swap ~= nil and next(engine_swap) == nil) then
        return Core.Notification({ title = '', message = Lang['engine_swap_must_start_task'], type = 'inform'})
    end

    local isOpen, currentText = lib.isTextUIOpen()
    if isOpen then
        lib.hideTextUI() 
    end
    
    curHoist = CreateHoistClass(itemData)
    curHoist.carry(true)
end)

AddStateBagChangeHandler('tuningsystem:forceEngineSound' --[[key filter]], nil --[[bag filter]], function(bagName, key, value, _unused, replicated)
	Wait(0)
	local entity = GetEntityFromStateBagName(bagName)
	if not value or entity == 0 then return end
	ForceVehicleEngineAudio(entity, value)
    if Config.Debug then
        print("engine sound updated to: ", value)
    end
end)

if Config.Debug then
    RegisterCommand('engineswap', function(source, args, rawCommand)
        if isPlayerAdmin then
            local vehicle = GetVehiclePedIsIn(player, false)
            local plate = GetVehicleNumberPlateText(vehicle)
            local soundName = args[1] ~= nil and args[1] or 't20'
            
            local netId = NetworkGetNetworkIdFromEntity(vehicle)
            local modelName = GetVehicleModelName(vehicle)

            TriggerServerEvent('tuningsystem:server:setEngineSound', netId, soundName, modelName)
        end
    end, false)
end