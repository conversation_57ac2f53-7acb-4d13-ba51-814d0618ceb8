if Config.AdminMenu.Command.enable then
    RegisterCommand(Config.AdminMenu.Command.string, function()
        TuningAdminMenu()
    end)
end

if Config.AdminMenu.Keybind.enable then
    RegisterCommand('TuningAdmin', function()
        TuningAdminMenu()
    end, false)
    RegisterKeyMapping('TuningAdmin', Config.AdminMenu.Keybind.description, 'keyboard', Config.AdminMenu.Keybind.defaultMapping)
end

TuningAdminMenu = function()
    if isPlayerAdmin then
        lib.registerContext({
            id = 'admin_tuning_menu',
            title = Lang['title_tuning_admin'],
            options = {
                {
                    title = Lang['title_admin_view_shops'],
                    icon = 'list',
                    event = 'tuningsystem:client:viewShops',
                    arrow = true,
                },
                {
                    title = Lang['title_admin_create_shop'],
                    icon = 'plus',
                    onSelect = function(args)
                        local x,y,z = table.unpack(GetEntityCoords(PlayerPedId()))
                        local display_coords = {Lib.RoundNumber(x,2), Lib.RoundNumber(y,2), Lib.RoundNumber(z,2)}
                        local blipCoords = vector3(Lib.RoundNumber(x,2), Lib.RoundNumber(y,2), Lib.RoundNumber(z,2))
                        local categories = {}
                        local i = 1
                        for k,v in pairs(Config.ModCategories) do
                            categories[i] = {label = k, value = i}
                            i = i + 1
                        end
                        local input = false
                        if Config.ShopCreator.Blip.input then
                            input = lib.inputDialog(Lang['input_title_shop_creation'], {
                                {type = 'input', label = Lang['input_label_shop_name'], required = true, description = Lang['input_desc_shop_name']},
                                {type = 'input', label = Lang['input_label_job_name'], required = true, description = Lang['input_desc_job_name']},
                                {type = 'input', label = Lang['input_label_job_label'], required = true, description = Lang['input_desc_job_label']},
                                {type = 'number', label = Lang['input_label_account'], required = true, default = 0, min = 0, description = Lang['input_desc_account']},
                                {type = 'multi-select', label = Lang['input_label_select_categories'], required = true, clearable = true, options = categories, description = Lang['input_desc_select_categories']},
                                {type = 'input', label = Lang['input_label_blip_coords'], disabled = true, default = json.encode(display_coords), value = display_coords, description = Lang['input_desc_blip_coords']},
                                {type = 'checkbox', label = Lang['input_label_blip_enable'], checked = true, description = Lang['input_desc_blip_enable']},
                                {type = 'number', label = Lang['input_label_blip_sprite'], required = true, default = Config.ShopCreator.Blip.sprite, min = 0, max = 826, description = Lang['input_desc_blip_sprite']},
                                {type = 'number', label = Lang['input_label_blip_color'], required = true, default = Config.ShopCreator.Blip.color, min = 0, max = 85, description = Lang['input_desc_blip_color']},
                            })
                        else
                            input = lib.inputDialog(Lang['input_title_shop_creation'], {
                                {type = 'input', label = Lang['input_label_shop_name'], required = true, description = Lang['input_desc_shop_name']},
                                {type = 'input', label = Lang['input_label_job_name'], required = true, description = Lang['input_desc_job_name']},
                                {type = 'input', label = Lang['input_label_job_label'], required = true, description = Lang['input_desc_job_label']},
                                {type = 'number', label = Lang['input_label_account'], required = true, default = 0, min = 0, description = Lang['input_desc_account']},
                                {type = 'multi-select', label = Lang['input_label_select_categories'], required = true, clearable = true, options = categories, description = Lang['input_desc_select_categories']},
                                {type = 'input', label = Lang['input_label_blip_coords'], disabled = true, default = json.encode(display_coords), value = display_coords, description = Lang['input_desc_blip_coords']},
                                {type = 'checkbox', label = Lang['input_label_blip_enable'], checked = true},
                            })
                        end

                        if not input then 
                            return TuningAdminMenu()
                        end

                        local modCategories = {}
                        for k,v in pairs(input[5]) do
                            modCategories[k] = categories[v].label
                        end

                        local data = {shop_name = input[1], job_name = input[2], job_label = input[3], account = input[4], categories = modCategories, blip_coords = blipCoords, blip_use = input[7] }
                        if Config.ShopCreator.Blip.input then
                            data.blip_sprite = input[8]
                            data.blip_color = input[9]
                        end

                        TriggerServerEvent('tuningsystem:server:createShop', data)
                    end,
                },
            },
        })
        lib.showContext('admin_tuning_menu')
    else
        if Config.Debug then
            print('you are not an admin.')
        end
    end
end

RegisterNetEvent('tuningsystem:client:viewShops', function()
    local menuOptions = {}
    Core.TriggerCallback('tuningsystem:server:getShops', function(results)
        if results and next(results) then
            for id, shop in pairs(results) do
                local boss_name = Lang['meta_not_answered']
                for k,v in pairs(shop.employees) do
                    if v.identifier == shop.boss then
                        boss_name = v.char
                    end
                end
                table.insert(menuOptions, {
                    title = shop.name,
                    icon = 'people-group',
                    args = shop.id,
                    metadata = {
                        {label = Lang['meta_shop_id'], value = id},
                        {label = Lang['meta_boss'], value = boss_name or Lang['meta_not_answered']},
                        {label = Lang['meta_boss_id'], value = shop.boss or Lang['meta_not_answered']},
                        {label = Lang['meta_job_name'], value = Lang['meta_job_label_and_name']:format(shop.job.label, shop.job.name)},
                    },
                    event = 'tuningsystem:client:manageShop'
                })
            end
            lib.registerContext({
                id = 'admin_view_shops',
                title = Lang['title_admin_view_shops'],
                menu = 'admin_tuning_menu',
                onBack = function()
                    TuningAdminMenu()
                end,
                options = menuOptions
            })
            lib.showContext('admin_view_shops')
        else
            TuningAdminMenu()
            Core.Notification({
                title = '',
                message = Lang['no_tuner_shops'],
                type = 'error'
            })
        end
    end)
end)

RegisterNetEvent('tuningsystem:client:manageShop', function(shopId)
    local meta_data = {}
    for cat,data in pairs(Config.ModCategories) do
        local enabled = false
        if Config.Shops[shopId].categories ~= nil and next(Config.Shops[shopId].categories) then
            for k,v in pairs(Config.Shops[shopId].categories) do
                if cat == v then
                    enabled = true
                    break
                end
            end
        end
        table.insert(meta_data, {label = data.title, value = enabled})
    end
    lib.registerContext({
        id = 'admin_manage_shop',
        title = Lang['title_admin_manage_shop']:format(Config.Shops[shopId].name, Config.Shops[shopId].id),
        menu = 'admin_view_shops',
        options = {
            {
                title = Lang['title_admin_set_boss'], description = Lang['desc_admin_set_boss'], icon = 'rotate', args = shopId, event = 'tuningsystem:client:setBoss'
            },
            {
                title = Lang['title_admin_set_account'], description = Lang['desc_admin_set_account']:format(Config.Shops[shopId].account), icon = 'dollar', args = shopId,
                onSelect = function()
                    local input = lib.inputDialog(Lang['input_title_set_account_balance'], {
                        {type = 'input', label = Lang['input_label_cur_balance'], icon = 'sack-dollar', disabled = true, default = (Config.Shops[shopId].account)},
                        {type = 'number', label = Lang['input_label_enter_amount'], icon = 'money-bill-trend-up', min = 0, required = true, placeholder = 100, description = Lang['input_desc_set_account_balance']}
                    })
                    if not input then
                        return TriggerEvent('tuningsystem:client:manageShop', shopId)
                    end
                    if input[2] == 0 then 
                        Core.Notification({
                            title = '',
                            message = Lang['input_amount_higher_0'],
                            type = 'error'
                        })
                        return TriggerEvent('tuningsystem:client:manageShop', shopId)
                    end
                    TriggerServerEvent('tuningsystem:server:setAccount', shopId, tonumber(input[2]))
                end
            },
            {
                title = Lang['title_admin_set_mod_categories'], description = Lang['desc_admin_set_mod_categories'], metadata = meta_data, icon = 'list', args = shopId,
                onSelect = function()
                    local categories = {}
                    local i = 1
                    for k,v in pairs(Config.ModCategories) do
                        categories[i] = {label = k, value = i}
                        i = i + 1
                    end
                    local input = lib.inputDialog(Lang['input_title_set_mod_categories'], {
                        {type = 'multi-select', label = Lang['input_label_select_categories'], required = true, clearable = true, options = categories, description = Lang['input_desc_select_categories']}
                    })
                    if not input then 
                        return TriggerEvent('tuningsystem:client:manageShop', shopId)
                    end
                    local modCategories = {}
                    for k,v in pairs(input[1]) do
                        modCategories[k] = categories[v].label
                    end
                    TriggerServerEvent('tuningsystem:server:setCategories', shopId, modCategories)
                end
            },
            {
                title = Lang['title_admin_marker_manage'], description = Lang['desc_admin_marker_management'], icon = 'arrow-pointer', arrow = true, args = shopId, event = 'tuningsystem:client:markerManagement'
            },
            {
                title = Lang['title_admin_set_delivery_coords'], description = Lang['desc_admin_set_delivery_coords'], metadata = {{label = Lang['meta_cur_coords'], value = Config.Shops[shopId].delivery ~= nil and json.encode(Config.Shops[shopId].delivery) or nil}}, icon = 'location-dot', args = shopId,
                onSelect = function()
                    local x,y,z = table.unpack(GetEntityCoords(PlayerPedId()))
                    local deliveryCoords = {x = Lib.RoundNumber(x,2), y = Lib.RoundNumber(y,2), z = Lib.RoundNumber(z,2)}
                    local alert = lib.alertDialog({
                        header = Lang['title_admin_set_delivery_coords'],
                        content = Lang['alert_content_delivery_coords']:format(json.encode(deliveryCoords)),
                        centered = true,
                        cancel = true
                    })
                    if alert == 'confirm' then
                        TriggerServerEvent('tuningsystem:server:setDeliveryCoords', shopId, deliveryCoords)
                    else
                        TriggerEvent('tuningsystem:client:manageShop', shopId)
                    end
                end
            },
            {
                title = Lang['title_admin_delete_shop'], description = Lang['desc_admin_delete_shop'], icon = 'trash', args = shopId,
                onSelect = function(shopId)
                    local alert = lib.alertDialog({
                        header = Lang['alert_header_delete_shop'],
                        content = Lang['alert_content_shop_delete_confirm']:format(Config.Shops[shopId].name, shopId),
                        centered = true,
                        cancel = true
                    })
                    if alert == 'confirm' then
                        TriggerServerEvent('tuningsystem:server:deleteShop', shopId)
                    else
                        TriggerEvent('tuningsystem:client:manageShop', shopId)
                    end
                end
            },
        },
    })
    lib.showContext('admin_manage_shop')
end)

RegisterNetEvent('tuningsystem:client:markerManagement', function(shopId)
    local menuOptions = {}

    menuOptions[1] = {
        title = Lang['title_admin_create_marker'],
        icon = 'plus',
        description = Lang['desc_admin_marker_create'],
        onSelect = function()
            local x,y,z = table.unpack(GetEntityCoords(PlayerPedId()))
            local markerCoords = {Lib.RoundNumber(x,2), Lib.RoundNumber(y,2), Lib.RoundNumber(z,2)}
            local markerClasses = {}
            for k,v in pairs(Config.Markers) do
                if v.enable then
                    markerClasses[#markerClasses + 1] = {label = v.interact.title, value = #markerClasses + 1, class = k}
                end
            end
            local input = lib.inputDialog(Lang['input_title_create_marker'], {
                {type = 'select', label = Lang['input_label_marker_class'], required = true, clearable = true, options = markerClasses, description = Lang['input_desc_marker_class']},
                {type = 'input', label = Lang['input_label_marker_name'], required = true, description = Lang['input_desc_marker_name']},
                {type = 'input', label = Lang['input_label_marker_coords'], disabled = true, default = json.encode(markerCoords), value = markerCoords, description = Lang['input_desc_marker_coords']},
                {type = 'number', label = Lang['input_label_marker_type'], default = 20, required = true, description = Lang['input_desc_marker_type'], min = 0, max = 43},
                {type = 'color', label = Lang['input_label_marker_color'], required = true, default = Config.MarkerDefaultRGBA, description = Lang['input_desc_marker_color'], format = 'rgba'},
                {type = 'checkbox', label = Lang['input_label_marker_bob'], description = Lang['input_desc_marker_bob']},
                {type = 'checkbox', label = Lang['input_label_marker_camera'], checked = true, description = Lang['input_desc_marker_camera']},
                {type = 'checkbox', label = Lang['input_label_marker_blip'], checked = true, description = Lang['input_desc_marker_blip']},
            })

            if not input then
                return TriggerEvent('tuningsystem:client:markerManagement', shopId)
            end
            
            local class = markerClasses[input[1]].class
            local data = {class = class, name = input[2], coords = {x = markerCoords[1], y = markerCoords[2], z = markerCoords[3]}, type = input[4], color = Lib.GetRGBA(input[5]), bobUpAndDown = input[6], faceCamera = input[7], blip = input[8]}
            if class == 'storage' or class == 'stash' then
                data.stash = {slots = Config.Stash.slots, weight = Config.Stash.weight}
            end
            TriggerServerEvent('tuningsystem:server:createMarker', shopId, class, data)
        end,
    }

    if Config.Shops[shopId].markers ~= nil and next(Config.Shops[shopId].markers) then
        local viewMarkers = false
        for k,v in pairs(Config.Markers) do
            if Config.Shops[shopId].markers[k] ~= nil and next(Config.Shops[shopId].markers[k]) then 
                viewMarkers = true 
                break
            end
        end
        if viewMarkers then 
            menuOptions[2] = {
                title = Lang['title_admin_view_markers'],
                icon = 'list',
                description = Lang['desc_admin_marker_view'],
                args = shopId,
                event = 'tuningsystem:client:viewMarkers',
            }
        end
    end

    lib.registerContext({
        id = 'marker_management',
        title = Lang['title_admin_marker_manage'],
        menu = 'admin_manage_shop',
        onBack = function()
            TriggerEvent('tuningsystem:client:manageShop', shopId)
        end,
        options = menuOptions,
    })
    lib.showContext('marker_management')
end)

RegisterNetEvent('tuningsystem:client:viewMarkers', function(shopId)
    local menuOptions = {}
    if Config.Shops[shopId].markers ~= nil and next(Config.Shops[shopId].markers) then
        for class,markers in pairs(Config.Shops[shopId].markers) do
            for k,v in pairs(markers) do
                local meta_data = {
                    {label = Lang['meta_marker_class'], value = Config.Markers[class].interact.title},
                    {label = Lang['meta_marker_name'], value = v.name or v.id},
                    {label = Lang['meta_marker_coords'], value = json.encode(v.coords)}
                }
                menuOptions[#menuOptions + 1] = {
                    title = v.name or v.id,
                    icon = Config.Markers[class].interact.icon,
                    description = Lang['desc_admin_marker_manage'],
                    metadata = meta_data,
                    args = {shopId = shopId, class = class, markerKey = v.id, marker = v},
                    event = 'tuningsystem:client:manageMarker',
                }
            end
        end
    else
        return TriggerEvent('tuningsystem:client:markerManagement', shopId)
    end

    lib.registerContext({
        id = 'view_markers',
        title = Lang['title_admin_view_markers'],
        menu = 'marker_management',
        options = menuOptions
    })
    lib.showContext('view_markers')
end)

RegisterNetEvent('tuningsystem:client:manageMarker', function(args)
    local shopId, class, markerKey, marker = args.shopId, args.class, args.markerKey, args.marker
    local menuOptions = {}

    if Config.MarkerTeleport == true then
        menuOptions[#menuOptions + 1] = {
            title = Lang['title_admin_teleport_marker'],
            icon = 'plane',
            description = Lang['desc_admin_marker_teleport'],
            metadata = {
                {label = Lang['meta_marker_coords'], value = json.encode(marker.coords)}
            },
            onSelect = function()
                SetEntityCoords(player, marker.coords.x, marker.coords.y, marker.coords.z, 0.0, 0.0, 0.0, false)
            end
        }
    end

    if Config.MarkerEdit == true then
        menuOptions[#menuOptions + 1] = {
            title = Lang['title_admin_edit_marker'],
            icon = 'pen-to-square',
            description = Lang['desc_admin_marker_edit'],
            onSelect = function()
                local input = false
                local x,y,z = table.unpack(GetEntityCoords(PlayerPedId()))
                local markerCoords = {Lib.RoundNumber(x,2), Lib.RoundNumber(y,2), Lib.RoundNumber(z,2)}
                if class == 'storage' or class == 'stash' then
                    input = lib.inputDialog('Edit Selected Marker', {
                        {type = 'select', label = Lang['input_label_marker_class'], options = {{label = Config.Markers[class].interact.title, value = 1}},disabled = true, default = 1, description = Lang['input_desc_marker_class']},
                        {type = 'input', label = Lang['input_label_marker_name'], required = true, description = Lang['input_desc_marker_name']},
                        {type = 'input', label = Lang['input_label_marker_coords'], disabled = true, default = json.encode(markerCoords), value = markerCoords, description = Lang['input_desc_marker_coords']},
                        {type = 'number', label = Lang['input_label_marker_type'], default = 20, required = true, description = Lang['input_desc_marker_type'], min = 0, max = 43},
                        {type = 'color', label = Lang['input_label_marker_color'], required = true, default = Config.MarkerDefaultRGBA, description = Lang['input_desc_marker_color'], format = 'rgba'},
                        {type = 'checkbox', label = Lang['input_label_marker_bob'], description = Lang['input_desc_marker_bob']},
                        {type = 'checkbox', label = Lang['input_label_marker_camera'], checked = true, description = Lang['input_desc_marker_camera']},
                        {type = 'checkbox', label = Lang['input_label_marker_blip'], checked = true, description = Lang['input_desc_marker_blip']},
                        {type = 'number', label = Lang['input_label_marker_slots'], required = true, min = 1, max = Config.Stash.maxSlots, description = Lang['input_desc_marker_slots']},
                        {type = 'number', label = Lang['input_label_marker_weight'], required = true, min = 1, max = Config.Stash.maxWeight, description = Lang['input_desc_marker_weight']},
                    })
                else
                    input = lib.inputDialog('Edit Selected Marker', {
                        {type = 'select', label = Lang['input_label_marker_class'], options = {{label = Config.Markers[class].interact.title, value = 1}},disabled = true, default = 1, description = Lang['input_desc_marker_class']},
                        {type = 'input', label = Lang['input_label_marker_name'], required = true, description = Lang['input_desc_marker_name']},
                        {type = 'input', label = Lang['input_label_marker_coords'], disabled = true, default = json.encode(markerCoords), value = markerCoords, description = Lang['input_desc_marker_coords']},
                        {type = 'number', label = Lang['input_label_marker_type'], default = 20, required = true, description = Lang['input_desc_marker_type'], min = 0, max = 43},
                        {type = 'color', label = Lang['input_label_marker_color'], required = true, default = Config.MarkerDefaultRGBA, description = Lang['input_desc_marker_color'], format = 'rgba'},
                        {type = 'checkbox', label = Lang['input_label_marker_bob'], description = Lang['input_desc_marker_bob']},
                        {type = 'checkbox', label = Lang['input_label_marker_camera'], checked = true, description = Lang['input_desc_marker_camera']},
                        {type = 'checkbox', label = Lang['input_label_marker_blip'], checked = true, description = Lang['input_desc_marker_blip']},
                    })
                end
    
                if not input then
                    return TriggerEvent('tuningsystem:client:viewMarkers', shopId)
                end
                
                local data = {class = class, name = input[2], coords = {x = markerCoords[1], y = markerCoords[2], z = markerCoords[3]}, type = input[4], color = Lib.GetRGBA(input[5]), bobUpAndDown = input[6], faceCamera = input[7], blip = input[8]}
                if class == 'storage' or class == 'stash' then
                    data.stash = {slots = input[9] or Config.Stash.slots, weight = input[10] or Config.Stash.weight}
                end
                TriggerServerEvent('tuningsystem:server:editMarker', shopId, class, data, marker.id)
            end
        }
    end

    menuOptions[#menuOptions + 1] = {
        title = Lang['title_admin_delete_marker'],
        icon = 'trash',
        description = Lang['desc_admin_marker_delete'],
        onSelect = function()
            TriggerServerEvent('tuningsystem:server:deleteMarker', shopId, class, marker.id)
        end
    }
    

    lib.registerContext({
        id = 'manage_marker',
        title = Lang['title_admin_manage_marker']:format(marker.name),
        menu = 'view_markers',
        onBack = function()
            TriggerEvent('tuningsystem:client:viewMarkers', shopId)
        end,
        options = menuOptions
    })
    lib.showContext('manage_marker')
end)

RegisterNetEvent('tuningsystem:client:setBoss', function(shopId)
    local players = {}
    if Config.Shops[shopId].boss ~= nil then
        local bossName = Lang['meta_not_answered']
        for k,v in pairs(Config.Shops[shopId].employees) do
            if v.identifier == Config.Shops[shopId].boss then
                bossName = v.char
            end
        end
        players[1] = {
            title = Lang['title_admin_remove_boss'],
            icon = 'trash',
            description = Lang['desc_admin_remove_boss'],
            metadata = {
                {label = Lang['meta_identifier'], value = Config.Shops[shopId].boss},
                {label = Lang['meta_player_char'], value = bossName or Lang['meta_not_answered']},
            },
            onSelect = function()
                TriggerServerEvent('tuningsystem:server:setBoss', shopId, Config.Shops[shopId].boss, true)
            end
        }
    end
    Core.TriggerCallback('t1ger_lib:getOnlinePlayers', function(results)
        for i = 1, #results, 1 do
            table.insert(players, {
                title = results[i].name,
                icon = 'user',
                description = Lang['desc_admin_click_to_set_boss'],
                metadata = {
                    {label = Lang['meta_player_id'], value = results[i].source},
                    {label = Lang['meta_player_char'], value = results[i].char},
                },
                onSelect = function()
                    TriggerServerEvent('tuningsystem:server:setBoss', shopId, results[i])
                end
            })
        end
        lib.registerContext({
            id = 'admin_set_boss',
            title = Lang['title_admin_set_boss'],
            menu = 'admin_manage_shop',
            options = players
        })
        lib.showContext('admin_set_boss')
    end)
end)