:root{--phone-color-primary: rgb(255, 255, 255);--phone-color-opacity: rgba(242, 242, 242, .4);--phone-color-opacity2: rgb(30, 30, 30, .5);--phone-color-highlight: rgb(250, 250, 250);--phone-color-highlight2: rgb(240, 240, 240);--phone-color-highlight3: rgb(220, 220, 220);--phone-highlight-opacity15: rgba(145, 145, 145, .15);--phone-highlight-opacity35: rgba(145, 145, 145, .35);--phone-highlight-opacity45: rgba(145, 145, 145, .45);--phone-highlight-opacity55: rgba(145, 145, 145, .55);--phone-color-input: rgba(241, 241, 241, .656);--phone-text-primary: rgb(0, 0, 0);--phone-text-secondary: rgb(142, 142, 147);--phone-color-hover: rgb(240, 240, 240);--phone-color-border: rgba(200, 200, 200, .4);--phone-color-grey: #8e8e93;--phone-color-blue: #0a84ff;--phone-color-green: #32d74b;--phone-color-green-secondary: #092911;--phone-color-red: #ff3b30;--phone-color-orange: rgb(255, 157, 10);--phone-color-yellow: #cca250;--phone-color-pink: #ff3b30;--instagram-primary: #ffffff;--instagram-blue: rgb(0, 149, 246);--instagram-red: rgb(237, 73, 86);--instagram-primary-text: rgb(38, 38, 38);--instagram-secondary-text: rgb(142, 142, 142);--instagram-stroke: rgb(219, 219, 219);--instagram-border: rgb(219, 219, 219);--instagram-highlight: rgb(239, 239, 239);--tinder-color-pink: #ff4573;--tinder-color-orange: #ff5f65;--tinder-color-mix: #f5547c;--twitter-primary: #f5f8fa;--twitter-secondary: #14171a;--twitter-background-highlight: rgb(239, 243, 244);--twitter-primary-text: #14171a;--twitter-secondary-text: #657786;--twitter-alt-text: #657786;--twitter-border: #bdc5cd75;--twitter-border-secondary: #1d9bf0;--twitter-highlight: #1d9bf0;--twitter-hover: rgba(15, 20, 25, .1);--twitter-action: #14171a;--twitter-blue: #1d9bf0;--tiktok-primary: #ffffff;--tiktok-secondary: #000000;--tiktok-text-primary: #000000;--tiktok-text-secondary: #86878b;--tiktok-color-border: #d0d1d3;--tiktok-color-pink: #fe2c55;--tiktok-color-aqua: #00f2ea;--tiktok-color-yellow: #f8cd14;--tiktok-color-blue: #479fc5;--tiktok-color-unread: rgba(254, 44, 86, .2);--crypto-color-primary: rgb(255, 255, 255);--browser-primary: rgb(245, 245, 245);--browser-secondary: rgba(153, 153, 153, .15);--browser-gradient: linear-gradient(230deg, #f4d6ff, #c5f1ff);--browser-footer: rgba(255, 255, 255, .75);--browser-border: rgba(102, 102, 102, .75);--browser-text-secondary: #696969;--controlcentre-opacity: rgba(255, 255, 255, .15);--controlcentre-opacity2: rgba(255, 255, 255, .2);--controlcentre-active: rgba(255, 255, 255, .5);--notification-primary: rgba(215, 215, 215, .5);--notification-secondary: rgba(215, 215, 215, .1);--lockscreeneditor-background: rgba(255, 255, 255, .75);--lockscreeneditor-secondary: #d9d9d9;--app-bg: #ececec;--app-bg2: #ffffff;--app-secondary: #ffffff;--app-secondary2: #ececec;--app-highlight: #cccccc;--app-highlight2: #999999;--app-highlight3: #ffffff;--app-border: #666666;--app-slider: #cccccc;--app-slider-active: #333333;--app-button: #ffffff;--components-bg: #eeeeee;--components-secondary: #ffffff;--components-highlight: #cccccc}[data-theme=dark]{--phone-color-primary: #000000;--phone-color-opacity: rgb(30, 30, 30, .5);--phone-color-opacity2: rgba(242, 242, 242, .4);--phone-color-highlight: rgb(15, 15, 15);--phone-color-highlight2: rgb(20, 20, 20);--phone-color-highlight3: rgb(25, 25, 25);--phone-highlight-opacity35: rgba(145, 145, 145, .35);--phone-highlight-opacity45: rgba(50, 50, 50, .6);--phone-highlight-opacity55: rgb(60, 60, 60, .8);--phone-color-input: rgba(60, 60, 67, .6);--phone-text-primary: #f2f2f7;--phone-text-secondary: #6f6f6f;--phone-color-grey: #636366;--phone-color-hover: rgb(30, 30, 30);--phone-color-border: rgba(150, 150, 150, .2);--phone-color-blue: #076bcf;--instagram-primary: #000000;--instagram-blue: rgb(0, 149, 246);--instagram-red: rgb(237, 73, 86);--instagram-primary-text: rgb(250, 250, 250);--instagram-secondary-text: rgb(142, 142, 142);--instagram-stroke: rgb(219, 219, 219);--instagram-border: rgb(54, 54, 54);--instagram-highlight: rgb(38, 38, 38);--twitter-primary: #000000;--twitter-secondary: #f5f8fa;--twitter-background-highlight: rgb(20, 20, 20);--twitter-primary-text: #f5f8fa;--twitter-secondary-text: #aab8c2;--twitter-alt-text: #657786;--twitter-border: #38444d;--twitter-border-secondary: #38444d;--twitter-hover: rgba(150, 150, 150, .1);--twitter-highlight: #dcdcdc;--twitter-action: #1d9bf0;--twitter-blue: #1d9bf0;--crypto-color-primary: rgb(24, 26, 32);--tiktok-text-primary: #f2f2f7;--tiktok-text-secondary: #6f6f6f;--tiktok-color-border: #96969633;--browser-primary: rgb(15, 15, 15);--browser-secondary: rgba(153, 153, 153, .15);--browser-gradient: linear-gradient(230deg, #453b48, #2f393d);--browser-footer: rgba(51, 51, 51, .75);--browser-border: rgba(102, 102, 102, .75);--browser-text-secondary: #999999;--controlcentre-opacity: rgba(0, 0, 0, .15);--controlcentre-opacity2: rgba(0, 0, 0, .2);--controlcentre-active: rgba(0, 0, 0, .5);--notification-primary: rgba(0, 0, 0, .1);--notification-secondary: rgba(0, 0, 0, .12);--lockscreeneditor-background: rgba(0, 0, 0, .8);--lockscreeneditor-secondary: #333333;--app-bg: #000000;--app-bg2: #000000;--app-secondary: #141414;--app-secondary2: #141414;--app-highlight: #cccccc;--app-highlight2: #696969;--app-highlight3: #212121;--app-border: #cccccc;--app-slider: #999999;--app-slider-active: #ffffff;--app-button: #333333;--components-bg: #000000;--components-secondary: #141414;--components-highlight: #696969}@keyframes zoomIn{0%{transform:scale(.5)}to{transform:scale(1)}}@keyframes zoomOut{0%{transform:scale(1.5)}to{transform:scale(1)}}@keyframes slideDown{0%{transform:translateY(-20%)}to{transform:translateY(0)}}@keyframes slideUp{0%{transform:translateY(40%)}to{transform:translateY(0)}}@keyframes slideRight{0%{transform:translate(-10%)}to{transform:translate(0)}}@keyframes slideLeft{0%{transform:translate(10%)}to{transform:translate(0)}}@keyframes appJiggle{0%{transform:rotate(-1deg);animation-timing-function:ease-in}50%{transform:rotate(1.5deg);animation-timing-function:ease-out}}@keyframes appJiggle2{0%{transform:rotate(1deg);animation-timing-function:ease-in}50%{transform:rotate(-1.5deg);animation-timing-function:ease-out}}@keyframes widgetJiggle{0%{transform:rotate(-.5deg);animation-timing-function:ease-in}50%{transform:rotate(.5deg);animation-timing-function:ease-out}}@keyframes widgetJiggle2{0%{transform:rotate(.5deg);animation-timing-function:ease-in}50%{transform:rotate(-.5deg);animation-timing-function:ease-out}}.settings-container{position:relative;display:flex;flex-direction:column;align-items:center;gap:1rem;height:100%;background-color:var(--app-bg);transition:background-color .2s ease-in-out}.settings-container .animation-container{width:100%;height:100%;display:flex;flex-direction:column;align-items:center;gap:1rem;margin-top:4rem;overflow-y:auto}.settings-container .animation-container::-webkit-scrollbar{display:none}.settings-container .animation-container.display div,.settings-container .animation-container.display section{transition:background-color .2s ease-in-out,color .2s ease-in-out}.settings-container .title{text-align:center;font-size:20px;font-weight:500;font-style:bold;white-space:nowrap;color:var(--phone-text-primary)}.settings-container .title.default{font-size:28px;font-weight:600;text-align:left;width:88%}.settings-container .top{width:90%;box-sizing:border-box;display:flex;flex-direction:row;justify-content:space-between;align-items:center}.settings-container .top div{flex:1}.settings-container .top .back{display:flex;flex-direction:row;align-items:center;cursor:pointer;color:var(--phone-color-blue);font-size:16px;gap:.4rem}.settings-container .top .back svg{font-size:22px;margin-right:-.5rem}.settings-container .top .title{margin:0;color:var(--phone-text-primary)}.settings-container .top .value{color:var(--phone-color-blue);font-size:17px;text-align:right;cursor:pointer}.settings-container .searchbox{width:83%;background-color:var(--app-secondary);padding:.5rem 1rem;border-radius:10px}.settings-container .profile{margin-bottom:1rem;background-color:var(--app-secondary);width:85%;display:flex;flex-direction:row;align-items:center;padding:.75rem;border-radius:14px;gap:.75rem;cursor:pointer;transition:all .2s ease-in-out}.settings-container .profile:hover{background-color:var(--phone-color-highlight3)}.settings-container .profile .avatar{display:flex;width:3.25rem;height:3.25rem;border-radius:50%;justify-content:center;align-items:center;font-size:22px;font-weight:400;background-position:center;background-size:cover;background-repeat:no-repeat;color:#fff;box-shadow:inset 0 4px 15px #00000040}.settings-container .profile .avatar:not([data-hasavatar=true]):not(:has(img)){background:linear-gradient(180deg,#e3e3e3,#999999)}.settings-container .profile .profile-info{display:flex;flex-direction:column;color:var(--phone-text-primary);font-weight:400}.settings-container .profile .profile-info .name{font-size:20px}.settings-container .profile .profile-info .info{color:var(--phone-text-secondary);font-size:13px}.settings-container .profile svg{margin-left:auto;margin-right:.5rem;font-size:25px;color:var(--phone-text-secondary);opacity:.5}.settings-container .category-title{width:85%;font-size:13px;color:#88888c;text-transform:uppercase;text-align:left;margin-bottom:-.5rem}.settings-container section{margin-bottom:1rem;display:flex;flex-direction:column;justify-content:center;background-color:var(--app-secondary);width:90%;border-radius:14px;box-sizing:border-box}.settings-container section[data-last=true]{margin-bottom:3rem}.settings-container section .settings-item{display:flex;flex-direction:row;justify-content:space-between;align-items:center;padding:.75rem 1rem;border-width:1rem;border-bottom:1px solid var(--phone-color-border);position:relative}.settings-container section .settings-item[data-disabled=true]{cursor:not-allowed;opacity:.8}.settings-container section .settings-item[data-clickable=true]{cursor:pointer;transition:all .2s ease-in-out}.settings-container section .settings-item[data-clickable=true]:first-child{border-top-left-radius:10px;border-top-right-radius:10px}.settings-container section .settings-item[data-clickable=true]:last-child{border-bottom-left-radius:10px;border-bottom-right-radius:10px}.settings-container section .settings-item[data-clickable=true]:hover{background-color:var(--phone-color-highlight3)}.settings-container section .settings-item:last-child{border:none}.settings-container section .settings-item input{font-size:19px;text-align:right;border:none;background:transparent;outline:none;color:var(--phone-text-secondary)}.settings-container section .settings-item input:focus{outline:none}.settings-container section .settings-item input::placeholder{color:var(--phone-text-secondary)}.settings-container section .settings-item .item-header{display:flex;gap:.65rem}.settings-container section .settings-item .item-header img{width:3.75rem;aspect-ratio:1/1;border-radius:8px}.settings-container section .settings-item .item-header .info{display:flex;flex-direction:column}.settings-container section .settings-item .item-header .info .title{font-size:18px;font-weight:500;color:var(--phone-text-primary)}.settings-container section .settings-item .item-header .info .author{font-size:16px;color:var(--phone-text-secondary)}.settings-container section .settings-item .item-content{font-size:17px;color:var(--phone-text-secondary)}.settings-container section .settings-item .colors{display:flex;align-items:center;width:100%;gap:.75rem;box-sizing:border-box;padding:0 1rem}.settings-container section .settings-item .colors .color{width:2rem;height:2rem;aspect-ratio:1/1;border-radius:50%;cursor:pointer;transition:all .2s ease-in-out;border:3px solid transparent;background-position:center;background-size:cover;background-repeat:no-repeat}.settings-container section .settings-item .colors .color[data-active=true]{border:3px solid var(--phone-color-blue)}.settings-container section .settings-item .colors .color:hover{filter:brightness(.7)}.settings-container section .settings-item .wallpapers{display:flex;align-items:center;justify-content:center;gap:1rem;position:relative;width:100%;margin:0 1rem}.settings-container section .settings-item .wallpapers .preview{width:50%;border-radius:15px;height:18rem;background-position:center;background-size:cover;background-repeat:no-repeat}.settings-container section .settings-item .wallpapers .preview .lockscreen-container{width:100%;display:flex;flex-direction:column;align-items:center;margin-top:1rem;color:#fff}.settings-container section .settings-item .wallpapers .preview .lockscreen-container .time-container{display:flex;flex-direction:column;gap:.2rem;padding:.5rem 1rem;box-sizing:border-box;border:4px solid transparent;border-radius:15px;transition:all .2s ease-in-out}.settings-container section .settings-item .wallpapers .preview .lockscreen-container .time-container[data-layout="1"]{align-items:center}.settings-container section .settings-item .wallpapers .preview .lockscreen-container .time-container[data-layout="2"]{align-items:center}.settings-container section .settings-item .wallpapers .preview .lockscreen-container .time-container[data-layout="2"] .time{display:flex;flex-direction:column;align-items:center}.settings-container section .settings-item .wallpapers .preview .lockscreen-container .time-container[data-layout="3"]{align-items:flex-start;justify-content:flex-start;margin-right:auto}.settings-container section .settings-item .wallpapers .preview .lockscreen-container .time-container[data-layout="4"]{align-items:flex-end;margin-left:auto}.settings-container section .settings-item .wallpapers .preview .lockscreen-container .time-container[data-editmode=true]{border-color:#ffffff4d}.settings-container section .settings-item .wallpapers .preview .lockscreen-container .time-container>div{text-align:center}.settings-container section .settings-item .wallpapers .preview .lockscreen-container .time-container .time{font-size:35px;letter-spacing:-2px;line-height:2rem;text-shadow:0 0 4px rgba(0,0,0,.3)}.settings-container section .settings-item .wallpapers .preview .lockscreen-container .time-container .date{font-size:8px;font-weight:400;line-height:.5rem;text-shadow:1px 2px 5px rgba(0,0,0,.3);font-family:Nunito-Medium,sans-serif}.settings-container section .settings-item .wallpapers .preview .lockscreen-container svg{color:#fff;font-size:10px;margin-bottom:.4rem}.settings-container section .settings-item .wallpapers .preview .settings-homescreen-container{height:100%;width:100%;display:flex;flex-direction:column;align-items:center;position:relative}.settings-container section .settings-item .wallpapers .preview .settings-homescreen-container .settings-homescreen-apps{display:grid;grid-template-columns:repeat(4,1fr);grid-template-rows:repeat(4,1fr);gap:.5rem 0;margin-top:1.5rem;width:100%;height:auto;box-sizing:border-box}.settings-container section .settings-item .wallpapers .preview .settings-homescreen-container .upper{display:flex;width:92%;position:relative;top:.25rem;margin-top:-.1rem;padding-bottom:.1rem}.settings-container section .settings-item .wallpapers .preview .settings-homescreen-container .upper .header{font-size:8px;display:flex;align-items:center;width:100%;padding:0}.settings-container section .settings-item .wallpapers .preview .settings-homescreen-container .upper .header .right{margin-right:.75rem}.settings-container section .settings-item .wallpapers .preview .settings-homescreen-container .upper .header .right svg{margin:0;font-size:12px}.settings-container section .settings-item .wallpapers .preview .settings-homescreen-container .favourite-apps{position:absolute;bottom:.5rem;display:flex;flex-direction:row;justify-content:center;gap:.5rem;border-radius:10px;padding:.4rem;background-color:#f2f2f266;width:80%}.settings-container section .settings-item .wallpapers .preview .settings-homescreen-container .settings-homescreen-app{display:flex;flex-direction:column;align-items:center;gap:.05rem}.settings-container section .settings-item .wallpapers .preview .settings-homescreen-container .settings-homescreen-app img{height:1.3rem;width:1.3rem;border-radius:5px;user-drag:none}.settings-container section .settings-item .wallpapers .preview .settings-homescreen-container .settings-homescreen-app .app-name{font-size:4px;font-weight:200;color:#fff;opacity:.6}.settings-container section .settings-item .setting{display:flex;flex-direction:row;align-items:center;gap:.5rem;justify-content:left}.settings-container section .settings-item .setting .setting-info{display:flex;align-items:flex-start;flex-direction:column;gap:.25rem}.settings-container section .settings-item .setting .setting-info .description{font-size:13px;font-weight:400;color:var(--phone-text-secondary)}.settings-container section .settings-item .setting .title{font-size:18px;font-weight:300;color:var(--phone-text-primary);margin:0}.settings-container section .settings-item .setting .title.blue{color:var(--phone-color-blue);background:none;font-weight:400}.settings-container section .settings-item .setting .title.red{color:var(--phone-color-red);background:none;font-weight:400}.settings-container section .settings-item .setting .title.grey{color:var(--phone-text-secondary);background:none;font-weight:400}.settings-container section .settings-item .setting img{width:2rem;height:2rem;border-radius:7px}.settings-container section .settings-item .setting>svg:not(.checkmark,.remove){margin-left:-.1rem;margin-right:.25rem;color:#fff;font-size:30px;width:1.1rem;height:1.1rem;display:flex;align-items:center;justify-content:center;padding:.5rem;border-radius:10px}.settings-container section .settings-item .setting .remove{padding:.2rem;font-size:30px;width:1.75rem;height:1.75rem;color:var(--phone-color-red);cursor:pointer;margin-left:0}.settings-container section .settings-item .setting .hidden{visibility:hidden}.settings-container section .settings-item .setting .orange{background-color:#ffa338}.settings-container section .settings-item .setting .blue{background-color:#348de9}.settings-container section .settings-item .setting .darkblue{background-color:#1a3fd4}.settings-container section .settings-item .setting .lightblue{background-color:#5aa7d7}.settings-container section .settings-item .setting .green{background-color:#57d26e}.settings-container section .settings-item .setting .red{background-color:#ff5252}.settings-container section .settings-item .setting .pink{background-color:#f5315e}.settings-container section .settings-item .setting .purple{background-color:#5958c0}.settings-container section .settings-item .setting .grey{background-color:#8e8e92}.settings-container section .settings-item .settings-notification{position:absolute;display:flex;align-items:center;justify-content:center;right:2.5rem;width:1.65rem;height:1.65rem;border-radius:50%;font-size:15px;color:#fff;background-color:var(--phone-color-red)}.settings-container section .settings-item .setting-value{display:flex;flex-direction:row;align-items:center;gap:.5rem;color:var(--phone-text-secondary)}.settings-container section .settings-item .setting-value .value{font-size:18px;font-weight:300}.settings-container section .settings-item .setting-value .value a{color:var(--phone-color-blue);text-decoration:none;cursor:pointer}.settings-container section .settings-item .setting-value>svg{font-size:22px;cursor:pointer}.settings-container section .settings-item>svg{margin-left:.5rem;margin-right:.5rem;font-size:28px;color:var(--phone-text-secondary)}.settings-container section .settings-item .checkmark{margin:0;font-size:30px;color:var(--phone-color-blue)}.settings-container section .settings-item .checkmark.hidden{visibility:hidden}.settings-container section .settings-item .chevron{font-size:20px;margin:0;margin-right:-.4rem;opacity:.5}.settings-container .up-to-date{display:flex;flex-direction:column;align-items:center;position:absolute;top:22rem;font-size:18px;font-weight:400;color:var(--phone-text-secondary)}.settings-container .up-to-date .title{font-size:inherit;font-weight:inherit;color:inherit}.settings-container .wallpapers-container{display:grid;grid-template-columns:repeat(3,1fr);gap:.25rem;margin-top:1rem;padding-bottom:5rem;width:100%;box-sizing:border-box}.settings-container .wallpapers-container .wallpaper{width:100%;height:18rem;cursor:pointer;transition:all .3s ease-in-out;background-position:center;background-size:cover;background-repeat:no-repeat;border-radius:4px;box-sizing:border-box}.settings-container .wallpapers-container .wallpaper.add{display:flex;align-items:center;justify-content:center;background-color:var(--phone-highlight-opacity55)}.settings-container .wallpapers-container .wallpaper.add svg{font-size:50px;color:var(--phone-text-primary);opacity:.8}.settings-container .wallpapers-container .wallpaper:hover:not(.add),.settings-container .wallpapers-container .wallpaper.active,.settings-container .wallpapers-container .wallpaper:focus{opacity:.75;border:3px solid var(--phone-color-blue);border-radius:10px}.settings-container .appearance-container{width:100%;display:flex;flex-direction:column}.settings-container .appearance-container .appearance-switches{display:flex;flex-direction:row;align-items:center;justify-content:center;gap:4rem}.settings-container .appearance-container .appearance-switches .mode{display:flex;flex-direction:column;align-items:center;justify-content:center;gap:.5rem}.settings-container .appearance-container .appearance-switches .mode img{width:5rem}.settings-container .appearance-container .appearance-switches .mode .mode-text{font-size:18px;font-weight:400;color:var(--phone-text-primary)}.settings-container .passcode-container{height:100%;width:100%;display:flex;flex-direction:column;align-items:center}.settings-container .passcode-container .pin{display:flex;flex-direction:column;align-items:center;width:80%;gap:1rem;margin-top:15rem;font-size:18px;color:var(--phone-text-primary)}.settings-container .passcode-container .numbers{position:absolute;bottom:0;width:90%;padding:1rem 2rem 2rem;border-radius:30px;background-color:var(--phone-color-highlight3)}.settings-container input{color:var(--phone-color-input)}.settings-container input ::placeholder{color:var(--phone-color-input)}
