if not Config.Missions.Enable then 
    return 
end

curMission = {}

---Opens the main menu for mechanic missions/jobs
---@param returnMenu boolean optional param. `true` returns to the mechanic action menu. `false` just closes menu
function MissionsMainMenu(returnMenu)
    if not Config.Missions.Enable then return end

    -- check if isMechanic:
    local isMechanic, shopId = IsPlayerMechanic()
    if not isMechanic then return end 

    local menuOptions = {}

    -- populate menuOptions:
    if curMission and next(curMission) then
        menuOptions[#menuOptions+1] = {
            title = locale("menu_title.missions_cancel"),
            icon = Config.Missions.CancelIcon,
            description = locale("menu_description.missions_cancel"),
            onSelect = function()
                curMission.cancel = true
            end
        }
    else
        if type(Config.Missions.Types) ~= "table" and next(Config.Missions.Types) == nil then return end
        for missionType, mission in pairs(Config.Missions.Types) do
            if mission.enable then
                menuOptions[#menuOptions+1] = {
                    title = locale("menu_title.missions_"..missionType),
                    icon = mission.icon,
                    description = locale("menu_description.missions_"..missionType),
                    onSelect = function()
                        local missionIndex = GetMissionLocation(missionType)
                        if not missionIndex then return end

                        -- set data:
                        curMission = {type = missionType, index = missionIndex}

                        -- sync inUse state:
                        TriggerServerEvent("t1ger_mechanic:server:missionInUse", missionType, missionIndex, true)

                        -- start the mission:
                        if missionType == "breakdown" then
                            Breakdown()
                        elseif missionType == "roadsiderepair" then
                            RoadsideRepair()
                        elseif missionType == "carscrapping" then
                            CarScrapping()
                        end
                    end
                }
            end
        end
    end

    -- check if menu options has data:
    if not menuOptions or #menuOptions == 0 then
        if returnMenu then
            return lib.showContext("mechanic_action_menu")
        end
        return
    end

    -- sort options by title:
    table.sort(menuOptions, function(a, b)
        return a.title < b.title
    end)
    
    -- basic context menu settings:
    local context = {
        id = "mechanic_missions_main",
        title = locale("menu_title.missions_main"),
        options = menuOptions
    }

    -- return menu if true:
    if returnMenu then
        context.menu = "mechanic_action_menu"
    end

    -- register and show context:
    lib.registerContext(context)
    lib.showContext("mechanic_missions_main")
end

---Returns an available location for a given mission type
---@param missionType string the type of mission: `breakdown`, `roadsiderepair` or `carscrapping`
---@return missionIndex number the index of the mission location for the given mission type
function GetMissionLocation(missionType)
    -- check if has ongoing mission:
    if curMission and next(curMission) then 
        return _API.ShowNotification(locale("notification.have_ongoing_mission"), "inform", {})
    end

    -- validate locations for mission type:
    if not Config.Missions.Locations[missionType] then 
        return error(string.format("[GetMissionLocation] the mission type '%s' does not exist in Config.Missions.Locations", missionType))
    end

    -- get random mission:
    math.randomseed(GetGameTimer())
    local missionIndex = math.random(1, #Config.Missions.Locations[missionType])

    ---Calculates travel distance between points and returns true/false on whether distance is greater than input distance
    ---@param pos vector4 coords to check travel distance for
    ---@param travelDistance number minimum required travel distance
    ---@return boolean ValidateTravelDistance `true` if travel distance is greater than input distance. `false` otherwise
    local function ValidateTravelDistance(pos, travelDistance)
        local distance = CalculateTravelDistanceBetweenPoints(coords.x, coords.y, coords.z, pos.x, pos.y, pos.z)
        if distance > travelDistance then
            return true
        else
            return false
        end
    end

    -- check travel distance:
    local travelDistance = ValidateTravelDistance(Config.Missions.Locations[missionType][missionIndex].pos, Config.Missions.Types[missionType].travelDistance)

    local count = 0
    -- find available location with valid travel distance:
	while not travelDistance and count < 100 do
		count = count + 1
		missionIndex = math.random(1, #Config.Missions.Locations[missionType])
		while Config.Missions.Locations[missionType].inUse and count < 100 do
			missionIndex = math.random(1, #Config.Missions.Locations[missionType])
		end
		travelDistance = ValidateTravelDistance(Config.Missions.Locations[missionType][missionIndex].pos, Config.Missions.Types[missionType].travelDistance)
	end

    -- check if found available mission location:
    if count == 100 then
        _API.ShowNotification(locale("notification.no_missions_available"), "inform", {})
        return
    end

    -- return mission index
    return missionIndex
end

--- Event to update inUse state of mission location for given mission type:
RegisterNetEvent("t1ger_mechanic:client:missionInUse", function(missionType, missionIndex, inUse)
    Config.Missions.Locations[missionType][missionIndex].inUse = inUse
end)

--- Command to open Missions Main Menu
if Config.Missions.Command.enable == true then
    RegisterCommand(Config.Missions.Command.name, function()
        MissionsMainMenu()
    end, false)
end

---Creates a mission blip for the specific mission at given coords
---@param missionCoords vector3 coords of the mission
---@param missionBlip table mission blip settings
---@return integer blip the created blip handle
function CreateMissionBlip(missionCoords, missionBlip)
    local blip = AddBlipForCoord(missionCoords.x, missionCoords.y, missionCoords.z)
    SetBlipSprite(blip, missionBlip.sprite)
    SetBlipColour(blip, missionBlip.color)
    SetBlipScale(blip, missionBlip.scale)
    SetBlipDisplay(blip, 4)
    -- blip text:
    AddTextEntry("MYBLIP", missionBlip.label)
    BeginTextCommandSetBlipName("MYBLIP")
    EndTextCommandSetBlipName(blip)
    
    if missionBlip.route.enable then
        SetBlipRoute(blip, true)
        SetBlipRouteColour(blip, missionBlip.route.color)
    end

    return blip
end

---Creates a mission blip for the specific mission at given coords
---@param missionCoords vector3 coords of the mission
---@param missionBlip table mission blip settings
---@param routeEnable boolean enable/disable route to blip
---@return integer blip the created blip handle
function CreateMissionEntityBlip(missionEntity, missionBlip, routeEnable)
    local blip = AddBlipForEntity(missionEntity)
    
    SetBlipColour(blip, missionBlip.color)
    SetBlipScale(blip, missionBlip.scale)
    SetBlipDisplay(blip, 4)

    -- blip text:
    AddTextEntry("MYBLIP", missionBlip.label)
    BeginTextCommandSetBlipName("MYBLIP")
    EndTextCommandSetBlipName(blip)

    -- route:
    if routeEnable then
        SetBlipRoute(blip, true)
        SetBlipRouteColour(blip, missionBlip.color)
    end

    return blip
end

---Creates a mission vehicle with randomized vehicle model at given mission coords
---@param missionCoords vector4 coords & heading to spawn the vehicle
---@return vehicle entity the vehicle entity handle
function CreateMissionVehicle(missionCoords)
    local missionVehicle = nil

    ClearAreaOfVehicles(missionCoords.x, missionCoords.y, missionCoords.z, 5.0, false, false, false, false, false)

    -- random vehicle
    math.randomseed(GetGameTimer())
    local index = math.random(#Config.Missions.RandomVehicles)
    local vehicleModel = Config.Missions.RandomVehicles[index]

    _API.SpawnVehicle(vehicleModel, vector3(missionCoords.x, missionCoords.y, missionCoords.z), missionCoords.w, false, function(spawnedVehicle)
        SetEntityCoordsNoOffset(spawnedVehicle, missionCoords.x, missionCoords.y, missionCoords.z)
        SetEntityHeading(spawnedVehicle, missionCoords.w)
        SetVehicleOnGroundProperly(spawnedVehicle)
        missionVehicle = spawnedVehicle
    end, true)

    while not DoesEntityExist(missionVehicle) do 
        Wait(10)
    end
    
    return missionVehicle
end

---Creates a mission ped with randomized ped models at given mission coords
---@param pedCoords vector4 coords and heading of the ped
---@param scenario string scenario for ped to play
---@return ped entity the ped entity handle
function CreateMissionPed(pedCoords, scenario)
    -- ground coords:
    local groundBool, groundZ = GetGroundZFor_3dCoord(pedCoords.x, pedCoords.y, pedCoords.z, false)

    -- set relationship:
    SetPedRelationshipGroupHash(player, GetHashKey("PLAYER"))
	AddRelationshipGroup("NPC")

    -- get random ped:
    math.randomseed(GetGameTimer())
    local index = math.random(#Config.Missions.RandomPeds)
    local pedModel = Config.Missions.RandomPeds[index]

    -- check if scrapyard:
    if pedCoords == Config.Missions.Types["carscrapping"].scrapyard.ped.coords then
        pedModel = Config.Missions.Types["carscrapping"].scrapyard.ped.model
    end

    -- request model:
    lib.requestModel(pedModel)

    -- create ped:
    local missionPed = CreatePed(7, GetHashKey(pedModel), pedCoords.x, pedCoords.y, groundZ, pedCoords.w, 0, true, true)

    -- networked:
    NetworkRegisterEntityAsNetworked(missionPed)
    SetNetworkIdCanMigrate(NetworkGetNetworkIdFromEntity(missionPed), true)
    SetNetworkIdExistsOnAllMachines(NetworkGetNetworkIdFromEntity(missionPed), true)
    
    -- settings:
    SetPedDropsWeaponsWhenDead(missionPed, false)
    SetEntityInvincible(missionPed, false)
    SetEntityVisible(missionPed, true)
	FreezeEntityPosition(missionPed, true)

    -- scenario:
	TaskStartScenarioInPlace(missionPed, scenario, 0, false)
    SetPedKeepTask(missionPed, true)
    TaskSetBlockingOfNonTemporaryEvents(missionPed, true)

    -- apply relationship
	SetPedRelationshipGroupHash(missionPed, GetHashKey("NPC"))	
	SetRelationshipBetweenGroups(0, GetHashKey("PLAYER"), GetHashKey("NPC"))
	SetRelationshipBetweenGroups(0, GetHashKey("NPC"), GetHashKey("PLAYER"))

    while not DoesEntityExist(missionPed) do 
        Wait(10)
    end

    -- return mission ped
    return missionPed
end

---Creates a mission ped with randomized ped models inside given mission vehicle
---@param missionVehicle entity the entity handle of the mission vehicle
---@return ped entity the ped entity handle
function CreateMissionPedInsideVehicle(missionVehicle)    
    -- set relationship:
    SetPedRelationshipGroupHash(player, GetHashKey("PLAYER"))
	AddRelationshipGroup("NPC")

    -- get random ped:
    math.randomseed(GetGameTimer())
    local index = math.random(#Config.Missions.RandomPeds)
    local pedModel = Config.Missions.RandomPeds[index]

    -- request model:
    lib.requestModel(pedModel)

    -- create ped:
    local missionPed = CreatePedInsideVehicle(missionVehicle, 1, GetHashKey(pedModel), -1, true, true)

    -- networked:
    NetworkRegisterEntityAsNetworked(missionPed)
    SetNetworkIdCanMigrate(NetworkGetNetworkIdFromEntity(missionPed), true)
    SetNetworkIdExistsOnAllMachines(NetworkGetNetworkIdFromEntity(missionPed), true)
    
    -- settings:
    SetPedDropsWeaponsWhenDead(missionPed, false)
    SetEntityInvincible(missionPed, false)
    SetEntityVisible(missionPed, true)
    SetPedKeepTask(missionPed, true)
    TaskSetBlockingOfNonTemporaryEvents(missionPed, true)

    -- apply relationship
	SetPedRelationshipGroupHash(missionPed, GetHashKey("NPC"))	
	SetRelationshipBetweenGroups(0, GetHashKey("PLAYER"), GetHashKey("NPC"))
	SetRelationshipBetweenGroups(0, GetHashKey("NPC"), GetHashKey("PLAYER"))

    while not DoesEntityExist(missionPed) do 
        Wait(10)
    end

    -- return mission ped
    return missionPed
end