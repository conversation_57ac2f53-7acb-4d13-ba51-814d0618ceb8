local lastVehicleSpeed = 0.0
local currentVehicleData = nil

--- Updates service part mileage and degrades associated core parts if overdue
--- @param vehicleType string The mechanic vehicle type ("gas" or "electric")
--- @param drivenMileage number The distance driven this tick
--- @param vehicleData table The vehicle's statebag data (must include service_parts and core_parts)
--- @param vehicle number The vehicle entity handle
local function DegradeServiceParts(vehicleType, drivenMileage, vehicleData, vehicle)
    for servicePart, mileage in pairs(vehicleData.service_parts or {}) do
        local sPart = Config.ServiceParts[servicePart]
        
        -- if service part compatible with vehicle type
        if sPart and (sPart.type == vehicleType or sPart.type == "shared") then

            -- Add driven mileage to service part
            local newMileage = math.round(mileage + drivenMileage, 2)
            vehicleData.service_parts[servicePart] = newMileage

            -- if associated wear enabled
            if Config.AssociatedWear.Enabled then
                
                -- if service part is overdue
                local serviceInterval = GetVehicleServiceInterval(vehicle, servicePart)
                if serviceInterval and vehicleData.service_parts[servicePart] > serviceInterval then
                    if type(sPart.associated) == "table" and next(sPart.associated) then
                        for _, corePart in pairs(sPart.associated) do
                            local cPart = Config.CoreParts[corePart]

                            -- is core part compatible with vehicle type
                            if cPart and (cPart.type == vehicleType or cPart.type == "shared") then

                                -- degrade core part health
                                if vehicleData.core_parts and vehicleData.core_parts[corePart] then
                                    local currentHealth = vehicleData.core_parts[corePart]
                                    local degradeAmount = currentHealth * (Config.AssociatedWear.Rate / 100)
                                    vehicleData.core_parts[corePart] = math.round(math.max(0.0, currentHealth - degradeAmount), 2)

                                    -- optional debug print
                                    if Config.Debug then
                                        print(("Core part '%s' degraded to %.2f due to overdue service part '%s'"):format(corePart, vehicleData.core_parts[corePart], servicePart))
                                    end
                                    
                                end

                            end

                        end
                    end
                end
            end
        end
    end
end

--- Attempts to trigger a random malfunction effect based on degraded core parts
--- @param vehicle number The vehicle entity
--- @param vehicleState table The vehicle's statebag
--- @param vehicleData table The synced vehicle data (must include core_parts)
--- @param vehicleType string `"gas"` or `"electric"`
--- @return boolean didTrigger Whether a malfunction was triggered this tick
local function TriggerRandomMalfunctionEffect(vehicle, vehicleState, vehicleData, vehicleType)
    local degradedParts = {}

    -- numerically insert degraded parts in degradedParts cache
    for partName, health in pairs(currentVehicleData.core_parts) do
        local partInfo = GetCorePartInfo(partName, health)
        if partInfo and (partInfo.type == vehicleType or partInfo.type == "shared") then
            if partInfo.isDegraded then
                table.insert(degradedParts, {
                    partName = partName,
                    partLabel = partInfo.label,
                    health = health,
                    condition = partInfo.condition
                })
            end
        end
    end

    if #degradedParts == 0 then return false end

    math.randomseed(GetGameTimer())
    local selected = degradedParts[math.random(1, #degradedParts)]

    if selected.condition.index == 4 then
        vehicleState:set("t1ger_mechanic:engineDisabled", true, true)

        if Config.MalfunctionEffects.Notify then
            _API.ShowNotification(string.format(locale("notification.part_has_failed"), selected.partLabel), "error", {duration = 5000})
        end

        return true
    else
        if not vehicleState["t1ger_mechanic:malfunctionActive"] then
            vehicleState:set("t1ger_mechanic:malfunctionActive", true, true)

            vehicleState:set("t1ger_mechanic:malfunctionEffects", {
                part = selected.partName,
                health = vehicleData.core_parts[selected.partName] or selected.health,
                condition = selected.condition,
                tick = GetGameTimer()
            }, true)

            return true
        end
    end

    return false
end

--- Tracks vehicle mileage, service part wear, and core part degradation while driving
--- Initializes vehicle data if not already loaded
--- Disables engine if any core parts have failed
local function IsDrivingVehicle()
    if not cache.vehicle then return end
    local vehicle = cache.vehicle

    -- Get vehicle state:
    local vehicleState = Entity(vehicle).state

    -- load vehicle data
    if not vehicleState["t1ger_mechanic:vehicleData"] then
        local attempts = 10
        local data = nil

        while attempts > 0 do
            local plate = GetVehicleNumberPlateText(vehicle)
            data = lib.callback.await("t1ger_mechanic:server:loadVehicleData", false, VehToNet(vehicle))

            -- If we got valid data, break out immediately
            if data then
                break
            end

            -- If vehicle no longer exists, stop trying
            if not DoesEntityExist(vehicle) then
                return print("Vehicle no longer exists, stopping load attempt.")
            end

            -- Retry after small wait
            Wait(500) -- half a second between retries
            attempts -= 1
        end

        if not data then
            return print("Failed to load vehicle data after retries")
        end

        vehicleState:set("t1ger_mechanic:vehicleData", data, true)
    end

    -- Get vehicle type (gas / electric)
    local vehicleType = GetMechanicVehicleType(vehicle)

    -- Whether we should track degradation and parts on the given vehicle
    local isTrackable = IsTrackableVehicle(vehicle)

    -- Check if has failured parts
    if DoesVehicleHaveFailuredParts(vehicle) then
        vehicleState:set("t1ger_mechanic:engineDisabled", true, true)
    end

    -- Tracking setup
    local lastCoords = GetEntityCoords(vehicle)
    local unsyncedDistance = 0.0
    local effectInterval = 0

	while cache.seat == -1 do
		if not DoesEntityExist(vehicle) then return end

        -- Get vehicle data every tick
        currentVehicleData = vehicleState["t1ger_mechanic:vehicleData"]

        -- Get current speed & cache lastVehicleSpeed
        local speed = GetEntitySpeed(vehicle)
        lastVehicleSpeed = Config.Mileage.Metric and (speed * 3.6) or (speed * 2.236936)

        -- Get distance moved and cache last coords
        local currentCoords = GetEntityCoords(vehicle)
        local distance = #(currentCoords - lastCoords)
        lastCoords = currentCoords
        
        if distance > 1.0 then -- Ignore micro movement
            -- Convert to km or miles
            local distanceKm = distance * 0.001
            local drivenMileage = Config.Mileage.Metric and distanceKm or (distanceKm * 0.621371)

            -- Add to mileage
            currentVehicleData.mileage = math.round(((currentVehicleData.mileage or 0) + drivenMileage), 2)
            
            -- Add to unsynced distance
            unsyncedDistance += drivenMileage

            -- Degrade service parts
            if isTrackable then
                DegradeServiceParts(vehicleType, drivenMileage, currentVehicleData, vehicle)
            end

            -- Update statebag value
            if unsyncedDistance >= 0.25 then
                vehicleState:set("t1ger_mechanic:vehicleData", currentVehicleData, true)
                unsyncedDistance = 0.0
            else
                vehicleState:set("t1ger_mechanic:vehicleData", currentVehicleData, false)
            end

            -- Optional debug print
            if Config.Debug then
                print(("Mileage: %.2f %s"):format(currentVehicleData.mileage, Config.Mileage.Unit))
            end

        end

        -- Malfunctions effects?
        if currentVehicleData and Config.MalfunctionEffects and Config.MalfunctionEffects.Enabled and isTrackable then
            effectInterval = effectInterval + 1

            -- Reset malfunction interval if malfunction effects are playing
            if vehicleState["t1ger_mechanic:malfunctionActive"] then
                effectInterval = 0
            end

            -- Interval tick reached and not engine disabled
            if effectInterval >= Config.MalfunctionEffects.Interval and not vehicleState["t1ger_mechanic:engineDisabled"] then
                TriggerRandomMalfunctionEffect(vehicle, vehicleState, currentVehicleData, vehicleType)
                
                -- Always reset interval after attempt (no matter success/fail)
                effectInterval = 0
            end
        end

        if currentVehicleData and vehicleState then
            local hasFailures = DoesVehicleHaveFailuredParts(vehicle)
            local isMalfunctionActive = vehicleState["t1ger_mechanic:malfunctionActive"]
        
            if not hasFailures and not isMalfunctionActive and vehicleState["t1ger_mechanic:engineDisabled"] then
                vehicleState:set("t1ger_mechanic:engineDisabled", false, true)
        
                if Config.Debug then
                    print("[t1ger_mechanic] Engine re-enabled — no failed parts or active malfunctions (via IsDrivingVehicle).")
                end
            end
        end

		Wait(1000)
    end

    -- if not in driver's seat, set replicated statebag value:
    if currentVehicleData then
        vehicleState:set("t1ger_mechanic:vehicleData", currentVehicleData, true)

        if Config.Debug then
            print(("Final mileage sync on exit: %.2f %s"):format(currentVehicleData.mileage, Config.Mileage.Unit))
            print(json.encode(currentVehicleData, {indent = true}))
        end

        currentVehicleData = nil
    end
end

--- Handles forced engine disablement and driveability while the player is in the driver's seat
--- @return void
local function VehicleEngineHandle()
    if not cache.vehicle then return end
    local vehicle = cache.vehicle

    -- get vehicle statebag
    local vehicleState = Entity(vehicle).state

    -- is electric?
    local isElectric = IsVehicleElectric(vehicle)
    
    -- while in driver's seat
    while cache.seat == -1 do
        if not DoesEntityExist(vehicle) then return end

        -- if engine is disabled
        if vehicleState["t1ger_mechanic:engineDisabled"] then
            -- if engine is starting or running, then turn off
            if IsVehicleEngineStarting(vehicle) or GetIsVehicleEngineRunning(vehicle) then
                Wait(100)
                SetVehicleEngineOn(vehicle, false, false, true)
            end

            -- if electric, then set vehicle undriveable
            if isElectric then
                SetVehicleUndriveable(vehicle, true)
            end
        else
            Wait(800)
        end

        Wait(200)
    end
end

--- check if driver when script (re)starts:
if cache.seat == -1 then
    Wait(1000)

    -- create threads
    CreateThread(IsDrivingVehicle)
    CreateThread(VehicleEngineHandle)
end

--- is seats changed:
lib.onCache("seat", function(seat)
    -- if driver:
	if seat == -1 then
		SetTimeout(0, IsDrivingVehicle)
		SetTimeout(0, VehicleEngineHandle)
	end
end)

--- Applies degradation on random core parts on the vehicle on collisions
--- @param vehicle entity The vehicle entity handle
function ApplyCollisionDegradation(vehicle)
    if not vehicle or not DoesEntityExist(vehicle) then return end
    if not cache.vehicle or vehicle ~= cache.vehicle or cache.seat ~= -1 then return end

    local vehicleType = GetMechanicVehicleType(vehicle)
    local trackable = IsTrackableVehicle(vehicle)
    if not trackable then return end

    local vehicleState = Entity(vehicle).state

    if type(currentVehicleData) ~= "table" or next(currentVehicleData) == nil or not currentVehicleData.core_parts then
        return
    end

    local compatibleParts = {}
    for part, health in pairs(currentVehicleData.core_parts) do
        local partCfg = Config.CoreParts[part]
        if partCfg and (partCfg.type == vehicleType or partCfg.type == "shared") then
            table.insert(compatibleParts, part)
        end
    end

    if #compatibleParts == 0 then return end

    local degradeCount = math.min(Config.VehicleCollision.PartCount, #compatibleParts)

    for i = 1, degradeCount do
        local randIndex = math.random(1, #compatibleParts)
        local selectedPart = table.remove(compatibleParts, randIndex)
        local currentHealth = currentVehicleData.core_parts[selectedPart]
        local degradePercent = math.random(Config.VehicleCollision.MinPercent, Config.VehicleCollision.MaxPercent)
        local newHealth = math.max(0.0, currentHealth - (currentHealth * (degradePercent / 100)))

        -- set core part health:
        currentVehicleData.core_parts[selectedPart] = math.round(newHealth, 2)

        if Config.Debug then
            print(("[Collision] %s degraded by %d%% → New health: %.2f"):format(selectedPart, degradePercent, currentVehicleData.core_parts[selectedPart]))
        end
    end

    vehicleState:set("t1ger_mechanic:vehicleData", currentVehicleData, true)
end
exports("ApplyCollisionDegradation", ApplyCollisionDegradation)

if Config.VehicleCollision.Enable then
    local lastDamageTick = {} -- Table to store last damage timestamps

    -- game event listener
    AddEventHandler("gameEventTriggered", function(eventName, args)
        -- check for entity damage
        if eventName == "CEventNetworkEntityDamage" then
            local damagedVehicle = args[1]
            local entityHit = args[2]

            -- return if no entity hit or vehicle not matching
            if entityHit == -1 then return end
            if not cache.vehicle or damagedVehicle ~= cache.vehicle or cache.seat ~= -1 then return end

            -- return if not above required speed
            if lastVehicleSpeed < Config.VehicleCollision.MinImpactSpeed then return end

            -- get netId and current game timer
            local netId = VehToNet(damagedVehicle)
            local now = GetGameTimer()

            -- if not cooldown, proceed:
            if not lastDamageTick[netId] or now - lastDamageTick[netId] > Config.VehicleCollision.CooldownTime then
                lastDamageTick[netId] = now
                
                if Config.Debug then
                    print(("[Collision] Speed: %.2f %s"):format(lastVehicleSpeed, Config.Mileage.Unit))
                    print("[Collision] Degradation applied to vehicle Net ID:", netId)
                end

                -- Degradation logic
                ApplyCollisionDegradation(damagedVehicle)
            end
        end
    end)
end

-- Statebag Change Handler for engine disabled
AddStateBagChangeHandler("t1ger_mechanic:engineDisabled", --[[key filter]] nil --[[bag filter]], function(bagName, key, value, _unused, replicated)
    Wait(0)
    local vehicle = GetEntityFromStateBagName(bagName)
	if vehicle == 0 or not DoesEntityExist(vehicle) then return end
    if type(value) ~= "boolean" or replicated then return end

    -- check if electric vehicle
    local isElectric = IsVehicleElectric(vehicle)

    if value == true then
        -- Force the engine off and prevent auto-start
        SetVehicleEngineOn(vehicle, false, true, true)
        -- if electric set vehicle undriveable
        if isElectric then 
            SetVehicleUndriveable(vehicle, true)
        end
    elseif value == false then
        -- Optionally allow turning engine back on manually (based on config)
        SetVehicleEngineOn(vehicle, true, false, Config.DisableAutoEngineStart)
        -- if electric set vehicle driveable
        if isElectric then 
            SetVehicleUndriveable(vehicle, false)
        end
    end
end)

--- Plays malfunction effects on a specific core part based on its condition. This function is triggered when a degraded core part meets the malfunction interval.
--- @param vehicle entity The vehicle entity handle
--- @param part string The name of the core part triggering the effect (e.g., "brakes", "radiator")
--- @param health number The current health value of the core part (0.0 - 100.0)
--- @param condition table The condition stage table from Config.PartConditions.core_parts
local function PlayMalfunctionEffects(vehicle, part, health, condition)
    -- debug print
    if Config.Debug then
        print("[Malfunction] Part:", part, "Health:", health, "Condition:", condition.label)
    end

    -- get part config
    local partConfig = Config.CoreParts[part]

    -- notify if enabled
    if Config.MalfunctionEffects.Notify then
        _API.ShowNotification(string.format(locale("notification.part_malfunction"), partConfig.label, condition.label), "error", {duration = 5000})
    end

    -- Get vehicle state:
    local vehicleState = Entity(vehicle).state

    -- Alternator: cuts engine power temporarily
    if part == "alternator" then
        -- Worn: simulate stuttering (disable & re-enable engine a few times)
        if condition.index == 2 then
            local duration = 5000
            local cycleTime = 1000 -- off time + on time
            local cycleCount = math.floor(duration / cycleTime)

            for i = 1, cycleCount do
                SetVehicleEngineOn(vehicle, false, true, true)
                Wait(700)
                SetVehicleEngineOn(vehicle, true, true, Config.DisableAutoEngineStart)
                Wait(300)
            end
    
        -- Critical: full shutdown for the entire duration
        elseif condition.index == 3 then
            vehicleState:set("t1ger_mechanic:engineDisabled", true, true)
            Wait(10000)
            vehicleState:set("t1ger_mechanic:engineDisabled", false, true)
        end    

    -- Brakes: reduce braking power
    elseif part == "brakes" then
        local originalBrakeForce = GetVehicleHandlingFloat(vehicle, "CHandlingData", "fBrakeForce")
        
        local duration, force = 5000, 0.2
        if condition.index == 3 then 
            duration = 15000
            force = 0.1
        end

        SetVehicleHandlingFloat(vehicle, "CHandlingData", "fBrakeForce", force)
        Wait(duration)
        SetVehicleHandlingFloat(vehicle, "CHandlingData", "fBrakeForce", originalBrakeForce)

    -- Radiator: overheat with smoke and short engine disable
    elseif part == "radiator" then
        local currentTemp = GetVehicleEngineTemperature(vehicle)
        local currentEngineHealth = GetVehicleEngineHealth(vehicle)

        local duration = 5000
        if condition.index == 3 then 
            duration = 15000
        end

        SetVehicleEngineTemperature(vehicle, 200.0)
        SetVehicleEngineHealth(vehicle, 25.0)
        SetVehicleEngineOn(vehicle, false, true, true)
        Wait(1000)
        SetVehicleEngineOn(vehicle, true, false, Config.DisableAutoEngineStart)
        Wait(duration)
        SetVehicleEngineTemperature(vehicle, currentTemp)
        SetVehicleEngineHealth(vehicle, currentEngineHealth)

    -- Transmission: mess with acceleration and shifting
    elseif part == "transmission" then
        local rateScaleUpShift = GetVehicleHandlingFloat(vehicle, "CHandlingData", "fClutchChangeRateScaleUpShift")
        local rateScaleDownShift = GetVehicleHandlingFloat(vehicle, "CHandlingData", "fClutchChangeRateScaleDownShift")

        local duration = 5000
        if condition.index == 3 then 
            duration = 15000
        end

        SetVehicleHandlingFloat(vehicle, "CHandlingData", "fClutchChangeRateScaleUpShift", (rateScaleUpShift * 0.1))
        SetVehicleHandlingFloat(vehicle, "CHandlingData", "fClutchChangeRateScaleDownShift", (rateScaleDownShift * 0.1))
        
        local scale = GetVehicleSteeringScale(vehicle)
        SetVehicleHandbrake(vehicle, true)
        for i = 1, 200 do
            SetVehicleSteeringScale(vehicle, i)
            Citizen.Wait(1)
        end
        SetVehicleHandbrake(vehicle, false)
        SetVehicleSteeringScale(vehicle, scale)
        SetVehicleEnginePowerMultiplier(vehicle, -15.0)

        Wait(duration)

        SetVehicleEnginePowerMultiplier(vehicle, 0.0)
        SetVehicleHandlingFloat(vehicle, "CHandlingData", "fClutchChangeRateScaleUpShift", rateScaleUpShift)
        SetVehicleHandlingFloat(vehicle, "CHandlingData", "fClutchChangeRateScaleDownShift", rateScaleDownShift)

    -- Fuel Injector: irregular fuel delivery / sputtering or misfiring
    elseif part == "fuel_injector" then
        local currentFuel = _API.GetVehicleFuel(vehicle)

        local count = 5
        if condition.index == 3 then
            count = 15
        end

        for i = 1, count do
            SetVehicleEnginePowerMultiplier(vehicle, -10.0)
            _API.SetVehicleFuel(vehicle, 1.0)
            Wait(math.random(200, 400))
            _API.SetVehicleFuel(vehicle, currentFuel)
            Wait(math.random(200, 400))
        end
        
        SetVehicleEnginePowerMultiplier(vehicle, 0.0)
        _API.SetVehicleFuel(vehicle, currentFuel)

    -- Power Steering Pump: reduced steering lock
    elseif part == "power_steering_pump" then
        local fSteeringLock = GetVehicleHandlingFloat(vehicle, "CHandlingData", "fSteeringLock")

        local duration, force = 5000, 0.40
        if condition.index == 3 then 
            duration = 15000
            force = 0.25
        end

        SetVehicleHandlingFloat(vehicle, "CHandlingData", "fSteeringLock", fSteeringLock * force)
        Wait(duration)
        SetVehicleHandlingFloat(vehicle, "CHandlingData", "fSteeringLock", fSteeringLock)

    -- Electric Motor: stuttering and full shut down
    elseif part == "electric_motor" then
        -- Worn: simulate stuttering (disable & re-enable engine a few times)
        if condition.index == 2 then
            local duration = 5000
            local cycleTime = 1000 -- off time + on time
            local cycleCount = math.floor(duration / cycleTime)

            for i = 1, cycleCount do
                SetVehicleEngineOn(vehicle, false, true, true)
                Wait(700)
                SetVehicleEngineOn(vehicle, true, true, Config.DisableAutoEngineStart)
                Wait(300)
            end
    
        -- Critical: full shutdown for the entire duration
        elseif condition.index == 3 then
            local duration = 10000
            local cycleTime = 1200
            local cycleCount = math.floor(duration / cycleTime)

            vehicleState:set("t1ger_mechanic:engineDisabled", true, true)

            for i = 1, cycleCount do
                -- Flash dashboard lights
                SetVehicleLights(vehicle, 2)
                PlaySoundFromEntity(-1, "Click", vehicle, "DLC_HEIST_HACKING_SNAKE_SOUNDS", false, 0)
                Wait(400)
                SetVehicleLights(vehicle, 0)
                Wait(800)
            end

            vehicleState:set("t1ger_mechanic:engineDisabled", false, true)
        end

    -- EV Battery: low power 
    elseif part == "ev_battery" then
        local duration, force = 5000, -50.0
        if condition.index == 3 then 
            duration = 15000
            force = -90.0
        end

        SetVehicleEnginePowerMultiplier(vehicle, force)
        Wait(duration)
        SetVehicleEnginePowerMultiplier(vehicle, 0.0)
    end

    -- reset malfunction active state
    vehicleState:set("t1ger_mechanic:malfunctionActive", false, true)
end

-- Statebag Change Handler for malfunction effects
AddStateBagChangeHandler("t1ger_mechanic:malfunctionEffects", --[[key filter]] nil --[[bag filter]], function(bagName, key, value, _unused, replicated)
    Wait(0)
    local vehicle = GetEntityFromStateBagName(bagName)
	if vehicle == 0 or not DoesEntityExist(vehicle) then return end
    if value == nil or replicated then return end

    local currentVehicle = GetVehiclePedIsIn(PlayerPedId(), false)
    if currentVehicle == vehicle then
        PlayMalfunctionEffects(vehicle, value.part, value.health, value.condition)
    end
end)

--- Shows notification with current vehicle mileage
local function ShowCurrentVehicleMileage()
    local vehicle = GetVehiclePedIsIn(player, false)
    if not vehicle or not DoesEntityExist(vehicle) then return end
    _API.ShowNotification(string.format("Mileage: %s %s", math.groupdigits(GetVehicleMileage(vehicle)), Config.Mileage.Unit), "inform", {})
end

if Config.Mileage.Command.enable then
    RegisterCommand(Config.Mileage.Command.name, function(source, args, rawCommand)
        ShowCurrentVehicleMileage()
    end, false)
end