local GetVehicleExtrasOptions = function(vehicle, modName, modVariants)
    local options = {}
    
    for k,v in ipairs(modVariants) do 
        local isExtraTurnedOn = IsVehicleExtraTurnedOn(vehicle, v.modValue)
        local toggleValue = isExtraTurnedOn and 0 or 1

        local title, modLabel = {}, {}, {}, {}
        for i, extra in pairs(Config.Mods[v.modName].variants) do
            local num = (tonumber(i) + 1)
            local menuLabel = '['..num..'] '..extra.label

            if extra.index == toggleValue then
                menuLabel = '['..num..'] '..extra.label..' - '..Lang['title_mod_installed']
            end

            title[num] = menuLabel
            modLabel[num] = '['..num..'] '..extra.label
        end

        options[#options + 1] = {
            label = v.modLabel..': '..v.modValue,
            values = title,
            defaultIndex = isExtraTurnedOn and 2 or 1,
            args = {labels = modLabel, menuLabel = title, modName = v.modName, modType = v.modType, modValue = v.modValue},
        }

    end

    return options
end

local GetModRGBOption = function(modName, rgb)
    local args = {modName = modName, modType = Config.Mods[modName].modType, openDialogue = true, modValue = rgb}
    local values = {
        [1] = Lang['options_value_pick_color_tool'],
        [2] = Lang['title_install']
    }
    if rgb == nil then
        values[2] = Lang['options_value_pick_color_first']
    end

    if modName == 'color1' or modName == 'color2' then
        args.paintJob = true
    end

    return {
        label = Config.Mods[modName].label.. ' - RGB',
        values = values,
        args = args,
        description = Lang['options_desc_rgb_color'],
        defaultIndex = 1
    }
end

local GetModScrollableOption = function(modName, modVariants, props)
    local title, modLabel, modValue, defaultIndex = {}, {}, {}, 1
    for i = 1, #modVariants do
        local menuLabel = '['..i..'] '..modVariants[i].modLabel
        if modVariants[i].modName == 'xenonColor' then
            if (props[modVariants[i].modName] == tonumber(modVariants[i].modValue)) then
                if props['customXenon'] == nil or next(props['customXenon']) == nil then
                    menuLabel = '['..i..'] '..modVariants[i].modLabel..' - '..Lang['title_mod_installed']
                    defaultIndex = i
                end
            end
            modValue[i] = modVariants[i].modValue
        elseif modVariants[i].modName == 'neonColor' then
            if json.encode(props['neonColor']) == json.encode(Config.Mods[modName].variants[tostring(modVariants[i].modValue)].rgb) then
                menuLabel = '['..i..'] '..modVariants[i].modLabel..' - '..Lang['title_mod_installed']
                defaultIndex = i
            end
            modValue[i] = Config.Mods[modName].variants[tostring(modVariants[i].modValue)].rgb
        end

        title[i] = menuLabel
        modLabel[i] = '['..i..'] '..modVariants[i].modLabel
    end

    return {
        label = Config.Mods[modName].label,
        values = title,
        defaultIndex = defaultIndex,
        args = {labels = modLabel, menuLabel = title, modName = modName, modType = Config.Mods[modName].modType, modValue = modValue},
    }
end

local GetVehicleXenonColorOptions = function(vehicle, modName, props, modVariants, rgb)
    local options = {}

    -- xenonColor | pre-defined color options:
    options[#options + 1] = GetModScrollableOption(modName, modVariants, props)

    -- customXenon | RGB option:
    options[#options + 1] = GetModRGBOption('customXenon', rgb)

    return options
end

local GetVehicleNeonKitOptions = function(vehicle, modName, modVariants)
    local options = {}

    -- neonEnabled | checked/toggle options:
    for k,v in ipairs(modVariants) do
        local isNeonToggled = IsVehicleNeonLightEnabled(vehicle, tonumber(v.modValue))
        local toggleValue = isNeonToggled and 1 or 0

        options[#options + 1] = {
            label = '['..(#options + 1)..'] '..v.modLabel,
            checked = toggleValue == 1 and true or false,
            args = {modName = v.modName, modType = v.modType, modValue = v.modValue}
        }
    end

    -- neonEnabled | confirm neon settings
    local cfg = Config.Mods['neonEnabled']
    local neonkit = {IsVehicleNeonLightEnabled(vehicle, 0), IsVehicleNeonLightEnabled(vehicle, 1), IsVehicleNeonLightEnabled(vehicle, 2), IsVehicleNeonLightEnabled(vehicle, 3)}

    options[#options + 1] = {
        label = Lang['title_install']..' '..cfg.label,
        args = {modName = 'neonEnabled', modType = cfg.modType, modValue = neonkit}
    }

    return options
end

local GetVehicleNeonColorOptions = function(vehicle, modName, props, modVariants, rgb)
    local options = {}

    -- neonColor | pre-defined color options:
    options[#options + 1] = GetModScrollableOption(modName, modVariants, props)

    -- neonColor | RGB option:
    options[#options + 1] = GetModRGBOption(modName, rgb)

    return options
end

local GetVehicleRimOptions = function(vehicle, modName, modVariants)
    local options = {}

    local IsWheelTypeAllowed = function(vehicle, wheelType)
        local class = GetVehicleClass(vehicle)

        if class == 13 then -- disable wheels for cycles
            return false 
        end
        if class == 8 and wheelType ~= 6 or wheelType == 6 and class ~= 8 then -- bike wheels only for bikes
            return false
        end
        if class == 22 and wheelType ~= 10 then -- open wheel racing vehicles
            return false
        end

        return true
    end

    local currentWheelType = GetVehicleWheelType(vehicle)
    local currentWheel, currentWheel2, currentIndex, currentIndex2 = GetVehicleMod(vehicle, Config.Mods['modFrontWheels'].modType), GetVehicleMod(vehicle, Config.Mods['modBackWheels'].modType), 1, 1

    for i = 1, #modVariants do
        if IsWheelTypeAllowed(vehicle, modVariants[i].modValue) then 
            SetVehicleWheelType(vehicle, modVariants[i].modValue)

            local title, modLabel, modValue, title2 = {}, {}, {}, {}
            local modCount = GetNumVehicleMods(vehicle, modVariants[i].modType)

            for wheel = 0, modCount, 1 do
                local num = (wheel + 1)
                local index = tonumber(wheel - 1)
                modLabel[num] = GetVehicleModVariantLabel(vehicle, modName, index)
                modValue[num] = index
                title[num] = '['..num..'] '..modLabel[num]
                title2[num] = '['..num..'] '..modLabel[num]

                if currentWheel == index then
                    currentIndex = num
                    title[num] = '['..num..'] '..modLabel[num]..' - '..Lang['title_mod_installed']
                end
                if currentWheel2 ~= nil and currentWheel2 == index then 
                    currentIndex2 = num
                    title2[num] = '['..num..'] '..modLabel[num]..' - '..Lang['title_mod_installed']
                end
            end

            options[#options + 1] = {
                label = '['..i..'] '..modVariants[i].modLabel,
                values = title,
                defaultIndex = currentWheelType == modVariants[i].modValue and currentIndex or 1,
                args = {menuIndex = #options + 1, labels = modLabel, menuLabel = title, modName = modName, modType = Config.Mods[modName].modType, wheelType = modVariants[i].modValue, modValue = modValue}
            }

            if GetVehicleClass(vehicle) == 8 then
                local cfg = Config.Mods['modBackWheels']
                options[#options + 1] = {
                    label = '['..i..'] '..cfg.label,
                    values = title2,
                    defaultIndex = currentWheelType == modVariants[i].modValue and currentIndex2 or 1,
                    args = {menuIndex = #options + 1, labels = modLabel, menuLabel = title2, modName = 'modBackWheels', modType = cfg.modType, wheelType = modVariants[i].modValue, modValue = modValue, rear = true} 
                }
            end
        end
    end

    SetVehicleWheelType(vehicle, currentWheelType)

    return options
end

local GetVehicleTyreSmokeOptions = function(vehicle, modName, modVariants, rgb)
    local options = {}
    
    local isTyreSmokeEnabled = IsToggleModOn(vehicle, 20)
    if not isTyreSmokeEnabled then
        ToggleVehicleMod(vehicle, 20, true)
        isTyreSmokeEnabled = true
    end
    local currentTyreSmokeColor = {GetVehicleTyreSmokeColor(vehicle)}

    -- tyreSmokeColor | current color:
    options[#options + 1] = {
        label = Lang['options_label_cur_tyre_smoke']:format(math.floor(currentTyreSmokeColor[1]), math.floor(currentTyreSmokeColor[2]), math.floor(currentTyreSmokeColor[3])),
        args = {modName = modName, modValue = true, menuIndex = 1},
        description = Lang['options_desc_tyre_smoke_preview'],
    }

    -- tyreSmokeColor | predefined colors:
    local title, modLabel, modValue, defaultIndex = {}, {}, {}, 1
    for i = 1, #modVariants do
        modValue[i] = Config.Mods[modVariants[i].modName].variants[tostring(modVariants[i].modValue)].rgb
        modLabel[i] = '['..i..'] '..modVariants[i].modLabel

        title[i] = '['..i..'] '..modVariants[i].modLabel
        if json.encode(modValue[i]) == json.encode(currentTyreSmokeColor) then 
            defaultIndex = i
            title[i] = '['..i..'] '..modVariants[i].modLabel..' - '..Lang['title_mod_installed']
        end
    end
    options[#options + 1] = {
        label = Config.Mods[modName].label,
        values = title,
        defaultIndex = defaultIndex,
        args = {menuIndex = #options + 1, labels = modLabel, menuLabel = title, modName = modName, modType = modName, modValue = modValue}
    }

    -- tyreSmokeColor | RGB option:
    if isTyreSmokeEnabled then
        options[#options + 1] = GetModRGBOption('tyreSmokeColor', rgb)
    end

    return options
end

local GetVehicleColorOptions = function(vehicle, modName, modVariants, rgb)
    local options = {}
    
    local currentColor = modName == 'dashboardColor' and GetVehicleDashboardColor(vehicle) or modName == 'interiorColor' and GetVehicleInteriorColour(vehicle) or nil
    if modName == 'pearlescentColor' or modName == 'wheelColor' then 
        local curPealescent, curWheelColor = GetVehicleExtraColours(vehicle)
        currentColor = modName == 'pearlescentColor' and curPealescent or modName == 'wheelColor' and curWheelColor
    elseif modName == 'color1' or modName == 'color2' then 
        local primaryColor, secondaryColor = GetVehicleColours(vehicle)
        currentColor = modName == 'color1' and primaryColor or modName == 'color2' and secondaryColor
    end

    for k,v in ipairs(modVariants) do

        if (modName == 'dashboardColor' or modName == 'interiorColor') and v.modValue == 'chameleon' then 
            goto skipLoop
        end

        local title, modLabel, modValue, modPaintType, defaultIndex = {}, {}, {}, {}, 1
        for i = 1, #Config.Colors[v.modValue] do
            modValue[i] = Config.Colors[v.modValue][i].index
            modLabel[i] = '['..i..'] '..Config.Colors[v.modValue][i].label

            if modName == 'color1' or modName == 'color2' then 
                modPaintType[i] = v.modPaintType
            end

            title[i] = '['..i..'] '..Config.Colors[v.modValue][i].label
            local match = false
            if Config.Colors[v.modValue][i].index == currentColor then 
                defaultIndex = i
                title[i] = '['..i..'] '..Config.Colors[v.modValue][i].label..' - '..Lang['title_mod_installed']
            end
        end

        options[#options + 1] = {
            label = '['..k..'] '..v.modLabel,
            values = title,
            defaultIndex = defaultIndex,
            args = {menuIndex = #options + 1, labels = modLabel, menuLabel = title, modName = v.modName, modType = v.modType, colorCategory = v.modValue, modValue = modValue, paintType = next(modPaintType) and modPaintType or nil}
        }

        ::skipLoop::
    end

    -- color1 / color2 | RGB Color Option
    if modName == 'color1' or modName == 'color2' then 
        options[#options + 1] = GetModRGBOption(modName, rgb)
    end

    return options
end

local GetItemModVariantOptions = function(vehicle, props, modName, modType, rgb)
    local options = {}

    local modVariants = nil

    if modName == 'wheelColor' or modName == 'pearlescentColor' or modName == 'dashboardColor' or modName == 'interiorColor' or modName == 'color1' or modName == 'color2' then
        modVariants = GetVehicleColorVariants(vehicle, modName)
    else
        modVariants = GetVehicleModVariants(vehicle, modName, modType)
    end

    if modVariants ~= nil and next(modVariants) ~= nil then 

        if modName == 'extras' then
            return GetVehicleExtrasOptions(vehicle, modName, modVariants)
        elseif modName == 'xenonColor' or modName == 'customXenon' then 
            return GetVehicleXenonColorOptions(vehicle, modName, props, modVariants, rgb)
        elseif modName == 'neonEnabled' then
            return GetVehicleNeonKitOptions(vehicle, modName, modVariants)
        elseif modName == 'neonColor' then
            return GetVehicleNeonColorOptions(vehicle, modName, props, modVariants, rgb)
        elseif modName == 'modFrontWheels' or modName == 'modBackWheels' then 
            return GetVehicleRimOptions(vehicle, modName, modVariants)
        elseif modName == 'tyreSmokeColor' then 
            return GetVehicleTyreSmokeOptions(vehicle, modName, modVariants, rgb)
        elseif modName == 'dashboardColor' or modName == 'interiorColor' or modName == 'wheelColor' or modName == 'pearlescentColor' or modName == 'color1' or modName == 'color2' then
            return GetVehicleColorOptions(vehicle, modName, modVariants, rgb)
        end

        for k,v in ipairs(modVariants) do
            local menuLabel = '['..k..'] '..v.modLabel
            if (props[modName] == v.modValue) or (type(props[modName]) == 'boolean' and Lib.BooleanToNumber(props[modName]) == v.modValue) or json.encode(props[modName]) == json.encode(v.modValue) then
                menuLabel = '['..k..'] '..v.modLabel..' - '..Lang['title_mod_installed']
            end
    
            options[#options + 1] = {
                label = menuLabel,
                args = {labels = '['..k..'] '..v.modLabel, menuLabel = menuLabel, modName = v.modName, modType = v.modType, modValue = v.modValue},
            }
        end

    end

    return options
end

local IsItemModValid = function(vehicle, props, modName)
    if Config.Mods[modName] ~= nil then
        if DoesModExist(vehicle, modName, props) then
            local modLabel = GetVehicleModSlotName(vehicle, modName)
            for k,weaponMod in pairs(Config.DisableMods) do
                if modLabel == weaponMod then
                    return false
                end
            end
            return true
        else
            return false
        end
    else
        return false
    end
end

GetAllowedFromItem = function(itemName, vehicle, props)
    local options = {}
    for modName,v in pairs(Config.Mods) do
        if v.item ~= nil and v.item == itemName then
            if IsItemModValid(vehicle, props, modName) then
                options[#options + 1] = {
                    label = GetVehicleModSlotName(vehicle, modName),
                    args = {modName = modName, modType = v.modType, itemName = itemName}
                }
            end
        end
    end
    return options
end

RegisterNetEvent('tuningsystem:client:useableModItem', function(itemName, itemLabel)
    if Config.Debug then
        print("itemName: "..itemName.." | itemLabel: "..itemLabel)
    end

    local isTunerJob, shopId = IsTunerJob(Core.GetJob().name)

    if Config.T1GER_MechanicSystem and not isTunerJob then
        if Config.Debug then 
            print("not hired in tuner shop, so checking mechanic shops")
        end
        isTunerJob = exports['t1ger_mechanicsystem']:IsPlayerMechanic()
    end

    if not isTunerJob then
        return Core.Notification({
            title = '',
            message = Lang['need_tuner_job'],
            type = 'inform'
        }) 
    end

    if Config.Debug then
        print("playTunerId: "..playerTunerId.." | job name: "..Core.GetJob().name.." | shop id: "..shopId)
    end

    local vehicle, closestDist = GetVehiclePedIsIn(player, false), 0
    if vehicle == nil or vehicle == 0 then 
        vehicle, closestDist = Lib.GetClosestVehicle(coords, 5.0, false)
    end

    if vehicle == nil or not DoesEntityExist(vehicle) then 
        return Core.Notification({
            title = '',
            message = Lang['no_vehicle_nearby'],
            type = 'inform'
        })
    end

    itemMod.vehicle = vehicle
    itemMod.props = Core.GetVehicleProperties(itemMod.vehicle)

    local SetVehicleTiresMod = function(vehicle, itemName)
        if Config.T1GER_MechanicSystem then
            local isOnCarJack = exports['t1ger_mechanicsystem']:IsVehicleOnCarJack(NetworkGetNetworkIdFromEntity(vehicle))
            if not isOnCarJack then
                return Core.Notification({
                    title = '',
                    message = Lang['carjack_not_raised'],
                    type = 'inform'
                })
            end
        end

        local modName = itemName == 'mod_bullettires' and 'bulletProofTyres' or 'driftTyres'
        
        local installCallback = ModInstallAnimation(vehicle, {modName = modName})
        if installCallback == true then
            TriggerServerEvent('t1ger_lib:server:removeItem', itemName, 1)
    
            local props = {}
            if itemName == 'mod_stocktires' then -- stock tires:
                props = {['driftTyres'] = false, ['bulletProofTyres'] = false}
            elseif itemName == 'mod_drifttires' then -- drift tyres:
                props = {['driftTyres'] = true}
            elseif itemName == 'mod_bullettires' then -- bulletproof tires:
                props = {['bulletProofTyres'] = true}
            end
            Core.SetVehicleProperties(vehicle, props)
            PlayUpgradeSound()
            
            if Config.Debug then
                local isDriftEnabled = GetDriftTyresEnabled(vehicle)
                local isBulletProof = GetVehicleTyresCanBurst(vehicle)
                print("isDriftEnabled:", isDriftEnabled, "isBulletProof:", isBulletProof)
            end
        end
    end

    if itemName == 'mod_stocktires' or itemName == 'mod_drifttires' or itemName == 'mod_bullettires' then
        return SetVehicleTiresMod(itemMod.vehicle, itemName)
    end

    local allowedMods = GetAllowedFromItem(itemName, itemMod.vehicle, itemMod.props)

    if next(allowedMods) == nil then 
        return Core.Notification({
            title = '',
            message = 'No mods available',
            type = 'inform'
        })
    end

    SetVehicleUndriveable(itemMod.vehicle, true)

    if #allowedMods > 1 then
        lib.registerMenu({
            id = 'item_mod_'..itemName,
            title = itemLabel,
            position = 'top-left',
            onClose = function(keyPressed)
                SetVehicleUndriveable(itemMod.vehicle, false)
                Core.SetVehicleProperties(itemMod.vehicle, itemMod.props)
                itemMod = {}
            end,
            onSelected = function(selected, secondary, args)
                PlaySoundFrontend(-1, Config.MenuSoundFrontend.AudioName, Config.MenuSoundFrontend.AudioRef, true)
                itemMod.mainMenu = {id = 'item_mod_'..itemName, index = selected}
            end,
            options = allowedMods
        }, function(selected, scrollIndex, args)
            ShowItemModVariants(args)
        end)
    
        lib.showMenu('item_mod_'..itemName, 1)
    else
        ShowItemModVariants(allowedMods[1].args)
    end
end)

ShowItemModVariants = function(data)
    local modName, itemName = data.modName, data.itemName
    local cfg = Config.Mods[modName]

    menuOptions = GetItemModVariantOptions(itemMod.vehicle, itemMod.props, modName, cfg.modType, itemMod.rgb)

    if next(menuOptions) == nil then
        return Core.Notification({
            title = '',
            message = Lang['no_variants_for_mod'],
            type = 'inform'
        })
    end
    
    SetVehicleModKit(itemMod.vehicle, 0)

    browsingMods = true

    ModPreviewOpenDoors(menuOptions, itemMod.vehicle, modName)

    if modName == 'tyreSmokeColor' then
        if itemMod.props['modSmokeEnabled'] == false or itemMod.props['modSmokeEnabled'] == 0 then
            ToggleVehicleMod(itemMod.vehicle, 20, true)
            itemMod.props['modSmokeEnabled'] = true
        end
        SetVehicleUndriveable(itemMod.vehicle, false)
        SetVehicleBurnout(itemMod.vehicle, true)
    elseif modName == 'modXenon' or modName == 'xenonColor' or modName == 'customXenon' or modName == 'neonEnabled' or modName == 'neonColor' then 
        SetVehicleEngineOn(itemMod.vehicle, true, true, true)
    end

    lib.registerMenu({
        id = 'item_mod_'..modName,
        title = GetVehicleModSlotName(itemMod.vehicle, modName),
        position = 'top-left',
        onClose = function(keyPressed)
            SetVehicleDoorsShut(itemMod.vehicle, true)
            Core.SetVehicleProperties(itemMod.vehicle, itemMod.props)
            if itemMod.mainMenu ~= nil then
                local newOptions = GetAllowedFromItem(itemName, itemMod.vehicle, itemMod.props)
                lib.setMenuOptions(itemMod.mainMenu.id, newOptions)
                lib.showMenu(itemMod.mainMenu.id, itemMod.mainMenu.index)
            else
                -- reset vehicle & mods:
                SetVehicleBurnout(itemMod.vehicle, false)
                SetVehicleUndriveable(itemMod.vehicle, false)
                browsingMods = false
                itemMod = {}
            end
        end,
        onSelected = function(selected, secondary, args)
            PlaySoundFrontend(-1, Config.MenuSoundFrontend.AudioName, Config.MenuSoundFrontend.AudioRef, true)
            itemMod.lastMenu = {id = 'item_mod_'..modName, index = selected}

            if args.modName == 'xenonColor' or args.modName == 'customXenon' or args.modName == 'neonEnabled' or args.modName == 'neonColor' or args.modName == 'wheelColor' or args.modName == 'tyreSmokeColor' or args.modName == 'color1' or args.modName == 'color2' or args.modName == 'pearlescentColor' or args.modName == 'dashboardColor' or args.modName == 'interiorColor' or args.modName == 'extras' then
                return
            end

            local props = {[args.modName] = args.modValue}
            if args.modName == 'modFrontWheels' or args.modName == 'modBackWheels' then
                props = {['wheels'] = args.wheelType, [args.modName] = args.modValue[secondary]}
            end

            Core.SetVehicleProperties(itemMod.vehicle, props)

            if args.modName == 'modHorns' then 
                StartVehicleHorn(itemMod.vehicle, 2000, 'HELDDOWN', false)
            end
        end,
        onSideScroll = function(selected, scrollIndex, args)
            PlaySoundFrontend(-1, Config.MenuSoundFrontend.AudioName, Config.MenuSoundFrontend.AudioRef, true)
            if args.modName == 'xenonColor' or args.modName == 'wheelColor' or args.modName == 'modFrontWheels' or args.modName == 'modBackWheels' or args.modName == 'pearlescentColor' or args.modName == 'dashboardColor' or args.modName == 'interiorColor' or ((args.modName == 'color1' or args.modName == 'color2') and args.openDialogue == nil) then
                local props = {[args.modName] = args.modValue[scrollIndex]}
                if args.modName == 'color1' then 
                    props['paintType1'] = args.paintType[scrollIndex]
                    props['color2'] = itemMod.props['color2']
                elseif args.modName == 'color2' then
                    props['paintType2'] = args.paintType[scrollIndex]
                    props['color1'] = itemMod.props['color1']
                    props['paintType1'] = itemMod.props['paintType1']
                end
                Core.SetVehicleProperties(itemMod.vehicle, props)
            elseif args.modName == 'extras' then 
                local toggle = scrollIndex == 2 and 0 or scrollIndex == 1 and 1
                local props = {[args.modName] = {[tostring(args.modValue)] = toggle}}
                Core.SetVehicleProperties(itemMod.vehicle, props)
            elseif (args.modName == 'neonColor' or args.modName == 'tyreSmokeColor') and args.openDialogue == nil then
                local props = {[args.modName] = {math.floor(args.modValue[scrollIndex][1]), math.floor(args.modValue[scrollIndex][2]), math.floor(args.modValue[scrollIndex][3])}}
                Core.SetVehicleProperties(itemMod.vehicle, props)
            end
        end,
        onCheck = function(selected, checked, args)
            PlaySoundFrontend(-1, Config.MenuSoundFrontend.AudioName, Config.MenuSoundFrontend.AudioRef, true)
            if args.modName == 'neonEnabled' then
                local neons = {}
                for i = 0, 3 do
                    if i == args.modValue then
                        neons[tostring(i)] = checked
                    else
                        neons[tostring(i)] = IsVehicleNeonLightEnabled(itemMod.vehicle, i)
                    end
                end
                local props = {[args.modName] = neons}
                Core.SetVehicleProperties(itemMod.vehicle, props)
                itemMod.neons = neons
            end
        end,
        options = menuOptions
    }, function(selected, scrollIndex, args)
        if Config.Debug then
            print("install mod | ", selected, scrollIndex, args)
        end
        
        local newOptions, installMod = {}, true
        local alreadyInstalled = false

        if args.menuLabel ~= nil and type(args.menuLabel) == 'string' and string.match(args.menuLabel, Lang['title_mod_installed']) then 
            alreadyInstalled = true
            Core.Notification({
                title = '',
                message = Lang['mod_variant_already_installed'],
                type = 'inform'
            })
        end

        if args.modName == 'neonEnabled' then
            if itemMod.neons == nil or next(itemMod.neons) == nil then 
                alreadyInstalled = true
                Core.Notification({
                    title = '',
                    message = Lang['mod_variant_already_installed'],
                    type = 'inform'
                })
            else
                local match = true
                local curNeon, propNeon = itemMod.neons, itemMod.props[args.modName]
                for i = 0, 3 do
                    local neon_1 = type(propNeon[tostring(i)]) == 'boolean' and Lib.BooleanToNumber(propNeon[tostring(i)]) or propNeon[tostring(i)]
                    local neon_2 = type(curNeon[tostring(i)]) == 'boolean' and Lib.BooleanToNumber(curNeon[tostring(i)]) or curNeon[tostring(i)]
                    if neon_1 ~= neon_2 then 
                        match = false 
                        break
                    end
                end
                if match then 
                    alreadyInstalled = true
                    Core.Notification({
                        title = '',
                        message = Lang['mod_variant_already_installed'],
                        type = 'inform'
                    })
                end
            end
        end

        if alreadyInstalled then
            local newOptions = GetItemModVariantOptions(itemMod.vehicle, itemMod.props, modName, cfg.modType, itemMod.rgb)
            lib.setMenuOptions(itemMod.lastMenu.id, newOptions)
            lib.showMenu(itemMod.lastMenu.id, itemMod.lastMenu.index)
            return
        end

        -- RGB Color Picker
        if args.openDialogue ~= nil and args.openDialogue == true then 
            if scrollIndex == 1 then 
                local inputOptions = {
                    {type = 'color', label = Lang['input_label_rgb_color'], default = 'rgb(255, 255, 255)', format = 'rgb', required = true}
                }
    
                if args.paintJob ~= nil and args.paintJob == true then
                    inputOptions[#inputOptions + 1] = {type = 'select', label = 'Paint Job', required = true, default = 0, options = Config.PaintTypes}
                end
    
                local input = lib.inputDialog(Lang['input_title_rgb_color'], inputOptions)
    
                if input == nil then
                    Core.Notification({
                        title = '',
                        message = Lang['input_required'],
                        type = 'inform'
                    })
                else
                    local rgb = lib.math.tovector(input[1], 0, 255, true)
                    local props = {[args.modName] = {rgb.x, rgb.y, rgb.z}}
        
                    if args.modName == 'color1' then
                        props['paintType1'] = input[2] ~= nil and input[2] or 0
                        props['color2'] = itemMod.props['color2']
                    elseif args.modName == 'color2' then
                        props['paintType2'] = input[2] ~= nil and input[2] or 0
                        props['color1'] = itemMod.props['color1']
                        props['paintType1'] = itemMod.props['paintType1']
                    end
                    
                    itemMod.rgb = rgb
                    Core.SetVehicleProperties(itemMod.vehicle, props)
                end
    
                installMod = false
    
            elseif scrollIndex == 2 and (itemMod.rgb == nil or args.modValue == nil) then
                installMod = false
            end
        end

        if args.modName == 'tyreSmokeColor' then 
            if selected == 1 then
                installMod = false
            end
        end

        if installMod == true then
            -- save new vehicle props (updated mods)
            local vehicleProps = Core.GetVehicleProperties(itemMod.vehicle)
            -- update props on vehicle to old props:
            Core.SetVehicleProperties(itemMod.vehicle, itemMod.props)
            -- make player leave vehicle:
            if IsPedInAnyVehicle(player, true) then 
                TaskLeaveVehicle(player, vehicle, 0)
                Wait(800)
            end
            -- mod install callback:
            local installCallback = ModInstallAnimation(itemMod.vehicle, args, scrollIndex)
            if installCallback == true then
                -- remove item:
                TriggerServerEvent('t1ger_lib:server:removeItem', itemName, 1)
                -- update vehicle props to new changes:
                Core.SetVehicleProperties(itemMod.vehicle, vehicleProps)
                TriggerServerEvent('t1ger_lib:server:saveVehicleProperties', GetVehicleNumberPlateText(itemMod.vehicle), Core.GetVehicleProperties(itemMod.vehicle))
                local vehiclePlate = GetVehicleNumberPlateText(itemMod.vehicle)
                local isTunerJob, shopId = IsTunerJob(Core.GetJob().name)
                if Config.Shops[shopId] ~= nil and Config.Shops[shopId].orders ~= nil and Config.Shops[shopId].orders[vehiclePlate] ~= nil then
                    if Config.Shops[shopId].orders[vehiclePlate].taken ~= nil and Config.Shops[shopId].orders[vehiclePlate].taken == true then
                        modOrder.UpdateTask(vehiclePlate, args.modName, vehicleProps, args.modValue, scrollIndex)
                    end
                end
                -- Play Sound:
                PlayUpgradeSound()
                -- Reset:
                SetVehicleUndriveable(itemMod.vehicle, false)
                SetVehicleEngineOn(itemMod.vehicle, false, true, false)
                SetVehicleBurnout(itemMod.vehicle, false)
                SetVehicleDoorsShut(itemMod.vehicle, true)
                itemMod = {}
            else
                local newOptions = GetItemModVariantOptions(itemMod.vehicle, itemMod.props, modName, cfg.modType, itemMod.rgb)
                lib.setMenuOptions(itemMod.lastMenu.id, newOptions)
                lib.showMenu(itemMod.lastMenu.id, itemMod.lastMenu.index)
            end
        else
            local newOptions = GetItemModVariantOptions(itemMod.vehicle, itemMod.props, modName, cfg.modType, itemMod.rgb)
            lib.setMenuOptions(itemMod.lastMenu.id, newOptions)
            lib.showMenu(itemMod.lastMenu.id, itemMod.lastMenu.index)
        end
    end)

    local defaultIndex = 1
    for k,v in ipairs(menuOptions) do
        if v.args.modName == 'modFrontWheels' or v.args.modName == 'modBackWheels' then
            if itemMod.props['wheels'] == v.args.wheelType then
                if v.args.rear ~= nil then 
                    defaultIndex = 1
                else
                    defaultIndex = v.args.menuIndex or 1
                end
            end
        elseif v.args.modName == 'color1' or v.args.modName == 'color2' or v.args.modName == 'wheelColor' or v.args.modName == 'pearlescentColor' or v.args.modName == 'interiorColor' or v.args.modName == 'dashboardColor' then
            if Config.Colors[v.args.colorCategory] ~= nil then 
                for i = 1, #Config.Colors[v.args.colorCategory] do
                    if itemMod.props[v.args.modName] == Config.Colors[v.args.colorCategory][i].index then
                        defaultIndex = v.args.menuIndex
                        break
                    end
                end
            end
        else
            if itemMod.props[v.args.modName] == v.args.modValue or json.encode(itemMod.props[v.args.modName]) == json.encode(v.args.modValue) then 
                defaultIndex = k
            end
        end
    end

    lib.showMenu('item_mod_'..modName, defaultIndex)
end

CreateModObject = function(modName)
    local mod = Config.Mods[modName]
    local object, netId = nil, nil

    local pos = vector3(mod.prop.pos[1], mod.prop.pos[2], mod.prop.pos[3])
    local rot = vector3(mod.prop.rot[1], mod.prop.rot[2], mod.prop.rot[3])

    Lib.LoadModel(mod.prop.model)
    local object = CreateObjectNoOffset(GetHashKey(mod.prop.model), coords.x, coords.y, coords.z, true, false, false)
    while not DoesEntityExist(object) do 
        Wait(10)
    end
    local netId = ObjToNet(object)

    return object, netId, pos, rot
end

ModInstallAnimation = function(vehicle, args, scrollIndex)

    local modName, installDone, installingMod, installCancel = args.modName, false, true, false
    local anim = Config.Mods[modName].anim.idle
    if anim.dict ~= nil then
        Lib.LoadAnim(anim.dict)
        TaskPlayAnim(player, anim.dict, anim.name, anim.blendIn, anim.blendOut, anim.duration, anim.flags, 0, 0, 0, 0)
    end

    Wait(250)

    local modObject, netId, pos, rot = CreateModObject(modName)
    AttachEntityToEntity(modObject, player, GetPedBoneIndex(player, 28422), pos.x, pos.y, pos.z, rot.x, rot.y, rot.z, true, true, false, true, 2, true)

    local inAction = false

    local InstallVehicleMod = function(data, object, modName, idleAnim)
        local canInstall = GetVehicleModInstallPoint(data.entity, modName)
        if canInstall then
            local useSkillCheck = true
            local installAnim = Config.Mods[modName].anim.install
            if Config.Mods[modName].point ~= 'wheel' then
                TaskTurnPedToFaceEntity(player, data.entity, 1000)
            end
            Wait(1000)
            if installAnim.dict ~= nil then
                Lib.LoadAnim(installAnim.dict)
                TaskPlayAnim(player, installAnim.dict, installAnim.name, installAnim.blendIn, installAnim.blendOut, installAnim.duration, installAnim.flags, 0, 0, 0, 0)
                if modName == 'wheelColor' or modName == 'pearlescentColor' or modName == 'dashboardColor' or modName == 'interiorColor' or modName == 'color1' or modName == 'color2' then
                    
                    local paintType = nil
                    useSkillCheck = false

                    if args.labels ~= nil and next(args.labels) then
                        paintType = args.labels[scrollIndex]:lower()
                    elseif args.openDialogue ~= nil and args.openDialogue == true and scrollIndex == 2 and itemMod.rgb ~= nil then 
                        paintType = 'custom'
                    end

                    for i = 1, 3 do 
                        SprayCanParticles('scr_recartheft', 'scr_wheel_burnout', paintType, itemMod.rgb)
                        Wait(1500)
                    end

                    RemoveNamedPtfxAsset('scr_recartheft')
                end
            end
            Wait(100)

            local success = {}, nil
            if Config.ItemBasedMods.SkillCheck.Enable then 
                if useSkillCheck then
                    success = SkillCheck(Config.ItemBasedMods.SkillCheck.Difficulty, Config.ItemBasedMods.SkillCheck.Inputs)
                end
            else
                success = true
            end
    
            if success then
                inAction = true
                Lib.RemoveLocalEntity(data.entity, {'tuningsystem:target:installMod_'..tostring(modName)}, Config.ItemBasedMods.Target.Label)
                lib.hideTextUI()
                Wait(250)
                installingMod = false
                installDone = true
                ClearPedTasks(player)
                Wait(200)
                local netId = NetworkGetNetworkIdFromEntity(object)
                TriggerServerEvent('tuningsystem:server:deleteEntity', netId)
                Wait(500)
                SetVehicleDoorsShut(data.entity, false)
            else
                Wait(200)
                ClearPedTasks(player)
                if idleAnim.dict ~= nil then
                    TaskPlayAnim(player, idleAnim.dict, idleAnim.name, idleAnim.blendIn, idleAnim.blendOut, idleAnim.duration, idleAnim.flags, 0, 0, 0, 0)
                end
                Wait(500)
            end
        else
            Core.Notification({
                title = '',
                message = Lang['move_closer_mod_interaction']:format(Config.Mods[modName].point),
                type = 'inform'
            })
        end
    end
    
    local CreateVehicleModTarget = function(vehicle, dist, object, modName, idleAnim)
        local target = {
            options = {
                {
                    name = 'tuningsystem:target:installMod_'..tostring(modName),
                    icon = Config.ItemBasedMods.Target.Icon,
                    label = Config.ItemBasedMods.Target.Label,
                    type = 'client',
                    canInteract = IsNearVehicle,
                    distance =  dist,
                    onSelect = function(data) 
                        InstallVehicleMod(data, object, modName, idleAnim)
                    end
                }
            },
            distance = dist,
            canInteract = IsNearVehicle,
        }
        Lib.AddLocalEntity(vehicle, target)
    end

    CreateVehicleModTarget(vehicle, 5.0, modObject, modName, anim)
    
    lib.showTextUI(Config.ItemBasedMods.CancelInteraction.String, {position = 'left-center'})
    while modObject ~= nil and DoesEntityExist(modObject) do 
        Wait(0)
        if IsControlJustPressed(0, Config.ItemBasedMods.CancelInteraction.Button) and not inAction then
            lib.hideTextUI()
            local netId = NetworkGetNetworkIdFromEntity(modObject)
            TriggerServerEvent('tuningsystem:server:deleteEntity', netId)
            ClearPedTasks(player)
            Lib.RemoveLocalEntity(vehicle, {'tuningsystem:target:installMod_'..tostring(modName)}, Config.ItemBasedMods.Target.Label)
            installingMod = false
            installCancel = true
            installDone = true
        end
    end

    while not installDone do 
        Wait(500)
    end

    if installCancel then 
        return false 
    else
        return true
    end

end

Citizen.CreateThread(function()
    while true do
        Wait(5)
        local sleep = true
        if browsingMods and itemMod ~= nil and itemMod.vehicle ~= nil then
            sleep = false
            if IsControlJustPressed(0, Config.ItemBasedMods.PreviewControls.Hood) then -- numpad 4 // hood
                SetVehicleDoorOpen(itemMod.vehicle, 4, false)
            elseif IsControlJustPressed(0, Config.ItemBasedMods.PreviewControls.Trunk) then -- numpad 5 // trunk
                SetVehicleDoorOpen(itemMod.vehicle, 5, false)
            elseif IsControlJustPressed(0, Config.ItemBasedMods.PreviewControls.Doors) then -- numpad 6 // all normal doors
                for i = 0, 3, 1 do 
                    SetVehicleDoorOpen(itemMod.vehicle, i, false)
                end
            elseif IsControlJustPressed(0, Config.ItemBasedMods.PreviewControls.Close) then -- numpad 9 // shut all doors
                SetVehicleDoorsShut(itemMod.vehicle, false)
            end
        end
        if sleep then 
            Wait(2000)
        end
    end
end)

GetVehicleClosestWheel = function(vehicle, maxDist)
    local closestWheel = {}
    
    local numWheels = tostring(GetVehicleNumberOfWheels(vehicle))
    for tyreIdx,v in pairs(Lib.VehicleWheels[numWheels]) do
        local boneIndex = GetEntityBoneIndexByName(vehicle, v.bone)
        local bonePos = GetEntityBonePosition_2(vehicle, boneIndex)
        local distance = #(coords - bonePos)
        if distance < maxDist then
            maxDist = distance
            closestWheel = Lib.VehicleWheels[numWheels][tyreIdx]
            closestWheel.pos = bonePos
        end
    end

    return closestWheel
end

GetVehicleModInstallPoint = function(vehicle, modName)
    local d1,d2 = GetModelDimensions(GetEntityModel(vehicle))
    local dist = 2.0
    local point = Config.Mods[modName].point

    local pointCoords, pointDist = nil, 0

    if point == 'front' or point == 'engine' then
        pointCoords = GetOffsetFromEntityInWorldCoords(vehicle, 0.0, (d2.y + 0.2), 0.0)
    elseif point == 'rear' then 
        pointCoords = GetOffsetFromEntityInWorldCoords(vehicle, 0.0, (d1.y - 0.4), 0.0)
    elseif point == 'vehicle' then
        pointCoords = GetEntityCoords(vehicle)
        dist = 3.0
    elseif point == 'wheel' then
        local wheel = GetVehicleClosestWheel(vehicle, 1.2)
        if next(wheel) then
            TaskTurnPedToFaceCoord(player, wheel.pos.x, wheel.pos.y, wheel.pos.z, 1000)
            return true
        else
            return false
        end
    elseif point == 'inside' then
        if IsPedInVehicle(player, vehicle, false) then 
            return true
        else
            return false
        end
    end

    pointDist = #(coords - pointCoords)

    if pointDist < dist then 
        return true
    end

    return false
end

SprayCanParticles = function(ptfx, effectName, paint, rgb)
    local fwdVec = GetEntityForwardVector(player)
    local particleCoords = GetEntityCoords(player) + fwdVec * 1.0 + vector3(0.0, 0.0, -2.5)

    Lib.LoadPtfxAsset(ptfx)

    local color = {0, 0, 0}
    if paint == 'custom' then 
        color = rgb
    else
        local match = false
        for k,v in pairs(Config.ItemBasedMods.StringToRGB) do
            if string.match(paint, k) then
                color = {v[1], v[2], v[3]}
                match = true 
                break
            end
        end
        if not match then
            color = Config.ItemBasedMods.StringToRGB['white']
        end
    end

    local heading = GetEntityHeading(player)
    UseParticleFxAssetNextCall(ptfx)
    SetParticleFxNonLoopedColour(color[1] / 255, color[2] / 255, color[3] / 255)
    SetParticleFxNonLoopedAlpha(1.0)
    local spray = StartNetworkedParticleFxNonLoopedAtCoord(effectName, particleCoords.x, particleCoords.y, particleCoords.z + 1.5, 0.0, 0.0, heading, 0.7, 0.0, 0.0, 0.0)
end
