ALTER TABLE
    `users`
ADD
    IF NOT EXISTS `motorhome_inside` TEXT NULL DEFAULT NULL;

CREATE TABLE IF NOT EXISTS `motorhome_decorations` (
	`id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
	`plate` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8mb3_general_ci',
	`creator` VARCHAR(70) NOT NULL DEFAULT '0' COLLATE 'utf8mb3_general_ci',
	`modelName` VARCHAR(50) NOT NULL DEFAULT '0' COLLATE 'utf8mb3_general_ci',
	`coords` TEXT NULL DEFAULT NULL COLLATE 'utf8mb3_general_ci',
	`rotation` TEXT NOT NULL DEFAULT '' COLLATE 'utf8mb3_general_ci',
	`inStash` TINYINT(1) NOT NULL DEFAULT '0',
	`created` TIMESTAMP NULL DEFAULT NULL,
	PRIMARY KEY (`id`) USING BTREE,
	INDEX `id` (`id`, `plate`) USING BTREE
)
COLLATE='utf8mb3_general_ci'
ENGINE=InnoDB
AUTO_INCREMENT=5
;

CREATE TABLE IF NOT EXISTS `motorhome` (
	`id` INT(11) NOT NULL AUTO_INCREMENT,
	`plate` VARCHAR(50) NOT NULL DEFAULT '0' COLLATE 'utf8mb3_general_ci',
	`stash` TEXT NULL DEFAULT NULL COLLATE 'utf8mb3_general_ci',
	`wardrobe` TEXT NULL DEFAULT NULL COLLATE 'utf8mb3_general_ci',
	`charge` TEXT NULL DEFAULT NULL COLLATE 'utf8mb3_general_ci',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE INDEX `plate` (`plate`) USING BTREE
)
COLLATE='utf8mb3_general_ci'
ENGINE=InnoDB
AUTO_INCREMENT=13
;
