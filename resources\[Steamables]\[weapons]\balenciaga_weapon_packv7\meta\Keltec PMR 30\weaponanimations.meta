<?xml version="1.0" encoding="UTF - 8"?>

<CWeaponAnimationsSets>
	<WeaponAnimationsSets>
		<Item key="Default">
			<WeaponAnimations>
				<Item key="WEAPON_KELTECPMR30">
					<CoverMovementClipSetHash/>
					<CoverMovementExtraClipSetHash/>
					<CoverAlternateMovementClipSetHash>cover@move@ai@base@1h</CoverAlternateMovementClipSetHash>
					<CoverWeaponClipSetHash>Cover_Wpn_Pistol</CoverWeaponClipSetHash>
					<MotionClipSetHash>weapons@pistol@pistol</MotionClipSetHash>
					<MotionFilterHash>BothArms_filter</MotionFilterHash>
					<MotionCrouchClipSetHash/>
					<MotionStrafingClipSetHash/>
					<MotionStrafingStealthClipSetHash/>
					<MotionStrafingUpperBodyClipSetHash/>
					<WeaponClipSetHash>weapons@pistol@pistol</WeaponClipSetHash>
					<WeaponClipSetStreamedHash>weapons@pistol@pistol_str</WeaponClipSetStreamedHash>
					<WeaponClipSetHashInjured>weapons@pistol@pistol_injured</WeaponClipSetHashInjured>
					<WeaponClipSetHashStealth>weapons@pistol@pistol@stealth</WeaponClipSetHashStealth>
					<WeaponClipSetHashHiCover/>
					<AlternativeClipSetWhenBlocked/>
					<ScopeWeaponClipSet/>
					<AlternateAimingStandingClipSetHash/>
					<AlternateAimingCrouchingClipSetHash/>
					<FiringVariationsStandingClipSetHash>combat_fire_variations_pistol</FiringVariationsStandingClipSetHash>
					<FiringVariationsCrouchingClipSetHash/>
					<AimTurnStandingClipSetHash>combat_aim_turns_pistol</AimTurnStandingClipSetHash>
					<AimTurnCrouchingClipSetHash/>
					<MeleeClipSetHash>melee@pistol@streamed_core</MeleeClipSetHash>
					<MeleeVariationClipSetHash/>
					<MeleeTauntClipSetHash/>
					<MeleeSupportTauntClipSetHash/>
					<MeleeStealthClipSetHash/>
					<ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
					<JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@PISTOL</JumpUpperbodyClipSetHash>
					<FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@PISTOL</FallUpperbodyClipSetHash>
					<FromStrafeTransitionUpperBodyClipSetHash>weapons@pistol@</FromStrafeTransitionUpperBodyClipSetHash>
					<SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
					<SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
					<AnimFireRateModifier value="0.800000"/>
					<AnimBlindFireRateModifier value="1.000000"/>
					<AnimWantingToShootFireRateModifier value="3.000000"/>
					<UseFromStrafeUpperBodyAimNetwork value="true"/>
					<AimingDownTheBarrel value="true"/>
					<WeaponSwapData ref="SWAP_DEFAULT"/>
					<AimGrenadeThrowNormalClipsetHash>Wpn_Thrown_Grenade_Aiming_Rifle</AimGrenadeThrowNormalClipsetHash>
					<AimGrenadeThrowAlternateClipsetHash/>
				</Item>
			</WeaponAnimations>
		</Item>
		<Item key="Gang">
			<Fallback>Default</Fallback>
			<WeaponAnimations>
				<Item key="WEAPON_KELTECPMR30">
					<CoverMovementClipSetHash/>
					<CoverMovementExtraClipSetHash/>
					<CoverAlternateMovementClipSetHash/>
					<CoverWeaponClipSetHash/>
					<MotionClipSetHash/>
					<MotionFilterHash/>
					<MotionCrouchClipSetHash/>
					<MotionStrafingClipSetHash/>
					<MotionStrafingStealthClipSetHash/>
					<MotionStrafingUpperBodyClipSetHash/>
					<WeaponClipSetHash/>
					<WeaponClipSetStreamedHash/>
					<WeaponClipSetHashInjured/>
					<WeaponClipSetHashStealth/>
					<WeaponClipSetHashHiCover/>
					<AlternativeClipSetWhenBlocked/>
					<ScopeWeaponClipSet/>
					<AlternateAimingStandingClipSetHash/>
					<AlternateAimingCrouchingClipSetHash/>
					<FiringVariationsStandingClipSetHash>combat_fire_variations_gang_pistol</FiringVariationsStandingClipSetHash>
					<FiringVariationsCrouchingClipSetHash/>
					<AimTurnStandingClipSetHash/>
					<AimTurnCrouchingClipSetHash/>
					<MeleeClipSetHash/>
					<MeleeVariationClipSetHash/>
					<MeleeTauntClipSetHash/>
					<MeleeSupportTauntClipSetHash/>
					<MeleeStealthClipSetHash/>
					<ShellShockedClipSetHash/>
					<JumpUpperbodyClipSetHash/>
					<FallUpperbodyClipSetHash/>
					<FromStrafeTransitionUpperBodyClipSetHash>weapons@pistol@</FromStrafeTransitionUpperBodyClipSetHash>
					<SwapWeaponFilterHash/>
					<SwapWeaponInLowCoverFilterHash/>
					<AnimFireRateModifier value="0.800000"/>
					<AnimBlindFireRateModifier value="1.000000"/>
					<AnimWantingToShootFireRateModifier value="-1.000000"/>
					<UseFromStrafeUpperBodyAimNetwork value="true"/>
					<AimingDownTheBarrel value="true"/>
					<WeaponSwapData ref="NULL"/>
					<AimGrenadeThrowNormalClipsetHash/>
					<AimGrenadeThrowAlternateClipsetHash/>
				</Item>
			</WeaponAnimations>
		</Item>
		<Item key="Hillbilly">
			<Fallback>Default</Fallback>
			<WeaponAnimations>
				<Item key="WEAPON_KELTECPMR30">
					<CoverMovementClipSetHash/>
					<CoverMovementExtraClipSetHash/>
					<CoverAlternateMovementClipSetHash>cover@move@ai@base@1h</CoverAlternateMovementClipSetHash>
					<CoverWeaponClipSetHash>Cover_Wpn_Pistol</CoverWeaponClipSetHash>
					<MotionClipSetHash>weapons@pistol_1h@hillbilly</MotionClipSetHash>
					<MotionFilterHash>BothArms_filter</MotionFilterHash>
					<MotionCrouchClipSetHash/>
					<MotionStrafingClipSetHash/>
					<MotionStrafingStealthClipSetHash/>
					<MotionStrafingUpperBodyClipSetHash/>
					<WeaponClipSetHash>weapons@pistol_1h@hillbilly</WeaponClipSetHash>
					<WeaponClipSetStreamedHash>weapons@pistol_1h@hillbilly_str</WeaponClipSetStreamedHash>
					<WeaponClipSetHashInjured>weapons@pistol@pistol_injured</WeaponClipSetHashInjured>
					<WeaponClipSetHashStealth>weapons@pistol@pistol@stealth</WeaponClipSetHashStealth>
					<WeaponClipSetHashHiCover/>
					<AlternativeClipSetWhenBlocked/>
					<ScopeWeaponClipSet/>
					<AlternateAimingStandingClipSetHash/>
					<AlternateAimingCrouchingClipSetHash/>
					<FiringVariationsStandingClipSetHash>combat_fire_variations_hillbilly_pistol_1H</FiringVariationsStandingClipSetHash>
					<FiringVariationsCrouchingClipSetHash/>
					<AimTurnStandingClipSetHash>combat_aim_turns_pistol</AimTurnStandingClipSetHash>
					<AimTurnCrouchingClipSetHash/>
					<MeleeClipSetHash>melee@pistol@streamed_core</MeleeClipSetHash>
					<MeleeVariationClipSetHash/>
					<MeleeTauntClipSetHash/>
					<MeleeSupportTauntClipSetHash/>
					<MeleeStealthClipSetHash/>
					<ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
					<JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@PISTOL</JumpUpperbodyClipSetHash>
					<FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@PISTOL</FallUpperbodyClipSetHash>
					<FromStrafeTransitionUpperBodyClipSetHash>weapons@pistol_1h@hillbilly</FromStrafeTransitionUpperBodyClipSetHash>
					<SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
					<SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
					<AnimFireRateModifier value="0.800000"/>
					<AnimBlindFireRateModifier value="1.000000"/>
					<AnimWantingToShootFireRateModifier value="3.000000"/>
					<UseFromStrafeUpperBodyAimNetwork value="true"/>
					<AimingDownTheBarrel value="true"/>
					<WeaponSwapData ref="SWAP_DEFAULT"/>
					<AimGrenadeThrowNormalClipsetHash/>
					<AimGrenadeThrowAlternateClipsetHash/>
					<GestureBeckonOverrideClipSetHash>combat_gestures_beckon_pistol_1h_hillbilly</GestureBeckonOverrideClipSetHash>
					<GestureOverThereOverrideClipSetHash>combat_gestures_overthere_pistol_1h_hillbilly</GestureOverThereOverrideClipSetHash>
					<GestureHaltOverrideClipSetHash>combat_gestures_halt_pistol_1h_hillbilly</GestureHaltOverrideClipSetHash>
					<GestureGlancesOverrideClipSetHash>combat_gestures_glances_pistol_1h_hillbilly</GestureGlancesOverrideClipSetHash>
					<CombatReactionOverrideClipSetHash>combat_reactions_pistol_1h_hillbilly</CombatReactionOverrideClipSetHash>
					<UseLeftHandIKAllowTags value="true"/>
				</Item>
			</WeaponAnimations>
		</Item>
		<Item key="Gang1H">
			<Fallback>Gang</Fallback>
			<WeaponAnimations>
				<Item key="WEAPON_KELTECPMR30">
					<CoverMovementClipSetHash/>
					<CoverMovementExtraClipSetHash/>
					<CoverAlternateMovementClipSetHash>cover@move@ai@base@1h</CoverAlternateMovementClipSetHash>
					<CoverWeaponClipSetHash>Cover_Wpn_Pistol</CoverWeaponClipSetHash>
					<MotionClipSetHash>weapons@pistol_1h@gang</MotionClipSetHash>
					<MotionFilterHash>BothArms_filter</MotionFilterHash>
					<MotionCrouchClipSetHash/>
					<MotionStrafingClipSetHash/>
					<MotionStrafingStealthClipSetHash/>
					<MotionStrafingUpperBodyClipSetHash/>
					<WeaponClipSetHash>weapons@pistol_1h@gang</WeaponClipSetHash>
					<WeaponClipSetStreamedHash>weapons@pistol_1h@gang_str</WeaponClipSetStreamedHash>
					<WeaponClipSetHashInjured>weapons@pistol@pistol_injured</WeaponClipSetHashInjured>
					<WeaponClipSetHashStealth>weapons@pistol@pistol@stealth</WeaponClipSetHashStealth>
					<WeaponClipSetHashHiCover/>
					<AlternativeClipSetWhenBlocked/>
					<ScopeWeaponClipSet/>
					<AlternateAimingStandingClipSetHash/>
					<AlternateAimingCrouchingClipSetHash/>
					<FiringVariationsStandingClipSetHash>combat_fire_variations_gang_pistol_1H</FiringVariationsStandingClipSetHash>
					<FiringVariationsCrouchingClipSetHash/>
					<AimTurnStandingClipSetHash>combat_aim_turns_pistol</AimTurnStandingClipSetHash>
					<AimTurnCrouchingClipSetHash/>
					<MeleeClipSetHash>melee@pistol@streamed_core</MeleeClipSetHash>
					<MeleeVariationClipSetHash/>
					<MeleeTauntClipSetHash/>
					<MeleeSupportTauntClipSetHash/>
					<MeleeStealthClipSetHash/>
					<ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
					<JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@PISTOL</JumpUpperbodyClipSetHash>
					<FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@PISTOL</FallUpperbodyClipSetHash>
					<FromStrafeTransitionUpperBodyClipSetHash>weapons@pistol_1h@gang</FromStrafeTransitionUpperBodyClipSetHash>
					<SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
					<SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
					<AnimFireRateModifier value="0.800000"/>
					<AnimBlindFireRateModifier value="1.000000"/>
					<AnimWantingToShootFireRateModifier value="3.000000"/>
					<UseFromStrafeUpperBodyAimNetwork value="true"/>
					<AimingDownTheBarrel value="true"/>
					<WeaponSwapData ref="SWAP_DEFAULT"/>
					<AimGrenadeThrowNormalClipsetHash/>
					<AimGrenadeThrowAlternateClipsetHash/>
					<GestureBeckonOverrideClipSetHash>combat_gestures_beckon_pistol_1h_gang</GestureBeckonOverrideClipSetHash>
					<GestureOverThereOverrideClipSetHash>combat_gestures_overthere_pistol_1h_gang</GestureOverThereOverrideClipSetHash>
					<GestureHaltOverrideClipSetHash>combat_gestures_halt_pistol_1h_gang</GestureHaltOverrideClipSetHash>
					<GestureGlancesOverrideClipSetHash>combat_gestures_glances_pistol_1h_gang</GestureGlancesOverrideClipSetHash>
					<CombatReactionOverrideClipSetHash>combat_reactions_pistol_1h_gang</CombatReactionOverrideClipSetHash>
					<UseLeftHandIKAllowTags value="true"/>
				</Item>
			</WeaponAnimations>
		</Item>
		<Item key="FirstPerson">
			<Fallback>Default</Fallback>
			<WeaponAnimations>
				<Item key="WEAPON_KELTECPMR30">
					<MovementOverrideClipSetHash>move_m@generic</MovementOverrideClipSetHash>
					<CoverMovementClipSetHash/>
					<CoverMovementExtraClipSetHash/>
					<CoverAlternateMovementClipSetHash>cover@move@ai@base@1h</CoverAlternateMovementClipSetHash>
					<CoverWeaponClipSetHash>Cover_FirstPerson_Wpn_Pistol</CoverWeaponClipSetHash>
					<MotionClipSetHash>weapons@first_person@aim_idle@generic@pistol@shared@core</MotionClipSetHash>
					<MotionFilterHash>BothArms_filter</MotionFilterHash>
					<MotionCrouchClipSetHash/>
					<MotionStrafingClipSetHash/>
					<MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
					<MotionStrafingUpperBodyClipSetHash/>
					<WeaponClipSetHash>weapons@first_person@aim_idle@generic@pistol@shared@core</WeaponClipSetHash>
					<WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@pistol@pistol_str</WeaponClipSetStreamedHash>
					<WeaponClipSetHashInjured>weapons@pistol@pistol_injured</WeaponClipSetHashInjured>
					<WeaponClipSetHashStealth>weapons@first_person@aim_stealth@generic@pistol@shared@core</WeaponClipSetHashStealth>
					<WeaponClipSetHashHiCover/>
					<AlternativeClipSetWhenBlocked/>
					<ScopeWeaponClipSet/>
					<AlternateAimingStandingClipSetHash/>
					<AlternateAimingCrouchingClipSetHash/>
					<FiringVariationsStandingClipSetHash>combat_fire_variations_pistol</FiringVariationsStandingClipSetHash>
					<FiringVariationsCrouchingClipSetHash/>
					<AimTurnStandingClipSetHash>combat_aim_turns_pistol</AimTurnStandingClipSetHash>
					<AimTurnCrouchingClipSetHash/>
					<MeleeClipSetHash>melee@pistol@streamed_fps</MeleeClipSetHash>
					<MeleeVariationClipSetHash/>
					<MeleeTauntClipSetHash/>
					<MeleeSupportTauntClipSetHash/>
					<MeleeStealthClipSetHash/>
					<ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
					<JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@PISTOL</JumpUpperbodyClipSetHash>
					<FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@PISTOL</FallUpperbodyClipSetHash>
					<FromStrafeTransitionUpperBodyClipSetHash>weapons@pistol@</FromStrafeTransitionUpperBodyClipSetHash>
					<SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
					<SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
					<AnimFireRateModifier value="0.800000"/>
					<AnimBlindFireRateModifier value="1.000000"/>
					<AnimWantingToShootFireRateModifier value="3.000000"/>
					<UseFromStrafeUpperBodyAimNetwork value="true"/>
					<AimingDownTheBarrel value="true"/>
					<WeaponSwapData ref="SWAP_DEFAULT"/>
					<AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@pistol@shared@core</AimGrenadeThrowNormalClipsetHash>
					<AimGrenadeThrowAlternateClipsetHash/>
					<FPSTransitionFromIdleHash/>
					<FPSTransitionFromRNGHash>weapons@first_person@aim_rng@p_m_zero@pistol@shared@aim_trans@rng_to_idle</FPSTransitionFromRNGHash>
					<FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@pistol@shared@aim_trans@lt_to_idle</FPSTransitionFromLTHash>
					<FPSTransitionFromScopeHash>weapons@first_person@aim_scope@p_m_zero@pistol@shared@aim_trans@scope_to_idle</FPSTransitionFromScopeHash>
					<FPSTransitionFromUnholsterHash>weapons@first_person@aim_idle@p_m_zero@pistol@shared@aim_trans@unholster_to_idle</FPSTransitionFromUnholsterHash>
					<FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@pistol@shared@aim_trans@stealth_to_idle</FPSTransitionFromStealthHash>
					<FPSTransitionToStealthHash>weapons@first_person@aim_idle@p_m_zero@pistol@shared@aim_trans@idle_to_stealth</FPSTransitionToStealthHash>
					<FPSTransitionToStealthFromUnholsterHash>weapons@first_person@aim_stealth@p_m_zero@pistol@shared@aim_trans@unholster_to_stealth</FPSTransitionToStealthFromUnholsterHash>
					<FPSFidgetClipsetHashes>
						<Item>weapons@first_person@aim_idle@p_m_zero@pistol@shared@fidgets@a</Item>
						<Item>weapons@first_person@aim_idle@p_m_zero@pistol@shared@fidgets@b</Item>
						<Item>weapons@first_person@aim_idle@p_m_zero@pistol@shared@fidgets@c</Item>
					</FPSFidgetClipsetHashes>
					<WeaponClipSetHashForClone>weapons@first_person@aim_idle@remote_clone@pistol@shared@core</WeaponClipSetHashForClone>
				</Item>
			</WeaponAnimations>
		</Item>
		<Item key="FirstPersonAiming">
			<Fallback>Default</Fallback>
			<WeaponAnimations>
				<Item key="WEAPON_KELTECPMR30">
					<CoverMovementClipSetHash/>
					<CoverMovementExtraClipSetHash/>
					<CoverAlternateMovementClipSetHash>cover@move@ai@base@1h</CoverAlternateMovementClipSetHash>
					<CoverWeaponClipSetHash>Cover_FirstPerson_Wpn_Pistol</CoverWeaponClipSetHash>
					<MotionClipSetHash>weapons@first_person@aim_rng@generic@pistol@shared@core</MotionClipSetHash>
					<MotionFilterHash>BothArms_filter</MotionFilterHash>
					<MotionCrouchClipSetHash/>
					<MotionStrafingClipSetHash/>
					<MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
					<MotionStrafingUpperBodyClipSetHash/>
					<WeaponClipSetHash>weapons@first_person@aim_lt@generic@pistol@w_fire</WeaponClipSetHash>
					<WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@pistol@pistol_str</WeaponClipSetStreamedHash>
					<WeaponClipSetHashInjured>weapons@pistol@pistol_injured</WeaponClipSetHashInjured>
					<WeaponClipSetHashStealth>weapons@first_person@aim_lt@generic@pistol@w_fire</WeaponClipSetHashStealth>
					<WeaponClipSetHashHiCover/>
					<AlternativeClipSetWhenBlocked/>
					<ScopeWeaponClipSet/>
					<AlternateAimingStandingClipSetHash/>
					<AlternateAimingCrouchingClipSetHash/>
					<FiringVariationsStandingClipSetHash>combat_fire_variations_pistol</FiringVariationsStandingClipSetHash>
					<FiringVariationsCrouchingClipSetHash/>
					<AimTurnStandingClipSetHash>combat_aim_turns_pistol</AimTurnStandingClipSetHash>
					<AimTurnCrouchingClipSetHash/>
					<MeleeClipSetHash>melee@pistol@streamed_fps</MeleeClipSetHash>
					<MeleeVariationClipSetHash/>
					<MeleeTauntClipSetHash/>
					<MeleeSupportTauntClipSetHash/>
					<MeleeStealthClipSetHash/>
					<ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
					<JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@PISTOL</JumpUpperbodyClipSetHash>
					<FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@PISTOL</FallUpperbodyClipSetHash>
					<FromStrafeTransitionUpperBodyClipSetHash>weapons@pistol@</FromStrafeTransitionUpperBodyClipSetHash>
					<SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
					<SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
					<AnimFireRateModifier value="0.800000"/>
					<AnimBlindFireRateModifier value="1.000000"/>
					<AnimWantingToShootFireRateModifier value="3.000000"/>
					<UseFromStrafeUpperBodyAimNetwork value="true"/>
					<AimingDownTheBarrel value="true"/>
					<WeaponSwapData ref="SWAP_DEFAULT"/>
					<AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@pistol@shared@core</AimGrenadeThrowNormalClipsetHash>
					<AimGrenadeThrowAlternateClipsetHash/>
					<FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@pistol@shared@aim_trans@idle_to_lt</FPSTransitionFromIdleHash>
					<FPSTransitionFromRNGHash>weapons@first_person@aim_rng@p_m_zero@pistol@shared@aim_trans@rng_to_lt</FPSTransitionFromRNGHash>
					<FPSTransitionFromLTHash/>
					<FPSTransitionFromScopeHash>weapons@first_person@aim_scope@p_m_zero@pistol@shared@aim_trans@scope_to_lt</FPSTransitionFromScopeHash>
					<FPSTransitionFromUnholsterHash>weapons@first_person@aim_lt@p_m_zero@pistol@shared@aim_trans@unholster_to_lt</FPSTransitionFromUnholsterHash>
					<FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@pistol@shared@aim_trans@stealth_to_lt</FPSTransitionFromStealthHash>
					<FPSTransitionToStealthHash>weapons@first_person@aim_lt@p_m_zero@pistol@shared@aim_trans@lt_to_stealth</FPSTransitionToStealthHash>
					<FPSFidgetClipsetHashes>
						<Item>weapons@first_person@aim_lt@p_m_zero@pistol@shared@fidgets@a</Item>
						<Item>weapons@first_person@aim_lt@p_m_zero@pistol@shared@fidgets@b</Item>
						<Item>weapons@first_person@aim_lt@p_m_zero@pistol@shared@fidgets@c</Item>
						<Item>weapons@first_person@aim_lt@p_m_zero@pistol@shared@fidgets@d</Item>
					</FPSFidgetClipsetHashes>
				</Item>
			</WeaponAnimations>
		</Item>
		<Item key="FirstPersonRNG">
			<Fallback>Default</Fallback>
			<WeaponAnimations>
				<Item key="WEAPON_KELTECPMR30">
					<CoverMovementClipSetHash/>
					<CoverMovementExtraClipSetHash/>
					<CoverAlternateMovementClipSetHash>cover@move@ai@base@1h</CoverAlternateMovementClipSetHash>
					<CoverWeaponClipSetHash>Cover_FirstPerson_Wpn_Pistol</CoverWeaponClipSetHash>
					<MotionClipSetHash>weapons@first_person@aim_rng@generic@pistol@shared@core</MotionClipSetHash>
					<MotionFilterHash>BothArms_filter</MotionFilterHash>
					<MotionCrouchClipSetHash/>
					<MotionStrafingClipSetHash/>
					<MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
					<MotionStrafingUpperBodyClipSetHash/>
					<WeaponClipSetHash>weapons@first_person@aim_rng@pistol@pistol</WeaponClipSetHash>
					<WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@pistol@pistol_str</WeaponClipSetStreamedHash>
					<WeaponClipSetHashInjured>weapons@pistol@pistol_injured</WeaponClipSetHashInjured>
					<WeaponClipSetHashStealth>weapons@first_person@aim_rng@pistol@pistol</WeaponClipSetHashStealth>
					<WeaponClipSetHashHiCover/>
					<AlternativeClipSetWhenBlocked/>
					<ScopeWeaponClipSet/>
					<AlternateAimingStandingClipSetHash/>
					<AlternateAimingCrouchingClipSetHash/>
					<FiringVariationsStandingClipSetHash>combat_fire_variations_pistol</FiringVariationsStandingClipSetHash>
					<FiringVariationsCrouchingClipSetHash/>
					<AimTurnStandingClipSetHash>combat_aim_turns_pistol</AimTurnStandingClipSetHash>
					<AimTurnCrouchingClipSetHash/>
					<MeleeClipSetHash>melee@pistol@streamed_fps</MeleeClipSetHash>
					<MeleeVariationClipSetHash/>
					<MeleeTauntClipSetHash/>
					<MeleeSupportTauntClipSetHash/>
					<MeleeStealthClipSetHash/>
					<ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
					<JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@PISTOL</JumpUpperbodyClipSetHash>
					<FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@PISTOL</FallUpperbodyClipSetHash>
					<FromStrafeTransitionUpperBodyClipSetHash>weapons@pistol@</FromStrafeTransitionUpperBodyClipSetHash>
					<SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
					<SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
					<AnimFireRateModifier value="0.800000"/>
					<AnimBlindFireRateModifier value="1.000000"/>
					<AnimWantingToShootFireRateModifier value="3.000000"/>
					<UseFromStrafeUpperBodyAimNetwork value="true"/>
					<AimingDownTheBarrel value="true"/>
					<WeaponSwapData ref="SWAP_DEFAULT"/>
					<AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@pistol@shared@core</AimGrenadeThrowNormalClipsetHash>
					<AimGrenadeThrowAlternateClipsetHash/>
					<FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@pistol@shared@aim_trans@idle_to_rng</FPSTransitionFromIdleHash>
					<FPSTransitionFromRNGHash/>
					<FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@pistol@shared@aim_trans@lt_to_rng</FPSTransitionFromLTHash>
					<FPSTransitionFromScopeHash>weapons@first_person@aim_scope@p_m_zero@pistol@shared@aim_trans@scope_to_rng</FPSTransitionFromScopeHash>
					<FPSTransitionFromUnholsterHash>weapons@first_person@aim_rng@p_m_zero@pistol@shared@aim_trans@unholster_to_rng</FPSTransitionFromUnholsterHash>
					<FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@pistol@shared@aim_trans@stealth_to_rng</FPSTransitionFromStealthHash>
					<FPSTransitionToStealthHash>weapons@first_person@aim_rng@p_m_zero@pistol@shared@aim_trans@rng_to_stealth</FPSTransitionToStealthHash>
					<FPSFidgetClipsetHashes>
						<Item>weapons@first_person@aim_rng@p_m_zero@pistol@shared@fidgets@a</Item>
						<Item>weapons@first_person@aim_rng@p_m_zero@pistol@shared@fidgets@b</Item>
						<Item>weapons@first_person@aim_rng@p_m_zero@pistol@shared@fidgets@c</Item>
					</FPSFidgetClipsetHashes>
				</Item>
			</WeaponAnimations>
		</Item>
		<Item key="FirstPersonScope">
			<Fallback>Default</Fallback>
			<WeaponAnimations>
				<Item key="WEAPON_KELTECPMR30">
					<CoverMovementClipSetHash/>
					<CoverMovementExtraClipSetHash/>
					<CoverAlternateMovementClipSetHash>cover@move@ai@base@1h</CoverAlternateMovementClipSetHash>
					<CoverWeaponClipSetHash>Cover_FirstPerson_Wpn_Pistol</CoverWeaponClipSetHash>
					<MotionClipSetHash>weapons@first_person@aim_rng@generic@pistol@shared@core</MotionClipSetHash>
					<MotionFilterHash>BothArms_filter</MotionFilterHash>
					<MotionCrouchClipSetHash/>
					<MotionStrafingClipSetHash/>
					<MotionStrafingStealthClipSetHash>move_ped_strafing_stealth</MotionStrafingStealthClipSetHash>
					<MotionStrafingUpperBodyClipSetHash/>
					<WeaponClipSetHash>weapons@first_person@aim_scope@generic@pistol@w_fire</WeaponClipSetHash>
					<WeaponClipSetStreamedHash>weapons@first_person@aim_rng@generic@pistol@pistol_str</WeaponClipSetStreamedHash>
					<WeaponClipSetHashInjured>weapons@pistol@pistol_injured</WeaponClipSetHashInjured>
					<WeaponClipSetHashStealth>weapons@first_person@aim_scope@generic@pistol@w_fire</WeaponClipSetHashStealth>
					<WeaponClipSetHashHiCover/>
					<AlternativeClipSetWhenBlocked/>
					<ScopeWeaponClipSet/>
					<AlternateAimingStandingClipSetHash/>
					<AlternateAimingCrouchingClipSetHash/>
					<FiringVariationsStandingClipSetHash>combat_fire_variations_pistol</FiringVariationsStandingClipSetHash>
					<FiringVariationsCrouchingClipSetHash/>
					<AimTurnStandingClipSetHash>combat_aim_turns_pistol</AimTurnStandingClipSetHash>
					<AimTurnCrouchingClipSetHash/>
					<MeleeClipSetHash>melee@pistol@streamed_fps</MeleeClipSetHash>
					<MeleeVariationClipSetHash/>
					<MeleeTauntClipSetHash/>
					<MeleeSupportTauntClipSetHash/>
					<MeleeStealthClipSetHash/>
					<ShellShockedClipSetHash>reaction@shellshock@unarmed</ShellShockedClipSetHash>
					<JumpUpperbodyClipSetHash>MOVE_JUMP@WEAPONS@PISTOL</JumpUpperbodyClipSetHash>
					<FallUpperbodyClipSetHash>MOVE_FALL@WEAPONS@PISTOL</FallUpperbodyClipSetHash>
					<FromStrafeTransitionUpperBodyClipSetHash>weapons@pistol@</FromStrafeTransitionUpperBodyClipSetHash>
					<SwapWeaponFilterHash>RightArm_NoSpine_filter</SwapWeaponFilterHash>
					<SwapWeaponInLowCoverFilterHash>RightArm_NoSpine_filter</SwapWeaponInLowCoverFilterHash>
					<AnimFireRateModifier value="0.800000"/>
					<AnimBlindFireRateModifier value="1.000000"/>
					<AnimWantingToShootFireRateModifier value="3.000000"/>
					<UseFromStrafeUpperBodyAimNetwork value="true"/>
					<AimingDownTheBarrel value="true"/>
					<WeaponSwapData ref="SWAP_DEFAULT"/>
					<AimGrenadeThrowNormalClipsetHash>weapons@first_person@aim_rng@generic@pistol@shared@core</AimGrenadeThrowNormalClipsetHash>
					<AimGrenadeThrowAlternateClipsetHash/>
					<FPSTransitionFromIdleHash>weapons@first_person@aim_idle@p_m_zero@pistol@shared@aim_trans@idle_to_scope</FPSTransitionFromIdleHash>
					<FPSTransitionFromRNGHash>weapons@first_person@aim_rng@p_m_zero@pistol@shared@aim_trans@rng_to_scope</FPSTransitionFromRNGHash>
					<FPSTransitionFromLTHash>weapons@first_person@aim_lt@p_m_zero@pistol@shared@aim_trans@lt_to_scope</FPSTransitionFromLTHash>
					<FPSTransitionFromScopeHash/>
					<FPSTransitionFromUnholsterHash>weapons@first_person@aim_scope@p_m_zero@pistol@shared@aim_trans@unholster_to_scope</FPSTransitionFromUnholsterHash>
					<FPSTransitionFromStealthHash>weapons@first_person@aim_stealth@p_m_zero@pistol@shared@aim_trans@stealth_to_scope</FPSTransitionFromStealthHash>
					<FPSTransitionToStealthHash>weapons@first_person@aim_scope@p_m_zero@pistol@shared@aim_trans@scope_to_stealth</FPSTransitionToStealthHash>
				</Item>
			</WeaponAnimations>
		</Item>
	</WeaponAnimationsSets>
</CWeaponAnimationsSets>

