Shops = {}
shopBlips = {}

RegisterNetEvent("t1ger_mechanic:client:loadShops", function(shops)
    Shops = shops

    for k,v in pairs(Shops) do
        -- create shop blip:
        CreateShopBlip(v.id, v.blip, v.name)

        -- load shop markers:
        LoadMarkers(v.id)
    end
end)

RegisterNetEvent("t1ger_mechanic:client:shopCreated", function(newShop)
    -- sync data:
    Shops[newShop.id] = newShop
    -- create shop blip:
    CreateShopBlip(newShop.id, newShop.blip, newShop.name)
end)

RegisterNetEvent("t1ger_mechanic:client:shopDeleted", function(shopId)
    -- delete markers:
    if Shops[shopId] and type(Shops[shopId].markers) == "table" then
        for markerClass, markers in pairs(Shops[shopId].markers or {}) do
            if type(markers) == "table" then
                for markerId, markerData in pairs(markers or {}) do
                    RemoveMarker(shopId, markerClass, markerId)
                end
            end
        end
    end
    -- remove shop blip:
    RemoveShopBlip(shopId)
    -- sync data:
    Shops[shopId] = nil
end)

-- Event to update shop data on the client
RegisterNetEvent("t1ger_mechanic:client:updateShopData", function(shopId, key, value)
    if not Shops[shopId] then return end
    Shops[shopId][key] = value
    if Config.Debug then 
        print(("Updated shop attribute %s for shop %d"):format(key, shopId))
    end
end)

-- Event to update shop marker data on the client
RegisterNetEvent("t1ger_mechanic:client:updateMarkerData", function(shopId, class, markerId, value)
    if not Shops[shopId] then return end -- Ensure shop exists
    Shops[shopId].markers = Shops[shopId].markers or {} -- Ensure markers table exists
    Shops[shopId].markers[class] = Shops[shopId].markers[class] or {} -- Ensure marker class exists

    if value == nil then
        -- Remove marker visuals first before deleting the data
        RemoveMarker(shopId, class, markerId)

        -- Now safely remove the marker from storage
        Shops[shopId].markers[class][markerId] = nil
    else
        -- Remove current marker visuals before updating data
        RemoveMarker(shopId, class, markerId)

        -- Now update the marker data
        Shops[shopId].markers[class][markerId] = value

        -- Now add/update the marker visuals
        AddMarker(shopId, class, markerId)
    end
end)

-- Event to update shop's billing data on the client
RegisterNetEvent("t1ger_mechanic:client:updateBilling", function(shopId, billingNumber, data)
    if not Shops[shopId] then return end
    if not Shops[shopId].billing then Shops[shopId].billing = {} end
    Shops[shopId].billing[billingNumber] = data
end)

--- Deletes a specific shop blip if it exists.
--- @param shopId (number) The unique ID of the shop blip to be deleted.
function RemoveShopBlip(shopId)
    if shopBlips[shopId] then
        if DoesBlipExist(shopBlips[shopId]) then
            RemoveBlip(shopBlips[shopId])
        end
        shopBlips[shopId] = nil -- Remove reference from the table
    end
end

--- Creates a blip for a shop if enabled.
--- Removes any existing blip for the same shop before creating a new one.
--- @param shopId (number) The unique ID of the shop.
--- @param shopBlip (table) Contains blip configuration (coords, sprite, display, scale, color, enable).
--- @param shopName (string) The name of the shop to be displayed on the blip.
function CreateShopBlip(shopId, shopBlip, shopName)
    -- Remove existing blip
    RemoveShopBlip(shopId)

    -- Validate blip data and ensure blip is enabled
    if not shopBlip or not shopBlip.enable then
        return
    end

    -- Ensure coordinates exist
    if not shopBlip.coords or not shopBlip.coords.x or not shopBlip.coords.y or not shopBlip.coords.z then
        return error(("[CreateShopBlip] Invalid coordinates for shopId: %s"):format(shopId))
    end

    -- Create new blip
    local blip = AddBlipForCoord(shopBlip.coords.x, shopBlip.coords.y, shopBlip.coords.z)
    SetBlipSprite(blip, shopBlip.sprite or 1)  -- Default sprite if none provided
    SetBlipDisplay(blip, shopBlip.display or 4)
    SetBlipScale(blip, shopBlip.scale or 1.0)
    SetBlipColour(blip, shopBlip.color or 0)
    SetBlipAsShortRange(blip, true)

    -- Set blip name
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString(shopName or "Unknown Shop")
    EndTextCommandSetBlipName(blip)

    -- Store the blip for future reference
    shopBlips[shopId] = blip
end

--- Checks if the local player is an employee of a specific shop or any shop.
---@param shopId number|nil (Optional) The ID of the shop to check. If nil, checks all shops.
---@return boolean isEmployee True if the player is an employee, false otherwise.
---@return number|nil foundShopId The ID of the shop the player is an employee of, or nil if not found.
function IsPlayerEmployee(shopId)
    if not Shops or not _API.Player then return false end

    local playerIdentifier = _API.Player.charId
    if not playerIdentifier then return false end

    if shopId then
        -- Check only the specified shop
        local shopData = Shops[shopId]
        if shopData and type(shopData.employees) == "table" then
            for _, emp in pairs(shopData.employees) do
                if emp.identifier == playerIdentifier then
                    return true, shopId
                end
            end
        end
    else
        -- Check all shops
        for id, shopData in pairs(Shops) do
            if type(shopData.employees) == "table" then
                for _, emp in pairs(shopData.employees) do
                    if emp.identifier == playerIdentifier then
                        return true, id
                    end
                end
            end
        end
    end

    return false
end
exports("IsPlayerEmployee", IsPlayerEmployee)

--- Checks if the local player has job matching any shop job.
---@param shopId number|nil (Optional) The ID of the shop to check. If nil, checks all shops.
---@return boolean hasJob True if the player has mechanic job, false otherwise.
---@return number|nil shopId The ID of the shop the player has job of, or nil if not found.
function IsPlayerMechanic(shopId)
    if not Shops or not _API.Player then return false end

    local playerJob = _API.Player:GetJob()
    if not playerJob or not playerJob.name then return false end

    if shopId then
        -- Check only the specified shop
        local shopData = Shops[shopId]
        if shopData and shopData.job and shopData.job.name == playerJob.name then
            return true, shopId
        end
    else
        -- Check all shops
        for id, shopData in pairs(Shops) do
            if shopData.job and shopData.job.name == playerJob.name then
                return true, id
            end
        end
    end

    return false
end
exports("IsPlayerMechanic", IsPlayerMechanic)

--- Retrieves the highest boss grade for a given shop.
---@param shopId number The ID of the shop to check.
---@return number|nil bossGrade Highest boss grade found, or nil if none exist.
function GetBossGrade(shopId)
    local shopData = Shops and Shops[shopId]
    if not shopData or not shopData.job or not shopData.job.grades then
        return nil -- Invalid shop or no job data
    end
    
    local highestBossGrade = nil
    local highestGrade = nil

    for _, v in pairs(shopData.job.grades) do
        if v.isboss then
            if not highestBossGrade or v.grade > highestBossGrade then
                highestBossGrade = v.grade
            end
        end
        if not highestGrade or v.grade > highestGrade then
            highestGrade = v.grade
        end
    end

    return highestBossGrade or highestGrade or nil
end

--- Checks if the local player's job is a boss-grade mechanic in a specific shop or any shop.
---@param shopId number|nil (Optional) The ID of the shop to check. If nil, checks all shops.
---@return boolean isBoss True if the player has a boss mechanic job, false otherwise.
---@return number|nil foundShopId The ID of the shop where the player is a boss, or nil if not found.
function IsPlayerMechanicBoss(shopId)
    if not Shops or not _API.Player then return false end

    local playerJob = _API.Player:GetJob()
    if not playerJob or not playerJob.name then return false end

    if shopId then
        -- Check only the specified shop
        local shopData = Shops[shopId]
        if shopData and shopData.job and shopData.job.name == playerJob.name then
            local bossGrade = GetBossGrade(shopId)
            if bossGrade and playerJob.grade >= bossGrade then
                return true, shopId
            end
        end
    else
        -- Check all shops
        for id, shopData in pairs(Shops) do
            if shopData.job and shopData.job.name == playerJob.name then
                local bossGrade = GetBossGrade(shopId)
                if bossGrade and playerJob.grade >= bossGrade then
                    return true, id
                end
            end
        end
    end

    return false
end
exports("IsPlayerMechanicBoss", IsPlayerMechanicBoss)

--- Checks if the local player is the owner of a specific shop or any shop.
---@param shopId number|nil (Optional) The ID of the shop to check. If nil, checks all shops.
---@return boolean isOwner True if the player is the owner, false otherwise.
---@return number|nil foundShopId The ID of the shop the player owns, or nil if not found.
function IsPlayerShopOwner(shopId)
    if not Shops or not _API.Player then return false end

    local playerIdentifier = _API.Player.charId
    if not playerIdentifier then return false end

    if shopId then
        -- Check only the specified shop
        local shopData = Shops[shopId]
        if shopData and shopData.owner and shopData.owner == playerIdentifier then
            return true, shopId
        end
    else
        -- Check all shops
        for id, shopData in pairs(Shops) do
            if shopData.owner and shopData.owner == playerIdentifier then
                return true, id
            end
        end
    end

    return false
end
exports("IsPlayerShopOwner", IsPlayerShopOwner)

--- Thread to buy shops that are for_sale
local buyingShop = false
Citizen.CreateThread(function()
    while true do
        local sleep = true

        if Config.Shop.Sale then
            for shopId, shopData in pairs(Shops or {}) do
                if type(shopData.for_sale) == "boolean" and shopData.for_sale == true and type(shopData.sale_price) == "number" then
                    local saleCoords = vector3(shopData.blip.coords.x, shopData.blip.coords.y, shopData.blip.coords.z)
                    if #(coords - saleCoords) <= 2.0 then
                        sleep = false
                        local price = math.groupdigits(shopData.sale_price)
                        Draw3DText(saleCoords.x, saleCoords.y, saleCoords.z, "~r~[E]~s~ Buy Shop | ~g~"..Config.Currency..tostring(price).."~s~")
                        if IsControlJustPressed(0, 38) and not buyingShop then
                            buyingShop = true
                            TriggerServerEvent("t1ger_mechanic:server:buyShop", shopId)
                            Wait(1000)
                            buyingShop = false
                        end
                    end
                end
            end
        end

        if sleep then
            Wait(1000)
        else
            Wait(5)
        end

    end
end)