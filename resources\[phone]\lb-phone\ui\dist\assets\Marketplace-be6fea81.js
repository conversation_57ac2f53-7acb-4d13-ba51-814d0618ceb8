import{u as d,j as r,m as M,a as t,L as i,d as y,C as b,b as O,ax as $,q as C,s as L,P as N,ab as j,a5 as F,b8 as H,r as P,K as B,G as X,a0 as q,F as z,ai as W,x as J,S as G,bN as Q,bR as Z,I as _,y as ee,E as K,A as te,h as ae,k as se,bS as k,f as ne,i as ie,aa as re,b0 as ce,O as U,J as T}from"./index-99e0aeb1.js";import{T as le}from"./Textarea-88ad0a75.js";function oe(){var h;const e=d(E),s=d(N.PhoneNumber),n=()=>{var c,o;if(!(e!=null&&e.title)||!(e!=null&&e.description)||!(e!=null&&e.price)||!(e!=null&&e.attachments)||((c=e==null?void 0:e.attachments)==null?void 0:c.length)===0){let l;switch(!0){case!(e!=null&&e.title):l=i("APPS.MARKETPLACE.ERROR_POPUP.NO_TITLE");break;case!(e!=null&&e.description):l=i("APPS.MARKETPLACE.ERROR_POPUP.NO_DESCRIPTION");break;case!(e!=null&&e.price):l=i("APPS.MARKETPLACE.ERROR_POPUP.NO_PRICE");break;case(!(e!=null&&e.attachments)||((o=e==null?void 0:e.attachments)==null?void 0:o.length)===0):l=i("APPS.MARKETPLACE.ERROR_POPUP.NO_ATTACHMENTS");break;default:l="Unknown error"}setTimeout(()=>{b.PopUp.set({title:i("APPS.MARKETPLACE.ERROR_POPUP.TITLE"),description:l,buttons:[{title:i("APPS.MARKETPLACE.ERROR_POPUP.OK")}]})},250)}else{let l={...e,number:s};C("MarketPlace",{action:"sendPost",data:l},Math.floor(Math.random()*1e3).toString()).then(m=>{if(!m)return L("warning","Failed to send post, no id could be generated");g.set([{...l,id:m},...g.value]),E.reset()})}};return r(M.div,{initial:{opacity:0,y:100},animate:{opacity:1,y:0},exit:{opacity:0,y:100},transition:{duration:.2,ease:"easeInOut"},className:"new-post-container",children:[r("div",{className:"new-post-header",children:[t("div",{}),t("div",{className:"close",children:t("div",{onClick:()=>E.set(null)})}),t("div",{className:"post",onClick:n,children:i("APPS.MARKETPLACE.POST")})]}),r("div",{className:"new-post-body",children:[r("div",{className:"item",children:[t("div",{className:"title",children:i("APPS.MARKETPLACE.PRICE")}),t(y,{type:"number",placeholder:"0",onChange:c=>{if(!c.target.value.match(/^[0-9]*$/))return c.preventDefault();E.patch({price:parseFloat(c.target.value)})}})]}),r("div",{className:"item",children:[t("div",{className:"title",children:i("APPS.MARKETPLACE.TITLE")}),t(y,{type:"text",placeholder:"Title",maxLength:50,onChange:c=>E.patch({title:c.target.value})})]}),r("div",{className:"item",children:[t("div",{className:"title",children:i("APPS.MARKETPLACE.DESCRIPTION")}),t(le,{type:"text",placeholder:"Your post",maxLength:250,rows:5,onChange:c=>E.patch({description:c.target.value})})]}),r("div",{className:"item",children:[r("div",{className:"title",children:[" ",i("APPS.MARKETPLACE.IMAGES")]}),r("div",{className:"images",children:[(e==null?void 0:e.attachments)&&e.attachments.length>0&&e.attachments.map((c,o)=>t("div",{className:"image",style:{backgroundImage:`url(${c})`},onClick:()=>{E.patch({attachments:e.attachments.filter((l,m)=>m!==o)})}},o)),(!(e!=null&&e.attachments)||((h=e==null?void 0:e.attachments)==null?void 0:h.length)<=3)&&t("div",{className:"image",onClick:()=>{var c,o,l;b.Gallery.set({allowExternal:(l=(o=(c=O)==null?void 0:c.value)==null?void 0:o.AllowExternal)==null?void 0:l.MarketPlace,onSelect:m=>E.patch({attachments:[...(e==null?void 0:e.attachments)??[],m.src]})})},children:t($,{})})]})]})]})]})}function de({post:e}){const s=d(O),n=d(N.PhoneNumber),h=d(D);return t(M.div,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},exit:{opacity:0,scale:.9},viewport:{once:!0},transition:{duration:.2,ease:"easeInOut"},className:"post-item",onClick:()=>A.set(e),children:r("div",{className:"post-info",children:[r("div",{className:"post-header",children:[t("div",{className:"title",children:e.title}),t("div",{className:"date",children:Y(e.timestamp)})]}),r("div",{className:"post-content",children:[r("div",{className:"post-text",children:[t("div",{className:"description",children:j(e.description)}),t("div",{className:"price",children:s==null?void 0:s.CurrencyFormat.replace("%s",e.price.toLocaleString())})]}),t("div",{className:"attachment",children:t(F,{src:e.attachments[0],blur:e.number!==n})})]}),(n===e.number||h)&&t("div",{className:"post-footer",children:t(H,{className:"red",onClick:c=>{c.stopPropagation(),V(e.id)}})})]})})}const x={posts:[{id:"1",title:"X80 Proto",description:"X80 Proto, white with red details. Has been driven carefully and is in mint condition",number:"0601232354",attachments:["https://www.digitaltrends.com/wp-content/uploads/2020/08/grotti-x80-proto.jpg"],price:2e6,timestamp:Date.now()-1e3*60*60*2},{id:"2",title:"Banshee",description:"Selling my 2020 model Bravado Banshee, low mileage and in perfect condition. Price is negotiable.",number:"0606643134",attachments:["https://static.wikia.nocookie.net/gtawiki/images/e/ea/Penumbra-GTAV-front-Hao.png","https://static.wikia.nocookie.net/gtawiki/images/e/ea/Penumbra-GTAV-front-Hao.png"],price:74999,timestamp:Date.now()-1e3*60*60*24*2},{id:"3",title:"Sanchez, 2018 Model",description:"Selling my 2018 Sanchez. It has low mileage and is in perfect condition. Price is negotiable.",number:"0651566734",attachments:["https://static.wikia.nocookie.net/gtawiki/images/9/93/Sanchez2-GTAV-front.png","https://img.gta5-mods.com/q95/images/honda-crf-450-livery-sanchez/79a3f3-QlX0LDJLBUGUwml4_mil2w_0_0.jpg"],price:1999,timestamp:Date.now()-1e3*60*60*24*3},{id:"4",title:"Dominator",description:"Selling my 2020 model Dominator, low mileage and in perfect condition.",number:"1234567890",attachments:["https://static.wikia.nocookie.net/gtawiki/images/7/78/Dominator-GTAV-front.png","https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSMLEjCaWEavvnX3kliYe0gwrIGYljDaPb-KA&s"],price:49999,timestamp:Date.now()-1e3*60*60*24*5}]};function me(){d(N.Settings);const e=d(N.PhoneNumber),s=d(I),n=d(g),[h,c]=P.useState([]),[o,l]=P.useState(""),[m,R]=P.useState(""),{handleScroll:u,setPage:f,setFetchedEverything:S}=B({fetchData:a=>C("MarketPlace",{action:"getPosts",from:s==="personal_posts"?e:null,query:(o==null?void 0:o.length)>0?o:null,page:a}),onDataFetched:a=>o.length>0?c([...h,...a]):g.set([...n,...a]),perPage:15});return P.useEffect(()=>{X("MarketPlace")&&(f(0),S(!1),c([]),C("MarketPlace",{action:"getPosts",from:s==="personal_posts"?e:null},x.posts.filter(a=>s==="personal_posts"?a.number===e:!0)).then(a=>{a&&a.length>0?g.set(a):S(!0)}))},[s]),P.useEffect(()=>{f(0),S(!1),c([]),o.length>0&&C("MarketPlace",{action:"getPosts",from:s==="personal_posts"?e:null,query:o},x.posts.filter(a=>{var p;return((p=a.title)==null?void 0:p.toLowerCase().includes(o==null?void 0:o.toLowerCase()))&&(s==="personal_posts"?a.number===e:!0)})).then(a=>{if(!a)return L("warning","Failed to fetch search results");c(a)})},[o]),P.useEffect(()=>{const a=setTimeout(()=>l(m),500);return()=>clearTimeout(a)},[m]),P.useEffect(()=>{c([]),g.set([])},[s]),q("marketPlace:newPost",a=>{a.number!==e&&g.set([a,...n])},{waitUntilService:!0}),r(z,{children:[r("div",{className:"marketplace-header",children:[r("div",{className:"marketplace-header-top",children:[t("div",{className:"title",children:i("APPS.MARKETPLACE.MARKETPLACE")}),t(W,{onClick:()=>E.set({})})]}),t(J,{placeholder:i("APPS.MARKETPLACE.SEARCH"),onChange:a=>R(a.target.value)})]}),t("div",{className:"marketplace-wrapper",children:t(M.div,{...G(s==="home"?"left":"right",s,.2),className:"posts",onScroll:u,children:(o.length>0?h:n).map((a,p)=>t(de,{post:a},a.id))})})]})}const he=[{icon:t(Q,{}),title:i("APPS.MARKETPLACE.HOME"),value:"home"},{icon:t(Z,{}),title:i("APPS.MARKETPLACE.YOUR_POSTS"),value:"personal_posts"}];function ue(){const e=d(I);return t("div",{className:"marketplace-footer",children:he.map((s,n)=>r("div",{className:"item","data-active":e===s.value,onClick:()=>I.set(s.value),children:[s.icon,s.title]},n))})}function pe(){const e=d(A),s=d(O),n=d(N.PhoneNumber),h=d(D),[c,o]=P.useState(!1),l=P.useRef(null),[m,R]=P.useState(0),u={pos:{startLeft:0,startX:0},onMouseDown:a=>{X("MarketPlace")&&(u.pos={startLeft:l.current.scrollLeft,startX:a.clientX},l.current.style.userSelect="none",document.addEventListener("mouseup",u.onMouseUp),document.addEventListener("mousemove",u.onMove))},onMove:a=>{const p=(a.clientX-u.pos.startX)/k();l.current.scrollLeft=u.pos.startLeft-p;const v=l.current.getBoundingClientRect();(v.left*k()-5>a.clientX||a.clientX>v.right*k()-5)&&u.onMouseUp()},onMouseUp:()=>{l.current.style.removeProperty("user-select"),document.removeEventListener("mouseup",u.onMouseUp),document.removeEventListener("mousemove",u.onMove);const a=e.attachments,p=l.current.clientWidth;let v=m;const w=l.current.scrollLeft-u.pos.startLeft;w>p/2&&v<a.length-1?v++:w<-p/2&&v>0&&v--,f(v)}},f=a=>{a<0||a>=e.attachments.length||(l.current.scrollTo({left:a*l.current.offsetWidth,behavior:"smooth"}),R(a))},S=a=>{a&&b.PopUp.set({title:i("APPS.SERVICES.CALL_POPUP.TITLE"),description:i("APPS.SERVICES.CALL_POPUP.DESCRIPTION").format({name:ne(a)}),buttons:[{title:i("APPS.SERVICES.CALL_POPUP.CANCEL")},{title:i("APPS.SERVICES.CALL_POPUP.PROCEED"),cb:()=>{let p=K(a);ie({...p,number:a})}}]})};return r("div",{className:"post-container",children:[t("div",{className:"marketplace-header",children:t("div",{className:"marketplace-header-top",children:r("div",{className:"back",onClick:()=>A.reset(),children:[t(_,{}),i("APPS.MARKETPLACE.HOME")]})})}),t("div",{className:"marketplace-wrapper",children:r("div",{className:"post-data",children:[r("div",{className:"attachments",children:[t("div",{className:"attachments-grid",ref:l,onMouseDown:u.onMouseDown,children:e.attachments&&e.attachments.map((a,p)=>t("div",{className:"image",onClick:()=>o(!0),children:t(F,{src:a,blur:!c&&e.number!==n})},p))}),r("div",{className:"arrows",children:[t("div",{className:"arrow","data-disabled":m===0,children:t(_,{onClick:()=>f(m-1)})}),t("div",{className:"arrow","data-disabled":m===e.attachments.length-1,children:t(ee,{onClick:()=>f(m+1)})})]}),t("div",{className:"count",children:i("APPS.MARKETPLACE.AMOUNT_OF_TOTAL").format({amount:m+1,total:e.attachments.length})})]}),r("div",{className:"post-content",children:[t("div",{className:"date",children:Y(e.timestamp)}),t("div",{className:"title",children:e.title}),t("div",{className:"price",children:s.CurrencyFormat.replace("%s",e.price.toLocaleString())}),t("div",{className:"description",children:e.description}),r("div",{className:"buttons",children:[r("div",{className:"button green",onClick:()=>{let a=K(e.number);te.patch({active:{name:"Messages",data:{number:e.number,name:a==null?void 0:a.name,avatar:a==null?void 0:a.avatar,view:"messages"}}})},children:[t(ae,{}),i("APPS.MARKETPLACE.MESSAGE")]}),r("div",{className:"button blue",onClick:()=>S(e.number),children:[t(se,{}),i("APPS.MARKETPLACE.CALL")]}),(n===e.number||h)&&r("div",{className:"button red",onClick:()=>V(e.id),children:[t(H,{}),i("APPS.MARKETPLACE.REMOVE")]})]})]})]})})]})}const I=T("home"),D=T(!1),g=T([]),A=T(null),E=T(null);function ge(){const e=d(A),s=d(E),n=d(N.Styles.TextColor);return P.useEffect(()=>{C("isAdmin").then(h=>D.set(h))},[]),r("div",{className:"marketplace-container",children:[re()?r(M.div,{...G(e?"right":"left",e?"post":"home",.2),className:"marketplace-canister",children:[t(me,{}),t(U,{children:e&&t(pe,{})}),t(ue,{})]}):t("div",{className:"loading",children:t(ce,{size:40,lineWeight:5,speed:2,color:n})}),t(U,{children:s&&t(oe,{})})]})}const V=e=>{if(!e)return L("error","No id provided to delete post");b.PopUp.set({title:i("APPS.MARKETPLACE.DELETE_POPUP.TITLE"),description:i("APPS.MARKETPLACE.DELETE_POPUP.TEXT"),buttons:[{title:i("APPS.MARKETPLACE.DELETE_POPUP.CANCEL")},{title:i("APPS.MARKETPLACE.DELETE_POPUP.PROCEED"),color:"red",cb:()=>{C("MarketPlace",{action:"deletePost",id:e},!0).then(s=>{if(!s)return L("error","Failed to delete post");g.set(g.value.filter(n=>n.id!==e)),A!=null&&A.value&&A.reset()})}}]})},Y=e=>{const s=new Date,n=new Date(e);if(s.getFullYear()===n.getFullYear()&&s.getMonth()===n.getMonth()){let h=n.getHours()<10?`0${n.getHours()}`:n.getHours(),c=n.getMinutes()<10?`0${n.getMinutes()}`:n.getMinutes();return s.getDate()===n.getDate()?i("MISC.TODAY_TIME").format({hours:h,minutes:c}):s.getDate()-1===n.getDate()?i("MISC.YESTERDAY_TIME").format({hours:h,minutes:c}):`${n.toLocaleString("default",{month:"long"})} ${n.getDate()}th, ${n.getFullYear()}`}};export{A as ActivePost,D as IsAdmin,E as NewPost,g as Posts,I as View,ge as default,V as deletePost,Y as formatDate};
