CarLiftHandles = {} -- Global variable to store CarLiftHandle objects

local function CreateCarLift()
    -- request model:
    lib.requestModel(Config.CarLift.Model.base)

    -- get pos & rot:
    local position = GetEntityCoords(player)
    local rotation = GetEntityRotation(player)

    -- create object:
    local baseObject = CreateObject(Config.CarLift.Model.base, position.x, position.y, position.z, false, false, false)
    SetModelAsNoLongerNeeded(baseObject)

    -- freeze & set rotation:
    FreezeEntityPosition(baseObject, true)
    PlaceObjectOnGroundProperly(baseObject)
    SetEntityRotation(baseObject, rotation.x, rotation.y, rotation.z)

    -- get updated coords & rotation:
    local lift_coords = GetEntityCoords(baseObject)
    local lift_rotation = GetEntityRotation(baseObject)

    -- cleanup:
    DeleteEntity(baseObject)

    -- create/sync:
    TriggerServerEvent("t1ger_mechanic:server:createCarLift", lift_coords, lift_rotation)
end
exports("CreateCarLift", CreateCarLift)

-- Event to load car lifts:
RegisterNetEvent("t1ger_mechanic:client:loadCarLifts", function(carlifts)
    if type(carlifts) == "table" and next(carlifts) then
        -- loop through carlifts:
        for id, carlift in pairs(carlifts) do
            if carlift.baseNetId then
                -- instanciate CarLiftHandle object and store in CarLiftHandles table
                CarLiftHandles[tostring(carlift.baseNetId)] = CarLiftHandle:New(id, carlift)
            end
        end
    end
end)

-- Statebag for created car lift:
AddStateBagChangeHandler("t1ger_mechanic:carLiftCreated" --[[key filter]], nil --[[bag filter]], function(bagName, key, value, _unused, replicated)
    if not value then return end
    if replicated then return end

    -- check the entity:
    local lift_base = GetEntityFromStateBagName(bagName)
    if lift_base == 0 then return end

    -- get netId:
    local netId = tonumber(bagName:gsub("entity:", ""), 10)

    -- get arm entity:
    local lift_arm = NetworkGetEntityFromNetworkId(value.armNetId)

    -- set rotation:
    SetEntityRotation(lift_base, value.rotation.x, value.rotation.y, value.rotation.z)
    SetEntityRotation(lift_arm, value.rotation.x, value.rotation.y, value.rotation.z)

    --validate base net id:
    if value.baseNetId ~= netId then
        value.baseNetId = netId
    end

    -- print:
    if Config.Debug then
        print("base entity: ", lift_base, "netId: ", value.baseNetId, "arm entity: ", NetworkGetEntityFromNetworkId(value.armNetId), "netId: ", value.armNetId)
    end

    -- instanciate CarLiftHandle object and store in CarLiftHandles table
    CarLiftHandles[tostring(value.baseNetId)] = CarLiftHandle:New(value.id, value)
end)

-- Event to delete a car lift
RegisterNetEvent("t1ger_mechanic:client:carLiftDeleted", function(netId)
    -- Remove from CarLifts table
    if CarLiftHandles[tostring(netId)] then

        -- check if arms exists:
        local arm = NetworkGetEntityFromNetworkId(CarLiftHandles[tostring(netId)].armNetId)
        if arm and DoesEntityExist(arm) then 
            DeleteEntity(arm)
        end

        -- check if base exists:
        local base = NetworkGetEntityFromNetworkId(CarLiftHandles[tostring(netId)].baseNetId)
        if base and DoesEntityExist(base) then 
            DeleteEntity(base)
        end

        -- Remove from table
        CarLiftHandles[tostring(netId)] = nil
    end
end)

-- Command to create a carlift:
RegisterCommand(Config.CarLift.Command, function(source, args, rawCommand)
    if _API.Player.isAdmin then
        CreateCarLift()
    else
        print("Only admins can use this command!")
    end
end, false)

---Animates the carlift with up, down or stop 
---@param entity number carlift arms entity handle
---@param id number carlift id
---@param method string animation method (up, down or stop)
local function AnimateLift(entity, id, netId, method)
    local entityCoords = GetEntityCoords(entity)
    -- get vehicle for physics:
    local vehicle, vehicleCoords = lib.getClosestVehicle(GetEntityCoords(entity), 3.0, false)
    -- enable physics?
    local physics = false
    if vehicle ~= nil and DoesEntityExist(vehicle) then
        physics = true
    end
    -- animate lift arms:
    for i = 1, 600, 1 do
        -- activate physics:
        if physics then 
            ActivatePhysics(vehicle)
        end
        -- if lift stopped:
        if CarLiftHandles[tostring(netId)].stop == true then
            CarLiftHandles[tostring(netId)].stop = false
            break
        end
        -- get height:
        local height = GetEntityHeightAboveGround(entity)
        -- animate lift:
        if method == "up" then
            if height >= 2.1 then
                break
            end
            entityCoords = GetEntityCoords(entity)
            SetEntityCoords(entity, entityCoords.x, entityCoords.y, entityCoords.z + 0.005, 0.0, 0.0, 0.0, false)
        elseif method == "down" then
            if height <= 0.005 then
                break
            end
            entityCoords = GetEntityCoords(entity)
            SetEntityCoords(entity, entityCoords.x, entityCoords.y, entityCoords.z - 0.005, 0.0, 0.0, 0.0, false)
        elseif method == "stop" then
            CarLiftHandles[tostring(netId)].stop = true
            break
        end
        Wait(10)
    end
end

-- Statebag for animation/handle car lift:
AddStateBagChangeHandler("t1ger_mechanic:carLiftControl" --[[key filter]], nil --[[bag filter]], function(bagName, key, value, _unused, replicated)
    -- we only want to cause an explosion when the value is set to true!
    if not value then return end
    if replicated then return end
    local entity = GetEntityFromStateBagName(bagName)
    -- the entity didn"t exist
    if entity == 0 then return end
    AnimateLift(entity, value.id, value.netId, value.method)
end)

--- Checks if player is near a car lift
--- @param entity number entity handle
--- @return boolean IsNearCarLift `true` if near carlift. `false` otherwise
local function IsNearCarLift(entity)
    if IsEntityValid(entity) and GetEntityType(entity) ~= 0 then 
        if GetEntityModel(entity) == Config.CarLift.Model.base then
            if not IsPedInAnyVehicle(player, false) and not IsPedInAnyVehicle(player, true) then
                if IsPlayerMechanic() then 
                    return true
                end
            end
        end
    end
    return false
end

--- Checks if admin-player is near a car lift
--- @param entity number entity handle
--- @return boolean IsNearCarLift `true` if near carlift. `false` otherwise
local function IsAdminNearCarLift(entity)
    if IsEntityValid(entity) and GetEntityType(entity) ~= 0 then 
        if GetEntityModel(entity) == Config.CarLift.Model.base then
            if not IsPedInAnyVehicle(player, false) and not IsPedInAnyVehicle(player, true) then
                if _API.Player.isAdmin then 
                    return true
                end
            end
        end
    end
    return false
end

-- Creates target options for car lift handles
CreateThread(function()
    while not _Target do Wait(100) end -- wait for target to initialize

    --- carlift target labels and icons
    local carlift_target = {
        ["up"] = {label = locale("target.carlift_up"), icon = Config.CarLift.Icons["up"]},
        ["down"] = {label = locale("target.carlift_down"), icon = Config.CarLift.Icons["down"]},
        ["stop"] = {label = locale("target.carlift_stop"), icon = Config.CarLift.Icons["stop"]},
        ["delete"] = {label = locale("target.carlift_delete"), icon = Config.CarLift.Icons["delete"]},
    }

    local target_options = {}
    local optionsOrder = {"up", "down", "stop", "delete"}

    -- create options:
    for i = 1, #optionsOrder, 1 do
        local method = optionsOrder[i]
        target_options[#target_options+1] = {
            name = "t1ger_mechanic:carlift:"..method,
            icon = carlift_target[method].icon,
            label = carlift_target[method].label,
            canInteract = method == "delete" and IsAdminNearCarLift or IsNearCarLift,
            distance = 2.0,
            onSelect = function(entity)
                local netId = NetworkGetNetworkIdFromEntity(entity)
                if CarLiftHandles[tostring(netId)] then
                    if method == "delete" then
                        CarLiftHandles[tostring(netId)]:Delete()
                    else
                        CarLiftHandles[tostring(netId)]:Control(method)
                    end
                else
                    if Config.Debug then
                        print("no data on this lift")
                    end
                end
                
            end
        }
    end

    -- add target:
    _API.Target.AddGlobalObject(target_options)
end)