local towing = {inUse = false, truck = nil, target = nil}

---Returns whether the vehicle is a valid tow truck or not
---@param towtruck entity entity handle for the vehicle to check
---@return boolean IsVehicleTowTruck `true` if vehicle is a towtruck. `false` otherwise
local function IsVehicleTowTruck(towtruck)
	-- check if entity exists
	if not DoesEntityExist(towtruck) then return false end

	-- check if entity is a vehicle
	if not IsEntityAVehicle(towtruck) then return false end

	-- loop config
	for model, data in pairs(Config.FlatbedTowing.Trucks) do
		if GetHashKey(model) == GetEntityModel(towtruck) then
			return true
		end
	end

	return false
end
exports("IsVehicleTowTruck", IsVehicleTowTruck)

---Returns data for a given towtruck specified in config
---@param towtruck entity entity handle for the vehicle to check
---@return table data returns offset and bonename for the flatbed truck
local function GetTowTruckData(towtruck)
	for model, data in pairs(Config.FlatbedTowing.Trucks) do
		if Get<PERSON><PERSON><PERSON><PERSON>(model) == GetEntityModel(towtruck) then
			return data
		end
	end
	return
end

---Returns whether the vehicle is blacklisted from being attached to the towtruck
---@param vehicle entity entity handle for the vehicle to check
---@return boolean IsBlacklisted `true` if vehicle is blacklisted. `false` otherwise
local function VehicleIsBlacklistedForTowing(vehicle)
	for k,v in pairs(Config.FlatbedTowing.Blacklisted) do
		if GetHashKey(k) == GetEntityModel(vehicle) then
			return true
		end
	end
	return false
end

---Returns the closest towtruck vehicle
---@param dist number (optional) distance to check for closest towtruck, otherwise defaults to 4.0
---@return entity towtruck the entity handle for the towtruck
local function GetClosestTowTruck(dist)
	-- get vehicle ped is in
	local towtruck = GetVehiclePedIsIn(player, false)

	-- if vehicle not found, get closest vehicle to player coords:
	if not towtruck or towtruck == 0 then
		towtruck, towtruckCoords = lib.getClosestVehicle(coords, dist or 4.0, false)
	end

	-- check if vehicle is a valid tow truck model
	if not IsVehicleTowTruck(towtruck) then return end

	-- return the found tow truck entity
	return towtruck
end

---Tow function to attach a vehicle onto a flatbed/towtruck
function TowVehicle()
	if not Config.FlatbedTowing.Enable then return end
	
    -- check if isMechanic:
    local isMechanic, shopId = IsPlayerMechanic()
    if not isMechanic then return end

	-- get towtruck:
	local towtruck = GetClosestTowTruck(4.0)
	if not towtruck then
		return _API.ShowNotification(locale("notification.no_towtruck_nearby"), "inform", {})
	end

	-- assign attributes
	towing.truck = towtruck
	towing.model = GetEntityModel(towtruck)
	towing.inUse = true

	_API.ShowNotification(locale("notification.towtruck_ready_to_control"), "success", {})

	while towing.inUse do
		Wait(1)

		local sleep = true

		local min, max = GetModelDimensions(towing.model)
		local attachCoords = GetOffsetFromEntityInWorldCoords(towtruck, 0.0, (min.y - 3.0), 0.0)
		local truckCoords = GetOffsetFromEntityInWorldCoords(towtruck, (min.x-0.05), (min.y + 1.0), 0.0)
		local truckDist = #(coords - vector3(truckCoords.x, truckCoords.y, truckCoords.z))

		local direction = vector3(0.0, 0.0, 0.0)
		local rotation = vector3(0.0, 0.0, 0.0)
		local mk = Config.FlatbedTowing.Marker

		if truckDist < 10.0 then
			sleep = false 
			if not towing.vehicle then
				local attachDist = #(coords - vector3(attachCoords.x, attachCoords.y, attachCoords.z))
				if attachDist < 10.0 then
					DrawMarker(
						mk.type, attachCoords.x, attachCoords.y, attachCoords.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, mk.scale.x, mk.scale.y, mk.scale.z, mk.color.r, mk.color.g, mk.color.b, mk.color.a, false, true, 2, false, false, false, false
					)
				end
				if truckDist < 1.5 then
					Draw3DText(truckCoords.x, truckCoords.y, truckCoords.z, locale("drawtext.tow_attach"))
					if IsControlJustPressed(0, Config.FlatbedTowing.Keybind) then
						towing.vehicle = AttachVehicle(attachCoords)
						if towing.vehicle then
							towing.inUse = false
						end
					end
				end
			else
				if truckDist < 1.5 then
					Draw3DText(truckCoords.x, truckCoords.y, truckCoords.z, locale("drawtext.tow_detach"))
					if IsControlJustPressed(0, Config.FlatbedTowing.Keybind) then
						DetachEntity(towing.vehicle)
						SetEntityCoords(towing.vehicle, attachCoords.x, attachCoords.y, attachCoords.z, 1, 0, 0, 1)
						SetVehicleOnGroundProperly(towing.vehicle)
						towing.vehicle = nil
						towing.inUse = false
					end
				end
			end
		end

		-- cancel if distance too big
		if truckDist > 20.0 then
			towing.inUse = false
		end

		-- sleep
		if sleep then 
			Wait(500)
		end
	end
end

---Attaches a vehicle onto the flatbed / tow truck
---@param attachCoords vector3 coords to check for vehicle to be attached
---@return vehicle entity returns the attached vehicle entity
function AttachVehicle(attachCoords)
	if not towing.truck or not IsVehicleTowTruck(towing.truck) then
		return print("could not find towtruck. Repeat process!")
	end

	if towing.vehicle then
		return print("flatbed busy")
	end
	
	-- get vehicle to attach:
	local vehicle, vehicleDist = lib.getClosestVehicle(attachCoords, 2.0, false)
	if not vehicle or not DoesEntityExist(vehicle) or not IsEntityAVehicle(vehicle) then
		return _API.ShowNotification(locale("notification.towtruck_park_vehicle_in_marker"), "inform", {})
	end

	-- check if vehicle is blacklisted:
	if VehicleIsBlacklistedForTowing(vehicle) then
		return _API.ShowNotification(locale("notification.towing_vehicle_blacklisted"), "inform", {})
	end

	-- get tow truck data:
	local truckData = GetTowTruckData(towing.truck)
	if not truckData then
		return error("[AttachVehicle] Configuration invalid for the towtruck")
	end

	-- get boneIndex:
	local boneIndex = GetEntityBoneIndexByName(towing.truck, truckData.boneName)
	if not boneIndex or boneIndex == -1 then
		return error("[AttachVehicle] Invalid boneIndex from given boneName: "..truckData.boneName)
	end

	-- attach vehicle to towtruck:
	AttachEntityToEntity(vehicle, towing.truck, boneIndex, truckData.offset.x, truckData.offset.y, truckData.offset.z, 0, 0, 0, 1, 1, 0, 1, 0, 1)

	-- return attached vehicle
	return vehicle
end

--- Command to trigger towing feature
if type(Config.FlatbedTowing.Enable) == "boolean" and Config.FlatbedTowing.Enable == true then
    RegisterCommand(Config.FlatbedTowing.Command, function(source, args)
        if towing.inUse == false then 
            TowVehicle()
        else
            towing.inUse = false
        end
    end, false)
end