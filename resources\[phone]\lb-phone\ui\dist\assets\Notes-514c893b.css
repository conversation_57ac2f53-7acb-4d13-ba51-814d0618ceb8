:root{--phone-color-primary: rgb(255, 255, 255);--phone-color-opacity: rgba(242, 242, 242, .4);--phone-color-opacity2: rgb(30, 30, 30, .5);--phone-color-highlight: rgb(250, 250, 250);--phone-color-highlight2: rgb(240, 240, 240);--phone-color-highlight3: rgb(220, 220, 220);--phone-highlight-opacity15: rgba(145, 145, 145, .15);--phone-highlight-opacity35: rgba(145, 145, 145, .35);--phone-highlight-opacity45: rgba(145, 145, 145, .45);--phone-highlight-opacity55: rgba(145, 145, 145, .55);--phone-color-input: rgba(241, 241, 241, .656);--phone-text-primary: rgb(0, 0, 0);--phone-text-secondary: rgb(142, 142, 147);--phone-color-hover: rgb(240, 240, 240);--phone-color-border: rgba(200, 200, 200, .4);--phone-color-grey: #8e8e93;--phone-color-blue: #0a84ff;--phone-color-green: #32d74b;--phone-color-green-secondary: #092911;--phone-color-red: #ff3b30;--phone-color-orange: rgb(255, 157, 10);--phone-color-yellow: #cca250;--phone-color-pink: #ff3b30;--instagram-primary: #ffffff;--instagram-blue: rgb(0, 149, 246);--instagram-red: rgb(237, 73, 86);--instagram-primary-text: rgb(38, 38, 38);--instagram-secondary-text: rgb(142, 142, 142);--instagram-stroke: rgb(219, 219, 219);--instagram-border: rgb(219, 219, 219);--instagram-highlight: rgb(239, 239, 239);--tinder-color-pink: #ff4573;--tinder-color-orange: #ff5f65;--tinder-color-mix: #f5547c;--twitter-primary: #f5f8fa;--twitter-secondary: #14171a;--twitter-background-highlight: rgb(239, 243, 244);--twitter-primary-text: #14171a;--twitter-secondary-text: #657786;--twitter-alt-text: #657786;--twitter-border: #bdc5cd75;--twitter-border-secondary: #1d9bf0;--twitter-highlight: #1d9bf0;--twitter-hover: rgba(15, 20, 25, .1);--twitter-action: #14171a;--twitter-blue: #1d9bf0;--tiktok-primary: #ffffff;--tiktok-secondary: #000000;--tiktok-text-primary: #000000;--tiktok-text-secondary: #86878b;--tiktok-color-border: #d0d1d3;--tiktok-color-pink: #fe2c55;--tiktok-color-aqua: #00f2ea;--tiktok-color-yellow: #f8cd14;--tiktok-color-blue: #479fc5;--tiktok-color-unread: rgba(254, 44, 86, .2);--crypto-color-primary: rgb(255, 255, 255);--browser-primary: rgb(245, 245, 245);--browser-secondary: rgba(153, 153, 153, .15);--browser-gradient: linear-gradient(230deg, #f4d6ff, #c5f1ff);--browser-footer: rgba(255, 255, 255, .75);--browser-border: rgba(102, 102, 102, .75);--browser-text-secondary: #696969;--controlcentre-opacity: rgba(255, 255, 255, .15);--controlcentre-opacity2: rgba(255, 255, 255, .2);--controlcentre-active: rgba(255, 255, 255, .5);--notification-primary: rgba(215, 215, 215, .5);--notification-secondary: rgba(215, 215, 215, .1);--lockscreeneditor-background: rgba(255, 255, 255, .75);--lockscreeneditor-secondary: #d9d9d9;--app-bg: #ececec;--app-bg2: #ffffff;--app-secondary: #ffffff;--app-secondary2: #ececec;--app-highlight: #cccccc;--app-highlight2: #999999;--app-highlight3: #ffffff;--app-border: #666666;--app-slider: #cccccc;--app-slider-active: #333333;--app-button: #ffffff;--components-bg: #eeeeee;--components-secondary: #ffffff;--components-highlight: #cccccc}[data-theme=dark]{--phone-color-primary: #000000;--phone-color-opacity: rgb(30, 30, 30, .5);--phone-color-opacity2: rgba(242, 242, 242, .4);--phone-color-highlight: rgb(15, 15, 15);--phone-color-highlight2: rgb(20, 20, 20);--phone-color-highlight3: rgb(25, 25, 25);--phone-highlight-opacity35: rgba(145, 145, 145, .35);--phone-highlight-opacity45: rgba(50, 50, 50, .6);--phone-highlight-opacity55: rgb(60, 60, 60, .8);--phone-color-input: rgba(60, 60, 67, .6);--phone-text-primary: #f2f2f7;--phone-text-secondary: #6f6f6f;--phone-color-grey: #636366;--phone-color-hover: rgb(30, 30, 30);--phone-color-border: rgba(150, 150, 150, .2);--phone-color-blue: #076bcf;--instagram-primary: #000000;--instagram-blue: rgb(0, 149, 246);--instagram-red: rgb(237, 73, 86);--instagram-primary-text: rgb(250, 250, 250);--instagram-secondary-text: rgb(142, 142, 142);--instagram-stroke: rgb(219, 219, 219);--instagram-border: rgb(54, 54, 54);--instagram-highlight: rgb(38, 38, 38);--twitter-primary: #000000;--twitter-secondary: #f5f8fa;--twitter-background-highlight: rgb(20, 20, 20);--twitter-primary-text: #f5f8fa;--twitter-secondary-text: #aab8c2;--twitter-alt-text: #657786;--twitter-border: #38444d;--twitter-border-secondary: #38444d;--twitter-hover: rgba(150, 150, 150, .1);--twitter-highlight: #dcdcdc;--twitter-action: #1d9bf0;--twitter-blue: #1d9bf0;--crypto-color-primary: rgb(24, 26, 32);--tiktok-text-primary: #f2f2f7;--tiktok-text-secondary: #6f6f6f;--tiktok-color-border: #96969633;--browser-primary: rgb(15, 15, 15);--browser-secondary: rgba(153, 153, 153, .15);--browser-gradient: linear-gradient(230deg, #453b48, #2f393d);--browser-footer: rgba(51, 51, 51, .75);--browser-border: rgba(102, 102, 102, .75);--browser-text-secondary: #999999;--controlcentre-opacity: rgba(0, 0, 0, .15);--controlcentre-opacity2: rgba(0, 0, 0, .2);--controlcentre-active: rgba(0, 0, 0, .5);--notification-primary: rgba(0, 0, 0, .1);--notification-secondary: rgba(0, 0, 0, .12);--lockscreeneditor-background: rgba(0, 0, 0, .8);--lockscreeneditor-secondary: #333333;--app-bg: #000000;--app-bg2: #000000;--app-secondary: #141414;--app-secondary2: #141414;--app-highlight: #cccccc;--app-highlight2: #696969;--app-highlight3: #212121;--app-border: #cccccc;--app-slider: #999999;--app-slider-active: #ffffff;--app-button: #333333;--components-bg: #000000;--components-secondary: #141414;--components-highlight: #696969}.notes-container{position:relative;height:100%;display:flex;align-items:center;background-color:var(--app-bg)}.notes-container .notes-wrapper{position:relative;height:100%;width:100%;display:flex;flex-direction:column;align-items:center;gap:.5rem}.notes-container .notes-wrapper.note{background-color:var(--app-bg2)}.notes-container .title{font-size:28px;font-weight:500;margin-right:auto;margin-left:2rem;margin-top:4.5rem;color:var(--phone-text-primary)}.notes-container::-webkit-scrollbar{display:none}.notes-container .searchbox{width:84%;margin-bottom:.5rem;background-color:var(--app-secondary);padding:.5rem 1rem;border-radius:.75rem}.notes-container .notes-body{max-height:55rem;width:100%;display:flex;flex-direction:column;align-items:center;overflow-y:scroll;padding-bottom:7rem}.notes-container .notes-body::-webkit-scrollbar{display:none}.notes-container .notes-list{width:80%;display:flex;flex-direction:column;align-items:center;justify-content:center;padding:0rem 1.5rem;border-radius:.75rem;background-color:var(--app-secondary)}.notes-container .notes-list .note-item{width:100%;display:flex;flex-direction:column;gap:.25rem;cursor:pointer;padding:.75rem 0;border-bottom:1px solid var(--phone-color-border)}.notes-container .notes-list .note-item:first-child{padding-top:1rem}.notes-container .notes-list .note-item:last-child{border-bottom:none;padding-bottom:1rem}.notes-container .notes-list .note-item .note-title{font-size:18px;font-weight:500;font-style:bold;color:var(--phone-text-primary)}.notes-container .notes-list .note-item .note-details{display:flex;flex-direction:row;align-items:center;gap:.5rem;color:var(--phone-text-secondary);font-weight:400;font-size:15px}.notes-container .notes-bottom{position:absolute;bottom:0;z-index:2;display:flex;flex-direction:row;justify-content:space-between;align-items:center;padding:1rem 1.5rem 2.5rem;width:90%}.notes-container .notes-bottom .amount{color:var(--phone-text-primary);font-weight:400;font-size:15px;opacity:.8;text-align:center}.notes-container .notes-bottom svg{color:var(--phone-color-blue);cursor:pointer;font-size:28px}.notes-container .notes-bottom svg.hidden{visibility:hidden}.notes-container .top{width:90%;display:flex;flex-direction:row;justify-content:space-between;align-items:center;padding-top:4rem;padding-bottom:.5rem}.notes-container .top>div,.notes-container .top input{flex:1}.notes-container .top .back{display:flex;flex-direction:row;align-items:center;gap:.1rem;color:var(--phone-color-blue);font-size:17px;cursor:pointer}.notes-container .top .back svg{font-size:22px}.notes-container .top .title{text-align:center;width:60%;font-size:19px;font-weight:700;margin:0;background-color:transparent;border:none;color:var(--phone-text-primary)}.notes-container .top .title:active,.notes-container .top .title:focus{outline:none}.notes-container textarea{width:80%;height:78%;display:flex;border:none;font-size:16px;font-family:Roboto;background-color:transparent;resize:none;color:var(--phone-text-primary)}.notes-container textarea::-webkit-scrollbar{display:none}.notes-container textarea:active,.notes-container textarea:focus{outline:none}
