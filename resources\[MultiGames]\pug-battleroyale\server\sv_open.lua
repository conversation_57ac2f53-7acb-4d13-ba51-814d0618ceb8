if Framework == "QBCore" then
	FWork.Functions.CreateUseableItem('pug_vr', function(source, item)
		if  GetResourceState("pug-paintball") == 'started' then
			TriggerClientEvent('Pug:client:ViewBattleGameMenus', source, item)
		else
			TriggerClientEvent('Pug:client:ViewBattleRoyale', source, item)
		end
	end)
else
	FWork.RegisterUsableItem("pug-vr", function(source, item)
		local ItemName = item
		if Config.InventoryType == "qs" then
			ItemName = item.name
		end
		if  GetResourceState("pug-paintball") == 'started' then
			TriggerClientEvent('Pug:client:ViewBattleGameMenus', source, ItemName)
		else
			TriggerClientEvent('Pug:client:ViewBattleRoyale', source, ItemName)
		end
	end)
end

CreateThread(function()
	while GetResourceState("es_extended") ~= 'started' and GetResourceState("qb-core") ~= 'started' do Wait(1000) end
	Wait(1000)
	MySQL.query([[
		CREATE TABLE IF NOT EXISTS `pug_battleroyale_stats` (
			`id` int(11) NOT NULL AUTO_INCREMENT,
			`type` varchar(50) DEFAULT NULL,
			`citizenid` varchar(50) DEFAULT NULL,
			`kills` int(11) DEFAULT 0,
			`deaths` int(11) DEFAULT 0,
			`wins` int(11) DEFAULT 0,
			`losses` int(11) DEFAULT 0,
			`name` varchar(100) DEFAULT NULL,
			PRIMARY KEY (`id`),
			UNIQUE KEY `unique_type_cid` (`type`, `citizenid`)
		) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;
	]])
	MySQL.query([[
		CREATE TABLE IF NOT EXISTS `pug_battleroyale_rankstats` (
			`id` int(11) NOT NULL AUTO_INCREMENT,
			`citizenid` varchar(50) DEFAULT NULL,
			`xp` int(11) DEFAULT 0,
			`unlocks` int(11) DEFAULT 0,
			PRIMARY KEY (`id`),
			UNIQUE KEY `unique_cid` (`citizenid`)
		) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;
	]])
end)


RegisterServerEvent("Pug:server:GiveWeaponBypassBattleRoyale", function(Bool)
    local src = source
    -- exports["fiveguard-resource-name"]:SetTempPermission( -- CHANGE THIS TO YOUR fiveguard-resource-name
    --     src, -- Source
    --     "Client", -- Category
    --     "BypassGiveWeapon", -- Permission
    --     Bool, -- Allow
    --     false -- Ignore static permissions
    -- )
	if Bool then
		 if GetResourceState("Queti") == "started" then
			exports["Queti"]:tempWhitelistPlayer(src, "antiVehicle")
			exports["Queti"]:tempWhitelistPlayer(src, "antiNoclip")
			exports["Queti"]:tempWhitelistPlayer(src, "antiSpectate")
			exports["Queti"]:tempWhitelistPlayer(src, "antiExplosion")
			exports["Queti"]:tempWhitelistPlayer(src, "antiSuperJump")
			exports["Queti"]:tempWhitelistPlayer(src, "antiWeapon")
		 end
	else
	 	if GetResourceState("Queti") == "started" then
			exports["Queti"]:tempUnwhitelistPlayer(src, "antiVehicle")
			exports["Queti"]:tempUnwhitelistPlayer(src, "antiNoclip")
			exports["Queti"]:tempUnwhitelistPlayer(src, "antiSpectate")
			exports["Queti"]:tempUnwhitelistPlayer(src, "antiExplosion")
			exports["Queti"]:tempUnwhitelistPlayer(src, "antiSuperJump")
			exports["Queti"]:tempUnwhitelistPlayer(src, "antiWeapon")
		end
	end
end)

