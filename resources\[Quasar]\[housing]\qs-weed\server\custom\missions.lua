function CreateQuests(source)
    if GetResourceState('qs-inventory') ~= 'started' then
        Debug('qs-inventory not started, skipping weed quest creation.')
        return 
    end

    local quest1 = exports['qs-inventory']:createQuest(source, {
        name = 'plant_weed_home',
        title = 'Green Beginnings',
        description = 'Plant your first weed plant inside your house.',
        reward = 200,
        requiredLevel = 1
    })

    local quest2 = exports['qs-inventory']:createQuest(source, {
        name = 'feed_weed_home',
        title = 'Plant Caretaker',
        description = 'Give nutrition to a weed plant growing in your house.',
        reward = 150,
        requiredLevel = 1
    })

    local quest3 = exports['qs-inventory']:createQuest(source, {
        name = 'harvest_weed_home',
        title = 'First Harvest',
        description = 'Harvest your first batch of home-grown weed.',
        reward = 300,
        requiredLevel = 2
    })

    local quest4 = exports['qs-inventory']:createQuest(source, {
        name = 'remove_dead_plant',
        title = 'Clean Grower',
        description = 'Remove a dead weed plant from your house.',
        reward = 150,
        requiredLevel = 1
    })

    local quest5 = exports['qs-inventory']:createQuest(source, {
        name = 'move_weed_plant',
        title = 'Rearranging Nature',
        description = 'Move a weed plant to a new position inside your house.',
        reward = 150,
        requiredLevel = 1
    })

    Debug('Weed quests assigned to player:', source, {
        plant_weed_home = quest1,
        feed_weed_home = quest2,
        harvest_weed_home = quest3,
        remove_dead_plant = quest4,
        move_weed_plant = quest5
    })
end
