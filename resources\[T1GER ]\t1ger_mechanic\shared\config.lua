Config = {}

Config.Debug = false

Config.Currency = "$" -- currency used for pricing

Config.ServerSideProps = false -- create objects/props on server or client?

--- Mechanic Action Menu
Config.ActionMenu = {
	Enable = true, -- set to false to disable mechanic action menu
	Command = {enable = true, name = "mechanic"}, -- enable/disable command, set command string.
	Keybind = {enable = true, defaultMapping = "F7", description = "Open Mechanic Action Menu"} -- enable/disable keybind, set default mapping (players edit button in-game inside GTA Settings)
}

--- Car Lifts settings
Config.CarLift = {
    Model = {
        base = `t1ger_carlift_base`, -- model for carlift base
        arm = `t1ger_carlift_arms`, -- model for carlift arm
    },
	Command = "createlift", -- command to create a carlift
	Icons = { -- icons for target options on carlift
		["up"] = "fa-sharp fa-solid fa-arrow-up",
		["down"] = "fa-sharp fa-solid fa-arrow-down",
		["stop"] = "fa-regular fa-circle-stop",
		["delete"] = "fa-solid fa-ban"
	}
}

--- Car Jack
Config.CarJack = {
	Item = "t1ger_carjack",
	Marker = {type = 27, scale = vector3{0.35, 0.35, 0.35}, color = {r = 200, g = 100, b = 200, a = 100}},
    Prop = {model = "imp_prop_car_jack_01a", pos = vector3(-0.40, -1.4, -0.8), rot = vector3(5.0, 0.0, -180.0), boneId = 28422},
	Keybind = 38, -- Default [E] | Attaches carjack prop to vehicle
	Icons = {
		target = { -- target icons
			remove = "fa-solid fa-trash",
			handle = "fa-solid fa-gamepad"
		},
		menu = {
			raise = "arrow-up",
			lower = "arrow-down"
		}
	},
	AllowEveryone = false, -- allow everyone with carjack to use on a vehicle?
}

--- Default random values when generating data for unowned vehicles (e.g. NPC vehicles, rental cars, etc.)
Config.RandomVehicleDefaults = {
    MileageRange = {5000, 200000},      -- Min and max total vehicle mileage (in km or mi)
    ServicePartRange = {0.0, 0.9},		-- Min and max percentage of the part's service interval used. Example: 0.9 = 90% of interval used. Values over 1.0 are allowed (e.g., 1.1 = 10% overdue).
    CorePartHealthRange = {65, 100},    -- Min and max health (0–100%) of core parts. Values outside 0-100 will be clamped safely.
}

--- Mileage settings
Config.Mileage = {-- 1:1 Real-World Mileage Tracking! There could be a maximum of 5% difference.
	Metric = true, -- true is kilometers and false is miles
	Unit = 'Mi', -- or 'KM' or 'Mi' or whatever
	Command = {enable = true, name = "mileage"}, -- enable command to view vehicle mileage?
}

--- Associated wear on core parts for service parts that are overdue 
Config.AssociatedWear = { -- This simulates real-world mechanical wear caused by neglected maintenance on service parts.
    Enabled = true,       -- Enable or disable associated core part degradation
    Rate = 0.5            -- % of health lost per second (1000 ms) for each affected core part
}

--- Malfunction Effects Settings
Config.MalfunctionEffects = {
	Enabled = true, -- Enable or disable malfunction effects caused by degraded core parts.
	Interval = 10, -- Delay between each malfunction effect in thread ticks (1 tick = 1 second). For example, 30 means one malfunction effect every 30 seconds (if any degraded core parts exist).
	Notify = true, -- notify with part label and condition when malfunction effects plays?
}

--- Multiplier for service intervals by vehicle class. Lower = more frequent maintenance, higher = less frequent.
--- Applied to Config.ServiceParts[<partName>].interval based on the vehicle's class.
Config.ServiceIntervalMultipliers = {
    [0]  = 1.4,  -- Compacts: lightweight, low-stress vehicles
    [1]  = 1.2,  -- Sedans: everyday use, moderate wear
    [2]  = 1.0,  -- SUVs: heavier, average wear
    [3]  = 1.1,  -- Coupes: generally better maintained, moderate wear
    [4]  = 0.8,  -- Muscle: powerful engines, driven hard = faster wear
    [5]  = 0.7,  -- Sports Classics: older tech, higher maintenance needs
    [6]  = 0.85, -- Sports: performance use, more wear than average
    [7]  = 0.75, -- Super: high-performance, demanding servicing
    [8]  = 1.3,  -- Motorcycles: light use but exposed components
    [9]  = 0.6,  -- Off-road: harsh terrain = rapid degradation
    [10] = 0.5,  -- Industrial: heavy loads, constant strain
    [11] = 0.6,  -- Utility: work vehicles, rugged use
    [12] = 0.9,  -- Vans: often used commercially, moderate wear
    [17] = 0.6,  -- Service: fleet use, high uptime
    [18] = 0.5,  -- Emergency: aggressive use, high stress
    [19] = 0.4,  -- Military: off-road, rugged, heavy-duty
    [20] = 0.5,  -- Commercial: delivery/freight, long hours
    [22] = 0.3   -- Open Wheel: race use, extreme servicing needs
}

--- Blacklist certain vehicle models from being tracked regarding degradation of parts on overdue and collisions
Config.BlacklistTracking = {
	"mesa3",
	"nightshark"
}

--- Disable Automatic Engine Start
Config.DisableAutoEngineStart = true -- If true, disables GTA's automatic engine start behavior when entering a vehicle.

--- Part Conditions
Config.PartConditions = {
    -- Condition percent for service parts (based on used mileage compared with interval)
    service_parts = {
        {percent = 16, label = locale("menu_title.service_ok")},               -- The part has 16% or more of its service interval remaining.
        {percent = 0, label = locale("menu_title.service_due")},               -- The part has between 0% and 15% of its service interval remaining. A service is recommended soon.
        {percent = -math.huge, label = locale("menu_title.service_overdue")}   -- The part has exceeded its service interval (negative remaining mileage), posing a risk of core part damage.
    },

    -- Condition percent for core parts (based on health)
    core_parts = {
        {percent = 51, label = locale("menu_title.part_optimal")},	-- No malfunctions. The part is in good condition, and the vehicle operates normally.
        {percent = 21, label = locale("menu_title.part_worn")},		-- Minor malfunctions occur occasionally. Players will begin noticing small issues.
        {percent = 1, label = locale("menu_title.part_critical")},	-- Frequent and sustained malfunctions. Players will experience major performance issues.
        {percent = 0, label = locale("menu_title.part_failed")}		-- The part has failed completely, causing constant effects or rendering the vehicle undriveable.
    }
}

--- Vehicle Collision Settings
Config.VehicleCollision = {
    Enable = true,       	-- Enable or disable collision-based part degradation
    MinImpactSpeed = 40, 	-- Minimum vehicle speed (in KMH or MPH based on Config.Mileage) required for collision to cause degradation
    CooldownTime = 5000,	-- Cooldown (in milliseconds) between consecutive degradable collisions to avoid rapid triggering
    PartCount = 2,          -- How many core parts to degrade per collision
    MinPercent = 5,         -- Minimum % degradation per part
    MaxPercent = 15         -- Maximum % degradation per part
}

--- Define Electric Vehicles
Config.ElectricVehicles = { -- if using gamebuild 3258+ then script will auto detect electric vehicles a using native
	"surge",
	"buffalo5",
	"caddy",
	"caddy2",
	"caddy3",
	"cyclone",
	"dilettante",
	"dilettante2",
	"iwagen",
	"imorgen",
	"khamelion",
	"neon",
	"omnisegt",
	"raiden",
	"tezeract",
	"voltic",
	"voltic2",
	"virtue"
}

--- Admin Vehicle Reapir
Config.AdminRepair = {
	Enable = true, -- set to false to disable this command
	Command = "fix", -- command name to quick fix a vehicle
	Parts = true, -- repair core_parts and service_parts?
	Refuel = true -- set to fuel to 100.0?
}

--- Repair Kits
Config.RepairKits = {
	Skillcheck = {enable = true, difficulty = {"easy", "easy", "easy"}, inputs = {"w", "a", "s", "d"}}, -- skillcheck settings
	Duration = 3000,

	Types = {
		-- basic repair kit
		basic = {
			item = "t1ger_repairkit",
			engineHealth = 600.0, -- minimum health after repair (clamped 250.0–1000.0)
			repairBody = false, -- repair body health?
		},
	
		-- advanced repair kit
		advanced = {
			item = "t1ger_repairkit_adv",
			engineHealth = 1000.0, -- value to set the engine health to?
			repairBody = true, -- repair body health?
			mechanicOnly = false, -- only allow mechanics to use?
			whitelitedJobs = {"police", "ems", "police2"}, -- always allow these jobs to use this kit. Set to nil or {} to ignore.
		}
	}
}

--- Patch Kit Configuration
Config.PatchKit = {
    Item = "t1ger_patchkit", -- Item name required to use a patch kit
    CorePartHealth = 5.0, -- Sets all failed core parts' health to this value. (Clamped between 1% and 100%)
	ServicePartRemainingMileage = 10.0, -- if overdue, it reset the mileage to 10.0 km/mi before overdue
	Skillcheck = {enable = true, difficulty = {"easy", "easy", "easy"}, inputs = {"w", "a", "s", "d"}}, -- skillcheck settings
	Duration = 4000, -- Repair duration in miliseconds (MS)
}

--- Components (Service- and Core-Parts) related interactions
Config.Components = {
	Repair = {
		targetIcon = "fa-solid fa-hammer", -- icon for target when installing parts
		skillcheck = {enable = true, difficulty = {"easy", "easy", "easy"}, inputs = {"w", "a", "s", "d"}}, -- enable skillcheck before installing part?
		duration = 3000, -- Repair/replace duration in miliseconds (MS)
		cancelCommand = {enable = true, name = "component:cancel"},

		-- setting to return salvaged materials on part replacement
		materialReturn = {
			enable = true, -- Enable or disable material return
			scaleWithCondition = true, -- If true: scale based on condition, if false: random between min/max
			minAmount = 0, -- Random minimum (only if scaleWithCondition = false)
			maxAmount = 5, -- Random maximum (but clamped to recipe material count requirement)
			notify = true, -- Send notification of salvaged/returned materials?
		}
	},

	Diagnostic = {
		item = "t1ger_diagnostictool",
		duration = 4000, -- duration to diagnose in miliseconds (MS)
		icons = { -- menu icons for system overview, core parts and service parts
			system_overview = "fa-tachometer-alt",
			core_parts = "https://i.postimg.cc/X7GXytsj/t1ger-brakes.png",
			service_parts = "https://i.postimg.cc/bNBb7QBJ/t1ger-oilfilter.png",
			start_workflow = "fa-screwdriver-wrench",
		},
		command = {enable = true, name = "diagnose"},
		systemOverview = {
			enable = true, -- display system overview in diagnostic tool menu?
			options = { -- options to show
				engineHealth = {enable = true, label = locale("menu_metadata.diagnostic_engine_health"), icon = "kit-medical", symbol = "%"},
				bodyHealth = {enable = true, label = locale("menu_metadata.diagnostic_body_health"), icon = "car", symbol = "%"},
				tankHealth = {enable = true, label = locale("menu_metadata.diagnostic_tank_health"), icon = "gas-pump", symbol = "%"},
				fuelLevel = {enable = true, label = locale("menu_metadata.diagnostic_fuel_level"), icon = "gauge", symbol = "%"},
				dirtLevel = {enable = true, label = locale("menu_metadata.diagnostic_dirt_level"), icon = "shower", symbol = "%"},
				oilLevel = {enable = true, label = locale("menu_metadata.diagnostic_oil_level"), icon = "oil-can", symbol = "L"},
				engineTemp = {enable = true, label = locale("menu_metadata.diagnostic_engine_temp"), icon = "temperature-high", symbol = "°C"},
				mileage = {enable = true, label = locale("menu_metadata.diagnostic_mileage"), icon = "road", symbol = " "..Config.Mileage.Unit},
				plate = {enable = true, label = locale("menu_metadata.diagnostic_plate"), icon = "id-card", symbol = ""}
			}
		}
	},

    WorkflowTitle = "Workflow - %s", -- workflow title followed by plate
    WorkflowName = "<strong>%s</strong>" -- work flow (body part label)
}

--- Service History
Config.ServiceHistory = {
	Item = "t1ger_servicebook",
	Command = {enable = true, name = "servicebook"}
}

--- Body Repair and Inspection
Config.BodyRepair = {
	TargetIcon = "fa-solid fa-car", -- icon for target when installing parts
	Skillcheck = {enable = true, difficulty = {"easy", "easy", "easy"}, inputs = {"w", "a", "s", "d"}}, -- enable skillcheck before installing part?
	Duration = 3000, -- Repair/replace duration in miliseconds (MS)

	AllowEveryoneForWheelRepair = false, -- allow everyone to use wheel to fix damaged wheels?
	FixEngineHealth = true, -- sets engine health to 1000.0 once body repair is completed?

	Inspect = { -- Inspect vehicle to get damage report and trigger workflow UI
		diagnosticTool = true, -- allow inspection by using diagnostic tool when not inside or at engine?
		command = {enable = true, name = "inspect:start"}, -- command to trigger inspection of body? command name
		target = {enable = true, icon = "fa-solid fa-car"}, -- target to trigger inspection of body?  icon for target option

        marker = {type = 20, scale = {x = 0.35, y = 0.35, z = 0.35}, color = {r = 217, g = 164, b = 61, a = 100}}, -- marker settings
		textUI = {
			icon = "magnifying-glass", -- textUI icon
			position = "right-center", -- textUI position
			style = {borderRadius = 0, backgroundColor = "#d9a43dcc", color = "white"} -- textUI style/css
		},
        keybind = 38, -- [E]
        duration = 2000, -- duration in MS for each inspection point
		cancel = {command = "inspect:cancel"}
	},

	CancelCommand = "body:cancel", -- command to cancel body part installation

    WorkflowTitle = "Workflow - %s", -- workflow title followed by plate
    WorkflowName = "<strong>%s</strong>" -- work flow (body part label)
}

--- Workflow UI
Config.WorkflowUI = {
	Commands = {
		open = "mecworkflow:open",
		close = "mecworkflow:close",
		cursor = "mecworkflow:cursor",
		clear = "mecworkflow:clear"
	},
	CursorKeybind = {
		description = "Toggle Workflow UI Cursor",
		defaultKey = "J"
	},
	DefaultTitle = "Workflow UI", -- default workflow UI title (if u open with no data set)
}

--- Static / NPC repair stations
Config.RepairStations = {
	Blip = {name = "Repair Station", sprite = 402, color = 7, scale = 1.0}, -- blip settings
	Marker = {type = 20, scale = {x = 0.35, y = 0.35, z = 0.35}, color = {r = 162, g = 31, b = 224, a = 100}}, -- draw marker settings
	Keybind = 38, -- Default [E] keybind

	TextUi = {
		icon = "wrench", -- textUi icon
		position = "right-center", -- textUi position
		style = {borderRadius = 0, backgroundColor = "#a21fe080", color = "white"} -- textUi style/css
	},

	Repairs = { -- allowed repairs
		engine = {enable = true, value = 1000.0, duration = 2000, price = 1500, icon = "gauge-high"}, -- allow engine repair? Fixed engine health value?
		body = {enable = true, value = 1000.0, duration = 3000, price = 1000, icon = "car-side"}, -- allow body repair? Fixed body health value?
		core_parts = {enable = true, value = 100.0, duration = 4000, price = 2500, icon = "tools"}, -- allow core_parts repair? Fixed parts value (min = 0.0, max = 100.0)
		service_parts = {enable = true, value = 0, duration = 5000, price = 3000, icon = "wrench"} -- allow service_parts mileage set? Reset mileage on service parts to value greater than 0.
	},

	Mode = "individual", -- "full" or "individual". Individual, select repair types in menu. Full repairs all enabled repair types above.
	FullRepairIcon = "wrench", -- icon in menu for full repair
	FullRepairDuration = 10000, -- repair duration if mode is set to "full"
	FullRepairPrice = 8000, -- repair price if mode is set to "full"

	RestrictWithOnlineMechanics = true, -- if true players cannot use repair station if mechanics are online
	AllowIfMechanicCountBelow = 3, -- allow repair station usage if fewer than 3 mechanics online
	RestrictOnWhitelistedStations = false, -- if false ignore online mechanics on repair stations with jobs defined. If true it checks for online count.

	Locations = { -- repair locations. Coords for the point. Set jobs = nil to allow everyone. Enable/disable blip. Add free = true to allow free repairs.
		[1] = {coords = vector3(433.62, -1018.28, 28.84), jobs = {"police", "police2"}, blip = true, free = true},
		[2] = {coords = vector3(344.39, -553.79, 28.74), jobs = {"ambulance", "ems"}, blip = true, free = true},
		[3] = {coords = vector3(548.18, -206.17, 54.15), jobs = nil, blip = true},
	}
}

--- Flatbed Towing
Config.FlatbedTowing = {
	Enable = true, -- set to false to disable
	Command = "tow", -- command to use tow feature
	ActionMenu = {enable = true, icon = "anchor"}, -- enable from action menu? icon for menu option
	Trucks = { -- supported trucks index by modelname.
		["flatbed"] = {offset = vector3(0.0, -2.0, 0.8), boneName = "bodyshell"},
		-- add more
	},
	Blacklisted = {"flatbed", "towtruck", "cargobob"}, -- vehicles that cannot be attached to flatbed
	Marker = {type = 20, scale = {x = 0.50, y = 0.50, z = 0.50}, color = {r = 240, g = 52, b = 52, a = 100}},
	Keybind = 47, -- default G | Attach or detach vehicle from flatbed/towtruck
}

--- Push Vehicle
Config.PushVehicle = {
	Enable = true, -- set to false to disable
	Command = {enable = true, name = "pushvehicle"}, -- enable command to push vehicle?
	ActionMenu = {enable = true, icon = "hand"}, -- enable from action menu? icon for menu option
	AllowEveryone = false, -- if false only mechanics can push, else everyone can push
	DrawDist = 4.0, -- istance to draw 3d text
	PushKey = 21, --  DEFAULT KEY: [LEFT SHIFT]
	StopKey = 177, --  DEFAULT KEY: [BACKSPACE]
	LeftKey = 34, --  DEFAULT KEY: [A]
	RightKey = 35, --  DEFAULT KEY: [D]
}

--- Flip Vehicle
Config.FlipVehicle = {
	Enable = true, -- set to false to disable
	Command = {enable = true, name = "flipvehicle"}, -- enable command to unlock vehicle?
	ActionMenu = {enable = true, icon = "turn-up"}, -- enable from action menu? icon for menu option
	DrawDist = 4.0, -- istance to draw 3d text
	Keybind = 38, -- DEFAULT KEY: [E]
	Scenario = "CODE_HUMAN_MEDIC_TEND_TO_DEAD",
	Duration = 3000 -- progbar duration
}

--- Unlock Vehicle
Config.UnlockVehicle = {
	Enable = true, -- set to false to disable
	Command = {enable = true, name = "unlockvehicle"}, -- enable command to unlock vehicle?
	ActionMenu = {enable = true, icon = "unlock"}, -- enable from action menu? icon for menu option
	DrawDist = 4.0, -- istance to draw 3d text
	Keybind = 38, -- DEFAULT KEY: [E]
	Anim = {dict = "anim@amb@clubhouse@tutorial@bkr_tut_ig3@", clip = "machinic_loop_mechandplayer", blendIn = 3.0, blendOut = 3.0, flag = 31},
	Duration = 3000 -- progbar duration
}

--- Impound Vehicle
Config.ImpoundVehicle = {
	Enable = true, -- set to false to disable
	Command = {enable = true, str = "impoundvehicle"}, -- enable command to unlock vehicle?
	ActionMenu = {enable = true, icon = "square-parking"}, -- enable from action menu? icon for menu option
	DrawDist = 4.0, -- istance to draw 3d text
	Keybind = 38, -- DEFAULT KEY: [E]
	Scenario = "CODE_HUMAN_MEDIC_TEND_TO_DEAD",
	Duration = 3000 -- progbar duration
}

--- Prop Emotes
Config.Emotes = {
	Keybind = 38, -- keybind to place prop,
	Distance = 2.0, -- distance to interact with prop
	Icons = { -- icons for taget
		remove = "fa-solid fa-trash", -- remove icon
		attach = "fa-solid fa-hand" -- attach (carry/push) icon
	},
	Anim = {
		['push'] = {dict = 'anim@heists@box_carry@', clip = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
		['pickup'] = {dict = 'random@domestic', clip = 'pickup_low', blendIn = 5.0, blendOut = 3.0, duration = -1, flags = 48}
	},
	Props = {
		['-1036807324'] = {label = 'Road Cone', item = "t1ger_roadcone", model = 'prop_roadcone02a', bone = 28422, pos = {0.6,-0.15,-0.1}, rot = {315.0,288.0,0.0}},
		['-1776497660'] = {label = 'Tools Trolley', item = "t1ger_toolstrolley", model = 'prop_cs_trolley_01', push = true, bone = 28422, pos = {-0.1,-0.6,-0.85}, rot = {-180.0,-165.0,90.0}},
		['-1972842851'] = {label = 'Tool Box', item = "t1ger_toolbox", model = 'prop_tool_box_04', bone = 28422, pos = {0.4,-0.1,-0.1}, rot = {315.0,288.0,0.0}},
		['-2146714905'] = {label = 'Con Sign', item = "t1ger_consign", model = 'prop_consign_02a', push = true, bone = 28422, pos = {0.0,0.2,-1.05}, rot = {-195.0,-180.0,180.0}},
		['-205311355'] = {label = 'Road Barrier', item = "t1ger_roadbarrier", model = 'prop_mp_barrier_02b', push = true, bone = 28422, pos = {0.0,0.2,-1.05}, rot = {-195.0,-180.0,180.0}}
	}
}

--- Mechanic NPC Missions / Jobs
Config.Missions = {
	Enable = true, -- set to false to disable built in mechanic missions/jobs
	Command = {enable = true, name = "missions"}, -- enable command to open missions menu?
	ActionMenu = {enable = true, icon = "comment-dots"}, -- enable from action menu? icon for menu option
	CancelIcon = "ban", -- icon menu to cancel job?
	UseAdvancedNotify = true, -- set to false to use standard notification (in case u have HUD blocking etc.)

	Types = {
		breakdown = {
			enable = true, -- enable mission type
			icon = "truck-pickup", -- icon in menu
			travelDistance = 1000.0, -- minimum travel distance from players coords to the "fetched" mission location.
			blip = {label = "Breakdown Mission", sprite = 1, color = 5, scale = 0.7, route = {enable = true, color = 5}}, -- blip settings
		},
		roadsiderepair = {
			enable = true, -- enable mission type
			icon = "screwdriver-wrench", -- icon in menu
			travelDistance = 1000.0, -- minimum travel distance from players coords to the "fetched" mission location.
			blip = {label = "Roadside Repair Mission", sprite = 1, color = 5, scale = 0.7, route = {enable = true, color = 5}}, -- blip settings
			types = {
				fuel = {enable = true, subtitle = "Out of Fuel", item = "t1ger_fuelcan"}, -- enable/disable fuel repair type, subtitle for notify and set required item
				battery = {enable = true, subtitle = "Battery Failure", item = "t1ger_jumpstarter"},  -- enable/disable battery repair type subtitle for notify and set required item
				tire = {enable = true, subtitle = "Flat Tire", item = "t1ger_repairkit_tire"} -- enable/disable tyre repair type subtitle for notify and set required item
			},
			duration = 5000, -- time in ms for anim duration
		},
		carscrapping = {
			enable = true, -- enable mission type
			icon = "recycle", -- icon in menu
			travelDistance = 1000.0, -- minimum travel distance from players coords to the "fetched" mission location.
			blip = {label = "Car Scrapping Mission", sprite = 1, color = 5, scale = 0.7, route = {enable = true, color = 5}}, -- blip settings
			scrapyard = {
				ped = {
					model = "s_m_y_xmech_02_mp", -- scrapyard ped model
					coords = vector4(-465.77,-1707.58,18.8,252.19), -- scrapyard ped coords
				},
				dropoff = vector3(-457.29,-1713.84,18.64), -- vehicle drop off coords
				inspect = vector4(-459.98,-1712.81,18.67,240.04), -- inspect pos and heading
				scenarios = {
					idle = "WORLD_HUMAN_AA_SMOKE", -- ped idle scenario
					inspect = "WORLD_HUMAN_CLIPBOARD" -- ped inspect scenario
				}
			},
			reward = {
				count = 4, -- how many types of random materials to receive, like alu, steel etc.
				amount = {min = 1, max = 5} -- of selected material, how many to receive randomly from min-max
			}
		}
	},

	NotifyIcon = "CHAR_MP_MECHANIC", -- advanced notification icon
	
	RandomPeds = {"s_m_y_dealer_01", "a_m_m_indian_01", "a_m_m_polynesian_01", "a_m_m_prolhost_01", "a_m_m_socenlat_01"}, -- randomized ped models used for the NPC missions
	
	RandomVehicles = {"sultan", "blista", "glendale", "exemplar"}, -- randomized vehicle models used for the NPC missions

	Locations = require("shared/missions") -- Load locations from the shared/missions.lua file index by mission type. DO NOT TOUCH!
}