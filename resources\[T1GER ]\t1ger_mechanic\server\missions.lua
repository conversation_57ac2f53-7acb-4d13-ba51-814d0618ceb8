-- Event to update mission location inUse state
RegisterNetEvent("t1ger_mechanic:server:missionInUse", function(missionType, missionIndex, inUse)
    Config.Missions.Locations[missionType][missionIndex].inUse = inUse
    TriggerClientEvent("t1ger_mechanic:client:missionInUse", -1, missionType, missionIndex, inUse)
end)

-- Event to give mission rewards
RegisterNetEvent("t1ger_mechanic:server:missionPayout", function(missionType, missionIndex, missionCoords)
    local src = source
    
    -- check if missionType is valid:
    if type(missionType) ~= "string" or not Config.Missions.Locations[missionType] then return end

    -- check if missionIndex is valid:
    if type(missionIndex) ~= "number" or not Config.Missions.Locations[missionType][missionIndex] then return end

    -- get player ped & coords:
    local playerPed = GetPlayerPed(src)
    if not playerPed then return end
    local plyCoords = GetEntityCoords(playerPed)

    -- compare coords:
    local compareCoords = nil
    if missionType == "breakdown" then
        compareCoords = Config.Missions.Locations[missionType][missionIndex].dropoff
    elseif missionType == "roadsiderepair" then
        local locationCoords = Config.Missions.Locations[missionType][missionIndex].pos
        compareCoords = vector3(locationCoords.x, locationCoords.y, locationCoords.z)
    else
        return
    end

    -- distance checks:
    if not missionCoords or not compareCoords or #(compareCoords - missionCoords) > 10.0 then return end
    if #(plyCoords - missionCoords) > 20.0 then return end 

    -- add money
    math.randomseed(GetGameTimer())
    local amount = math.random(Config.Missions.Locations[missionType][missionIndex].payout.min, Config.Missions.Locations[missionType][missionIndex].payout.max)
    _API.Player.AddMoney(src, amount)

    -- notification
    _API.SendNotification(src, string.format(locale("notification.mission_cash_reward"), string.format("%s%s", Config.Currency, math.groupdigits(amount))), "success", {})
end)

-- Event to give mission scrap materials rewards
RegisterNetEvent("t1ger_mechanic:server:missionScrap", function(missionType, netId, missionCoords)
    local src = source
    
    -- check if missionType is valid:
    if type(missionType) ~= "string" or not Config.Missions.Locations[missionType] or not Config.Missions.Types[missionType] then return end

    -- check if missionEntity is valid:
    if not netId then return end
    local missionEntity = NetworkGetEntityFromNetworkId(netId)
    if not missionEntity or not DoesEntityExist(missionEntity) then return end
    local missionEntityCoords = GetEntityCoords(missionEntity)

    -- get player ped & coords:
    local playerPed = GetPlayerPed(src)
    if not playerPed then return end
    local plyCoords = GetEntityCoords(playerPed)

    -- compare coords:
    if #(plyCoords - missionEntityCoords) > 10.0 then return end 
    if #(missionEntityCoords - vector3(missionCoords.x, missionCoords.y, missionCoords.z)) > 10.0 then return end

    -- get material and index numerically
    local materials = Config.Materials
    local materialKeys = {}
    for k in pairs(materials) do
        table.insert(materialKeys, k)
    end

    local itemAdded = {}

    -- items to add:
    for i = 1, Config.Missions.Types[missionType].reward.count do
        local index = math.random(#materialKeys)

        -- check if this material was already given
        while itemAdded[index] do
            index = math.random(#materialKeys)
            Wait(1)
        end

        -- randomize amount
        local amount = math.random(Config.Missions.Types[missionType].reward.amount.min, Config.Missions.Types[missionType].reward.amount.max)

        -- add item
        local materialName = materialKeys[index]
        _API.Player.AddItem(src, materialName, amount)
        itemAdded[index] = true
    end

    -- notification
    _API.SendNotification(src, locale("notification.carscrapping_reward"), "success", {})
end)