--- Loads all markers for a given shop.
---@param shopId number The ID of the shop.
function LoadMarkers(shopId)
    if not Shops[shopId] or type(Shops[shopId].markers) ~= "table" then 
        return 
    end

    for markerClass, markers in pairs(Shops[shopId].markers) do
        for markerId, _ in pairs(markers) do
            AddMarker(shopId, markerClass, markerId)
        end
    end
end

--- Loads all markers for all shops.
function LoadAllMarkers()
    if not Shops or not next(Shops) then 
        return 
    end

    for shopId, shopData in pairs(Shops) do
        LoadMarkers(shopData.id)
    end
end

--- Determines whether the local player can view a specific marker in a shop.
---@param shopId number The ID of the shop to check.
---@param class string The type of marker to check (e.g., "boss", "duty", or others).
---@return boolean canView True if the player can view the marker, false otherwise.
local function CanViewMarker(shopId, class)
    if class == "boss" then
        -- Check if player is the shop owner or has a boss-grade mechanic job.
        if IsPlayerShopOwner(shopId) or IsPlayerMechanicBoss(shopId) then 
            return true
        end
    elseif class == "duty" then
        -- Check if player is hired in the shop.
        if IsPlayerEmployee(shopId) then 
            return true
        end
    else
        -- All other markers require a mechanic job in the shop.
        if IsPlayerMechanic(shopId) then
            return true
        end
    end
    return false
end

--- Removes the visuals (oxPoint and blip) for a marker.
---@param shopId number The ID of the shop.
---@param class string The marker class (e.g., "storage", "workbench").
---@param markerId string The unique identifier for the marker.
function RemoveMarker(shopId, class, markerId)
    -- Ensure the shop and marker exist
    if not Shops[shopId] or not Shops[shopId].markers[class] or not Shops[shopId].markers[class][markerId] then return end

    local markerData = Shops[shopId].markers[class][markerId]

    -- Remove oxPoint interaction
    if markerData.oxPoint then
        markerData.oxPoint:remove(markerData.oxPoint)
        markerData.oxPoint = nil
    end

    -- Remove map blip
    if markerData.mapBlip then
        RemoveBlip(markerData.mapBlip)
        markerData.mapBlip = nil
    end
end

--- Creates or updates a marker for a shop.
--- This will first remove the existing marker visuals before creating a new one.
---@param shopId number The ID of the shop.
---@param class string The marker class (e.g., "storage", "workbench").
---@param markerId string The unique identifier for the marker.
function AddMarker(shopId, class, markerId)
    -- Ensure the shop and marker exist
    if not Shops[shopId] or not Shops[shopId].markers[class] or not Shops[shopId].markers[class][markerId] then return end

    -- Get the latest marker data
    local markerData = Shops[shopId].markers[class][markerId]
    if not markerData then return end

    -- Remove old marker visuals before creating the new one
    RemoveMarker(shopId, class, markerId)

    -- Ensure Config exists:
    if not Config.Shop.Markers[class] then
        return error(string.format("marker config for class: '%s' does not exist", class))
    end
    local cfg = Config.Shop.Markers[class]

    -- check if can view/draw/access marker
    -- duty checks if hired in shop. boss checks for owner or highest job grade. rest checks on job
    if not CanViewMarker(shopId, class) then 
        return 
    end

    -- Create map blip
    if markerData.blip.enable then
        markerData.mapBlip = AddBlipForCoord(markerData.coords.x, markerData.coords.y, markerData.coords.z)
        SetBlipSprite(markerData.mapBlip, markerData.blip.sprite or cfg.blip.sprite)
        SetBlipScale(markerData.mapBlip, markerData.blip.scale or cfg.blip.scale)
        SetBlipColour(markerData.mapBlip, markerData.blip.color or cfg.blip.color)
        SetBlipDisplay(markerData.mapBlip, markerData.blip.display or cfg.blip.display)
		SetBlipAsShortRange(markerData.mapBlip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(markerData.name)
        EndTextCommandSetBlipName(markerData.mapBlip)
    end

    local textUI = Config.Shop.MarkerSettings.textUi

    -- Create oxPoint interaction
    markerData.oxPoint = lib.points.new({
        coords = vector3(markerData.coords.x, markerData.coords.y, markerData.coords.z),
        distance = Config.Shop.MarkerSettings.distance,

        onExit = function(point)
            local isOpen, text = lib.isTextUIOpen()
            if isOpen and text == locale("textui."..class.."_marker") then 
                lib.hideTextUI()
            end
        end,

        nearby = function(point)
            -- draw marker:
            DrawMarker(
                markerData.type, point.coords.x, point.coords.y, point.coords.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.35, 0.35, 0.35, markerData.color.r, markerData.color.g, markerData.color.b, markerData.color.a, markerData.bobUpAndDown, markerData.faceCamera, 0, true, false, false, false)
            
            -- distance to interact:
            if point.currentDistance <= 1.0 then
                -- text ui:
                local isOpen, currentText = lib.isTextUIOpen()
                if isOpen and currentText ~= locale("textui."..class.."_marker") then
                    lib.showTextUI(locale("textui."..class.."_marker"), {position = textUI.position, icon = cfg.icon, style = textUI.style})
                else
                    lib.showTextUI(locale("textui."..class.."_marker"), {position = textUI.position, icon = cfg.icon, style = textUI.style})
                end

                -- keybind:
                if IsControlJustReleased(0, Config.Shop.MarkerSettings.keybind) then
                    lib.hideTextUI()
                    print("open marker: ", class, "markerId: ", markerId)
                    if class == "boss" then
                        BossMain(shopId, markerId)
                    elseif class == "duty" then
                        DutyMain(shopId, markerId)
                    elseif class == "garage" then 
                        GarageMain(shopId, markerId)
                    elseif class == "storage" then 
                        StorageMain(shopId, markerId, markerData.stash)
                    elseif class == "workbench" then 
                        WorkbenchMain(shopId, markerId)
                    elseif class == "supplier" then 
                        SupplierMain(shopId, markerId)
                    end
                end
            else
                local isOpen, currentText = lib.isTextUIOpen()
                if isOpen and currentText == locale("textui."..class.."_marker") then 
                    lib.hideTextUI()
                end
            end
        end
    })
end