# Pug Royale.
For any questions please contact me here: here https://discord.gg/jYZuWYjfvq.
For any of my other scripts you can purchase here: https://pug-webstore.tebex.io/category/custom-scripts

# Installation
Move the 'int_arcade' folder found inside of the pug-battleroyale/[ARCADE-MLO] folder into your resources folder and make sure that it is ensured.
Ox_inventory users NEED to put this around the bottom of your server.cfg anywhere-------> setr inventory:weaponmismatch false
Download the island from here https://github.com/FranczeK/cayoperico-island. Credits to #FranczeK
If you are using fiveguard anticheat read the functions RoyalMatchHasBegun() & RoyalMatchHasEnded() in the client/open.lua

--
# If you are using qb-weathersync then put this in qb-weathersync/client/client.lua right below [ local disable = Config.Disabled ] It Should look like this: https://i.imgur.com/D4A3xKq.png
RegisterNetEvent('Pug:client:ToggleWeatherSyncOff', function()
    disable = true
end)
RegisterNetEvent('Pug:client:ToggleWeatherSyncOn', function()
    disable = false
end)

# If you are using cd_easytime put this at the bottom oc cd_easytime/client.lua
RegisterNetEvent('Pug:client:ToggleWeatherSyncOff', function()
    TriggerEvent("cd_easytime:PauseSync", true)
end)
RegisterNetEvent('Pug:client:ToggleWeatherSyncOn', function()
    TriggerEvent("cd_easytime:PauseSync")
end)
--
--
# (OPTIONAL) If you want the med kit and slurp juice to remove the bleeding This goes at the very bottom of qb-ambulancejob/client/wounding.lua
RegisterNetEvent("Pug:Client:StopBleeding", function()
    isBleeding = 0
end)
--

--
# in youR dispatch script, search up shots fired or discharge and add this if statement where you can to remove shots fired called wile in paintball. add "if not exports["pug-battleroyale"]:IsInBattleRoyale() then" to your shots fired call.
# if you are ps dispatch fine the "CEventGunShot" handler in ps-dispatch/client/cl_eventhandlers and replace it with here here:
AddEventHandler('CEventGunShot', function(witnesses, ped)
    if exports["pug-battleroyale"]:IsInBattleRoyale() then return end -- (THIS LINE HERE WAS ADDED)
    if IsPedCurrentWeaponSilenced(cache.ped) then return end
    if inNoDispatchZone then return end
    if BlacklistedWeapon(cache.ped) then return end
        
    WaitTimer('Shooting', function()
        if cache.ped ~= ped then return end

        if PlayerData.job.type == 'leo' then
            if not Config.Debug then
                return
            end
        end

        if inHuntingZone then
            exports['ps-dispatch']:Hunting()
            return
        end

        if witnesses and not isPedAWitness(witnesses, ped) then return end

        if cache.vehicle then
            exports['ps-dispatch']:VehicleShooting()
        else
            exports['ps-dispatch']:Shooting()
        end
    end)
end)
--

--
# IF YOU ARE HAVING AN ISSUE WHERE YOUR GUN GETS PUT AWAY WHEN THE MATCH STARTS AND ARE USING QB-ANTICHEAT FIND THIS LOOP IN QB-ANTICHEAT/CLIENT/MAIN.LUA AND REPLACE IT WITH THIS
CreateThread(function()	-- Check if ped has weapon in inventory --
    while true do
        Wait(5000)

        if LocalPlayer.state.isLoggedIn and not exports["pug-battleroyale"]:IsInBattleRoyale() then

            local PlayerPed = PlayerPedId()
            local player = PlayerId()
            local CurrentWeapon = GetSelectedPedWeapon(PlayerPed)
            local WeaponInformation = QBCore.Shared.Weapons[CurrentWeapon]

            if WeaponInformation ~= nil and WeaponInformation["name"] ~= "weapon_unarmed" then
                QBCore.Functions.TriggerCallback('qb-anticheat:server:HasWeaponInInventory', function(HasWeapon)
                    if not HasWeapon then
                        RemoveAllPedWeapons(PlayerPed, false)
                        TriggerServerEvent("qb-log:server:CreateLog", "anticheat", "Weapon removed!", "orange", "** @everyone " ..GetPlayerName(player).. "** had a weapon on them that they did not have in his inventory. QB Anticheat has removed the weapon.")
                    end
                end, WeaponInformation)
            end
        end
    end
end)
--


# Battle Royale.
For any questions please contact me here: here https://discord.gg/jYZuWYjfvq.
For any of my other scripts you can purchase here: https://pug-webstore.tebex.io/category/custom-scripts

This script is using escrow encryption on only the main client.lua everything else is entirely open including the client/open.lua

PREVIEW HERE: https://youtu.be/aOvNlbYnNrc

CONFIG HERE: https://i.imgur.com/PEj17IN.png
README HERE: https://i.imgur.com/1EyEsNl.png

This completely configurable script consists of:

● Extremely extensive Config you can adjust!
● Players are placed in the games own dimension so they don't interrupt other players who are not playing the Royale in the server.
● 800+ different loot locations around the island. Configurable.
● VR headset option or ped option with a target to join the royal.
● VR headset creates a clone ped at your last position when entering the Royale and deletes when you finish the Royale.
● Loot random spawns as just ammo piles, gun and ammo piles, or just weapon/item piles. Configurable.
● A plane that brings the players in over the island, allowing them to drop where they like with multiple different random entering and exiting paths Configurable.
● UAV Killstreak reward after getting 3 kills.
● A custom ui that tracks players kills, position in the royal with updated player left as well, custom icons, icon that displays when the zone is shrinking or on cooldown.
● The custom ui fits perfectly all screen resolutions and also displays when a player has to reload or pickup loot.
● Random zones that shrink as the game proceeds and when you are outside of them a storm begins slowly killing the player. Configurable.
● A loot system that shoots all the players weapons, ammo, and items out of the player when they die for other players to pickup and loot.
● Loot removal on all players in the Royale being artificially synced when players loot an item from the ground.
● A custom gulag that players get sent to when they die on their first death. If the player wins the 1v1 gulag they get sent back into the match and the loser gets removed.
● A mechanic that makes the player only capable of having two primary weapons. Melee weapons, throwables, and other not primary type weapons do not apply and are configurable to decide on what is considered a primary.
● 6 helicopter spawn locations on the map, but only 3 helicopters spawn at a time. The script uses 3 random cards from 2 different tables to make it seem more random. Configurable.
● 62 vehicle spawn options around the island, but only 31 vehicles spawn at a time. The script uses 31 random cards from 2 different tables to make it seem more random Configurable.
● A mechanic that pickups up ammo and all none primary weapons when walking over them automatically.
● Runs at 0.0 ms resmon when not in a game and 0.03 when in a game.
● This script is completely open source other than the main client.lua.
● 0 known scuff. Was tested over and over with 10+ people to plug every issue that was found.
● Max player count is whatever your server player count is. Configurable.
● Players can choose the zone shrinking down time through the game menu, 6 options to choose from for faster or slower pace games.
● Mechanic to re-pull parachute multiple times after ejecting from the plane.
● A wager amount set to minimum $500 and maximum $25,000. Rearward at the end of the game gives 1st, 2nd, and 3rd a winning cash prized based off the amount of players X the wager X set percentage. Configurable.
● 50 weapon options and 7 different ammo options in the loot table. Configurable.
● 11 different car model options that spawn around the island. You can add as many car options as you like. Configurable.
● Custom sounds that are triggered in the Royale. (Sounds files will provide).
● Unlimited sprint. Configurable.
● The option to spectate players. Configurable.
● 7 unique Royale items that give special advantages
- Vr Headset
- UAV tablet item you can use to display a red dot on on your map that fades in and out for 30 seconds displaying the location of your enemy
- bullet proof heavy armor physical clothing vest that has 100 health and breaks after it receives enough damage
- bullet proof medium armor physical clothing vest that has 50 health and breaks after it receives enough damage
- bullet proof light armor physical clothing vest that has 20 health and breaks after it receives enough damage
- Slurp juice that regenerates health, armor, and gives a short speed boost to the player
- A Super jump item that gives the player a short 30 second super jump ability.

Requirements consist of:
QBCore
qb-menu OR ox_lib (ps-ui or any qb-menu resource name changed will work)
qb-target OR ox_target (any qb-inventory resource name changed will work)
Polyzone