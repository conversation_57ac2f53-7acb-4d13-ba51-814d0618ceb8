Config.Shop = {}

Config.Shop.Job = { -- Job Settings
    defaultGrades = {
        [0] = {grade = 0, name = "recruit", label = "Recruit", salary = 50},
        [1] = {grade = 1, name = "employee", label = "Employee", salary = 75},
        [2] = {grade = 2, name = "boss", label = "Boss", salary = 100},
        -- make sure boss grade is the very last one (higest grade)
    },
    defaultDuty = true, -- only qbcore/qbox
    offDutyPay = false, -- only qbcore/qbox
}

Config.Shop.JobAccount = true -- true = Use JobAccount system for society/shared account | false = Use our integrated account system

Config.Shop.Sale = true -- allow buy shops? And admins to list shop for_sale or cancel sale?

Config.Shop.Admin = {
    menu = { 
		enable = true, -- enable/disable admin mechanic menu?
		command = {enable = true, str = "mechanic:admin"}, -- enable/disable command
		keybind = {enable = true, default = "F10", description = "Mechanic Admin Menu"} -- enable/disable keybind
	},
}

Config.Shop.Creation = {
    command = {enable = true, str = "mechanic:create"}, -- enable/disable command to directly open creator interface?
    blip = { -- blip settings for shop blips
		input = true, -- allow admins to modify sprite & color in shop creator interface?
		sprite = 446, -- default sprite
		color = 5, -- default color
		display = 4, -- default display
		scale = 0.75 -- default scale 
	},
}

Config.Shop.Management = {
    command = {enable = true, str = "mechanic:manage"}, -- enable/disable command for admins to directly open management interface?
    markerEdit = true, -- set to false to disable option in admin management menu to edit marker. 
    markerTeleport = true, -- set to false to disable option in admin management menu to TP to marker.
}

Config.Shop.Storage = {
    slots = 50, -- default slots for created storage/stash
    weight = 100000, -- default weight for created storage/stash
    maxSlots = 500, -- max slots for editing storage/stash
    maxWeight = 999999 -- max weight for editing storage/stash
}

Config.Shop.MarkerSettings = {
    keybind = 38, -- Keybind for interacting with the marker. 
    distance = 5.0, -- distance to load/draw marker
    textUi = { -- UI settings for prompts.
        position = 'right-center',
        style = {borderRadius = 0, backgroundColor = '#48BB78', color = 'white'}
    },
}

Config.Shop.Markers = {
    duty = {
        title = "Duty",
        enable = true, -- Toggle this marker class on/off.
        icon = 'briefcase', -- Icon used for text UI and/or menu.
        blip = {sprite = 280, display = 4, scale = 0.65, color = 0} -- Blip settings.
    },

    boss = {
        title = "Boss",
        enable = true,
        icon = 'user-tie',
        blip = {sprite = 164, display = 4, scale = 0.65, color = 0}
    },

    garage = {
        title = "Garage",
        enable = true,
        icon = 'square-parking',
        blip = {sprite = 357, display = 4, scale = 0.65, color = 0}
    },

    storage = {
        title = "Storage",
        enable = true,
        icon = 'warehouse',
        blip = {sprite = 587, display = 4, scale = 0.65, color = 0}
    },

    workbench = {
        title = "Workbench",
        enable = true,
        icon = 'tools',
        blip = {sprite = 566, display = 4, scale = 0.65, color = 0}
    },

    supplier = {
        title = "Supplier",
        enable = true,
        icon = 'shopping-cart',
        blip = {sprite = 521, display = 4, scale = 0.65, color = 0}
    }
}

Config.Shop.Workbench = {
	enable = true, -- set to false to disable and add your own crafting export/event
	duration = 2500, -- duration in ms for crafting
	anim = {dict = 'mini@repair', clip = 'fixing_a_player', blendIn = 8.0, blendOut = -8.0, flag = 49},
	skillcheck = {enable = true, difficulty = {'easy', 'easy'}, inputs = {'w', 'a', 's', 'd'}},
    maxCraftAmount = 20, -- max allowed amount to craft of selected item.
    categories = require("shared/workbench") -- Load categories from the shared/workbench.lua file
}

Config.Shop.Supplier = {
    receiveInStash = true, -- `true` ordered items delivered to stash. `false` otherwise in player inventory
    playerPay = false, -- `true` player/mechanic pays for items. `false` shop account pays for items
    maxQuantity = 30, -- max quantity of an item that can be ordered.
    catalogue = require("shared/supplier") -- Load catalogue from the shared/supplier.lua file
}

Config.Shop.Billing = {
	enable = true, -- set to false to disable built in billing entirely
	command = {enable = true, name = 'billing'}, -- enable command to open billing interface
	actionMenu = {enable = true, icon = 'file-invoice-dollar'}, -- enable from action menu? icon for menu option
}