import{r as m,j as v,a,O as I,m as L,u as Y,G as B,bO as F,b as g,s as N,q as _,a0 as k,ad as b,v as y,aa as $,b0 as D,F as j,L as t,c6 as M,c7 as H,x as z,c8 as q,c9 as G,a5 as X,C as T,f as W,P as w,A as V}from"./index-99e0aeb1.js";function J({children:e,text:c}){const[h,S]=m.useState(!1);return v("div",{className:"tooltip-wrapper",onMouseEnter:()=>S(!0),onMouseLeave:()=>S(!1),children:[e,a(I,{children:h&&a(L.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3,ease:"easeInOut"},className:"tooltip top",children:a("div",{dangerouslySetInnerHTML:{__html:c}})})})]})}const U=m.createContext(null);function te(){var d;const e=Y(g),c=Y(w.Settings),h=Y(V),[S,O]=m.useState(0),f=Y(w.Styles.TextColor),[R,r]=m.useState([]),[P,A]=m.useState(""),[p,n]=m.useState("0"),[l,u]=m.useState(c==null?void 0:c.streamerMode);m.useEffect(()=>{B("Crypto")&&(F()&&fetch(`https://api.coingecko.com/api/v3/coins/markets?vs_currency=usd&sparkline=true&order=market_cap_desc&per_page=100&page=1&ids=${g.value.Crypto.Coins.join(",")}`).then(i=>i.json()).then(i=>{r(i.map(o=>{var C;return{id:o.id,name:o.name,symbol:o.symbol,image:o.image,current_price:o.current_price,prices:(C=o.sparkline_in_7d)==null?void 0:C.price,change_24h:o.price_change_percentage_24h}}))}).catch(i=>N("error",i)),_("Crypto",{action:"get"}).then(i=>{if(!i)return N("warning","No data received from Crypto NUI event");r(i)}))},[h==null?void 0:h.active]),k("crypto:updateCoins",i=>{if(!i)return N("warning","No data received from the update Crypto NUI event");r(i)}),m.useEffect(()=>O(R.reduce((i,o)=>i+(o.owned??0)*o.current_price,0)),[R]),m.useEffect(()=>{n(l?b("∗".repeat(y(S,2).toString().length)):b(y(S,2).toString()))},[S,l]);const s=i=>{let o={0:50,10:45,12:40,14:35},C=0;for(let E=0;E<Object.keys(o).length;E++)i.length>=parseInt(Object.keys(o)[E])&&(C=o[Object.keys(o)[E]]);return C};return a("div",{className:"crypto-container",children:$()?v(j,{children:[v("div",{className:"crypto-header",children:[a("img",{src:"./assets/img/icons/apps/Cryptopng.png",alt:"btc"}),t("APPS.CRYPTO.TITLE")]}),a(U.Provider,{value:{Coins:[R,r]},children:v("div",{className:"crypto-wrapper",children:[v("div",{className:"balance",children:[v("div",{className:"title",children:[t("APPS.CRYPTO.BALANCE"),a("div",{className:"icon",onClick:()=>u(!l),children:l?a(M,{}):a(H,{})})]}),a("div",{className:"amount",style:{fontSize:s(p.toString())},onClick:()=>u(!l),children:(d=e==null?void 0:e.CurrencyFormat)==null?void 0:d.replace("%s",p.toString())})]}),a("div",{className:"search",children:a(z,{placeholder:"Search Cryptocurrency",onChange:i=>A(i.target.value)})}),a(K,{search:P}),a("div",{className:"credits",children:"Powered by CoinGecko"})]})})]}):a("div",{className:"loading",children:a(D,{size:40,lineWeight:5,speed:2,color:f})})})}const K=({search:e})=>{const[c,h]=m.useState("balance"),[S,O]=m.useState(!0),f=["Name","Graph","Balance"],[R]=m.useContext(U).Coins;return a("div",{className:"holdings",children:v("div",{className:"items",children:[a("div",{className:"item filters",children:f.map((r,P)=>v("div",{className:"filter",onClick:()=>{if(h(r.toLowerCase()),c===r.toLowerCase())return O(!S);O(!0)},children:[r,r.toLowerCase()===c&&S?a(q,{}):a(G,{})]},P))}),R.sort((r,P)=>c==="name"?S?r.name.localeCompare(P.name):P.name.localeCompare(r.name):c==="graph"?S?r.change_24h-P.change_24h:P.change_24h-r.change_24h:c==="balance"?S?(P.owned??0)*P.current_price-(r.owned??0)*r.current_price:(r.owned??0)*r.current_price-(P.owned??0)*P.current_price:0).filter(r=>r.name.toLowerCase().includes(e.toLowerCase())||r.symbol.toLowerCase().includes(e.toLowerCase())).map((r,P)=>a(Q,{data:r},P))]})})},Q=({data:e})=>{const[c,h]=m.useState(!1),[S,O]=m.useState(""),[f,R]=m.useContext(U).Coins,r=(p,n)=>{var l,u;if(n>((u=(l=g.value)==null?void 0:l.CryptoLimit)==null?void 0:u.Buy))return N("warning","Amount is too high, cancelling request (MAX 1,000,000)");n>1?_("Crypto",{action:"buy",coin:p,amount:n}).then(s=>{if(s!=null&&s.success)return R(d=>d.map(i=>i.id===p?{...i,owned:(i.owned??0)+n/i.current_price,value:(i.value??0)+n,invested:(i.invested??0)+n}:i));s!=null&&s.msg&&setTimeout(()=>{T.PopUp.set({title:t("APPS.CRYPTO.ERROR"),description:t(`APPS.CRYPTO.${s.msg}`),buttons:[{title:t("APPS.CRYPTO.CLOSE")}]})},250)}):setTimeout(()=>{T.PopUp.set({title:t("APPS.CRYPTO.ERROR"),description:t("APPS.CRYPTO.ATLEAST_ONE_COIN").format({action:t("APPS.CRYPTO.BUY").toLowerCase(),currency:g.value.CurrencyFormat.replace("%s","")}),buttons:[{title:t("APPS.CRYPTO.CLOSE")}]})},250)},P=(p,n,l)=>{var u,s;if(n>((s=(u=g.value)==null?void 0:u.CryptoLimit)==null?void 0:s.Sell))return N("warning","Amount is too high, cancelling request (MAX 1,000,000)");l>0?_("Crypto",{action:"sell",coin:p,amount:l}).then(d=>{if(d!=null&&d.success)return R(i=>i.map(o=>o.id===p?{...o,owned:(o.owned??0)-n/o.current_price,value:(o.value??0)+n,invested:(o.invested??0)-n}:o));d!=null&&d.msg&&setTimeout(()=>{T.PopUp.set({title:t("APPS.CRYPTO.ERROR"),description:t(`APPS.CRYPTO.${d.msg}`),buttons:[{title:t("APPS.CRYPTO.CLOSE")}]})},250)}):setTimeout(()=>{T.PopUp.set({title:t("APPS.CRYPTO.ERROR"),description:t("APPS.CRYPTO.ATLEAST_ONE_COIN").format({action:t("APPS.CRYPTO.SELL").toLowerCase(),currency:g.value.CurrencyFormat.replace("%s","")}),buttons:[{title:t("APPS.CRYPTO.CLOSE")}]})},250)},A=(p,n,l,u)=>{l>0?_("Crypto",{action:"transfer",coin:p,amount:l,number:u}).then(s=>{s!=null&&s.success&&setTimeout(()=>{T.PopUp.set({title:t("APPS.CRYPTO.SUCCESSFULLY_TRANSFERED_POPUP.TITLE"),description:t("APPS.CRYPTO.SUCCESSFULLY_TRANSFERED_POPUP.DESCRIPTION").format({amount:n,coin:e.symbol,number:W(u)}),buttons:[{title:t("APPS.CRYPTO.CLOSE")}]}),R(d=>d.map(i=>i.id===p?{...i,owned:(i.owned??0)-n/i.current_price,value:(i.value??0)+n,invested:(i.invested??0)-n}:i))},250),s!=null&&s.msg&&setTimeout(()=>{T.PopUp.set({title:t("APPS.CRYPTO.ERROR"),description:t(`APPS.CRYPTO.${s.msg}`),buttons:[{title:t("APPS.CRYPTO.CLOSE")}]})},250)}):setTimeout(()=>{T.PopUp.set({title:t("APPS.CRYPTO.ERROR"),description:t("APPS.CRYPTO.ATLEAST_ONE_COIN").format({action:t("APPS.CRYPTO.TRANSFER").toLowerCase(),currency:g.value.CurrencyFormat.replace("%s","")}),buttons:[{title:t("APPS.CRYPTO.CLOSE")}]})},250)};return e.symbol=e.symbol.toUpperCase(),e.owned=e.owned??0,e.value=e.current_price*(e.owned??0),v("div",{className:"item",onClick:()=>h(!c),children:[v("div",{className:"top",children:[v("div",{className:"info",children:[a(X,{src:e.image,className:"icon",alt:"Icon"}),v("div",{className:"coin",children:[a("div",{className:"name",children:e.symbol}),a("div",{className:"symbol",children:e.name})]})]}),a(Z,{data:e}),v("div",{className:"amount",children:[a("div",{className:"value",children:b(y(e.owned,4))}),v("div",{className:"asset",children:["$",b(y(e.value,2))]})]})]}),a(I,{children:c&&v(L.div,{initial:{height:0},animate:{height:"auto"},exit:{height:0},className:"bottom",children:[a(L.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.1},className:"button fill",onClick:p=>{var n,l,u,s,d,i,o;p.stopPropagation(),T.PopUp.set({title:t("APPS.CRYPTO.BUY_POPUP.TITLE").format({coin:e.symbol}),description:t("APPS.CRYPTO.BUY_POPUP.DESCRIPTION").format({coin:e.symbol}),input:{placeholder:t("APPS.CRYPTO.BUY_POPUP.PLACEHOLDER"),type:"number",value:"0",max:((u=(l=(n=g.value)==null?void 0:n.CryptoLimit)==null?void 0:l.Buy)==null?void 0:u.toString())??"1000000",maxLength:((o=(i=(d=(s=g.value)==null?void 0:s.CryptoLimit)==null?void 0:d.Buy)==null?void 0:i.toString())==null?void 0:o.length)+2,onChange:C=>O(C)},buttons:[{title:t("APPS.CRYPTO.BUY_POPUP.CANCEL")},{title:t("APPS.CRYPTO.BUY_POPUP.PROCEED"),cb:()=>{O(C=>(r(e.id,parseFloat(C)),C))}}]})},children:t("APPS.CRYPTO.BUY")}),a(L.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.1},className:"button",onClick:p=>{var n,l,u,s,d,i,o;p.stopPropagation(),T.PopUp.set({title:t("APPS.CRYPTO.SELL_POPUP.TITLE").format({coin:e.symbol}),description:t("APPS.CRYPTO.SELL_POPUP.DESCRIPTION").format({coin:e.symbol}),input:{placeholder:t("APPS.CRYPTO.BUY_POPUP.PLACEHOLDER"),type:"number",value:"0",max:((u=(l=(n=g.value)==null?void 0:n.CryptoLimit)==null?void 0:l.Sell)==null?void 0:u.toString())??"1000000",maxLength:((o=(i=(d=(s=g.value)==null?void 0:s.CryptoLimit)==null?void 0:d.Sell)==null?void 0:i.toString())==null?void 0:o.length)+2,onChange:C=>O(C)},buttons:[{title:t("APPS.CRYPTO.BUY_POPUP.CANCEL")},{title:t("APPS.CRYPTO.BUY_POPUP.PROCEED"),cb:()=>{O(C=>{let E=parseFloat(C),x=E/e.current_price;return P(e.id,E,x),C})}}]})},children:t("APPS.CRYPTO.SELL")}),a(L.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.1},className:"button blue",onClick:p=>{p.stopPropagation(),T.PopUp.set({title:t("APPS.CRYPTO.TRANSFER_POPUP.TITLE").format({coin:e.symbol}),description:t("APPS.CRYPTO.TRANSFER_POPUP.DESCRIPTION").format({coin:e.symbol}),input:{placeholder:t("APPS.CRYPTO.BUY_POPUP.PLACEHOLDER"),type:"number",value:"0",onChange:n=>O(n)},buttons:[{title:t("APPS.CRYPTO.BUY_POPUP.CANCEL")},{title:t("APPS.CRYPTO.TRANSFER_POPUP.NEXT"),cb:()=>{O(n=>{let l=parseFloat(n),u=l/e.current_price;return T.ContactSelector.set({onSelect:s=>{if(!s)return N("warning","No contact selected? Cancelling request");A(e.id,l,u,s.number)}}),n})}}]})},children:t("APPS.CRYPTO.TRANSFER")}),a(L.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.1},className:"button red",onClick:p=>{p.stopPropagation(),T.PopUp.set({title:t("APPS.CRYPTO.SELL_ALL_POPUP.TITLE").format({coin:e.symbol}),description:t("APPS.CRYPTO.SELL_ALL_POPUP.DESCRIPTION").format({coin:e.symbol,amount:e.owned*e.current_price}),buttons:[{title:t("APPS.CRYPTO.BUY_POPUP.CANCEL")},{title:t("APPS.CRYPTO.BUY_POPUP.PROCEED"),color:"red",cb:()=>{let n=e.owned*e.current_price;P(e.id,n,e.owned)}}]})},children:t("APPS.CRYPTO.SELL_ALL")})]})})]})},Z=e=>{if(!(e!=null&&e.data.prices))return null;let c=e.data.prices;const h=m.useRef(null),[S,O]=m.useState(null),[f,R]=m.useState(null);return c=c.slice(0,24),m.useEffect(()=>{if(!(h!=null&&h.current))return;const r=h.current;r.width=150,r.height=100;const P=r.getContext("2d");P.lineWidth=3;const A=Math.min(...c),n=Math.max(...c)-A,l=r.height-10;let u=0,s=r.height-5-(c[0]-A)/n*l;P.beginPath(),P.moveTo(u,s);const d=c[c.length-1]-c[0]>0?"green":"red";P.strokeStyle=d;for(let C=1;C<c.length;C++)u+=r.width/(c.length-1),s=r.height-5-(c[C]-A)/n*l,P.lineTo(u,s);P.stroke();const i=(c[c.length-1]-c[0])/c[0]*100;i>0?O(`<div style="color: var(--phone-color-green)">+${y(i,2)}%</div>`):i<0?O(`<div style="color: var(--phone-color-red)">${y(i,2)}%</div>`):O(`<div>${y(i,2)}%</div>`);let o=(e.data.value-e.data.invested)/e.data.invested*100;o>0?R(`<div style="color: var(--phone-color-green)">+${y(o,2)}%</div>`):o<0&&R(`<div style="color: var(--phone-color-red)">${y(o,2)}%</div>`)},[h,c]),a(J,{text:`$${y(e.data.current_price,2)} ${S??""} ${f??""}`,children:a("canvas",{ref:h})})};export{te as default};
