Config.BodyParts = {
    door = {
        item = "t1ger_vehicledoor",
        label = "Vehicle Door",
        icon = "https://i.postimg.cc/hvHDrP2S/t1ger-vehicledoor.png",
        prop = {model = "imp_prop_impexp_car_door_04a", pos = vector3(-0.7, -0.2, 0.0), rot = vector3(0.0, -10.0, 90.0), boneId = 28422},
        anim = {dict = "anim@amb@clubhouse@tutorial@bkr_tut_ig3@", clip = "machinic_loop_mechandplayer", duration = -1, blendIn = 2.0, blendOut = 2.0, flag = 1}
    },
    hood = {
        item = "t1ger_vehiclehood",
        label = "Vehicle Hood",
        icon = "https://i.postimg.cc/5yH9LFq7/t1ger-vehiclehood.png",
        prop = {model = "imp_prop_impexp_bonnet_04a", pos = vector3(0.0, 0.20, 0.12), rot = vector3(-20.0, 0.0, 180.0), boneId = 28422},
        anim = {dict = "mini@repair", clip = "fixing_a_player", duration = -1, blendIn = 2.0, blendOut = 2.0, flag = 1}
    },
    trunk = {
        item = "t1ger_vehicletrunk",
        label = "Vehicle Trunk",
        icon = "https://i.postimg.cc/WpK1jXTQ/t1ger-vehicletrunk.png",
        prop = {model = "imp_prop_impexp_trunk_01a", pos = vector3(0.0, 0.15, 0.1), rot = vector3(30.0, 0.0, 0.0), boneId = 28422},
        anim = {dict = "mini@repair", clip = "fixing_a_player", duration = -1, blendIn = 2.0, blendOut = 2.0, flag = 1}
    },
    wheel = {
        item = "t1ger_vehiclewheel",
        label = "Vehicle Wheel",
        icon = "https://i.postimg.cc/15q5zwz4/t1ger-vehiclewheel.png",
        prop = {model = "imp_prop_impexp_tyre_01c", pos = vector3(0.0, -0.1, 0.2), rot = vector3(0.0, 0.0, 0.0), boneId = 28422},
        anim = {dict = "anim@amb@clubhouse@tutorial@bkr_tut_ig3@", clip = "machinic_loop_mechandplayer", duration = -1, blendIn = 2.0, blendOut = 2.0, flag = 1}
    },
    window = {
        item = "t1ger_vehiclewindow",
        label = "Vehicle Window",
        icon = "https://i.postimg.cc/pLMV5r9C/t1ger-vehiclewindow.png",
        prop = {model = "h4_prop_yacht_glass_04", pos = vector3(0.0, -0.1, 0.3), rot = vector3(0.0, 0.0, 0.0), boneId = 28422},
        anim = {dict = "missmechanic", clip = "work_in", duration = -1, blendIn = 2.0, blendOut = 2.0, flag = 1}
    },
}

Config.ServiceParts = {
    air_filter = {
        item = "t1ger_airfilter",
        label = "Air Filter",
        icon = "https://i.postimg.cc/hj1T66kW/t1ger-airfilter.png",
        interval = 8000,
        associated = {"fuel_injector"},
        type = "gas",
        prop = {model = "t1ger_airfilter", pos = vector3(0.0, 0.0, 0.0), rot = vector3(0.0, 120.0, 0.0), boneId = 60309},
    },
    battery_coolant = {
        item = "t1ger_batterycoolant",
        label = "Battery Coolant",
        icon = "https://i.postimg.cc/26DnHrX7/t1ger-batterycoolant.png",
        interval = 20000,
        associated = {"ev_battery"},
        type = "electric",
        prop = {model = "t1ger_batterycoolant", pos = vector3(0.0, 0.0, -0.1), rot = vector3(0.0, 0.0, 25.0), boneId = 28422, small = true},
    },
    brake_fluid = {
        item = "t1ger_brakefluid",
        label = "Brake Fluid",
        icon = "https://i.postimg.cc/jjNzQLvj/t1ger-brakefluid.png",
        interval = 15000,
        associated = {"brakes"},
        type = "shared",
        prop = {model = "t1ger_brakefluid", pos = vector3(-0.03, 0.03, 0.0), rot = vector3(-90.0, -40.0, -150.0), boneId = 60309},
    },
    brake_pad = {
        item = "t1ger_brakepad",
        label = "Brake Pads",
        icon = "https://i.postimg.cc/jSBH7zH9/t1ger-brakepad.png",
        interval = 10000,
        associated = {"brakes"},
        type = "gas",
        prop = {model = "t1ger_brakepad", pos = vector3(0.0, 0.0, 0.0), rot = vector3(-40.0, 0.0, 0.0), boneId = 60309},
    },
    coolant = {
        item = "t1ger_coolant",
        label = "Coolant",
        icon = "https://i.postimg.cc/Y02YH874/t1ger-coolant.png",
        interval = 15000,
        associated = {"radiator"},
        type = "gas",
        prop = {model = "t1ger_coolant", pos = vector3(0.0, 0.0, -0.1), rot = vector3(0.0, 0.0, 25.0), boneId = 28422, small = true},
    },
    drive_belt = {
        item = "t1ger_drivebelt",
        label = "Drive Belt",
        icon = "https://i.postimg.cc/bYL1TX01/t1ger-drivebelt.png",
        interval = 20000,
        associated = {"alternator", "radiator"},
        type = "gas",
        prop = {model = "t1ger_drivebelt", pos = vector3(0.0, 0.0, 0.0), rot = vector3(0.0, 0.0, 0.0), boneId = 60309},
    },
    fuel_filter = {
        item = "t1ger_fuelfilter",
        label = "Fuel Filter",
        icon = "https://i.postimg.cc/3NLpJprt/t1ger-fuelfilter.png",
        interval = 12000,
        associated = {"fuel_injector"},
        type = "gas",
        prop = {model = "t1ger_fuelfilter", pos = vector3(0.0, 0.0, 0.0), rot = vector3(0.0, 120.0, 0.0), boneId = 60309},
    },
    hv_wiring = {
        item = "t1ger_hvwiring",
        label = "High-Voltage Wiring",
        icon = "https://i.postimg.cc/g09hJGdb/t1ger-hvwiring.png",
        interval = 25000,
        associated = {"electric_motor"},
        type = "electric",
        prop = {model = "t1ger_hvwiring", pos = vector3(0.0, 0.0, 0.0), rot = vector3(90.0, 0.0, 0.0), boneId = 60309},
    },
    oil_filter = {
        item = "t1ger_oilfilter",
        label = "Oil + Filter",
        icon = "https://i.postimg.cc/bNBb7QBJ/t1ger-oilfilter.png",
        interval = 5000,
        associated = {"fuel_injector", "transmission"},
        type = "gas",
        prop = {model = "t1ger_oilfilter", pos = vector3(0.0, 0.0, 0.0), rot = vector3(90.0, 0.0, 0.0), boneId = 60309},
    },
    spark_plugs = {
        item = "t1ger_sparkplugs",
        label = "Spark Plugs",
        icon = "https://i.postimg.cc/NfMmCNbS/t1ger-sparkplugs.png",
        interval = 15000,
        associated = {"fuel_injector"},
        type = "gas",
        prop = {model = "t1ger_sparkplugs", pos = vector3(-0.03, 0.0, 0.0), rot = vector3(0.0, 120.0, 0.0), boneId = 60309},
    },
    steering_fluid = {
        item = "t1ger_steeringfluid",
        label = "Power Steering Fluid",
        icon = "https://i.postimg.cc/rwRWMKB9/t1ger-steeringfluid.png",
        interval = 20000,
        associated = {"power_steering_pump"},
        type = "gas",
        prop = {model = "t1ger_steeringfluid", pos = vector3(-0.03, 0.03, 0.0), rot = vector3(-90.0, -40.0, -150.0), boneId = 60309},
    },
    tires = {
        item = "t1ger_tires",
        label = "Tires",
        icon = "https://i.postimg.cc/BvxKPRQL/t1ger-tires.png",
        interval = 50000,
        associated = {},
        type = "shared",
        prop = {model = "t1ger_tires", pos = vector3(0.0, -0.05, 0.0), rot = vector3(0.0, -90.0, -90.0), boneId = 28422},
    },
    transmission_fluid = {
        item = "t1ger_transmissionfluid",
        label = "Transmission Fluid",
        icon = "https://i.postimg.cc/4dsh6wm8/t1ger-transmissionfluid.png",
        interval = 25000,
        associated = {"transmission"},
        type = "gas",
        prop = {model = "t1ger_transmissionfluid", pos = vector3(-0.03, 0.03, 0.0), rot = vector3(-90.0, -40.0, -150.0), boneId = 60309},
    },
}

Config.CoreParts = {
    alternator = {
        item = "t1ger_alternator",
        label = "Alternator",
        icon = "https://i.postimg.cc/CM6z5wHr/t1ger-alternator.png",
        associated = {"drive_belt"},
        type = "gas",
        prop = {model = "t1ger_alternator", pos = vector3(0.0, 0.0, 0.0), rot = vector3(90.0, 120.0, 0.0), boneId = 60309},
    },
    brakes = {
        item = "t1ger_brakes",
        label = "Brakes",
        icon = "https://i.postimg.cc/X7GXytsj/t1ger-brakes.png",
        associated = {"brake_fluid", "brake_pads"},
        type = "shared",
        prop = {model = "t1ger_brakes", pos = vector3(0.0, 0.0, 0.0), rot = vector3(0.0, 0.0, -90.0), boneId = 60309},
    },
    electric_motor = {
        item = "t1ger_electricmotor",
        label = "Electric Motor",
        icon = "https://i.postimg.cc/J0gn2QzC/t1ger-electricmotor.png",
        associated = {"hv_wiring"},
        type = "electric",
        prop = {model = "t1ger_electricmotor", pos = vector3(0.0, 0.0, -0.1), rot = vector3(90.0, 0.0, 0.0), boneId = 60309},
    },
    ev_battery = {
        item = "t1ger_evbattery",
        label = "EV Battery",
        icon = "https://i.postimg.cc/ZKGnPjQr/t1ger-evbattery.png",
        associated = {"battery_coolant"},
        type = "electric",
        prop = {model = "t1ger_evbattery", pos = vector3(-0.01, -0.1, -0.13), rot = vector3(0.0, -2.0, 0.0), boneId = 28422},  -- texture fix
    },
    fuel_injector = {
        item = "t1ger_fuelinjector",
        label = "Fuel Injector",
        icon = "https://i.postimg.cc/Pqhq9JTJ/t1ger-fuelinjector.png",
        associated = {"oil_filter", "air_filter", "fuel_filter", "spark_plugs"},
        type = "gas",
        prop = {model = "t1ger_fuelinjector", pos = vector3(0.0, 0.0, 0.0), rot = vector3(0.0, -90.0, 0.0), boneId = 60309},
    },
    power_steering_pump = {
        item = "t1ger_powersteeringpump",
        label = "Power Steering Pump",
        icon = "https://i.postimg.cc/XY9JsdMt/t1ger-powersteeringpump.png",
        associated = {"steering_fluid"},
        type = "gas",
        prop = {model = "t1ger_powersteeringpump", pos = vector3(0.0, -0.12, 0.10), rot = vector3(120.0, 0.0, 0.0), boneId = 60309},
    },
    radiator = {
        item = "t1ger_radiator",
        label = "Radiator",
        icon = "https://i.postimg.cc/0NfNsvyf/t1ger-radiator.png",
        associated = {"coolant", "drive_belt"},
        type = "gas",
        prop = {model = "t1ger_radiator", pos = vector3(0.0, -0.1, 0.1), rot = vector3(0.0, 0.0, 0.0), boneId = 28422},
    },
    transmission = {
        item = "t1ger_transmission",
        label = "Transmission",
        icon = "https://i.postimg.cc/bJwwY5Gj/t1ger-transmission.png",
        associated = {"oil_filter", "transmission_fluid"},
        type = "gas",
        prop = {model = "t1ger_transmission", pos = vector3(-0.1, -0.1, -0.03), rot = vector3(0.0, -10.0, -90.0), boneId = 28422},
    }
}

Config.Materials = { -- index is item name and value is item label.
    scrap_metal = "Scrap Metal",
    steel = "Steel",
    aluminium = "Aluminium",
    plastic = "Plastic",
    rubber = "Rubber",
    electric_scrap = "Electric Scrap",
    glass = "Glass",
    copper = "Copper",
    carbon_fiber = "Carbon Fiber",
    brass = "Brass",
    synthetic_oil = "Synthetic Oil",
    acid = "Acid"
}