Config.Debug = false -- Debug true or false
----------
Config.JumpKey = 51 -- control id of the button to jump from the plane. Default: (E) [key codes can be found here: https://docs.fivem.net/docs/game-references/controls/]
Config.PickupKey = 51 -- control id of the button to pickup items. Default: (E) [key codes can be found here: https://docs.fivem.net/docs/game-references/controls/]
Config.PickupLable = 'E ' -- if you change Config.PickupKey then change this to match it
Config.ReloadIcon = 'R ' -- Shouldnt really change this unless your keyboard is a different launguage
----------
Config.MinPlayers = 1 -- minimum players required for players to be able to start a match (This is set to 1 so that you the developer can test the script)
Config.MaxPlayers = 110 -- Max players that can be in a mtch at once
Config.ReadyUpRequirment = 0.75 -- Default is 75% of players are required to ready up for the game to start.
Config.MinWager = 0 -- minimum bet players can make
Config.MaxWager = 25000 -- maximum bet players can make
Config.IconBlip = { -- The arcade blip settings
    Icon = 740,
    Color = 27,
    Size = 0.8,
}
Config.RoyalePed = "u_m_y_rsranger_01" -- Battle Royale sign up ped (THIS DOESNT MATTER IF Config.UseVrHeadSet IS TRUE)
Config.RoyalePedLoc = vector4(-1288.3, -310.17, 36.65, 341.88) -- Battle Royale sign up ped location (THIS DOESNT MATTER IF Config.UseVrHeadSet IS TRUE)
Config.BattlePlane = 'AVENGER' -- Plane or helicopter that flys the players in. (MAKE SURE THIS VEHICLE IS NOT BLACKLISTED IN YOUR SERVER)
Config.LeaderboardLocation = vector4(-1287.60, -311.16, 36.80, 159.0) -- Location of the leaderboard
----------
Config.VrHeadSetPropMale = 176 -- (OPTIONAL CAN IGNORE REALLY) whatever number the vr headset is for you in your clothing menu hat section (MALE) [clothing hat prop can be found here: https://www.gta5-mods.com/player/oculus-rift-cv1-eup]
Config.VrHeadSetPropFemale = 122 -- (OPTIONAL CAN IGNORE REALLY) whatever number the vr headset is for you in your clothing menu hat section (FEMALE) [clothing hat prop can be found here: https://www.gta5-mods.com/player/oculus-rift-cv1-eup]
----------
Config.EnablePlayerDamageEffect = true -- Show damage numbers above the player head when you damage them.
Config.PlayHitMarkerSound = true -- Play hitmarker sound when damaging a player.
Config.RequireAdminToStartGane = false -- Make this true if you want only admins to be able to start a battle royale game.
Config.DrawOutlineOnLoot = true -- Make this false if you dont want there to be an outline on items in the game.
Config.SpectateEnabled = true -- if you dont want players to have the option to spectate make this false.
Config.UsingCrossHairByDefault = false -- This was requested for server that have crosshair on always. If you are one of those servers then make this true or else just leave it (applies to qbcore only)
----------
Config.WhitelistedCIDsToStartGame = { -- If you want to whitelist the game so that only specific people can start it, add their CID to this table if you're using QBCore. If you're using ESX, add their identifier instead.
    -- "EJD97814",
}
----------
-- (DO NOT CHANGE THIS)
Config.BeginningTimeInSeconds = 60 -- How many seconds players have to loot before zone stuff starts happening
Config.CircleCooldownSeconds = 120 -- How many seconds players have in between each zone shrinking (DEFAULT) This number can be changed from the menu in game
----------
Config.OutlineColor = {R = 255, G = 255, B = 255} -- Outline RGB color of the weapons and pickup items 
----------
----------
-- CAR MODELS THAT SPAWN ON THE MAP IN RANDOM Config.CarSpawn LOCATIONS
Config.CarOptions = {
    'bison',
    'caracara',
    'dubsta2',
    'dubsta3',
    'sanchez',
    'blazer2',
    'vetir',
    'blazer5',
    'crusader',
    'barracks',
    'nightshark',
}
-- HELI MODELS THAT SPAWN ON THE MAP IN RANDOM Config.HeliSpawns LOCATIONS
Config.HeliOptions = {
    'buzzard2',
}
----------
----------
Config.PlayerXP = {
    PerKill = { -- Amount of xp a player can get per kill
        Min = 1, -- Minimum amount of xp a player can get per kill
        Max = 5, -- Maximum amount of xp a player can get per kill
    },
    PerWin = { -- Amount of xp a player can get per win
        FirstPlaceMin = 25, -- Minimum amount of xp a player can get per winning first place
        FirstPlaceMax = 30, -- Maximum amount of xp a player can get per winning first place
        ----------
        SecondPlaceMin = 15, -- Minimum amount of xp a player can get per winning second place
        SecondPlaceMax = 20, -- Maximum amount of xp a player can get per winning second place
        ----------
        ThirdPlaceMin = 10, -- Minimum amount of xp a player can get per winning third place
        ThirdPlaceMax = 15, -- Maximum amount of xp a player can get per winning third place
    },
    PerSurviving = { -- Amount of xp a player can get per surviving when another player dies
        Min = 1, -- Minimum amount of xp a player can get per surviving when another player dies
        Max = 3, -- Maximum amount of xp a player can get per surviving when another player dies
    },
}
Config.Ranks = {}
local base = 50
for i = 1, 100 do
    Config.Ranks[i] = math.floor(base + (i ^ 1.25))
end
----------