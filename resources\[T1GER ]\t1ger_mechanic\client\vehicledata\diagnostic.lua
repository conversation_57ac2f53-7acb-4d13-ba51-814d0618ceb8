local selectedParts = {} -- stores selected components
isDiagnosing = false

--- Returns whether the player is currently diagnosing or inspecting a vehicle.
--- @return boolean
function IsPlayerDiagnosing()
    return isDiagnosing
end
exports("IsPlayerDiagnosing", IsPlayerDiagnosing)

--- Sets whether the player is currently diagnosing or inspecting a vehicle.
--- @param state boolean
function SetPlayerDiagnosing(state)
    isDiagnosing = state
end
exports("SetDiagnosing", SetPlayerDiagnosing)

--- Returns string with vehicle system overview details
--- @param vehicle number The vehicle entity handle
--- @param mileage number The vehicle's current mileage
--- @return string?
local function GetVehicleSystemOverviewString(vehicle, mileage)
    
    local GetOptionValue = function(veh, vehMileage, option) -- local function to get option value
        local value = nil

        if option == "engineHealth" then
            value = GetVehicleEngineHealth(veh)/10
        elseif option == "bodyHealth" then 
            value = GetVehicleBodyHealth(veh)/10
        elseif option == "tankHealth" then 
            value = GetVehiclePetrolTankHealth(veh)/10
        elseif option == "fuelLevel" then 
            value = _API.GetVehicleFuel(veh)
        elseif option == "dirtLevel" then 
            value = GetVehicleDirtLevel(veh)
        elseif option == "oilLevel" then  
            value = GetVehicleOilLevel(veh)
        elseif option == "engineTemp" then
            value = GetVehicleEngineTemperature(veh)
        elseif option == "mileage" then
            value = type(vehMileage) == "number" and vehMileage or 0.0
        elseif option == "plate" then
            value = GetVehicleNumberPlateText(veh)
        end
        -- round number:
        if type(value) == "number" then
            value = math.round(value, 2)
        end
        -- return:
        return value
    end

    -- Initialize an empty string for the system overview
    local overviewText = ""

    -- Ordered list of keys to display in order
    local orderedKeys = { "engineHealth", "bodyHealth", "tankHealth", "fuelLevel", "dirtLevel", "oilLevel", "engineTemp", "mileage", "plate" }

    -- Loop through each system value and format the string
    for _, option in ipairs(orderedKeys) do
        local cfg = Config.Components.Diagnostic.systemOverview.options[option]
        local overviewValue = GetOptionValue(vehicle, mileage, option)
        if overviewValue then
            -- Append formatted system data
            overviewText = overviewText .. cfg.label .. " - " .. overviewValue .. cfg.symbol .. "\n"
        end
    end

    -- Trim any trailing newline characters (optional)
    overviewText = overviewText:match("^(.-)\n?$")

    -- Return the final formatted system overview string
    return overviewText
end

--- Creates a table with workflow tasks indexed by taskId(number)
--- @param components table The selected components
--- @return table
local function CreateWorkflowTasks(components)
    local workflowTasks = {}
    for partName, partInfo in pairs(components or {}) do
        local taskId = (#workflowTasks + 1)
        workflowTasks[taskId] = {
            id = taskId,
            name = string.format(Config.BodyRepair.WorkflowName, partInfo.label),
            completed = false,
            partLabel = partInfo.label,
            partName = partName
        }
    end
    return workflowTasks
end

--- Registers options for the vehicle diagnostics main menu
--- @param vehicle number The vehicle entity handle
local function RegisterVehicleDiagnosticsOptions(vehicle)
    local vehicleData = GetVehicleData(vehicle)

    --- Returns menu option for given component type
    --- @param veh number The vehicle entity handle
    --- @param vehData table The vehicleData statebag
    --- @param partType string Type of component `"core_parts"` or `"service_parts"`
    --- @return table
    local function GetMenuOptionForComponentType(veh, vehData, partType)
        return {
            title = locale("menu_title.diagnostic_"..partType),
            icon = Config.Components.Diagnostic.icons[partType],
            description = locale("menu_description.diagnostic_"..partType),
            onSelect = function()
                ViewComponents(veh, vehData, partType)
            end
        }
    end
    
    local menuOptions = {}

    -- Insert vehicle system overview
    if Config.Components.Diagnostic.systemOverview.enable then
        menuOptions[#menuOptions+1] = {
            title = locale("menu_title.diagnostic_system_overview"),
            icon = Config.Components.Diagnostic.icons.system_overview,
            description = GetVehicleSystemOverviewString(vehicle, vehicleData.mileage),
        }
    end

    -- Insert Core Parts
    if vehicleData.core_parts and next(vehicleData.core_parts) then
        menuOptions[#menuOptions+1] = GetMenuOptionForComponentType(vehicle, vehicleData, "core_parts")
    end

    -- Insert Service Parts
    if vehicleData.service_parts and next(vehicleData.service_parts) then
        menuOptions[#menuOptions+1] = GetMenuOptionForComponentType(vehicle, vehicleData, "service_parts")
    end

    -- Insert Button to start repair workflow
    if type(selectedParts) == "table" and next(selectedParts) then
        local menuDescription = locale("menu_description.diagnostic_workflow")
        for partName, partInfo in pairs(selectedParts) do
            local partString = string.format(locale("menu_description.diagnostic_workflow2"), partInfo.label, partInfo.condition.label, partInfo.health)
            menuDescription = menuDescription..partString
        end
        menuOptions[#menuOptions+1] = {
            title = locale("menu_title.diagnostic_workflow"),
            icon = Config.Components.Diagnostic.icons.start_workflow,
            description = menuDescription,
            onSelect = function()
                -- Store in statebag for other mechanics/scripts
                local workflowTasks = CreateWorkflowTasks(selectedParts)
                local vehState = Entity(vehicle).state
                vehState:set("t1ger_mechanic:workflowTasks", workflowTasks, true)

                -- trigger workflow UI
                local plate = GetVehicleNumberPlateText(vehicle)
                StartWorkflow(string.format(Config.Components.WorkflowTitle, plate), workflowTasks)
            end
        }
    end

    if #menuOptions <= 0 then 
        return
    end

    -- register context
    lib.registerContext({
        id = "component_diagnostic_main",
        title = locale("menu_title.diagnostic_main"),
        options = menuOptions,
    })
end

--- Called when clicking on `Core Parts` or `Service Parts` in the vehicle diagnostic main menu
--- @param vehicle number The vehicle entity handle
--- @param vehicleData table The vehicleData statebag
--- @param componentType string Type of component `"core_parts"` or `"service_parts"`
function ViewComponents(vehicle, vehicleData, componentType)
    if not vehicleData or not vehicleData[componentType] then return end

    --- Returns the parts metadata
    --- @param partData table The part's info/data
    --- @param partType string The part type: `"core_parts"` or `"service_parts"`
    --- @param partName string The part's name
    --- @param veh number The vehicle entity handle
    --- @return table
    local function GetComponentMetadata(partData, partType, partName, veh)
        
        local metadata = {
            {label = locale("menu_metadata.component_condition"), value = partData.condition.label},
            {label = locale("menu_metadata.component_health"), value = partData.health.."%"},
            {label = locale("menu_metadata.component_associated"), value = partData.associatedList},
        }

        if type(partType) == "string" and partType == "service_parts" then
            metadata[#metadata+1] = {label = locale("menu_metadata.component_next_service"), value = partData.remaining.." "..Config.Mileage.Unit}
            metadata[#metadata+1] = {label = locale("menu_metadata.component_since_service"), value = partData.mileage.." "..Config.Mileage.Unit}
            local serviceInterval = GetVehicleServiceInterval(veh, partName)
            metadata[#metadata+1] = {label = locale("menu_metadata.component_service_interval"), value = serviceInterval.." "..Config.Mileage.Unit}
        end

        return metadata
    end

    local vehicleType = GetMechanicVehicleType(vehicle)

    local partList = {}
    for partName, value in pairs(vehicleData[componentType] or {}) do
        local partInfo = componentType == "core_parts" and GetCorePartInfo(partName, value) or componentType == "service_parts" and GetServicePartInfo(vehicle, partName, value)
        if partInfo and (partInfo.type == vehicleType or partInfo.type == "shared") then
            partList[#partList+1] = {
                title = partInfo.label.." - "..tostring(partInfo.health).."%",
                icon = partInfo.icon,
                metadata = GetComponentMetadata(partInfo, componentType, partName, vehicle),
                onSelect = function()
                    UpdateDiagnosticsTasks(partName, partInfo)
                    Wait(200)
                    lib.showContext("component_diagnostic_"..componentType)
                end,
            }
        end
    end

    -- sort options:
    table.sort(partList, function(a, b)
        return a.title < b.title
    end)

    -- register & show context
    lib.registerContext({
        id = "component_diagnostic_"..componentType,
        menu = "component_diagnostic_main", -- return menu
        onBack = function()
            RegisterVehicleDiagnosticsOptions(vehicle)
        end,
        title = locale("menu_title.diagnostic_"..componentType),
        canClose = false,
        options = partList,
    })

    lib.showContext("component_diagnostic_"..componentType)
end

--- Updates diagnostics task list
--- @param partName string The part name
--- @param partInfo table The part data
function UpdateDiagnosticsTasks(partName, partInfo)
    if type(selectedParts) ~= "table" then selectedParts = {} end

    if not selectedParts[partName] then
        -- add to selected parts
        selectedParts[partName] = partInfo
        _API.ShowNotification(string.format(locale("notification.component_tasklist_added"), partInfo.label), "inform", {duration = 4000})
    else
        -- remove from selected parts
        selectedParts[partName] = nil
        _API.ShowNotification(string.format(locale("notification.component_tasklist_removed"), partInfo.label), "error", {duration = 4000})
    end
end

--- Called when using diagnostic tool item / command
local function UseDiagnosticTool()
    if not IsPlayerMechanic() then return end
    
    if IsPlayerDiagnosing() then
        return _API.ShowNotification(locale("notification.is_diagnosing"), "inform", {})
    end

    local vehicle, vehicleDist = lib.getClosestVehicle(coords, 6.0, true) -- also gets vehiclel player is because of "true"
    if not vehicle or not DoesEntityExist(vehicle) or not IsEntityAVehicle(vehicle) then
        return _API.ShowNotification(locale("notification.component_no_vehicle"), "inform", {})
    end

    local taskAnim, openHood = {}, false

    SetPlayerDiagnosing(true)

    if cache.seat and cache.seat == -1 then
        taskAnim = { dict = "anim@amb@clubhouse@tutorial@bkr_tut_ig3@", clip = "machinic_loop_mechandplayer", blendIn = 2.0, blendOut = 2.0, flag = 49}
        SetVehicleUndriveable(vehicle, true)
    else
        -- Is near the hood/engine?
        local hood = GetVehicleClosestHood(vehicle, 0.8, false)
        if not hood then
            if Config.BodyRepair.Inspect.diagnosticTool then
                SetPlayerDiagnosing(false)
                return InspectVehicleBody(vehicle)
            else
                SetPlayerDiagnosing(false)
                return _API.ShowNotification(locale("notification.must_be_near_engine_hood"), "inform", {})
            end
        end
        TaskTurnPedToFaceEntity(player, vehicle, 1000)
        VehicleHoodAnimation(vehicle, false)
        taskAnim = {dict = "mini@repair", clip = "fixing_a_player", blendIn = 2.0, blendOut = 2.0, flag = 1}
        openHood = true
    end

    -- Progress bar
    local success = ProgressBar({
        duration = Config.Components.Diagnostic.duration, -- You can move this to Config if you want
        label = locale("progressbar.component_diagnose"),
        useWhileDead = false,
        canCancel = true,
        anim = next(taskAnim) and taskAnim or nil,
        disable = { move = true, combat = true, car = true}
    })

    -- clear tasks
    ClearPedTasks(player)

    -- if success
    if success then
        selectedParts = {}

        -- Register context
        RegisterVehicleDiagnosticsOptions(vehicle)

        -- Show context
        lib.showContext("component_diagnostic_main")
    end
    
    if openHood then
        VehicleHoodAnimation(vehicle, true)
    else
        SetVehicleUndriveable(vehicle, false)
    end
    
    SetPlayerDiagnosing(false)
end

--- Event for using diagnostic tool item:
RegisterNetEvent("t1ger_mechanic:client:useDiagnosticTool", function()
    UseDiagnosticTool(partName, partType)
end)

--- Command to open diagnostics menu
if Config.Components.Diagnostic.command.enable then
    RegisterCommand(Config.Components.Diagnostic.command.name, function(source, args, rawCommand)
        UseDiagnosticTool()
    end)
end