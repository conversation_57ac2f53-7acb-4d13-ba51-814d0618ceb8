fx_version 'cerulean'
game 'gta5'
lua54 'yes'
use_experimental_fxv2_oal 'yes'

name 't1ger_tuningsystem'
author 'T1GE<PERSON> Scripts'
discord 'https://discord.gg/FdHkq5q'
description 'T1GER Tuning System'
version '1.8.5'

dependencies { 
    '/server:7290',     -- ⚠️PLEASE READ⚠️; Requires at least SERVER build 7290.
    '/gameBuild:3095',  -- ⚠️PLEASE READ⚠️; Requires at least GAME build 3095.
    't1ger_lib', 		-- ⚠️PLEASE READ⚠️; Requires t1ger_lib.
}

shared_scripts {
    '@ox_lib/init.lua',
	'shared/language.lua',
	'shared/config.lua',
	'shared/items.lua',
	'shared/mods.lua',
	'shared/npc_jobs.lua',
	'shared/workbench.lua',
	'shared/engine_swaps.lua',
	'shared/nitrous.lua',
}

client_scripts {
	'client/functions.lua',
	'client/main.lua',
	'client/nui.lua',
	'client/class.lua',
	'client/admin_menu.lua',
	'client/boss_menu.lua',
	'client/mods_menu.lua',
	'client/mods_tuner.lua',
	'client/engine_swaps.lua',
	'client/nitrous.lua',
	'client/dyno.lua',
	'client/npc_jobs.lua',
	'client/billing.lua'
}

server_scripts {
	'@mysql-async/lib/MySQL.lua',
	'server/functions.lua',
	'server/main.lua',
	'server/class.lua'
}

files {
	'web/index.html',
	'web/assets/*.*',
    'carcols_gen9.meta',
    'carmodcols_gen9.meta'
}

ui_page 'web/index.html'

data_file 'CARCOLS_GEN9_FILE' 'carcols_gen9.meta'
data_file 'CARMODCOLS_GEN9_FILE' 'carmodcols_gen9.meta'

escrow_ignore {
    'shared/*.lua',
    'client/*.lua',
    'server/*.lua'
}

exports {
	'IsPlayerTuner',
	'GetItemInfo'
}

server_exports {
	'SetMarkup',
	'GetAccountMoney',
	'SetAccountMoney',
	'AddAccountMoney',
	'RemoveAccountMoney'
}

dependency '/assetpacks'