:root{--phone-color-primary: rgb(255, 255, 255);--phone-color-opacity: rgba(242, 242, 242, .4);--phone-color-opacity2: rgb(30, 30, 30, .5);--phone-color-highlight: rgb(250, 250, 250);--phone-color-highlight2: rgb(240, 240, 240);--phone-color-highlight3: rgb(220, 220, 220);--phone-highlight-opacity15: rgba(145, 145, 145, .15);--phone-highlight-opacity35: rgba(145, 145, 145, .35);--phone-highlight-opacity45: rgba(145, 145, 145, .45);--phone-highlight-opacity55: rgba(145, 145, 145, .55);--phone-color-input: rgba(241, 241, 241, .656);--phone-text-primary: rgb(0, 0, 0);--phone-text-secondary: rgb(142, 142, 147);--phone-color-hover: rgb(240, 240, 240);--phone-color-border: rgba(200, 200, 200, .4);--phone-color-grey: #8e8e93;--phone-color-blue: #0a84ff;--phone-color-green: #32d74b;--phone-color-green-secondary: #092911;--phone-color-red: #ff3b30;--phone-color-orange: rgb(255, 157, 10);--phone-color-yellow: #cca250;--phone-color-pink: #ff3b30;--instagram-primary: #ffffff;--instagram-blue: rgb(0, 149, 246);--instagram-red: rgb(237, 73, 86);--instagram-primary-text: rgb(38, 38, 38);--instagram-secondary-text: rgb(142, 142, 142);--instagram-stroke: rgb(219, 219, 219);--instagram-border: rgb(219, 219, 219);--instagram-highlight: rgb(239, 239, 239);--tinder-color-pink: #ff4573;--tinder-color-orange: #ff5f65;--tinder-color-mix: #f5547c;--twitter-primary: #f5f8fa;--twitter-secondary: #14171a;--twitter-background-highlight: rgb(239, 243, 244);--twitter-primary-text: #14171a;--twitter-secondary-text: #657786;--twitter-alt-text: #657786;--twitter-border: #bdc5cd75;--twitter-border-secondary: #1d9bf0;--twitter-highlight: #1d9bf0;--twitter-hover: rgba(15, 20, 25, .1);--twitter-action: #14171a;--twitter-blue: #1d9bf0;--tiktok-primary: #ffffff;--tiktok-secondary: #000000;--tiktok-text-primary: #000000;--tiktok-text-secondary: #86878b;--tiktok-color-border: #d0d1d3;--tiktok-color-pink: #fe2c55;--tiktok-color-aqua: #00f2ea;--tiktok-color-yellow: #f8cd14;--tiktok-color-blue: #479fc5;--tiktok-color-unread: rgba(254, 44, 86, .2);--crypto-color-primary: rgb(255, 255, 255);--browser-primary: rgb(245, 245, 245);--browser-secondary: rgba(153, 153, 153, .15);--browser-gradient: linear-gradient(230deg, #f4d6ff, #c5f1ff);--browser-footer: rgba(255, 255, 255, .75);--browser-border: rgba(102, 102, 102, .75);--browser-text-secondary: #696969;--controlcentre-opacity: rgba(255, 255, 255, .15);--controlcentre-opacity2: rgba(255, 255, 255, .2);--controlcentre-active: rgba(255, 255, 255, .5);--notification-primary: rgba(215, 215, 215, .5);--notification-secondary: rgba(215, 215, 215, .1);--lockscreeneditor-background: rgba(255, 255, 255, .75);--lockscreeneditor-secondary: #d9d9d9;--app-bg: #ececec;--app-bg2: #ffffff;--app-secondary: #ffffff;--app-secondary2: #ececec;--app-highlight: #cccccc;--app-highlight2: #999999;--app-highlight3: #ffffff;--app-border: #666666;--app-slider: #cccccc;--app-slider-active: #333333;--app-button: #ffffff;--components-bg: #eeeeee;--components-secondary: #ffffff;--components-highlight: #cccccc}[data-theme=dark]{--phone-color-primary: #000000;--phone-color-opacity: rgb(30, 30, 30, .5);--phone-color-opacity2: rgba(242, 242, 242, .4);--phone-color-highlight: rgb(15, 15, 15);--phone-color-highlight2: rgb(20, 20, 20);--phone-color-highlight3: rgb(25, 25, 25);--phone-highlight-opacity35: rgba(145, 145, 145, .35);--phone-highlight-opacity45: rgba(50, 50, 50, .6);--phone-highlight-opacity55: rgb(60, 60, 60, .8);--phone-color-input: rgba(60, 60, 67, .6);--phone-text-primary: #f2f2f7;--phone-text-secondary: #6f6f6f;--phone-color-grey: #636366;--phone-color-hover: rgb(30, 30, 30);--phone-color-border: rgba(150, 150, 150, .2);--phone-color-blue: #076bcf;--instagram-primary: #000000;--instagram-blue: rgb(0, 149, 246);--instagram-red: rgb(237, 73, 86);--instagram-primary-text: rgb(250, 250, 250);--instagram-secondary-text: rgb(142, 142, 142);--instagram-stroke: rgb(219, 219, 219);--instagram-border: rgb(54, 54, 54);--instagram-highlight: rgb(38, 38, 38);--twitter-primary: #000000;--twitter-secondary: #f5f8fa;--twitter-background-highlight: rgb(20, 20, 20);--twitter-primary-text: #f5f8fa;--twitter-secondary-text: #aab8c2;--twitter-alt-text: #657786;--twitter-border: #38444d;--twitter-border-secondary: #38444d;--twitter-hover: rgba(150, 150, 150, .1);--twitter-highlight: #dcdcdc;--twitter-action: #1d9bf0;--twitter-blue: #1d9bf0;--crypto-color-primary: rgb(24, 26, 32);--tiktok-text-primary: #f2f2f7;--tiktok-text-secondary: #6f6f6f;--tiktok-color-border: #96969633;--browser-primary: rgb(15, 15, 15);--browser-secondary: rgba(153, 153, 153, .15);--browser-gradient: linear-gradient(230deg, #453b48, #2f393d);--browser-footer: rgba(51, 51, 51, .75);--browser-border: rgba(102, 102, 102, .75);--browser-text-secondary: #999999;--controlcentre-opacity: rgba(0, 0, 0, .15);--controlcentre-opacity2: rgba(0, 0, 0, .2);--controlcentre-active: rgba(0, 0, 0, .5);--notification-primary: rgba(0, 0, 0, .1);--notification-secondary: rgba(0, 0, 0, .12);--lockscreeneditor-background: rgba(0, 0, 0, .8);--lockscreeneditor-secondary: #333333;--app-bg: #000000;--app-bg2: #000000;--app-secondary: #141414;--app-secondary2: #141414;--app-highlight: #cccccc;--app-highlight2: #696969;--app-highlight3: #212121;--app-border: #cccccc;--app-slider: #999999;--app-slider-active: #ffffff;--app-button: #333333;--components-bg: #000000;--components-secondary: #141414;--components-highlight: #696969}.racing-container{height:100%;width:100%;position:relative;z-index:-1;display:flex;flex-direction:column;align-items:center;gap:1.25rem;background-color:#f2f2f7}.racing-container .view-wrapper{width:100%;height:100%;display:flex;flex-direction:column;align-items:center;gap:1.25rem}.racing-container .gradient{position:absolute;top:0;z-index:-1;width:100%;height:28%;background:linear-gradient(289deg,#d14fa5 36.88%,#5786cd 69.59%);-webkit-mask-image:linear-gradient(to top,transparent 0%,black 20%)}.racing-container .gradient.blue{background:rgb(0,122,255);opacity:.4;-webkit-mask-image:linear-gradient(to top,transparent 25%,black 75%)}.racing-container .racing-header{display:flex;align-items:center;justify-content:space-between;position:relative;width:90%;padding:6.5rem 1.5rem 1.5rem}.racing-container .racing-header .title{font-size:36px;font-weight:600}.racing-container .racing-header .profile-image{display:flex;width:2.5rem;height:2.5rem;aspect-ratio:1/1;border-radius:50%;justify-content:center;align-items:center;font-size:18px;font-weight:400;background-position:center;background-size:cover;background-repeat:no-repeat;background-color:var(--phone-text-secondary)}.racing-container .racing-header .profile-image.big{width:5rem;height:5rem;font-size:22px}.racing-container .racing-header .profile-image.bigger{width:9rem;height:9rem;font-size:50px}.racing-container .racing-header .profile-image.custom{background-color:transparent}.racing-container .racing-header .back{display:flex;align-items:center;position:absolute;top:4rem;font-size:18px;color:var(--phone-color-blue);cursor:pointer}.racing-container .racing-header .back svg{font-size:24px}.racing-container .panel{box-sizing:border-box;width:88%;display:flex;flex-direction:column;gap:1rem;padding:1rem;border-radius:10px;background-color:var(--phone-color-primary)}.racing-container .panel .panel-header{display:flex;align-items:center;gap:.25rem;color:#ff3b30;font-size:16px;font-weight:500}.racing-container .panel .panel-header svg{stroke-width:20;font-size:25px}.racing-container .panel .panel-content{display:flex}.racing-container .panel .panel-content .item{display:flex;flex-direction:column;gap:.05rem;padding:0 .8rem;border-left:2px solid var(--phone-highlight-opacity45)}.racing-container .panel .panel-content .item:first-child{padding-left:0;border-left:none}.racing-container .panel .panel-content .item .title{font-size:15px;font-weight:500}.racing-container .panel .panel-content .item .title[data-color=red]{color:var(--phone-color-red)}.racing-container .panel .panel-content .item .title[data-color=yellow]{color:#fc0}.racing-container .panel .panel-content .item .title[data-color=green]{color:#34c759}.racing-container .panel .panel-content .item .title[data-color=blue]{color:var(--phone-color-blue)}.racing-container .panel .panel-content .item .value{display:flex;align-items:flex-end;gap:.1rem;font-size:13px;font-weight:400;color:var(--phone-text-secondary)}.racing-container .panel .panel-content .item .value span{font-size:15px;font-weight:600;color:var(--phone-text-primary)}.racing-container section{width:88%;display:flex;flex-direction:column;gap:.5rem}.racing-container section .subtitle{margin-left:1rem;font-size:14px;color:var(--phone-text-secondary);text-transform:uppercase}.racing-container section .items{display:flex;flex-direction:column;background-color:var(--phone-color-primary);border-radius:10px}.racing-container section .items .item{width:100%;box-sizing:border-box;display:flex;align-items:center;gap:.7rem;padding:0 1rem;cursor:pointer}.racing-container section .items .item svg{color:var(--phone-color-blue);font-size:24px}.racing-container section .items .item:last-child .border{border-bottom:none}.racing-container section .items .item .border{width:100%;display:flex;align-items:center;justify-content:space-between;padding:.75rem 0;border-bottom:1px solid var(--phone-highlight-opacity45)}.racing-container section .items .item .border .info{display:flex;flex-direction:column;gap:.1rem}.racing-container section .items .item .border .info .title{font-size:16px;font-weight:400;color:var(--phone-text-primary)}.racing-container section .items .item .border .info .description{font-size:13px;font-weight:400;color:var(--phone-text-secondary)}.racing-container section .items .item .border .details{display:flex;align-items:center;gap:.4rem;color:var(--phone-text-secondary);font-size:18px}.racing-container section .items .item .border .chevron{color:var(--phone-text-secondary);font-size:18px}.racing-container .racing-footer{position:absolute;bottom:0;box-sizing:border-box;width:100%;padding:1rem 2.5rem 2.5rem;display:flex;align-items:center;justify-content:space-between;background-color:var(--phone-color-primary);border-top:1px solid var(--phone-highlight-opacity45)}.racing-container .racing-footer .item{display:flex;flex-direction:column;align-items:center;gap:.13rem;color:var(--phone-text-secondary);font-size:13px;cursor:pointer;transition:all .2s ease-in-out}.racing-container .racing-footer .item svg{font-size:28px}.racing-container .racing-footer .item[data-active=true],.racing-container .racing-footer .item:hover{color:var(--phone-color-blue)}
