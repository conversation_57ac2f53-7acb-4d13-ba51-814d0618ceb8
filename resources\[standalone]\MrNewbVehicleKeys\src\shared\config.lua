--		___  ___       _   _                  _      _____              _         _
--		|  \/  |      | \ | |                | |    /  ___|            (_)       | |
--		| .  . | _ __ |  \| |  ___ __      __| |__  \ `--.   ___  _ __  _  _ __  | |_  ___
--		| |\/| || '__|| . ` | / _ \\ \ /\ / /| '_ \  `--. \ / __|| '__|| || '_ \ | __|/ __|
--		| |  | || |   | |\  ||  __/ \ V  V / | |_) |/\__/ /| (__ | |   | || |_) || |_ \__ \
--		\_|  |_/|_|   \_| \_/ \___|  \_/\_/  |_.__/ \____/  \___||_|   |_|| .__/  \__||___/
--									          							  | |
--									          							  |_|
--
--		  Need support? Join our Discord server for help: https://discord.gg/mrnewbscripts
--		  If you need help with configuration or have any questions, please do not hesitate to ask.
--		  Docs Are Always Available At -- https://mrnewbs-scrips.gitbook.io/guide


Config = {}

Config.Utility = {
	Debug = false,														-- DO NOT ENABLE THIS UNLESS YOU KNOW WHAT YOU ARE DOING!
	EngineToggle = true,												-- set this to true to use my engine toggle command and keybind, if you have a hud/command that already has this set it as false to prevent overlap.
}

Config.KeybingSettings = {
	LockKeyBind = "l",													-- set this to the defualt keybind you want to use for the lock/unlock feature. (players can rebind this)
	HotWireBind = "",													-- set this to the defualt keybind you want to use for the hotwire feature.
	EngineKeyBind = "m",												-- set this to the defualt keybind you want to use for the engine toggle feature from line 21 here in the config. (players can rebind this)
	Keyring = "",														-- set this to the defualt keybind you want to use for the keyring feature (only for non item setting). (players can rebind this)
}

Config.HotWireSettings = {
	Cooldown = true,
	HotWireCooldownTime = 4000,											-- set this to the cooldown time between hotwiring attempts on a vehicle in milliseconds (1000 = 1 second)
	MaxAttempts = 3,													-- set this to the maximum number of hotwire attempts per vehicle
	GrantVehicleKeysOnHotWire = true,									-- set this to false to disable the key being given on successful hotwire. If you dont grant the key it has a "hotwired status" that allows engine toggle and to drive it only.
}

Config.VehicleSettings = {
	HotwireDisabled = false,											-- set this to true to disable the hotwire features, this will make it so a player can just get in and drive the car without the keys. This forces keys to mainly focus on lock/unlocking.
	EngineRemainsOnWhenExiting = false,									-- set this to true to keep a vehicle running when exiting it, this is a more realistic approach where youd have to turn it off when getting out or leave it on if you want to plan an escape etc.
	GiveKeysIfEngineRunning = false,									-- set this to true to grant the keys on entering the vehicles that are running (like real life keys would be in the ignition right?), anyone who enters driver seat while its running will get a key.
	IsFronk = {
		-- this is the door index, its for if a car has a trunk in the front etc
		[`adder`] = 4,
		[`reaper`] = 4,
		[`torero`] = 5,
		[`t20`] = 4,
	},
	JobBypassKeys = {
		-- This is to make it so the listed job does not require keys for the listed vehicles
		-- If a cop gets in another cop car they can just drive, but if they get in a civ car they need keys.
		--[[
		["JobName"] = { -- this is the job name, anthing in this table will be bypassed with this job
			"vehiclename"	-- this is the spawn name of the vehicle.
		},
		--]]
		["police"] = {
			[`police`] 	= true,
			[`police2`] = true,
			[`polmav`] 	= true,
		},
		["ambulance"] = {
			[`ambulance`] = true,
			[`polmav`]	  = true,
		},

	},
	No_Key_Required_Vehicles = {
		[`bmx`] 		= true,
		[`cruiser`] 	= true,
		[`scorcher`] 	= true,
		[`tribike2`] 	= true,
		[`tribike3`] 	= true,
		[`fixter`] 		= true,
		[`tribike`] 	= true,
		[`inductor`] 	= true,
		[`inductor2`] 	= true,
		[`serv_electricscooter`] 	= true,
		--put other vehicles you wish to remove key requirements from here
		--this must be the model name itself
	},
	ElectricVehicles = {
		[`dilettante2`] 	= true,
		[`voltic`] 			= true,
		[`voltic2`] 		= true,
		[`dilettante`] 		= true,
		[`pipistrello`] 	= true,
		[`omnisegt`] 		= true,
		[`surge`] 			= true,
		[`khamelion`] 		= true,
		[`raiden`] 			= true,
		[`mower`] 			= true,
		[`coureur`] 		= true,
		[`buffalo5`] 		= true,
		[`cyclone`] 		= true,
		[`neon`] 			= true,
		[`tezeract`] 		= true,
		[`imorgon`] 		= true,
		[`iwagen`] 			= true,
		[`virtue`] 			= true,
		[`vivanite`] 		= true,
		[`envisage`] 		= true,
		-- this is a list of all known vanilla electric vehicles, if you have a custom one you can add it here
	},
	ConvertableVehicles = {
		[`ninef2`] 			= true,
		[`cogcabrio`] 		= true,
		[`felon2`] 			= true,
		[`carbonizzare`] 	= true,
		[`rt3000`] 			= true,
		[`issi2`] 			= true,
		[`tornado2`] 		= true,
		[`rapidgt2`] 		= true,
		[`sentinel2`] 		= true,
		[`zion2`] 			= true,
		[`comet7`] 			= true,
		[`surano`] 			= true,
		[`windsor2`] 		= true,
		[`tornado5`] 		= true,
		[`Vigero3`] 		= true,
		[`dominator9`] 		= true,
		-- this is a list of all known vanilla convertable vehicles, if you have a custom one you can add it here
	},
	HijackImmune = {
		--[`police`] = true,
		-- this is a list of vehicles that are immune to hijacking, if you have a custom one you can add it here. ie prevent civs from stealing cop cars
	}
}

Config.VehicleLockingChance = {
	EnableLocalVehicleLocking = true,									-- set this to to true to enable the chance of the local vehicles being locked, parked/driving otherwise only locked vehicles will be set by spawning locked or set by a player with keys
	UnlockedChance = 50,												-- set this to the percentage chance that local vehicles will be unlocked. Set to 0 to lock all cars (both parked and driving)
}

Config.ItemBasedSettings = {
	lockpickItemName = "lockpick",										-- set this to the itemname that would be used for the lockpick
	advancedlockpickItemName = "advancedlockpick",						-- set this to the itemname that would be used for the advanced lockpick
	using_key_items = true,												-- set this to true if you want to use the keys as an item
	DisableKeyFobUI = false,											-- set this to true if you want to use the keys as an item WITHOUT UI
	vehicleKeysItem = "vehiclekeys",									-- set this to the itemname that would be used for the keys
	keyRingItem = "keyring",											-- set this to the itemname that would be used for the keyring
	LockChangeSystemEnabled = true,										-- set this to true to enable the lock change system (this will allow players to change the lock on a vehicle making old item based keys useless)
	locksystemItem = "aftermarket_locks",								-- set this to the itemname that would be used for the lock change
	FallBackTimerCheck = 3000,											-- set this to the time in milliseconds that the script will check for keys if they are not found on the player, 3000 = 3 seconds and seems fair. lower the number if you want it to check more often.
}

Config.LockSmithSettings = {
	-- please be aware if you dont own a vehicle through the framework tables you will not be able to purchaase spare keys
	SpareKeyPrice = 500,												-- set to to the price of a spare key
	KeyRingPrice = 50,													-- set to the price of a key ring
	Locations = {
		["Dirks Locksmithing"] = {
			coords = vector4(-564.0670, -590.2076, 40.4303, 177.0),
			pedmodel = "ig_bestmen",
			distance = 100,
			blipSprite = 811, -- if you use an outdated hud use 186, if you use a newer hud use 811 or if you want no blip comment this line out
			blipColor = 1,
			blipDisplay = 4,
			blipScale = 0.5,
		},

		--[[
		-- The below is an example for how to add more locations
		freds = {
			coords = vector4(1036.2108, -2111.0986, 32.6040, 357.9673),
			pedmodel = "u_m_y_pogo_01",
			distance = 100,
			blipSprite = 186,
			blipColor = 1,
			blipDisplay = 4,
			blipScale = 1.2,
		}--]]
	},
}

Config.AnimationSettings = {
	animDict = "veh@break_in@0h@p_m_one@",								-- set to the animation dictionary for progressbar
	anim = "low_force_entry_ds",										-- set to the animation name for progressbar
	keyProp = "prop_cuff_keys_01",										-- set to the prop name for the keys used when locking/unlocking a car
}

Config.Prefrences = {
	HelpTextPostion 	= "right-center",								-- set this to the position of the help text messages (the ones that say press e to hotwire)
	UseThirdPartyLockpick = false,										-- set this to true to use a third party lockpick system, set to false to use the built in one here. This is for compatability with qb-smallresources/other lockpick systems in housing scripts. If you need to alter it its in shared/init.lua
	LockpickAlertChance = 100,											-- set this to the percentage chance that a dispatch alert will be sent when a vehicle is lockpicked -- set to 0 to never alert
	HotwireAlertChance 	= 100,											-- set this to the percentage chance that a dispatch alert will be sent when a vehicle is Hotwired -- set to 0 to never alert
	LockpickBreakChance = 50,											-- set this to the percentage chance that the lockpick will break when lockpicking fails, set to 100 to never break
	HijackingAlertChance = 100,											-- set this to the percentage chance that a dispatch alert will be sent when a vehicle is hijacked -- set to 0 to never alert
	TargetVehicleFunctionsEnabled = true,								-- set this to true to enable the target features on cars to lock/unlock/toggle engine etc
	DisableVehicleAutoStart = false,									-- set this to true to disable the auto start of the vehicle when entering
	LockPickMiniGame = "ox",											-- set this to the minigame you want to use for lockpicking "ox", "bagus", "ps-ui", "bl-progress", "bl-keyspam", "bl-circle", "bl-printlock", "t3_lockpick", "xmmx", "rainmadlockpick", "rainmadaction", "rainmadquick", "rainmadmash", "rainmadangled", "false" (false for none and always success)
	HotWireMiniGame = "ox",												-- set this to the minigame you want to use for hotwiring "ox", "bagus", "ps-ui", "bl-progress", "bl-keyspam", "bl-circle", "bl-printlock", "t3_lockpick", "xmmx",  "rainmadlockpick", "rainmadaction", "rainmadquick", "rainmadmash", "rainmadangled", "false" (false for none and always success)
	ProximityLocking = {												-- set this to true to enable the proximity locking system for cars you have keys to
		enabled = true,
		lockdistance = 12,												-- set this to the distance for the proximity locking system when the vehicle is locked
		unlockdistance = 5,												-- set this to the distance for the proximity locking system when the vehicle is unlocked
	},
	DispatchAlertJobs = {
		-- set this to the jobs that will receive dispatch alerts for hotwire/lockpick etc
		"police"
	},
	ForceUnlockJobs = {
		-- set this to the jobs that will be able to force unlock vehicles (ie cops or whatever you want)
		"ambulance",
		"mechanic",
		"police",
	},
}

function AdditionalProximityChecks()
	-- these are additional checks you can do for the proximity locking system
	-- for example if a player is dead or handcuffed you wouldnt want them to be able to lock/unlock a car when a ems comes
    if Bridge.Framework.GetIsPlayerDead() then return false end
	-- if GetResourceState("wasabi_police") == "started" then
	-- 	if exports.wasabi_police:IsHandcuffed() then return false end
	-- end
	-- if GetResourceState("qb-policejob") == "started" then
	-- 	if exports["qb-policejob"]:IsHandcuffed() then return false end
	-- end
	if LocalPlayer.state.inv_busy then return false end
	if LocalPlayer.state.cuffed then return false end
	-- you can add additional checks for proximity locking here.
	-- return false to disable proximity locking on this condition
	return true
end

Config.CustomCarsMissingLabels = {
	-- This is for messed up cars that display "NULL" in the garage menus/When hitting z in a car. 
	-- This will also prevent keyfobs from saying "NULL" as the name of the vehicle
	-- If you already have something fixing these ignore this
	-- This is just a additional option to help you.
	--{model = "spawnname", label = "desired in game name"},
	{model = "aerocab", label = "Aerocab"},
	{model = "blacktop", label = "Blacktop"},
	{model = "brickades", label = "Brickades"},
	{model = "linerunner", label = "linerunner"},
	{model = "vetirs", label = "Vetirs"},
}