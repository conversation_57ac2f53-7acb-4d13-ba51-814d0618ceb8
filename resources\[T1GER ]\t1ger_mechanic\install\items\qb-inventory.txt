    -- Body Parts
    ["t1ger_vehicledoor"] = { ["name"] = "t1ger_vehicledoor", ["label"] = "Vehicle Door", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_vehicledoor.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Used to visually repair damaged vehicle body parts." },
    ["t1ger_vehiclehood"] = { ["name"] = "t1ger_vehiclehood", ["label"] = "Vehicle Hood", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_vehiclehood.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Used to visually repair damaged vehicle body parts." },
    ["t1ger_vehicletrunk"] = { ["name"] = "t1ger_vehicletrunk", ["label"] = "Vehicle Trunk", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_vehicletrunk.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Used to visually repair damaged vehicle body parts." },
    ["t1ger_vehiclewheel"] = { ["name"] = "t1ger_vehiclewheel", ["label"] = "Vehicle Wheel", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_vehiclewheel.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Used to visually repair damaged vehicle body parts." },
    ["t1ger_vehiclewindow"] = { ["name"] = "t1ger_vehiclewindow", ["label"] = "Vehicle Window", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_vehiclewindow.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Used to visually repair damaged vehicle body parts." },
    -- Core Parts
    ["t1ger_alternator"] = { ["name"] = "t1ger_alternator", ["label"] = "Alternator", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_alternator.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Critical vehicle component used for major mechanical repairs." },
    ["t1ger_brakes"] = { ["name"] = "t1ger_brakes", ["label"] = "Brakes", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_brakes.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Critical vehicle component used for major mechanical repairs." },
    ["t1ger_electricmotor"] = { ["name"] = "t1ger_electricmotor", ["label"] = "Electric Motor", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_electricmotor.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Critical vehicle component used for major mechanical repairs." },
    ["t1ger_evbattery"] = { ["name"] = "t1ger_evbattery", ["label"] = "EV Battery", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_evbattery.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Critical vehicle component used for major mechanical repairs." },
    ["t1ger_fuelinjector"] = { ["name"] = "t1ger_fuelinjector", ["label"] = "Fuel Injector", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_fuelinjector.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Critical vehicle component used for major mechanical repairs." },
    ["t1ger_powersteeringpump"] = { ["name"] = "t1ger_powersteeringpump", ["label"] = "Power Steering Pump", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_powersteeringpump.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Critical vehicle component used for major mechanical repairs." },
    ["t1ger_radiator"] = { ["name"] = "t1ger_radiator", ["label"] = "Radiator", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_radiator.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Critical vehicle component used for major mechanical repairs." },
    ["t1ger_transmission"] = { ["name"] = "t1ger_transmission", ["label"] = "Transmission", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_transmission.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Critical vehicle component used for major mechanical repairs." },
    -- Service Parts
    ["t1ger_airfilter"] = { ["name"] = "t1ger_airfilter", ["label"] = "Air Filter", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_airfilter.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Part used during vehicle servicing and regular maintenance." },
    ["t1ger_batterycoolant"] = { ["name"] = "t1ger_batterycoolant", ["label"] = "Battery Coolant", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_batterycoolant.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Part used during vehicle servicing and regular maintenance." },
    ["t1ger_brakefluid"] = { ["name"] = "t1ger_brakefluid", ["label"] = "Brake Fluid", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_brakefluid.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Part used during vehicle servicing and regular maintenance." },
    ["t1ger_brakepad"] = { ["name"] = "t1ger_brakepad", ["label"] = "Brake Pads", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_brakepad.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Part used during vehicle servicing and regular maintenance." },
    ["t1ger_coolant"] = { ["name"] = "t1ger_coolant", ["label"] = "Coolant", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_coolant.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Part used during vehicle servicing and regular maintenance." },
    ["t1ger_drivebelt"] = { ["name"] = "t1ger_drivebelt", ["label"] = "Drive Belt", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_drivebelt.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Part used during vehicle servicing and regular maintenance." },
    ["t1ger_fuelfilter"] = { ["name"] = "t1ger_fuelfilter", ["label"] = "Fuel Filter", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_fuelfilter.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Part used during vehicle servicing and regular maintenance." },
    ["t1ger_hvwiring"] = { ["name"] = "t1ger_hvwiring", ["label"] = "High Voltage Wiring", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_hvwiring.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Part used during vehicle servicing and regular maintenance." },
    ["t1ger_oilfilter"] = { ["name"] = "t1ger_oilfilter", ["label"] = "Oil Filter", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_oilfilter.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Part used during vehicle servicing and regular maintenance." },
    ["t1ger_sparkplugs"] = { ["name"] = "t1ger_sparkplugs", ["label"] = "Spark Plugs", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_sparkplugs.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Part used during vehicle servicing and regular maintenance." },
    ["t1ger_steeringfluid"] = { ["name"] = "t1ger_steeringfluid", ["label"] = "Steering Fluid", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_steeringfluid.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Part used during vehicle servicing and regular maintenance." },
    ["t1ger_tires"] = { ["name"] = "t1ger_tires", ["label"] = "Tires", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_tires.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Part used during vehicle servicing and regular maintenance." },
    ["t1ger_transmissionfluid"] = { ["name"] = "t1ger_transmissionfluid", ["label"] = "Transmission Fluid", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_transmissionfluid.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Part used during vehicle servicing and regular maintenance." },
    -- Kits
    ["t1ger_repairkit"] = { ["name"] = "t1ger_repairkit", ["label"] = "Repair Kit", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_repairkit.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "A useful tool or kit used during mechanic operations." },
    ["t1ger_repairkit_adv"] = { ["name"] = "t1ger_repairkit_adv", ["label"] = "Advanced Repair Kit", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_repairkit_adv.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "A useful tool or kit used during mechanic operations." },
    ["t1ger_carjack"] = { ["name"] = "t1ger_carjack", ["label"] = "Car Jack", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_carjack.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "A useful tool or kit used during mechanic operations." },
    ["t1ger_patchkit"] = { ["name"] = "t1ger_patchkit", ["label"] = "Patch Kit", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_patchkit.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "A useful tool or kit used during mechanic operations." },
    ["t1ger_fuelcan"] = { ["name"] = "t1ger_fuelcan", ["label"] = "Fuel Can", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_fuelcan.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "A useful tool or kit used during mechanic operations." },
    ["t1ger_jumpstarter"] = { ["name"] = "t1ger_jumpstarter", ["label"] = "Jump Starter", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_jumpstarter.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "A useful tool or kit used during mechanic operations." },
    ["t1ger_repairkit_tire"] = { ["name"] = "t1ger_repairkit_tire", ["label"] = "Tire Repair Kit", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_repairkit_tire.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "A useful tool or kit used during mechanic operations." },
    ["t1ger_diagnostictool"] = { ["name"] = "t1ger_diagnostictool", ["label"] = "Vehicle Diagnostic Tool", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_diagnostictool.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "A useful tool or kit used during mechanic operations." },
    ["t1ger_servicebook"] = { ["name"] = "t1ger_servicebook", ["label"] = "Service Book", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_servicebook.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "View service and maintenance history inside a vehicle." },
    -- Prop Emotes
    ["t1ger_roadcone"] = { ["name"] = "t1ger_roadcone", ["label"] = "Road Cone", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_roadcone.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Prop used for mechanic roleplay or roadside work." },
    ["t1ger_toolstrolley"] = { ["name"] = "t1ger_toolstrolley", ["label"] = "Tools Trolley", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_toolstrolley.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Prop used for mechanic roleplay or roadside work." },
    ["t1ger_toolbox"] = { ["name"] = "t1ger_toolbox", ["label"] = "Tool Box", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_toolbox.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Prop used for mechanic roleplay or roadside work." },
    ["t1ger_consign"] = { ["name"] = "t1ger_consign", ["label"] = "Con Sign", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_consign.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Prop used for mechanic roleplay or roadside work." },
    ["t1ger_roadbarrier"] = { ["name"] = "t1ger_roadbarrier", ["label"] = "Road Barrier", ["weight"] = 1, ["type"] = "item", ["image"] = "t1ger_roadbarrier.png", ["unique"] = false, ["useable"] = true, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Prop used for mechanic roleplay or roadside work." },
    -- Materials
    ["scrap_metal"] = {["name"] = "scrap_metal", ["label"] = "Scrap Metal", ["weight"] = 1, ["type"] = "item", ["image"] = "scrap_metal.png", ["unique"] = false, ["useable"] = false, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Material - Scrap Metal"},
    ["steel"] = {["name"] = "steel", ["label"] = "Steel", ["weight"] = 1, ["type"] = "item", ["image"] = "steel.png", ["unique"] = false, ["useable"] = false, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Material - Steel"},
    ["aluminium"] = {["name"] = "aluminium", ["label"] = "Aluminium", ["weight"] = 1, ["type"] = "item", ["image"] = "aluminium.png", ["unique"] = false, ["useable"] = false, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Material - Aluminium"},
    ["plastic"] = {["name"] = "plastic", ["label"] = "Plastic", ["weight"] = 1, ["type"] = "item", ["image"] = "plastic.png", ["unique"] = false, ["useable"] = false, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Material - Plastic"},
    ["rubber"] = {["name"] = "rubber", ["label"] = "Rubber", ["weight"] = 1, ["type"] = "item", ["image"] = "rubber.png", ["unique"] = false, ["useable"] = false, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Material - Rubber"},
    ["electric_scrap"] = {["name"] = "electric_scrap", ["label"] = "Electric Scrap", ["weight"] = 1, ["type"] = "item", ["image"] = "electric_scrap.png", ["unique"] = false, ["useable"] = false, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Material - Electric Scrap"},
    ["glass"] = {["name"] = "glass", ["label"] = "Glass", ["weight"] = 1, ["type"] = "item", ["image"] = "glass.png", ["unique"] = false, ["useable"] = false, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Material - Glass"},
    ["copper"] = {["name"] = "copper", ["label"] = "Copper", ["weight"] = 1, ["type"] = "item", ["image"] = "copper.png", ["unique"] = false, ["useable"] = false, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Material - Copper"},
    ["carbon_fiber"] = {["name"] = "carbon_fiber", ["label"] = "Carbon Fiber", ["weight"] = 1, ["type"] = "item", ["image"] = "carbon_fiber.png", ["unique"] = false, ["useable"] = false, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Material - Carbon Fiber"},
    ["brass"] = {["name"] = "brass", ["label"] = "Brass", ["weight"] = 1, ["type"] = "item", ["image"] = "brass.png", ["unique"] = false, ["useable"] = false, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Material - Brass"},
    ["synthetic_oil"] = {["name"] = "synthetic_oil", ["label"] = "Synthetic Oil", ["weight"] = 1, ["type"] = "item", ["image"] = "synthetic_oil.png", ["unique"] = false, ["useable"] = false, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Material - Synthetic Oil"},
    ["acid"] = {["name"] = "acid", ["label"] = "Synthetic Acid", ["weight"] = 1, ["type"] = "item", ["image"] = "acid.png", ["unique"] = false, ["useable"] = false, ["shouldClose"] = true, ["combinable"] = nil, ["description"] = "Material - Synthetic Acid"},