--- Spawns an invisible, frozen duplicate of the given vehicle.
--- Useful for retrieving default bone positions without modifying the original vehicle.
--- @param originalVehicle number The entity handle of the vehicle to duplicate.
--- @return number # The entity handle of the duplicated vehicle.
local function SpawnDuplicateVehicle(originalVehicle)
    local model = GetEntityModel(originalVehicle)
    local coords = GetEntityCoords(originalVehicle)
    local heading = GetEntityHeading(originalVehicle)

    lib.requestModel(model)

    local duplicateVehicle = CreateVehicle(model, coords, heading, false, false)
    SetEntityVisible(duplicateVehicle, false, false)  -- Make it invisible
    FreezeEntityPosition(duplicateVehicle, true)      -- Prevent it from moving

    return duplicateVehicle
end

--- Calculates the offset from the vehicle center to the given bone.
--- @param vehicle number The vehicle entity handle
--- @param boneName string The bone name to calculate
--- @return vector3 # The relative offset of the bone from the vehicle center
local function GetBoneOffsetFromCenter(vehicle, boneName)
    if not vehicle or not DoesEntityExist(vehicle) then return vector3(0, 0, 0) end

    local boneIndex = GetEntityBoneIndexByName(vehicle, boneName)
    if not boneIndex or boneIndex == -1 then return vector3(0, 0, 0) end

    local vehicleCoords = GetEntityCoords(vehicle)
    local boneCoords = GetEntityBonePosition_2(vehicle, boneIndex)

    return (boneCoords - vehicleCoords)
end

--- Retrieves all doors of a given vehicle, indexed by doorIndex.
--- @param vehicle number The vehicle entity handle.
--- @return table<number, {label: string, boneName: string, boneIndex: number, index: number, coords: vector3, centerOffset: vector3, isDamaged: boolean, isMissing: boolean}>
function GetVehicleDoors(vehicle)
    if not vehicle or not DoesEntityExist(vehicle) then return {} end

    local vehicleDoors = {}
    local centerCoords = GetEntityCoords(vehicle)
    local heading = GetEntityHeading(vehicle)

    -- Spawn invisible duplicate vehicle to get default bone positions
    local duplicateVehicle = SpawnDuplicateVehicle(vehicle)
    if not duplicateVehicle or not DoesEntityExist(duplicateVehicle) then
        return {} -- fallback if duplicateVehicle failed somehow
    end
    local duplicateVehicleCoords = GetEntityCoords(duplicateVehicle)

    -- Iterate vehicle doors
    for doorIndex, doorData in pairs(_API.VehicleDoors or {}) do
        if GetIsDoorValid(vehicle, doorData.index) then
            local boneIndex = GetEntityBoneIndexByName(vehicle, doorData.bone)
            local boneName = doorData.bone

            -- Handle secondary bone if needed
            if (not boneIndex or boneIndex == -1) and doorData.bone2 ~= nil then
                boneIndex = GetEntityBoneIndexByName(vehicle, doorData.bone2)
                boneName = doorData.bone2
            end
            
            if boneIndex and boneIndex ~= -1 then
                local boneCoords = GetEntityBonePosition_2(vehicle, boneIndex)

                vehicleDoors[doorIndex] = {
                    label = doorData.label,
                    bone = boneName,
                    boneIndex = boneIndex,
                    index = doorData.index,
                    coords = boneCoords,
                    centerOffset = GetBoneOffsetFromCenter(duplicateVehicle, boneName),
                    isDamaged = IsVehicleDoorDamaged(vehicle, doorData.index),
                    isMissing = (#(boneCoords - centerCoords) <= 0.1)
                }
            end
        end
    end

    -- Cleanup
    DeleteEntity(duplicateVehicle)

    return vehicleDoors
end

--- Retrieves all wheels of a given vehicle, indexed by wheelIndex.
--- @param vehicle number The vehicle entity handle.
--- @return table<number, {label: string, boneName: string, boneIndex: number, index: number, wheelId: number, health: number, coords: vector3, centerOffset: vector3, isDamaged: boolean, isMissing: boolean}>
function GetVehicleWheels(vehicle)
    if not vehicle or not DoesEntityExist(vehicle) then return {} end

    local numWheels = GetVehicleNumberOfWheels(vehicle)
    if not numWheels or numWheels <= 0 then return {} end

    local vehicleWheels = {}
    local centerCoords = GetEntityCoords(vehicle)
    local heading = GetEntityHeading(vehicle)

    -- Spawn invisible duplicate vehicle to get default bone positions
    local duplicateVehicle = SpawnDuplicateVehicle(vehicle)
    if not duplicateVehicle or not DoesEntityExist(duplicateVehicle) then
        return {} -- fallback if duplicateVehicle failed somehow
    end
    local duplicateVehicleCoords = GetEntityCoords(duplicateVehicle)

    -- Iterate vehicle wheels
    for wheelIndex, wheelData in pairs(_API.VehicleWheels[tostring(numWheels)] or {}) do
        local boneIndex = GetEntityBoneIndexByName(vehicle, wheelData.bone)
        if boneIndex and boneIndex ~= -1 then
            local boneCoords = GetEntityBonePosition_2(vehicle, boneIndex)

            vehicleWheels[wheelIndex] = {
                label = wheelData.label,
                boneName = wheelData.bone,
                boneIndex = boneIndex,
                index = wheelData.index,
                wheelId = wheelData.wheelId,
                health = GetVehicleWheelHealth(vehicle, wheelData.wheelId),
                coords = boneCoords,
                centerOffset = GetBoneOffsetFromCenter(duplicateVehicle, wheelData.bone),
                isDamaged = IsVehicleWheelDamaged(vehicle, wheelData.index),
                isMissing = (#(boneCoords - centerCoords) <= 0.1)
            }
        end
    end

    -- Cleanup
    DeleteEntity(duplicateVehicle)

    return vehicleWheels
end

--- Retrieves all windows of a given vehicle, indexed by windowIndex.
--- @param vehicle number The vehicle entity handle.
--- @return table<number, {label: string, boneName: string, boneIndex: number, index: number, coords: vector3, centerOffset: vector3, isDamaged: boolean, isMissing: boolean}>
function GetVehicleWindows(vehicle)
    if not vehicle or not DoesEntityExist(vehicle) then return {} end

    local vehicleWindows = {}
    local centerCoords = GetEntityCoords(vehicle)
    local heading = GetEntityHeading(vehicle)

    -- Spawn invisible duplicate vehicle to get default bone positions
    local duplicateVehicle = SpawnDuplicateVehicle(vehicle)
    if not duplicateVehicle or not DoesEntityExist(duplicateVehicle) then
        return {} -- fallback if duplicateVehicle failed somehow
    end
    local duplicateVehicleCoords = GetEntityCoords(duplicateVehicle)

    -- Iterate vehicle windows
    for windowIndex, windowData in pairs(_API.VehicleWindows or {}) do
        local boneIndex = GetEntityBoneIndexByName(vehicle, windowData.bone)
        if boneIndex and boneIndex ~= -1 then
            local boneCoords = GetEntityBonePosition_2(vehicle, boneIndex)
            
            vehicleWindows[windowIndex] = {
                label = windowData.label,
                boneName = windowData.bone,
                boneIndex = boneIndex,
                index = windowData.index,
                coords = boneCoords,
                centerOffset = GetBoneOffsetFromCenter(duplicateVehicle, windowData.bone),
                isDamaged = IsVehicleWindowDamaged(vehicle, windowData.index),
                isMissing = (#(boneCoords - centerCoords) <= 0.1)
            }
        end
    end

    -- Cleanup
    DeleteEntity(duplicateVehicle)

    return vehicleWindows
end

--- Checks if a vehicle wheel is damaged, burst or broken off
--- @param vehicle number The vehicle entity handle
--- @param index number The wheel/tyre index (not wheelId)
--- @return boolean
function IsVehicleWheelDamaged(vehicle, index)
    if type(index) ~= "number" and type(index) ~= "string" then return false end
    if not vehicle or not DoesEntityExist(vehicle) then return false end

    local numWheels = GetVehicleNumberOfWheels(vehicle)
    if not numWheels or numWheels <= 0 then return false end

    local vehicleWheels = _API.VehicleWheels[tostring(numWheels)]
    if not vehicleWheels or not vehicleWheels[tostring(index)] then return false end

    local wheelData = vehicleWheels[tostring(index)]

    local isVehicleTyreBurst = IsVehicleTyreBurst(vehicle, wheelData.index, false)
    local isVehicleWheelBrokenOff = IsVehicleWheelBrokenOff(vehicle, wheelData.bone)
    local isVehicleWheelHealthLow = GetVehicleWheelHealth(vehicle, wheelData.wheelId) <= 100.0

    return isVehicleTyreBurst or isVehicleWheelBrokenOff or isVehicleWheelHealthLow
end

--- Checks if a vehicle window is damaged (broken or missing)
--- @param vehicle number The vehicle entity handle
--- @param windowIndex number The window index
--- @return boolean
function IsVehicleWindowDamaged(vehicle, windowIndex)
    if type(windowIndex) ~= "number" then
        print("[IsVehicleWindowDamaged] Invalid type for windowIndex. Must be a number")
        return false
    end

    if not vehicle or not DoesEntityExist(vehicle) then
        return false
    end

    return not IsVehicleWindowIntact(vehicle, windowIndex)
end

--- Returns whether given wheel is broken off from the vehicle
--- @param vehicle number The vehicle entity handle.
--- @param wheelBone string The bone name of the given wheel
--- @return boolean
function IsVehicleWheelBrokenOff(vehicle, wheelBone)
    if type(wheelBone) ~= "string" or wheelBone == "" then
        print("[IsVehicleWheelBrokenOff] Invalid type for wheelBone. Must be a non-empty string")
        return false
    end

    if not vehicle or not DoesEntityExist(vehicle) then return false end

    local boneIndex = GetEntityBoneIndexByName(vehicle, wheelBone)
    if not boneIndex or boneIndex == -1 then
        print(string.format("[IsVehicleWheelBrokenOff] Boneindex for given wheel bone '%s' not found", wheelBone))
        return false
    end

    local boneCoords = GetEntityBonePosition_2(vehicle, boneIndex)
    local centerCoords = GetEntityCoords(vehicle)

    if #(boneCoords - centerCoords) <= 0.1 then
        return true -- Wheel is missing
    end

    return false
end

--- Calculates the real-time world position for a bone using centerCoords, heading, and centerOffset
--- @param centerCoords vector3 The center coords of the vehicle
--- @param heading number The heading of the vehicle
--- @param centerOffset vector3 The local offset of the bone from center
--- @return vector3
function CalculateBoneWorldCoords(centerCoords, heading, centerOffset)
    return GetOffsetFromCoordAndHeadingInWorldCoords(centerCoords.x, centerCoords.y, centerCoords.z, heading, centerOffset.x, centerOffset.y, centerOffset.z)
end

--- Finds the closest vehicle wheel to the player
--- @param vehicle number The vehicle entity handle
--- @param maxDistance number The max distance to consider
--- @param checkDamage boolean? Only return wheels that are damaged or missing
--- @return table? #Returns nil if closestWheel not found
function GetVehicleClosestWheel(vehicle, maxDistance, checkDamage)
    if not vehicle or not DoesEntityExist(vehicle) then return nil end

    maxDistance = maxDistance or 2.5

    local wheels = GetVehicleWheels(vehicle)
    local closestWheel = nil
    local closestDistance = maxDistance
    local centerCoords = GetEntityCoords(vehicle)
    local heading = GetEntityHeading(vehicle)

    for wheelIndex, wheelData in pairs(wheels) do
        if wheelData and wheelData.boneIndex and wheelData.coords then

            -- if checking for damage, skip non-damaged/non-missing wheels
            if checkDamage and not (wheelData.isDamaged or wheelData.isMissing) then
                goto continue
            end

            -- get correct boneCoords
            local defaultBoneCoords = CalculateBoneWorldCoords(centerCoords, heading, wheelData.centerOffset) or nil
            local boneCoords = defaultBoneCoords or wheelData.coords

            local dist = #(coords - boneCoords)
            if dist < closestDistance then
                closestDistance = dist
                closestWheel = wheelData
                closestWheel.defaultBoneCoords = defaultBoneCoords
            end
        end
        ::continue::
    end

    return closestWheel
end

--- Finds the closest vehicle window to the player.
--- Only returns a window if its corresponding door exists and is not missing.
--- @param vehicle number The vehicle entity handle
--- @param maxDistance number The max distance to consider
--- @param checkDamage boolean? Only return windows that are damaged or missing
--- @return table? #Returns nil if closestWindow not found
function GetVehicleClosestWindow(vehicle, maxDistance, checkDamage)
    if not vehicle or not DoesEntityExist(vehicle) then return nil end

    maxDistance = maxDistance or 2.5
    
    local vehicleClass = GetVehicleClass(vehicle)
    if vehicleClass == 10 or vehicleClass == 17 or vehicleClass == 20 then
        maxDistance = (maxDistance * 1.8)
    end

    local windows = GetVehicleWindows(vehicle)
    local doors = GetVehicleDoors(vehicle) -- cache all doors!
    local closestWindow = nil
    local closestDistance = maxDistance
    local centerCoords = GetEntityCoords(vehicle)
    local heading = GetEntityHeading(vehicle)

    for windowIndex, windowData in pairs(windows) do
        if windowData and windowData.boneIndex and windowData.coords then
            -- Get corresponding door
            local correspondingDoor = doors[windowIndex]

            -- Check if door is missing
            if correspondingDoor and correspondingDoor.isMissing then
                goto continue
            end

            -- if checking for damage, skip non-damaged/non-missing windows
            if checkDamage and not (windowData.isDamaged or windowData.isMissing) then
                goto continue
            end

            -- get correct boneCoords
            local defaultBoneCoords = CalculateBoneWorldCoords(centerCoords, heading, windowData.centerOffset) or nil
            local boneCoords = defaultBoneCoords or windowData.coords

            local dist = #(coords - boneCoords)
            if dist < closestDistance then
                closestDistance = dist
                closestWindow = windowData
                closestWindow.defaultBoneCoords = defaultBoneCoords
            end
        end
        ::continue::
    end

    return closestWindow
end

--- Finds the closest vehicle door (not hood or trunk) to the player.
--- @param vehicle number The vehicle entity handle
--- @param maxDistance number The max distance to consider
--- @param checkDamage boolean? Only return doors that are damaged or missing
--- @return table? #Returns nil if closestDoor not found
function GetVehicleClosestDoor(vehicle, maxDistance, checkDamage)
    if not vehicle or not DoesEntityExist(vehicle) then return nil end

    maxDistance = maxDistance or 2.5

    -- Adjust max distance for certain vehicle classes
    local vehicleClass = GetVehicleClass(vehicle)
    if vehicleClass == 10 or vehicleClass == 17 or vehicleClass == 20 then
        maxDistance = (maxDistance * 1.8)
    end

    local doors = GetVehicleDoors(vehicle)
    local closestDoor = nil
    local closestDistance = maxDistance
    local centerCoords = GetEntityCoords(vehicle)
    local heading = GetEntityHeading(vehicle)

    for doorIndex, doorData in pairs(doors) do
        -- if hood (4) or trunk (5) skip
        if doorData.index == 4 or doorData.index == 5 then
            goto continue
        end
        
        if doorData and doorData.boneIndex and doorData.coords then

            -- if checking for damage, skip non-damaged/non-missing doors
            if checkDamage and not (doorData.isDamaged or doorData.isMissing) then
                goto continue
            end

            -- get correct boneCoords
            local defaultBoneCoords = CalculateBoneWorldCoords(centerCoords, heading, doorData.centerOffset) or nil
            local boneCoords = defaultBoneCoords or doorData.coords

            local dist = #(coords - boneCoords)
            if dist < closestDistance then
                closestDistance = dist
                closestDoor = doorData
                closestDoor.defaultBoneCoords = defaultBoneCoords
            end
        end

        ::continue::
    end

    return closestDoor
end

--- Finds the closest vehicle hood to the player. Automatically detects if the hood is at the front or rear.
--- @param vehicle number The vehicle entity handle
--- @param maxDistance number The max distance to consider
--- @param checkDamage boolean? Only return hood if damaged or missing
--- @return table? #Returns nil if hood not found
function GetVehicleClosestHood(vehicle, maxDistance, checkDamage)
    if not vehicle or not DoesEntityExist(vehicle) then return nil end

    maxDistance = maxDistance or 2.5

    local vehicleClass = GetVehicleClass(vehicle)
    if vehicleClass == 10 or vehicleClass == 17 or vehicleClass == 20 then
        maxDistance = maxDistance * 1.8
    end

    local centerCoords = GetEntityCoords(vehicle)
    local heading = GetEntityHeading(vehicle)

    local doors = GetVehicleDoors(vehicle)
    local hood = doors and doors[tostring(4)] -- door index 4 = hood

    local min, max = GetModelDimensions(GetEntityModel(vehicle))
    local frontPos = GetOffsetFromEntityInWorldCoords(vehicle, 0.0, max.y, 0.3)
    
    if hood and hood.boneIndex then
        if checkDamage and not (hood.isDamaged or hood.isMissing) then
            return nil
        end

        local hoodCoords = hood.isMissing and CalculateBoneWorldCoords(centerCoords, heading, hood.centerOffset) or hood.coords

        local min, max = GetModelDimensions(GetEntityModel(vehicle))
        local rearPos = GetOffsetFromEntityInWorldCoords(vehicle, 0.0, min.y, 0.3)

        local distanceToFront = #(hoodCoords - frontPos)
        local distanceToRear = #(hoodCoords - rearPos)

        local correctBoneCoords = (distanceToFront < distanceToRear) and frontPos or rearPos

        local coords = GetEntityCoords(PlayerPedId())
        local dist = #(coords - correctBoneCoords)

        if dist <= maxDistance then
            hood.defaultBoneCoords = correctBoneCoords
            return hood
        end
    else
        -- Fallback: Use front position directly
        local coords = GetEntityCoords(PlayerPedId())
        local dist = #(coords - frontPos)
        maxDistance = maxDistance + 0.2

        if dist <= maxDistance then
            return {
                defaultBoneCoords = frontPos,
                isFallback = true
            }
        end
    end

    return nil
end

--- Finds the closest vehicle trunk to the player. Automatically prefers rear side of vehicle.
--- @param vehicle number The vehicle entity handle
--- @param maxDistance number The max distance to consider
--- @param checkDamage boolean? Only return trunk if damaged or missing
--- @return table? #Returns nil if trunk not found
function GetVehicleClosestTrunk(vehicle, maxDistance, checkDamage)
    if not vehicle or not DoesEntityExist(vehicle) then return nil end

    maxDistance = maxDistance or 2.5

    local vehicleClass = GetVehicleClass(vehicle)
    if vehicleClass == 10 or vehicleClass == 17 or vehicleClass == 20 then
        maxDistance = maxDistance * 1.8
    end

    local doors = GetVehicleDoors(vehicle)
    local centerCoords = GetEntityCoords(vehicle)
    local heading = GetEntityHeading(vehicle)

    local trunk = doors[tostring(5)] -- door index 5 = trunk
    if not trunk or not trunk.boneIndex then
        return nil
    end

    if checkDamage and not (trunk.isDamaged or trunk.isMissing) then
        return nil
    end

    -- If trunk is missing, fallback to centerOffset calculated world coords
    local trunkCoords
    if trunk.isMissing then
        trunkCoords = CalculateBoneWorldCoords(centerCoords, heading, trunk.centerOffset)
    else
        trunkCoords = trunk.coords
    end

    -- Detect front or rear
    local min, max = GetModelDimensions(GetEntityModel(vehicle))
    local frontPos = GetOffsetFromEntityInWorldCoords(vehicle, 0.0, max.y, 0.3)
    local rearPos = GetOffsetFromEntityInWorldCoords(vehicle, 0.0, min.y, 0.3)

    local distanceToFront = #(trunkCoords - frontPos)
    local distanceToRear = #(trunkCoords - rearPos)

    local correctBoneCoords = (distanceToRear < distanceToFront) and rearPos or frontPos

    -- Final distance check against player
    local dist = #(coords - correctBoneCoords)
    if dist <= maxDistance then
        trunk.defaultBoneCoords = correctBoneCoords
        return trunk
    end

    return nil
end

--- Plays repair sound
function RepairSound()
    PlaySoundFromEntity(-1, 'Hydraulics_Down', player, 'Lowrider_Super_Mod_Garage_Sounds', true, 0)
    Wait(100)
    PlaySoundFromEntity(-1, "Bar_Unlock_And_Raise", player, "DLC_IND_ROLLERCOASTER_SOUNDS", true, 0)
    Wait(200)
    PlaySoundFromEntity(-1, "Ride_Stop", player, "DLC_IND_ROLLERCOASTER_SOUNDS", true, 0)

    --PlaySoundFromEntity(-1, "Bar_Unlock_And_Raise", player, "DLC_IND_ROLLERCOASTER_SOUNDS", true, 0)
    --PlaySoundFromEntity(-1, "Bar_Lower_And_Lock", player, "DLC_IND_ROLLERCOASTER_SOUNDS", true, 0)
    --PlaySoundFromEntity(-1, "Engine_fail", player, "DLC_PILOT_ENGINE_FAILURE_SOUNDS", true, 0)
    --PlaySoundFromEntity(-1, "Ride_Stop", player, "DLC_IND_ROLLERCOASTER_SOUNDS", true, 0)
end