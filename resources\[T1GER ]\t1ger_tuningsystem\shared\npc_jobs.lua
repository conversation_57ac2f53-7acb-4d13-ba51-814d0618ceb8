Config.SalvageJob = {
	Enable = true, -- enable/disable this job
	Cooldown = {enable = true, time = 5}, -- enable/disable cooldown. time in minutes.
	Menu = {title = 'Salvage Car', icon = 'car-burst', description = 'Salvage a junk car to retrive materials - and maybe a chance for some valuable and useable parts?'},
	-- cars that will be used in the scrambler to spawn:
	JunkCars = {'sultan', 'asterope', 'club', 'exemplar', 'felon', 'fugitive'}, 
	Blip = {label = 'Junk Car', sprite = 380, color = 51, scale = 0.65, route = {enable = true, color = 51}},
	SkillCheck = {enable = true, difficulty = {'easy', 'easy', 'easy'}, inputs = {'w', 'a', 's', 'd'}},
	AdvNotify = {textureDict = 'CHAR_PROPERTY_CAR_SCRAP_YARD', textureName = 'CHAR_PROPERTY_CAR_SCRAP_YARD'},
	Target = {label = 'Salvage %s', icon = 'fa-solid fa-hammer'},

	Reward = {
		wheel = { -- reward when salvaging wheels:
			['mod_rim'] = {chance = 20, min = 1, max = 1},
			['mod_stocktires'] = {chance = 20, min = 1, max = 1},
			['mod_tyresmoke'] = {chance = 15, min = 1, max = 1},
			['mod_bullettires'] = {chance = 1, min = 1, max = 1},
			['mod_drifttires'] = {chance = 5, min = 1, max = 1},
			['rubber'] = {chance = 100, min = 1, max = 5},
			['aluminium'] = {chance = 100, min = 1, max = 5},
			['steel'] = {chance = 80, min = 1, max = 4},
		},
		door = { -- reward when salvaging doors/hood/trunk:
			['mod_frontbumper'] = {chance = 20, min = 1, max = 1},
			['mod_rearbumper'] = {chance = 20, min = 1, max = 1},
			['mod_spoiler'] = {chance = 20, min = 1, max = 1},
			['mod_sideskirt'] = {chance = 20, min = 1, max = 1},
			['mod_hood'] = {chance = 20, min = 1, max = 1},
			['mod_fender'] = {chance = 20, min = 1, max = 1},
			['mod_grille'] = {chance = 20, min = 1, max = 1},
			['mod_exterior'] = {chance = 20, min = 1, max = 1},
			['plastic'] = {chance = 100, min = 3, max = 6},
			['scrap_metal'] = {chance = 40, min = 1, max = 3},
			['copper'] = {chance = 70, min = 2, max = 4},
		},
	},

	Locations = {
		-- Scrapyard La Puerta:
		{pos = vector4(-451.14, -1723.09, 18.66, 348.66), inUse = false},
		{pos = vector4(-450.36, -1691.61, 18.95, 155.90), inUse = false},
		{pos = vector4(-468.30, -1675.68, 19.05, 178.58), inUse = false},
		{pos = vector4(-425.61, -1687.68, 19.01, 158.74), inUse = false},
		{pos = vector4(-496.40, -1753.30, 18.31, 266.45), inUse = false},
		{pos = vector4(-551.26, -1703.73, 19.00, 212.59), inUse = false},
		{pos = vector4(-581.59, -1696.18, 19.06, 354.33), inUse = false},
	}
}

Config.MobileTuningJob = {
	Enable = true, -- enable/disable this job
	Cooldown = {enable = true, time = 5}, -- enable/disable cooldown. time in minutes.
	Menu = {title = 'Mobile Tuning', icon = 'spray-can', description = 'A customer needs on-site performance enhancements and/or cosmetic installations.'},
	-- cars that will be used in the scrambler to spawn:
	Cars = {'euros', 'zr350', 'sultan3', 'rt3000', 'previon', 'jester3'},
	Peds = {'a_m_m_bevhills_02', 'a_m_m_socenlat_01', 'a_m_y_bevhills_01'},
	Blip = {label = 'Mobile Tuning Job', sprite = 643, color = 51, scale = 0.65, route = {enable = true, color = 51}},
	AdvNotify = {textureDict = 'CHAR_CARSITE3', textureName = 'CHAR_CARSITE3'},
	Target = {label = 'Talk with NPC', icon = 'fa-solid fa-comments'},
	Reward = {min = 500, max = 5000},

	Locations = {
		{pos = vector4(-237.94, -2077.31, 27.61, 31.18), inUse = false},
		{pos = vector4(-47.36, -2103.38, 16.69, 198.42), inUse = false},
		{pos = vector4(-315.57, -2736.34, 5.99, 130.39), inUse = false},
		{pos = vector4(256.57, -3314.43, 5.77, 184.25), inUse = false},
		{pos = vector4(1125.61, -3138.51, 5.89, 0.0), inUse = false},
		{pos = vector4(1000.90, -1955.57, 30.88, 175.74), inUse = false},
		{pos = vector4(-1679.27, -924.27, 7.81, 323.14), inUse = false},
		{pos = vector4(1131.25, 255.89, 80.84, 56.69), inUse = false},
		{pos = vector4(853.26, -895.72, 25.30, 266.45), inUse = false},
		{pos = vector4(22.66, -1068.55, 38.14, 73.70), inUse = false},
		{pos = vector4(-725.80, -405.12, 34.99, 87.87), inUse = false},
		{pos = vector4(-2314.77, 449.45, 174.45, 170.07), inUse = false},
	}
}