Shop = {}
Shop.__index = Shop

--- Creates a new Shop instance
---@param id number Shop ID
---@param data table Shop data from the database
---@return Shop
function Shop:New(id, data)
    local self = setmetatable({}, Shop)

    -- Core Shop Data
    self.id = id
    self.name = data.name
    self.job = data.job
    self.blip = data.blip
    self.owner = data.owner
    self.account = data.account
    self.employees = data.employees
    self.markers = data.markers
    self.billing = data.billing
    self.for_sale = (data.for_sale == 1 or data.for_sale == true) and true or false
    self.sale_price = data.sale_price or nil

    return self
end

--- Updates a general shop attribute (non-marker related).
--- @param key string The name of the attribute to update (e.g., "name", "sale_price", "owner").
--- @param value any The new value for the attribute.
function Shop:UpdateShopData(key, value)
    if type(key) ~= "string" or key == "" then
        error("Shop:UpdateShopData - Invalid key. Expected a non-empty string.")
    end

    self[key] = value
    local sqlValue = (type(value) == "table") and json.encode(value) or value

    -- Update database
    local query = "UPDATE t1ger_mechanic SET " .. key .. " = ? WHERE id = ?"
    MySQL.update(query, {sqlValue, self.id})

    -- Trigger client event with only the changed data
    TriggerClientEvent("t1ger_mechanic:client:updateShopData", -1, self.id, key, value)
end

--- Renames the shop
---@param name string New shop name
function Shop:SetName(name)
    self:UpdateShopData("name", name)
end

--- Changes shop ownership
---@param newOwner string New owner identifier
function Shop:SetOwner(newOwner)
    self:UpdateShopData("owner", newOwner)
end

--- Removes shop owner
function Shop:RemoveOwner()
    self:UpdateShopData("owner", nil)
end

--- Adds money to the shop account
---@param amount number Amount to add
function Shop:AddMoney(amount)
    if type(amount) ~= 'number' then 
        return error("[Shop:RemoveMoney] Invalid amount parameter. Must be a number")
    end
    if Config.Shop.JobAccount then
        _API.JobAccount.AddMoney(self.job.name, amount)
    end
    self.account = self.account + amount
    self:UpdateShopData("account", self.account)
end

--- Removes money from the shop account
---@param amount number Amount to remove
function Shop:RemoveMoney(amount)
    if type(amount) ~= 'number' then 
        return error("[Shop:RemoveMoney] Invalid amount parameter. Must be a number")
    end
    if Config.Shop.JobAccount then
        _API.JobAccount.RemoveMoney(self.job.name, amount)
    end
    self.account = math.max(0, self.account - amount)
    self:UpdateShopData("account", self.account)
end

--- Sets a specific amount in the shop account
---@param amount number New account balance
function Shop:SetMoney(amount)
    if type(amount) ~= 'number' then 
        return error("[Shop:SetMoney] Invalid amount parameter. Must be a number")
    end
    if Config.Shop.JobAccount then
        _API.JobAccount.SetMoney(self.job.name, amount)
    end
    self.account = math.max(0, amount)
    self:UpdateShopData("account", self.account)
end

--- Retrieves the current shop account balance
---@return number Account balance
function Shop:GetAccount()
    if Config.Shop.JobAccount then
        self.account = _API.JobAccount.GetBalance(self.job.name)
    end
    return self.account
end

--- Get the highest boss-grade; fallback to highest job grade if `isboss` is missing.
---@return number Boss grade (highest found)
function Shop:GetBossGrade()
    local highestBossGrade = nil
    local highestGrade = nil

    for _, v in pairs(self.job.grades) do
        if v.isboss then
            if not highestBossGrade or v.grade > highestBossGrade then
                highestBossGrade = v.grade
            end
        end
        if not highestGrade or v.grade > highestGrade then
            highestGrade = v.grade
        end
    end
    return highestBossGrade or highestGrade
end

--- Get an employees of the shop
---@param identifier string Player identifier
---@return boolean isEmployee, table employee (Name, Identifier, Job Grade), integer index
function Shop:GetEmployee(identifier)
    if self.employees and next(self.employees) then
        for i = 1, #self.employees do
            if self.employees[i].identifier == identifier then 
                return true, self.employees[i], i
            end
        end
    end
    return false
end

--- Hire/Add a new employee.
---@param identifier string Player's unique identifier
---@param grade number Job grade
---@param setJob boolean Whether to set the player's job
---@return boolean success
function Shop:AddEmployee(identifier, grade, setJob)
    local isEmployee = self:GetEmployee(identifier)
    if isEmployee then return false end

    if not self.job.grades[tostring(grade)] then -- Validate parsed job grade
        grade = 0
    end

    table.insert(self.employees, {
        identifier = identifier,
        name = _API.Player.GetCharacterName(identifier),
        grade = grade
    })

    if setJob then
        local targetPlayer = _API.Player.GetFromIdentifier(identifier)
        if targetPlayer then
            local targetSrc = _API.Player.GetSource(identifier)
            if targetSrc and type(targetSrc) == "number" and targetSrc > 0 then
                _API.Player.SetJob(targetSrc, self.job.name, grade)
            end
        end
    end

    self:UpdateShopData("employees", self.employees)
    return true
end

--- Fire/Remove an employee.
--- @param identifier string Player's unique identifier
--- @param setJob boolean Whether to reset the player's job
--- @return boolean success
function Shop:RemoveEmployee(identifier, setJob)
    local isEmployee, employee, index = self:GetEmployee(identifier)
    if not isEmployee then return false end

    table.remove(self.employees, index)

    if setJob then
        local targetPlayer = _API.Player.GetFromIdentifier(identifier)
        if targetPlayer then
            local targetSrc = _API.Player.GetSource(identifier)
            if targetSrc and type(targetSrc) == "number" and targetSrc > 0 then
                _API.Player.RemoveFromJob(targetSrc, self.job.name)
            end
        else
            _API.Player.RemoveFromJobOffline(identifier, self.job.name)
        end
    end

    self:UpdateShopData("employees", self.employees)
    return true 
end

--- Promote or demote an employee.
--- @param identifier string Player's unique identifier
--- @param jobGrade number New job grade
--- @param setJob boolean Whether to set the player's job
--- @return boolean success
function Shop:UpdateEmployee(identifier, jobGrade, setJob)
    local isEmployee, employee, index = self:GetEmployee(identifier)
    if not isEmployee then return false end

    local newGrade = self.job.grades[tostring(jobGrade)] and self.job.grades[tostring(jobGrade)].grade or 0
    employee.grade = newGrade

    if setJob then
        local targetPlayer = _API.Player.GetFromIdentifier(identifier)
        if targetPlayer then
            local targetSrc = _API.Player.GetSource(identifier)
            if targetSrc and type(targetSrc) == "number" and targetSrc > 0 then
                _API.Player.SetJob(targetSrc, self.job.name, newGrade)
            end
        else
            _API.Player.SetJobOffline(identifier, self.job.name, newGrade)
        end
    end

    self:UpdateShopData("employees", self.employees)
    return true
end

--- Generates a random number with a specified length.
---@param length number Number of digits in the random number (default: 4)
---@return number Random number with the specified length
local function GetRandomNumber(length)
    length = length or 5 -- Default to 4 digits if not provided
    local min = 10^(length-1)  -- Smallest number (e.g., 1000 for 4 digits)
    local max = (10^length)-1   -- Largest number (e.g., 9999 for 4 digits)
    return math.random(min, max)
end

--- Generates a random string of uppercase letters with a specified length.
---@param length number Number of characters in the random string (default: 5)
---@return string Random string with the specified length
local function GetRandomLetters(length)
    length = length or 5 -- Default to 5 letters if not provided
    local letters = ""
    for _ = 1, length do
        -- ASCII range for uppercase A-Z is 65-90
        local char = string.char(math.random(65, 90))
        letters = letters .. char
    end
    return letters
end

--- Updates a specific marker or marker class for the shop.
--- @param class string The class of marker to update (e.g., "storage", "workbay").
--- @param markerId string|nil The specific marker ID to update (optional).
--- @param value any The new marker data (or nil to remove).
function Shop:UpdateMarkerData(class, markerId, value)
    if type(class) ~= "string" or class == "" then
        error("[Shop:UpdateMarkerData] Invalid class. Expected a non-empty string.")
    end
    if type(markerId) ~= "string" or markerId == "" then
        error("[Shop:UpdateMarkerData] Invalid markerId. Expected a non-empty string.")
    end

    -- Ensure markers:
    self.markers = self.markers or {}
    
    -- Ensure marker class exists
    self.markers[class] = self.markers[class] or {}

    if value == nil then
        -- Remove the specific marker
        self.markers[class][markerId] = nil
        print(("Removed markerId %s from class %s in shop %d"):format(markerId, class, self.id))
    else
        -- Update only the affected marker
        self.markers[class][markerId] = value
        print(("Updated markerId %s in class %s for shop %d"):format(markerId, class, self.id))
    end

    -- Save updated markers to the database
    local sqlValue = json.encode(self.markers)
    local query = "UPDATE t1ger_mechanic SET markers = ? WHERE id = ?"
    MySQL.update(query, {sqlValue, self.id})

    -- Trigger client event for marker updates only
    TriggerClientEvent("t1ger_mechanic:client:updateMarkerData", -1, self.id, class, markerId, value)
end

--- Generate a unique marker ID for a shop (ensures no conflicts with stashes).
---@param shopId number Shop ID
---@param class string Marker class (e.g., "storage", "workbench")
---@param markers table Existing markers table
---@return string Unique marker ID
function Shop:GenerateMarkerId(shopId, class, markers)
    local markerId
    local attempts = 0
    repeat
        math.randomseed(os.time() + math.random(1000, 9999)) -- Improve randomness
        markerId = string.format("mechanic%s_%s_%s", shopId, class, GetRandomNumber(4))
        attempts = attempts + 1
    until (not markers[class] or not markers[class][markerId]) or attempts > 100 -- Limit retries

    return markerId
end

--- Check if a marker ID already exists in the shop.
---@param class string Marker class
---@param markerId string Marker ID
---@return boolean exists
function Shop:MarkerExists(class, markerId)
    return self.markers[class] and self.markers[class][markerId] ~= nil
end

--- Create a new marker in the shop.
---@param class string Marker class (e.g., "storage", "workbench")
---@param data table Marker properties {coords, name, blip, drawMarker}
---@param markerId string|nil Optional predefined marker ID (used when updating markers)
---@return string|nil markerId (nil if failed)
function Shop:CreateMarker(class, data, markerId)
    -- Ensure marker config exists
    if not Config.Shop.Markers[class] then
        return error(string.format("[Shop:CreateMarker] Config doesn't exist for marker class '%s'", class))
    end
    if not self.markers[class] then self.markers[class] = {} end -- Create category if missing

    -- If markerId is not provided, generate a new one
    if not markerId then
        markerId = self:GenerateMarkerId(self.id, class, self.markers)
    end

    -- Ensure no duplicate markers exist
    if self:MarkerExists(class, markerId) then
        return error(string.format("[Shop:CreateMarker] Marker with markerId: '%s' already exists...", markerId))
    end

    data.id = markerId -- Assign marker ID
    self.markers[class][markerId] = data -- Store marker in the shop

    -- if storage:
    if class == "storage" then
        self.markers[class][markerId].stash = {
            id = markerId,
            label = markerId,
            slots = data.stash.slots or Config.Shop.Storage.slots,
            weight = data.stash.weight or Config.Shop.Storage.weight,
            owner = nil -- make sure its accessable by everyone!!
        }
        local stash = self.markers[class][markerId].stash
        _API.Stash.Create(stash.id, stash.label, stash.slots, stash.weight, stash.owner)
    end

    -- Update Marker Data:
    self:UpdateMarkerData(class, markerId, self.markers[class][markerId])

    return markerId
end

--- Delete a marker from the shop.
---@param class string Marker class
---@param markerId string Marker ID
---@return boolean success
function Shop:DeleteMarker(class, markerId)
    if not self:MarkerExists(class, markerId) then return false end -- Check if marker exists

    self.markers[class][markerId] = nil -- Remove marker

    -- If the marker class is now empty, remove the entire entry
    if next(self.markers[class]) == nil then
        self.markers[class] = nil
    end

    -- Update Marker Data:
    self:UpdateMarkerData(class, markerId, nil) -- Sync to database and clients

    return true
end

--- Update an existing marker (keeps same markerId).
---@param class string Marker class
---@param markerId string Marker ID
---@param newData table New marker properties {coords, name, blip, drawMarker}
---@return boolean success
function Shop:UpdateMarker(class, markerId, newData)
    if not self:MarkerExists(class, markerId) then return false end -- Ensure marker exists

    -- Cache the marker ID, delete the old marker, then create a new one with same ID
    self:DeleteMarker(class, markerId)
    local newMarkerId = self:CreateMarker(class, newData, markerId)

    return newMarkerId == markerId -- Ensure the marker ID remains unchanged
end

---Returns all bills
---@return table bills table containing bills indexed by billingNumber
function Shop:GetBills()
    return self.billing
end

---Creates a new bill with sender, receiver, amount, note, date and time
---@param sender string identifier of the sender
---@param receiver string identifier of the receiver
---@param amount number amount of money
---@param note? string (optional) attach a note to the bill?
function Shop:CreateBill(sender, receiver, amount, note)
    if not self.billing then self.billing = {} end

    --- Returns a unique generated billing number.
    ---@param table bills billing data
    ---@return string billingNumber a unique billing number
    local function GenerateBillingNumber(bills)
        local billingNumber = nil
        local attempts = 0
        repeat
            billingNumber = "#" .. string.upper(GetRandomLetters(2)) .. "-" .. tostring(GetRandomNumber(5))
            attempts = attempts + 1
        until not bills[billingNumber] or attempts > 1000

        return tostring(billingNumber)
    end

    -- generate billing number:
    local billingNumber = GenerateBillingNumber(self.billing)

    -- store billing data:
    self.billing[billingNumber] = {
        sender = _API.Player.GetCharacterName(sender),
        receiver = _API.Player.GetCharacterName(receiver),
        amount = amount,
        note = note,
        date = os.date("%d-%m-%Y"),
        time = os.date("%H:%M")
    }

    -- save & sync
    self:SaveBill(billingNumber)
end

---Deletes a bill from history
---@param billingNumber string unique billing id
function Shop:DeleteBill(billingNumber)
    if not self.billing or not self.billing[billingNumber] then return end

    -- delete:
    self.billing[billingNumber] = nil

    -- save & sync
    self:SaveBill(billingNumber)
end

---Saves billing data to database and syncs the specific bill with clients
---@param billingNumber string unique billing id
function Shop:SaveBill(billingNumber)
    -- Save updated billing to the database
    MySQL.update("UPDATE t1ger_mechanic SET billing = ? WHERE id = ?", {json.encode(self.billing), self.id})

    -- Trigger client event for billing updates only
    TriggerClientEvent("t1ger_mechanic:client:updateBilling", -1, self.id, billingNumber, self.billing[billingNumber])
end

--- Lists the shop for sale.
---@param price number Sale price
---@return boolean success
function Shop:ListForSale(price)
    if type(price) ~= "number" or price < 0 then return false end -- Prevent invalid prices

    self:UpdateShopData("for_sale", true)
    self:UpdateShopData("sale_price", price)

    return true
end

--- Cancels a shop sale, removing it from the market.
---@return boolean success
function Shop:CancelSale()
    if not self.for_sale then return false end -- Ensure it"s actually listed for sale

    self:UpdateShopData("for_sale", false)
    self:UpdateShopData("sale_price", nil)

    return true
end

--- Allows a player to purchase the shop.
---@param buyerSrc number Buyer source ID
---@return boolean success
function Shop:BuyShop(buyerSrc)
    if not self.for_sale then return false end -- Ensure shop is for sale

    local buyerIdentifier = _API.Player.GetIdentifier(buyerSrc)
    if not buyerIdentifier or self.owner == buyerIdentifier then return false end -- Prevent self-purchase

    -- Ensure buyer has enough funds
    local playerMoney = _API.Player.GetMoney(buyerSrc, "bank")
    if playerMoney < self.sale_price then return false end

    -- Deduct the purchase amount
    _API.Player.RemoveMoney(buyerSrc, self.sale_price, "bank")

    -- Transfer ownership and update job
    self:UpdateShopData("owner", buyerIdentifier)
    self:UpdateShopData("for_sale", false)
    self:UpdateShopData("sale_price", nil)

    -- Set the new owner as boss (highest job grade)
    _API.Player.SetJob(buyerSrc, self.job.name, self:GetBossGrade())

    return true
end