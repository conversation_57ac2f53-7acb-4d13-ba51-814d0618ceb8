import{r as o,u as Xs,j as e,c as Ns,t as da,a as xa,J as ts,b as $s,S as Dn,d as ua,L as tn,e as ma,f as ha,g as Ds,P as ja,h as js,i as ga,F as Tn,T as pa,U as fa,k as _a,H as ba,l as va,C as Na,R as Pn,I as $n,m as En,n as Zs,M as ya,O as Ln,o as zn,p as wa,X as An,q as Rn,D as Mn,s as Ca,v as Sa,w as an,A as On,x as Hs,y as ka,z as Vn,B as Ia,E as Da,G as Fn,K as Js,N as de,Q as qn,V as rs,W as Zn,Y as Ta,Z as as,_ as Oe,$ as Hn,a0 as Un,a1 as Gn,a2 as Pa,a3 as Kn,a4 as $a,a5 as vs,a6 as Bn,a7 as Ea,a8 as ps,a9 as Yn,aa as Wn,ab as Xn,ac as La,ad as Jn,ae as za,af as Qn,ag as et,ah as Aa,ai as Ra,aj as st,ak as Ma,al as Oa,am as Va,an as Fa,ao as nt,ap as tt,aq as qa,ar as at,as as Za,at as Ha,au as rt,av as Ua,aw as re,ax as q,ay as B,az as Ga,aA as Ve,aB as Fe,aC as oe,aD as we,aE as Ce,aF as Se,aG as ke,aH as Ie,aI as De,aJ as fe,aK as le,aL as ze,aM as qe,aN as Ze,aO as He,aP as Ue,aQ as xe,aR as Te,aS as Pe,aT as it,aU as Ae,aV as lt,aW as Ka,aX as ct,aY as ot,aZ as dt,a_ as xt,a$ as ut,b0 as Ba,b1 as mt,b2 as ht,b3 as Ya,b4 as Wa,b5 as rn,b6 as jt,b7 as Xa,b8 as Ge,b9 as je,ba as Ke,bb as Be,bc as Ye,bd as We,be as gt,bf as Ja,bg as pt,bh as Qa,bi as er,bj as _e,bk as ln,bl as sr,bm as ft,bn as Ts,bo as _t,bp as nr,bq as bt,br as tr,bs as ar,bt as rr,bu as vt,bv as ir,bw as lr,bx as cr,by as or,bz as dr,bA as Es,bB as cn,bC as xr,bD as ur,bE as mr,bF as hr,bG as jr,bH as gr,bI as pr,bJ as on,bK as fr,bL as Nt,bM as yt,bN as wt,bO as Ct,bP as St,bQ as kt,bR as _r,bS as br,bT as vr,bU as Nr,bV as yr,bW as wr,bX as Cr,bY as Sr}from"./vendor-CjAze826.js";(function(){const n=document.createElement("link").relList;if(n&&n.supports&&n.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))i(r);new MutationObserver(r=>{for(const a of r)if(a.type==="childList")for(const x of a.addedNodes)x.tagName==="LINK"&&x.rel==="modulepreload"&&i(x)}).observe(document,{childList:!0,subtree:!0});function t(r){const a={};return r.integrity&&(a.integrity=r.integrity),r.referrerPolicy&&(a.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?a.credentials="include":r.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function i(r){if(r.ep)return;r.ep=!0;const a=t(r);fetch(r.href,a)}})();const It=()=>!window.invokeNative,kr=()=>{};async function Y(s,n,t,i){const r={method:"post",headers:{"Content-Type":"application/json; charset=UTF-8"},body:JSON.stringify(n)};if(It()&&t!=null)return i&&await new Promise(a=>setTimeout(a,i)),t;do try{const a=window.GetParentResourceName?window.GetParentResourceName():"nui-frame-app";return await(await fetch(`https://${a}/${s}`,r)).json()}catch{await new Promise(x=>setTimeout(x,500))}while(s!="hideFrame");return{}}const Dt=o.createContext(null),Ir=({children:s})=>{const[n,t]=o.useState(!1),[i,r]=o.useState(!1),a=Xs();function x(l){l?(r(!0),setTimeout(()=>t(l),100)):(t(l),Y("hideFrame"))}return o.useEffect(()=>{let l;return n||(l=setTimeout(()=>r(!1),500)),()=>clearTimeout(l)},[n]),o.useEffect(()=>{if(!n)return;const l=u=>{["Escape"].includes(u.code)&&(It()||Y("hideFrame"),t(!n),setTimeout(()=>a("/"),500))};return window.addEventListener("keydown",l),()=>window.removeEventListener("keydown",l)},[n]),e.jsx(Dt.Provider,{value:{visible:n,setVisible:x},children:e.jsx("div",{className:"fade-container",style:{opacity:n?1:0,display:i?"block":"none"},children:s})})},is=()=>o.useContext(Dt),us=(s,n)=>{const t=o.useRef(kr);o.useEffect(()=>{t.current=n},[n]),o.useEffect(()=>{const i=r=>{const{action:a,data:x}=r.data;t.current&&a===s&&t.current(x)};return window.addEventListener("message",i),()=>window.removeEventListener("message",i)},[s])},W=Ns(s=>({data:{},get:(n,...t)=>{let i=W.getState().data[n];if(!i)return n;for(let r of t)i=i.replace("%s",r);return i},set:n=>s({data:{...n}})}));function T(...s){return da(xa(s))}function $e(){ts.error("Invalid form data. Please re-check all of the tabs for missing data or mistakes.")}const Qs=$s("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),D=o.forwardRef(({className:s,variant:n,size:t,asChild:i=!1,...r},a)=>{const x=i?Dn:"button";return e.jsx(x,{className:T(Qs({variant:n,size:t,className:s})),ref:a,...r})});D.displayName="Button";const dn=[{icon:ma,label:"Dashboard",path:"/"},{icon:ha,label:"Settings",path:"/general"}],xn=[{icon:Ds,label:"Harvesting Zones",path:"/harvesting-zones"},{icon:ja,label:"Processing Zones",path:"/processing-zones"},{icon:js,label:"Plants",path:"/plants"},{icon:ga,label:"Plant Lights",path:"/plant-lights"},{icon:Tn,label:"Laboratories",path:"/laboratories"},{icon:pa,label:"Processing Tables",path:"/processing-tables"},{icon:fa,label:"Suppliers",path:"/suppliers"},{icon:_a,label:"Drug Effects",path:"/consumables"},{icon:ba,label:"Pocket Processing",path:"/pocket-processing"},{icon:va,label:"Retail Selling",path:"/retail-selling"},{icon:Na,label:"Wholesale Selling",path:"/wholesale-selling"}];function Dr(){const s=ua();Xs();const[n,t]=o.useState(0),[i,r]=o.useState(""),a=o.useRef(null),x=o.useRef(null),l=o.useRef(null);return o.useEffect(()=>{const u=dn.findIndex(m=>s.pathname===m.path);if(u!==-1){if(t(u),r("main"),a.current&&x.current){const m=x.current.children[u];if(m){const j=m.offsetTop;a.current.style.transform=`translateY(calc(${j===0?"2rem":j+"px + 0.5rem"}))`}}return}const _=xn.findIndex(m=>s.pathname===m.path);if(_!==-1){if(t(_),r("zone"),a.current&&l.current){const m=l.current.children[_];if(m){const j=m.offsetTop;a.current.style.transform=`translateY(calc(${j}px + 0.5rem))`}}return}t(-1),r("")},[s]),e.jsx("div",{className:"w-64 bg-background border-r h-full flex flex-col justify-between",children:e.jsxs("div",{className:"space-y-2 overflow-y-auto grow p-4 relative",children:[e.jsx("div",{ref:a,style:{transform:"translateY(2rem)"},className:"absolute left-4 right-4 rounded-md h-10 bg-muted transition-transform duration-300 ease-in-out z-0"}),e.jsxs("div",{className:" relative",children:[e.jsx("h3",{className:"px-3 text-xs font-semibold text-muted-foreground uppercase tracking-wider !mb-2",children:"Main"}),e.jsx("div",{ref:x,children:dn.map((u,_)=>e.jsx(D,{variant:"ghost",className:T("w-full justify-start text-muted-foreground hover:text-foreground hover:bg-transparent relative z-10",s.pathname===u.path&&"text-foreground"),asChild:!0,children:e.jsxs(tn,{to:u.path,children:[e.jsx(u.icon,{className:"mr-3 h-5 w-5"}),u.label]})},_))}),e.jsx("h3",{className:"px-3 text-xs font-semibold text-muted-foreground uppercase tracking-wider !mb-2 !mt-4",children:"FEATURES"}),e.jsx("div",{ref:l,children:xn.map((u,_)=>e.jsx(D,{variant:"ghost",className:T("w-full justify-start text-muted-foreground hover:text-foreground hover:bg-transparent relative z-10",s.pathname===u.path&&"text-foreground"),asChild:!0,children:e.jsxs(tn,{to:u.path,children:[e.jsx(u.icon,{className:"mr-3 h-5 w-5"}),u.label]})},_))})]})]})})}const Tt=o.forwardRef(({className:s,...n},t)=>e.jsx(Pn,{ref:t,className:T("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",s),...n}));Tt.displayName=Pn.displayName;const Pt=o.forwardRef(({className:s,...n},t)=>e.jsx($n,{ref:t,className:T("aspect-square h-full w-full",s),...n}));Pt.displayName=$n.displayName;const $t=o.forwardRef(({className:s,...n},t)=>e.jsx(En,{ref:t,className:T("flex h-full w-full items-center justify-center rounded-full bg-muted",s),...n}));$t.displayName=En.displayName;function Et(){const[s,n]=o.useState("dark");return o.useEffect(()=>{const t=localStorage.getItem("theme");t?n(t):window.matchMedia("(prefers-color-scheme: dark)").matches&&n("dark")},[]),o.useEffect(()=>{localStorage.setItem("theme",s),document.documentElement.classList.toggle("dark",s==="dark")},[s]),{theme:s,setTheme:n}}function Tr(){const{theme:s,setTheme:n}=Et();return e.jsxs(D,{variant:"outline",size:"icon",onClick:()=>n(s==="light"?"dark":"light"),className:"bg-background hover:bg-background2 text-foreground",children:[e.jsx(Zs,{className:"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),e.jsx(ya,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"})]})}const Lt=Ns(s=>({serverId:0,username:"",avatarUrl:"",setProfile:n=>s(n)}));function Pr({className:s}){const{username:n,serverId:t,avatarUrl:i}=Lt(),r=n.split(" ").map(a=>a[0]).join("").toUpperCase();return e.jsxs("header",{className:T("flex items-center justify-between p-4 bg-background border-b",s),children:[e.jsx(Tr,{}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{className:"flex flex-col items-end",children:[e.jsx("p",{className:"text-sm font-medium",children:n}),e.jsxs("p",{className:"text-sm font-medium",children:["ID: ",t]})]}),e.jsxs(Tt,{children:[e.jsx(Pt,{src:i,alt:n}),e.jsx($t,{children:r})]})]})]})}const ie=Ns(s=>({serverConfig:null,setServerConfig:n=>s({serverConfig:n}),updateConfig:n=>s(t=>({serverConfig:t.serverConfig?{...t.serverConfig,...n}:n})),updateHarvestingZone:(n,t)=>s(i=>{if(!i.serverConfig)return{serverConfig:null};const r=[...i.serverConfig.harvestingZones];return r[n]=t,Y("updateServerConfig",{indexes:["harvestingZones",n+1],data:t}),{serverConfig:{...i.serverConfig,harvestingZones:r}}}),updateHarvestingZones:n=>s(t=>t.serverConfig?(Y("updateServerConfig",{indexes:["harvestingZones"],data:n}),{serverConfig:{...t.serverConfig,harvestingZones:n}}):{serverConfig:null}),updateProcessingZone:(n,t)=>s(i=>{if(!i.serverConfig)return{serverConfig:null};const r=[...i.serverConfig.processingZones];return r[n]=t,Y("updateServerConfig",{indexes:["processingZones",n+1],data:t}),{serverConfig:{...i.serverConfig,processingZones:r}}}),updateProcessingZones:n=>s(t=>t.serverConfig?(Y("updateServerConfig",{indexes:["processingZones"],data:n}),{serverConfig:{...t.serverConfig,processingZones:n}}):{serverConfig:null}),updatePlant:(n,t)=>s(i=>i.serverConfig?(Y("updateServerConfig",{indexes:["plants",Number(n)],data:t}),{serverConfig:{...i.serverConfig,plants:{...i.serverConfig.plants,[n]:t}}}):{serverConfig:null}),updatePlants:n=>s(t=>t.serverConfig?(Y("updateServerConfig",{indexes:["plants"],data:n}),{serverConfig:{...t.serverConfig,plants:n}}):{serverConfig:null}),updateLamp:(n,t)=>s(i=>{if(!i.serverConfig)return{serverConfig:null};const r=[...i.serverConfig.lamps];return r[n]=t,Y("updateServerConfig",{indexes:["lamps",n+1],data:t}),{serverConfig:{...i.serverConfig,lamps:r}}}),updateLamps:n=>s(t=>t.serverConfig?(Y("updateServerConfig",{indexes:["lamps"],data:n}),{serverConfig:{...t.serverConfig,lamps:n}}):{serverConfig:null}),updateLaboratory:(n,t)=>s(i=>i.serverConfig?(Y("updateServerConfig",{indexes:["laboratories",Number(n)],data:t}),{serverConfig:{...i.serverConfig,laboratories:{...i.serverConfig.laboratories,[n]:t}}}):{serverConfig:null}),updateLaboratories:n=>s(t=>t.serverConfig?(Y("updateServerConfig",{indexes:["laboratories"],data:n}),{serverConfig:{...t.serverConfig,laboratories:n}}):{serverConfig:null}),updateProcessingTable:(n,t)=>s(i=>{if(!i.serverConfig)return{serverConfig:null};const r=[...i.serverConfig.processingTables];return r[n]=t,Y("updateServerConfig",{indexes:["processingTables",n+1],data:t}),{serverConfig:{...i.serverConfig,processingTables:r}}}),updateProcessingTables:n=>s(t=>t.serverConfig?(Y("updateServerConfig",{indexes:["processingTables"],data:n}),{serverConfig:{...t.serverConfig,processingTables:n}}):{serverConfig:null}),updateSupplier:(n,t)=>s(i=>{if(!i.serverConfig)return{serverConfig:null};const r=[...i.serverConfig.suppliers];return r[n]=t,Y("updateServerConfig",{indexes:["suppliers",n+1],data:t}),{serverConfig:{...i.serverConfig,suppliers:r}}}),updateSuppliers:n=>s(t=>t.serverConfig?(Y("updateServerConfig",{indexes:["suppliers"],data:n}),{serverConfig:{...t.serverConfig,suppliers:n}}):{serverConfig:null}),updateConsumable:(n,t)=>s(i=>{if(!i.serverConfig)return{serverConfig:null};const r=[...i.serverConfig.consumables];return r[n]=t,Y("updateServerConfig",{indexes:["consumables",n+1],data:t}),{serverConfig:{...i.serverConfig,consumables:r}}}),updateConsumables:n=>s(t=>t.serverConfig?(Y("updateServerConfig",{indexes:["consumables"],data:n}),{serverConfig:{...t.serverConfig,consumables:n}}):{serverConfig:null}),updatePocketProcessing:(n,t)=>s(i=>{if(!i.serverConfig)return{serverConfig:null};const r=[...i.serverConfig.pocketProcessing];return r[n]=t,Y("updateServerConfig",{indexes:["pocketProcessing",n+1],data:t}),{serverConfig:{...i.serverConfig,pocketProcessing:r}}}),updatePocketProcessings:n=>s(t=>t.serverConfig?(Y("updateServerConfig",{indexes:["pocketProcessing"],data:n}),{serverConfig:{...t.serverConfig,pocketProcessing:n}}):{serverConfig:null}),updateRetailSellingZone:(n,t)=>s(i=>{if(!i.serverConfig)return{serverConfig:null};const r=[...i.serverConfig.retailSellingZones];return r[n]=t,Y("updateServerConfig",{indexes:["retailSellingZones",n+1],data:t}),{serverConfig:{...i.serverConfig,retailSellingZones:r}}}),updateRetailSellingZones:n=>s(t=>t.serverConfig?(Y("updateServerConfig",{indexes:["retailSellingZones"],data:n}),{serverConfig:{...t.serverConfig,retailSellingZones:n}}):{serverConfig:null}),updateWholesaleSettings:n=>s(t=>t.serverConfig?(Y("updateServerConfig",{indexes:["wholesaleSettings"],data:n}),{serverConfig:{...t.serverConfig,wholesaleSettings:n}}):{serverConfig:null}),updateGeneralSettings:n=>s(t=>t.serverConfig?(Y("updateServerConfig",{indexes:["generalSettings"],data:n}),{serverConfig:{...t.serverConfig,generalSettings:n}}):{serverConfig:null})})),$r=Ns(s=>({framework:"es_extended",setFramework:n=>s({framework:n})})),zt=Ns(s=>({dailyStats:[],topSellingDrugs:[],totalProcessed:0,totalSold:0,previousPeriodProcessed:0,previousPeriodSold:0,topSellingDrug:null,setStats:n=>{s(t=>({...t,...n}))}})),Ee=Sa,Er=Ca,At=o.forwardRef(({className:s,...n},t)=>e.jsx(Ln,{ref:t,className:T("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...n}));At.displayName=Ln.displayName;const ve=o.forwardRef(({className:s,children:n,disableClose:t,...i},r)=>{const a=l=>{var u;l.key==="Escape"&&l.stopPropagation(),t||(u=i.onKeyDown)==null||u.call(i,l)},x=is();return e.jsx(Er,{children:e.jsxs("div",{style:{opacity:x!=null&&x.visible?1:0},children:[e.jsx(At,{}),e.jsxs(zn,{ref:r,className:T("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] rounded-2xl",s),onKeyDown:a,...i,children:[n,!t&&e.jsxs(wa,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[e.jsx(An,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"Close"})]})]})]})})});ve.displayName=zn.displayName;const Ne=({className:s,...n})=>e.jsx("div",{className:T("flex flex-col space-y-1.5 text-center sm:text-left",s),...n});Ne.displayName="DialogHeader";const en=({className:s,...n})=>e.jsx("div",{className:T("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...n});en.displayName="DialogFooter";const ye=o.forwardRef(({className:s,...n},t)=>e.jsx(Rn,{ref:t,className:T("text-lg font-bold leading-none tracking-tight",s),...n}));ye.displayName=Rn.displayName;const ys=o.forwardRef(({className:s,...n},t)=>e.jsx(Mn,{ref:t,className:T("text-sm text-muted-foreground",s),...n}));ys.displayName=Mn.displayName;const Lr=()=>{const s=is(),n=W();Xs(),o.useState(!1);const{theme:t}=Et(),{serverConfig:i,setServerConfig:r}=ie();$r();const{setStats:a}=zt(),{setProfile:x}=Lt(),[l,u]=o.useState(!1),[_,m]=o.useState(null),[j,h]=o.useState(!1);us("open",()=>{s.setVisible(!0)}),us("initDui",R=>{u(!0),s.setVisible(!0)}),us("openOnboarding",()=>{h(!0)}),us("updatePlant",R=>{R?m(R):setTimeout(()=>m(null),300)}),us("updateServerConfig",({config:R,force:E})=>{if(Array.isArray(R.laboratories)){const z={};R.laboratories.forEach((p,S)=>{z[S+1]=p}),R.laboratories=z}if(Array.isArray(R.plants)){const z={};R.plants.forEach((p,S)=>{z[S+1]=p}),R.plants=z}for(const z in R.laboratories){const p=R.laboratories[z];if(p&&Array.isArray(p.entrances)){const S={};p.entrances.forEach((w,y)=>{S[y+1]=w}),p.entrances=S}}E||(R={...R,generalSettings:{...R.generalSettings,webhook:(i==null?void 0:i.generalSettings.webhook)||""}}),r(R)}),us("updateStats",R=>{a(R)}),us("updateProfile",R=>{x(R)}),o.useEffect(()=>{(async()=>{const E=await Y("getProfile",void 0,{serverId:21,username:"Andrew.",avatarUrl:""});x(E)})()},[]),o.useEffect(()=>{(async()=>{try{const E=await Y("getLanguage",void 0,"en"),z=await fetch(`./locales/${E}.json`);if(!z.ok)throw new Error("Preferred language not available");const p=await z.json();n.set(p)}catch{console.warn("Failed to load preferred language, falling back to English");const p=await(await fetch("./locales/en.json")).json();n.set(p)}})()},[]);const M=R=>{Y("onboardingComplete",{usePresetConfig:R}),h(!1)};if(l){const E=_?[{name:"GROWTH",value:_.stats.growth||0,color:"#BFFF33",icon:Ds},{name:"SUNLIGHT",value:_.stats.sunlight||0,color:"#ffbe33",icon:Zs},{name:"WATER",value:_.stats.water||0,color:"#3399FF",icon:an},{name:"FERTILIZER",value:_.stats.fertilizer||0,color:"#66FF66",icon:js}]:[{name:"GROWTH",value:25,color:"#BFFF33",icon:Ds},{name:"SUNLIGHT",value:78,color:"#ffbe33",icon:Zs},{name:"WATER",value:97,color:"#3399FF",icon:an},{name:"FERTILIZER",value:20,color:"#66FF66",icon:js}];return e.jsxs("div",{className:"w-screen h-screen flex flex-col justify-start items-center bg-[#06060a] overflow-hidden antialiased",children:[e.jsx("div",{className:"w-full max-w-md relative overflow-hidden h-full",children:e.jsxs(On,{initial:!1,children:[e.jsx(Hs.div,{className:"w-full h-full absolute flex items-center justify-center",initial:{x:0},animate:{x:_?"-100%":0,opacity:_?.3:1},transition:{type:"spring",stiffness:300,damping:30},children:e.jsxs("div",{className:"w-full h-full flex flex-col items-center justify-center text-center space-y-8 px-4",children:[e.jsx(ka,{className:"w-32 h-32 text-[#33FF66] animate-pulse",strokeWidth:1.5}),e.jsx("h2",{className:"text-4xl font-bold uppercase tracking-wide text-[#33FF66]",children:"Ready to Scan"}),e.jsx("p",{className:"text-xl text-gray-400",children:"Point your device at a plant to analyze its properties"}),e.jsxs("div",{className:"w-full bg-[#333333] rounded-md p-6 mt-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("span",{className:"text-xl text-gray-400",children:"Scanner Status"}),e.jsx("span",{className:"text-xl font-semibold text-[#33FF66]",children:"Active"})]}),e.jsx("div",{className:"w-full bg-[#444444] h-3 rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-[#33FF66] animate-[scan_2s_ease-in-out_infinite]",style:{width:"60%"}})})]})]})},"scanning-screen"),_&&e.jsx(Hs.div,{className:"w-full h-full absolute flex items-center justify-center",initial:{x:"100%"},animate:{x:0},exit:{x:"100%"},transition:{type:"spring",stiffness:300,damping:30},children:e.jsxs("div",{className:"w-full space-y-4 px-4",children:[e.jsxs("div",{className:"bg-gradient-to-r from-[#1a2e1a] to-[#2d4d33] rounded-md p-6 relative overflow-hidden",children:[e.jsx("div",{className:"absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0zNiAxOGMtMS4zNiAwLTIuNTk4LjU0OS0zLjUgMS40MzJDMzEuNTk4IDE4LjU0OSAzMC4zNiAxOCAyOSAxOGMtMi43NiAwLTUgMi4yNC01IDVzMi4yNCA1IDUgNWMxLjM2IDAgMi41OTgtLjU0OSAzLjUtMS40MzJDMzMuNDAyIDI3LjQ1MSAzNC42NCAyOCAzNiAyOGMyLjc2IDAgNS0yLjI0IDUtNXMtMi4yNC01LTUtNXoiIHN0cm9rZT0icmdiYSg1MSwgMjU1LCAxMDIsIDAuMykiIHN0cm9rZS13aWR0aD0iMiIvPjwvZz48L3N2Zz4=')] opacity-10"}),e.jsxs("div",{className:"flex flex-col items-center justify-center space-y-2 relative z-10",children:[e.jsxs("div",{className:"flex items-center justify-center space-x-2 mb-1",children:[e.jsx(js,{className:"w-6 h-6 text-[#33FF66] animate-pulse"}),e.jsx("span",{className:"text-3xl font-black uppercase tracking-widest text-[#33FF66] bg-clip-text bg-gradient-to-r from-[#33FF66] to-[#66ffaa]",children:"PLANT STATS"}),e.jsx(js,{className:"w-6 h-6 text-[#33FF66] animate-pulse"})]}),e.jsx("div",{className:"w-full h-1 bg-gradient-to-r from-transparent via-[#33FF66] to-transparent opacity-70"}),e.jsx("div",{className:"text-xl text-[#a3ffb8] uppercase tracking-widest",children:"ANALYSIS COMPLETE"})]})]}),E.map((z,p)=>e.jsxs("div",{className:"rounded-md p-3 py-6 flex items-center justify-center",style:{backgroundColor:z.color},children:[Vn.createElement(z.icon,{className:"w-12 h-12 mr-3 text-black",strokeWidth:2.5}),e.jsx("span",{className:"text-4xl font-extrabold uppercase tracking-wide text-black text-center",children:`${z.name}: ${z.value}`})]},p))]})},"plant-details")]})}),e.jsx("style",{dangerouslySetInnerHTML:{__html:`
            @keyframes scan {
              0% { width: 0%; }
              50% { width: 100%; }
              100% { width: 0%; }
            }
          `}})]})}return e.jsxs("div",{className:"w-screen h-screen flex justify-center items-center bg-transparent",children:[e.jsx(Ia,{position:"top-center",theme:t,toastOptions:{className:"bg-background text-foreground font-[Inter] border"}}),e.jsxs("div",{className:"w-[76rem] h-[45rem] bg-background rounded-3xl shadow-lg border overflow-hidden flex flex-col",children:[e.jsx(Pr,{}),e.jsxs("div",{className:"flex grow overflow-hidden",children:[e.jsx(Dr,{}),e.jsx("div",{className:"w-full p-6",children:e.jsx(Da,{})})]})]}),e.jsx(Ee,{open:j,onOpenChange:h,children:e.jsxs(ve,{className:"sm:max-w-[500px]",disableClose:!0,children:[e.jsxs(Ne,{children:[e.jsx(ye,{className:"text-xl font-bold",children:n.get("ui_welcome_drugs_creator")}),e.jsx(ys,{className:"text-sm mt-2",children:n.get("ui_preset_config_description")})]}),e.jsxs("div",{className:"flex flex-col gap-3 py-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(Fn,{className:"h-5 w-5 text-primary flex-shrink-0"}),e.jsx("span",{className:"text-sm",children:n.get("ui_preset_drugs_count")})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(js,{className:"h-5 w-5 text-primary flex-shrink-0"}),e.jsx("span",{className:"text-sm",children:n.get("ui_preset_items_count")})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(Ds,{className:"h-5 w-5 text-primary flex-shrink-0"}),e.jsx("span",{className:"text-sm",children:n.get("ui_preset_harvesting_zones")})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(Tn,{className:"h-5 w-5 text-primary flex-shrink-0"}),e.jsx("span",{className:"text-sm",children:n.get("ui_preset_processing_tables")})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(Js,{className:"h-5 w-5 text-primary flex-shrink-0"}),e.jsx("span",{className:"text-sm",children:n.get("ui_preset_selling_zones")})]})]}),e.jsx(en,{children:e.jsxs("div",{className:"w-full flex justify-between",children:[e.jsx(D,{variant:"outline",onClick:()=>M(!1),children:n.get("ui_manual_setup")}),e.jsx(D,{onClick:()=>M(!0),children:n.get("ui_use_preset_config")})]})})]})})]})},zr=({isOpen:s,onClose:n,onConfirm:t,title:i,description:r})=>e.jsx(Ee,{open:s,onOpenChange:n,children:e.jsxs(ve,{"aria-describedby":void 0,children:[e.jsxs(Ne,{children:[e.jsx(ye,{children:i}),e.jsx(ys,{children:r})]}),e.jsxs(en,{children:[e.jsx(D,{variant:"outline",onClick:n,children:"Cancel"}),e.jsx(D,{onClick:t,children:"Confirm"})]})]})}),Rt=o.createContext(null),Xe=()=>{const s=o.useContext(Rt);if(!s)throw new Error("useConfirm must be used within a ConfirmProvider");return s.confirm},Ar=({children:s})=>{const[n,t]=o.useState({isOpen:!1,title:"",description:"",resolve:null}),i=o.useCallback((x,l)=>new Promise(u=>{t({isOpen:!0,title:x,description:l,resolve:u})}),[]),r=o.useCallback(()=>{t(x=>({...x,isOpen:!1})),n.resolve&&n.resolve(!1)},[n]),a=o.useCallback(()=>{t(x=>({...x,isOpen:!1})),n.resolve&&n.resolve(!0)},[n]);return e.jsxs(Rt.Provider,{value:{confirm:i},children:[s,e.jsx(zr,{isOpen:n.isOpen,onClose:r,onConfirm:a,title:n.title,description:n.description})]})},se=o.forwardRef(({className:s,...n},t)=>e.jsx("div",{className:"relative w-full overflow-auto",children:e.jsx("table",{ref:t,className:T("w-full caption-bottom text-sm",s),...n})}));se.displayName="Table";const ne=o.forwardRef(({className:s,...n},t)=>e.jsx("thead",{ref:t,className:T("[&_tr]:border-b",s),...n}));ne.displayName="TableHeader";const te=o.forwardRef(({className:s,...n},t)=>e.jsx("tbody",{ref:t,className:T("[&_tr:last-child]:border-0",s),...n}));te.displayName="TableBody";const Rr=o.forwardRef(({className:s,...n},t)=>e.jsx("tfoot",{ref:t,className:T("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",s),...n}));Rr.displayName="TableFooter";const K=o.forwardRef(({className:s,...n},t)=>e.jsx("tr",{ref:t,className:T("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",s),...n}));K.displayName="TableRow";const P=o.forwardRef(({className:s,...n},t)=>e.jsx("th",{ref:t,className:T("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",s),...n}));P.displayName="TableHead";const $=o.forwardRef(({className:s,...n},t)=>e.jsx("td",{ref:t,className:T("p-4 align-middle [&:has([role=checkbox])]:pr-0",s),...n}));$.displayName="TableCell";const Mr=o.forwardRef(({className:s,...n},t)=>e.jsx("caption",{ref:t,className:T("mt-4 text-sm text-muted-foreground",s),...n}));Mr.displayName="TableCaption";const k=o.forwardRef(({className:s,type:n,...t},i)=>(t.value=t.value===void 0?"":t.value,e.jsx("input",{type:n,className:T("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:i,...t})));k.displayName="Input";const Mt=de.union([de.string(),de.number()]).transform(s=>{if(typeof s=="string"){const n=parseFloat(s);return isNaN(n)?void 0:n}if(typeof s=="number")return s}),I=Mt.pipe(de.number()),be=Mt.pipe(de.number().min(0).optional()).optional(),Or=$s("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),Ot=o.forwardRef(({className:s,...n},t)=>e.jsx(qn,{ref:t,className:T(Or(),s),...n}));Ot.displayName=qn.displayName;const Le=Zn,Vt=o.createContext({}),N=({...s})=>e.jsx(Vt.Provider,{value:{name:s.name},children:e.jsx(Ta,{...s})}),Ls=()=>{const s=o.useContext(Vt),n=o.useContext(Ft),{getFieldState:t,formState:i}=rs(),r=t(s.name,i);if(!s)throw new Error("useFormField should be used within <FormField>");const{id:a}=n;return{id:a,name:s.name,formItemId:`${a}-form-item`,formDescriptionId:`${a}-form-item-description`,formMessageId:`${a}-form-item-message`,...r}},Ft=o.createContext({}),b=o.forwardRef(({className:s,...n},t)=>{const i=o.useId();return e.jsx(Ft.Provider,{value:{id:i},children:e.jsx("div",{ref:t,className:T("space-y-2",s),...n})})});b.displayName="FormItem";const C=o.forwardRef(({className:s,...n},t)=>{const{error:i,formItemId:r}=Ls();return e.jsx(Ot,{ref:t,className:T(i&&"text-destructive",s),htmlFor:r,...n})});C.displayName="FormLabel";const v=o.forwardRef(({...s},n)=>{const{error:t,formItemId:i,formDescriptionId:r,formMessageId:a}=Ls();return e.jsx(Dn,{ref:n,id:i,"aria-describedby":t?`${r} ${a}`:`${r}`,"aria-invalid":!!t,...s})});v.displayName="FormControl";const L=o.forwardRef(({className:s,...n},t)=>{const{formDescriptionId:i}=Ls();return e.jsx("p",{ref:t,id:i,className:T("text-sm text-muted-foreground",s),...n})});L.displayName="FormDescription";const O=o.forwardRef(({className:s,children:n,...t},i)=>{const r=W(),{error:a,formMessageId:x}=Ls(),l=a?r.get(String(a==null?void 0:a.message)):n;return!l||l===(a==null?void 0:a.message)?null:e.jsx("p",{ref:i,id:x,className:T("text-sm font-medium text-destructive",s),...t,children:l})});O.displayName="FormMessage";function Ps({name:s,label:n,description:t,is4D:i=!1,className:r,disableButton:a=!1,target:x=!1,forceDisableTarget:l=!1}){const{control:u,setValue:_,watch:m}=rs(),j=is(),h=async()=>{j.setVisible(!1);const R=await Y("getCoordsInput",{target:x,is4D:i,forceDisableTarget:l},{x:10,y:-10,z:20,w:10},500);j.setVisible(!0),_(s,R)},M=async()=>{j.setVisible(!1);const R=m(s);await Y("teleport",R)};return e.jsxs(b,{className:r,children:[e.jsx(C,{children:n}),e.jsx(v,{children:e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"flex gap-2",children:[["x","y","z",...i?["w"]:[]].map(R=>e.jsx(N,{control:u,name:`${s}.${R}`,render:({field:E})=>e.jsx(b,{className:"w-full",children:e.jsx(v,{children:e.jsx(k,{type:"number",placeholder:R.toUpperCase(),...E,value:E.value})})})},R)),!a&&e.jsxs(e.Fragment,{children:[e.jsx(D,{onClick:h,type:"button",size:"icon",className:"shrink-0",children:e.jsx(as,{className:"w-4 h-4"})}),e.jsx(D,{onClick:M,type:"button",size:"icon",className:"shrink-0",children:e.jsx(Oe,{className:"w-4 h-4"})})]})]})})}),t&&e.jsx(L,{children:t})]})}const Je=Pa,Re=o.forwardRef(({className:s,...n},t)=>e.jsx(Hn,{ref:t,className:T("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...n}));Re.displayName=Hn.displayName;const X=o.forwardRef(({className:s,...n},t)=>e.jsx(Un,{ref:t,className:T("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...n}));X.displayName=Un.displayName;const J=o.forwardRef(({className:s,...n},t)=>e.jsx(Gn,{ref:t,className:T("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...n}));J.displayName=Gn.displayName;const ge=o.forwardRef(({className:s,...n},t)=>e.jsx(Kn,{ref:t,className:T("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",s),...n,children:e.jsx($a,{className:T("flex items-center justify-center text-current"),children:e.jsx(vs,{className:"h-4 w-4"})})}));ge.displayName=Kn.displayName;const ms=Ma,hs=Oa,ls=o.forwardRef(({className:s,children:n,...t},i)=>e.jsxs(Bn,{ref:i,className:T("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...t,children:[n,e.jsx(Ea,{asChild:!0,children:e.jsx(ps,{className:"h-4 w-4 opacity-50"})})]}));ls.displayName=Bn.displayName;const qt=o.forwardRef(({className:s,...n},t)=>e.jsx(Yn,{ref:t,className:T("flex cursor-default items-center justify-center py-1",s),...n,children:e.jsx(Wn,{className:"h-4 w-4"})}));qt.displayName=Yn.displayName;const Zt=o.forwardRef(({className:s,...n},t)=>e.jsx(Xn,{ref:t,className:T("flex cursor-default items-center justify-center py-1",s),...n,children:e.jsx(ps,{className:"h-4 w-4"})}));Zt.displayName=Xn.displayName;const cs=o.forwardRef(({className:s,children:n,position:t="popper",...i},r)=>e.jsx(La,{children:e.jsxs(Jn,{ref:r,className:T("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:t,...i,children:[e.jsx(qt,{}),e.jsx(za,{className:T("p-1",t==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:n}),e.jsx(Zt,{})]})}));cs.displayName=Jn.displayName;const Vr=o.forwardRef(({className:s,...n},t)=>e.jsx(Qn,{ref:t,className:T("py-1.5 pl-8 pr-2 text-sm font-semibold",s),...n}));Vr.displayName=Qn.displayName;const Q=o.forwardRef(({className:s,children:n,...t},i)=>e.jsxs(et,{ref:i,className:T("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...t,children:[e.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:e.jsx(Aa,{children:e.jsx(vs,{className:"h-4 w-4"})})}),e.jsx(Ra,{children:n})]}));Q.displayName=et.displayName;const Fr=o.forwardRef(({className:s,...n},t)=>e.jsx(st,{ref:t,className:T("-mx-1 my-1 h-px bg-muted",s),...n}));Fr.displayName=st.displayName;const qr=qa,Us=o.forwardRef(({className:s,...n},t)=>e.jsx(Va,{ref:t,className:T("border-b",s),...n}));Us.displayName="AccordionItem";const Gs=o.forwardRef(({className:s,children:n,...t},i)=>e.jsx(Fa,{className:"flex",children:e.jsxs(nt,{ref:i,className:T("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",s),...t,children:[n,e.jsx(ps,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})}));Gs.displayName=nt.displayName;const Ks=o.forwardRef(({className:s,children:n,...t},i)=>e.jsx(tt,{ref:i,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...t,children:e.jsx("div",{className:T("pb-4 pt-0",s),children:n})}));Ks.displayName=tt.displayName;const ws=de.union([de.object({scenario:de.string().min(1,"ui_error_required"),playEnter:de.boolean().optional()}),de.object({dict:de.string().min(1,"ui_error_required"),clip:de.string().min(1,"ui_error_required"),flag:be,blendIn:be,blendOut:be,duration:be,playbackRate:be,lockX:de.boolean().optional(),lockY:de.boolean().optional(),lockZ:de.boolean().optional()})]),Cs=de.object({model:de.string().optional(),bone:be,pos:de.object({x:I,y:I,z:I}).catch({x:0,y:0,z:0}),rot:de.object({x:I,y:I,z:I}).catch({x:0,y:0,z:0})}).transform(s=>s.model!==void 0?s:void 0),un=["coke_cutting","coke_packing_basic","coke_packing_advanced","coke_unpacking","meth_breaking","meth_packing","meth_cooking","weed_sorting_left","weed_sorting_right","weed_sorting_left_v2","weed_sorting_right_v2"],Ss=({name:s,disableNone:n,syncedScene:t})=>{const i=W(),{control:r,watch:a,setValue:x,resetField:l}=rs(),u=a(s),_=a("syncedScene"),[m,j]=o.useState(()=>_?"syncedScene":u?"dict"in u?"animation":"scenario"in u?"scenario":"none":"none");return o.useEffect(()=>{u&&(m==="none"?(x(s,void 0),t&&x("syncedScene",void 0)):m==="scenario"&&(!u||!("scenario"in u)||!u.scenario)?(x(s,void 0),x(s,{scenario:""}),t&&x("syncedScene",void 0)):m==="animation"&&(!u||!("dict"in u)||!u.dict)?(x(s,void 0),x(s,{dict:"",clip:""}),t&&x("syncedScene",void 0)):m==="syncedScene"&&t&&(x(s,void 0),_||x("syncedScene",un[0])))},[m,s,x,t,_,u]),e.jsxs("div",{className:"space-y-4",children:[e.jsx(N,{control:r,name:`${s}.type`,render:({field:h})=>e.jsxs(b,{children:[e.jsx(C,{children:i.get("ui_animation_type")}),e.jsxs(ms,{onValueChange:M=>{j(M)},value:m,children:[e.jsx(v,{children:e.jsx(ls,{children:e.jsx(hs,{placeholder:i.get("ui_select_animation_type")})})}),e.jsxs(cs,{children:[!n&&e.jsx(Q,{value:"none",children:i.get("ui_none")}),e.jsx(Q,{value:"scenario",children:i.get("ui_scenario")}),e.jsx(Q,{value:"animation",children:i.get("ui_animation")}),t&&e.jsx(Q,{value:"syncedScene",children:i.get("ui_synced_scene")})]})]})]})}),m==="scenario"&&e.jsxs(e.Fragment,{children:[e.jsx(N,{control:r,name:`${s}.scenario`,render:({field:h})=>e.jsxs(b,{children:[e.jsx(C,{children:i.get("ui_scenario")}),e.jsx(v,{children:e.jsx(k,{...h,placeholder:"WORLD_HUMAN_CLIPBOARD"})}),e.jsx(L,{children:i.get("ui_scenario_desc")})]})}),e.jsx(N,{control:r,name:`${s}.playEnter`,render:({field:h})=>e.jsxs(b,{className:"flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4",children:[e.jsx(v,{children:e.jsx(ge,{checked:h.value,onCheckedChange:h.onChange})}),e.jsxs("div",{className:"space-y-1 leading-none",children:[e.jsx(C,{children:i.get("ui_play_enter_animation")}),e.jsx(L,{children:i.get("ui_play_enter_animation_desc")})]})]})})]}),m==="animation"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:r,name:`${s}.dict`,render:({field:h})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:i.get("ui_animation_dict")}),e.jsx(v,{children:e.jsx(k,{...h,placeholder:"e.g. anim@amb@clubhouse@tutorial@bkr_tut_ig3@"})}),e.jsx(L,{children:i.get("ui_animation_dict_desc")})]})}),e.jsx(N,{control:r,name:`${s}.clip`,render:({field:h})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:i.get("ui_animation_clip")}),e.jsx(v,{children:e.jsx(k,{...h,placeholder:"machinic_loop_mechandplayer"})}),e.jsx(L,{children:i.get("ui_animation_clip_desc")})]})})]}),e.jsxs(qr,{type:"single",collapsible:!0,children:[e.jsxs(Us,{value:"advanced-settings",children:[e.jsx(Gs,{children:i.get("ui_advanced_settings")}),e.jsxs(Ks,{className:"space-y-4 mt-4 px-4",children:[e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsx(N,{control:r,name:`${s}.flag`,render:({field:h})=>e.jsxs(b,{children:[e.jsx(C,{children:i.get("ui_flag")}),e.jsx(v,{children:e.jsx(k,{type:"number",...h})})]})}),e.jsx(N,{control:r,name:`${s}.blendIn`,render:({field:h})=>e.jsxs(b,{children:[e.jsx(C,{children:i.get("ui_blend_in")}),e.jsx(v,{children:e.jsx(k,{type:"number",step:"0.1",...h})})]})}),e.jsx(N,{control:r,name:`${s}.blendOut`,render:({field:h})=>e.jsxs(b,{children:[e.jsx(C,{children:i.get("ui_blend_out")}),e.jsx(v,{children:e.jsx(k,{type:"number",step:"0.1",...h})})]})})]}),e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsx(N,{control:r,name:`${s}.duration`,render:({field:h})=>e.jsxs(b,{children:[e.jsx(C,{children:i.get("ui_duration")}),e.jsx(v,{children:e.jsx(k,{type:"number",...h})})]})}),e.jsx(N,{control:r,name:`${s}.playbackRate`,render:({field:h})=>e.jsxs(b,{children:[e.jsx(C,{children:i.get("ui_playback_rate")}),e.jsx(v,{children:e.jsx(k,{type:"number",step:"0.1",...h})})]})}),e.jsxs("div",{className:"flex space-x-4 items-center mt-6",children:[e.jsx(N,{control:r,name:`${s}.lockX`,render:({field:h})=>e.jsxs(b,{className:"flex items-center space-x-2",children:[e.jsx(v,{children:e.jsx(ge,{checked:h.value,onCheckedChange:h.onChange})}),e.jsx(C,{children:i.get("ui_lock_x")})]})}),e.jsx(N,{control:r,name:`${s}.lockY`,render:({field:h})=>e.jsxs(b,{className:"flex items-center space-x-2",children:[e.jsx(v,{children:e.jsx(ge,{checked:h.value,onCheckedChange:h.onChange})}),e.jsx(C,{children:i.get("ui_lock_y")})]})}),e.jsx(N,{control:r,name:`${s}.lockZ`,render:({field:h})=>e.jsxs(b,{className:"flex items-center space-x-2",children:[e.jsx(v,{children:e.jsx(ge,{checked:h.value,onCheckedChange:h.onChange})}),e.jsx(C,{children:i.get("ui_lock_z")})]})})]})]})]})]}),e.jsxs(Us,{value:"animation-prop",children:[e.jsx(Gs,{children:i.get("ui_animation_prop")}),e.jsx(Ks,{className:"space-y-4 mt-4 px-4",children:e.jsx(Zr,{name:s})})]})]})]}),m==="syncedScene"&&t&&e.jsx(N,{control:r,name:"syncedScene",render:({field:h})=>e.jsxs(b,{children:[e.jsx(C,{children:i.get("ui_synced_scene")}),e.jsxs(ms,{onValueChange:h.onChange,value:h.value,children:[e.jsx(v,{children:e.jsx(ls,{children:e.jsx(hs,{placeholder:i.get("ui_select_synced_scene")})})}),e.jsx(cs,{children:un.map(M=>e.jsx(Q,{value:M,children:M},M))})]}),e.jsx(L,{children:i.get("ui_synced_scene_desc")})]})})]})},Zr=({name:s})=>{const n=W(),t=`${s}Prop`;return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(N,{name:`${t}.model`,render:({field:i})=>e.jsxs(b,{children:[e.jsx(C,{children:n.get("ui_model")}),e.jsx(v,{children:e.jsx(k,{...i,placeholder:"prop_cs_hand_radio"})}),e.jsx(L,{children:n.get("ui_model_desc")})]})}),e.jsx(N,{name:`${t}.bone`,render:({field:i})=>e.jsxs(b,{children:[e.jsx(C,{children:n.get("ui_bone")}),e.jsx(v,{children:e.jsx(k,{type:"number",...i,placeholder:"e.g. 28422"})}),e.jsx(L,{children:n.get("ui_bone_desc")})]})})]}),e.jsxs("div",{className:"grid grid-cols-6 gap-4",children:[e.jsx(N,{name:`${t}.pos.x`,render:({field:i})=>e.jsxs(b,{children:[e.jsx(C,{children:n.get("ui_offset_x")}),e.jsx(v,{children:e.jsx(k,{type:"number",step:"0.01",...i})})]})}),e.jsx(N,{name:`${t}.pos.y`,render:({field:i})=>e.jsxs(b,{children:[e.jsx(C,{children:n.get("ui_offset_y")}),e.jsx(v,{children:e.jsx(k,{type:"number",step:"0.01",...i})})]})}),e.jsx(N,{name:`${t}.pos.z`,render:({field:i})=>e.jsxs(b,{children:[e.jsx(C,{children:n.get("ui_offset_z")}),e.jsx(v,{children:e.jsx(k,{type:"number",step:"0.01",...i})})]})}),e.jsx(N,{name:`${t}.rot.x`,render:({field:i})=>e.jsxs(b,{children:[e.jsx(C,{children:n.get("ui_rotation_x")}),e.jsx(v,{children:e.jsx(k,{type:"number",step:"0.01",...i})})]})}),e.jsx(N,{name:`${t}.rot.y`,render:({field:i})=>e.jsxs(b,{children:[e.jsx(C,{children:n.get("ui_rotation_y")}),e.jsx(v,{children:e.jsx(k,{type:"number",step:"0.01",...i})})]})}),e.jsx(N,{name:`${t}.rot.z`,render:({field:i})=>e.jsxs(b,{children:[e.jsx(C,{children:n.get("ui_rotation_z")}),e.jsx(v,{children:e.jsx(k,{type:"number",step:"0.01",...i})})]})})]})]})},ce=o.forwardRef(({className:s,...n},t)=>e.jsx("div",{ref:t,className:T("rounded-lg border bg-card text-card-foreground shadow-sm",s),...n}));ce.displayName="Card";const fs=o.forwardRef(({className:s,...n},t)=>e.jsx("div",{ref:t,className:T("flex flex-col space-y-1.5 p-6",s),...n}));fs.displayName="CardHeader";const _s=o.forwardRef(({className:s,isDiv:n,...t},i)=>{const r=n?"div":"h3";return e.jsx(r,{ref:i,className:T("text-2xl font-semibold leading-none tracking-tight",s),...t})});_s.displayName="CardTitle";const Ht=o.forwardRef(({className:s,isDiv:n,...t},i)=>{const r=n?"div":"p";return e.jsx(r,{ref:i,className:T("text-sm text-muted-foreground",s),...t})});Ht.displayName="CardDescription";const ue=o.forwardRef(({className:s,...n},t)=>e.jsx("div",{ref:t,className:T("p-6 pt-0",s),...n}));ue.displayName="CardContent";const Ut=o.forwardRef(({className:s,...n},t)=>e.jsx("div",{ref:t,className:T("flex items-center p-6 pt-0",s),...n}));Ut.displayName="CardFooter";const he=o.forwardRef(({className:s,children:n,...t},i)=>e.jsxs(at,{ref:i,className:T("relative overflow-hidden",s),...t,children:[e.jsx(Za,{className:"h-full w-full rounded-[inherit]",children:n}),e.jsx(Gt,{}),e.jsx(Ha,{})]}));he.displayName=at.displayName;const Gt=o.forwardRef(({className:s,orientation:n="vertical",...t},i)=>e.jsx(rt,{ref:i,orientation:n,className:T("flex touch-none select-none transition-colors",n==="vertical"&&"h-full w-2.5 border-l border-l-transparent p-[1px]",n==="horizontal"&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",s),...t,children:e.jsx(Ua,{className:"relative flex-1 rounded-full bg-border"})}));Gt.displayName=rt.displayName;re(q({name:B().min(1),amount:Ga([q({min:I.catch(1),max:I.catch(1)}).catch({min:1,max:1}),I.catch(1)]).catch({min:1,max:1})}));function Hr({id:s,children:n}){const{setNodeRef:t,transform:i,transition:r}=le({id:s}),a={transform:ze.Transform.toString(i),transition:r};return e.jsx(K,{ref:t,style:a,children:n})}function Ur({id:s}){const{attributes:n,listeners:t,setNodeRef:i}=le({id:s});return e.jsx($,{ref:i,...n,...t,children:e.jsx(qe,{className:"h-4 w-4 cursor-move"})})}function Bs({name:s,control:n,className:t,disableRandom:i}){const r=W(),{fields:a,append:x,remove:l,move:u}=Ve({name:s,control:n}),_=Fe(oe(Ue),oe(He,{coordinateGetter:Ze}));function m(j){const{active:h,over:M}=j;if(h.id!==M.id){const R=a.findIndex(z=>z.id===h.id),E=a.findIndex(z=>z.id===M.id);u(R,E)}}return e.jsxs("div",{className:T("space-y-4 shrink-0",t),children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(C,{children:r.get("ui_items")}),e.jsx(D,{type:"button",variant:"outline",size:"sm",onClick:()=>x({name:"",amount:i?1:{min:1,max:1}}),className:"shrink-0",children:r.get("ui_add_item")})]}),e.jsx(ce,{children:e.jsx(ue,{className:"px-0 flex flex-col",children:e.jsx(he,{className:"h-96 overflow-x-hidden",children:e.jsx(we,{sensors:_,collisionDetection:Ce,onDragEnd:m,modifiers:[Se,ke],children:e.jsxs(se,{className:"table-fixed h-full",children:[e.jsx(ne,{children:e.jsxs(K,{children:[e.jsx(P,{className:"w-10"}),e.jsx(P,{children:r.get("ui_item_name")}),i?e.jsx(P,{children:r.get("ui_amount")}):e.jsxs(e.Fragment,{children:[e.jsx(P,{children:r.get("ui_min_count")}),e.jsx(P,{children:r.get("ui_max_count")})]}),e.jsx(P,{className:"w-24 text-center",children:r.get("ui_actions")})]})}),e.jsx(te,{children:e.jsx(Ie,{items:a.map(j=>j.id),strategy:De,children:a.map((j,h)=>e.jsxs(Hr,{id:j.id,children:[e.jsx(Ur,{id:j.id}),e.jsx($,{children:e.jsx(N,{name:`${s}.${h}.name`,control:n,render:({field:M})=>e.jsx(b,{children:e.jsx(v,{children:e.jsx(k,{...M,placeholder:r.get("ui_item_name")})})})})}),i?e.jsx($,{children:e.jsx(N,{name:`${s}.${h}.amount`,control:n,render:({field:M})=>e.jsx(b,{children:e.jsx(v,{children:e.jsx(k,{...M,type:"number",placeholder:r.get("ui_amount")})})})})}):e.jsxs(e.Fragment,{children:[e.jsx($,{children:e.jsx(N,{name:`${s}.${h}.amount.min`,control:n,render:({field:M})=>e.jsx(b,{children:e.jsx(v,{children:e.jsx(k,{...M,type:"number",placeholder:r.get("ui_min")})})})})}),e.jsx($,{children:e.jsx(N,{name:`${s}.${h}.amount.max`,control:n,render:({field:M})=>e.jsx(b,{children:e.jsx(v,{children:e.jsx(k,{...M,type:"number",placeholder:r.get("ui_max")})})})})})]}),e.jsx($,{className:"flex justify-center",children:e.jsx(D,{type:"button",variant:"outline",size:"icon",className:"text-red-500",onClick:()=>l(h),children:e.jsx(fe,{className:"h-4 w-4"})})})]},j.id))})})]})})})})})]})}function Gr({id:s,children:n}){const{attributes:t,listeners:i,setNodeRef:r,transform:a,transition:x}=le({id:s}),l={transform:ze.Transform.toString(a),transition:x};return e.jsx("div",{ref:r,style:l,children:n})}function Kr({id:s}){const{attributes:n,listeners:t,setNodeRef:i}=le({id:s});return e.jsx("div",{ref:i,...n,...t,children:e.jsx(qe,{className:"h-4 w-4 cursor-move shrink-0"})})}function Br({name:s,control:n,className:t}){const i=W(),{setValue:r,watch:a}=rs(),{fields:x,append:l,remove:u,move:_}=Ve({name:s,control:n}),m=is(),j=Fe(oe(Ue),oe(He,{coordinateGetter:Ze})),h=async E=>{m.setVisible(!1);const z=await Y("getCoordsInput",{forceDisableTarget:!0},{x:0,y:0,z:0},500);m.setVisible(!0),r(`${s}.${E}.coords`,z)},M=async E=>{m.setVisible(!1);const z=a(`${s}.${E}.coords`);await Y("teleport",z)};function R(E){const{active:z,over:p}=E;if(z.id!==p.id){const S=x.findIndex(y=>y.id===z.id),w=x.findIndex(y=>y.id===p.id);_(S,w)}}return e.jsxs("div",{className:T("space-y-4 shrink-0",t),children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(C,{children:i.get("ui_harvesting_zone_locations")}),e.jsx(D,{type:"button",variant:"outline",size:"sm",onClick:()=>l({coords:{x:0,y:0,z:0},radius:5,interval:2*6e4}),className:"shrink-0",children:i.get("ui_add_harvesting_zone_location")})]}),e.jsx(ce,{children:e.jsx(ue,{className:"px-0 flex flex-col",children:e.jsx(he,{className:"h-96 pr-2",children:e.jsx(we,{sensors:j,collisionDetection:Ce,onDragEnd:R,modifiers:[Se,ke],children:e.jsx(Ie,{items:x.map(E=>E.id),strategy:De,children:x.map((E,z)=>e.jsx(Gr,{id:E.id,children:e.jsxs("div",{className:"flex items-start space-x-2 p-2 border-b",children:[e.jsx(Kr,{id:E.id}),e.jsxs("div",{className:"flex-grow space-y-4 pb-2 px-4",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx(C,{children:i.get("ui_coords")}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"flex-grow grid grid-cols-3 gap-2",children:["x","y","z"].map(p=>e.jsx(N,{name:`${s}.${z}.coords.${p}`,control:n,render:({field:S})=>e.jsx(b,{children:e.jsx(v,{children:e.jsx(k,{...S,type:"number",placeholder:p.toUpperCase(),step:"0.01",className:"w-full"})})})},`coords.${p}`))}),e.jsx(D,{type:"button",variant:"outline",size:"icon",className:"shrink-0",onClick:()=>h(z),children:e.jsx(as,{className:"h-4 w-4"})}),e.jsx(D,{type:"button",variant:"outline",size:"icon",className:"shrink-0",onClick:()=>M(z),children:e.jsx(Oe,{className:"h-4 w-4"})})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsx(N,{name:`${s}.${z}.radius`,control:n,render:({field:p})=>e.jsxs(b,{children:[e.jsx(C,{children:i.get("ui_radius")}),e.jsx(v,{children:e.jsx(k,{...p,type:"number",step:"0.1",className:"w-full"})})]})}),e.jsx(N,{name:`${s}.${z}.interval`,control:n,render:({field:p})=>e.jsxs(b,{children:[e.jsx(C,{children:i.get("ui_interval")}),e.jsx(v,{children:e.jsx(k,{...p,type:"number",className:"w-full"})})]})})]})]}),e.jsx(D,{type:"button",variant:"outline",size:"icon",className:"text-red-500 shrink-0",onClick:()=>u(z),children:e.jsx(fe,{className:"h-4 w-4"})})]})},E.id))})})})})})]})}const ds=({name:s,description:n})=>{const t=W(),{control:i,formState:r}=rs();return e.jsx(N,{control:i,name:s,render:({field:a})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:t.get("ui_interaction_style")}),e.jsxs(ms,{onValueChange:x=>a.onChange(x==="true"?!0:x==="false"?!1:void 0),defaultValue:a.value===!0?"true":a.value===!1?"false":"undefined",children:[e.jsx(v,{children:e.jsx(ls,{children:e.jsx(hs,{placeholder:"Select targeting option"})})}),e.jsxs(cs,{children:[e.jsx(Q,{value:"true",children:t.get("ui_target")}),e.jsx(Q,{value:"false",children:t.get("ui_3d_prompts_or_text_ui")}),e.jsx(Q,{value:"undefined",children:t.get("ui_inherit_global")})]})]}),e.jsx(L,{children:n}),e.jsx(O,{})]})})},Yr=q({label:B().min(1,"ui_error_required"),progress:B().min(1,"ui_error_required"),icon:B().min(1,"ui_error_required"),radius:I,duration:I,animation:ws.optional(),animationProp:Cs.optional(),requiredItem:B().optional(),model:B().min(1,"ui_error_required"),interactionOffset:q({x:I,y:I,z:I}),maxSpawned:I,giveItems:re(q({name:B().min(1,"ui_error_required"),amount:q({min:I,max:I})})),locations:re(q({coords:q({x:I,y:I,z:I}),radius:I,interval:I})),target:xe().optional()}),mn={label:"",progress:"",radius:1.25,duration:5e3,icon:"hand",giveItems:[{name:"",amount:{min:1,max:3}}],interactionOffset:{x:0,y:0,z:0},locations:[{coords:{x:0,y:0,z:0},radius:5,interval:2*6e4}],model:"",maxSpawned:30};function Wr({isOpen:s,setIsOpen:n,editingZone:t,onSubmit:i}){const r=Te({resolver:Pe(Yr),defaultValues:t||mn}),a=W();o.useEffect(()=>{t?r.reset(t):r.reset(mn)},[t,r]);function x(l){i(l),n(!1)}return e.jsx(Ee,{open:s,onOpenChange:n,children:e.jsxs(ve,{"aria-describedby":void 0,className:"min-w-fit",children:[e.jsx(Ne,{children:e.jsx(ye,{children:t?a.get("ui_edit_harvesting_zone"):a.get("ui_add_harvesting_zone")})}),e.jsx(Le,{...r,children:e.jsxs("form",{onSubmit:r.handleSubmit(x,$e),className:"h-fit py-4 w-[50rem]",children:[e.jsxs(Je,{defaultValue:"basic",children:[e.jsxs(Re,{className:"grid w-full grid-cols-4",children:[e.jsx(X,{value:"basic",children:a.get("ui_basic")}),e.jsx(X,{value:"items",children:a.get("ui_items")}),e.jsx(X,{value:"locations",children:a.get("ui_locations")}),e.jsx(X,{value:"animation",children:a.get("ui_animation")})]}),e.jsxs(J,{value:"basic",className:"space-y-4 shrink-0 py-4",children:[e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:r.control,name:"label",render:({field:l})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_interaction_label")}),e.jsx(v,{children:e.jsx(k,{...l,placeholder:a.get("ui_harvest")})}),e.jsx(L,{children:a.get("ui_interaction_label_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:r.control,name:"progress",render:({field:l})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_progress")}),e.jsx(v,{children:e.jsx(k,{...l,placeholder:a.get("ui_harvesting")})}),e.jsx(L,{children:a.get("ui_progress_desc")}),e.jsx(O,{})]})})]}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:r.control,name:"icon",render:({field:l})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_icon")}),e.jsx(v,{children:e.jsx(k,{...l})}),e.jsx(L,{children:a.get("ui_icon_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:r.control,name:"radius",render:({field:l})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_radius")}),e.jsx(v,{children:e.jsx(k,{type:"number",...l})}),e.jsx(L,{children:a.get("ui_radius_desc")}),e.jsx(O,{})]})})]}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:r.control,name:"duration",render:({field:l})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_duration")}),e.jsx(v,{children:e.jsx(k,{type:"number",...l})}),e.jsx(L,{children:a.get("ui_duration_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:r.control,name:"requiredItem",render:({field:l})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_required_item")}),e.jsx(v,{children:e.jsx(k,{...l})}),e.jsx(L,{children:a.get("ui_required_item_desc")}),e.jsx(O,{})]})})]}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:r.control,name:"model",render:({field:l})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_model")}),e.jsx(v,{children:e.jsx(k,{...l,placeholder:"prop_weed_01"})}),e.jsx(L,{children:a.get("ui_model_harvest_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:r.control,name:"maxSpawned",render:({field:l})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_max_spawned")}),e.jsx(v,{children:e.jsx(k,{type:"number",...l})}),e.jsx(L,{children:a.get("ui_max_spawned_desc")}),e.jsx(O,{})]})})]}),e.jsx("div",{className:"flex space-x-4 pr-4",children:e.jsx(ds,{name:"target",description:a.get("ui_target_desc")})}),e.jsx(Ps,{name:"interactionOffset",label:a.get("ui_interaction_offset"),description:a.get("ui_interaction_offset_desc"),disableButton:!0})]}),e.jsx(J,{value:"items",className:"space-y-4 shrink-0 py-4",children:e.jsx(Bs,{name:"giveItems",control:r.control})}),e.jsx(J,{value:"animation",className:"space-y-4 shrink-0 py-4",children:e.jsx(Ss,{name:"animation"})}),e.jsx(J,{value:"locations",className:"space-y-4 shrink-0 py-4",children:e.jsx(Br,{name:"locations",control:r.control})})]}),e.jsx(D,{type:"submit",className:"w-full",children:t?a.get("ui_save_changes"):a.get("ui_add_harvesting_zone")})]})})]})})}const Qe=Ya,es=Wa,Xr=o.forwardRef(({className:s,inset:n,children:t,...i},r)=>e.jsxs(it,{ref:r,className:T("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",n&&"pl-8",s),...i,children:[t,e.jsx(Ae,{className:"ml-auto h-4 w-4"})]}));Xr.displayName=it.displayName;const Jr=o.forwardRef(({className:s,...n},t)=>e.jsx(lt,{ref:t,className:T("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...n}));Jr.displayName=lt.displayName;const Me=o.forwardRef(({className:s,sideOffset:n=4,...t},i)=>e.jsx(Ka,{children:e.jsx(ct,{ref:i,sideOffset:n,className:T("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...t})}));Me.displayName=ct.displayName;const ae=o.forwardRef(({className:s,inset:n,...t},i)=>e.jsx(ot,{ref:i,className:T("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",n&&"pl-8",s),...t}));ae.displayName=ot.displayName;const Qr=o.forwardRef(({className:s,children:n,checked:t,...i},r)=>e.jsxs(dt,{ref:r,className:T("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),checked:t,...i,children:[e.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:e.jsx(xt,{children:e.jsx(vs,{className:"h-4 w-4"})})}),n]}));Qr.displayName=dt.displayName;const ei=o.forwardRef(({className:s,children:n,...t},i)=>e.jsxs(ut,{ref:i,className:T("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...t,children:[e.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:e.jsx(xt,{children:e.jsx(Ba,{className:"h-2 w-2 fill-current"})})}),n]}));ei.displayName=ut.displayName;const si=o.forwardRef(({className:s,inset:n,...t},i)=>e.jsx(mt,{ref:i,className:T("px-2 py-1.5 text-sm font-semibold",n&&"pl-8",s),...t}));si.displayName=mt.displayName;const ni=o.forwardRef(({className:s,...n},t)=>e.jsx(ht,{ref:t,className:T("-mx-1 my-1 h-px bg-muted",s),...n}));ni.displayName=ht.displayName;const ss=({getContent:s,onDuplicate:n,isDropdown:t,text:i})=>{const r=W(),a=()=>{const x=s();n(x),ts.success(r.get("ui_duplicated")||"Duplicated successfully")};return t?e.jsxs(ae,{onClick:a,children:[e.jsx(rn,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:i||r.get("ui_duplicate")||"Duplicate"})]}):e.jsx(D,{variant:"outline",size:"icon",onClick:a,children:e.jsx(rn,{className:"h-4 w-4"})})};function ns({searchTerm:s}){const n=W();return e.jsxs("div",{className:"flex flex-col items-center justify-center h-[40vh] gap-6",children:[e.jsx("div",{className:"flex items-center justify-center w-20 h-20 bg-secondary rounded-full",children:s.length>0?e.jsx(jt,{className:"w-10 h-10 text-secondary-foreground"}):e.jsx(Xa,{className:"w-10 h-10 text-secondary-foreground"})}),e.jsx("div",{className:"space-y-2 text-center",children:s.length>0?e.jsxs(e.Fragment,{children:[e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:n.get("ui_no_results")}),e.jsx("p",{className:"text-muted-foreground",children:n.get("ui_no_results_description")})]}):e.jsxs(e.Fragment,{children:[e.jsx("h2",{className:"text-2xl font-bold tracking-tight",children:n.get("ui_no_data_to_display")}),e.jsx("p",{className:"text-muted-foreground",children:n.get("ui_no_data_to_display_description")})]})})]})}function ti(){const{serverConfig:s,updateHarvestingZone:n}=ie(),[t,i]=o.useState((s==null?void 0:s.harvestingZones)||[]),[r,a]=o.useState(!1),[x,l]=o.useState(null),[u,_]=o.useState(null),[m,j]=o.useState(1),h=6,[M,R]=o.useState(""),[E]=Ge(M,300),z=Xe(),p=W(),S=g=>{a(g),setTimeout(()=>{g||(l(null),_(null))},200)};o.useEffect(()=>{s&&i(s.harvestingZones||[])},[s]);const w=g=>{if(s){const c=Array.isArray(g)?g:[g],d=t.length,f=[...t,...c];i(f),c.forEach((F,ee)=>{n(d+ee,F)})}a(!1)},y=g=>{if(x&&u!==null){const c=t.map((d,f)=>f===u?g:d);i(c),n(u,g),a(!1),setTimeout(()=>{l(null),_(null)},500)}},A=async g=>{if(await z(p.get("ui_delete_harvesting_zone"),p.get("ui_delete_harvesting_zone_desc"))){const c=t.filter((d,f)=>f!==g);i(c),s&&ie.getState().updateHarvestingZones(c),U.length===1&&m>1&&j(m-1)}},G=g=>{if(s){const c={...g},d=[...t,c];i(d);const f=d.length-1;n(f,c);const F=Math.ceil(d.length/h);j(F)}},H=o.useMemo(()=>t.filter(g=>{var c;return((c=g.label)==null?void 0:c.toLowerCase().includes(E.toLowerCase()))||g.progress.toLowerCase().includes(E.toLowerCase())}),[t,E]),U=o.useMemo(()=>{const g=(m-1)*h;return H.slice(g,g+h)},[H,m]),V=Math.ceil(H.length/h),Z=o.useCallback(g=>{R(g.target.value),j(1)},[]);return e.jsxs("div",{className:"space-y-4 flex flex-col h-full",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(k,{placeholder:p.get("ui_search_harvesting_zones"),value:M,onChange:Z,className:"max-w-sm"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(D,{onClick:()=>a(!0),children:[e.jsx(je,{className:"mr-2 h-4 w-4"}),p.get("ui_add_harvesting_zone")]}),e.jsx(Wr,{isOpen:r,editingZone:x||void 0,setIsOpen:S,onSubmit:x?y:w})]})]}),e.jsx("div",{className:"grow",children:e.jsxs(se,{className:"table-fixed",children:[e.jsx(ne,{children:e.jsxs(K,{children:[e.jsx(P,{children:p.get("ui_label")}),e.jsx(P,{children:p.get("ui_progress")}),e.jsx(P,{children:p.get("ui_duration")}),e.jsx(P,{children:p.get("ui_actions")})]})}),e.jsx(te,{children:U.length===0?e.jsx(K,{className:"hover:bg-background",children:e.jsx($,{colSpan:4,className:"h-full hover:bg-background",children:e.jsx(ns,{searchTerm:E})})}):U.map((g,c)=>{const d=(m-1)*h+c;return e.jsxs(K,{children:[e.jsx($,{children:g.label}),e.jsx($,{children:g.progress}),e.jsx($,{children:g.duration}),e.jsx($,{children:e.jsxs(Qe,{children:[e.jsx(es,{asChild:!0,children:e.jsxs(D,{variant:"ghost",className:"h-8 w-8 p-0",children:[e.jsx("span",{className:"sr-only",children:"Open menu"}),e.jsx(Ke,{className:"h-4 w-4"})]})}),e.jsxs(Me,{align:"end",children:[e.jsxs(ae,{onClick:()=>{l(g),_(d),a(!0)},children:[e.jsx(Be,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:p.get("ui_edit")})]}),e.jsx(ss,{getContent:()=>g,onDuplicate:G,isDropdown:!0}),e.jsxs(ae,{className:"text-red-500",onClick:()=>A(d),children:[e.jsx(Ye,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:p.get("ui_delete")})]})]})]})})]},d)})})]})}),e.jsxs("div",{className:"flex justify-end items-center space-x-2",children:[e.jsx(D,{onClick:()=>j(g=>Math.max(g-1,1)),disabled:m===1,variant:"outline",children:e.jsx(We,{className:"h-4 w-4"})}),e.jsx("div",{className:"flex items-center space-x-2",children:[...Array(V)].map((g,c)=>{const d=c+1;return d===1||d===V||d>=m-1&&d<=m+1?e.jsx(D,{variant:m===d?"outline":"ghost",size:"icon",onClick:()=>j(d),children:d},c):d===m-2||d===m+2?e.jsx("span",{children:"..."},c):null})}),e.jsx(D,{onClick:()=>j(g=>Math.min(g+1,V)),disabled:m===V||V===0,variant:"outline",children:e.jsx(Ae,{className:"h-4 w-4"})})]})]})}const ai=q({coords:q({x:I,y:I,z:I}),animOrigin:q({x:I,y:I,z:I,w:I}).optional()});re(ai);function ri({id:s,children:n}){const{attributes:t,listeners:i,setNodeRef:r,transform:a,transition:x}=le({id:s}),l={transform:ze.Transform.toString(a),transition:x};return e.jsx("div",{ref:r,style:l,children:n})}function ii({id:s}){const{attributes:n,listeners:t,setNodeRef:i}=le({id:s});return e.jsx("div",{ref:i,...n,...t,children:e.jsx(qe,{className:"h-4 w-4 cursor-move shrink-0"})})}function li({name:s,control:n,className:t}){var S,w;const{fields:i,append:r,remove:a,move:x}=Ve({name:s,control:n}),{setValue:l,watch:u}=rs(),_=is(),m=W(),h=!!((S=n._formValues)==null?void 0:S.syncedScene),M=(w=n._formValues)==null?void 0:w.target,R=Fe(oe(Ue),oe(He,{coordinateGetter:Ze})),E=async(y,A)=>{_.setVisible(!1);const G=await Y("getCoordsInput",{target:A==="coords"?M:!1,is4D:A==="animOrigin",forceDisableTarget:A==="animOrigin"},A==="coords"?{x:0,y:0,z:0}:{x:0,y:0,z:0,w:0},500);_.setVisible(!0),l(`${s}.${y}.${A}`,G)},z=async(y,A)=>{_.setVisible(!1);const G=u(`${s}.${y}.${A}`);await Y("teleport",G)};function p(y){const{active:A,over:G}=y;if(A.id!==G.id){const H=i.findIndex(V=>V.id===A.id),U=i.findIndex(V=>V.id===G.id);x(H,U)}}return e.jsxs("div",{className:T("space-y-4 shrink-0",t),children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(C,{children:m.get("ui_locations")}),e.jsx(D,{type:"button",variant:"outline",size:"sm",onClick:()=>r({coords:{x:0,y:0,z:0}}),className:"shrink-0",children:m.get("ui_add_location")})]}),e.jsx(ce,{children:e.jsx(ue,{className:"px-0 flex flex-col",children:e.jsx(he,{className:"h-96 pr-2",children:e.jsx(we,{sensors:R,collisionDetection:Ce,onDragEnd:p,modifiers:[Se,ke],children:e.jsx(Ie,{items:i.map(y=>y.id),strategy:De,children:i.map((y,A)=>e.jsx(ri,{id:y.id,children:e.jsxs("div",{className:"flex items-center space-x-2 p-2 border-b",children:[e.jsx(ii,{id:y.id}),e.jsxs("div",{className:"flex-grow space-y-2 px-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs(C,{className:"w-20 flex items-center",children:[m.get("ui_coords"),":"]}),e.jsx("div",{className:"flex-grow grid grid-cols-3 gap-2",children:["x","y","z"].map(G=>e.jsx(N,{name:`${s}.${A}.coords.${G}`,control:n,render:({field:H})=>e.jsx(b,{children:e.jsx(v,{children:e.jsx(k,{...H,type:"number",placeholder:G.toUpperCase(),step:"0.01",className:"w-full"})})})},`coords.${G}`))}),e.jsx(D,{type:"button",variant:"outline",size:"icon",className:"shrink-0",onClick:()=>E(A,"coords"),children:e.jsx(as,{className:"h-4 w-4"})}),e.jsx(D,{type:"button",variant:"outline",size:"icon",className:"shrink-0",onClick:()=>z(A,"coords"),children:e.jsx(Oe,{className:"h-4 w-4"})})]}),h&&e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs(C,{className:"w-20 flex items-center",children:[m.get("ui_origin"),":"]}),e.jsx("div",{className:"flex-grow grid grid-cols-4 gap-2",children:["x","y","z","w"].map(G=>e.jsx(N,{name:`${s}.${A}.animOrigin.${G}`,control:n,render:({field:H})=>e.jsx(b,{children:e.jsx(v,{children:e.jsx(k,{...H,type:"number",placeholder:G.toUpperCase(),step:"0.01",className:"w-full"})})})},`animOrigin.${G}`))}),e.jsx(D,{type:"button",variant:"outline",size:"icon",className:"shrink-0",onClick:()=>E(A,"animOrigin"),children:e.jsx(as,{className:"h-4 w-4"})}),e.jsx(D,{type:"button",variant:"outline",size:"icon",className:"shrink-0",onClick:()=>z(A,"animOrigin"),children:e.jsx(Oe,{className:"h-4 w-4"})})]})]}),e.jsx(D,{type:"button",variant:"outline",size:"icon",className:"text-red-500 shrink-0",onClick:()=>a(A),children:e.jsx(fe,{className:"h-4 w-4"})})]})},y.id))})})})})})]})}const os=o.forwardRef(({className:s,orientation:n="horizontal",decorative:t=!0,...i},r)=>e.jsx(gt,{ref:r,decorative:t,orientation:n,className:T("shrink-0 bg-border",n==="horizontal"?"h-[1px] w-full":"h-full w-[1px]",s),...i}));os.displayName=gt.displayName;const ci=$s("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function gs({className:s,variant:n,...t}){return e.jsx("div",{className:T(ci({variant:n}),s),...t})}const Kt=Qa,Bt=er,sn=o.forwardRef(({className:s,align:n="center",sideOffset:t=4,...i},r)=>e.jsx(Ja,{children:e.jsx(pt,{ref:r,align:n,sideOffset:t,className:T("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...i})}));sn.displayName=pt.displayName;const Yt=o.forwardRef(({className:s,...n},t)=>e.jsx(_e,{ref:t,className:T("flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground",s),...n}));Yt.displayName=_e.displayName;const Wt=o.forwardRef(({className:s,...n},t)=>e.jsxs("div",{className:"flex items-center border-b px-3","cmdk-input-wrapper":"",children:[e.jsx(jt,{className:"mr-2 h-4 w-4 shrink-0 opacity-50"}),e.jsx(_e.Input,{ref:t,className:T("flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",s),...n})]}));Wt.displayName=_e.Input.displayName;const Xt=o.forwardRef(({className:s,...n},t)=>e.jsx(_e.List,{ref:t,className:T("max-h-[300px] overflow-y-auto overflow-x-hidden",s),...n}));Xt.displayName=_e.List.displayName;const Jt=o.forwardRef((s,n)=>e.jsx(_e.Empty,{ref:n,className:"py-6 text-center text-sm",...s}));Jt.displayName=_e.Empty.displayName;const Ys=o.forwardRef(({className:s,...n},t)=>e.jsx(_e.Group,{ref:t,className:T("overflow-hidden p-1 text-foreground [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground",s),...n}));Ys.displayName=_e.Group.displayName;const Qt=o.forwardRef(({className:s,...n},t)=>e.jsx(_e.Separator,{ref:t,className:T("-mx-1 h-px bg-border",s),...n}));Qt.displayName=_e.Separator.displayName;const bs=o.forwardRef(({className:s,...n},t)=>e.jsx(_e.Item,{ref:t,className:T("relative flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled=true]:pointer-events-none data-[selected='true']:bg-accent data-[selected=true]:text-accent-foreground data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",s),...n}));bs.displayName=_e.Item.displayName;const hn=$s("m-1 transition ease-in-out delay-150 hover:-translate-y-1 hover:scale-110 duration-300",{variants:{variant:{default:"border-foreground/10 text-foreground bg-card hover:bg-card/80",secondary:"border-foreground/10 bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",inverted:"inverted"}},defaultVariants:{variant:"default"}}),ea=o.forwardRef(({options:s,onValueChange:n,variant:t,defaultValue:i=[],placeholder:r="Select options",animation:a=0,maxCount:x=1,modalPopover:l=!1,asChild:u=!1,className:_,...m},j)=>{const[h,M]=o.useState(i),[R,E]=o.useState(!1),[z,p]=o.useState(!1),S=W(),w=V=>{if(V.key==="Enter")E(!0);else if(V.key==="Backspace"&&!V.currentTarget.value){const Z=[...h];Z.pop(),M(Z),n(Z)}},y=V=>{const Z=h.includes(V)?h.filter(g=>g!==V):[...h,V];M(Z),n(Z)},A=()=>{M([]),n([])},G=()=>{E(V=>!V)},H=()=>{const V=h.slice(0,x);M(V),n(V)},U=()=>{if(h.length===s.length)A();else{const V=s.map(Z=>Z.value);M(V),n(V)}};return e.jsxs(Kt,{open:R,onOpenChange:E,modal:l,children:[e.jsx(Bt,{asChild:!0,children:e.jsx(D,{ref:j,...m,onClick:G,className:T("flex w-full p-1 rounded-md border min-h-10 h-auto items-center justify-between bg-inherit hover:bg-inherit [&_svg]:pointer-events-auto",_),children:h.length>0?e.jsxs("div",{className:"flex justify-between items-center w-full",children:[e.jsxs("div",{className:"flex flex-wrap items-center",children:[h.slice(0,x).map(V=>{const Z=s.find(c=>c.value===V),g=Z==null?void 0:Z.icon;return e.jsxs(gs,{className:T(z?"animate-bounce":"",hn({variant:t})),style:{animationDuration:`${a}s`},children:[g&&e.jsx(g,{className:"h-4 w-4 mr-2"}),Z==null?void 0:Z.label,e.jsx(ln,{className:"ml-2 h-4 w-4 cursor-pointer",onClick:c=>{c.stopPropagation(),y(V)}})]},V)}),h.length>x&&e.jsxs(gs,{className:T("bg-transparent text-foreground border-foreground/1 hover:bg-transparent",z?"animate-bounce":"",hn({variant:t})),style:{animationDuration:`${a}s`},children:[`+ ${h.length-x} more`,e.jsx(ln,{className:"ml-2 h-4 w-4 cursor-pointer",onClick:V=>{V.stopPropagation(),H()}})]})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(An,{className:"h-4 mx-2 cursor-pointer text-muted-foreground",onClick:V=>{V.stopPropagation(),A()}}),e.jsx(os,{orientation:"vertical",className:"flex min-h-6 h-full"}),e.jsx(ps,{className:"h-4 mx-2 cursor-pointer text-muted-foreground"})]})]}):e.jsxs("div",{className:"flex items-center justify-between w-full mx-auto",children:[e.jsx("span",{className:"text-sm text-muted-foreground mx-3",children:r}),e.jsx(ps,{className:"h-4 cursor-pointer text-muted-foreground mx-2"})]})})}),e.jsx(sn,{className:"w-auto p-0",align:"start",onEscapeKeyDown:()=>E(!1),children:e.jsxs(Yt,{children:[e.jsx(Wt,{placeholder:"Search...",onKeyDown:w}),e.jsxs(Xt,{children:[e.jsx(Jt,{children:"No results found."}),e.jsxs(Ys,{children:[e.jsxs(bs,{onSelect:U,className:"cursor-pointer",children:[e.jsx("div",{className:T("mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",h.length===s.length?"bg-primary text-primary-foreground":"opacity-50 [&_svg]:invisible"),children:e.jsx(vs,{className:"h-4 w-4"})}),e.jsx("span",{children:S.get("ui_select_all")})]},"all"),s.map(V=>{const Z=h.includes(V.value);return e.jsxs(bs,{onSelect:()=>y(V.value),className:"cursor-pointer",children:[e.jsx("div",{className:T("mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",Z?"bg-primary text-primary-foreground":"opacity-50 [&_svg]:invisible"),children:e.jsx(vs,{className:"h-4 w-4"})}),V.icon&&e.jsx(V.icon,{className:"mr-2 h-4 w-4 text-muted-foreground"}),e.jsx("span",{children:V.label})]},V.value)})]}),e.jsx(Qt,{}),e.jsx(Ys,{children:e.jsxs("div",{className:"flex items-center justify-between",children:[h.length>0&&e.jsxs(e.Fragment,{children:[e.jsx(bs,{onSelect:A,className:"flex-1 justify-center cursor-pointer",children:S.get("ui_clear")}),e.jsx(os,{orientation:"vertical",className:"flex min-h-6 h-full"})]}),e.jsx(bs,{onSelect:()=>E(!1),className:"flex-1 justify-center cursor-pointer max-w-full",children:S.get("ui_close")})]})})]})]})}),a>0&&h.length>0&&e.jsx(sr,{className:T("cursor-pointer my-2 text-foreground bg-background w-3 h-3",z?"":"text-muted-foreground"),onClick:()=>p(!z)})]})});ea.displayName="MultiSelect";const oi=["coke_cutting","coke_packing_basic","coke_packing_advanced","coke_unpacking","meth_breaking","meth_packing","meth_cooking","weed_sorting_left","weed_sorting_right","weed_sorting_left_v2","weed_sorting_right_v2"],di=q({label:B().min(1,"ui_error_required"),icon:B().min(1,"ui_error_required"),duration:I,progress:B().min(1,"ui_error_required"),radius:I,errorMessage:B().optional(),requiredItems:re(q({name:B().min(1,"ui_error_required"),amount:I})),giveItems:re(q({name:B().min(1,"ui_error_required"),amount:q({min:I,max:I})})),animation:ws.optional(),animationProp:Cs.optional(),syncedScene:ft(oi).optional(),requiresLab:re(Ts()).default([]),locations:re(q({coords:q({x:I,y:I,z:I}),animOrigin:q({x:I,y:I,z:I,w:I}).optional()})),target:xe().optional()}),jn={label:"",duration:1e4,radius:1.25,icon:"hand",progress:"",requiredItems:[{name:"",amount:1}],giveItems:[{name:"",amount:{min:1,max:3}}],locations:[{coords:{x:0,y:0,z:0},animOrigin:{x:0,y:0,z:0,w:0}}],requiresLab:[]};function xi({isOpen:s,setIsOpen:n,editingZone:t,onSubmit:i}){const r=Te({resolver:Pe(di),defaultValues:t||jn}),a=W(),{serverConfig:x}=ie(),l=(x==null?void 0:x.laboratories)||{};o.useState(new Date().getTime()),o.useEffect(()=>{t?r.reset(t):r.reset(jn)},[t,r]);function u(j){const h={...j};i(h),n(!1)}const _=Object.entries(l).map(([j,h])=>({id:parseInt(j),...h})),m=_.filter(j=>j.label&&j.label.length>0).map(j=>({value:j.id.toString(),label:`${j.label} - ${a.get(`ui_${j.iplTier}_tier`)}`}));return e.jsx(Ee,{open:s,onOpenChange:n,children:e.jsxs(ve,{"aria-describedby":void 0,className:"min-w-fit max-w-[50rem]",children:[e.jsx(Ne,{children:e.jsx(ye,{children:t?a.get("ui_edit_processing_zone"):a.get("ui_add_processing_zone")})}),e.jsx(Le,{...r,children:e.jsxs("form",{onSubmit:r.handleSubmit(u,$e),className:"h-fit py-4 w-[50rem]",children:[e.jsxs(Je,{defaultValue:"basic",children:[e.jsxs(Re,{className:"grid w-full grid-cols-5",children:[e.jsx(X,{value:"basic",children:a.get("ui_basic")}),e.jsx(X,{value:"requiredItems",children:a.get("ui_required_items")}),e.jsx(X,{value:"giveItems",children:a.get("ui_give_items")}),e.jsx(X,{value:"locations",children:a.get("ui_locations")}),e.jsx(X,{value:"animation",children:a.get("ui_animation")})]}),e.jsxs(J,{value:"basic",className:"space-y-4 shrink-0 py-4",children:[e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:r.control,name:"label",render:({field:j})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_interaction_label")}),e.jsx(v,{children:e.jsx(k,{...j,placeholder:"Process"})}),e.jsx(L,{children:a.get("ui_interaction_label_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:r.control,name:"progress",render:({field:j})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_progress")}),e.jsx(v,{children:e.jsx(k,{...j,placeholder:a.get("ui_progress_placeholder")})}),e.jsx(L,{children:a.get("ui_progress_desc")}),e.jsx(O,{})]})})]}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:r.control,name:"icon",render:({field:j})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_icon")}),e.jsx(v,{children:e.jsx(k,{...j})}),e.jsx(L,{children:a.get("ui_icon_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:r.control,name:"radius",render:({field:j})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_radius")}),e.jsx(v,{children:e.jsx(k,{type:"number",...j})}),e.jsx(L,{children:a.get("ui_radius_desc")}),e.jsx(O,{})]})})]}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:r.control,name:"duration",render:({field:j})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_duration")}),e.jsx(v,{children:e.jsx(k,{type:"number",...j})}),e.jsx(L,{children:a.get("ui_duration_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:r.control,name:"errorMessage",render:({field:j})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_error_message")}),e.jsx(v,{children:e.jsx(k,{...j})}),e.jsx(L,{children:a.get("ui_error_message_desc")}),e.jsx(O,{})]})})]}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(ds,{name:"target",description:a.get("ui_target_desc")}),e.jsx(N,{control:r.control,name:"requiresLab",render:({field:j})=>{var h;return e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_required_labs")}),e.jsx(v,{children:e.jsx(ea,{options:m,onValueChange:M=>{j.onChange(M.map(R=>parseInt(R)))},defaultValue:((h=j.value)==null?void 0:h.map(M=>M.toString()))||[],placeholder:a.get("ui_select_required_labs"),disabled:_.length===0})}),e.jsx(L,{children:a.get("ui_required_labs_desc")}),e.jsx(O,{})]})}})]})]}),e.jsx(J,{value:"requiredItems",className:"space-y-4 shrink-0 py-4",children:e.jsx(Bs,{name:"requiredItems",control:r.control,disableRandom:!0})}),e.jsx(J,{value:"giveItems",className:"space-y-4 shrink-0 py-4",children:e.jsx(Bs,{name:"giveItems",control:r.control})}),e.jsx(J,{value:"animation",className:"space-y-4 shrink-0 py-4",children:e.jsx(Ss,{name:"animation",syncedScene:!0})}),e.jsx(J,{value:"locations",className:"space-y-4 shrink-0 py-4",children:e.jsx(li,{name:"locations",control:r.control})})]}),e.jsx(D,{type:"submit",className:"w-full mt-4",children:t?a.get("ui_save_changes"):a.get("ui_add_processing_zone")})]})})]})})}function ui(){const{serverConfig:s,updateProcessingZone:n}=ie(),[t,i]=o.useState((s==null?void 0:s.processingZones)||[]),[r,a]=o.useState(!1),[x,l]=o.useState(null),[u,_]=o.useState(null),[m,j]=o.useState(1),h=6,[M,R]=o.useState(""),[E]=Ge(M,300),z=Xe(),p=W(),S=(s==null?void 0:s.laboratories)||{},w=d=>{a(d),setTimeout(()=>{d||(l(null),_(null))},200)};o.useEffect(()=>{s&&i(s.processingZones||[])},[s]);const y=d=>{if(s){const f=Array.isArray(d)?d:[d],F=t.length,ee=[...t,...f];i(ee),f.forEach((me,xs)=>{n(F+xs,me)})}a(!1)},A=d=>{if(x&&u!==null){const f=t.map((F,ee)=>ee===u?d:F);i(f),n(u,d),a(!1),setTimeout(()=>{l(null),_(null)},500)}},G=async d=>{if(await z(p.get("ui_delete_processing_zone"),p.get("ui_delete_processing_zone_desc"))){const f=t.filter((F,ee)=>ee!==d);i(f),s&&ie.getState().updateProcessingZones(f),V.length===1&&m>1&&j(m-1)}},H=d=>{if(s){const f={...d},F=[...t,f];i(F);const ee=F.length-1;n(ee,f);const me=Math.ceil(F.length/h);j(me)}},U=o.useMemo(()=>t.filter(d=>{var f;return((f=d.label)==null?void 0:f.toLowerCase().includes(E.toLowerCase()))||d.progress.toLowerCase().includes(E.toLowerCase())}),[t,E]),V=o.useMemo(()=>{const d=(m-1)*h;return U.slice(d,d+h)},[U,m]),Z=Math.ceil(U.length/h),g=o.useCallback(d=>{R(d.target.value),j(1)},[]),c=(d=[])=>d.length?d.map(f=>{const F=S[f];return F?{label:F.label,tier:F.iplTier}:null}).filter(Boolean):null;return e.jsxs("div",{className:"space-y-4 flex flex-col h-full",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(k,{placeholder:p.get("ui_search_processing_zones"),value:M,onChange:g,className:"max-w-sm"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(D,{onClick:()=>a(!0),children:[e.jsx(je,{className:"mr-2 h-4 w-4"}),p.get("ui_add_processing_zone")]}),e.jsx(xi,{isOpen:r,editingZone:x||void 0,setIsOpen:w,onSubmit:x?A:y})]})]}),e.jsx("div",{className:"grow",children:e.jsxs(se,{className:"table-fixed",children:[e.jsx(ne,{children:e.jsxs(K,{children:[e.jsx(P,{children:p.get("ui_label")}),e.jsx(P,{children:p.get("ui_duration")}),e.jsx(P,{children:p.get("ui_required_labs")}),e.jsx(P,{children:p.get("ui_actions")})]})}),e.jsx(te,{children:V.length===0?e.jsx(K,{className:"hover:bg-background",children:e.jsx($,{colSpan:5,className:"h-full hover:bg-background",children:e.jsx(ns,{searchTerm:E})})}):V.map((d,f)=>{const F=(m-1)*h+f,ee=c(d.requiresLab);return e.jsxs(K,{children:[e.jsx($,{children:d.label}),e.jsx($,{children:d.duration}),e.jsx($,{children:ee&&ee.length>0?ee.map((me,xs)=>e.jsxs("div",{children:[me==null?void 0:me.label," - ",p.get(`ui_${me==null?void 0:me.tier}_tier`)]},xs)):p.get("ui_no_laboratory_required")}),e.jsx($,{children:e.jsxs(Qe,{children:[e.jsx(es,{asChild:!0,children:e.jsxs(D,{variant:"ghost",className:"h-8 w-8 p-0",children:[e.jsx("span",{className:"sr-only",children:"Open menu"}),e.jsx(Ke,{className:"h-4 w-4"})]})}),e.jsxs(Me,{align:"end",children:[e.jsxs(ae,{onClick:()=>{l(d),_(F),a(!0)},children:[e.jsx(Be,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:p.get("ui_edit")})]}),e.jsx(ss,{getContent:()=>d,onDuplicate:H,isDropdown:!0}),e.jsxs(ae,{className:"text-red-500",onClick:()=>G(F),children:[e.jsx(Ye,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:p.get("ui_delete")})]})]})]})})]},F)})})]})}),e.jsxs("div",{className:"flex justify-end items-center space-x-2",children:[e.jsx(D,{onClick:()=>j(d=>Math.max(d-1,1)),disabled:m===1,variant:"outline",children:e.jsx(We,{className:"h-4 w-4"})}),e.jsx("div",{className:"flex items-center space-x-2",children:[...Array(Z)].map((d,f)=>{const F=f+1;return F===1||F===Z||F>=m-1&&F<=m+1?e.jsx(D,{variant:m===F?"outline":"ghost",size:"icon",onClick:()=>j(F),children:F},f):F===m-2||F===m+2?e.jsx("span",{children:"..."},f):null})}),e.jsx(D,{onClick:()=>j(d=>Math.min(d+1,Z)),disabled:m===Z||Z===0,variant:"outline",children:e.jsx(Ae,{className:"h-4 w-4"})})]})]})}const sa=o.forwardRef(({className:s,...n},t)=>e.jsx(_t,{className:T("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",s),...n,ref:t,children:e.jsx(nr,{className:T("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));sa.displayName=_t.displayName;const pe=o.forwardRef(({className:s,...n},t)=>e.jsxs(bt,{ref:t,className:T("relative flex w-full touch-none select-none items-center",s),...n,children:[e.jsx(tr,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:e.jsx(ar,{className:"absolute h-full bg-primary"})}),e.jsx(rr,{className:"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]}));pe.displayName=bt.displayName;const mi=q({model:B().min(1,"ui_error_required"),offsetZ:I}),hi=q({name:B().min(1,"ui_error_required"),amount:I,removeAfterUse:xe()}),ji=q({progressDuration:I,seedItem:B().min(1,"ui_error_required"),waterItem:B().min(1,"ui_error_required"),fertilizerItem:B().min(1,"ui_error_required"),requiredItems:re(hi),stages:re(mi).min(1,"ui_error_required"),grassOnly:xe(),giveItem:B().min(1,"ui_error_required"),giveSeedChance:I,maxGiveCount:I,maxPerPlayer:be,growthDuration:I,statsDecrement:I,blockRadius:I,target:xe().optional()}),gn={progressDuration:2e3,seedItem:"",waterItem:"",fertilizerItem:"",requiredItems:[{name:"",amount:1,removeAfterUse:!0}],stages:[{model:"",offsetZ:0}],grassOnly:!1,giveItem:"",giveSeedChance:.25,maxGiveCount:3,growthDuration:180,statsDecrement:.005,blockRadius:1.5};function pn({id:s,children:n}){const{setNodeRef:t,transform:i,transition:r}=le({id:s}),a={transform:ze.Transform.toString(i),transition:r};return e.jsx(K,{ref:t,style:a,children:n})}function fn({id:s}){const{attributes:n,listeners:t,setNodeRef:i}=le({id:s});return e.jsx($,{className:"w-10",children:e.jsx("div",{ref:i,...n,...t,className:"flex items-center justify-center cursor-grab",children:e.jsx(qe,{className:"h-4 w-4"})})})}function gi({isOpen:s,setIsOpen:n,editingPlant:t,onSubmit:i}){const r=Te({resolver:Pe(ji),defaultValues:t||gn}),a=W(),{fields:x,append:l,remove:u,move:_}=Ve({control:r.control,name:"requiredItems"}),{fields:m,append:j,remove:h,move:M}=Ve({control:r.control,name:"stages"}),R=Fe(oe(Ue),oe(He,{coordinateGetter:Ze})),E=S=>{const{active:w,over:y}=S;if(w.id!==y.id){const A=x.findIndex(H=>H.id===w.id),G=x.findIndex(H=>H.id===y.id);_(A,G)}},z=S=>{const{active:w,over:y}=S;if(w.id!==y.id){const A=m.findIndex(H=>H.id===w.id),G=m.findIndex(H=>H.id===y.id);M(A,G)}};o.useEffect(()=>{t?r.reset(t):r.reset(gn)},[t,r]);function p(S){i(S),n(!1)}return e.jsx(Ee,{open:s,onOpenChange:n,children:e.jsxs(ve,{"aria-describedby":void 0,className:"min-w-fit",children:[e.jsx(Ne,{children:e.jsx(ye,{children:t?a.get("ui_edit_plant"):a.get("ui_add_plant")})}),e.jsx(Le,{...r,children:e.jsxs("form",{onSubmit:r.handleSubmit(p,$e),className:"h-fit py-4 w-[50rem]",children:[e.jsxs(Je,{defaultValue:"basic",children:[e.jsxs(Re,{className:"grid w-full grid-cols-3",children:[e.jsx(X,{value:"basic",children:a.get("ui_basic")}),e.jsx(X,{value:"items",children:a.get("ui_items")}),e.jsx(X,{value:"stages",children:a.get("ui_growth_stages")})]}),e.jsxs(J,{value:"basic",className:"space-y-4 shrink-0 py-4",children:[e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:r.control,name:"progressDuration",render:({field:S})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_progress_duration")}),e.jsx(v,{children:e.jsx(k,{type:"number",...S})}),e.jsx(L,{children:a.get("ui_plant_duration_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:r.control,name:"growthDuration",render:({field:S})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_growth_duration")}),e.jsx(v,{children:e.jsx(k,{type:"number",...S})}),e.jsx(L,{children:a.get("ui_growth_duration_desc")}),e.jsx(O,{})]})})]}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:r.control,name:"statsDecrement",render:({field:S})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_stats_decrement")}),e.jsx(v,{children:e.jsx(k,{type:"number",step:"0.01",min:0,max:1,...S})}),e.jsx(L,{children:a.get("ui_stats_decrement_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:r.control,name:"blockRadius",render:({field:S})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_block_radius")}),e.jsx(v,{children:e.jsx(k,{type:"number",...S})}),e.jsx(L,{children:a.get("ui_block_radius_desc")}),e.jsx(O,{})]})})]}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:r.control,name:"maxGiveCount",render:({field:S})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_max_give_count")}),e.jsx(v,{children:e.jsx(k,{type:"number",...S})}),e.jsx(L,{children:a.get("ui_max_give_count_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:r.control,name:"maxPerPlayer",render:({field:S})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_max_per_player")}),e.jsx(v,{children:e.jsx(k,{type:"number",...S})}),e.jsx(L,{children:a.get("ui_max_per_player_desc")}),e.jsx(O,{})]})})]}),e.jsxs("div",{className:"flex space-x-4 items-center",children:[e.jsx(ds,{name:"target",description:a.get("ui_target_desc")}),e.jsx(N,{control:r.control,name:"giveSeedChance",render:({field:S})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_give_seed_chance")}),e.jsx(v,{children:e.jsx(pe,{min:0,max:1,step:.01,value:[S.value],onValueChange:w=>S.onChange(w[0])})}),e.jsxs(L,{children:[Math.round(S.value*100),"% - ",a.get("ui_give_seed_chance_desc")]}),e.jsx(O,{})]})})]}),e.jsx(N,{control:r.control,name:"grassOnly",render:({field:S})=>e.jsxs(b,{className:"flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4",children:[e.jsx(v,{children:e.jsx(ge,{checked:S.value,onCheckedChange:S.onChange})}),e.jsxs("div",{className:"space-y-1 leading-none",children:[e.jsx(C,{children:a.get("ui_grass_only")}),e.jsx(L,{children:a.get("ui_grass_only_desc")})]})]})})]}),e.jsxs(J,{value:"items",className:"space-y-4 shrink-0 py-4",children:[e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:r.control,name:"seedItem",render:({field:S})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_seed_item")}),e.jsx(v,{children:e.jsx(k,{...S})}),e.jsx(L,{children:a.get("ui_seed_item_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:r.control,name:"waterItem",render:({field:S})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_water_item")}),e.jsx(v,{children:e.jsx(k,{...S})}),e.jsx(L,{children:a.get("ui_water_item_desc")}),e.jsx(O,{})]})})]}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:r.control,name:"fertilizerItem",render:({field:S})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_fertilizer_item")}),e.jsx(v,{children:e.jsx(k,{...S})}),e.jsx(L,{children:a.get("ui_fertilizer_item_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:r.control,name:"giveItem",render:({field:S})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_harvest_item")}),e.jsx(v,{children:e.jsx(k,{...S})}),e.jsx(L,{children:a.get("ui_harvest_item_desc")}),e.jsx(O,{})]})})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(C,{children:a.get("ui_required_items_plant")}),e.jsxs(D,{type:"button",variant:"outline",size:"sm",onClick:()=>{l({name:"",amount:1,removeAfterUse:!0})},children:[e.jsx(je,{className:"h-4 w-4 mr-2"}),a.get("ui_add_item")]})]}),e.jsx(ce,{children:e.jsx(ue,{className:"px-0 flex flex-col",children:e.jsx(he,{className:"h-[200px]",children:e.jsx(we,{sensors:R,collisionDetection:Ce,onDragEnd:E,modifiers:[Se,ke],children:e.jsxs(se,{className:"table-fixed",children:[e.jsx(ne,{children:e.jsxs(K,{children:[e.jsx(P,{className:"w-12"}),e.jsx(P,{children:a.get("ui_item_name")}),e.jsx(P,{children:a.get("ui_amount")}),e.jsx(P,{className:"w-32 text-center",children:a.get("ui_remove")}),e.jsx(P,{className:"w-32 text-center",children:a.get("ui_actions")})]})}),e.jsx(te,{children:e.jsx(Ie,{items:x.map(S=>S.id),strategy:De,children:x.map((S,w)=>e.jsxs(pn,{id:S.id,children:[e.jsx(fn,{id:S.id}),e.jsx($,{children:e.jsx(N,{control:r.control,name:`requiredItems.${w}.name`,render:({field:y})=>e.jsx(b,{children:e.jsx(v,{children:e.jsx(k,{...y,placeholder:a.get("ui_item_name")})})})})}),e.jsx($,{children:e.jsx(N,{control:r.control,name:`requiredItems.${w}.amount`,render:({field:y})=>e.jsx(b,{children:e.jsx(v,{children:e.jsx(k,{type:"number",...y})})})})}),e.jsx($,{children:e.jsx(N,{control:r.control,name:`requiredItems.${w}.removeAfterUse`,render:({field:y})=>e.jsx(b,{className:"flex justify-center",children:e.jsx(v,{children:e.jsx(sa,{checked:y.value,onCheckedChange:y.onChange})})})})}),e.jsx($,{children:e.jsx("div",{className:"flex justify-center space-x-2",children:e.jsx(D,{type:"button",variant:"outline",size:"icon",className:"text-red-500 shrink-0",onClick:()=>u(w),children:e.jsx(fe,{className:"h-4 w-4"})})})})]},S.id))})})]})})})})})]})]}),e.jsxs(J,{value:"stages",className:"space-y-4 shrink-0 py-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(C,{children:a.get("ui_growth_stages")}),e.jsxs(D,{type:"button",variant:"outline",size:"sm",onClick:()=>{j({model:"",offsetZ:0})},children:[e.jsx(je,{className:"h-4 w-4 mr-2"}),a.get("ui_add_stage")]})]}),e.jsx(ce,{children:e.jsx(ue,{className:"px-0 flex flex-col",children:e.jsx(he,{className:"h-[300px]",children:e.jsx(we,{sensors:R,collisionDetection:Ce,onDragEnd:z,modifiers:[Se,ke],children:e.jsxs(se,{className:"table-fixed",children:[e.jsx(ne,{children:e.jsxs(K,{children:[e.jsx(P,{className:"w-12"}),e.jsx(P,{className:"w-20",children:a.get("ui_stage")}),e.jsx(P,{children:a.get("ui_model")}),e.jsx(P,{children:a.get("ui_z_offset")}),e.jsx(P,{className:"w-32 text-center",children:a.get("ui_actions")})]})}),e.jsx(te,{children:e.jsx(Ie,{items:m.map(S=>S.id),strategy:De,children:m.map((S,w)=>e.jsxs(pn,{id:S.id,children:[e.jsx(fn,{id:S.id}),e.jsxs($,{className:"font-medium text-center",children:["#",w+1]}),e.jsx($,{children:e.jsx(N,{control:r.control,name:`stages.${w}.model`,render:({field:y})=>e.jsx(b,{children:e.jsx(v,{children:e.jsx(k,{...y,placeholder:a.get("ui_model_placeholder")})})})})}),e.jsx($,{children:e.jsx(N,{control:r.control,name:`stages.${w}.offsetZ`,render:({field:y})=>e.jsx(b,{children:e.jsx(v,{children:e.jsx(k,{type:"number",step:"0.01",...y})})})})}),e.jsx($,{children:e.jsx("div",{className:"flex justify-center space-x-2",children:e.jsx(D,{type:"button",variant:"outline",size:"icon",className:"text-red-500 shrink-0",onClick:()=>h(w),disabled:m.length<=1,children:e.jsx(fe,{className:"h-4 w-4"})})})})]},S.id))})})]})})})})})]})]}),e.jsx(D,{type:"submit",className:"w-full mt-4",children:t?a.get("ui_save_changes"):a.get("ui_add_plant")})]})})]})})}function pi(){const{serverConfig:s,updatePlant:n,updatePlants:t}=ie(),[i,r]=o.useState(Object.entries((s==null?void 0:s.plants)||{}).map(([c,d])=>({id:parseInt(c),plant:d}))||[]),[a,x]=o.useState(!1),[l,u]=o.useState(null),[_,m]=o.useState(null),[j,h]=o.useState(1),M=6,[R,E]=o.useState(""),[z]=Ge(R,300),p=Xe(),S=W(),w=c=>{x(c),setTimeout(()=>{c||(u(null),m(null))},200)};o.useEffect(()=>{if(s){const c=Object.entries(s.plants||{}).map(([d,f])=>({id:parseInt(d),plant:f}));r(c)}},[s]);const y=c=>{if(s){const d=i.length>0?Math.max(...i.map(F=>F.id))+1:1,f={id:d,plant:c};r([...i,f]),n(d,c)}x(!1)},A=c=>{if(l&&_!==null){const d=i.map(f=>f.id===_?{...f,plant:c}:f);r(d),n(_,c),x(!1),setTimeout(()=>{u(null),m(null)},500)}},G=async c=>{if(await p(S.get("ui_delete_plant"),S.get("ui_delete_plant_desc"))){const d=i.filter(f=>f.id!==c);if(r(d),s){const f={...s.plants};delete f[c],t(f)}V.length===1&&j>1&&h(j-1)}},H=c=>{if(s){const f=Math.max(...i.map(qs=>qs.id),0)+1,F={...c},ee={id:f,plant:F},me=[...i,ee];r(me),n(f,F);const xs=Math.ceil(me.length/M);h(xs)}},U=o.useMemo(()=>i.filter(c=>{var d,f;return c.plant&&(((d=c.plant.seedItem)==null?void 0:d.toLowerCase().includes(z.toLowerCase()))||((f=c.plant.giveItem)==null?void 0:f.toLowerCase().includes(z.toLowerCase())))}),[i,z]),V=o.useMemo(()=>{const c=(j-1)*M;return U.slice(c,c+M)},[U,j]),Z=Math.ceil(U.length/M),g=o.useCallback(c=>{E(c.target.value),h(1)},[]);return e.jsxs("div",{className:"space-y-4 flex flex-col h-full",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(k,{placeholder:S.get("ui_search_plants"),value:R,onChange:g,className:"max-w-sm"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(D,{onClick:()=>x(!0),children:[e.jsx(je,{className:"mr-2 h-4 w-4"}),S.get("ui_add_plant")]}),e.jsx(gi,{isOpen:a,editingPlant:l||void 0,setIsOpen:w,onSubmit:l?A:y})]})]}),e.jsx("div",{className:"grow",children:e.jsxs(se,{className:"table-fixed",children:[e.jsx(ne,{children:e.jsxs(K,{children:[e.jsx(P,{children:S.get("ui_id")}),e.jsx(P,{children:S.get("ui_seed_item")}),e.jsx(P,{children:S.get("ui_harvest_item")}),e.jsx(P,{children:S.get("ui_growth_duration")}),e.jsx(P,{children:S.get("ui_actions")})]})}),e.jsx(te,{children:V.length===0?e.jsx(K,{className:"hover:bg-background",children:e.jsx($,{colSpan:5,className:"h-full hover:bg-background",children:e.jsx(ns,{searchTerm:z})})}):V.map(c=>e.jsxs(K,{children:[e.jsx($,{children:c.id}),e.jsx($,{children:c.plant.seedItem}),e.jsx($,{children:c.plant.giveItem}),e.jsx($,{children:c.plant.growthDuration}),e.jsx($,{children:e.jsxs(Qe,{children:[e.jsx(es,{asChild:!0,children:e.jsxs(D,{variant:"ghost",className:"h-8 w-8 p-0",children:[e.jsx("span",{className:"sr-only",children:"Open menu"}),e.jsx(Ke,{className:"h-4 w-4"})]})}),e.jsxs(Me,{align:"end",children:[e.jsxs(ae,{onClick:()=>{u(c.plant),m(c.id),x(!0)},children:[e.jsx(Be,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:S.get("ui_edit")})]}),e.jsx(ss,{getContent:()=>c.plant,onDuplicate:H,isDropdown:!0}),e.jsxs(ae,{className:"text-red-500",onClick:()=>G(c.id),children:[e.jsx(Ye,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:S.get("ui_delete")})]})]})]})})]},c.id))})]})}),e.jsxs("div",{className:"flex justify-end items-center space-x-2",children:[e.jsx(D,{onClick:()=>h(c=>Math.max(c-1,1)),disabled:j===1,variant:"outline",children:e.jsx(We,{className:"h-4 w-4"})}),e.jsx("div",{className:"flex items-center space-x-2",children:[...Array(Z)].map((c,d)=>{const f=d+1;return f===1||f===Z||f>=j-1&&f<=j+1?e.jsx(D,{variant:j===f?"outline":"ghost",size:"icon",onClick:()=>h(f),children:f},d):f===j-2||f===j+2?e.jsx("span",{children:"..."},d):null})}),e.jsx(D,{onClick:()=>h(c=>Math.min(c+1,Z)),disabled:j===Z||Z===0,variant:"outline",children:e.jsx(Ae,{className:"h-4 w-4"})})]})]})}const fi=ir,_i=lr,bi=cr,na=o.forwardRef(({className:s,sideOffset:n=4,...t},i)=>(o.useEffect(()=>{const r=document.querySelector("[data-radix-popper-content-wrapper]");r&&(r.style.position="absolute")},[]),e.jsx(vt,{ref:i,sideOffset:n,className:T("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...t})));na.displayName=vt.displayName;function vi({name:s,className:n,showPrices:t=!1}){const{control:i,watch:r,setValue:a}=rs(),[x,l]=o.useState(null),u=is(),_=W();r(s)||a(s,{});const m=r(s)||{},j=Object.keys(m).map(Number),h=()=>{const p=j.length>0?Math.max(...j)+1:1;a(`${s}.${p}`,{target:{x:0,y:0,z:0},coords:{x:0,y:0,z:0,w:0},price:0,disableAnim:!1}),l(p)},M=p=>{const S={...m};delete S[p],a(s,S),x===p&&l(null)},R=async(p,S)=>{u.setVisible(!1);try{const w=await Y("getCoordsInput",{target:S==="target",is4D:S==="coords",forceDisableTarget:S==="coords"},{x:0,y:0,z:0,w:0},500);u.setVisible(!0),S==="target"?a(`${s}.${p}.target`,{x:w.x,y:w.y,z:w.z}):a(`${s}.${p}.coords`,{x:w.x,y:w.y,z:w.z,w:w.w||0})}catch(w){u.setVisible(!0),console.error("Error fetching coordinates:",w)}},E=async(p,S)=>{u.setVisible(!1);const w=r(`${s}.${p}.${S}`);await Y("teleport",w)},z=p=>{l(x===p?null:p)};return e.jsxs("div",{className:T("space-y-4 shrink-0",n),children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("div",{className:"flex items-center gap-2",children:e.jsx(C,{className:"text-lg font-semibold",children:_.get("ui_laboratory_locations")})}),e.jsx(fi,{children:e.jsxs(_i,{children:[e.jsx(bi,{asChild:!0,children:e.jsxs(D,{type:"button",variant:"outline",size:"sm",onClick:h,className:"shrink-0 hover:bg-primary/10 transition-colors",children:[e.jsx(je,{className:"h-4 w-4 mr-2"})," ",_.get("ui_add_location")]})}),e.jsx(na,{children:e.jsx("p",{children:_.get("ui_add_lab_location_desc")})})]})})]}),e.jsx(ce,{className:"border-primary/20 shadow-sm",children:e.jsx(ue,{className:"p-4",children:e.jsx(he,{className:"h-[350px] pr-4",children:e.jsx("div",{className:"space-y-4",children:j.length===0?e.jsxs("div",{className:"flex flex-col items-center justify-center py-12 text-muted-foreground",children:[e.jsx(Oe,{className:"h-12 w-12 mb-4 text-primary/40"}),e.jsx("p",{className:"text-center mb-2",children:_.get("ui_no_locations_added")}),e.jsxs(D,{type:"button",variant:"outline",size:"sm",onClick:h,className:"mt-2",children:[e.jsx(je,{className:"h-4 w-4 mr-2"})," ",_.get("ui_add_first_location")]})]}):j.map(p=>{var S;return e.jsxs(ce,{className:T("border overflow-hidden transition-all duration-200",x===p?"border-primary/50 shadow-md":"hover:border-primary/30"),children:[e.jsxs("div",{className:T("flex justify-between items-center p-4 cursor-pointer",x===p?"bg-primary/5":""),onClick:()=>z(p),children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(gs,{variant:"outline",className:"bg-primary/10 text-primary",children:["#",p]}),e.jsx("h3",{className:"text-base font-medium",children:_.get("ui_location")})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[t&&e.jsx("div",{className:"flex items-center gap-4 mr-4",children:e.jsxs("div",{className:"flex items-center text-sm",children:[e.jsx(Js,{className:"h-3.5 w-3.5 mr-1 text-green-500"}),e.jsx("span",{children:((S=m[p])==null?void 0:S.price)||0})]})}),e.jsx(D,{type:"button",variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:w=>{w.stopPropagation(),M(p)},children:e.jsx(fe,{className:"h-4 w-4 text-red-500"})}),x===p?e.jsx(Wn,{className:"h-5 w-5 text-primary"}):e.jsx(ps,{className:"h-5 w-5"})]})]}),e.jsx(On,{children:x===p&&e.jsx(Hs.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},transition:{duration:.2},className:"overflow-hidden",children:e.jsxs("div",{className:"p-4 border-t space-y-6",children:[e.jsxs(b,{children:[e.jsx(C,{children:_.get("ui_target_coords")}),e.jsx(v,{children:e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"flex gap-2",children:[["x","y","z"].map(w=>e.jsx(N,{control:i,name:`${s}.${p}.target.${w}`,render:({field:y})=>e.jsx(b,{className:"w-full",children:e.jsx(v,{children:e.jsx(k,{type:"number",placeholder:w.toUpperCase(),...y,value:y.value,step:"0.1"})})})},w)),e.jsx(D,{onClick:()=>R(p,"target"),type:"button",size:"icon",className:"shrink-0",children:e.jsx(as,{className:"w-4 h-4"})}),e.jsx(D,{onClick:()=>E(p,"target"),type:"button",size:"icon",className:"shrink-0",children:e.jsx(Oe,{className:"w-4 h-4"})})]})})}),e.jsx(L,{children:_.get("ui_target_coords_desc")})]}),e.jsxs(b,{children:[e.jsx(C,{children:_.get("ui_teleport_coords")}),e.jsx(v,{children:e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{className:"flex gap-2",children:[["x","y","z","w"].map(w=>e.jsx(N,{control:i,name:`${s}.${p}.coords.${w}`,render:({field:y})=>e.jsx(b,{className:"w-full",children:e.jsx(v,{children:e.jsx(k,{type:"number",placeholder:w.toUpperCase(),...y,value:y.value,step:"0.1"})})})},w)),e.jsx(D,{onClick:()=>R(p,"coords"),type:"button",size:"icon",className:"shrink-0",children:e.jsx(as,{className:"w-4 h-4"})}),e.jsx(D,{onClick:()=>E(p,"coords"),type:"button",size:"icon",className:"shrink-0",children:e.jsx(Oe,{className:"w-4 h-4"})})]})})}),e.jsx(L,{children:_.get("ui_teleport_coords_desc")})]}),e.jsxs("div",{className:T("grid gap-6",t?"grid-cols-2":"grid-cols-1"),children:[t&&e.jsx(N,{name:`${s}.${p}.price`,control:i,render:({field:w})=>e.jsxs(b,{children:[e.jsx(C,{children:_.get("ui_location_price")}),e.jsx(v,{children:e.jsx(k,{...w,type:"number",onChange:y=>w.onChange(parseInt(y.target.value))})}),e.jsx(L,{children:_.get("ui_location_price_desc")})]})}),e.jsx(N,{name:`${s}.${p}.disableAnim`,control:i,render:({field:w})=>e.jsxs(b,{className:T("flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4",t?"mt-6":""),children:[e.jsx(v,{children:e.jsx(ge,{checked:w.value,onCheckedChange:w.onChange,className:"data-[state=checked]:bg-primary data-[state=checked]:border-primary"})}),e.jsxs("div",{className:"space-y-1 leading-none",children:[e.jsx(C,{children:_.get("ui_disable_animation")}),e.jsx(L,{children:_.get("ui_disable_animation_desc")})]})]})})]})]})})})]},p)})})})})})]})}const Ni=q({label:B().min(1,"ui_error_required"),purchaseable:xe().default(!1),account:B().min(1,"ui_error_required"),requiredItem:B().optional(),laptop:q({x:I,y:I,z:I}),iplTier:ft(["basic","upgrade"]),exit:q({target:q({x:I,y:I,z:I}),coords:q({x:I,y:I,z:I,w:I})}),entrances:or(q({target:q({x:I,y:I,z:I}),coords:q({x:I,y:I,z:I,w:I}),price:I,disableAnim:xe()})),blipEnabled:xe().optional(),blipData:q({sprite:be,color:be,size:be}).optional(),target:xe().optional()}).refine(s=>s.blipEnabled?s.blipData&&s.blipData.sprite!==void 0&&s.blipData.color!==void 0&&s.blipData.size!==void 0:!0,{message:"ui_error_required",path:["blipData"]}),_n={label:"",purchaseable:!1,account:"black_money",laptop:{x:0,y:0,z:0},iplTier:"basic",exit:{target:{x:0,y:0,z:0},coords:{x:0,y:0,z:0,w:0}},entrances:{},blipEnabled:!1,blipData:{sprite:1,color:1,size:1}};function yi({isOpen:s,setIsOpen:n,editingLaboratory:t,onSubmit:i}){const r=Te({resolver:Pe(Ni),defaultValues:t||_n}),a=W(),x=r.watch("purchaseable");o.useEffect(()=>{r.reset(t||_n)},[t,r]);function l(u){i(u)}return Array.isArray(r.watch("blipData"))&&r.setValue("blipData",{sprite:0,color:1,size:1}),e.jsx(Ee,{open:s,onOpenChange:n,children:e.jsxs(ve,{"aria-describedby":void 0,className:"min-w-fit",children:[e.jsx(Ne,{children:e.jsx(ye,{children:t?a.get("ui_edit_laboratory"):a.get("ui_add_laboratory")})}),e.jsx(Le,{...r,children:e.jsxs("form",{onSubmit:r.handleSubmit(l,$e),className:"h-fit py-4 w-[50rem] max-w-[50rem]",children:[e.jsxs(Je,{defaultValue:"basic",children:[e.jsxs(Re,{className:"grid w-full grid-cols-4",children:[e.jsx(X,{value:"basic",children:a.get("ui_basic")}),e.jsx(X,{value:"blip",children:a.get("ui_blip")}),e.jsx(X,{value:"exit",children:a.get("ui_exit")}),e.jsx(X,{value:"entrances",children:a.get("ui_entrances")})]}),e.jsxs(J,{value:"basic",className:"space-y-4 shrink-0 py-4",children:[e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:r.control,name:"label",render:({field:u})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_laboratory_name")}),e.jsx(v,{children:e.jsx(k,{...u,placeholder:a.get("ui_laboratory_name_placeholder")})}),e.jsx(L,{children:a.get("ui_laboratory_name_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:r.control,name:"account",render:({field:u})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_account")}),e.jsx(v,{children:e.jsx(k,{...u,placeholder:a.get("ui_account_placeholder")})}),e.jsx(L,{children:a.get("ui_account_desc")}),e.jsx(O,{})]})})]}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:r.control,name:"requiredItem",render:({field:u})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_required_item")}),e.jsx(v,{children:e.jsx(k,{...u,placeholder:a.get("ui_lab_card_placeholder")})}),e.jsx(L,{children:a.get("ui_required_item_lab_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:r.control,name:"iplTier",render:({field:u})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_ipl_tier")}),e.jsxs(ms,{onValueChange:u.onChange,defaultValue:u.value,children:[e.jsx(v,{children:e.jsx(ls,{children:e.jsx(hs,{placeholder:"Select tier"})})}),e.jsxs(cs,{children:[e.jsx(Q,{value:"basic",children:a.get("ui_basic_tier")}),e.jsx(Q,{value:"upgrade",children:a.get("ui_upgrade_tier")})]})]}),e.jsx(L,{children:a.get("ui_ipl_tier_desc")}),e.jsx(O,{})]})})]}),e.jsx(ds,{name:"target",description:a.get("ui_target_desc")}),e.jsx(N,{control:r.control,name:"purchaseable",render:({field:u})=>e.jsxs(b,{className:"flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4",children:[e.jsx(v,{children:e.jsx(ge,{checked:u.value,onCheckedChange:u.onChange})}),e.jsxs("div",{className:"space-y-1 leading-none",children:[e.jsx(C,{children:a.get("ui_purchaseable")}),e.jsx(L,{children:a.get("ui_purchaseable_desc")})]})]})}),x&&e.jsx(Ps,{name:"laptop",label:a.get("ui_laptop_position"),description:a.get("ui_laptop_position_desc")})]}),e.jsxs(J,{value:"blip",className:"space-y-4 shrink-0 py-4",children:[e.jsx("div",{className:"w-full",children:e.jsx(N,{control:r.control,name:"blipEnabled",render:({field:u})=>e.jsxs(b,{className:"flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4 w-full",children:[e.jsx(v,{children:e.jsx(ge,{checked:u.value,onCheckedChange:u.onChange})}),e.jsxs("div",{className:"space-y-1 leading-none",children:[e.jsx(C,{children:a.get("ui_enable_blip")}),e.jsx(L,{children:a.get("ui_enable_blip_desc")})]})]})})}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:r.control,name:"blipData.sprite",render:({field:u})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_blip_sprite")}),e.jsx(v,{children:e.jsx(k,{type:"number",...u,disabled:!r.watch("blipEnabled")})}),e.jsx(L,{children:a.get("ui_blip_sprite_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:r.control,name:"blipData.color",render:({field:u})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_blip_color")}),e.jsx(v,{children:e.jsx(k,{type:"number",...u,disabled:!r.watch("blipEnabled")})}),e.jsx(L,{children:a.get("ui_blip_color_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:r.control,name:"blipData.size",render:({field:u})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_blip_size")}),e.jsx(v,{children:e.jsx(k,{type:"number",...u,min:0,step:.25,disabled:!r.watch("blipEnabled")})}),e.jsx(L,{children:a.get("ui_blip_size_desc")}),e.jsx(O,{})]})})]})]}),e.jsxs(J,{value:"exit",className:"space-y-4 shrink-0 py-4",children:[e.jsx(Ps,{name:"exit.target",label:a.get("ui_exit_target_position"),description:a.get("ui_exit_target_position_desc"),target:!0}),e.jsx(Ps,{name:"exit.coords",label:a.get("ui_exit_coordinates"),description:a.get("ui_exit_coordinates_desc"),is4D:!0,forceDisableTarget:!0})]}),e.jsx(J,{value:"entrances",className:"space-y-4 shrink-0 py-4",children:e.jsx(vi,{name:"entrances",showPrices:x})})]}),e.jsx(D,{type:"submit",className:"w-full mt-4",children:t?a.get("ui_save_changes"):a.get("ui_add_laboratory")})]})})]})})}function wi(){const{serverConfig:s,updateLaboratory:n,updateLaboratories:t}=ie(),[i,r]=o.useState(Object.entries((s==null?void 0:s.laboratories)||{}).map(([c,d])=>({id:parseInt(c),laboratory:d}))||[]),[a,x]=o.useState(!1),[l,u]=o.useState(null),[_,m]=o.useState(null),[j,h]=o.useState(1),M=6,[R,E]=o.useState(""),[z]=Ge(R,300),p=Xe(),S=W(),w=c=>{x(c),setTimeout(()=>{c||(u(null),m(null))},200)};o.useEffect(()=>{if(s){const c=Object.entries(s.laboratories||{}).map(([d,f])=>({id:parseInt(d),laboratory:f}));r(c)}},[s]);const y=c=>{if(s){const d=i.length>0?Math.max(...i.map(F=>F.id))+1:1,f={id:d,laboratory:c};r([...i,f]),n(d,c)}x(!1)},A=c=>{if(l&&_!==null){const d=i.map(f=>f.id===_?{...f,laboratory:c}:f);r(d),n(_,c),x(!1),setTimeout(()=>{u(null),m(null)},500)}},G=async c=>{if(await p(S.get("ui_delete_laboratory"),S.get("ui_delete_laboratory_desc"))){const d=i.filter(f=>f.id!==c);if(r(d),s){const f={...s.laboratories};delete f[c],t(f)}V.length===1&&j>1&&h(j-1)}},H=c=>{if(s){const f=Math.max(...i.map(qs=>qs.id),0)+1,F={...c},ee={id:f,laboratory:F},me=[...i,ee];r(me),n(f,F);const xs=Math.ceil(me.length/M);h(xs)}},U=o.useMemo(()=>i.filter(c=>{var d;return c.laboratory&&(((d=c.laboratory.label)==null?void 0:d.toLowerCase().includes(z.toLowerCase()))||c.laboratory.requiredItem&&c.laboratory.requiredItem.toLowerCase().includes(z.toLowerCase()))}),[i,z]),V=o.useMemo(()=>{const c=(j-1)*M;return U.slice(c,c+M)},[U,j]),Z=Math.ceil(U.length/M),g=o.useCallback(c=>{E(c.target.value),h(1)},[]);return e.jsxs("div",{className:"space-y-4 flex flex-col h-full",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(k,{placeholder:S.get("ui_search_laboratories"),value:R,onChange:g,className:"max-w-sm"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(D,{onClick:()=>x(!0),children:[e.jsx(je,{className:"mr-2 h-4 w-4"}),S.get("ui_add_laboratory")]}),e.jsx(yi,{isOpen:a,editingLaboratory:l||void 0,setIsOpen:w,onSubmit:l?A:y})]})]}),e.jsx("div",{className:"grow",children:e.jsxs(se,{className:"table-fixed",children:[e.jsx(ne,{children:e.jsxs(K,{children:[e.jsx(P,{children:S.get("ui_id")}),e.jsx(P,{children:S.get("ui_label")}),e.jsx(P,{children:S.get("ui_purchaseable")}),e.jsx(P,{children:S.get("ui_ipl_tier")}),e.jsx(P,{children:S.get("ui_actions")})]})}),e.jsx(te,{children:V.length===0?e.jsx(K,{className:"hover:bg-background",children:e.jsx($,{colSpan:5,className:"h-full hover:bg-background",children:e.jsx(ns,{searchTerm:z})})}):V.map(c=>e.jsxs(K,{children:[e.jsx($,{children:c.id}),e.jsx($,{children:c.laboratory.label}),e.jsx($,{children:c.laboratory.purchaseable?"Yes":"No"}),e.jsx($,{children:S.get(`ui_${c.laboratory.iplTier}_tier`)}),e.jsx($,{children:e.jsxs(Qe,{children:[e.jsx(es,{asChild:!0,children:e.jsxs(D,{variant:"ghost",className:"h-8 w-8 p-0",children:[e.jsx("span",{className:"sr-only",children:"Open menu"}),e.jsx(Ke,{className:"h-4 w-4"})]})}),e.jsxs(Me,{align:"end",children:[e.jsxs(ae,{onClick:()=>{u(c.laboratory),m(c.id),x(!0)},children:[e.jsx(Be,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:S.get("ui_edit")})]}),e.jsx(ss,{getContent:()=>c.laboratory,onDuplicate:H,isDropdown:!0}),e.jsxs(ae,{className:"text-red-500",onClick:()=>G(c.id),children:[e.jsx(Ye,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:S.get("ui_delete")})]})]})]})})]},c.id))})]})}),e.jsxs("div",{className:"flex justify-end items-center space-x-2",children:[e.jsx(D,{onClick:()=>h(c=>Math.max(c-1,1)),disabled:j===1,variant:"outline",children:e.jsx(We,{className:"h-4 w-4"})}),e.jsx("div",{className:"flex items-center space-x-2",children:[...Array(Z)].map((c,d)=>{const f=d+1;return f===1||f===Z||f>=j-1&&f<=j+1?e.jsx(D,{variant:j===f?"outline":"ghost",size:"icon",onClick:()=>h(f),children:f},d):f===j-2||f===j+2?e.jsx("span",{children:"..."},d):null})}),e.jsx(D,{onClick:()=>h(c=>Math.min(c+1,Z)),disabled:j===Z||Z===0,variant:"outline",children:e.jsx(Ae,{className:"h-4 w-4"})})]})]})}function Ci({id:s,children:n}){const{setNodeRef:t,transform:i,transition:r}=le({id:s}),a={transform:ze.Transform.toString(i),transition:r};return e.jsx(K,{ref:t,style:a,children:n})}function Si({id:s}){const{attributes:n,listeners:t,setNodeRef:i}=le({id:s});return e.jsx($,{ref:i,...n,...t,children:e.jsx(qe,{className:"h-4 w-4 cursor-move"})})}function bn({name:s,control:n,className:t,label:i,description:r,isGiveItems:a=!1}){const x=W(),{fields:l,append:u,remove:_,move:m}=Ve({name:s,control:n}),j=Fe(oe(Ue),oe(He,{coordinateGetter:Ze}));function h(R){const{active:E,over:z}=R;if(E.id!==z.id){const p=l.findIndex(w=>w.id===E.id),S=l.findIndex(w=>w.id===z.id);m(p,S)}}const M=()=>{u(a?{name:"",amount:{min:1,max:1}}:{name:"",amount:1})};return e.jsxs("div",{className:T("space-y-4 shrink-0",t),children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"my-4",children:[e.jsx(C,{children:i}),e.jsx(L,{children:r})]}),e.jsx(D,{type:"button",variant:"outline",size:"sm",onClick:M,className:"shrink-0",children:x.get("ui_add_item")})]}),e.jsx(ce,{children:e.jsx(ue,{className:"px-0 flex flex-col",children:e.jsx(he,{className:"h-48 overflow-x-hidden",children:e.jsx(we,{sensors:j,collisionDetection:Ce,onDragEnd:h,modifiers:[Se,ke],children:e.jsxs(se,{className:"table-fixed h-full",children:[e.jsx(ne,{children:e.jsxs(K,{children:[e.jsx(P,{className:"w-10"}),e.jsx(P,{children:x.get("ui_item_name")}),a?e.jsxs(e.Fragment,{children:[e.jsx(P,{className:"w-32 text-center",children:x.get("ui_amount_min")}),e.jsx(P,{className:"w-32 text-center",children:x.get("ui_amount_max")})]}):e.jsx(P,{children:x.get("ui_amount")}),e.jsx(P,{className:"w-24 text-center",children:x.get("ui_actions")})]})}),e.jsx(te,{children:e.jsx(Ie,{items:l.map(R=>R.id),strategy:De,children:l.map((R,E)=>e.jsxs(Ci,{id:R.id,children:[e.jsx(Si,{id:R.id}),e.jsx($,{children:e.jsx(N,{name:`${s}.${E}.name`,control:n,render:({field:z})=>e.jsx(b,{children:e.jsx(v,{children:e.jsx(k,{...z,placeholder:x.get("ui_item_name_placeholder")})})})})}),a?e.jsxs(e.Fragment,{children:[e.jsx($,{children:e.jsx(N,{name:`${s}.${E}.amount.min`,control:n,render:({field:z})=>e.jsx(b,{children:e.jsx(v,{children:e.jsx(k,{...z,type:"number",placeholder:"Min"})})})})}),e.jsx($,{children:e.jsx(N,{name:`${s}.${E}.amount.max`,control:n,render:({field:z})=>e.jsx(b,{children:e.jsx(v,{children:e.jsx(k,{...z,type:"number",placeholder:"Max"})})})})})]}):e.jsx($,{children:e.jsx(N,{name:`${s}.${E}.amount`,control:n,render:({field:z})=>e.jsx(b,{children:e.jsx(v,{children:e.jsx(k,{...z,type:"number",placeholder:x.get("ui_amount")})})})})}),e.jsx($,{className:"flex justify-center",children:e.jsx(D,{type:"button",variant:"outline",size:"icon",className:"text-red-500",onClick:()=>_(E),children:e.jsx(fe,{className:"h-4 w-4"})})})]},R.id))})})]})})})})})]})}const ki=q({duration:I,progress:B().min(1).catch(""),requiredItems:re(q({name:B().min(1),amount:I})).transform(s=>s.filter(n=>n.name!==""&&n.amount>0)),giveItems:re(q({name:B().min(1),amount:q({min:I,max:I})})).transform(s=>s.filter(n=>n.name!==""&&n.amount.min>0))}),ta=re(ki);function Ii({id:s,children:n}){const{setNodeRef:t,transform:i,transition:r}=le({id:s}),a={transform:ze.Transform.toString(i),transition:r};return e.jsx(K,{ref:t,style:a,children:n})}function Di({id:s}){const{attributes:n,listeners:t,setNodeRef:i}=le({id:s});return e.jsx($,{ref:i,...n,...t,children:e.jsx(qe,{className:"h-4 w-4 cursor-move"})})}function aa({name:s,control:n,className:t}){const i=W(),{fields:r,append:a,remove:x,move:l}=Ve({name:s,control:n}),u=Fe(oe(Ue),oe(He,{coordinateGetter:Ze}));function _(m){const{active:j,over:h}=m;if(j.id!==h.id){const M=r.findIndex(E=>E.id===j.id),R=r.findIndex(E=>E.id===h.id);l(M,R)}}return e.jsxs("div",{className:T("space-y-4 shrink-0",t),children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(C,{children:i.get("ui_recipes")}),e.jsx(D,{type:"button",variant:"outline",size:"sm",onClick:()=>a({duration:1e4,requiredItems:[{name:"",amount:1}],giveItems:[{name:"",amount:{min:1,max:1}}]}),className:"shrink-0",children:i.get("ui_add_recipe")})]}),e.jsx(ce,{children:e.jsx(ue,{className:"px-0 flex flex-col",children:e.jsx(he,{className:"h-96 overflow-x-hidden",children:e.jsx(we,{sensors:u,collisionDetection:Ce,onDragEnd:_,modifiers:[Se,ke],children:e.jsxs(se,{className:"table-fixed h-full",children:[e.jsx(ne,{children:e.jsxs(K,{children:[e.jsx(P,{className:"w-10"}),e.jsx(P,{children:i.get("ui_duration")}),e.jsx(P,{children:i.get("ui_progress")}),e.jsx(P,{className:"w-36 text-center",children:i.get("ui_actions")})]})}),e.jsx(te,{children:e.jsx(Ie,{items:r.map(m=>m.id),strategy:De,children:r.map((m,j)=>e.jsxs(Ii,{id:m.id,children:[e.jsx(Di,{id:m.id}),e.jsx($,{children:e.jsx(N,{name:`${s}.${j}.duration`,control:n,render:({field:h})=>e.jsx(b,{children:e.jsx(v,{children:e.jsx(k,{...h,type:"number",placeholder:i.get("ui_duration_placeholder")})})})})}),e.jsx($,{children:e.jsx(N,{name:`${s}.${j}.progress`,control:n,render:({field:h})=>e.jsx(b,{children:e.jsx(v,{children:e.jsx(k,{...h,placeholder:i.get("ui_progress_placeholder")})})})})}),e.jsxs($,{className:"flex justify-center gap-1",children:[e.jsxs(Kt,{children:[e.jsx(Bt,{asChild:!0,children:e.jsx(D,{type:"button",variant:"outline",size:"icon",className:"shrink-0",children:e.jsx(dr,{className:"h-4 w-4"})})}),e.jsx(sn,{className:"w-[38rem] h-[25rem] rounded-xl",side:"left",children:e.jsxs(Je,{defaultValue:"requiredItems",children:[e.jsxs(Re,{className:"grid w-full grid-cols-2",children:[e.jsx(X,{value:"requiredItems",children:i.get("ui_required_items")}),e.jsx(X,{value:"giveItems",children:i.get("ui_give_items")})]}),e.jsx(J,{value:"requiredItems",children:e.jsx(bn,{name:`${s}.${j}.requiredItems`,control:n,label:i.get("ui_required_items"),description:i.get("ui_required_items_desc")})}),e.jsx(J,{value:"giveItems",children:e.jsx(bn,{name:`${s}.${j}.giveItems`,control:n,label:i.get("ui_give_items"),description:i.get("ui_give_items_desc"),isGiveItems:!0})})]})})]}),e.jsx(D,{type:"button",variant:"outline",size:"icon",className:"text-red-500 shrink-0",onClick:()=>x(j),children:e.jsx(fe,{className:"h-4 w-4"})})]})]},m.id))})})]})})})})})]})}const Ti=q({x:I,y:I,z:I}),Pi=q({item:B().min(1,"ui_error_required"),model:B().min(1,"ui_error_required"),duration:I,interactionOffset:Ti,radius:I,animation:ws.optional(),animationProp:Cs.optional(),blockMultipleUsers:xe(),recipes:ta,target:xe().optional()}),vn={item:"",model:"",duration:1e4,interactionOffset:{x:0,y:0,z:0},radius:1.25,blockMultipleUsers:!0,recipes:[{duration:1e4,progress:"",requiredItems:[{name:"",amount:1}],giveItems:[{name:"",amount:{min:1,max:1}}]}]};function $i({isOpen:s,setIsOpen:n,editingTable:t,onSubmit:i,isSubmitting:r=!1}){const a=W(),x=Te({resolver:Pe(Pi),defaultValues:t||vn});o.useEffect(()=>{x.reset(t||vn)},[t,x]);function l(u){i(u),n(!1)}return e.jsx(Ee,{open:s,onOpenChange:n,children:e.jsxs(ve,{"aria-describedby":void 0,className:"min-w-fit",children:[e.jsxs(Ne,{children:[e.jsx(ye,{children:t?a.get("ui_edit_processing_table"):a.get("ui_add_processing_table")}),e.jsx(ys,{})]}),e.jsx(Le,{...x,children:e.jsxs("form",{onSubmit:x.handleSubmit(l,$e),className:"h-fit py-4 w-[50rem]",children:[e.jsxs(Je,{defaultValue:"basic",children:[e.jsxs(Re,{className:"grid w-full grid-cols-3",children:[e.jsx(X,{value:"basic",children:a.get("ui_basic")}),e.jsx(X,{value:"recipes",children:a.get("ui_recipes")}),e.jsx(X,{value:"animation",children:a.get("ui_animation")})]}),e.jsxs(J,{value:"basic",className:"space-y-4 shrink-0 py-4",children:[e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:x.control,name:"item",render:({field:u})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_item")}),e.jsx(v,{children:e.jsx(k,{...u,placeholder:"meth_table"})}),e.jsx(L,{children:a.get("ui_item_name_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:x.control,name:"model",render:({field:u})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_model")}),e.jsx(v,{children:e.jsx(k,{...u,placeholder:"prop_table_01"})}),e.jsx(L,{children:a.get("ui_model_desc")}),e.jsx(O,{})]})})]}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:x.control,name:"radius",render:({field:u})=>e.jsxs(b,{className:"flex-1 w-1/2",children:[e.jsx(C,{children:a.get("ui_radius")}),e.jsx(v,{children:e.jsx(k,{type:"number",step:"0.25",...u})}),e.jsx(L,{children:a.get("ui_radius_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:x.control,name:"duration",render:({field:u})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_table_duration")}),e.jsx(v,{children:e.jsx(k,{type:"number",...u})}),e.jsx(L,{children:a.get("ui_table_duration_desc")}),e.jsx(O,{})]})})]}),e.jsx("div",{className:"flex space-x-4",children:e.jsx(N,{control:x.control,name:"interactionOffset",render:({field:u})=>e.jsxs(b,{className:"flex-1 w-1/2",children:[e.jsx(C,{children:a.get("ui_interaction_offset")}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:x.control,name:"interactionOffset.x",render:({field:_})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(v,{children:e.jsx(k,{type:"number",step:"0.1",..._})}),e.jsx(O,{})]})}),e.jsx(N,{control:x.control,name:"interactionOffset.y",render:({field:_})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(v,{children:e.jsx(k,{type:"number",step:"0.1",..._})}),e.jsx(O,{})]})}),e.jsx(N,{control:x.control,name:"interactionOffset.z",render:({field:_})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(v,{children:e.jsx(k,{type:"number",step:"0.1",..._})}),e.jsx(O,{})]})})]}),e.jsx(L,{children:a.get("ui_interaction_offset_desc")})]})})}),e.jsx(ds,{name:"target",description:a.get("ui_target_desc")}),e.jsx(N,{control:x.control,name:"blockMultipleUsers",render:({field:u})=>e.jsxs(b,{className:"flex-1 flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4",children:[e.jsx(v,{children:e.jsx(ge,{checked:u.value,onCheckedChange:u.onChange})}),e.jsxs("div",{className:"space-y-1 leading-none",children:[e.jsx(C,{children:a.get("ui_block_multiple_users")}),e.jsx(L,{children:a.get("ui_block_multiple_users_desc")})]})]})})]}),e.jsx(J,{value:"recipes",className:"space-y-4 shrink-0 py-4",children:e.jsx(aa,{name:"recipes",control:x.control})}),e.jsx(J,{value:"animation",className:"space-y-4 shrink-0 py-4",children:e.jsx(Ss,{name:"animation"})})]}),e.jsxs(D,{type:"submit",className:"w-full",disabled:r,children:[r&&e.jsx(Es,{className:"mr-2 h-4 w-4 animate-spin"}),t?a.get("ui_save_changes"):a.get("ui_add_processing_table")]})]})})]})})}function Ei(){const{serverConfig:s,updateProcessingTable:n}=ie(),[t,i]=o.useState((s==null?void 0:s.processingTables)||[]),[r,a]=o.useState(!1),[x,l]=o.useState(null),[u,_]=o.useState(null),[m,j]=o.useState(1),h=6,[M,R]=o.useState(""),[E]=Ge(M,300),z=Xe(),p=W(),S=g=>{a(g),setTimeout(()=>{g||(l(null),_(null))},200)};o.useEffect(()=>{s&&i(s.processingTables||[])},[s]);const w=g=>{if(s){const c=Array.isArray(g)?g:[g],d=t.length,f=[...t,...c];i(f),c.forEach((F,ee)=>{n(d+ee,F)})}a(!1)},y=g=>{if(x&&u!==null){const c=t.map((d,f)=>f===u?g:d);i(c),n(u,g),a(!1),setTimeout(()=>{l(null),_(null)},500)}},A=async g=>{if(await z(p.get("ui_delete_processing_table"),p.get("ui_are_you_sure_delete_processing_table"))){const c=t.filter((d,f)=>f!==g);i(c),s&&ie.getState().updateProcessingTables(c),U.length===1&&m>1&&j(m-1)}},G=g=>{if(s){const c={...g},d=[...t,c];i(d);const f=d.length-1;n(f,c);const F=Math.ceil(d.length/h);j(F)}},H=o.useMemo(()=>t.filter(g=>{var c;return(c=g.item)==null?void 0:c.toLowerCase().includes(E.toLowerCase())}),[t,E]),U=o.useMemo(()=>{const g=(m-1)*h;return H.slice(g,g+h)},[H,m]),V=Math.ceil(H.length/h),Z=o.useCallback(g=>{R(g.target.value),j(1)},[]);return e.jsxs("div",{className:"space-y-4 flex flex-col h-full",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(k,{placeholder:p.get("ui_search_processing_tables"),value:M,onChange:Z,className:"max-w-sm"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(D,{onClick:()=>a(!0),children:[e.jsx(je,{className:"mr-2 h-4 w-4"}),p.get("ui_add_processing_table")]}),e.jsx($i,{isOpen:r,editingTable:x||void 0,setIsOpen:S,onSubmit:x?y:w})]})]}),e.jsx("div",{className:"grow",children:e.jsxs(se,{className:"table-fixed",children:[e.jsx(ne,{children:e.jsxs(K,{children:[e.jsx(P,{children:p.get("ui_item")}),e.jsx(P,{children:p.get("ui_duration")}),e.jsx(P,{children:p.get("ui_recipes")}),e.jsx(P,{children:p.get("ui_actions")})]})}),e.jsx(te,{children:U.length===0?e.jsx(K,{className:"hover:bg-background",children:e.jsx($,{colSpan:5,className:"h-full hover:bg-background",children:e.jsx(ns,{searchTerm:E})})}):U.map((g,c)=>{var f;const d=(m-1)*h+c;return e.jsxs(K,{children:[e.jsx($,{children:g.item}),e.jsx($,{children:g.duration}),e.jsx($,{children:((f=g.recipes)==null?void 0:f.length)||0}),e.jsx($,{children:e.jsxs(Qe,{children:[e.jsx(es,{asChild:!0,children:e.jsxs(D,{variant:"ghost",className:"h-8 w-8 p-0",children:[e.jsx("span",{className:"sr-only",children:"Open menu"}),e.jsx(Ke,{className:"h-4 w-4"})]})}),e.jsxs(Me,{align:"end",children:[e.jsxs(ae,{onClick:()=>{l(g),_(d),a(!0)},children:[e.jsx(Be,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:p.get("ui_edit")})]}),e.jsx(ss,{getContent:()=>g,onDuplicate:G,isDropdown:!0}),e.jsxs(ae,{className:"text-red-500",onClick:()=>A(d),children:[e.jsx(Ye,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:p.get("ui_delete")})]})]})]})})]},d)})})]})}),e.jsxs("div",{className:"flex justify-end items-center space-x-2",children:[e.jsx(D,{onClick:()=>j(g=>Math.max(g-1,1)),disabled:m===1,variant:"outline",children:e.jsx(We,{className:"h-4 w-4"})}),e.jsx("div",{className:"flex items-center space-x-2",children:[...Array(V)].map((g,c)=>{const d=c+1;return d===1||d===V||d>=m-1&&d<=m+1?e.jsx(D,{variant:m===d?"outline":"ghost",size:"icon",onClick:()=>j(d),children:d},c):d===m-2||d===m+2?e.jsx("span",{children:"..."},c):null})}),e.jsx(D,{onClick:()=>j(g=>Math.min(g+1,V)),disabled:m===V||V===0,variant:"outline",children:e.jsx(Ae,{className:"h-4 w-4"})})]})]})}const Li=q({x:I.catch(0),y:I.catch(0),z:I.catch(0),w:I.catch(0)}),zi=re(Li);function Ai({id:s,children:n}){const{setNodeRef:t,transform:i,transition:r}=le({id:s}),a={transform:ze.Transform.toString(i),transition:r};return e.jsx(K,{ref:t,style:a,children:n})}function Ri({id:s}){const{attributes:n,listeners:t,setNodeRef:i}=le({id:s});return e.jsx($,{ref:i,...n,...t,children:e.jsx(qe,{className:"h-4 w-4 cursor-move"})})}function Mi({name:s,className:n,pedField:t}){const i=W(),{control:r,watch:a,setValue:x}=rs(),{fields:l,append:u,remove:_,move:m}=Ve({name:s,control:r}),j=is();Array.isArray(a("locations"))||x("locations",[]);const h=a("target"),M=a("prop"),R=a(t||"ped"),E=Fe(oe(Ue),oe(He,{coordinateGetter:Ze})),z=async w=>{j.setVisible(!1);const y=await Y("getCoordsInput",{target:h,is4D:!0,prop:M,ped:[R]},{x:0,y:0,z:0,w:0},500);j.setVisible(!0),x(`${s}.${w}`,y)},p=async w=>{j.setVisible(!1);const y=a(`${s}.${w}`);await Y("teleport",y)};function S(w){const{active:y,over:A}=w;if(y.id!==A.id){const G=l.findIndex(U=>U.id===y.id),H=l.findIndex(U=>U.id===A.id);m(G,H)}}return e.jsxs("div",{className:T("space-y-4 shrink-0",n),children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(C,{children:i.get("ui_locations")}),e.jsx(D,{type:"button",variant:"outline",size:"sm",onClick:()=>u({x:0,y:0,z:0,w:0}),className:"shrink-0",children:i.get("ui_add_location")})]}),e.jsx(ce,{children:e.jsx(ue,{className:"px-0 flex flex-col",children:e.jsx(he,{className:"h-96",children:e.jsx(we,{sensors:E,collisionDetection:Ce,onDragEnd:S,modifiers:[Se,ke],children:e.jsxs(se,{className:"table-fixed",children:[e.jsx(ne,{children:e.jsxs(K,{children:[e.jsx(P,{className:"w-8 px-2"}),e.jsx(P,{className:"w-[18%]",children:"X"}),e.jsx(P,{className:"w-[18%]",children:"Y"}),e.jsx(P,{className:"w-[18%]",children:"Z"}),e.jsx(P,{className:"w-[18%]",children:"W"}),e.jsx(P,{className:"w-[20%] text-center",children:i.get("ui_actions")})]})}),e.jsx(te,{children:e.jsx(Ie,{items:l.map(w=>w.id),strategy:De,children:l.map((w,y)=>e.jsxs(Ai,{id:w.id,children:[e.jsx(Ri,{id:w.id}),["x","y","z","w"].map(A=>e.jsx($,{children:e.jsx(N,{name:`${s}.${y}.${A}`,control:r,render:({field:G})=>e.jsx(b,{children:e.jsx(v,{children:e.jsx(k,{...G,type:"number",placeholder:A.toUpperCase(),step:"0.1"})})})})},A)),e.jsx($,{children:e.jsxs("div",{className:"flex justify-center space-x-2",children:[e.jsx(D,{type:"button",variant:"outline",size:"icon",className:"shrink-0",onClick:()=>z(y),children:e.jsx(as,{className:"h-4 w-4"})}),e.jsx(D,{type:"button",variant:"outline",size:"icon",className:"shrink-0",onClick:()=>p(y),children:e.jsx(Oe,{className:"h-4 w-4"})}),e.jsx(D,{type:"button",variant:"outline",size:"icon",className:"text-red-500 shrink-0",onClick:()=>_(y),children:e.jsx(fe,{className:"h-4 w-4"})})]})})]},w.id))})})]})})})})})]})}const Oi=q({label:B().min(1,"ui_error_required"),radius:I,model:B().min(1,"ui_error_required"),account:B().min(1,"ui_error_required"),locations:zi,items:re(q({name:B().min(1,"ui_error_required"),price:I})),target:xe().optional(),blipEnabled:xe().optional(),blipData:q({sprite:be,color:be,size:be}).optional()}).refine(s=>s.blipEnabled?s.blipData&&s.blipData.sprite!==void 0&&s.blipData.color!==void 0&&s.blipData.size!==void 0:!0,{message:"ui_error_required",path:["blipData"]}),ks={label:"",radius:1.25,model:"",account:"money",locations:[{x:0,y:0,z:0,w:0}],items:[{name:"",price:100}],blipEnabled:!1,blipData:{sprite:1,color:1,size:1}};function Vi({isOpen:s,setIsOpen:n,editingSupplier:t,onSubmit:i}){var l;const r=Te({resolver:Pe(Oi),defaultValues:t||ks}),a=W();o.useEffect(()=>{t?r.reset({...t,items:t.items||ks.items,locations:t.locations||ks.locations}):r.reset(ks)},[t,r]);function x(u){i(u),n(!1)}return e.jsx(Ee,{open:s,onOpenChange:n,children:e.jsxs(ve,{"aria-describedby":void 0,className:"min-w-fit",children:[e.jsx(Ne,{children:e.jsx(ye,{children:t?a.get("ui_edit_supplier"):a.get("ui_add_supplier")})}),e.jsx(Le,{...r,children:e.jsxs("form",{onSubmit:r.handleSubmit(x,$e),className:"h-fit py-4 w-[50rem]",children:[e.jsxs(Je,{defaultValue:"basic",children:[e.jsxs(Re,{className:"grid w-full grid-cols-4",children:[e.jsx(X,{value:"basic",children:a.get("ui_basic")}),e.jsx(X,{value:"blip",children:a.get("ui_blip")}),e.jsx(X,{value:"items",children:a.get("ui_items")}),e.jsx(X,{value:"locations",children:a.get("ui_locations")})]}),e.jsxs(J,{value:"basic",className:"space-y-4 shrink-0 py-4",children:[e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:r.control,name:"label",render:({field:u})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_interaction_label")}),e.jsx(v,{children:e.jsx(k,{...u,placeholder:a.get("ui_supplier")})}),e.jsx(L,{children:a.get("ui_interaction_label_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:r.control,name:"account",render:({field:u})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_account")}),e.jsx(v,{children:e.jsx(k,{...u,placeholder:"money"})}),e.jsx(L,{children:a.get("ui_account_desc")}),e.jsx(O,{})]})})]}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:r.control,name:"radius",render:({field:u})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_radius")}),e.jsx(v,{children:e.jsx(k,{type:"number",...u})}),e.jsx(L,{children:a.get("ui_radius_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:r.control,name:"model",render:({field:u})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_ped_model")}),e.jsx(v,{children:e.jsx(k,{...u,placeholder:"mp_m_freemode_01"})}),e.jsx(L,{children:a.get("ui_ped_model_desc")}),e.jsx(O,{})]})})]}),e.jsx("div",{className:"flex space-x-4",children:e.jsx(ds,{name:"target",description:a.get("ui_target_desc")})})]}),e.jsxs(J,{value:"blip",className:"space-y-4 shrink-0 py-4",children:[e.jsx("div",{className:"w-full",children:e.jsx(N,{control:r.control,name:"blipEnabled",render:({field:u})=>e.jsxs(b,{className:"flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4 w-full",children:[e.jsx(v,{children:e.jsx(ge,{checked:u.value,onCheckedChange:u.onChange})}),e.jsxs("div",{className:"space-y-1 leading-none",children:[e.jsx(C,{children:a.get("ui_enable_blip")}),e.jsx(L,{children:a.get("ui_enable_blip_desc")})]})]})})}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:r.control,name:"blipData.sprite",render:({field:u})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_blip_sprite")}),e.jsx(v,{children:e.jsx(k,{type:"number",...u,disabled:!r.watch("blipEnabled")})}),e.jsx(L,{children:a.get("ui_blip_sprite_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:r.control,name:"blipData.color",render:({field:u})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_blip_color")}),e.jsx(v,{children:e.jsx(k,{type:"number",...u,disabled:!r.watch("blipEnabled")})}),e.jsx(L,{children:a.get("ui_blip_color_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:r.control,name:"blipData.size",render:({field:u})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_blip_size")}),e.jsx(v,{children:e.jsx(k,{type:"number",...u,min:0,step:.25,disabled:!r.watch("blipEnabled")})}),e.jsx(L,{children:a.get("ui_blip_size_desc")}),e.jsx(O,{})]})})]})]}),e.jsx(J,{value:"items",className:"space-y-4 shrink-0 py-4",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(C,{children:a.get("ui_items")}),e.jsxs(D,{type:"button",variant:"outline",size:"sm",onClick:()=>{const u=r.getValues("items")||[];r.setValue("items",[...u,{name:"",price:100}])},className:"shrink-0",children:[e.jsx(je,{className:"mr-2 h-4 w-4"}),a.get("ui_add_item")]})]}),e.jsx(ce,{children:e.jsx(ue,{className:"px-0 flex flex-col",children:e.jsx(he,{className:"h-96",children:e.jsxs(se,{className:"table-fixed",children:[e.jsx(ne,{children:e.jsxs(K,{children:[e.jsx(P,{children:a.get("ui_item_name")}),e.jsx(P,{children:a.get("ui_price")}),e.jsx(P,{className:"w-24 text-center",children:a.get("ui_actions")})]})}),e.jsx(te,{children:(l=r.watch("items"))==null?void 0:l.map((u,_)=>e.jsxs(K,{children:[e.jsx($,{children:e.jsx(N,{name:`items.${_}.name`,control:r.control,render:({field:m})=>e.jsx(b,{children:e.jsx(v,{children:e.jsx(k,{...m,placeholder:a.get("ui_item_name")})})})})}),e.jsx($,{children:e.jsx(N,{name:`items.${_}.price`,control:r.control,render:({field:m})=>e.jsx(b,{children:e.jsx(v,{children:e.jsx(k,{...m,type:"number",placeholder:a.get("ui_price")})})})})}),e.jsx($,{className:"flex justify-center",children:e.jsx(D,{type:"button",variant:"outline",size:"icon",className:"text-red-500",onClick:()=>{const m=r.getValues("items");r.setValue("items",m.filter((j,h)=>h!==_))},children:e.jsx(fe,{className:"h-4 w-4"})})})]},_))})]})})})})]})}),e.jsx(J,{value:"locations",className:"space-y-4 shrink-0 py-4",children:e.jsx(Mi,{name:"locations",pedField:"model"})})]}),e.jsx(D,{type:"submit",className:"w-full mt-4",children:t?a.get("ui_save_changes"):a.get("ui_add_supplier")})]})})]})})}function Fi(){const{serverConfig:s,updateSupplier:n}=ie(),[t,i]=o.useState((s==null?void 0:s.suppliers)||[]),[r,a]=o.useState(!1),[x,l]=o.useState(null),[u,_]=o.useState(null),[m,j]=o.useState(1),h=6,[M,R]=o.useState(""),[E]=Ge(M,300),z=Xe(),p=W(),S=g=>{a(g),setTimeout(()=>{g||(l(null),_(null))},200)};o.useEffect(()=>{s&&i(s.suppliers||[])},[s]);const w=g=>{if(s){const c=Array.isArray(g)?g:[g],d=t.length,f=[...t,...c];i(f),c.forEach((F,ee)=>{n(d+ee,F)})}a(!1)},y=g=>{if(x&&u!==null){const c=t.map((d,f)=>f===u?g:d);i(c),n(u,g),a(!1),setTimeout(()=>{l(null),_(null)},500)}},A=async g=>{if(await z(p.get("ui_delete_supplier"),p.get("ui_delete_supplier_desc"))){const c=t.filter((d,f)=>f!==g);i(c),s&&ie.getState().updateSuppliers(c),U.length===1&&m>1&&j(m-1)}},G=g=>{if(s){const c={...g},d=[...t,c];i(d);const f=d.length-1;n(f,c);const F=Math.ceil(d.length/h);j(F)}},H=o.useMemo(()=>t.filter(g=>{var c;return((c=g.label)==null?void 0:c.toLowerCase().includes(E.toLowerCase()))||g.account.toLowerCase().includes(E.toLowerCase())}),[t,E]),U=o.useMemo(()=>{const g=(m-1)*h;return H.slice(g,g+h)},[H,m]),V=Math.ceil(H.length/h),Z=o.useCallback(g=>{R(g.target.value),j(1)},[]);return e.jsxs("div",{className:"space-y-4 flex flex-col h-full",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(k,{placeholder:p.get("ui_search_suppliers"),value:M,onChange:Z,className:"max-w-sm"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(D,{onClick:()=>a(!0),children:[e.jsx(je,{className:"mr-2 h-4 w-4"}),p.get("ui_add_supplier")]}),e.jsx(Vi,{isOpen:r,editingSupplier:x||void 0,setIsOpen:S,onSubmit:x?y:w})]})]}),e.jsx("div",{className:"grow",children:e.jsxs(se,{className:"table-fixed",children:[e.jsx(ne,{children:e.jsxs(K,{children:[e.jsx(P,{children:p.get("ui_label")}),e.jsx(P,{children:p.get("ui_account")}),e.jsx(P,{children:p.get("ui_items")}),e.jsx(P,{children:p.get("ui_locations")}),e.jsx(P,{children:p.get("ui_actions")})]})}),e.jsx(te,{children:U.length===0?e.jsx(K,{className:"hover:bg-background",children:e.jsx($,{colSpan:5,className:"h-full hover:bg-background",children:e.jsx(ns,{searchTerm:E})})}):U.map((g,c)=>{const d=(m-1)*h+c;return e.jsxs(K,{children:[e.jsx($,{children:g.label}),e.jsx($,{children:g.account}),e.jsxs($,{children:[g.items.length," items"]}),e.jsxs($,{children:[g.locations.length," locations"]}),e.jsx($,{children:e.jsxs(Qe,{children:[e.jsx(es,{asChild:!0,children:e.jsxs(D,{variant:"ghost",className:"h-8 w-8 p-0",children:[e.jsx("span",{className:"sr-only",children:"Open menu"}),e.jsx(Ke,{className:"h-4 w-4"})]})}),e.jsxs(Me,{align:"end",children:[e.jsxs(ae,{onClick:()=>{l(g),_(d),a(!0)},children:[e.jsx(Be,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:p.get("ui_edit")})]}),e.jsx(ss,{getContent:()=>g,onDuplicate:G,isDropdown:!0}),e.jsxs(ae,{className:"text-red-500",onClick:()=>A(d),children:[e.jsx(Ye,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:p.get("ui_delete")})]})]})]})})]},d)})})]})}),e.jsxs("div",{className:"flex justify-end items-center space-x-2",children:[e.jsx(D,{onClick:()=>j(g=>Math.max(g-1,1)),disabled:m===1,variant:"outline",children:e.jsx(We,{className:"h-4 w-4"})}),e.jsx("div",{className:"flex items-center space-x-2",children:[...Array(V)].map((g,c)=>{const d=c+1;return d===1||d===V||d>=m-1&&d<=m+1?e.jsx(D,{variant:m===d?"outline":"ghost",size:"icon",onClick:()=>j(d),children:d},c):d===m-2||d===m+2?e.jsx("span",{children:"..."},c):null})}),e.jsx(D,{onClick:()=>j(g=>Math.min(g+1,V)),disabled:m===V||V===0,variant:"outline",children:e.jsx(Ae,{className:"h-4 w-4"})})]})]})}const qi=q({name:B().min(1,"ui_error_required"),progress:B().min(1,"ui_error_required"),progressDuration:I,animation:ws.optional(),animationProp:Cs.optional(),effects:q({duration:I,visualEffect:q({intensity:I,type:B().min(1,"ui_error_required")}),cameraShake:q({intensity:I,type:B().min(1,"ui_error_required")}),walkClipSet:B(),tripChance:I,speed:I,health:I,armour:I,hunger:I,thirst:I,overdoseLevel:I})}),Nn={name:"",progress:"",progressDuration:7500,effects:{duration:6e4,visualEffect:{intensity:0,type:"none"},cameraShake:{intensity:0,type:"none"},walkClipSet:"",tripChance:0,speed:1,health:0,armour:0,hunger:0,thirst:0,overdoseLevel:0}};function Zi({isOpen:s,setIsOpen:n,editingConsumable:t,onSubmit:i}){const r=Te({resolver:Pe(qi),defaultValues:t?{...t}:Nn}),a=W();o.useEffect(()=>{t?r.reset({...t}):r.reset(Nn)},[t,r]),o.useEffect(()=>{r.watch("effects.armour")===void 0&&r.setValue("effects.armour",0)},[r.watch("effects.armour")]);function x(l){i(l),n(!1)}return e.jsx(Ee,{open:s,onOpenChange:n,children:e.jsxs(ve,{"aria-describedby":void 0,className:"min-w-fit",children:[e.jsx(Ne,{children:e.jsx(ye,{children:t?a.get("ui_edit_consumable"):a.get("ui_add_consumable")})}),e.jsx(Zn,{...r,children:e.jsx(Le,{...r,children:e.jsxs("form",{onSubmit:r.handleSubmit(x,$e),className:"h-fit py-4 w-[50rem]",children:[e.jsxs(Je,{defaultValue:"basic",children:[e.jsxs(Re,{className:"grid w-full grid-cols-5",children:[e.jsx(X,{value:"basic",children:a.get("ui_basic")}),e.jsx(X,{value:"animation",children:a.get("ui_animation")}),e.jsx(X,{value:"visual",children:a.get("ui_visual_effects")}),e.jsx(X,{value:"movement",children:a.get("ui_movement")}),e.jsx(X,{value:"stats",children:a.get("ui_stats")})]}),e.jsxs(J,{value:"basic",className:"space-y-4 shrink-0 py-4",children:[e.jsx("div",{className:"flex space-x-4",children:e.jsx(N,{control:r.control,name:"name",render:({field:l})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_consumable_item_name")}),e.jsx(v,{children:e.jsx(k,{...l,placeholder:"weed_joint"})}),e.jsx(L,{children:a.get("ui_consumable_item_name_desc")}),e.jsx(O,{})]})})}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:r.control,name:"progress",render:({field:l})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_progress")}),e.jsx(v,{children:e.jsx(k,{...l,placeholder:"Smoking joint..."})}),e.jsx(L,{children:a.get("ui_progress_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:r.control,name:"progressDuration",render:({field:l})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_progress_duration")}),e.jsx(v,{children:e.jsx(k,{type:"number",...l})}),e.jsx(L,{children:a.get("ui_progress_duration_desc")}),e.jsx(O,{})]})})]})]}),e.jsx(J,{value:"animation",className:"space-y-4 shrink-0 py-4",children:e.jsx(Ss,{name:"animation"})}),e.jsx(J,{value:"visual",className:"space-y-4 shrink-0 py-4",children:e.jsxs("div",{className:"space-y-6",children:[e.jsx(N,{control:r.control,name:"effects.duration",render:({field:l})=>e.jsxs(b,{children:[e.jsx(C,{children:a.get("ui_effect_duration")}),e.jsx(v,{children:e.jsx(k,{type:"number",...l})}),e.jsx(L,{children:a.get("ui_effect_duration_desc")}),e.jsx(O,{})]})}),e.jsx(os,{}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsxs("div",{className:"flex-1 space-y-4",children:[e.jsx(N,{control:r.control,name:"effects.visualEffect.type",render:({field:l})=>e.jsxs(b,{children:[e.jsx(C,{children:a.get("ui_main_effect_type")}),e.jsxs(ms,{onValueChange:l.onChange,defaultValue:l.value,children:[e.jsx(v,{children:e.jsx(ls,{children:e.jsx(hs,{placeholder:a.get("ui_select_visual_effect_type")})})}),e.jsxs(cs,{children:[e.jsx(Q,{value:"drug_drive_blend01",children:"drug_drive_blend01"}),e.jsx(Q,{value:"drug_flying_02",children:"drug_flying_02"}),e.jsx(Q,{value:"drug_flying_base",children:"drug_flying_base"}),e.jsx(Q,{value:"drug_wobbly",children:"drug_wobbly"}),e.jsx(Q,{value:"drug_flying_01",children:"drug_flying_01"})]})]}),e.jsx(O,{})]})}),e.jsx(N,{control:r.control,name:"effects.visualEffect.intensity",render:({field:l})=>e.jsxs(b,{children:[e.jsxs(C,{children:[a.get("ui_intensity"),": ",l.value]}),e.jsx(v,{children:e.jsx(pe,{value:[l.value],min:0,max:10,step:.1,onValueChange:u=>l.onChange(u[0])})}),e.jsx(O,{})]})})]}),e.jsx("div",{className:"w-px bg-border self-stretch mx-2"}),e.jsxs("div",{className:"flex-1 space-y-4",children:[e.jsx(N,{control:r.control,name:"effects.cameraShake.type",render:({field:l})=>e.jsxs(b,{children:[e.jsx(C,{children:a.get("ui_camera_shake_type")}),e.jsxs(ms,{onValueChange:l.onChange,defaultValue:l.value,children:[e.jsx(v,{children:e.jsx(ls,{children:e.jsx(hs,{placeholder:a.get("ui_select_camera_shake_type")})})}),e.jsxs(cs,{children:[e.jsx(Q,{value:"DRUNK_SHAKE",children:"DRUNK_SHAKE"}),e.jsx(Q,{value:"FAMILY5_DRUG_TRIP_SHAKE",children:"FAMILY5_DRUG_TRIP_SHAKE"}),e.jsx(Q,{value:"SMALL_EXPLOSION_SHAKE",children:"SMALL_EXPLOSION_SHAKE"}),e.jsx(Q,{value:"MEDIUM_EXPLOSION_SHAKE",children:"MEDIUM_EXPLOSION_SHAKE"}),e.jsx(Q,{value:"LARGE_EXPLOSION_SHAKE",children:"LARGE_EXPLOSION_SHAKE"}),e.jsx(Q,{value:"JOLT_SHAKE",children:"JOLT_SHAKE"}),e.jsx(Q,{value:"VIBRATE_SHAKE",children:"VIBRATE_SHAKE"}),e.jsx(Q,{value:"ROAD_VIBRATION_SHAKE",children:"ROAD_VIBRATION_SHAKE"}),e.jsx(Q,{value:"HAND_SHAKE",children:"HAND_SHAKE"}),e.jsx(Q,{value:"DEATH_FAIL_IN_EFFECT_SHAKE",children:"DEATH_FAIL_IN_EFFECT_SHAKE"}),e.jsx(Q,{value:"GAMEPLAY_EXPLOSION_SHAKE",children:"GAMEPLAY_EXPLOSION_SHAKE"}),e.jsx(Q,{value:"GAMEPLAY_FLY_SHAKE",children:"GAMEPLAY_FLY_SHAKE"}),e.jsx(Q,{value:"GAMEPLAY_BULLET_IMPACT_SHAKE",children:"GAMEPLAY_BULLET_IMPACT_SHAKE"}),e.jsx(Q,{value:"SKY_DIVING_SHAKE",children:"SKY_DIVING_SHAKE"}),e.jsx(Q,{value:"DRUNK_SHAKE_SOFT",children:"DRUNK_SHAKE_SOFT"})]})]}),e.jsx(O,{})]})}),e.jsx(N,{control:r.control,name:"effects.cameraShake.intensity",render:({field:l})=>e.jsxs(b,{children:[e.jsxs(C,{children:[a.get("ui_intensity"),": ",l.value]}),e.jsx(v,{children:e.jsx(pe,{value:[l.value],min:0,max:10,step:.1,onValueChange:u=>l.onChange(u[0])})}),e.jsx(O,{})]})})]})]})]})}),e.jsx(J,{value:"movement",className:"space-y-4 shrink-0 py-4",children:e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"space-y-5",children:[e.jsx(N,{control:r.control,name:"effects.walkClipSet",render:({field:l})=>e.jsxs(b,{children:[e.jsx(C,{children:a.get("ui_walk_clip_set")}),e.jsx(v,{children:e.jsx(k,{...l,placeholder:"MOVE_M@DRUNK@VERYDRUNK"})}),e.jsx(L,{children:a.get("ui_walk_clip_set_desc")}),e.jsx(O,{})]})}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:r.control,name:"effects.tripChance",render:({field:l})=>e.jsxs(b,{className:"w-full",children:[e.jsxs(C,{children:[a.get("ui_trip_chance"),": ",Math.round(l.value*100),"%"]}),e.jsx(v,{children:e.jsx(pe,{value:[l.value],min:0,max:1,step:.01,onValueChange:u=>l.onChange(u[0])})}),e.jsx(O,{})]})}),e.jsx(N,{control:r.control,name:"effects.speed",render:({field:l})=>e.jsxs(b,{className:"w-full",children:[e.jsxs(C,{children:[a.get("ui_speed_multiplier"),": ",l.value,"x"]}),e.jsx(v,{children:e.jsx(pe,{value:[l.value],min:.1,max:2,step:.1,onValueChange:u=>l.onChange(u[0])})}),e.jsx(O,{})]})})]})]})})}),e.jsx(J,{value:"stats",className:"space-y-4 shrink-0 py-4",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 gap-4",children:[e.jsxs("div",{className:"flex flex-col md:flex-row md:space-x-4 space-y-4 md:space-y-0",children:[e.jsx(N,{control:r.control,name:"effects.health",render:({field:l})=>e.jsxs(b,{className:"w-full",children:[e.jsxs(C,{children:[a.get("ui_health_effect"),": ",l.value]}),e.jsx(v,{children:e.jsx(pe,{value:[l.value],min:-100,max:100,step:1,onValueChange:u=>l.onChange(u[0])})}),e.jsx(O,{})]})}),e.jsx(N,{control:r.control,name:"effects.armour",render:({field:l})=>e.jsxs(b,{className:"w-full",children:[e.jsxs(C,{children:[a.get("ui_armour_effect"),": ",l.value]}),e.jsx(v,{children:e.jsx(pe,{value:[l.value],min:-100,max:100,step:1,onValueChange:u=>l.onChange(u[0])})}),e.jsx(O,{})]})})]}),e.jsxs("div",{className:"flex flex-col md:flex-row md:space-x-4 space-y-4 md:space-y-0",children:[e.jsx(N,{control:r.control,name:"effects.hunger",render:({field:l})=>e.jsxs(b,{className:"w-full",children:[e.jsxs(C,{children:[a.get("ui_hunger_effect"),": ",l.value]}),e.jsx(v,{children:e.jsx(pe,{value:[l.value],min:-100,max:100,step:1,onValueChange:u=>l.onChange(u[0])})}),e.jsx(O,{})]})}),e.jsx(N,{control:r.control,name:"effects.thirst",render:({field:l})=>e.jsxs(b,{className:"w-full",children:[e.jsxs(C,{children:[a.get("ui_thirst_effect"),": ",l.value]}),e.jsx(v,{children:e.jsx(pe,{value:[l.value],min:-100,max:100,step:1,onValueChange:u=>l.onChange(u[0])})}),e.jsx(O,{})]})})]})]}),e.jsx(N,{control:r.control,name:"effects.overdoseLevel",render:({field:l})=>e.jsxs(b,{children:[e.jsx(C,{children:a.get("ui_overdose_level")}),e.jsx(v,{children:e.jsx(k,{type:"number",...l,min:0,max:1,step:.01})}),e.jsx(L,{children:a.get("ui_overdose_level_desc")}),e.jsx(O,{})]})})]})})]}),e.jsx(D,{type:"submit",className:"w-full mt-4",children:t?a.get("ui_save_changes"):a.get("ui_add_consumable")})]})})})]})})}function Hi(){const{serverConfig:s,updateConsumable:n}=ie(),[t,i]=o.useState((s==null?void 0:s.consumables)||[]),[r,a]=o.useState(!1),[x,l]=o.useState(null),[u,_]=o.useState(null),[m,j]=o.useState(1),h=6,[M,R]=o.useState(""),[E]=Ge(M,300),z=Xe(),p=W(),S=c=>{a(c),setTimeout(()=>{c||(l(null),_(null))},200)};o.useEffect(()=>{s&&i(s.consumables||[])},[s]);const w=c=>{if(s){const d=Array.isArray(c)?c:[c],f=t.length,F=[...t,...d];i(F),d.forEach((ee,me)=>{n(f+me,ee)})}a(!1)},y=c=>{if(x&&u!==null){const d=t.map((f,F)=>F===u?c:f);i(d),n(u,c),a(!1),setTimeout(()=>{l(null),_(null)},500)}},A=async c=>{if(await z(p.get("ui_delete_consumable"),p.get("ui_delete_consumable_desc"))){const d=t.filter((f,F)=>F!==c);i(d),s&&ie.getState().updateConsumables(d),U.length===1&&m>1&&j(m-1)}},G=c=>{if(s){const d={...c},f=[...t,d];i(f);const F=f.length-1;n(F,d);const ee=Math.ceil(f.length/h);j(ee)}},H=o.useMemo(()=>t.filter(c=>{var d;return((d=c.name)==null?void 0:d.toLowerCase().includes(E.toLowerCase()))||c.progress.toLowerCase().includes(E.toLowerCase())}),[t,E]),U=o.useMemo(()=>{const c=(m-1)*h;return H.slice(c,c+h)},[H,m]),V=Math.ceil(H.length/h),Z=o.useCallback(c=>{R(c.target.value),j(1)},[]),g=c=>{if(c<1e3)return`${c}ms`;const d=c/1e3;if(d<60)return`${d}s`;const f=Math.floor(d/60),F=Math.round(d%60);return`${f}m ${F}s`};return e.jsxs("div",{className:"space-y-4 flex flex-col h-full",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(k,{placeholder:p.get("ui_search_consumables"),value:M,onChange:Z,className:"max-w-sm"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(D,{onClick:()=>a(!0),children:[e.jsx(je,{className:"mr-2 h-4 w-4"}),p.get("ui_add_consumable")]}),e.jsx(Zi,{isOpen:r,editingConsumable:x||void 0,setIsOpen:S,onSubmit:x?y:w})]})]}),e.jsx("div",{className:"grow",children:e.jsxs(se,{className:"table-fixed",children:[e.jsx(ne,{children:e.jsxs(K,{children:[e.jsx(P,{children:p.get("ui_item_name")}),e.jsx(P,{children:p.get("ui_progress")}),e.jsx(P,{children:p.get("ui_duration")}),e.jsx(P,{children:p.get("ui_effect_duration")}),e.jsx(P,{children:p.get("ui_actions")})]})}),e.jsx(te,{children:U.length===0?e.jsx(K,{className:"hover:bg-background",children:e.jsx($,{colSpan:5,className:"h-full hover:bg-background",children:e.jsx(ns,{searchTerm:E})})}):U.map((c,d)=>{const f=(m-1)*h+d;return e.jsxs(K,{children:[e.jsx($,{children:c.name}),e.jsx($,{children:c.progress}),e.jsx($,{children:g(c.progressDuration)}),e.jsx($,{children:g(c.effects.duration)}),e.jsx($,{children:e.jsxs(Qe,{children:[e.jsx(es,{asChild:!0,children:e.jsxs(D,{variant:"ghost",className:"h-8 w-8 p-0",children:[e.jsx("span",{className:"sr-only",children:"Open menu"}),e.jsx(Ke,{className:"h-4 w-4"})]})}),e.jsxs(Me,{align:"end",children:[e.jsxs(ae,{onClick:()=>{l(c),_(f),a(!0)},children:[e.jsx(Be,{className:"mr-2 h-4 w-4"}),p.get("ui_edit")]}),e.jsx(ss,{getContent:()=>c,onDuplicate:G,isDropdown:!0}),e.jsxs(ae,{className:"text-red-500",onClick:()=>A(f),children:[e.jsx(Ye,{className:"mr-2 h-4 w-4"}),p.get("ui_delete")]})]})]})})]},f)})})]})}),e.jsxs("div",{className:"flex justify-end items-center space-x-2",children:[e.jsx(D,{onClick:()=>j(c=>Math.max(c-1,1)),disabled:m===1,variant:"outline",children:e.jsx(We,{className:"h-4 w-4"})}),e.jsx("div",{className:"flex items-center space-x-2",children:[...Array(V)].map((c,d)=>{const f=d+1;return f===1||f===V||f>=m-1&&f<=m+1?e.jsx(D,{variant:m===f?"outline":"ghost",size:"icon",onClick:()=>j(f),children:f},d):f===m-2||f===m+2?e.jsx("span",{children:"..."},d):null})}),e.jsx(D,{onClick:()=>j(c=>Math.min(c+1,V)),disabled:m===V||V===0,variant:"outline",children:e.jsx(Ae,{className:"h-4 w-4"})})]})]})}const Ui=q({activationItem:B().min(1,"ui_error_required"),animation:ws.optional(),animationProp:Cs.optional(),recipes:ta}),yn={activationItem:"",recipes:[{duration:1e4,progress:"",requiredItems:[{name:"",amount:1}],giveItems:[{name:"",amount:{min:1,max:1}}]}]};function Gi({isOpen:s,setIsOpen:n,editingProcessing:t,onSubmit:i,isSubmitting:r=!1}){const a=W(),x=Te({resolver:Pe(Ui),defaultValues:t||yn});o.useEffect(()=>{x.reset(t||yn)},[t,x]);function l(u){i(u)}return e.jsx(Ee,{open:s,onOpenChange:n,children:e.jsxs(ve,{"aria-describedby":void 0,className:"min-w-fit",children:[e.jsxs(Ne,{children:[e.jsx(ye,{children:t?a.get("ui_edit_pocket_processing"):a.get("ui_add_pocket_processing")}),e.jsx(ys,{})]}),e.jsx(Le,{...x,children:e.jsxs("form",{onSubmit:x.handleSubmit(l,$e),className:"h-fit py-4 w-[55rem]",children:[e.jsxs(Je,{defaultValue:"basic",children:[e.jsxs(Re,{className:"grid w-full grid-cols-3",children:[e.jsx(X,{value:"basic",children:a.get("ui_basic")}),e.jsx(X,{value:"recipes",children:a.get("ui_recipes")}),e.jsx(X,{value:"animation",children:a.get("ui_animation")})]}),e.jsx(J,{value:"basic",className:"space-y-4 shrink-0 py-4",children:e.jsx("div",{className:"flex space-x-4",children:e.jsx(N,{control:x.control,name:"activationItem",render:({field:u})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_activation_item")}),e.jsx(v,{children:e.jsx(k,{...u,placeholder:"plastic_bag"})}),e.jsx(L,{children:a.get("ui_activation_item_desc")}),e.jsx(O,{})]})})})}),e.jsx(J,{value:"recipes",className:"space-y-4 shrink-0 py-4",children:e.jsx(aa,{name:"recipes",control:x.control})}),e.jsx(J,{value:"animation",className:"space-y-4 shrink-0 py-4",children:e.jsx(Ss,{name:"animation"})})]}),e.jsx("div",{className:"flex justify-end mt-4",children:e.jsxs(D,{type:"submit",disabled:r,children:[r&&e.jsx(Es,{className:"mr-2 h-4 w-4 animate-spin"}),t?a.get("ui_save_changes"):a.get("ui_add_pocket_processing")]})})]})})]})})}function Ki(){const{serverConfig:s,updatePocketProcessing:n}=ie(),[t,i]=o.useState((s==null?void 0:s.pocketProcessing)||[]),[r,a]=o.useState(!1),[x,l]=o.useState(null),[u,_]=o.useState(null),[m,j]=o.useState(1),h=6,[M,R]=o.useState(""),[E]=Ge(M,300),z=Xe(),p=W(),S=g=>{a(g),setTimeout(()=>{g||(l(null),_(null))},200)};o.useEffect(()=>{s&&i(s.pocketProcessing||[])},[s]);const w=g=>{if(s){const c=Array.isArray(g)?g:[g],d=t.length,f=[...t,...c];i(f),c.forEach((F,ee)=>{n(d+ee,F)})}a(!1)},y=g=>{if(x&&u!==null){const c=t.map((d,f)=>f===u?g:d);i(c),n(u,g),a(!1),setTimeout(()=>{l(null),_(null)},500)}},A=async g=>{if(await z(p.get("ui_delete_pocket_processing"),p.get("ui_are_you_sure_delete_pocket_processing"))){const c=t.filter((d,f)=>f!==g);i(c),s&&ie.getState().updatePocketProcessings(c),U.length===1&&m>1&&j(m-1)}},G=g=>{if(s){const c={...g},d=[...t,c];i(d);const f=d.length-1;n(f,c);const F=Math.ceil(d.length/h);j(F)}},H=o.useMemo(()=>t.filter(g=>{var c,d;return((c=g.activationItem)==null?void 0:c.toLowerCase().includes(E.toLowerCase()))||((d=g.recipes)==null?void 0:d.some(f=>{var F;return(F=f.progress)==null?void 0:F.toLowerCase().includes(E.toLowerCase())}))}),[t,E]),U=o.useMemo(()=>{const g=(m-1)*h;return H.slice(g,g+h)},[H,m]),V=Math.ceil(H.length/h),Z=o.useCallback(g=>{R(g.target.value),j(1)},[]);return e.jsxs("div",{className:"space-y-4 flex flex-col h-full",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(k,{placeholder:p.get("ui_search_pocket_processing"),value:M,onChange:Z,className:"max-w-sm"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(D,{onClick:()=>a(!0),children:[e.jsx(je,{className:"mr-2 h-4 w-4"}),p.get("ui_add_pocket_processing")]}),e.jsx(Gi,{isOpen:r,editingProcessing:x||void 0,setIsOpen:S,onSubmit:x?g=>y(g):g=>w(g)})]})]}),e.jsx("div",{className:"grow",children:e.jsxs(se,{className:"table-fixed",children:[e.jsx(ne,{children:e.jsxs(K,{children:[e.jsx(P,{children:p.get("ui_activation_item")}),e.jsx(P,{children:p.get("ui_recipes")}),e.jsx(P,{children:p.get("ui_actions")})]})}),e.jsx(te,{children:U.length===0?e.jsx(K,{className:"hover:bg-background",children:e.jsx($,{colSpan:3,className:"h-full hover:bg-background",children:e.jsx(ns,{searchTerm:E})})}):U.map((g,c)=>{var f;const d=(m-1)*h+c;return e.jsxs(K,{children:[e.jsx($,{children:g.activationItem}),e.jsx($,{children:((f=g.recipes)==null?void 0:f.length)||0}),e.jsx($,{children:e.jsxs(Qe,{children:[e.jsx(es,{asChild:!0,children:e.jsxs(D,{variant:"ghost",className:"h-8 w-8 p-0",children:[e.jsx("span",{className:"sr-only",children:"Open menu"}),e.jsx(Ke,{className:"h-4 w-4"})]})}),e.jsxs(Me,{align:"end",children:[e.jsxs(ae,{onClick:()=>{l(g),_(d),a(!0)},children:[e.jsx(Be,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:p.get("ui_edit")})]}),e.jsx(ss,{getContent:()=>g,onDuplicate:G,isDropdown:!0}),e.jsxs(ae,{className:"text-red-500",onClick:()=>A(d),children:[e.jsx(Ye,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:p.get("ui_delete")})]})]})]})})]},d)})})]})}),e.jsxs("div",{className:"flex justify-end items-center space-x-2",children:[e.jsx(D,{onClick:()=>j(g=>Math.max(g-1,1)),disabled:m===1,variant:"outline",children:e.jsx(We,{className:"h-4 w-4"})}),e.jsx("div",{className:"flex items-center space-x-2",children:[...Array(V)].map((g,c)=>{const d=c+1;return d===1||d===V||d>=m-1&&d<=m+1?e.jsx(D,{variant:m===d?"outline":"ghost",size:"icon",onClick:()=>j(d),children:d},c):d===m-2||d===m+2?e.jsx("span",{children:"..."},c):null})}),e.jsx(D,{onClick:()=>j(g=>Math.min(g+1,V)),disabled:m===V||V===0,variant:"outline",children:e.jsx(Ae,{className:"h-4 w-4"})})]})]})}re(q({name:B().min(1,"ui_error_required"),price:I.catch(0),amount:q({min:I.catch(1),max:I.catch(1)}).catch({min:1,max:1})}));function Bi({id:s,children:n}){const{setNodeRef:t,transform:i,transition:r}=le({id:s}),a={transform:ze.Transform.toString(i),transition:r};return e.jsx(K,{ref:t,style:a,children:n})}function Yi({id:s}){const{attributes:n,listeners:t,setNodeRef:i}=le({id:s});return e.jsx($,{ref:i,...n,...t,children:e.jsx(qe,{className:"h-4 w-4 cursor-move"})})}function Wi({name:s,control:n,className:t}){const i=W(),{fields:r,append:a,remove:x,move:l}=Ve({name:s,control:n}),u=Fe(oe(Ue),oe(He,{coordinateGetter:Ze}));function _(m){const{active:j,over:h}=m;if(j.id!==h.id){const M=r.findIndex(E=>E.id===j.id),R=r.findIndex(E=>E.id===h.id);l(M,R)}}return e.jsxs("div",{className:T("space-y-4 shrink-0",t),children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(C,{children:i.get("ui_items")}),e.jsx(D,{type:"button",variant:"outline",size:"sm",onClick:()=>a({name:"",price:0,amount:{min:1,max:1}}),className:"shrink-0",children:i.get("ui_add_item")})]}),e.jsx(ce,{children:e.jsx(ue,{className:"px-0 flex flex-col",children:e.jsx(he,{className:"h-96 overflow-x-hidden",children:e.jsx(we,{sensors:u,collisionDetection:Ce,onDragEnd:_,modifiers:[Se,ke],children:e.jsxs(se,{className:"table-fixed h-full",children:[e.jsx(ne,{children:e.jsxs(K,{children:[e.jsx(P,{className:"w-10"}),e.jsx(P,{children:i.get("ui_item_name")}),e.jsx(P,{children:i.get("ui_price")}),e.jsx(P,{children:i.get("ui_amount_min")}),e.jsx(P,{children:i.get("ui_amount_max")}),e.jsx(P,{className:"w-24 text-center",children:i.get("ui_actions")})]})}),e.jsx(te,{children:e.jsx(Ie,{items:r.map(m=>m.id),strategy:De,children:r.map((m,j)=>e.jsxs(Bi,{id:m.id,children:[e.jsx(Yi,{id:m.id}),e.jsx($,{children:e.jsx(N,{name:`${s}.${j}.name`,control:n,render:({field:h})=>e.jsx(b,{children:e.jsx(v,{children:e.jsx(k,{...h,placeholder:i.get("ui_item_name")})})})})}),e.jsx($,{children:e.jsx(N,{name:`${s}.${j}.price`,control:n,render:({field:h})=>e.jsx(b,{children:e.jsx(v,{children:e.jsx(k,{...h,type:"number",placeholder:i.get("ui_price")})})})})}),e.jsx($,{children:e.jsx(N,{name:`${s}.${j}.amount.min`,control:n,render:({field:h})=>e.jsx(b,{children:e.jsx(v,{children:e.jsx(k,{...h,type:"number",placeholder:i.get("ui_min")})})})})}),e.jsx($,{children:e.jsx(N,{name:`${s}.${j}.amount.max`,control:n,render:({field:h})=>e.jsx(b,{children:e.jsx(v,{children:e.jsx(k,{...h,type:"number",placeholder:i.get("ui_max")})})})})}),e.jsx($,{className:"flex justify-center",children:e.jsx(D,{type:"button",variant:"outline",size:"icon",className:"text-red-500",onClick:()=>x(j),children:e.jsx(fe,{className:"h-4 w-4"})})})]},m.id))})})]})})})})})]})}const Xi=q({coords:q({x:I.catch(0),y:I.catch(0),z:I.catch(0)}),radius:I.catch(5)});re(Xi);function Ji({id:s,children:n}){const{setNodeRef:t,transform:i,transition:r}=le({id:s}),a={transform:ze.Transform.toString(i),transition:r};return e.jsx(K,{ref:t,style:a,children:n})}function Qi({id:s}){const{attributes:n,listeners:t,setNodeRef:i}=le({id:s});return e.jsx($,{ref:i,...n,...t,children:e.jsx(qe,{className:"h-4 w-4 cursor-move"})})}function el({name:s,className:n,pedField:t}){const i=W(),{control:r,watch:a,setValue:x}=rs(),{fields:l,append:u,remove:_,move:m}=Ve({name:s,control:r}),j=is();Array.isArray(a("locations"))||x("locations",[]);const h=a("target"),M=a("prop"),R=a(t||"ped"),E=Fe(oe(Ue),oe(He,{coordinateGetter:Ze})),z=async w=>{j.setVisible(!1);const y=await Y("getCoordsInput",{target:h,is4D:!1,forceDisableTarget:!0,prop:M,ped:[R]},{x:0,y:0,z:0},500);j.setVisible(!0),x(`${s}.${w}.coords`,y)},p=async w=>{j.setVisible(!1);const y=a(`${s}.${w}.coords`);await Y("teleport",y)};function S(w){const{active:y,over:A}=w;if(y.id!==A.id){const G=l.findIndex(U=>U.id===y.id),H=l.findIndex(U=>U.id===A.id);m(G,H)}}return e.jsxs("div",{className:T("space-y-4 shrink-0",n),children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(C,{children:i.get("ui_locations")}),e.jsx(D,{type:"button",variant:"outline",size:"sm",onClick:()=>u({coords:{x:0,y:0,z:0},radius:5}),className:"shrink-0",children:i.get("ui_add_location")})]}),e.jsx(ce,{children:e.jsx(ue,{className:"px-0 flex flex-col",children:e.jsx(he,{className:"h-96",children:e.jsx(we,{sensors:E,collisionDetection:Ce,onDragEnd:S,modifiers:[Se,ke],children:e.jsxs(se,{className:"table-fixed",children:[e.jsx(ne,{children:e.jsxs(K,{children:[e.jsx(P,{className:"w-8 px-2"}),e.jsx(P,{className:"w-[18%]",children:"X"}),e.jsx(P,{className:"w-[18%]",children:"Y"}),e.jsx(P,{className:"w-[18%]",children:"Z"}),e.jsx(P,{className:"w-[18%]",children:"Radius"}),e.jsx(P,{className:"w-[20%] text-center",children:i.get("ui_actions")})]})}),e.jsx(te,{children:e.jsx(Ie,{items:l.map(w=>w.id),strategy:De,children:l.map((w,y)=>e.jsxs(Ji,{id:w.id,children:[e.jsx(Qi,{id:w.id}),["x","y","z"].map(A=>e.jsx($,{children:e.jsx(N,{name:`${s}.${y}.coords.${A}`,control:r,render:({field:G})=>e.jsx(b,{children:e.jsx(v,{children:e.jsx(k,{...G,type:"number",placeholder:A.toUpperCase(),step:"0.1"})})})})},A)),e.jsx($,{children:e.jsx(N,{name:`${s}.${y}.radius`,control:r,render:({field:A})=>e.jsx(b,{children:e.jsx(v,{children:e.jsx(k,{...A,type:"number",placeholder:"Radius",step:"0.1"})})})})}),e.jsx($,{children:e.jsxs("div",{className:"flex justify-center space-x-2",children:[e.jsx(D,{type:"button",variant:"outline",size:"icon",className:"shrink-0",onClick:()=>z(y),children:e.jsx(as,{className:"h-4 w-4"})}),e.jsx(D,{type:"button",variant:"outline",size:"icon",className:"shrink-0",onClick:()=>p(y),children:e.jsx(Oe,{className:"h-4 w-4"})}),e.jsx(D,{type:"button",variant:"outline",size:"icon",className:"text-red-500 shrink-0",onClick:()=>_(y),children:e.jsx(fe,{className:"h-4 w-4"})})]})})]},w.id))})})]})})})})})]})}const sl=q({label:B().min(1,"ui_error_required"),priceTolerance:Ts().min(0).max(1),maxAttempts:I,acceptChance:Ts().min(0).max(1),reportChance:Ts().min(0).max(1),account:B().min(1,"ui_error_required"),items:re(q({name:B().min(1,"ui_error_required"),price:I,amount:q({min:I,max:I})})),locations:re(q({coords:q({x:I,y:I,z:I}),radius:I})),target:xe().optional()}),wn={label:"",priceTolerance:.1,maxAttempts:3,acceptChance:.7,reportChance:.3,account:"black_money",items:[{name:"",price:0,amount:{min:1,max:3}}],locations:[{coords:{x:0,y:0,z:0},radius:400}]};function nl({isOpen:s,setIsOpen:n,editingZone:t,onSubmit:i}){const r=W(),a=Te({resolver:Pe(sl),defaultValues:t||wn});o.useEffect(()=>{t?a.reset(t):a.reset(wn)},[t,a]);function x(l){i(l),n(!1)}return e.jsx(Ee,{open:s,onOpenChange:n,children:e.jsxs(ve,{"aria-describedby":void 0,className:"min-w-fit",children:[e.jsx(Ne,{children:e.jsx(ye,{children:t?r.get("ui_edit_retail_selling_zone"):r.get("ui_add_retail_selling_zone")})}),e.jsx(Le,{...a,children:e.jsxs("form",{onSubmit:a.handleSubmit(x,$e),className:"h-fit py-4 w-[50rem]",children:[e.jsxs(Je,{defaultValue:"basic",children:[e.jsxs(Re,{className:"grid w-full grid-cols-3",children:[e.jsx(X,{value:"basic",children:r.get("ui_basic")}),e.jsx(X,{value:"items",children:r.get("ui_items")}),e.jsx(X,{value:"locations",children:r.get("ui_locations")})]}),e.jsxs(J,{value:"basic",className:"space-y-4 shrink-0 py-4",children:[e.jsx(N,{control:a.control,name:"label",render:({field:l})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:r.get("ui_retail_selling_label")}),e.jsx(v,{children:e.jsx(k,{...l,placeholder:r.get("ui_retail_selling_label_placeholder")})}),e.jsx(L,{children:r.get("ui_retail_selling_label_desc")}),e.jsx(O,{})]})}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:a.control,name:"maxAttempts",render:({field:l})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:r.get("ui_max_attempts")}),e.jsx(v,{children:e.jsx(k,{type:"number",...l})}),e.jsx(L,{children:r.get("ui_max_attempts_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:a.control,name:"account",render:({field:l})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:r.get("ui_account")}),e.jsx(v,{children:e.jsx(k,{...l})}),e.jsx(L,{children:r.get("ui_account_desc")}),e.jsx(O,{})]})})]}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:a.control,name:"priceTolerance",render:({field:l})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:r.get("ui_price_tolerance")}),e.jsx(v,{children:e.jsx(pe,{min:0,max:1,step:.01,value:[l.value],onValueChange:u=>l.onChange(u[0])})}),e.jsxs(L,{children:[Math.round(l.value*100),"% - ",r.get("ui_price_tolerance_desc")]}),e.jsx(O,{})]})}),e.jsx(N,{control:a.control,name:"acceptChance",render:({field:l})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:r.get("ui_accept_chance")}),e.jsx(v,{children:e.jsx(pe,{min:0,max:1,step:.01,value:[l.value],onValueChange:u=>l.onChange(u[0])})}),e.jsxs(L,{children:[Math.round(l.value*100),"% - ",r.get("ui_accept_chance_desc")]}),e.jsx(O,{})]})})]}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(ds,{name:"target",description:r.get("ui_target_desc")}),e.jsx(N,{control:a.control,name:"reportChance",render:({field:l})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:r.get("ui_report_chance")}),e.jsx(v,{children:e.jsx(pe,{min:0,max:1,step:.01,value:[l.value],onValueChange:u=>l.onChange(u[0])})}),e.jsxs(L,{children:[Math.round(l.value*100),"% - ",r.get("ui_report_chance_desc")]}),e.jsx(O,{})]})})]})]}),e.jsx(J,{value:"items",className:"space-y-4 shrink-0 py-4",children:e.jsx(Wi,{name:"items",control:a.control})}),e.jsx(J,{value:"locations",className:"space-y-4 shrink-0 py-4",children:e.jsx(el,{name:"locations"})})]}),e.jsx(D,{type:"submit",className:"w-full mt-4",children:t?r.get("ui_save_changes"):r.get("ui_add_retail_selling_zone")})]})})]})})}function tl(){const{serverConfig:s,updateRetailSellingZone:n}=ie(),[t,i]=o.useState((s==null?void 0:s.retailSellingZones)||[]),[r,a]=o.useState(!1),[x,l]=o.useState(null),[u,_]=o.useState(null),[m,j]=o.useState(1),h=6,[M,R]=o.useState(""),[E]=Ge(M,300),z=Xe(),p=W(),S=g=>{a(g),setTimeout(()=>{g||(l(null),_(null))},200)};o.useEffect(()=>{s&&i(s.retailSellingZones||[])},[s]);const w=g=>{if(s){const c=Array.isArray(g)?g:[g],d=t.length,f=[...t,...c];i(f),c.forEach((F,ee)=>{n(d+ee,F)})}a(!1)},y=g=>{if(x&&u!==null){const c=t.map((d,f)=>f===u?g:d);i(c),n(u,g),a(!1),setTimeout(()=>{l(null),_(null)},500)}},A=async g=>{if(await z(p.get("ui_delete_retail_selling_zone"),p.get("ui_delete_retail_selling_zone_desc"))){const c=t.filter((d,f)=>f!==g);i(c),s&&ie.getState().updateRetailSellingZones(c),U.length===1&&m>1&&j(m-1)}},G=g=>{if(s){const c={...g},d=[...t,c];i(d);const f=d.length-1;n(f,c);const F=Math.ceil(d.length/h);j(F)}},H=o.useMemo(()=>t.filter(g=>{var c;return((c=g.label)==null?void 0:c.toLowerCase().includes(E.toLowerCase()))||g.account.toLowerCase().includes(E.toLowerCase())}),[t,E]),U=o.useMemo(()=>{const g=(m-1)*h;return H.slice(g,g+h)},[H,m]),V=Math.ceil(H.length/h),Z=o.useCallback(g=>{R(g.target.value),j(1)},[]);return e.jsxs("div",{className:"space-y-4 flex flex-col h-full",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(k,{placeholder:p.get("ui_search_retail_selling_zones"),value:M,onChange:Z,className:"max-w-sm"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(D,{onClick:()=>a(!0),children:[e.jsx(je,{className:"mr-2 h-4 w-4"}),p.get("ui_add_retail_selling_zone")]}),e.jsx(nl,{isOpen:r,editingZone:x||void 0,setIsOpen:S,onSubmit:x?y:w})]})]}),e.jsx("div",{className:"grow",children:e.jsxs(se,{className:"table-fixed",children:[e.jsx(ne,{children:e.jsxs(K,{children:[e.jsx(P,{children:p.get("ui_label")}),e.jsx(P,{children:p.get("ui_account")}),e.jsx(P,{children:p.get("ui_price_tolerance")}),e.jsx(P,{children:p.get("ui_accept_chance")}),e.jsx(P,{children:p.get("ui_report_chance")}),e.jsx(P,{children:p.get("ui_actions")})]})}),e.jsx(te,{children:U.length===0?e.jsx(K,{className:"hover:bg-background",children:e.jsx($,{colSpan:6,className:"h-full hover:bg-background",children:e.jsx(ns,{searchTerm:E})})}):U.map((g,c)=>{const d=(m-1)*h+c;return e.jsxs(K,{children:[e.jsx($,{children:g.label}),e.jsx($,{children:g.account}),e.jsxs($,{children:[g.priceTolerance*100,"%"]}),e.jsxs($,{children:[g.acceptChance*100,"%"]}),e.jsxs($,{children:[g.reportChance*100,"%"]}),e.jsx($,{children:e.jsxs(Qe,{children:[e.jsx(es,{asChild:!0,children:e.jsxs(D,{variant:"ghost",className:"h-8 w-8 p-0",children:[e.jsx("span",{className:"sr-only",children:"Open menu"}),e.jsx(Ke,{className:"h-4 w-4"})]})}),e.jsxs(Me,{align:"end",children:[e.jsxs(ae,{onClick:()=>{l(g),_(d),a(!0)},children:[e.jsx(Be,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:p.get("ui_edit")})]}),e.jsx(ss,{getContent:()=>g,onDuplicate:G,isDropdown:!0}),e.jsxs(ae,{className:"text-red-500",onClick:()=>A(d),children:[e.jsx(Ye,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:p.get("ui_delete")})]})]})]})})]},d)})})]})}),e.jsxs("div",{className:"flex justify-end items-center space-x-2",children:[e.jsx(D,{onClick:()=>j(g=>Math.max(g-1,1)),disabled:m===1,variant:"outline",children:e.jsx(We,{className:"h-4 w-4"})}),e.jsx("div",{className:"flex items-center space-x-2",children:[...Array(V)].map((g,c)=>{const d=c+1;return d===1||d===V||d>=m-1&&d<=m+1?e.jsx(D,{variant:m===d?"outline":"ghost",size:"icon",onClick:()=>j(d),children:d},c):d===m-2||d===m+2?e.jsx("span",{children:"..."},c):null})}),e.jsx(D,{onClick:()=>j(g=>Math.min(g+1,V)),disabled:m===V,variant:"outline",children:e.jsx(Ae,{className:"h-4 w-4"})})]})]})}const al=q({enabled:xe().default(!0),phoneItem:B().min(1,"ui_error_required"),clientInterval:q({min:I,max:I}),locations:re(q({x:I,y:I,z:I,w:I})),account:B().min(1,"ui_error_required"),items:re(q({name:B().min(1,"ui_error_required"),price:q({min:I,max:I}),amount:q({min:I,max:I})}))});function rl({id:s,children:n}){const{setNodeRef:t,transform:i,transition:r}=le({id:s}),a={transform:ze.Transform.toString(i),transition:r};return e.jsx(K,{ref:t,style:a,children:n})}function il({id:s,children:n}){const{setNodeRef:t,transform:i,transition:r}=le({id:s}),a={transform:ze.Transform.toString(i),transition:r};return e.jsx(K,{ref:t,style:a,children:n})}function Cn({id:s}){const{attributes:n,listeners:t,setNodeRef:i}=le({id:s});return e.jsx($,{ref:i,...n,...t,children:e.jsx(qe,{className:"h-4 w-4 cursor-move"})})}function ll(){const{serverConfig:s,updateWholesaleSettings:n}=ie(),[t,i]=o.useState(!1),r=W(),{setVisible:a}=is(),x=Fe(oe(Ue),oe(He,{coordinateGetter:Ze})),l=Te({resolver:Pe(al),defaultValues:(s==null?void 0:s.wholesaleSettings)||{enabled:!0,phoneItem:"phone",clientInterval:{min:30,max:60},locations:[{x:0,y:0,z:0,w:0}],account:"money",items:[{name:"",price:{min:100,max:200},amount:{min:1,max:5}}]}}),u=l.watch("items")||[],_=l.watch("locations")||[];o.useEffect(()=>{s!=null&&s.wholesaleSettings&&l.reset(s.wholesaleSettings)},[s,l]);const m=async w=>{i(!0);try{n(w)}finally{i(!1)}},j=()=>{const w=l.getValues("items")||[];l.setValue("items",[...w,{name:"",price:{min:100,max:200},amount:{min:1,max:5}}])},h=w=>{const y=l.getValues("items")||[];if(y.length>1){const A=[...y];A.splice(w,1),l.setValue("items",A)}},M=()=>{const w=[..._];l.setValue("locations",[...w,{x:0,y:0,z:0,w:0}])},R=w=>{const y=[..._];y.splice(w,1),l.setValue("locations",y)},E=async w=>{a(!1);const y=await Y("getCoordsInput",{is4D:!0,prop:void 0,ped:["g_m_m_mexboss_01"]},{x:0,y:0,z:0,w:0},500);a(!0),l.setValue(`locations.${w}`,y)},z=async w=>{a(!1);const y=_[w];await Y("teleport",{x:y.x,y:y.y,z:y.z,w:y.w})},p=w=>{const{active:y,over:A}=w;if(y.id!==A.id){const G=parseInt(y.id),H=parseInt(A.id),U=l.getValues("items"),V=cn(U,G,H);l.setValue("items",V)}},S=w=>{const{active:y,over:A}=w;if(y.id!==A.id){const G=_.findIndex((V,Z)=>Z.toString()===y.id),H=_.findIndex((V,Z)=>Z.toString()===A.id),U=cn(_,G,H);l.setValue("locations",U)}};return e.jsx(Le,{...l,children:e.jsx("form",{onSubmit:l.handleSubmit(m,$e),className:"flex flex-col h-full",children:e.jsx("div",{className:"flex-1 overflow-hidden flex flex-col",children:e.jsx(he,{className:"flex-1",children:e.jsxs("div",{className:"space-y-6 pr-4 pl-2",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-lg font-bold",children:r.get("ui_wholesale_settings")}),e.jsx("p",{className:"text-sm text-muted-foreground",children:r.get("ui_wholesale_settings_desc")})]}),e.jsx(N,{control:l.control,name:"enabled",render:({field:w})=>e.jsxs(b,{className:"flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4",children:[e.jsx(v,{children:e.jsx(ge,{checked:w.value,onCheckedChange:w.onChange})}),e.jsxs("div",{className:"space-y-1 leading-none",children:[e.jsx(C,{children:r.get("ui_enable_wholesale_selling")}),e.jsx(L,{children:r.get("ui_enable_wholesale_selling_desc")})]})]})}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:l.control,name:"phoneItem",render:({field:w})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:r.get("ui_phone_item")}),e.jsx(v,{children:e.jsx(k,{...w,placeholder:"phone"})}),e.jsx(L,{children:r.get("ui_phone_item_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:l.control,name:"account",render:({field:w})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:r.get("ui_account")}),e.jsx(v,{children:e.jsx(k,{...w,placeholder:"money"})}),e.jsx(L,{children:r.get("ui_account_desc")}),e.jsx(O,{})]})})]}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:l.control,name:"clientInterval.min",render:({field:w})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:r.get("ui_min_interval")}),e.jsx(v,{children:e.jsx(k,{type:"number",...w})}),e.jsx(L,{children:r.get("ui_min_interval_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:l.control,name:"clientInterval.max",render:({field:w})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:r.get("ui_max_interval")}),e.jsx(v,{children:e.jsx(k,{type:"number",...w})}),e.jsx(L,{children:r.get("ui_max_interval_desc")}),e.jsx(O,{})]})})]}),e.jsx(os,{}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-lg font-bold",children:r.get("ui_locations")}),e.jsx("p",{className:"text-sm text-muted-foreground",children:r.get("ui_wholesale_locations_desc")})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs(D,{type:"button",variant:"outline",size:"sm",onClick:M,children:[e.jsx(je,{className:"h-4 w-4 mr-2"}),r.get("ui_add_location")]})}),e.jsx(ce,{children:e.jsx(ue,{className:"px-0 flex flex-col",children:e.jsx(he,{className:"h-96",children:e.jsx(we,{sensors:x,collisionDetection:Ce,onDragEnd:S,modifiers:[Se,ke],children:e.jsxs(se,{className:"table-fixed",children:[e.jsx(ne,{children:e.jsxs(K,{children:[e.jsx(P,{className:"w-8 px-2"}),e.jsx(P,{className:"w-[18%]",children:"X"}),e.jsx(P,{className:"w-[18%]",children:"Y"}),e.jsx(P,{className:"w-[18%]",children:"Z"}),e.jsx(P,{className:"w-[18%]",children:"W"}),e.jsx(P,{className:"w-[20%] text-center",children:r.get("ui_actions")})]})}),e.jsx(te,{children:e.jsx(Ie,{items:_.map((w,y)=>y.toString()),strategy:De,children:_.map((w,y)=>e.jsxs(il,{id:y.toString(),children:[e.jsx(Cn,{id:y.toString()}),["x","y","z","w"].map(A=>e.jsx($,{children:e.jsx(N,{name:`locations.${y}.${A}`,control:l.control,render:({field:G})=>e.jsx(b,{children:e.jsx(v,{children:e.jsx(k,{...G,type:"number",placeholder:A.toUpperCase(),step:"0.1"})})})})},A)),e.jsx($,{children:e.jsxs("div",{className:"flex justify-center space-x-2",children:[e.jsx(D,{type:"button",variant:"outline",size:"icon",className:"shrink-0",onClick:()=>E(y),children:e.jsx(as,{className:"h-4 w-4"})}),e.jsx(D,{type:"button",variant:"outline",size:"icon",className:"shrink-0",onClick:()=>z(y),children:e.jsx(Oe,{className:"h-4 w-4"})}),e.jsx(D,{type:"button",variant:"outline",size:"icon",className:"text-red-500 shrink-0",onClick:A=>{A.preventDefault(),A.stopPropagation(),R(y)},children:e.jsx(fe,{className:"h-4 w-4"})})]})})]},y))})})]})})})})})]}),e.jsx(os,{}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-lg font-bold",children:r.get("ui_wholesale_items")}),e.jsx("p",{className:"text-sm text-muted-foreground",children:r.get("ui_wholesale_items_desc")})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs(D,{type:"button",variant:"outline",size:"sm",onClick:j,children:[e.jsx(je,{className:"h-4 w-4 mr-2"}),r.get("ui_add_item")]})}),e.jsx(ce,{children:e.jsx(ue,{className:"px-0 flex flex-col",children:e.jsx(he,{className:"h-72",children:e.jsx(we,{sensors:x,collisionDetection:Ce,onDragEnd:p,modifiers:[Se,ke],children:e.jsxs(se,{className:"table-fixed",children:[e.jsx(ne,{children:e.jsxs(K,{children:[e.jsx(P,{className:"w-8 px-2"}),e.jsx(P,{className:"w-[25%]",children:r.get("ui_item_name")}),e.jsx(P,{className:"w-[15%]",children:r.get("ui_price_min")}),e.jsx(P,{className:"w-[15%]",children:r.get("ui_price_max")}),e.jsx(P,{className:"w-[17%]",children:r.get("ui_amount_min")}),e.jsx(P,{className:"w-[17%]",children:r.get("ui_amount_max")}),e.jsx(P,{className:"w-[15%] text-center",children:r.get("ui_actions")})]})}),e.jsx(te,{children:e.jsx(Ie,{items:u.map((w,y)=>y.toString()),strategy:De,children:u.map((w,y)=>e.jsxs(rl,{id:y.toString(),children:[e.jsx(Cn,{id:y.toString()}),e.jsx($,{children:e.jsx(N,{control:l.control,name:`items.${y}.name`,render:({field:A})=>e.jsxs(b,{children:[e.jsx(v,{children:e.jsx(k,{...A,placeholder:r.get("ui_item_name")})}),e.jsx(O,{})]})})}),e.jsx($,{children:e.jsx(N,{control:l.control,name:`items.${y}.price.min`,render:({field:A})=>e.jsxs(b,{children:[e.jsx(v,{children:e.jsx(k,{type:"number",...A,placeholder:r.get("ui_price_min")})}),e.jsx(O,{})]})})}),e.jsx($,{children:e.jsx(N,{control:l.control,name:`items.${y}.price.max`,render:({field:A})=>e.jsxs(b,{children:[e.jsx(v,{children:e.jsx(k,{type:"number",...A,placeholder:r.get("ui_price_max")})}),e.jsx(O,{})]})})}),e.jsx($,{children:e.jsx(N,{control:l.control,name:`items.${y}.amount.min`,render:({field:A})=>e.jsxs(b,{children:[e.jsx(v,{children:e.jsx(k,{type:"number",...A,placeholder:r.get("ui_amount_min")})}),e.jsx(O,{})]})})}),e.jsx($,{children:e.jsx(N,{control:l.control,name:`items.${y}.amount.max`,render:({field:A})=>e.jsxs(b,{children:[e.jsx(v,{children:e.jsx(k,{type:"number",...A,placeholder:r.get("ui_amount_max")})}),e.jsx(O,{})]})})}),e.jsx($,{children:e.jsx("div",{className:"flex justify-center space-x-2",children:e.jsx(D,{type:"button",variant:"outline",size:"icon",className:"text-red-500 shrink-0",onClick:A=>{A.preventDefault(),A.stopPropagation(),h(y)},disabled:u.length<=1,children:e.jsx(fe,{className:"h-4 w-4"})})})})]},y))})})]})})})})})]}),e.jsxs(D,{type:"submit",className:"max-w-xs flex items-center justify-center",disabled:t||!l.formState.isDirty,children:[t&&e.jsx(Es,{className:"mr-2 h-4 w-4 animate-spin"}),r.get("ui_save_changes")]})]})})})})})}const cl={light:"",dark:".dark"},ra=o.createContext(null);function ia(){const s=o.useContext(ra);if(!s)throw new Error("useChart must be used within a <ChartContainer />");return s}const la=o.forwardRef(({id:s,className:n,children:t,config:i,...r},a)=>{const x=o.useId(),l=`chart-${s||x.replace(/:/g,"")}`;return e.jsx(ra.Provider,{value:{config:i},children:e.jsxs("div",{"data-chart":l,ref:a,className:T("flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none",n),...r,children:[e.jsx(ol,{id:l,config:i}),e.jsx(xr,{children:t})]})})});la.displayName="Chart";const ol=({id:s,config:n})=>{const t=Object.entries(n).filter(([i,r])=>r.theme||r.color);return t.length?e.jsx("style",{dangerouslySetInnerHTML:{__html:Object.entries(cl).map(([i,r])=>`
${r} [data-chart=${s}] {
${t.map(([a,x])=>{var u;const l=((u=x.theme)==null?void 0:u[i])||x.color;return l?`  --color-${a}: ${l};`:null}).join(`
`)}
}
`).join(`
`)}}):null},dl=ur,ca=o.forwardRef(({active:s,payload:n,className:t,indicator:i="dot",hideLabel:r=!1,hideIndicator:a=!1,label:x,labelFormatter:l,labelClassName:u,formatter:_,color:m,nameKey:j,labelKey:h},M)=>{const{config:R}=ia(),E=o.useMemo(()=>{var A;if(r||!(n!=null&&n.length))return null;const[p]=n,S=`${h||p.dataKey||p.name||"value"}`,w=Ws(R,p,S),y=!h&&typeof x=="string"?((A=R[x])==null?void 0:A.label)||x:w==null?void 0:w.label;return l?e.jsx("div",{className:T("font-medium",u),children:l(y,n)}):y?e.jsx("div",{className:T("font-medium",u),children:y}):null},[x,l,n,r,u,R,h]);if(!s||!(n!=null&&n.length))return null;const z=n.length===1&&i!=="dot";return e.jsxs("div",{ref:M,className:T("grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl",t),children:[z?null:E,e.jsx("div",{className:"grid gap-1.5",children:n.map((p,S)=>{const w=`${j||p.name||p.dataKey||"value"}`,y=Ws(R,p,w),A=m||p.payload.fill||p.color;return e.jsx("div",{className:T("flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground",i==="dot"&&"items-center"),children:_&&(p==null?void 0:p.value)!==void 0&&p.name?_(p.value,p.name,p,S,p.payload):e.jsxs(e.Fragment,{children:[y!=null&&y.icon?e.jsx(y.icon,{}):!a&&e.jsx("div",{className:T("shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]",{"h-2.5 w-2.5":i==="dot","w-1":i==="line","w-0 border-[1.5px] border-dashed bg-transparent":i==="dashed","my-0.5":z&&i==="dashed"}),style:{"--color-bg":A,"--color-border":A}}),e.jsxs("div",{className:T("flex flex-1 justify-between leading-none",z?"items-end":"items-center"),children:[e.jsxs("div",{className:"grid gap-1.5",children:[z?E:null,e.jsx("span",{className:"text-muted-foreground",children:(y==null?void 0:y.label)||p.name})]}),p.value&&e.jsx("span",{className:"font-mono font-medium tabular-nums text-foreground",children:p.value.toLocaleString()})]})]})},p.dataKey)})})]})});ca.displayName="ChartTooltip";const xl=o.forwardRef(({className:s,hideIcon:n=!1,payload:t,verticalAlign:i="bottom",nameKey:r},a)=>{const{config:x}=ia();return t!=null&&t.length?e.jsx("div",{ref:a,className:T("flex items-center justify-center gap-4",i==="top"?"pb-3":"pt-3",s),children:t.map(l=>{const u=`${r||l.dataKey||"value"}`,_=Ws(x,l,u);return e.jsxs("div",{className:T("flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground"),children:[_!=null&&_.icon&&!n?e.jsx(_.icon,{}):e.jsx("div",{className:"h-2 w-2 shrink-0 rounded-[2px]",style:{backgroundColor:l.color}}),_==null?void 0:_.label]},l.value)})}):null});xl.displayName="ChartLegend";function Ws(s,n,t){if(typeof n!="object"||n===null)return;const i="payload"in n&&typeof n.payload=="object"&&n.payload!==null?n.payload:void 0;let r=t;return t in n&&typeof n[t]=="string"?r=n[t]:i&&t in i&&typeof i[t]=="string"&&(r=i[t]),r in s?s[r]:s[t]}const ul=[{value:"daily",label:"ui_daily"},{value:"weekly",label:"ui_weekly"},{value:"monthly",label:"ui_monthly"}];function ml(){return new Date().getFullYear()}function Is(s){return s.toLocaleDateString("en-US",{month:"short",day:"numeric"})}function hl(s){const n=new Date(s);n.setDate(s.getDate()+6);const t=s.toLocaleDateString("en-US",{month:"short"}),i=n.toLocaleDateString("en-US",{month:"short"});return t===i?`${t} ${s.getDate()}-${n.getDate()}`:`${t} ${s.getDate()}-${i} ${n.getDate()}`}function jl(s,n){var r;const t=s.map(a=>({...a,date:typeof a.date=="string"?a.date:a.date||new Date().toISOString()})).sort((a,x)=>{const l=new Date(a.date).getTime();return new Date(x.date).getTime()-l});if(t.length===0)return[];const i=(r=t[0])!=null&&r.date?new Date(t[0].date):new Date;return n==="daily"?Array.from({length:7},(a,x)=>{const l=new Date(i);l.setDate(i.getDate()-x);const u=Is(l),_=t.find(m=>Is(new Date(m.date))===u);return{name:u,drugsProcessed:(_==null?void 0:_.drugsProcessed)||0,drugsSold:(_==null?void 0:_.drugsSold)||0,date:l.toISOString()}}).reverse():n==="weekly"?Array.from({length:4},(a,x)=>{const l=new Date(i);l.setDate(i.getDate()-x*7);let u={name:hl(l),drugsProcessed:0,drugsSold:0,date:l.toISOString()};for(let _=0;_<7;_++){const m=new Date(l);m.setDate(l.getDate()-_);const j=Is(m),h=t.find(M=>Is(new Date(M.date))===j);h&&(u.drugsProcessed+=h.drugsProcessed,u.drugsSold+=h.drugsSold)}return u}).reverse():Array.from({length:12},(a,x)=>{const l=new Date(i);l.setMonth(i.getMonth()-x);let _={name:l.toLocaleDateString("en-US",{month:"short"}),drugsProcessed:0,drugsSold:0,date:new Date(l.getFullYear(),l.getMonth(),1).toISOString()};return t.forEach(m=>{const j=new Date(m.date);j.getMonth()===l.getMonth()&&j.getFullYear()===l.getFullYear()&&(_.drugsProcessed+=m.drugsProcessed,_.drugsSold+=m.drugsSold)}),_}).reverse()}function Sn(s,n){if(n===0)return s===0?"+0":"+100";const t=(s-n)/n*100;return`${t>=0?"+":""}${t.toFixed(1)}`}function kn(s,n){switch(s){case"daily":return n.get("ui_week");case"weekly":case"monthly":return n.get("ui_month");default:return""}}function gl(){const[s,n]=o.useState("daily"),{dailyStats:t,topSellingDrugs:i,totalProcessed:r,totalSold:a,previousPeriodProcessed:x,previousPeriodSold:l,topSellingDrug:u}=zt(),_=W(),m={drugsProcessed:{label:_.get("ui_drugs_processed"),color:"hsl(var(--chart-1))"},drugsSold:{label:_.get("ui_drugs_sold"),color:"hsl(var(--chart-2))"}},j=jl(t,s);return e.jsxs("div",{className:"space-y-6 h-full flex flex-col px-2",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("h1",{className:"text-xl font-bold",children:_.get("ui_dashboard")}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("span",{className:"text-sm text-muted-foreground",children:[_.get("ui_time_frame"),":"]}),e.jsxs(ms,{value:s,onValueChange:h=>n(h),children:[e.jsx(ls,{className:"w-[140px]",children:e.jsx(hs,{placeholder:_.get("ui_select_time_frame")})}),e.jsx(cs,{children:ul.map(h=>e.jsx(Q,{value:h.value,children:_.get(h.label)},h.value))})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs(ce,{children:[e.jsxs(fs,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(_s,{className:"text-sm font-medium",children:_.get("ui_drugs_processed")}),e.jsx(Fn,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(ue,{children:[e.jsx("div",{className:"text-lg font-bold",children:r}),e.jsxs(gs,{variant:"outline",className:"mt-2 text-muted-foreground",children:[Sn(r,x),"% ",_.get("ui_compared_to_previous")," ",kn(s,_)]})]})]}),e.jsxs(ce,{children:[e.jsxs(fs,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(_s,{className:"text-sm font-medium",children:_.get("ui_drugs_sold")}),e.jsx(Js,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(ue,{children:[e.jsx("div",{className:"text-lg font-bold",children:a}),e.jsxs(gs,{variant:"outline",className:"mt-2 text-muted-foreground",children:[Sn(a,l),"% ",_.get("ui_compared_to_previous")," ",kn(s,_)]})]})]}),e.jsxs(ce,{children:[e.jsxs(fs,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(_s,{className:"text-sm font-medium",children:_.get("ui_top_selling_drug")}),e.jsx(mr,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(ue,{children:[e.jsx("div",{className:"font-bold text-lg line-clamp-1",children:(u==null?void 0:u.name)||_.get("ui_none")}),u&&e.jsxs(gs,{variant:"outline",className:"mt-2 text-muted-foreground",children:[_.get("ui_units_sold"),": ",u.amount]})]})]})]}),e.jsxs(ce,{className:"mt-6 grow overflow-hidden flex flex-col",children:[e.jsxs(fs,{children:[e.jsx(_s,{className:"text-lg",children:_.get("ui_performance_overview")}),e.jsx(Ht,{children:_.get("ui_track_player_performance")})]}),e.jsxs(ue,{className:"p-0 grow relative",children:[e.jsx(la,{config:m,className:"h-72 w-full",children:e.jsxs(hr,{data:j,margin:{top:20,right:30,left:12,bottom:20},accessibilityLayer:!0,children:[e.jsx(jr,{vertical:!1,className:"stroke-muted"}),e.jsx(gr,{dataKey:"name",className:"text-xs fill-muted-foreground",tickLine:!1,tickMargin:8,axisLine:!1}),e.jsx(pr,{className:"text-xs fill-muted-foreground",tickLine:!1,axisLine:!1,allowDecimals:!1,tickCount:5}),e.jsx(dl,{wrapperStyle:{width:"12rem"},cursor:!1,content:e.jsx(ca,{})}),e.jsxs("defs",{children:[e.jsxs("linearGradient",{id:"fillDrugsProcessed",x1:"0",y1:"0",x2:"0",y2:"1",children:[e.jsx("stop",{offset:"5%",stopColor:"hsl(var(--chart-1))",stopOpacity:.8}),e.jsx("stop",{offset:"95%",stopColor:"hsl(var(--chart-1))",stopOpacity:.1})]}),e.jsxs("linearGradient",{id:"fillDrugsSold",x1:"0",y1:"0",x2:"0",y2:"1",children:[e.jsx("stop",{offset:"5%",stopColor:"hsl(var(--chart-2))",stopOpacity:.8}),e.jsx("stop",{offset:"95%",stopColor:"hsl(var(--chart-2))",stopOpacity:.1})]})]}),e.jsx(on,{dataKey:"drugsSold",type:"monotone",fill:"url(#fillDrugsSold)",fillOpacity:.4,stroke:"hsl(var(--chart-2))",stackId:"a"}),e.jsx(on,{dataKey:"drugsProcessed",type:"monotone",fill:"url(#fillDrugsProcessed)",fillOpacity:.4,stroke:"hsl(var(--chart-1))",stackId:"a"})]})}),t.length===0&&e.jsx("p",{className:"text-center text-2xl text-muted-foreground absolute top-[40%] left-[48%] transform -translate-x-1/2 -translate-y-1/2",children:_.get("ui_no_data_available")})]}),e.jsx(Ut,{children:e.jsx("div",{className:"flex w-full items-start gap-2 text-sm",children:e.jsxs("div",{className:"grid gap-2",children:[e.jsxs("div",{className:"flex items-center gap-2 font-medium leading-none",children:[_.get("ui_trending_up","8.2",s.slice(0,-2))," ",e.jsx(fr,{className:"h-4 w-4"})]}),e.jsx("div",{className:"flex items-center gap-2 leading-none text-muted-foreground",children:(()=>{const h=ml();switch(s){case"daily":return`${_.get("ui_monday_sunday")} ${h}`;case"weekly":return`${_.get("ui_week_1_4")} ${h}`;case"monthly":return`${_.get("ui_january_june")} ${h}`;default:return h}})()})]})})})]})]})}const nn=br,pl=_r,oa=o.forwardRef(({className:s,...n},t)=>e.jsx(Nt,{className:T("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...n,ref:t}));oa.displayName=Nt.displayName;const zs=o.forwardRef(({className:s,...n},t)=>e.jsxs(pl,{children:[e.jsx(oa,{}),e.jsx(yt,{ref:t,className:T("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...n})]}));zs.displayName=yt.displayName;const As=({className:s,...n})=>e.jsx("div",{className:T("flex flex-col space-y-2 text-center sm:text-left",s),...n});As.displayName="AlertDialogHeader";const Rs=({className:s,...n})=>e.jsx("div",{className:T("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...n});Rs.displayName="AlertDialogFooter";const Ms=o.forwardRef(({className:s,...n},t)=>e.jsx(wt,{ref:t,className:T("text-lg font-semibold",s),...n}));Ms.displayName=wt.displayName;const Os=o.forwardRef(({className:s,...n},t)=>e.jsx(Ct,{ref:t,className:T("text-sm text-muted-foreground",s),...n}));Os.displayName=Ct.displayName;const Vs=o.forwardRef(({className:s,...n},t)=>e.jsx(St,{ref:t,className:T(Qs(),s),...n}));Vs.displayName=St.displayName;const Fs=o.forwardRef(({className:s,...n},t)=>e.jsx(kt,{ref:t,className:T(Qs({variant:"outline"}),"mt-2 sm:mt-0",s),...n}));Fs.displayName=kt.displayName;function fl(){const[s,n]=o.useState(!1),[t,i]=o.useState(!1),r=W(),a=async()=>{i(!0);try{await Y("resetToPresetConfig"),i(!1),n(!1),ts.success(r.get("ui_reset_success"))}catch{i(!1),ts.error(r.get("ui_reset_error"))}};return e.jsxs(e.Fragment,{children:[e.jsxs(D,{variant:"outline",size:"sm",type:"button",onClick:()=>n(!0),className:"bg-background hover:bg-background2 text-foreground",children:[e.jsx(vr,{className:"h-4 w-4 mr-2"}),r.get("ui_reset_config")]}),e.jsx(nn,{open:s,onOpenChange:n,children:e.jsxs(zs,{children:[e.jsxs(As,{children:[e.jsx(Ms,{children:r.get("ui_reset_configuration")}),e.jsx(Os,{children:r.get("ui_reset_config_description")})]}),e.jsxs(Rs,{children:[e.jsx(Fs,{children:r.get("ui_cancel")}),e.jsx(Vs,{onClick:x=>{x.preventDefault(),a()},children:r.get("ui_confirm_reset")})]})]})})]})}function _l(){const[s,n]=o.useState(!1),[t,i]=o.useState(!1),r=W(),a=async()=>{i(!0);try{const x=await Y("loadConfigBackup");i(!1),n(!1),x.success?ts.success(r.get("ui_restore_success")):ts.error(r.get("ui_no_backup_found"))}catch{i(!1),ts.error(r.get("ui_restore_error"))}};return e.jsxs(e.Fragment,{children:[e.jsxs(D,{variant:"outline",size:"sm",type:"button",onClick:()=>n(!0),className:"bg-background hover:bg-background2 text-foreground",children:[e.jsx(Nr,{className:"h-4 w-4 mr-2"}),r.get("ui_restore_backup")]}),e.jsx(nn,{open:s,onOpenChange:n,children:e.jsxs(zs,{children:[e.jsxs(As,{children:[e.jsx(Ms,{children:r.get("ui_restore_backup_title")}),e.jsx(Os,{children:r.get("ui_restore_backup_description")})]}),e.jsxs(Rs,{children:[e.jsx(Fs,{children:r.get("ui_cancel")}),e.jsx(Vs,{onClick:x=>{x.preventDefault(),a()},children:r.get("ui_confirm_restore")})]})]})})]})}function bl(){const[s,n]=o.useState(!1),[t,i]=o.useState(!1),r=W(),a=async()=>{i(!0);try{await Y("makeConfigBackup"),i(!1),n(!1),ts.success(r.get("ui_backup_success"))}catch{i(!1),ts.error(r.get("ui_backup_error"))}};return e.jsxs(e.Fragment,{children:[e.jsxs(D,{variant:"outline",size:"sm",type:"button",onClick:()=>n(!0),className:"bg-background hover:bg-background2 text-foreground",children:[e.jsx(yr,{className:"h-4 w-4 mr-2"}),r.get("ui_backup_config")]}),e.jsx(nn,{open:s,onOpenChange:n,children:e.jsxs(zs,{children:[e.jsxs(As,{children:[e.jsx(Ms,{children:r.get("ui_backup_config_title")}),e.jsx(Os,{children:r.get("ui_backup_config_description")})]}),e.jsxs(Rs,{children:[e.jsx(Fs,{children:r.get("ui_cancel")}),e.jsx(Vs,{onClick:x=>{x.preventDefault(),a()},children:r.get("ui_confirm_backup")})]})]})})]})}const vl=q({sellDivisor:I,plantInterval:I,retailSellingCommand:B().min(1,"ui_error_required"),retailSellingMinPolice:I,webhook:B().url("ui_error_url").or(B().length(0)),progressDurations:q({watering:I,fertilizing:I,harvesting:I}),disableHackingDevice:xe().optional(),betterTablePlacing:xe().optional()});function Nl(){const{serverConfig:s,updateGeneralSettings:n}=ie(),[t,i]=o.useState(!1),r=W(),a=Te({resolver:Pe(vl),defaultValues:(s==null?void 0:s.generalSettings)||{sellDivisor:1,plantInterval:1,retailSellingCommand:"selldrugs",retailSellingMinPolice:0,webhook:"",progressDurations:{watering:3e3,fertilizing:3e3,harvesting:7500},disableHackingDevice:!1,betterTablePlacing:!1}});o.useEffect(()=>{s!=null&&s.generalSettings&&a.reset(s.generalSettings)},[s,a]);const x=async l=>{i(!0);try{n(l),a.reset(l)}finally{i(!1)}};return e.jsx(Le,{...a,children:e.jsx("form",{onSubmit:a.handleSubmit(x,$e),className:"flex flex-col h-full",children:e.jsx("div",{className:"flex-1 overflow-hidden flex flex-col",children:e.jsx(he,{className:"flex-1",children:e.jsxs("div",{className:"space-y-6 pr-4 pl-2",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-lg font-bold",children:r.get("ui_general_settings")}),e.jsx("p",{className:"text-sm text-muted-foreground",children:r.get("ui_general_settings_desc")})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(bl,{}),e.jsx(_l,{}),e.jsx(fl,{})]})]}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:a.control,name:"sellDivisor",render:({field:l})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:r.get("ui_sell_divisor")}),e.jsx(v,{children:e.jsx(k,{type:"number",...l})}),e.jsx(L,{children:r.get("ui_sell_divisor_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:a.control,name:"plantInterval",render:({field:l})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:r.get("ui_plant_interval")}),e.jsx(v,{children:e.jsx(k,{type:"number",...l})}),e.jsx(L,{children:r.get("ui_plant_interval_desc")}),e.jsx(O,{})]})})]}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:a.control,name:"retailSellingCommand",render:({field:l})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:r.get("ui_retail_selling_command")}),e.jsx(v,{children:e.jsx(k,{...l,placeholder:"selldrugs"})}),e.jsx(L,{children:r.get("ui_retail_selling_command_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:a.control,name:"retailSellingMinPolice",render:({field:l})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:r.get("ui_retail_selling_min_police")}),e.jsx(v,{children:e.jsx(k,{type:"number",min:"0",...l})}),e.jsx(L,{children:r.get("ui_retail_selling_min_police_desc")}),e.jsx(O,{})]})})]}),e.jsx(N,{control:a.control,name:"disableHackingDevice",render:({field:l})=>e.jsxs(b,{className:"flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4",children:[e.jsx(v,{children:e.jsx(ge,{checked:l.value,onCheckedChange:l.onChange})}),e.jsxs("div",{className:"space-y-1 leading-none",children:[e.jsx(C,{children:r.get("ui_disable_hacking_device")}),e.jsx(L,{children:r.get("ui_disable_hacking_device_desc")||"When enabled, uses ox_lib menu instead of the hacking device interface"})]})]})}),e.jsx(N,{control:a.control,name:"betterTablePlacing",render:({field:l})=>e.jsxs(b,{className:"flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4",children:[e.jsx(v,{children:e.jsx(ge,{checked:l.value,onCheckedChange:l.onChange})}),e.jsxs("div",{className:"space-y-1 leading-none",children:[e.jsx(C,{children:r.get("ui_better_table_placing")}),e.jsx(L,{children:r.get("ui_better_table_placing_desc")||"When enabled, uses an improved table placement system"})]})]})}),e.jsx(os,{}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-lg font-bold",children:r.get("ui_progress_durations")}),e.jsx("p",{className:"text-sm text-muted-foreground",children:r.get("ui_progress_durations_desc")})]}),e.jsxs("div",{className:"grid grid-cols-3 gap-4",children:[e.jsx(N,{control:a.control,name:"progressDurations.watering",render:({field:l})=>e.jsxs(b,{children:[e.jsx(C,{children:r.get("ui_watering_duration")}),e.jsx(v,{children:e.jsx(k,{type:"number",...l})}),e.jsx(L,{children:r.get("ui_watering_duration_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:a.control,name:"progressDurations.fertilizing",render:({field:l})=>e.jsxs(b,{children:[e.jsx(C,{children:r.get("ui_fertilizing_duration")}),e.jsx(v,{children:e.jsx(k,{type:"number",...l})}),e.jsx(L,{children:r.get("ui_fertilizing_duration_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:a.control,name:"progressDurations.harvesting",render:({field:l})=>e.jsxs(b,{children:[e.jsx(C,{children:r.get("ui_harvesting_duration")}),e.jsx(v,{children:e.jsx(k,{type:"number",...l})}),e.jsx(L,{children:r.get("ui_harvesting_duration_desc")}),e.jsx(O,{})]})})]}),e.jsx(os,{}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-lg font-bold",children:r.get("ui_webhook_configuration")}),e.jsx("p",{className:"text-sm text-muted-foreground",children:r.get("ui_webhook_configuration_desc")})]}),e.jsx(N,{control:a.control,name:"webhook",render:({field:l})=>e.jsxs(b,{children:[e.jsx(C,{children:r.get("ui_discord_webhook_url")}),e.jsx(v,{children:e.jsx(k,{...l,placeholder:"https://discord.com/api/webhooks/..."})}),e.jsx(L,{children:r.get("ui_discord_webhook_url_desc")}),e.jsx(O,{})]})}),e.jsxs(D,{type:"submit",className:"max-w-xs flex items-center justify-center",disabled:t||!a.formState.isDirty,children:[t&&e.jsx(Es,{className:"mr-2 h-4 w-4 animate-spin"}),r.get("ui_save_changes")]})]})})})})})}const yl=q({model:B().min(1,"ui_error_required"),item:B().min(1,"ui_error_required"),range:I,headingOffset:I,ambient:xe(),target:xe().optional()}),In={model:"",item:"",range:10,headingOffset:0,ambient:!1,target:void 0};function wl({isOpen:s,setIsOpen:n,editingLamp:t,onSubmit:i}){const r=Te({resolver:Pe(yl),defaultValues:t||In}),a=W();o.useEffect(()=>{t?r.reset(t):r.reset(In)},[t,r]);function x(l){i(l),n(!1)}return e.jsx(Ee,{open:s,onOpenChange:n,children:e.jsxs(ve,{"aria-describedby":void 0,className:"min-w-fit",children:[e.jsx(Ne,{children:e.jsx(ye,{children:t?a.get("ui_edit_lamp"):a.get("ui_add_lamp")})}),e.jsx(Le,{...r,children:e.jsxs("form",{onSubmit:r.handleSubmit(x,$e),className:"h-fit py-4 w-[40rem]",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex space-x-4",children:[e.jsx(N,{control:r.control,name:"model",render:({field:l})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_model")}),e.jsx(v,{children:e.jsx(k,{...l})}),e.jsx(L,{children:a.get("ui_model_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:r.control,name:"item",render:({field:l})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_item")}),e.jsx(v,{children:e.jsx(k,{...l})}),e.jsx(L,{children:a.get("ui_item_desc")}),e.jsx(O,{})]})})]}),e.jsxs("div",{className:"flex space-x-4 items-center",children:[e.jsx(N,{control:r.control,name:"range",render:({field:l})=>e.jsxs(b,{className:"flex-1",children:[e.jsx(C,{children:a.get("ui_range")}),e.jsx(v,{children:e.jsx(k,{type:"number",...l})}),e.jsx(L,{children:a.get("ui_lamp_range_desc")}),e.jsx(O,{})]})}),e.jsx(N,{control:r.control,name:"headingOffset",render:({field:l})=>e.jsxs(b,{className:"flex-1",children:[e.jsxs(C,{children:[a.get("ui_heading_offset"),": ",l.value]}),e.jsx(v,{children:e.jsx(pe,{value:[l.value],min:0,max:360,step:1,onValueChange:u=>l.onChange(u[0]),disabled:r.watch("ambient")})}),e.jsx(L,{children:a.get("ui_heading_offset_desc")}),e.jsx(O,{})]})})]}),e.jsx(ds,{name:"target",description:a.get("ui_target_desc")}),e.jsx(N,{control:r.control,name:"ambient",render:({field:l})=>e.jsxs(b,{className:"flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4",children:[e.jsx(v,{children:e.jsx(ge,{checked:l.value,onCheckedChange:l.onChange})}),e.jsxs("div",{className:"space-y-1 leading-none",children:[e.jsx(C,{children:a.get("ui_ambient")}),e.jsx(L,{children:a.get("ui_ambient_desc")})]})]})})]}),e.jsx(D,{type:"submit",className:"w-full mt-4",children:t?a.get("ui_save_changes"):a.get("ui_add_lamp")})]})})]})})}function Cl(){const{serverConfig:s,updateLamp:n,updateLamps:t}=ie(),[i,r]=o.useState((s==null?void 0:s.lamps)||[]),[a,x]=o.useState(!1),[l,u]=o.useState(null),[_,m]=o.useState(null),[j,h]=o.useState(1),M=6,[R,E]=o.useState(""),[z]=Ge(R,300),p=Xe(),S=W(),w=c=>{x(c),setTimeout(()=>{c||(u(null),m(null))},200)};o.useEffect(()=>{s&&r(s.lamps||[])},[s]);const y=c=>{if(s){const d=[...i,c];r(d),t(d)}x(!1)},A=c=>{if(l&&_!==null){const d=i.map((f,F)=>F===_?c:f);r(d),n(_,c),x(!1),setTimeout(()=>{u(null),m(null)},500)}},G=async c=>{if(await p(S.get("ui_delete_lamp"),S.get("ui_delete_lamp_desc"))){const d=i.filter((f,F)=>F!==c);r(d),t(d),c===(j-1)*M&&j>1&&c===i.length-1&&h(j-1)}},H=c=>{if(s){const d={...c},f=[...i,d];r(f),t(f);const F=Math.ceil(f.length/M);h(F)}},U=o.useMemo(()=>i.filter(c=>{var d,f;return((d=c.model)==null?void 0:d.toLowerCase().includes(z.toLowerCase()))||((f=c.item)==null?void 0:f.toLowerCase().includes(z.toLowerCase()))}),[i,z]),V=o.useMemo(()=>{const c=(j-1)*M;return U.slice(c,c+M)},[U,j]),Z=Math.ceil(U.length/M),g=o.useCallback(c=>{E(c.target.value),h(1)},[]);return e.jsxs("div",{className:"space-y-4 flex flex-col h-full",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx(k,{placeholder:S.get("ui_search_lamps"),value:R,onChange:g,className:"max-w-sm"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(D,{onClick:()=>x(!0),children:[e.jsx(je,{className:"mr-2 h-4 w-4"}),S.get("ui_add_lamp")]}),e.jsx(wl,{isOpen:a,editingLamp:l||void 0,setIsOpen:w,onSubmit:l?A:y})]})]}),e.jsx("div",{className:"grow",children:e.jsxs(se,{className:"table-fixed",children:[e.jsx(ne,{children:e.jsxs(K,{children:[e.jsx(P,{className:"w-12",children:S.get("ui_id")}),e.jsx(P,{children:S.get("ui_model")}),e.jsx(P,{children:S.get("ui_item")}),e.jsx(P,{children:S.get("ui_range")}),e.jsx(P,{children:S.get("ui_actions")})]})}),e.jsx(te,{children:V.length===0?e.jsx(K,{className:"hover:bg-background",children:e.jsx($,{colSpan:5,className:"h-full hover:bg-background",children:e.jsx(ns,{searchTerm:z})})}):V.map((c,d)=>{const f=(j-1)*M+d;return e.jsxs(K,{children:[e.jsx($,{children:f+1}),e.jsx($,{children:c.model}),e.jsx($,{children:c.item}),e.jsx($,{children:c.range}),e.jsx($,{children:e.jsxs(Qe,{children:[e.jsx(es,{asChild:!0,children:e.jsxs(D,{variant:"ghost",className:"h-8 w-8 p-0",children:[e.jsx("span",{className:"sr-only",children:"Open menu"}),e.jsx(Ke,{className:"h-4 w-4"})]})}),e.jsxs(Me,{align:"end",children:[e.jsxs(ae,{onClick:()=>{u(c),m(f),x(!0)},children:[e.jsx(Be,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:S.get("ui_edit")})]}),e.jsx(ss,{getContent:()=>c,onDuplicate:H,isDropdown:!0}),e.jsxs(ae,{className:"text-red-500",onClick:()=>G(f),children:[e.jsx(Ye,{className:"mr-2 h-4 w-4"}),e.jsx("span",{children:S.get("ui_delete")})]})]})]})})]},f)})})]})}),e.jsxs("div",{className:"flex justify-end items-center space-x-2",children:[e.jsx(D,{onClick:()=>h(c=>Math.max(c-1,1)),disabled:j===1,variant:"outline",children:e.jsx(We,{className:"h-4 w-4"})}),e.jsx("div",{className:"flex items-center space-x-2",children:[...Array(Z)].map((c,d)=>{const f=d+1;return f===1||f===Z||f>=j-1&&f<=j+1?e.jsx(D,{variant:j===f?"outline":"ghost",size:"icon",onClick:()=>h(f),children:f},d):f===j-2||f===j+2?e.jsx("span",{children:"..."},d):null})}),e.jsx(D,{onClick:()=>h(c=>Math.min(c+1,Z)),disabled:j===Z||Z===0,variant:"outline",children:e.jsx(Ae,{className:"h-4 w-4"})})]})]})}const Sl=wr([{path:"/",element:e.jsx(Ir,{children:e.jsx(Ar,{children:e.jsx(Lr,{})})}),children:[{path:"/",element:e.jsx(gl,{})},{path:"/general",element:e.jsx(Nl,{})},{path:"/harvesting-zones",element:e.jsx(ti,{})},{path:"/processing-zones",element:e.jsx(ui,{})},{path:"/plants",element:e.jsx(pi,{})},{path:"/plant-lights",element:e.jsx(Cl,{})},{path:"/laboratories",element:e.jsx(wi,{})},{path:"/processing-tables",element:e.jsx(Ei,{})},{path:"/suppliers",element:e.jsx(Fi,{})},{path:"/consumables",element:e.jsx(Hi,{})},{path:"/pocket-processing",element:e.jsx(Ki,{})},{path:"/retail-selling",element:e.jsx(tl,{})},{path:"/wholesale-selling",element:e.jsx(ll,{})}]}]);Cr.createRoot(document.getElementById("root")).render(e.jsx(Vn.StrictMode,{children:e.jsx(Sr,{router:Sl})}));
