<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Battle Royale Leaderboard</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      background-color: #0e0e0e;
      color: white;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      width: 100%;
      height: 100%;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  
    .leaderboard-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
      text-align: center;
      width: 100%;
      height: 95vh;
      padding: 10px;
    }
  
    h1 {
      color: #00ff11;
      text-shadow: 0 0 10px #00ff11;
      margin-bottom: 6px;
      font-size: 1.5em;
      text-transform: uppercase;
      letter-spacing: 1px;
    }
  
    #leaderboard {
      width: 95%;
      max-width: 1100px;
      background: linear-gradient(145deg, #1b1b1b, #141414);
      padding: 10px;
      border-radius: 10px;
      box-shadow: 0 0 20px rgba(21, 255, 0, 0.3);
      overflow-y: auto;
      max-height: 70vh;
    }
  
    table {
      width: 100%;
      font-size: 0.72em;
      text-align: center;
      border-collapse: collapse;
    }
  
    thead th {
      padding: 4px;
      background-color: rgba(255,255,255,0.1);
      color: #00ff11;
      font-weight: bold;
      border-bottom: 2px solid #00ff11;
    }
  
    tbody td {
      padding: 4px;
      background-color: rgba(255,255,255,0.03);
      border-bottom: 1px solid rgba(255,255,255,0.05);
    }
  
    tbody tr:hover {
      background-color: rgba(255, 204, 0, 0.1);
      transition: 0.2s;
    }

    ::-webkit-scrollbar {
          width: 10px;
      }

      ::-webkit-scrollbar-track {
          background: #1b1b1b; 
          border-radius: 10px; 
      }

      ::-webkit-scrollbar-thumb {
          background: #1b1b1b; 
          border-radius: 10px; 
      }

      ::-webkit-scrollbar-thumb:hover {
          background: #1b1b1b; 
      }


  </style>
  
  
</head>
<body>
  <div class="leaderboard-container">
    <h1>Battle Royale Leaderboard</h1>
    <div id="leaderboard">
      <table>
        <thead>
          <tr>
            <th>Rank</th>
            <th>Player</th>
            <th>Kills</th>
            <th>Deaths</th>
            <th>K/D</th>
            <th>Wins</th>
            <th>Losses</th> <!-- renamed from W/L -->
            <th>W/L</th>     <!-- new actual win/loss ratio -->
            <th>Mode</th>
          </tr>
        </thead>
        <tbody id="leaderboard-body">
          <!-- Populated dynamically -->
        </tbody>
      </table>
    </div>
  </div>

  <script>
    function calcRatio(a, b) {
      if (b === 0) return a > 0 ? a.toFixed(2) : "0.00";
      return (a / b).toFixed(2);
    }

    function renderLeaderboard(data) {
      const tbody = document.getElementById("leaderboard-body");
      tbody.innerHTML = "";

      data.forEach(player => {
        const kd = calcRatio(player.kills, player.deaths);
        const wl = calcRatio(player.wins, player.losses);

        const modeMap = {
          1: "Solo's",
          2: "Duo's",
          3: "Trio's",
          4: "Quads's"
        };
        const modeLabel = player.type ? (modeMap[player.type] || `Type ${player.type}`) : "-";

        const row = document.createElement("tr");
        row.innerHTML = `
          <td>${player.rank}</td>
          <td>${player.name}</td>
          <td>${player.kills}</td>
          <td>${player.deaths}</td>
          <td>${kd}</td>
          <td>${player.wins}</td>
          <td>${player.losses}</td>
          <td>${wl}</td>
          <td>${modeLabel}</td>
        `;

        tbody.appendChild(row);
      });
    }


    window.onload = () => {
      fetch(`https://pug-battleroyale/getLeaderboardData`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({})
      })
      .then(res => res.json())
      .then(data => {
        renderLeaderboard(data);
      });
    };
  </script>
</body>
</html>
