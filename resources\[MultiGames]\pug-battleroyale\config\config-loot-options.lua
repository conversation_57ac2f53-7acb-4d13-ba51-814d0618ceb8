-- all loot options in the royale
Config.RoyaleLoot = {
    -- ammo:
    ["rifle_ammo"] = { model = "prop_ld_ammo_pack_02", name = "rifle_ammo_royale", type = "AMMO_RIFLE", lable = "Rifle Ammo", amount = 30, pickup = true},
    ["smg_ammo"] = { model = "prop_ld_ammo_pack_01", name = "smg_ammo_royale", type = "AMMO_SMG", lable = "SMG Ammo", amount = 30, pickup = true},
    ["pistol_ammo"] = { model = "w_pi_pistol50_mag2", name = "pistol_ammo_royale", type = "AMMO_PISTOL", lable = "Pistol Ammo", amount = 12, pickup = true},
    ["shotgun_ammo"] = { model = "w_sg_heavyshotgun_boxmag", name = "shotgun_ammo_royale", type = "AMMO_SHOTGUN", lable = "Shotgun Ammo", amount = 5, pickup = true},
    ["mg_ammo"] = { model = "w_mg_mg_mag2", name = "mg_ammo_royale", type = "AMMO_MG", lable = "MG Ammo", amount = 30, pickup = true},
    ["snp_ammo"] = { model = "prop_box_ammo07b", name = "snp_ammo_royale", type = "AMMO_SNIPER", lable = "Sniper Ammo", amount = 5, pickup = true},
    
    -- Items
    ["armorroyale_royale"] = { model = "prop_armour_pickup", name = "armorroyale_royale", lable = "Armor", pickup = true, item = true},
    ["healthroyale_royale"] = { model = "prop_ld_health_pack", name = "healthroyale_royale", lable = "Health Kit", pickup = true, item = true},
    ["uav_royale"] = { model = "xm_prop_x17_tablet_01", name = "uav_royale", lable = "UAV", pickup = true, item = true},
    ["armor1_royale"] = { model = "prop_bodyarmour_04", name = "armor1_royale", lable = "Light Armor", pickup = true, item = true},
    ["armor2_royale"] = { model = "prop_bodyarmour_05", name = "armor2_royale", lable = "Medium Armor", pickup = true, item = true},
    ["armor3_royale"] = { model = "prop_bodyarmour_03", name = "armor3_royale", lable = "Heavy Armor", pickup = true, item = true},
    ["jump_royale"] = { model = "prop_old_boot", name = "jump_royale", lable = "Super Jump", pickup = true, item = true},
    ["juice_royale"] = { model = "prop_food_bs_juice01", name = "juice_royale", lable = "Health Juice", pickup = true, item = true},
    -- Pistols:
    ["weapon_pistol"] = { model = "w_pi_pistol", name = "weapon_pistol_royale", lable = "Beretta M9"},
    ["weapon_combatpistol"] = { model = "w_pi_combatpistol", name = "weapon_combatpistol_royale", lable = "Combat Pistol"},
    ["weapon_heavypistol"] = { model = "w_pi_heavypistol", name = "weapon_heavypistol_royale", lable = "Heavy Pistol"},
    ["weapon_appistol"] = { model = "w_pi_appistol", name = "weapon_appistol_royale", lable = "AP-Pistol"},
    ["weapon_snspistol"] = { model = "w_pi_sns_pistol", name = "weapon_snspistol_royale", lable = "SNS Pistol"},
    ["weapon_pistol50"] = { model = "w_pi_pistol50", name = "weapon_pistol50_royale", lable = "Desert Eagle"},
    ["weapon_vintagepistol"] = { model = "w_pi_vintage_pistol", name = "weapon_vintagepistol_royale", lable = "Vintage Pistol"},
    -- assault rifles:
    ["weapon_carbinerifle"] = { model = "w_ar_carbinerifle", name = "weapon_carbinerifle_royale", lable = "Carbine Rifle"},
    ["weapon_compactrifle"] = { model = "w_ar_assaultrifle_smg", name = "weapon_compactrifle_royale", lable = "Draco"},
    ["weapon_carbinerifle_mk2"] = { model = "w_ar_carbineriflemk2", name = "weapon_carbinerifle_mk2_royale", lable = "Carbine RifleMK2"},
    ["weapon_assaultrifle"] = { model = "w_ar_assaultrifle", name = "weapon_assaultrifle_royale", lable = "AK47"},
    ["weapon_assaultrifle_mk2"] = { model = "w_ar_assaultriflemk2", name = "weapon_assaultrifle_mk2_royale", lable = "AK47 MK2"},
    ["weapon_specialcarbine"] = { model = "w_ar_specialcarbine", name = "weapon_specialcarbine_royale", lable = "Special Carbine"},
    ["weapon_specialcarbine_mk2"] = { model = "w_ar_specialcarbinemk2", name = "weapon_specialcarbine_mk2_royale", lable = "Special Carbine MK2"},
    ["weapon_bullpuprifle"] = { model = "w_ar_bullpuprifle", name = "weapon_bullpuprifle_royale", lable = "Bullpup Rifle"},
    ["weapon_bullpuprifle_mk2"] = { model = "w_ar_bullpupriflemk2", name = "weapon_bullpuprifle_mk2_royale", lable = "Bullpup Rifle MK2"},
    ["weapon_advancedrifle"] = { model = "w_ar_advancedrifle", name = "weapon_advancedrifle_royale", lable = "Advanced Rifle"},
    ["weapon_militaryrifle"] = { model = "w_ar_militaryrifle", name = "weapon_militaryrifle_royale", lable = "Military Rifle"},
    -- sub machine guns:
    ["weapon_microsmg"] = { model = "w_sb_microsmg", name = "weapon_microsmg_royale", lable = "UZI"},
    ["weapon_assaultsmg"] = { model = "w_sb_assaultsmg", name = "weapon_assaultsmg_royale", lable = "Assault SMG"},
    ["weapon_smg"] = { model = "w_sb_smg", name = "weapon_smg_royale", lable = "SMG"},
    ["weapon_combatpdw"] = { model = "w_sb_combatpdw", name = "weapon_combatpdw_royale", lable = "Combat PDW"},
    ["weapon_smg_mk2"] = { model = "w_sb_smgmk2", name = "weapon_smg_mk2_royale", lable = "SMG MK2"},
    ["weapon_gusenberg"] = { model = "w_sb_gusenberg", name = "weapon_gusenberg_royale", lable = "Tommy Gun"},
    -- MG guns:
    ["weapon_mg"] = { model = "w_mg_mg", name = "weapon_mg_royale", lable = "LMG"},
    ["weapon_combatmg"] = { model = "w_mg_combatmg", name = "weapon_combatmg_royale", lable = "Comat LMG"},
    ["weapon_minigun"] = { model = "w_mg_minigun", name = "weapon_minigun_royale", lable = "Minigun"},
    -- sniper rifles:
    ["weapon_sniperrifle"] = { model = "w_sr_sniperrifle", name = "weapon_sniperrifle_royale", lable = "Sniper Rifle"},
    ["weapon_marksmanrifle"] = { model = "w_sr_marksmanrifle", name = "weapon_marksmanrifle_royale", lable = "Marksman Rifle"},
    ["weapon_heavysniper"] = { model = "w_sr_heavysniper", name = "weapon_heavysniper_royale", lable = "Heavy Sniper"},
    -- shotguns:
    ["weapon_sawnoffshotgun"] = { model = "w_sg_sawnoff", name = "weapon_sawnoffshotgun_royale", lable = "Sawnoff Shotgun"},
    ["weapon_assaultshotgun"] = { model = "w_sg_assaultshotgun", name = "weapon_assaultshotgun_royale", lable = "Assault Shotgun"},
    ["weapon_bullpupshotgun"] = { model = "w_sg_bullpupshotgun", name = "weapon_bullpupshotgun_royale", lable = "Bullpup Shotgun"},
    ["weapon_pumpshotgun"] = { model = "w_sg_pumpshotgun", name = "weapon_pumpshotgun_royale", lable = "Pump Shotgun"},
    ["weapon_musket"] = { model = "w_ar_musket", name = "weapon_musket", lable = "Musket"},
    ["weapon_heavyshotgun"] = { model = "w_sg_heavyshotgun", name = "weapon_heavyshotgun_royale", lable = "Heavy Shotgun"},
    -- Bigger:
    ["weapon_rpg"] = { model = "w_lr_rpg", name = "weapon_rpg_royale", lable = "RPG", pickup = true, bigger = true},
    ["weapon_grenade"] = { model = "w_ex_grenadefrag", name = "weapon_grenade_royale", lable = "Frag Grenade", pickup = true, bigger = true},
    ["weapon_molotov"] = { model = "w_ex_molotov", name = "weapon_molotov_royale", lable = "Molotov", pickup = true, bigger = true},
    ["weapon_stickybomb"] = { model = "w_ex_pe", name = "weapon_stickybomb_royale", lable = "Stickybomb", pickup = true, bigger = true},
    ["weapon_grenadelauncher"] = { model = "w_lr_grenadelauncher", name = "weapon_grenadelauncher_royale", lable = "Grenade Launcher", pickup = true, bigger = true},
    ["weapon_pipebomb"] = { model = "prop_bomb_01", name = "weapon_pipebomb_royale", lable = "Pipe Bomb", pickup = true, bigger = true},
    -- mele weapons
    ["weapon_machete"] = { model = "w_me_machette_lr", name = "weapon_machete_royale", lable = "Machete"},
    ["weapon_bat"] = { model = "w_me_bat", name = "weapon_bat_royale", lable = "Bat"},
    ["weapon_bottle"] = { model = "w_me_bottle", name = "weapon_bottle_royale", lable = "Broken Bottle"},
    ["weapon_crowbar"] = { model = "w_me_crowbar", name = "weapon_crowbar_royale", lable = "Crowbar"},
    ["weapon_dagger"] = { model = "w_me_dagger", name = "weapon_dagger_royale", lable = "Dagger"},
    ["weapon_hammer"] = { model = "w_me_hammer", name = "weapon_hammer_royale", lable = "Hammer"},
}

-- loot option
Config.GunOptions = { -- This table is what ACTUALLY spawns the in world 3D models like the armor, medkit, weapon models etc. 
    [1] = { -- Rifle Ammo
        ammo = "prop_ld_ammo_pack_02", -- ammo type
        amount = 15, -- this number does not matter (ignore)
        [1] = "w_ar_assaultrifle",
        [2] = "w_ar_assaultriflemk2",
        [3] = "w_ar_advancedrifle",
        [4] = "w_ar_carbinerifle",
        [5] = "w_ar_carbineriflemk2",
        [6] = "w_ar_advancedrifle",
    },
    [2] = { -- Rifle Ammo
        ammo = "prop_ld_ammo_pack_02", -- ammo type
        amount = 15, -- this number does not matter (ignore)
        [1] = "w_ar_specialcarbine",
        [2] = "w_ar_bullpuprifle",
        [3] = "w_ar_assaultrifle_smg",
        [4] = "w_ar_specialcarbinemk2",
        [5] = "w_ar_bullpupriflemk2",
        [6] = "w_ar_militaryrifle",
    },
    [3] = { -- SMG Ammo
        ammo = "prop_ld_ammo_pack_01", -- ammo type
        amount = 15, -- this number does not matter (ignore)
        [1] = "w_sb_microsmg",
        [2] = "w_sb_smg",
        [3] = "w_sb_smgmk2",
        [4] = "w_sb_assaultsmg",
        [5] = "w_sb_microsmg",
        [6] = "w_sb_microsmg",
    },
    [4] = { -- Pistol Ammo
        ammo = "w_pi_pistol50_mag2", -- ammo type
        amount = 5, -- this number does not matter (ignore)
        [1] = "w_pi_pistol",
        [2] = "w_pi_heavypistol",
        [3] = "w_pi_combatpistol",
        [4] = "w_pi_appistol",
        [5] = "w_pi_sns_pistol",
        [6] = "w_pi_pistol50",
    },
    [5] = { -- Pistol Ammo
        ammo = "w_pi_pistol50_mag2", -- ammo type
        amount = 5, -- this number does not matter (ignore)
        [1] = "w_pi_pistol",
        [2] = "w_pi_vintage_pistol",
        [3] = "w_pi_combatpistol",
        [4] = "w_pi_appistol",
        [5] = "w_pi_appistol",
        [6] = "w_pi_pistol50",
    },
    [6] = { -- Shotgun Ammo
        ammo = "w_sg_heavyshotgun_boxmag", -- ammo type
        amount = 2, -- this number does not matter (ignore)
        [1] = "w_sg_sawnoff",
        [2] = "w_sg_assaultshotgun",
        [3] = "w_sg_bullpupshotgun",
        [4] = "w_sg_pumpshotgun",
        [5] = "w_ar_musket",
        [6] = "w_sg_heavyshotgun",
    },
    [7] = { -- MG Ammo
        ammo = "w_mg_mg_mag2", -- ammo type
        amount = 15, -- this number does not matter (ignore)
        [1] = "w_mg_mg",
        [2] = "w_mg_combatmg",
        [3] = "w_mg_mg",
        [4] = "w_mg_combatmg",
        [5] = "w_mg_mg",
        [6] = "w_sb_gusenberg",
    },
    [8] = { -- SNP/Sniper Ammo
        ammo = "prop_box_ammo07b", -- ammo type
        amount = 1, -- this number does not matter (ignore)
        [1] = "w_sr_marksmanrifle",
        [2] = "w_sr_heavysniper",
        [3] = "w_sr_marksmanrifle",
        [4] = "w_sr_sniperrifle",
        [5] = "w_sr_sniperrifle",
        [6] = "w_sr_heavysniper",
    },


    [9] = { -- ammo is setup as a placeholder for these single items
        ammo = "PICKUP_AMMO_SNIPER", -- ammo type
        amount = 'single', -- used to define loot
        [1] = "w_lr_rpg",
        [2] = "w_ex_grenadefrag",
        [3] = "w_ex_molotov",
        [4] = "w_ex_pe",
        [5] = "w_lr_grenadelauncher",
        [6] = "prop_bomb_01",
    },
    [10] = {
        ammo = "PICKUP_AMMO_SNIPER", -- ammo type
        amount = 'single', -- used to define loot
        [1] = "prop_armour_pickup",
        [2] = "prop_armour_pickup",
        [3] = "prop_armour_pickup",
        [4] = "prop_ld_health_pack",
        [5] = "prop_ld_health_pack",
        [6] = "prop_ld_health_pack",
    },
    [11] = {
        ammo = "PICKUP_AMMO_SNIPER", -- ammo type
        amount = 'single', -- used to define loot
        [1] = "w_me_dagger",
        [2] = "w_me_bat",
        [3] = "w_me_bottle",
        [4] = "w_me_crowbar",
        [5] = "w_me_hammer",
        [6] = "w_me_machette_lr",
    },
    [12] = {
        ammo = "PICKUP_AMMO_SNIPER", -- ammo type
        amount = 'single', -- used to define loot
        [1] = "prop_ld_ammo_pack_02",
        [2] = "prop_ld_ammo_pack_01",
        [3] = "w_pi_pistol50_mag2",
        [4] = "w_sg_heavyshotgun_boxmag",
        [5] = "w_mg_mg_mag2",
        [6] = "prop_box_ammo07b",
    },
    [13] = {
        ammo = "PICKUP_AMMO_SNIPER", -- ammo type
        amount = 'single', -- used to define loot
        [1] = "prop_armour_pickup",
        [2] = "prop_armour_pickup",
        [3] = "prop_armour_pickup",
        [4] = "prop_ld_health_pack",
        [5] = "prop_ld_health_pack",
        [6] = "xm_prop_x17_tablet_01",
    },
    [14] = {
        ammo = "PICKUP_AMMO_SNIPER", -- ammo type
        amount = 'single', -- used to define loot
        [1] = "w_me_dagger",
        [2] = "w_me_bat",
        [3] = "w_me_bottle",
        [4] = "w_me_crowbar",
        [5] = "w_me_hammer",
        [6] = "w_me_machette_lr",
    },
    [15] = {
        ammo = "PICKUP_AMMO_SNIPER", -- ammo type
        amount = 'single', -- used to define loot
        [1] = "prop_ld_ammo_pack_02",
        [2] = "prop_ld_ammo_pack_01",
        [3] = "w_pi_pistol50_mag2",
        [4] = "w_sg_heavyshotgun_boxmag",
        [5] = "w_mg_mg_mag2",
        [6] = "prop_box_ammo07b",
    },
    [16] = {
        ammo = "PICKUP_AMMO_SNIPER", -- ammo type
        amount = 'single', -- used to define loot
        [1] = "prop_bodyarmour_03",
        [2] = "prop_bodyarmour_05",
        [3] = "prop_bodyarmour_05",
        [4] = "prop_bodyarmour_04",
        [5] = "prop_bodyarmour_04",
        [6] = "prop_bodyarmour_04",
    },
    [17] = {
        ammo = "PICKUP_AMMO_SNIPER", -- ammo type
        amount = 'single', -- used to define loot
        [1] = "prop_old_boot",
        [2] = "prop_old_boot",
        [3] = "prop_food_bs_juice01",
        [4] = "prop_ld_health_pack",
        [5] = "prop_food_bs_juice01",
        [6] = "prop_food_bs_juice01",
    },
}