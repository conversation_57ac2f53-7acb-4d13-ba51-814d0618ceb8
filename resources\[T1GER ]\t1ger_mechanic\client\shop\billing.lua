if not Config.Shop.Billing.enable then 
    return 
end

---Main billing menu
---@param returnMenu? string (optional) return menu id
function BillingMain(returnMenu)
    if not Config.Shop.Billing.enable then return end

    -- check if isMechanic:
    local isMechanic, shopId = IsPlayerMechanic()
    if not isMechanic then return end

    -- check shop id and bill data
    if not Shops[shopId] or not Shops[shopId].billing then return end

    local menuOptions = {}

    -- if has bills
    if type(Shops[shopId].billing) == "table" and next(Shops[shopId].billing) then
        -- insert menu option into billing main
        menuOptions[#menuOptions + 1] = {title = locale("menu_title.billing_view"), icon = "receipt", arrow = true, menu = "view_mechanic_bills"}
        
        -- populate shopBills
        local shopBills = {}
        for billingNumber, bill in pairs(Shops[shopId].billing) do
            shopBills[#shopBills+1] = {
                title = string.format(locale("menu_title.billing_overview"), billingNumber, Config.Currency..tostring(math.groupdigits(bill.amount))),
                icon = "file-invoice-dollar",
                description = bill.note,
                metadata = {
                    {label = locale("menu_metadata.billing_reference"), value = billingNumber},
                    {label = locale("menu_metadata.billing_sender"), value = bill.sender},
                    {label = locale("menu_metadata.billing_receiver"), value = bill.receiver},
                    {label = locale("menu_metadata.billing_amount"), value = Config.Currency..tostring(math.groupdigits(bill.amount))},
                    {label = locale("menu_metadata.billing_date"), value = bill.date},
                    {label = locale("menu_metadata.billing_time"), value = bill.time},
                }
            }
        end

        -- register context menu:
        lib.registerContext({
            id = "view_mechanic_bills",
            title = locale("menu_title.billing_view"),
            menu = "mechanic_billing_menu",
            options = shopBills,
        })
    end

    -- create bill menu option:
    menuOptions[#menuOptions + 1] = {
        title = locale("menu_title.billing_create"),
        icon = "file-invoice-dollar",
        onSelect = function()
            local input = CreateBillingInput(locale("menu_title.billing_view"))
            if not input then 
                return lib.showContext("mechanic_billing_menu")
            else
                local bill = { player = input[1], amount = input[2], note = input[3], date = input[4], time = input[5] }
                TriggerServerEvent("t1ger_mechanic:server:createBill", shopId, bill)
            end
        end
    }

    -- register Billing Main Menu:
    local context = {
        id = "mechanic_billing_menu",
        title = locale("menu_title.billing_main"),
        options = menuOptions,
    }
    if returnMenu then
        context.menu = type(returnMenu) == "string" and returnMenu or "mechanic_action_menu"
    end
    lib.registerContext(context)

    -- show billing main context:
    lib.showContext("mechanic_billing_menu")
end
exports("BillingMain", BillingMain)

---Billing input dialogue to create bills
---@return input|nil billingInput returns the input data of the bill
function CreateBillingInput()
    -- get nearby players within 10.0 unit radius:
    local players = lib.getNearbyPlayers(coords, 10.0)
    if #players == 0 then
        _API.ShowNotification(locale("notification.no_players_nearby"), "inform", {})
        --return lib.showContext("mechanic_billing_menu")
    end

    -- insert players into options:
    local playerOptions = {}
    for i = 1, #players do
        local player = players[i]
        local playerSrc = GetPlayerServerId(player.id)
        playerOptions[#playerOptions+1] = {
            label = string.format("[%s] %s", playerSrc, GetPlayerName(player.id)),
            value = playerSrc,
        }
    end

    playerOptions[#playerOptions+1] = {
        label = "T1GER Scripts",
        value = 1,
    }
    
    -- create input options
    local inputOptions = {
        {type = "select", label = locale("input_label.bill_player"), description = locale("input_description.bill_player"), icon = "person", options = playerOptions, required = true},
        {type = "number", label = locale("input_label.bill_amount"), icon = "dollar-sign", required = true, min = 1},
        {type = "textarea", label = locale("input_label.bill_note"), description = locale("input_description.bill_note"), icon = "note", autosize = true, required = true},
        {type = "date", label = locale("input_label.bill_date"), icon = "calendar", default = true, disabled = true},
        {type = "time", label = locale("input_label.bill_time"), icon = "clock", format = 24, default = true, disabled = true},
    }

    -- create input
    local input = lib.inputDialog(locale("input_title.create_bill"), inputOptions)

    -- return input
    return input
end

-- Event to receive billing request on client
RegisterNetEvent("t1ger_mechanic:client:sendBill", function(shopId, bill, senderId)
    ---Function to accept the bill
    ---@param accepted boolean `true` if player accepted the bill. `false` otherwise
    local function PayBill(accepted)
        TriggerServerEvent("t1ger_mechanic:server:payBill", shopId, bill, senderId, accepted)
        if accepted then
            _API.ShowNotification(string.format(locale("notification.you_paid_bill"), Config.Currency..tostring(math.groupdigits(bill.amount)), Shops[shopId].name), "inform", {})
        else
            _API.ShowNotification(string.format(locale("notification.you_declined_bill"), Config.Currency..tostring(math.groupdigits(bill.amount)), Shops[shopId].name), "inform", {})
        end
    end

    -- create menu options for yes/no
    local menuOptions = {
        [1] = {
            title = locale("menu_title.yes"),
            icon = "check",
            onSelect = function()
                PayBill(true)
            end
        },
        [2] = {
            title = locale("menu_title.no"),
            icon = "ban",
            onSelect = function()
                PayBill(false)
            end
        },
    }

    -- display notes if added:
    if bill.note then
        menuOptions[#menuOptions+1] = {
            title = locale("menu_title.billing_note"),
            icon = "comment",
            readOnly = true,
            description = bill.note
        }
    end
    
    -- register context:
    lib.registerContext({
        id = "pay_mechanic_bill",
        title = string.format(locale("menu_title.billing_respond"), Config.Currency..tostring(math.groupdigits(bill.amount))),
        canClose = false,
        options = menuOptions
    })

    -- show context
    lib.showContext("pay_mechanic_bill")
end)

--- command to open billing main
if Config.Shop.Billing.command.enable == true then
    RegisterCommand(Config.Shop.Billing.command.name, function()
        BillingMain()
    end, false)
end