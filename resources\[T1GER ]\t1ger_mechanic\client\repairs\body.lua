local isCarryingBodyPart = false
local bodyPartProp = nil
local ptfxHandle = nil
local cancelInspection = false
local isInspecting = false

local carryAnim = {dict = "anim@heists@box_carry@", clip = "idle", blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49}

--- Returns whether the player is currently carrying a body part prop
--- @return boolean
function IsCarryingBodyPart()
    if isCarryingBodyPart or bodyPartProp ~= nil then 
        return true 
    else 
        return false 
    end
end
exports("IsCarryingBodyPart", IsCarryingBodyPart)

--- Play animation to carry the body part prop
local function CarryPropAnimation()
    lib.requestAnimDict(carryAnim.dict)
    TaskPlayAnim(player, carryAnim.dict, carryAnim.clip , carryAnim.blendIn, carryAnim.blendOut, carryAnim.duration, carryAnim.flags, 0, false, false, false)
end

--- Carry the body part prop: play anim and attach prop to player
--- @param prop table Prop data
local function CarryBodyPartProp(prop)
    -- carry prop anim:
    CarryPropAnimation(prop)
    Wait(250)

    bodyPartProp = CreateProp(prop.model, coords)

    -- attach prop to player:
    local boneIndex = GetPedBoneIndex(player, prop.boneId)
    AttachEntityToEntity(bodyPartProp, player, boneIndex, prop.pos.x, prop.pos.y, prop.pos.z, prop.rot.x, prop.rot.y, prop.rot.z, true, true, false, true, 1, true)
end

--- Returns a table of all damaged or missing vehicle body parts grouped by type.
--- The top-level keys in the returned table match part names used in your item/config system: 
--- "door", "hood", "trunk", "wheel", "window".
--- Each key maps to a table indexed by the part’s native index (doorIndex, wheelIndex, or windowIndex).
--- Hood and trunk are included under their own keys and are indexed by their door index (usually 4 and 5).
--- @param vehicle number The vehicle entity handle.
--- @return table #Damaged parts grouped by part type and indexed by part index as string.
local function GetVehicleDamagedBodyParts(vehicle)
    if not vehicle or not DoesEntityExist(vehicle) then return {} end

    local damagedParts = {}

    local doors = GetVehicleDoors(vehicle)
    for doorIndex, doorData in pairs(doors) do
        if doorData.isDamaged or doorData.isMissing then
            local bodyType = "door"
            if doorData.index == 4 then
                bodyType = "hood"
            elseif doorData.index == 5 then
                bodyType = "trunk"
            end

            damagedParts[bodyType] = damagedParts[bodyType] or {}
            damagedParts[bodyType][doorIndex] = doorData
        end
    end

    local wheels = GetVehicleWheels(vehicle)
    for wheelIndex, wheelData in pairs(wheels) do
        if wheelData and (wheelData.isDamaged or wheelData.isMissing) then
            damagedParts.wheel = damagedParts.wheel or {}
            damagedParts.wheel[wheelIndex] = wheelData
        end
    end

    local windows = GetVehicleWindows(vehicle)
    for windowIndex, windowData in pairs(windows) do
        if windowData and (windowData.isDamaged or windowData.isMissing) then
            damagedParts.window = damagedParts.window or {}
            damagedParts.window[windowIndex] = windowData
        end
    end

    damagedParts.bodyHealth = GetVehicleBodyHealth(vehicle)
    damagedParts.engineHealth = GetVehicleEngineHealth(vehicle)

    return damagedParts
end

--- Returns whether damagedParts table has any entries
--- @param damagedParts table The table for damaged parts from the vehicle
--- @param filter? string Optional filter param to check for specific body part type: "door", "hood", "trunk", "wheel" or "window"
--- @return boolean
local function HasDamagedParts(damagedParts, filter)
    if type(damagedParts) ~= "table" or next(damagedParts) == nil then
        return false
    end

    if type(filter) == "string" and type(damagedParts[filter]) == "table" then
        return next(damagedParts[filter]) ~= nil
    end

    for bodyType, parts in pairs(damagedParts) do
        if type(parts) == "table" and next(parts) then
            return true
        end
    end

    return false
end

--- Returns whether player can use window part item
--- @param vehicle number The vehicle entity handle
--- @param damagedWindows table The damaged windows indexed by windowIndex
--- @return boolean
local function CanUseWindowPartItem(vehicle, damagedWindows)
    if not vehicle or not DoesEntityExist(vehicle) then
        return false
    end

    if type(damagedWindows) ~= "table" or next(damagedWindows) == nil then
        return false
    end

    local doors = GetVehicleDoors(vehicle)

    for windowIndex, windowData in pairs(damagedWindows) do
        local correspondingDoor = doors[windowIndex]

        if correspondingDoor then
            if not correspondingDoor.isMissing then
                return true -- Door exists and is intact
            end
        else
            return true -- No door exists (e.g. windshields or rear windows on 2-door)
        end
    end

    return false -- All windows either had missing doors or none passed validation
end

--- @param vehicle number The vehicle entity handle
--- @param bodyType string The type of body part: "door", "hood", "trunk", "wheel" or "window"
--- @param closestBodyPart table The closest body part data (label, boneName, boneIndex, index, wheelId etc.)
local function SetBodyPartFixed(vehicle, bodyType, closestBodyPart)
    local vehState = Entity(vehicle).state
    local damagedParts = vehState["t1ger_mechanic:damagedParts"]
    local workflowTasks = vehState["t1ger_mechanic:workflowTasks"]

    -- update task if matched
    local taskUpdated = false
    for taskId, task in pairs(workflowTasks or {}) do
        if task.type == bodyType and task.index == closestBodyPart.index then
            UpdateWorkflowTask(taskId, true)
            task.completed = true
            taskUpdated = true
        end
    end
    if taskUpdated then
        vehState:set("t1ger_mechanic:workflowTasks", workflowTasks, true)
    end
    
    -- clear from damaged parts
    if damagedParts and damagedParts[bodyType] then
        damagedParts[bodyType][tostring(closestBodyPart.index)] = nil
        vehState:set("t1ger_mechanic:damagedParts", damagedParts, true)
    end
    
    -- full repair = reset health + clean workflowTasks
    if not HasDamagedParts(damagedParts) then
        damagedParts.bodyHealth = 1000.0
        if Config.BodyRepair.FixEngineHealth then
            damagedParts.engineHealth = 1000.0
        end
        if workflowTasks then
            vehState:set("t1ger_mechanic:workflowTasks", nil, true)
        end
        _API.ShowNotification(locale("notification.body_repair_complete"), "success", {})
    end
    
    -- refresh vehicle body damage
    vehState:set("t1ger_mechanic:refreshVehicleBody", {
        damagedParts = damagedParts,
        health = damagedParts.bodyHealth,
        engineHealth = damagedParts.engineHealth,
        wheelId = closestBodyPart.wheelId or nil
    }, true)
end

--- Creates target on given vehicle for the repair of the body part
--- @param vehicle number The vehicle entity handle
--- @param bodyType string The type of body part: "door", "hood", "trunk", "wheel" or "window"
local function CreateBodyPartRepairTarget(vehicle, bodyType)

    --- Returns whether the player is carrying body part prop and is near the repair vehicle
    --- @param entity number The vehicle entity handle
    --- @return boolean
    local function IsNearRepairVehicle(entity)
        if IsEntityValid(entity) and GetEntityType(entity) ~= 0 then
            local vehCoords = GetEntityCoords(entity)
            local vehDistance = #(coords - vehCoords)
            
            if vehDistance <= 7.0 and IsCarryingBodyPart() then
                return true
            end
        end
        return false
    end

    --- Returns cloest repair point and notification message (to be used if not found) for given body part
    --- @param entity number The vehicle entity handle
    --- @param partType string 
    --- @return table?
    --- @return string
    local function FindClosestBodyRepairPoint(entity, bodyType)
        -- Define search radius per part type
        local searchRadius = { door = 1.3, hood = 0.8, trunk = 0.8, window = 1.3, wheel = 1.3 }

        -- Define corresponding function mappings
        local partFunctions = {
            door = GetVehicleClosestDoor,
            hood = GetVehicleClosestHood,
            trunk = GetVehicleClosestTrunk,
            window = GetVehicleClosestWindow,
            wheel = GetVehicleClosestWheel
        }

        -- Call the appropriate function if valid bodyType is provided
        return partFunctions[bodyType] and partFunctions[bodyType](entity, searchRadius[bodyType], true) or nil
    end

    local partConfig = Config.BodyParts[bodyType]

    -- Add target on the repair vehicle entity
    _API.Target.AddLocalEntity(vehicle, {
        {
            name = "t1ger_mechanic:repair:body",
            icon = Config.BodyRepair.TargetIcon,
            label = string.format(locale("target.body_repair"), partConfig.label),
            canInteract = IsNearRepairVehicle,
            distance =  7.0,
            onSelect = function(entity)
                local closestBodyPart = FindClosestBodyRepairPoint(entity, bodyType)
                if type(closestBodyPart) ~= "table" then
                    return _API.ShowNotification(locale("notification.body_repair_move_closer"), "inform", {})
                end

                -- Face vehicle
                TaskTurnPedToFaceCoord(player, closestBodyPart.defaultBoneCoords.x, closestBodyPart.defaultBoneCoords.y, closestBodyPart.defaultBoneCoords.z, 1000.0)
                Wait(1000)
                
                -- Animation: 
                local anim = {}
                if bodyType == "door" or bodyType == "wheel" then
                    anim = {dict = "anim@amb@clubhouse@tutorial@bkr_tut_ig3@", clip = "machinic_loop_mechandplayer", duration = -1, blendIn = 2.0, blendOut = 2.0, flag = 1}
                elseif bodyType == "hood" or bodyType == "trunk" or bodyType == "window" then
                    anim = {dict = "mini@repair", clip = "fixing_a_player", duration = -1, blendIn = 2.0, blendOut = 2.0, flag = 1}
                end

                lib.requestAnimDict(anim.dict)
                TaskPlayAnim(player, anim.dict, anim.clip, anim.blendIn, anim.blendOut, anim.duration, anim.flag, 0, 0, 0, 0)
                Wait(500)

                -- Start ptfx:
                TriggerServerEvent("t1ger_mechanic:server:bodyRepairPtfx", "core", "ent_amb_foundry_arc_heat", closestBodyPart.defaultBoneCoords, vector3(0.0,0.0,0.0), 0.5)

                -- Skillcheck
                local skillcheck = false
                if Config.BodyRepair.Skillcheck.enable then
                    skillcheck = SkillCheck(Config.BodyRepair.Skillcheck.difficulty, Config.BodyRepair.Skillcheck.inputs)
                else
                    skillcheck = true 
                end

                -- Return if not skillcheck success
                if not skillcheck then
                    TriggerServerEvent("t1ger_mechanic:server:bodyRepairPtfx")
                    ClearPedTasks(player)
                    CarryPropAnimation(partConfig.prop)
                    return
                end

                -- Progressbar
                local success = ProgressBar({
                    duration = Config.BodyRepair.Duration,
                    label = string.format(locale("progressbar.body_repair"), partConfig.label),
                    useWhileDead = false,
                    canCancel = true,
                    disable = {move = true, car = true, combat = true, sprint = true},
                })
                
                -- Return if not progressbar success
                if not success then
                    TriggerServerEvent("t1ger_mechanic:server:bodyRepairPtfx")
                    ClearPedTasks(player)
                    CarryPropAnimation(partConfig.prop)
                    return
                end

                -- Remove ptfx
                TriggerServerEvent("t1ger_mechanic:server:bodyRepairPtfx")

                -- Remove Target
                _API.Target.RemoveLocalEntity(entity, {names = {"t1ger_mechanic:repair:body"}, labels = {string.format(locale("target.body_repair"), partConfig.label)}})

                -- Remove item
                TriggerServerEvent("t1ger_mechanic:server:removeItem", partConfig.item, 1)

                -- Set body part fixed if success
                if success then
                    local ptfxData = {dict = "core", name = "sp_foundry_sparks"}
                    lib.requestNamedPtfxAsset(ptfxData.dict)
                    UseParticleFxAsset(ptfxData.dict)
                    local ptfx = StartNetworkedParticleFxNonLoopedAtCoord(ptfxData.name, closestBodyPart.defaultBoneCoords.x, closestBodyPart.defaultBoneCoords.y, closestBodyPart.defaultBoneCoords.z, 0.0, 0.0, 0.0, 0.5, 0, 0, 0)
                    
                    SetBodyPartFixed(entity, bodyType, closestBodyPart)
                end

                -- Play repair sound
                RepairSound()

                -- Delete prop and clear tasks
                DeleteEntity(bodyPartProp)
                ClearPedTasks(player)

                -- Cleanup
                isCarryingBodyPart = false
                bodyPartProp = nil
            end
        }
    })
end

--- Function to trigger the useable item for the given body part
--- @param bodyType string The type of the used body part item ("door", "hood", "trunk", "wheel" or "window").
local function UseBodyPartItem(bodyType)
    -- Only allow non-mechanics to use wheel repairs if allowed by config
    if not IsPlayerMechanic() then
        if bodyType == "wheel" and Config.BodyRepair.AllowEveryoneForWheelRepair then
            -- continue for wheel repair
        else
            return -- block use for non-mechanics
        end
    end
    
    -- Check if the player is already carrying a body part prop
    if IsCarryingBodyPart() then
        return _API.ShowNotification(locale("notification.is_carrying_body_part"), "inform", {})
    end

    -- Get the closest vehicle
    local vehicle, vehicleDist = lib.getClosestVehicle(coords, 5.0, false)
	if not vehicle or not DoesEntityExist(vehicle) or not IsEntityAVehicle(vehicle) then
        return _API.ShowNotification(locale("notification.no_vehicle_nearby"), "inform", {})
    end

    local partConfig = Config.BodyParts[bodyType]

    -- Get damaged parts:
    local damagedParts = GetVehicleDamagedBodyParts(vehicle)
    if not HasDamagedParts(damagedParts) then
        return _API.ShowNotification(string.format(locale("notification.no_body_repairs_required"), partConfig.label), "inform", {})
    end

    -- If the bodyType is a 'window', ensure that at least one valid, undamaged door exists
    if bodyType == "window" and not CanUseWindowPartItem(vehicle, damagedParts[bodyType]) then
        return _API.ShowNotification(locale("notification.body_repair_window_check"), "error", {})
    end

    -- Carry and attach the body part
    isCarryingBodyPart = true 
    CarryBodyPartProp(partConfig.prop)

    -- store damaged parts in statebag
    local vehState = Entity(vehicle).state
    vehState:set("t1ger_mechanic:damagedParts", damagedParts, true)

    -- Create Target
    CreateBodyPartRepairTarget(vehicle, bodyType)

    while IsCarryingBodyPart() do
        Wait(1000)
        if not IsEntityPlayingAnim(player, carryAnim.dict, carryAnim.clip, 1) then
            TaskPlayAnim(player, carryAnim.dict, carryAnim.clip , carryAnim.blendIn, carryAnim.blendOut, carryAnim.duration, carryAnim.flags, 0, false, false, false)
        end
    end

    CancelBodyPartInstallation()
end

--- Cancels an ongoing body part installation
function CancelBodyPartInstallation()
    ClearPedTasks(player)
    if bodyPartProp and DoesEntityExist(bodyPartProp) then
        DeleteEntity(bodyPartProp)
    end
    isCarryingBodyPart = false
    bodyPartProp = nil
end
exports("CancelBodyPartInstallation", CancelBodyPartInstallation)

--- Event for using body part item:
RegisterNetEvent("t1ger_mechanic:client:useBodyPartItem", function(bodyType)
    UseBodyPartItem(bodyType)
end)

--- Event for handling body repair ptfx:
RegisterNetEvent("t1ger_mechanic:client:bodyRepairPtfx", function(dict, name, pos, rot, scale)
    if not dict then
        StopParticleFxLooped(ptfxHandle, 0)
        ptfxHandle = nil
    else
        lib.requestNamedPtfxAsset(dict)
        UseParticleFxAsset(dict)
        ptfxHandle = StartParticleFxLoopedAtCoord(name, pos.x, pos.y, pos.z, rot.x, rot.y, rot.z, scale, 0, 0, 0, 0)
    end
end)

-- Statebag to refresh vehicle body & reapply damages:
AddStateBagChangeHandler("t1ger_mechanic:refreshVehicleBody" --[[key filter]], nil --[[bag filter]], function(bagName, key, value, _unused, replicated)
    Wait(0)
    local vehicle = GetEntityFromStateBagName(bagName)
	if not vehicle or not DoesEntityExist(vehicle) then return end
    if not value or replicated then return end

    -- Set vehicle fixed:
    SetVehicleFixed(vehicle)

    -- Reapply damages
    for bodyType, parts in pairs(value.damagedParts or {}) do
        if type(parts) == "table" and next(parts) then
            for index, data in pairs(parts) do
                if bodyType == "door" or bodyType == "hood" or bodyType == "trunk" then
                    SetVehicleDoorBroken(vehicle, data.index, true)
                elseif bodyType == "wheel" then
                    BreakOffVehicleWheel(vehicle, data.wheelId, false, true, true, false)
                elseif bodyType == "window" then
                    RemoveVehicleWindow(vehicle, data.index)
                end
            end
        end
    end

    -- Set wheel health
    if value.wheelId then
        SetVehicleWheelHealth(vehicle, value.wheelId, 1000.0)
    end

    -- Set Body Health:
    SetVehicleBodyHealth(vehicle, value.bodyHealth or 1000.0)

    -- Set Engine Health:
    SetVehicleEngineHealth(vehicle, value.engineHealth or 1000.0)
end)

--- Handles the body inspection process. Returns `true` if success otherwise `false`.
--- @param vehicle number The vehicle entity handle
--- @return boolean
local function VehicleInspectionHandle(vehicle)

    local vehiclePoints = {
        ["front"] = {
            anim = {dict = "mp_intro_seq@", clip = "mp_mech_fix", blendIn = 4.0, blendOut = 4.0, flag = 1},
            inspected = false
        },
        ["rear"] = {
            anim = {dict = "anim@amb@clubhouse@tutorial@bkr_tut_ig3@", clip = "machinic_loop_mechandplayer", blendIn = 4.0, blendOut = 4.0, flag = 1, heading = 0.0},
            inspected = false
        },
        ["sideL"] = {
            anim = {dict = "mp_car_bomb", clip = "car_bomb_mechanic", blendIn = 8.0, blendOut = 8.0, flag = 1, heading = -90.0},
            inspected = false
        },
        ["sideR"] = {
            anim = {dict = "missmechanic", clip = "work2_base", blendIn = 4.0, blendOut = 4.0, flag = 1, heading = 90.0},
            inspected = false
        }
    }

    local function IsVehicleInspected(points)
        for index, data in pairs(points) do
            if not data.inspected then
                return false
            end
        end
        return true
    end

    local mk = Config.BodyRepair.Inspect.marker
    local textUI = Config.BodyRepair.Inspect.textUI
    local inspecting = false 
    
    _API.ShowNotification(locale("notification.body_inspect_start"), "inform", {})

    cancelInspection = false
    isInspecting = true

    while isInspecting do
        local sleep = true
        Wait(1)
        
        local vehicleCoords = GetEntityCoords(vehicle)
        if #(coords - vehicleCoords) <= 20 then
            local vehicleHeading = GetEntityHeading(vehicle)
            local d1, d2 = GetModelDimensions(GetEntityModel(vehicle))

            vehiclePoints["front"].coords = GetOffsetFromEntityInWorldCoords(vehicle, 0.0, (d2.y + 0.2), 0.0)
            vehiclePoints["front"].heading = (vehicleHeading - 180.0)

            vehiclePoints["rear"].coords = GetOffsetFromEntityInWorldCoords(vehicle, 0.0, (d1.y - 0.4), 0.0)
            vehiclePoints["rear"].heading = vehicleHeading

            vehiclePoints["sideL"].coords = GetOffsetFromEntityInWorldCoords(vehicle, (d1.x - 0.2), 0.0, 0.0)
            vehiclePoints["sideL"].heading = (vehicleHeading - 90.0)

            vehiclePoints["sideR"].coords = GetOffsetFromEntityInWorldCoords(vehicle, (d2.x + 0.3), 0.0, 0.0)
            vehiclePoints["sideR"].heading = (vehicleHeading + 90.0)

            for index, data in pairs(vehiclePoints) do
                local dist = #(coords - data.coords)
                if dist <= 5.0 and not data.inspected then
                    sleep = false
                    DrawMarker(mk.type, data.coords.x, data.coords.y, data.coords.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, mk.scale.x, mk.scale.y, mk.scale.z, mk.color.r, mk.color.g, mk.color.b, mk.color.a, false, true, 0, true, false, false, false)

                    if dist <= 1.0 then

                        -- text ui:
                        if data.currentTextUIIndex ~= index then
                            lib.showTextUI(locale("textui.body_inspect"), {
                                position = textUI.position,
                                icon = textUI.icon,
                                style = textUI.style
                            })
                            data.currentTextUIIndex = index
                        end

                        -- keybind:
                        if IsControlJustReleased(0, Config.BodyRepair.Inspect.keybind) and not inspecting then
                            lib.hideTextUI()
                            inspecting = true

                            SetEntityHeading(player, data.heading)
                            local groundBool, groundZ = GetGroundZFor_3dCoord(data.coords.x, data.coords.y, data.coords.z, false)
                            SetEntityCoords(player, data.coords.x, data.coords.y, groundZ, 0.0, 0.0, 0.0, false)

                            if ProgressBar({
                                duration = Config.BodyRepair.Inspect.duration,
                                label = locale("progressbar.body_inspect"),
                                useWhileDead = false,
                                canCancel = true,
                                disable = { move = true, combat = true, car = true},
                                anim = data.anim
                            }) then
                                vehiclePoints[index].inspected = true
                                if IsVehicleInspected(vehiclePoints) then
                                    isInspecting = false
                                    break
                                end
                            end

                            inspecting = false
                        end
                    else
                        if data.currentTextUIIndex == index then
                            lib.hideTextUI()
                            data.currentTextUIIndex = nil
                        end
                    end
                end
            end
            
        end

        if cancelInspection then
            break
        end

        if sleep then
            Wait(1000)
        end

    end

    local isOpen, text = lib.isTextUIOpen()
    if isOpen and text == locale("textui.body_inspect") then 
        lib.hideTextUI()
    end

    SetPlayerDiagnosing(false)

    if cancelInspection then
        return false
    end

    return true
end

--- Cancels an ongoing vehicle body inspection
function CancelVehicleBodyInspection()
    cancelInspection = true
end
exports("CancelInspection", CancelInspection)

--- Creates a table with workflow tasks indexed by taskId(number)
--- @param damagedParts table The damaged parts list of the vehicle
--- @return table
local function CreateWorkflowTasks(damagedParts)
    local workflowTasks = {}
    for bodyType, parts in pairs(damagedParts or {}) do
        if type(parts) == "table" and next(parts) then
            for index, data in pairs(parts) do
                local taskId = (#workflowTasks + 1)
                workflowTasks[taskId] = {
                    id = taskId,
                    name = string.format(Config.BodyRepair.WorkflowName, data.label),
                    completed = false,
                    label = data.label,
                    type = bodyType,
                    index = data.index
                }
            end
        end
    end
    return workflowTasks
end

--- Inspect the vehicle body and initiate the workflow UI with damaged body parts if any
--- @param entity? number The vehicle entity handle
function InspectVehicleBody(entity)
    if not IsPlayerMechanic() then return end
    local vehicle = nil

    if IsPlayerDiagnosing() then
        return _API.ShowNotification(locale("notification.is_diagnosing"), "inform", {})
    end

    SetPlayerDiagnosing(true)

    if type(entity) == "number" and DoesEntityExist(entity) and IsEntityAVehicle(entity) then
        vehicle = entity
    else
        vehicle = lib.getClosestVehicle(coords, 4.0, false)
    end

    if not vehicle or not DoesEntityExist(vehicle) or not IsEntityAVehicle(vehicle) then
        SetPlayerDiagnosing(false)
        return _API.ShowNotification(locale("notification.no_vehicle_nearby"), "inform", {})
    end

    local vehicleInspected = VehicleInspectionHandle(vehicle)
    isInspecting = false
    if not vehicleInspected then
        return _API.ShowNotification(locale("notification.body_inspect_cancelled"), "inform", {})
    end

    SetPlayerDiagnosing(false)

    local vehState = Entity(vehicle).state

    -- Get damaged parts:
    local damagedParts = GetVehicleDamagedBodyParts(vehicle)
    if not HasDamagedParts(damagedParts) then
        -- refresh vehicle body damage
        vehState:set("t1ger_mechanic:refreshVehicleBody", {
            damagedParts = damagedParts,
            health = 1000.0,
            engineHealth = Config.BodyRepair.FixEngineHealth and 1000.0 or damagedParts.engineHealth,
            wheelId = nil
        }, true)
        return _API.ShowNotification(locale("notification.body_repair_no_damage"), "inform", {})
    end

    FixVehicleDeformation(vehicle)
    
    _API.ShowNotification(locale("notification.body_inspection_complete"), "success", {})

    -- Store in statebag for other mechanics/scripts
    vehState:set("t1ger_mechanic:damagedParts", damagedParts, true)

    -- Store in statebag for other mechanics/scripts
    local workflowTasks = CreateWorkflowTasks(damagedParts)
    vehState:set("t1ger_mechanic:workflowTasks", workflowTasks, true)

    -- trigger workflow UI
    local plate = GetVehicleNumberPlateText(vehicle)
    StartWorkflow(string.format(Config.BodyRepair.WorkflowTitle, plate), workflowTasks)
end

--- Command to trigger vehicle body inspection
if Config.BodyRepair.Inspect.command.enable then
    RegisterCommand(Config.BodyRepair.Inspect.command.name, function(source, args, rawCommand)
        InspectVehicleBody()
    end)
end

--- Command to cancel vehicle body inspection
RegisterCommand(Config.BodyRepair.Inspect.cancel.command, function(source, args, rawCommand)
    CancelVehicleBodyInspection()
end)

--- Command to cancel vehicle body part installation
RegisterCommand(Config.BodyRepair.CancelCommand, function(source, args, rawCommand)
    CancelBodyPartInstallation()
end)

--- Target for inspect?
if Config.BodyRepair.Inspect.target.enable then
    CreateThread(function()
        while not _Target do Wait(100) end -- wait for target to initialize

        --- Checks if player can use interact vehicle body
        --- @param entity number entity handle
        --- @return boolean
        local function CanInteractVehicleBody(entity)
            if IsEntityValid(entity) and GetEntityType(entity) ~= 0 then
                if not IsCarryingBodyPart() and not IsCarryingComponent() and IsPlayerMechanic() then 
                    return true
                end
            end
            return false
        end

        -- target options
        local options = {
            {
                name = "t1ger_mechanic:vehicle:bodyInspect",
                icon = Config.BodyRepair.Inspect.target.icon,
                label = locale("target.body_inspect"),
                canInteract = CanInteractVehicleBody,
                distance = 4.0,
                onSelect = function(entity)
                    InspectVehicleBody(entity)
                end
            }
        }

        -- add global vehicle target:
        _API.Target.AddGlobalVehicle(options)
    end)
end