--- Breakdown mission
function Breakdown()
    local mission = Config.Missions.Types[curMission.type]
    local location = Config.Missions.Locations[curMission.type][curMission.index]
    local complete = false

    -- create blip for npc vehicle:
    curMission.blip = CreateMissionBlip(location.pos, mission.blip)

    --- Checks if player is near the mission entity
    --- @param entity number ped entity handle
    --- @return boolean IsNearMissionPed `true` if near mission ped. `false` otherwise
    local function IsNearMissionPed(entity)
        if IsEntityValid(entity) and GetEntityType(entity) ~= 0 then 
            if not IsPedInAnyVehicle(player, false) and not IsPedInAnyVehicle(player, true) then 
                return true
            end
        end
        return false
    end

    -- advanced notification:
    if Config.Missions.UseAdvancedNotify then
        local notifyIcon = Config.Missions.NotifyIcon
        local title, subtitle, message = locale("advanced_notification.breakdown_title"), locale("advanced_notification.breakdown_subtitle"), locale("advanced_notification.breakdown_message")
        _API.ShowAdvancedNotification(notifyIcon, notifyIcon, 6, title, false, subtitle, message)
    else
        local string = locale("advanced_notification.breakdown_title").."\n\n"..locale("advanced_notification.breakdown_subtitle").."\n\n"..locale("advanced_notification.breakdown_message")
        -- Calculate dynamic duration (e.g., 50ms per character)
        local perChar = 60
        local minDuration = 3000
        local maxDuration = 10000
        local dynamicDuration = math.min(maxDuration, math.max(minDuration, #string * perChar))
        _API.ShowNotification(string, "inform", {duration = dynamicDuration})
    end

    --- loop
    while not complete do
        Wait(1)

        if Config.Missions.Locations[curMission.type][curMission.index].inUse then

            local missionDist = #(coords - vector3(location.pos.x, location.pos.y, location.pos.z))

            -- create mission npc vehicle:
            if missionDist < 100.0 and not curMission.vehicle then
				curMission.vehicle = CreateMissionVehicle(location.pos)
				SetEntityAsMissionEntity(curMission.vehicle, true, true)
            end

            -- create mission npc ped:
            if missionDist < 90.0 and not curMission.ped then
				curMission.ped = CreateMissionPed(location.pedCoords, "WORLD_HUMAN_STAND_IMPATIENT")
				SetEntityAsMissionEntity(curMission.ped, true, true)
                local targetOptions = {}
                targetOptions[#targetOptions+1] = {
                    name = "t1ger_mechanic:"..curMission.type..":ped",
                    icon = "fa-solid fa-comment",
                    label = locale("target.missions_ped_interact"),
                    canInteract = IsNearMissionPed,
                    distance = 2.0,
                    onSelect = function(entity)
                        TaskPedToEnterTowVehicle(entity, mission.blip)
                    end
                }
                _API.Target.AddLocalEntity(curMission.ped, targetOptions)
            end

            -- if vehicle attached and ped is inside towtruck: 
            if curMission.towtruck and IsEntityAttachedToEntity(curMission.vehicle, curMission.towtruck) and (GetPedInVehicleSeat(curMission.towtruck, 0) == curMission.ped) and not curMission.attached then
                -- remove current blip:
                RemoveBlip(curMission.blip)
                -- create new blip:
                curMission.blip = CreateMissionBlip(location.dropoff, mission.blip)
                -- message:
                _API.ShowNotification(locale("notification.breakdown_head_to_dropoff"), "inform", {})
                -- marker settings:
                local dropoffMarker = lib.marker.new({
                    type = 36,
                    coords = location.dropoff,
                    color = {r = 255, g = 0, b = 0, a = 200},
                })
                -- create drop off zone: 
                curMission.dropZone = lib.zones.sphere({
                    coords = location.dropoff,
                    radius = 15.0,
                    debug = false,
                    inside = function(zone)
                        if not IsEntityAttachedToAnyVehicle(curMission.vehicle) then
                            local vehicleCoords = GetEntityCoords(curMission.vehicle)
                            if curMission.dropZone:contains(vec3(vehicleCoords.x, vehicleCoords.y, vehicleCoords.z)) then
                                -- leave vehicle:
                                TaskLeaveVehicle(curMission.ped, curMission.towtruck, 0)

                                -- remove current blip:
                                RemoveBlip(curMission.blip)

                                -- add blip for ped:
                                curMission.blip = CreateMissionEntityBlip(curMission.ped, mission.blip, false)

                                -- remove zone/marker:
                                curMission.dropZone:remove()
                                
                                -- create target:
                                local targetOptions = {}
                                targetOptions[#targetOptions+1] = {
                                    name = "t1ger_mechanic:"..curMission.type..":ped",
                                    icon = "fa-solid fa-comment",
                                    label = locale("target.missions_ped_interact"),
                                    canInteract = IsNearMissionPed,
                                    distance = 2.0,
                                    onSelect = function(entity)
                                        local taskAnim = {dict = "mp_common", clip = "givetake2_a", flag = 49, blendIn = 4.0, blendOut = -4.0}

                                        -- play anim on ped:
                                        lib.requestAnimDict(taskAnim.dict)
                                        TaskPlayAnim(entity, taskAnim.dict, taskAnim.clip, taskAnim.blendIn, taskAnim.blendOut, -1, taskAnim.flag, 1, 0, 0, 0)

                                        -- progressbar callback:
                                        local success = ProgressBar({
                                            duration = 2000,
                                            label = locale("progressbar.mission_reward"),
                                            useWhileDead = false,
                                            canCancel = true,
                                            anim = taskAnim,
                                            disable = {
                                                move = true,
                                                combat = true
                                            }
                                        })

                                        if not success then return end

                                        -- Remove Target
                                        _API.Target.RemoveLocalEntity(entity, {names = {"t1ger_mechanic:"..curMission.type..":ped"}, labels = {locale("target.missions_ped_interact")}})

                                        -- reward:
                                        TriggerServerEvent("t1ger_mechanic:server:missionPayout", curMission.type, curMission.index, location.dropoff)

                                        -- clean up:
                                        RemoveBlip(curMission.blip)
                                        ClearPedTasksImmediately(entity)
                                        TaskWanderStandard(entity, 10.0, 10)
                                        
                                        -- Delete entity:
                                        DeleteEntity(curMission.vehicle)

                                        -- Clean up:
                                        Wait(10000)
                                        curMission.cancel = true
                                    end
                                }
                                _API.Target.AddLocalEntity(curMission.ped, targetOptions)
                            end
                        else
                            dropoffMarker:draw()
                        end
                    end
                })
                -- update attributed:
                curMission.attached = true
            end

        end

        if curMission.cancel then
            complete = true
        end

    end
    
    -- clean up entities and blip
    if DoesEntityExist(curMission.vehicle) then DeleteVehicle(curMission.vehicle) end
    if DoesEntityExist(curMission.ped) then DeleteEntity(curMission.ped) end
    if DoesBlipExist(curMission.blip) then RemoveBlip(curMission.blip) end
    
    -- sync inUse state:
    TriggerServerEvent("t1ger_mechanic:server:missionInUse", curMission.type, curMission.index, false)

    curMission = {}
end

---Tasks the ped to enter the tow truck vehicle
---@param ped entity the ped entity handle
function TaskPedToEnterTowVehicle(ped)
    if not IsEntityAttachedToAnyVehicle(curMission.vehicle) then 
        return _API.ShowNotification(locale("notification.breakdown_vehicle_not_attached"), "inform", {})
    end

    -- validate towtruck:
    curMission.towtruck = GetEntityAttachedTo(curMission.vehicle)
    if not exports["t1ger_mechanic"]:IsVehicleTowTruck(curMission.towtruck) then
        return _API.ShowNotification(locale("notification.breakdown_invalid_towtruck"), "inform", {})
    end

    -- check tow truck passenger seat:
    if not IsVehicleSeatFree(curMission.towtruck, 0) then 
        return _API.ShowNotification(locale("notification.breakdown_passenger_seat_occupied"), "inform", {})
    end

    -- Remove Target
    _API.Target.RemoveLocalEntity(ped, {names = {"t1ger_mechanic:"..curMission.type..":ped"}, labels = {locale("target.missions_ped_interact")}})

    -- remove current blip:
    RemoveBlip(curMission.blip)
    
    -- add blip for ped:
    curMission.blip = CreateMissionEntityBlip(ped, Config.Missions.Types[curMission.type].blip, false)

    -- task for ped to enter towtruck vehicle
    ClearPedTasksImmediately(ped)
    FreezeEntityPosition(ped, false)
    TaskEnterVehicle(ped, curMission.towtruck, 20000, 0, 1.0, 1, 0)
end