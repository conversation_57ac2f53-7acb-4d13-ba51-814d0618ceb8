import{u as E,r as N,j as i,m as _,S as k,a as e,I as b,L as s,P as o,at as Se,au as Pe,av as Ne,aw as Te,ax as ae,F as g,C as G,b as L,ay as me,az as ne,y as U,G as le,aA as Q,aj as w,aB as z,aC as ee,aD as Y,A as ce,v as V,aE as re,V as oe,aF as ue,aG as he,aH as pe,aI as se,aJ as ie,q as C,M as Ee,s as K,z as X,f as q,aK as ve,p as ge,t as H,aL as Ae,aM as Ie,aN as Ce,l as fe,aO as Ge,aP as Oe,aQ as _e,aR as ke,aS as ye,aT as Re,x as Le,g as be,d as Ue}from"./index-99e0aeb1.js";import{S as W}from"./Slider-9dfab8ac.js";import{S as M}from"./Switch-4e23059b.js";function Be(r){const l=E(o.Settings),c=E(L),n=E(me),[T,a]=N.useState(!1),P=["#39334d","#465281","#87855e","#4b6a45","#702e2e","picker"];return i(_.div,{...k("right","display"),className:"animation-container display",children:[i("div",{className:"top",children:[i("div",{className:"back",onClick:()=>T?a(!1):r.setPage([]),children:[e(b,{}),s("APPS.SETTINGS.TITLE")]}),e("div",{className:"title",children:s("APPS.SETTINGS.DISPLAY_BRIGHTNESS")}),e("div",{})]}),e("div",{style:{marginTop:"2rem"}}),e("div",{className:"category-title",children:s("APPS.SETTINGS.APPEARANCE")}),i("section",{children:[e("div",{className:"settings-item",children:e("div",{className:"appearance-container",children:e("div",{className:"appearance-switches",children:["light","dark"].map((d,S)=>{var u;return i("div",{className:"mode",onClick:()=>{o.Settings.patch({display:{...l.display,theme:d}})},children:[e("img",{src:`./assets/img/${d}-mode-placeholder.webp`,alt:"Theme"}),e("div",{className:"mode-text",children:s(`APPS.SETTINGS.${d.toUpperCase()}`)}),e(Se,{checked:((u=l==null?void 0:l.display)==null?void 0:u.theme)===d})]},S)})})})}),i("div",{className:"settings-item",children:[e("div",{className:"setting",children:e("div",{className:"title",children:s("APPS.SETTINGS.AUTOMATIC")})}),e("div",{className:"setting-value",children:e(M,{checked:l.display.automatic,onChange:()=>{o.Settings.patch({display:{...l.display,automatic:!l.display.automatic}})}})})]})]}),e("div",{className:"category-title",children:s("APPS.SETTINGS.BRIGHTNESS")}),e("section",{children:i("div",{className:"settings-item",children:[e(Pe,{}),e(W,{onChange:d=>{o.Settings.patch({display:{...l.display,brightness:d}})},value:l.display.brightness,id:"brightness",min:.1,max:1,step:.01}),e(Ne,{})]})}),e("div",{className:"category-title",children:s("APPS.SETTINGS.PHONE_SCALE")}),e("section",{children:i("div",{className:"settings-item",children:[e(Te,{}),e(W,{onChange:d=>{n||o.Settings.patch({display:{...l.display,size:d}})},value:l.display.size,id:"phone-scale-slider",min:.5,max:1.1,step:.1}),e(ae,{})]})}),c.allowFrameColorChange&&i(g,{children:[e("div",{className:"category-title",children:s("APPS.SETTINGS.FRAME_COLOR")}),e("section",{children:e("div",{className:"settings-item",children:e("div",{className:"colors",children:P.map((d,S)=>{let u=d==="picker";return e("div",{className:"color",style:{backgroundImage:u&&"url(./assets/img/icons/picker.png)",boxShadow:!u&&`inset  0 0.1em 5em ${d}, 0 0 0 0.2em hsl(254, 30%, 85%)`},"data-active":l.display.frameColor===d,onClick:()=>{if(u){G.ColorPicker.set({onSelect:p=>{o.Settings.patch({display:{...l.display,frameColor:p}})}});return}o.Settings.patch({display:{...l.display,frameColor:d}})}},S)})})})})]})]})}function De(r){var d,S,u,p,m;const l=E(L),c=E(o.Settings),[n,T]=N.useState(!1),[a,P]=N.useState(null);return i(_.div,{...k("right",n?"notificationsInApp":"notifications"),className:"animation-container",children:[i("div",{className:"top",children:[i("div",{className:"back",onClick:()=>{n?T(!1):r.setPage([])},children:[e(b,{}),n?s("APPS.SETTINGS.BACK"):s("APPS.SETTINGS.TITLE")]}),e("div",{className:"title",children:n?s(`APP_NAMES.${a}`,(d=l.apps[a])==null?void 0:d.name):s("APPS.SETTINGS.NOTIFICATIONS")}),e("div",{})]}),e("div",{style:{marginTop:"0.5rem"}}),n?e(g,{children:i("section",{children:[i("div",{className:"settings-item",children:[e("div",{className:"setting",children:e("div",{className:"title",children:s("APPS.SETTINGS.ALLOW_NOTIFICATIONS")})}),e("div",{className:"setting-value",children:e(M,{onChange:()=>{var h;o.Settings.set({...o.Settings.value,notifications:{...c.notifications,[a]:{...c.notifications[a],enabled:!((h=c.notifications[a])!=null&&h.enabled)}}})},checked:((u=(S=c.notifications)==null?void 0:S[a])==null?void 0:u.enabled)??!0})})]}),i("div",{className:"settings-item",children:[e("div",{className:"setting",children:e("div",{className:"title",children:s("APPS.SETTINGS.SOUNDS")})}),e("div",{className:"setting-value",children:e(M,{onChange:()=>{var h,v,A;o.Settings.set({...o.Settings.value,notifications:{...c.notifications,[a]:{...(h=c.notifications)==null?void 0:h[a],sound:!((A=(v=c.notifications)==null?void 0:v[a])!=null&&A.sound)}}})},checked:((m=(p=c.notifications)==null?void 0:p[a])==null?void 0:m.sound)??!0})})]})]})}):i(g,{children:[e("div",{className:"category-title",children:s("APPS.SETTINGS.NOTIFICATIONS")}),e("section",{style:{marginBottom:"4rem"},children:Object.keys(l.apps).filter(h=>!!l.apps[h]).filter(h=>{var v;return ne(h)||((v=l.apps[h])==null?void 0:v.removable)===!1}).sort((h,v)=>s(`APP_NAMES.${h}`,l.apps[h].name).localeCompare(s(`APP_NAMES.${v}`,l.apps[v].name))).map(h=>{var v,A;return i("div",{className:"settings-item",style:{cursor:"pointer"},onClick:()=>{P(h),T(!0)},children:[i("div",{className:"setting",children:[e("img",{src:((v=l.apps[h])==null?void 0:v.icon)??`./assets/img/icons/apps/${h}.jpg`}),e("div",{className:"title",children:s(`APP_NAMES.${h}`,(A=l.apps[h])==null?void 0:A.name)})]}),e("div",{className:"setting-value",children:e(U,{className:"chevron"})})]},h)})})]})]})}function we(r){var S;const l=E(o.Settings),c=E(L),n=E(ce),[T,a]=N.useState(!1),[P,d]=N.useState(null);return N.useEffect(()=>{if(le("Settings"))return Q.set(!1),()=>{Q.set(!0)}},[n==null?void 0:n.active]),i(_.div,{...k("right",T?"soundBrowsingTunes":"sound"),className:"animation-container",children:[i("div",{className:"top",children:[i("div",{className:"back",onClick:()=>{T?a(!1):r.setPage([]),o.SoundManager.reset()},children:[e(b,{}),T?s("APPS.SETTINGS.BACK"):s("APPS.SETTINGS.TITLE")]}),e("div",{className:"title",children:T?P==="ringtone"?s("APPS.SETTINGS.RINGTONE"):s("APPS.SETTINGS.TEXT_TONE"):s("APPS.SETTINGS.SOUND_HAPTICS")}),e("div",{})]}),e("div",{style:{marginTop:"0.5rem"}}),T?i(g,{children:[e("div",{className:"category-title",children:P==="ringtone"?s("APPS.SETTINGS.RINGTONE"):s("APPS.SETTINGS.TEXT_TONE")}),e("section",{children:Object.keys((S=c.sounds)==null?void 0:S[P]).map((u,p)=>e("div",{className:"settings-item",style:{cursor:"pointer"},onClick:()=>{var m,h;o.Settings.set({...o.Settings.value,sound:{...o.Settings.value.sound,[P]:u}}),o.SoundManager.set({url:`./assets/sound/${P==="ringtone"?"ringtones":"texttones"}/${(h=(m=c.sounds)==null?void 0:m[P])==null?void 0:h[u]}`,timeout:P==="ringtones"?5e3:null,override:!0})},children:i("div",{className:"setting",children:[u==l.sound[P]?e(w,{className:"checkmark"}):e(w,{className:"checkmark hidden"}),e("div",{className:"title",children:z(u)})]})},p))})]}):i(g,{children:[e("div",{className:"category-title",children:s("APPS.SETTINGS.RINGTONE_VOLUME")}),e("section",{children:i("div",{className:"settings-item",children:[e(ee,{}),e(W,{onChange:u=>{u!==void 0&&o.Settings.patch({sound:{...o.Settings.value.sound,volume:u}})},value:l.sound.volume!==void 0?l.sound.volume:.5,min:0,max:1,step:.05}),e(Y,{})]})}),e("div",{className:"category-title",children:s("APPS.SETTINGS.CALL_VOLUME")}),e("section",{children:i("div",{className:"settings-item",children:[e(ee,{}),e(W,{onChange:u=>{o.Settings.set({...o.Settings.value,sound:{...o.Settings.value.sound,callVolume:u}})},value:l.sound.callVolume!==void 0?l.sound.callVolume:.5,min:0,max:1,step:.05}),e(Y,{})]})}),e("div",{className:"category-title",children:s("APPS.SETTINGS.SOUND_HAPTICS_PATTERNS")}),i("section",{children:[i("div",{className:"settings-item",style:{cursor:"pointer"},onClick:()=>{d("ringtone"),a(!0)},children:[e("div",{className:"setting",children:e("div",{className:"title",children:s("APPS.SETTINGS.RINGTONE")})}),i("div",{className:"setting-value",children:[e("div",{className:"value",children:z(l.sound.ringtone)}),e(U,{className:"chevron"})]})]}),i("div",{className:"settings-item",style:{cursor:"pointer"},onClick:()=>{d("texttone"),a(!0)},children:[e("div",{className:"setting",children:e("div",{className:"title",children:s("APPS.SETTINGS.TEXT_TONE")})}),i("div",{className:"setting-value",children:[e("div",{className:"value",children:z(l.sound.texttone)}),e(U,{className:"chevron"})]})]})]})]})]})}function Me(r){const{settingsSections:l}=N.useContext(j),c=E(o.Settings),n=E(L),T=8.25*1e3*1e3;return N.useEffect(()=>{var d,S;if(!n.apps)return;let a=Object.keys(n.apps).filter(u=>{var p;return((p=n.apps[u])==null?void 0:p.removable)===!1}).reduce((u,p)=>{var m;return u+(((m=n.apps[p])==null?void 0:m.size)??0)},0),P=c.storage.used-(a+T);L.patch({apps:{...n.apps,Photos:{...n.apps.Photos,size:((S=(d=n.apps)==null?void 0:d.Photos)==null?void 0:S.size)??0+P}}})},[]),i(_.div,{...k("right","storage"),className:"animation-container",children:[i("div",{className:"top",children:[i("div",{className:"back",onClick:()=>{var a;r.setPage((a=l==null?void 0:l[2])==null?void 0:a[0].options)},children:[e(b,{}),s("APPS.SETTINGS.TITLE")]}),e("div",{className:"title",children:s("APPS.SETTINGS.STORAGE")}),e("div",{})]}),e("div",{style:{marginTop:"0.5rem"}}),e("section",{children:i("div",{className:"settings-item",style:{cursor:"pointer"},children:[e("div",{className:"setting",children:e("div",{className:"title",children:s("APPS.SETTINGS.USED")})}),e("div",{className:"setting-value",children:i("div",{className:"value",children:[V(c.storage.used/1e3/1e3,2)," GB ",s("APPS.SETTINGS.OF")," ",V(c.storage.total/1e3/1e3,0)," GB"]})})]})}),e("section",{children:Object.keys(n.apps).filter(a=>!!n.apps[a]).filter(a=>{var P;return ne(a)||((P=n.apps[a])==null?void 0:P.removable)===!1}).sort((a,P)=>{var d,S;return(((d=n.apps[P])==null?void 0:d.size)??0)-(((S=n.apps[a])==null?void 0:S.size)??0)}).map(a=>{var P,d,S,u,p,m;return i("div",{className:"settings-item",style:{cursor:"pointer"},children:[i("div",{className:"setting",children:[e("img",{src:((P=n.apps[a])==null?void 0:P.icon)??`./assets/img/icons/apps/${a}.jpg`,onError:h=>h.currentTarget.src="./assets/img/icons/apps/unkown.png"}),e("div",{className:"title",children:s(`APP_NAMES.${a}`,n.apps[a].name)})]}),e("div",{className:"setting-value",children:e("div",{className:"value",children:((d=n.apps[a])==null?void 0:d.size)??0>1e6?V(((S=n.apps[a])==null?void 0:S.size)??0/1e6,2)+" GB":((u=n.apps[a])==null?void 0:u.size)??0>1e3?V(((p=n.apps[a])==null?void 0:p.size)??0/1e3,2)+" MB":((m=n.apps[a])==null?void 0:m.size)??"0 KB"})})]},a)})})]})}function Fe(r){const l=E(o.Settings),[c,n]=N.useState(!1),T=E(L);return i(_.div,{...k("right",c?"browsingWallpapers":"wallpaper"),className:"animation-container",children:[i("div",{className:"top",children:[i("div",{className:"back",onClick:()=>{c?n(!1):r.setPage([])},children:[e(b,{}),c?s("APPS.SETTINGS.WALLPAPER"):s("APPS.SETTINGS.TITLE")]}),i("div",{className:"title",children:[c?s("APPS.SETTINGS.WALLPAPERS"):s("APPS.SETTINGS.WALLPAPER")," "]}),e("div",{})]}),c?i("div",{className:"wallpapers-container",children:[e("div",{className:"wallpaper add",onClick:()=>{var a,P,d;G.Gallery.set({allowExternal:(d=(P=(a=L)==null?void 0:a.value)==null?void 0:P.AllowExternal)==null?void 0:d.Other,onSelect:S=>{o.Settings.patch({wallpaper:{...l.wallpaper,background:S.src}})}})},children:e(ae,{})}),l.wallpaper.background.includes("http")&&e("div",{className:"wallpaper active",style:{backgroundImage:`url(${l.wallpaper.background})`}}),Object.keys(T.wallpapers).map((a,P)=>e(Ke,{wallpaper:a,selected:l.wallpaper.background,theme:l.display.theme,cb:()=>{o.Settings.patch({wallpaper:{...l.wallpaper,background:a}})}},P))]}):e(xe,{setExploringWallpapers:n})]})}const Ke=({wallpaper:r,selected:l,cb:c})=>{const[n,T]=N.useState("");return N.useEffect(()=>{re(r,a=>T(a))},[]),e("div",{className:oe("wallpaper",l===r&&"active"),style:{backgroundImage:`url(${n})`},onClick:()=>c()})},xe=r=>{var T;const[l,c]=N.useState(""),n=E(o.Settings);return N.useEffect(()=>{re(n.wallpaper.background,a=>c(a))},[]),i("section",{children:[i("div",{className:"settings-item",style:{cursor:"pointer"},onClick:()=>r.setExploringWallpapers(!0),children:[e("div",{className:"setting",children:e("div",{className:"title",children:s("APPS.SETTINGS.CHOOSE_NEW_WALLPAPER")})}),e("div",{className:"setting-value",children:e(U,{className:"chevron"})})]}),e("div",{className:"settings-item",children:i("div",{className:"wallpapers",children:[e("div",{className:"preview",style:{backgroundImage:`url(${l})`},children:i("div",{className:"lockscreen-container",children:[e(ue,{}),e(he,{layout:((T=n.lockscreen)==null?void 0:T.layout)??1})]})}),e("div",{className:"preview",style:{backgroundImage:`url(${l})`},children:i("div",{className:"settings-homescreen-container",children:[e("div",{className:"settings-homescreen-apps",children:n.apps[1].filter(a=>!pe(a)).slice(0,24).map(a=>{let P=L.value.apps[a],d=(P==null?void 0:P.icon)??`./assets/img/icons/apps/${a}.jpg`;return i("div",{className:"settings-homescreen-app",children:[e("img",{src:d,onError:S=>S.currentTarget.src="./assets/img/icons/apps/unkown.png"}),e("div",{className:"app-name",children:a})]})})}),e("div",{className:"favourite-apps",children:n.apps[0].map(a=>{let P=L.value.apps[a],d=(P==null?void 0:P.icon)??`./assets/img/icons/apps/${a}.jpg`;return e("div",{className:"settings-homescreen-app",children:e("img",{src:d,onError:S=>S.currentTarget.src="./assets/img/icons/apps/unkown.png"})})})})]})})]})}),i("div",{className:"settings-item",children:[e("div",{className:"setting",children:e("div",{className:"title",children:s("APPS.SETTINGS.BLUR_HOMESCREEN")})}),e("div",{className:"setting-value",children:e(M,{checked:n.wallpaper.blur,onChange:()=>{o.Settings.patch({wallpaper:{...n.wallpaper,blur:!n.wallpaper.blur}})}})})]})]})};function He(r){const l=N.useRef(null),c=E(o.Settings),[n,T]=N.useState([]),[a,P]=N.useState(!1),[d,S]=N.useState([]),[u,p]=N.useState(!1),m=A=>{A=="backspace"?T(f=>f.slice(0,-1)):T(f=>{if(f.length===4)return f;let F=[...f,A];return F.length===4&&(u?C("Security",{action:"setPin",oldPin:d.join(""),pin:F.join("")}).then(x=>{if(!x){G.PopUp.set({title:s("APPS.SETTINGS.PIN_CODE_ERROR.TITLE"),description:s("APPS.SETTINGS.PIN_CODE_ERROR.WRONG"),buttons:[{title:s("APPS.SETTINGS.PIN_CODE_ERROR.OK")}]}),T([]);return}o.Settings.patch({security:{...c.security,pinCode:!0}}),p(!1),T([])}):C("Security",{action:"verifyPin",pin:F.join("")},!0).then(x=>{x?(P(!0),S(F),T([])):(l.current.className=`pin-circles wrong ${c.display.theme=="dark"?"light":"dark"}`,setTimeout(()=>{l.current.className=`pin-circles ${c.display.theme=="dark"?"light":"dark"}`,T([])},350))})),F})},h=A=>{A?C("Security",{action:"setFaceId",pin:d.join("")},!0).then(f=>{f&&o.Settings.patch({security:{...o.Settings.value.security,faceId:!0}})}):C("Security",{action:"removeFaceId",pin:d.join("")},!0).then(f=>{f&&o.Settings.patch({security:{...o.Settings.value.security,faceId:!1}})})},v=()=>{C("Security",{action:"removePin",pin:n.join("")},!0).then(A=>{A&&o.Settings.patch({security:{pinCode:!1,faceId:!1}})})};return i(_.div,{...k("right","passcode"),className:"animation-container",children:[i("div",{className:"top",children:[i("div",{className:"back",onClick:()=>r.setPage([]),children:[e("i",{className:"fal fa-chevron-left"}),a||!c.security.pinCode?s("APPS.SETTINGS.BACK"):s("CANCEL")]}),e("div",{className:"title",children:a||!c.security.pinCode?s("APPS.SETTINGS.FACE_ID_PASSWORD"):s("APPS.SETTINGS.ENTER_PASSCODE")}),e("div",{})]}),a||!c.security.pinCode?e(g,{children:u?i("div",{className:"passcode-container",children:[i("div",{className:"pin",children:[s("APPS.SETTINGS.ENTER_PASSCODE"),e(se,{pin:n,dark:c.display.theme==="light",Ref:l})]}),e("div",{className:"numbers",children:e(ie,{cb:m})})]}):i(g,{children:[e("div",{style:{marginTop:"0.5rem"}}),e("div",{className:"category-title",children:s("APPS.SETTINGS.FACE_ID")}),e("section",{children:c.security.faceId?e("div",{className:"settings-item",style:{cursor:"pointer"},onClick:()=>{G.PopUp.set({title:s("APPS.SETTINGS.DISABLE_FACE_ID_POPUP.TITLE"),description:s("APPS.SETTINGS.DISABLE_FACE_ID_POPUP.TEXT"),buttons:[{title:s("APPS.SETTINGS.DISABLE_FACE_ID_POPUP.CANCEL")},{title:s("APPS.SETTINGS.DISABLE_FACE_ID_POPUP.CONFIRM"),color:"red",cb:()=>h(!1)}]})},children:e("div",{className:"setting",children:e("div",{className:"title red",children:s("APPS.SETTINGS.RESET_FACE_ID")})})}):e("div",{className:"settings-item",style:{cursor:"pointer"},onClick:()=>{c.security.pinCode&&G.PopUp.set({title:s("APPS.SETTINGS.ENABLE_FACE_ID_POPUP.TITLE"),description:s("APPS.SETTINGS.ENABLE_FACE_ID_POPUP.TEXT"),buttons:[{title:s("APPS.SETTINGS.ENABLE_FACE_ID_POPUP.CANCEL")},{title:s("APPS.SETTINGS.ENABLE_FACE_ID_POPUP.CONFIRM"),cb:()=>h(!0)}]})},children:e("div",{className:"setting",children:e("div",{className:oe("title",c.security.pinCode?"blue":"grey"),children:s("APPS.SETTINGS.ENABLE_FACE_ID")})})})}),e("div",{className:"category-title",children:s("APPS.SETTINGS.PIN_CODE")}),e("section",{children:c.security.pinCode?i(g,{children:[e("div",{className:"settings-item",style:{cursor:"pointer"},onClick:()=>{G.PopUp.set({title:s("APPS.SETTINGS.DISABLE_PIN_POPUP.TITLE"),description:s("APPS.SETTINGS.DISABLE_PIN_POPUP.TEXT"),buttons:[{title:s("APPS.SETTINGS.DISABLE_PIN_POPUP.CANCEL")},{title:s("APPS.SETTINGS.DISABLE_PIN_POPUP.CONFIRM"),color:"red",cb:v}]})},children:e("div",{className:"setting",children:e("div",{className:"title red",children:s("APPS.SETTINGS.TURN_PASSCODE_OFF")})})}),e("div",{className:"settings-item",style:{cursor:"pointer"},onClick:()=>p(!0),children:e("div",{className:"setting",children:e("div",{className:"title blue",children:s("APPS.SETTINGS.CHANGE_PASSCODE")})})})]}):i(g,{children:[e("div",{className:"settings-item",style:{cursor:"pointer"},onClick:()=>p(!0),children:e("div",{className:"setting",children:e("div",{className:"title blue",children:s("APPS.SETTINGS.TURN_PASSCODE_ON")})})}),e("div",{className:"settings-item",children:e("div",{className:"setting",children:e("div",{className:"title grey",children:s("APPS.SETTINGS.CHANGE_PASSCODE")})})})]})})]})}):i("div",{className:"passcode-container",children:[i("div",{className:"pin",children:[s("APPS.SETTINGS.ENTER_PASSCODE"),e(se,{pin:n,dark:c.display.theme==="light",Ref:l})]}),e("div",{className:"numbers",children:e(ie,{cb:m})})]})]})}function Ve(r){var p;const l=E(o.Settings),c=E(o.PhoneNumber),[n,T]=N.useState(!1),[a,P]=N.useState(!1),[d,S]=N.useState([]);N.useEffect(()=>{le("Settings")&&C("Phone",{action:"getBlockedNumbers"},Ee.Blocked).then(m=>{if(!m)return K("warning","Failed to get blocked numbers, no response");S(m)})},[]);const u=m=>{C("Phone",{action:"toggleBlock",number:m,blocked:!1},!0).then(h=>{if(!h)return K("warning","Failed to unblock number",m);S(v=>v.filter(A=>A!==m))})};return i(_.div,{...k("right",n?"blocked":"phone"),className:"animation-container",children:[i("div",{className:"top",children:[i("div",{className:"back",onClick:()=>n?T(!1):r.setPage([]),children:[e(b,{}),n?s("APPS.SETTINGS.BACK"):s("APPS.SETTINGS.TITLE")]}),e("div",{className:"title",children:n?s("APPS.SETTINGS.BLOCKED_CONTACTS"):s("APPS.SETTINGS.PHONE")}),n?e("div",{className:"value",onClick:()=>P(m=>!m),children:a?s("APPS.SETTINGS.DONE"):s("APPS.SETTINGS.EDIT")}):e("div",{})]}),e("div",{style:{marginTop:"0.5rem"}}),n?e(g,{children:e("section",{children:d.map((m,h)=>i("div",{className:"settings-item",children:[i("div",{className:"setting",children:[a?e(X,{className:"remove",onClick:()=>{G.PopUp.set({title:s("APPS.SETTINGS.UNBLOCK_POPUP.TITLE"),description:s("APPS.SETTINGS.UNBLOCK_POPUP.TEXT"),buttons:[{title:s("APPS.SETTINGS.UNBLOCK_POPUP.CANCEL")},{title:s("APPS.SETTINGS.UNBLOCK_POPUP.CONFIRM"),color:"red",cb:()=>{u(m)}}]})}}):e(X,{className:"hidden"}),e("div",{className:"title",children:q(m)})]}),e("div",{className:"setting-value"})]},h))})}):i(g,{children:[e("section",{children:i("div",{className:"settings-item",children:[e("div",{className:"setting",children:e("div",{className:"title",children:s("APPS.SETTINGS.MY_NUMBER")})}),i("div",{className:"setting-value",children:[q(c),e(ve,{onClick:()=>ge(c)})]})]})}),i("section",{children:[i("div",{className:"settings-item",children:[e("div",{className:"setting",children:e("div",{className:"title",children:s("APPS.SETTINGS.SHOW_CALLER_ID")})}),e("div",{className:"setting-value",children:e(M,{onChange:()=>{var m;o.Settings.patch({phone:{...l.phone,showCallerId:!((m=l.phone)!=null&&m.showCallerId)}})},checked:((p=l.phone)==null?void 0:p.showCallerId)??!1})})]}),i("div",{className:"settings-item",style:{cursor:"pointer"},onClick:()=>T(!0),children:[e("div",{className:"setting",children:e("div",{className:"title",children:s("APPS.SETTINGS.BLOCKED_CONTACTS")})}),e("div",{className:"setting-value",children:e(U,{className:"chevron"})})]})]})]})]})}function We({setPage:r}){const l=E(o.PhoneNumber),c=E(H.MISC.BACKUPS),[n,T]=N.useState(!1);return N.useEffect(()=>{c||C("Backup",{action:"get"},[{number:l},{number:"13144122"}]).then(a=>{if(!a)return K("warning","Failed to get backups from the server");H.MISC.BACKUPS.set(a)})},[]),i(_.div,{...k("right","profile"),className:"animation-container",children:[i("div",{className:"top",children:[i("div",{className:"back",onClick:()=>r([]),children:[e(b,{}),s("APPS.SETTINGS.TITLE")]}),e("div",{className:"title",children:s("APPS.SETTINGS.CLOUD_BACKUP")}),e("div",{className:"value",onClick:()=>T(a=>!a),children:n?s("APPS.SETTINGS.DONE"):s("APPS.SETTINGS.EDIT")})]}),e("div",{style:{marginTop:"0.5rem"}}),e("div",{className:"category-title",children:s("APPS.SETTINGS.YOUR_PROFILE")}),e("section",{children:i("div",{className:"settings-item",style:{cursor:"pointer"},onClick:()=>{if(c!=null&&c.find(a=>a.number===l))return G.PopUp.set({title:s("APPS.SETTINGS.BACKUP_ERROR_POPUP.WARNING"),description:s("APPS.SETTINGS.BACKUP_ERROR_POPUP.ALREADY_HAVE"),buttons:[{title:s("APPS.SETTINGS.BACKUP_ERROR_POPUP.OK")}]});G.PopUp.set({title:s("APPS.SETTINGS.SAVE_BACKUP_POPUP.TITLE"),description:s("APPS.SETTINGS.SAVE_BACKUP_POPUP.TEXT"),buttons:[{title:s("APPS.SETTINGS.SAVE_BACKUP_POPUP.CANCEL")},{title:s("APPS.SETTINGS.SAVE_BACKUP_POPUP.CONFIRM"),cb:()=>{C("Backup",{action:"create"},!0).then(a=>{if(!a)return K("warning","Failed to create a backup");H.MISC.BACKUPS.set([{number:l},...c])})}}]})},children:[e("div",{className:"setting",children:e("div",{className:"title",children:s("APPS.SETTINGS.CREATE_BACKUP")})}),e("div",{className:"setting-value"})]})}),e("div",{className:"category-title",children:s("APPS.SETTINGS.BACKUPS")}),e("section",{children:c==null?void 0:c.map((a,P)=>i("div",{className:"settings-item",style:{cursor:a.number!==l?"pointer":"not-allowed"},"data-disabled":a.number===l&&!n,onClick:()=>{if(a.number===l)return G.PopUp.set({title:s("APPS.SETTINGS.BACKUP_ERROR_POPUP.ERROR"),description:s("APPS.SETTINGS.BACKUP_ERROR_POPUP.SAME_PHONE"),buttons:[{title:s("APPS.SETTINGS.BACKUP_ERROR_POPUP.OK")}]});G.PopUp.set({title:s("APPS.SETTINGS.APPLY_BACKUP_POPUP.TITLE"),description:s("APPS.SETTINGS.APPLY_BACKUP_POPUP.TEXT"),buttons:[{title:s("APPS.SETTINGS.APPLY_BACKUP_POPUP.CANCEL")},{title:s("APPS.SETTINGS.APPLY_BACKUP_POPUP.CONFIRM"),color:"red",cb:()=>{C("Backup",{action:"apply",number:a.number},!0).then(d=>{if(!d)return K("error","Could not apply backup, apply returned false");setTimeout(()=>{G.PopUp.set({title:s("APPS.SETTINGS.SUCCESS"),description:s("APPS.SETTINGS.SUCCESS_TEXT"),buttons:[{title:s("APPS.SETTINGS.OK")}]})},500)})}}]})},children:[i("div",{className:"setting",children:[n&&e(X,{className:"remove",onClick:d=>{d.stopPropagation(),G.PopUp.set({title:s("APPS.SETTINGS.REMOVE_BACKUP_POPUP.TITLE"),description:s("APPS.SETTINGS.REMOVE_BACKUP_POPUP.TEXT"),buttons:[{title:s("APPS.SETTINGS.REMOVE_BACKUP_POPUP.CANCEL")},{title:s("APPS.SETTINGS.REMOVE_BACKUP_POPUP.CONFIRM"),color:"red",cb:()=>{C("Backup",{action:"delete",number:a.number}).then(S=>{if(!S)return K("error","Could not delete backup");H.MISC.BACKUPS.set(H.MISC.BACKUPS.value.filter(u=>u.number!==a.number))})}}]})}}),e("div",{className:"title",children:q(a.number)})]}),e("div",{className:"setting-value"})]},P))})]})}function $(r,l){if(!r.match(/\d+\.\d+\.\d+/)||!l.match(/\d+\.\d+\.\d+/))return!1;const c=r.split(".").map(Number),n=l.split(".").map(Number);return(c[0]??0)<(n[0]??0)?!0:(c[0]??0)>(n[0]??0)?!1:(c[1]??0)<(n[1]??0)?!0:(c[1]??0)>(n[1]??0)?!1:(c[2]??0)<(n[2]??0)}function $e(r){const{settingsSections:l}=N.useContext(j),c=E(o.Settings);return i(_.div,{...k("right","softwareUpdate"),className:"animation-container",children:[i("div",{className:"top",children:[i("div",{className:"back",onClick:()=>{var n;return r.setPage((n=l==null?void 0:l[2])==null?void 0:n[0].options)},children:[e(b,{}),s("APPS.SETTINGS.TITLE")]}),e("div",{className:"title",children:s("APPS.SETTINGS.SOFTWARE_UPDATE")}),e("div",{})]}),e("div",{style:{marginTop:"0.5rem"}}),e("section",{children:i("div",{className:"settings-item",style:{cursor:"pointer"},children:[e("div",{className:"setting",children:e("div",{className:"title",children:s("APPS.SETTINGS.AUTOMATIC_UPDATES")})}),e("div",{className:"setting-value",children:e("div",{className:"value",children:s("APPS.SETTINGS.OFF")})})]})}),$(c.version,c.latestVersion)&&e("section",{children:i("div",{className:"settings-item",style:{flexDirection:"column",alignItems:"inherit",gap:"0.75rem"},children:[i("div",{className:"item-header",children:[e("img",{src:"./assets/img/lbos.webp"}),i("div",{className:"info",children:[e("div",{className:"title",children:c.latestVersion}),e("div",{className:"author",children:e(je,{})})]})]}),e("div",{className:"item-content",children:"Please tell your server owner to update the phone to the latest version."})]})}),!$(c.version,c.latestVersion)&&i("div",{className:"up-to-date",children:[i("div",{className:"version",children:["V ",c==null?void 0:c.version]}),e("div",{className:"title",children:s("APPS.SETTINGS.UP_TO_DATE")})]})]})}const je=()=>{const r="abcdefghijklmnopqrstuvwxyz".split("");return i(g,{children:[r[11].toUpperCase(),r[1].toUpperCase()]})};function te({setPage:r,type:l}){const{settingsSections:c}=N.useContext(j);return i(_.div,{...k("right",l??"system"),className:"animation-container",children:[i("div",{className:"top",children:[i("div",{className:"back",onClick:()=>{var n;return r((n=c==null?void 0:c[2])==null?void 0:n[0].options[1][1].options)},children:[e(b,{}),s("APPS.SETTINGS.TITLE")]}),e("div",{className:"title",children:s(`APPS.SETTINGS.${l.toUpperCase()}`)}),e("div",{})]}),e("div",{style:{marginTop:"0.5rem"}}),i("section",{children:[l==="Language"&&e(ze,{}),l==="Temperature"&&e(Ye,{})]})]})}const ze=()=>{const r=E(o.Settings),[l,c]=N.useState([]);return N.useEffect(()=>{C("getLocales",null,Ae).then(n=>{n&&c(n)})},[]),N.useEffect(()=>{Ie(r.locale)},[r.locale]),i(g,{children:[l.map((n,T)=>e("div",{className:"settings-item",style:{cursor:"pointer"},onClick:()=>o.Settings.patch({locale:n.locale}),children:i("div",{className:"setting",children:[n.locale===r.locale?e(w,{className:"checkmark"}):e(w,{className:"checkmark hidden"}),e("div",{className:"title",children:n.name})]})},T)),";"]})},Ye=()=>{const r=E(o.Settings);return i(g,{children:[e("div",{className:"settings-item",style:{cursor:"pointer"},onClick:()=>o.Settings.patch({weather:{celcius:!0}}),children:i("div",{className:"setting",children:[r.weather&&r.weather.celcius?e(w,{className:"checkmark"}):e(w,{className:"checkmark hidden"}),e("div",{className:"title",children:s("APPS.SETTINGS.CELCIUS")})]})}),e("div",{className:"settings-item",style:{cursor:"pointer"},onClick:()=>o.Settings.patch({weather:{celcius:!1}}),children:i("div",{className:"setting",children:[r.weather&&r.weather.celcius===!1?e(w,{className:"checkmark"}):e(w,{className:"checkmark hidden"}),e("div",{className:"title",children:s("APPS.SETTINGS.FAHRENHEIT")})]})})]})};const j=N.createContext(null);function Je(){var J;const r=E(o.Settings),l=E(L),[c,n]=N.useState(""),[T,a]=N.useState([]),P=E(ce),[d,S]=N.useState([]),[u,p]=N.useState(null),[m,h]=N.useState(null),[v,A]=N.useState(null),[f,F]=N.useState([]);N.useEffect(()=>{F(O=>[[{name:s("APPS.SETTINGS.AIRPLANE_MODE"),icon:e(Ce,{className:"orange"}),description:s("APPS.SETTINGS.AIRPLANE_MODE_DESCRIPTION"),switch:!0,value:r.airplaneMode,onChange:()=>o.Settings.patch({airplaneMode:!r.airplaneMode})},{name:s("APPS.SETTINGS.STREAMER_MODE"),icon:e(fe,{className:"purple"}),description:s("APPS.SETTINGS.STREAMER_MODE_DESCRIPTION"),switch:!0,value:r.streamerMode,onChange:()=>o.Settings.patch({streamerMode:!r.streamerMode})}],[{name:s("APPS.SETTINGS.NOTIFICATIONS"),description:s("APPS.SETTINGS.NOTIFICATIONS_DESCRIPTION"),icon:e(Ge,{className:"red"}),options:e(De,{setPage:S})},{name:s("APPS.SETTINGS.SOUND_HAPTICS"),description:s("APPS.SETTINGS.SOUND_HAPTICS_DESCRIPTION"),icon:e(Y,{className:"pink"}),options:e(we,{setPage:S})}],[{name:s("APPS.SETTINGS.GENERAL"),description:s("APPS.SETTINGS.GENERAL_DESCRIPTION"),icon:e(Oe,{className:"grey"}),notification:$(r.version,r.latestVersion),options:[[{name:s("APPS.SETTINGS.ABOUT"),options:[[{name:s("APPS.SETTINGS.NAME"),value:r.name,input:{type:"name",maxLength:20},cb:(I,t)=>{C("setPhoneName",I)}},{name:s("APPS.SETTINGS.VERSION"),value:r.version},{name:s("APPS.SETTINGS.MODEL_NAME"),value:"LB Phone"},{name:s("APPS.SETTINGS.SERIAL_NUMBER"),value:"DSDWWWSSF3"},{name:s("APPS.SETTINGS.DEVELOPED_BY"),value:"Breze & Loaf Scripts"},{name:s("APPS.SETTINGS.PURCHASE_AT"),value:"https://store.lbscripts.com",isLink:!0}],[{name:s("APPS.SETTINGS.SONGS"),value:"0"},{name:s("APPS.SETTINGS.VIDEOS"),value:0},{name:s("APPS.SETTINGS.PHOTOS"),value:0},{name:s("APPS.SETTINGS.APPLICATIONS"),value:x()},{name:s("APPS.SETTINGS.CAPACITY"),value:`${r.storage.total/1e3/1e3} GB`},{name:s("APPS.SETTINGS.AVAILABLE"),value:`${V((r.storage.total-r.storage.used)/1e3/1e3,1)} GB`}]]},{name:s("APPS.SETTINGS.SOFTWARE_UPDATE"),options:e($e,{setPage:S}),notification:$(r.version,r.latestVersion)},{name:s("APPS.SETTINGS.PHONE_STORAGE"),options:e(Me,{setPage:S})}],[{name:s("APPS.SETTINGS.DATE_TIME"),options:[[{id:"twelveHourClock",name:s("APPS.SETTINGS.24_HOUR_TIME"),switch:!0,value:!r.time.twelveHourClock,onChange:()=>{var I,t,y,R,D;return o.Settings.patch({time:{...(t=(I=o.Settings)==null?void 0:I.value)==null?void 0:t.time,twelveHourClock:!((D=(R=(y=o.Settings)==null?void 0:y.value)==null?void 0:R.time)!=null&&D.twelveHourClock)}})}}],[{name:s("APPS.SETTINGS.SET_AUTOMATICALLY"),switch:!0,value:!0,disabled:!0},{name:s("APPS.SETTINGS.TIME_ZONE"),value:Intl.DateTimeFormat().resolvedOptions().timeZone??"Unknown"}]]},{name:s("APPS.SETTINGS.LANGUAGE_REGION"),options:[[{name:s("APPS.SETTINGS.LANGUAGE"),value:s("LABEL"),options:e(te,{type:"Language",setPage:S})}],[{name:s("APPS.SETTINGS.TEMPERATURE"),value:r.weather&&r.weather.celcius?"C":"F",options:e(te,{type:"Temperature",setPage:S})}]]}],[{name:s("APPS.SETTINGS.RESET_POPUP.TITLE"),onClick:()=>{G.PopUp.set({title:s("APPS.SETTINGS.RESET_POPUP.TITLE"),description:s("APPS.SETTINGS.RESET_POPUP.TEXT"),buttons:[{title:s("APPS.SETTINGS.RESET_POPUP.CANCEL")},{title:s("APPS.SETTINGS.RESET_POPUP.CONFIRM"),color:"red",cb:()=>C("Security",{action:"factoryReset"})}]})}}]]},{name:s("APPS.SETTINGS.DISPLAY_BRIGHTNESS"),description:s("APPS.SETTINGS.DISPLAY_BRIGHTNESS_DESCRIPTION"),icon:e(_e,{className:"blue"}),options:e(Be,{setPage:S})},{name:s("APPS.SETTINGS.WALLPAPER"),description:s("APPS.SETTINGS.WALLPAPER_DESCRIPTION"),icon:e(ke,{className:"lightblue"}),options:e(Fe,{setPage:S})},{name:s("APPS.SETTINGS.FACE_ID_PASSWORD"),description:s("APPS.SETTINGS.FACE_ID_PASSWORD_DESCRIPTION"),icon:e(ye,{className:"green"}),options:e(He,{setPage:S})},{name:s("APPS.SETTINGS.BATTERY"),description:s("APPS.SETTINGS.BATTERY_DESCRIPTION"),icon:e(Re,{className:"green"})}],[{name:s("APPS.SETTINGS.PHONE"),description:s("APPS.SETTINGS.PHONE_DESCRIPTION"),icon:l.apps.Phone.icon??"./assets/img/icons/apps/Phone.jpg",color:"green",options:e(Ve,{setPage:S}),last:!0}]])},[r]);const x=()=>{let O=0;return r.apps.forEach(I=>{I.forEach(t=>{O++})}),O},Z=N.useRef(!0),B=N.useRef(!1);return N.useEffect(()=>{if(Z.current){Z.current=!1;return}B!=null&&B.current||(B.current=!0)},[r]),N.useEffect(()=>()=>{B!=null&&B.current&&(K("info","Settings","Changes detected, saving settings",o.Settings.value),C("setSettings",o.Settings.value),B.current=!1)},[P==null?void 0:P.active]),N.useEffect(()=>{if(c.length>0){let O=f.map(I=>I.filter(t=>{var y,R;return(y=t.name)!=null&&y.toLowerCase().includes(c.toLowerCase())?!0:t.options&&((R=t.options)!=null&&R.name)?t.options.filter(D=>{if(D.name.toLowerCase().includes(c.toLowerCase()))return!0;if(D.options)return D.options.filter(de=>{if(de.name.toLowerCase().includes(c.toLowerCase()))return!0})}):!1}));a(O)}else a([])},[c]),e(j.Provider,{value:{settingsSections:f},children:e("div",{className:"settings-container",children:d.length===0?i(_.div,{...k("left","home"),className:"animation-container",children:[e("div",{className:"title default",style:((J=r==null?void 0:r.display)==null?void 0:J.theme)==="dark"?{color:"#ffffff"}:{color:"#000000"},children:s("APPS.SETTINGS.TITLE")}),e(Le,{placeholder:s("SEARCH"),onChange:O=>n(O.target.value)}),T.length>0?e("section",{children:T.map((O,I)=>e(N.Fragment,{children:O.map((t,y)=>i("div",{className:"settings-item","data-clickable":!!(t.options||t.onClick),onClick:()=>{if(t.onClick)return t.onClick();t.options&&(S(t.options),p(t.name))},children:[i("div",{className:"setting",children:[t.icon&&(typeof t.icon=="string"?e("img",{src:t.icon}):t.icon),i("div",{className:"setting-info",children:[e("div",{className:"title",children:t.name}),t.description&&e("div",{className:"description",children:t.description})]})]}),i("div",{className:"setting-value",children:[t.switch&&e(M,{onChange:t.onChange,checked:t.value,disabled:t.disabled??!1}),t.value&&!t.switch&&e("div",{className:"value",children:t.value}),t.options&&e(U,{className:"chevron"})]})]},y))},I))}):i(g,{children:[i("div",{className:"profile",onClick:()=>S(e(We,{setPage:S})),children:[r.avatar?e("div",{className:"avatar","data-hasavatar":"true",style:{backgroundImage:`url(${r.avatar})`}}):e("div",{className:"avatar",children:be(r.name)}),i("div",{className:"profile-info",children:[e("div",{className:"name",children:r.name}),e("div",{className:"info",children:s("APPS.SETTINGS.CLOUD_BACKUP")})]}),e(U,{className:"chevron"})]}),f.map((O,I)=>e("section",{"data-last":I===f.length-1,children:O.map((t,y)=>i("div",{className:"settings-item","data-clickable":!!(t.options||t.onClick),onClick:()=>{if(t.onClick)return t.onClick();t.options&&(S(t.options),p(t.name))},children:[i("div",{className:"setting",children:[t.icon&&(typeof t.icon=="string"?e("img",{src:t.icon}):t.icon),i("div",{className:"setting-info",children:[e("div",{className:"title",children:t.name}),t.description&&e("div",{className:"description",children:t.description})]})]}),t.notification&&e("div",{className:"settings-notification",children:"1"}),i("div",{className:"setting-value",children:[t.switch&&e(M,{onChange:t.onChange,checked:t.value,disabled:t.disabled??!1}),t.value&&!t.switch&&e("div",{className:"value",children:t.value}),t.options&&e(U,{className:"chevron"})]})]},y))},I))]})]}):e(g,{children:d.length?i(_.div,{...k("right",d.length),className:"animation-container",children:[i("div",{className:"top",children:[i("div",{className:"back",onClick:()=>{S((m==null?void 0:m.page)??[]),p((m==null?void 0:m.name)??"Settings"),h(null),A(null)},children:[e(b,{}),v??s("APPS.SETTINGS.TITLE")]}),i("div",{className:"title",children:[u??s("APPS.SETTINGS.TITLE")," "]}),e("div",{})]}),d.map((O,I)=>e("section",{children:O.map((t,y)=>i("div",{className:"settings-item","data-clickable":!!(t.options||t.onClick),onClick:()=>{var R;if(t.onClick)return t.onClick();t.options&&(S(t.options),p(t.name),(R=t.options)!=null&&R[0]&&h({page:d,name:t.name}))},children:[e("div",{className:"setting",children:i("div",{className:"setting-info",children:[e("div",{className:"title",children:t.name}),t.description&&e("div",{className:"description",children:t.description})]})}),t.notification&&e("div",{className:"settings-notification",children:"1"}),e("div",{className:"setting-value",children:t.input?e(Ue,{defaultValue:t.value,maxLength:t.input.maxLength,onChange:R=>{let D={...r,[t.input.type]:R.target.value};o.Settings.set(D)},onBlur:()=>{t.cb&&t.cb(r[t.input.type],r)}}):i(g,{children:[t.switch&&e(M,{checked:t.value,disabled:t.disabled??!1,onChange:()=>{t.onChange()}}),t.value&&e("div",{className:"value",children:t.isLink?e("a",{onClick:()=>window.invokeNative("openUrl",t.value),children:t.value}):t.value}),t.options&&e(U,{className:"chevron"})]})})]},y))},I))]}):d})})})}export{j as AppContext,Je as default};
