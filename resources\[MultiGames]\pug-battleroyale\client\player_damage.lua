RegisterNetEvent('Pug:client:ShowHitEffectSplit', function(position, armorDamage, healthDamage, wasFinalHit, usedWeapon, armorCracked)
	local weaponGroupType = GetWeapontypeGroup(usedWeapon)
	if Config.EnablePlayerDamageEffect then
		if weaponGroupType ~= -********** then
			ShowSplitHitEffect(position, armorDamage, healthDamage, wasFinalHit)
		end
	end
    
    if armorCracked then
        SendNUIMessage({
            action = 'play',
            audio = "armorcracked.ogg",
            volume = 0.5
        })
    end
end)


function ShowSplitHitEffect(position, armorDamage, healthDamage, wasFatal)
	local alphaValue = 255
	local isDrawingText = true

	CreateThread(function()
		if wasFatal then
			PugSoundPlay("killsoundeffect", 0.08)
			Wait(900)
		else
			Wait(350)
		end
		isDrawingText = false
	end)

	CreateThread(function()
		local wobbleTimer = 0.0
		local driftAngle = math.random() * math.pi * 2
		local driftSpeed = 0.005
		local verticalSpeed = 0.0075
		local floatingPos = vector3(position.x, position.y, position.z - 0.3)

		while isDrawingText do
			alphaValue = alphaValue - 3
			wobbleTimer = wobbleTimer + 0.05

			local offsetX = math.cos(driftAngle) * driftSpeed
			local offsetY = math.sin(driftAngle) * driftSpeed
			floatingPos = floatingPos + vector3(offsetX, offsetY, verticalSpeed)

			local wobbleX = math.sin(wobbleTimer * 3.0) * 0.003
			local wobbleY = math.cos(wobbleTimer * 2.5) * 0.003

			local screenVisible, screenX, screenY = World3dToScreen2d(
				floatingPos.x + wobbleX,
				floatingPos.y + wobbleY,
				floatingPos.z + 1.4
		 )

			if screenVisible then
				SetTextScale(0.7, 0.7)
				SetTextFont(2)
				SetTextProportional(true)
				SetTextOutline()
				SetTextCentre(true)

				if armorDamage and armorDamage > 0 then
					SetTextColour(0, 0, 255, alphaValue) 
					SetTextEntry("STRING")
					AddTextComponentString(tostring(math.floor(armorDamage)))
					DrawText(screenX - 0.01, screenY) 
				end

				if healthDamage and healthDamage > 0 then
					if wasFatal then
						SetTextColour(255, 102, 102, alphaValue)
					elseif healthDamage >= 50 then
						SetTextColour(255, 165, 0, alphaValue)
					else
						SetTextColour(255, 255, 255, alphaValue)
					end
					SetTextEntry("STRING")
					AddTextComponentString(tostring(math.floor(healthDamage)))
					DrawText(screenX + 0.01, screenY) 
				end
			end

			Wait(0)
		end
	end)

	if Config.PlayHitMarkerSound then
		SendNUIMessage({
			action = 'play',
			audio = "hitmarker.ogg",
			volume = 0.2
		})
	end
end

