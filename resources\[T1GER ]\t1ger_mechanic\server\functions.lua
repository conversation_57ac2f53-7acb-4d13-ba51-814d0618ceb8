CreateThread(function()
    while Framework == nil or _FW[Framework] == nil do 
        Wait(500)
    end
    Wait(1000)

    -- Register Useable Items:
    RegisterUseableItems()

    -- Version Check:
    VersionCheck()
end)

--- Checks resource version and name
function VersionCheck()
    local resourceName = "t1ger_mechanic"
    local currentName = GetCurrentResourceName()
    local currentVersion = GetResourceMetadata(currentName, "version", 0)

    -- Check if resource was renamed
    if currentName ~= resourceName then
        print(("^1[ERROR]^7: Resource name mismatch! Expected: '%s' but found: '%s'"):format(resourceName, currentName))
        StopResource(currentName)
        return
    end

    --- Compares current version with required version and returns boolean
    --- @param currentVersion string
    --- @param requiredVersion string
    --- @return boolean
    function IsVersionAtLeast(currentVersion, requiredVersion)
        local function parseVersion(version)
            local major, minor, patch = version:match("(%d+)%.(%d+)%.(%d+)")
            return tonumber(major), tonumber(minor), tonumber(patch)
        end

        local cMajor, cMinor, cPatch = parseVersion(currentVersion)
        local rMajor, rMinor, rPatch = parseVersion(requiredVersion)

        if cMajor > rMajor then
            return true
        elseif cMajor == rMajor then
            if cMinor > rMinor then
                return true
            elseif cMinor == rMinor then
                if cPatch >= rPatch then
                    return true
                end
            end
        end
        return false
    end

    -- Check version
    local versionURL = "https://raw.githubusercontent.com/t1ger-scripts/versions/main/" .. resourceName .. ".json"
    PerformHttpRequest(versionURL, function(status, data, headers)
        if status == 200 and data then
            local success, latestVersion = pcall(function()
                return json.decode(data)
            end)

            if success and latestVersion then
                if not IsVersionAtLeast(currentVersion, latestVersion) then
                    print(("^3[WARNING] An update is available for %s (current version: v%s)^7"):format(resourceName, currentVersion))
                    print(("^3[WARNING] Download latest version (v%s) from: https://portal.cfx.re/assets/granted-assets^7"):format(latestVersion))
                end
            end
        end
    end, "GET", "", {})
end

--- Creates a networked object and returns netId
--- @param model string
--- @param coords vector3
--- @return integer
function CreateNetworkedObject(model, coords)
    local object = CreateObjectNoOffset(GetHashKey(model), coords.x, coords.y, coords.z, true, true, false)
    
    while not DoesEntityExist(object) do 
        Wait(1)
    end

    local netId = NetworkGetNetworkIdFromEntity(object)

    return netId
end

--- Adds to mechanic online count
--- @param src number The player source/id
function PlusMechanicCount(src)
    if onlineMechanics.players[src] then return end

    onlineMechanics.count = (onlineMechanics.count + 1)
    onlineMechanics.players[src] = true

    if Config.Debug then 
        print("PlusMechanicCount | Mechanic Count: ", onlineMechanics.count)
    end
end

--- Removes from mechanic online count
--- @param src number The player source/id
function MinusMechanicCount(src)
    if not onlineMechanics.players[src] then return end

    onlineMechanics.count = (onlineMechanics.count - 1)
    onlineMechanics.players[src] = nil

    if Config.Debug then 
        print("MinusMechanicCount | Mechanic Count: ", onlineMechanics.count)
    end
end

--- Returns online mechanic count
--- @return integer #Online mechanic count
function GetOnlineMechanicCount()
    if Config.Debug then
        print("GetOnlineMechanicCount | Mechanic Count: ", onlineMechanics.count)
    end
    return onlineMechanics.count
end