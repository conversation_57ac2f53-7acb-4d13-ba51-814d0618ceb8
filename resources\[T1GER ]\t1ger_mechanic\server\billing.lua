-- Event to create a bill
RegisterNetEvent("t1ger_mechanic:server:createBill", function(shopId, bill)
    local src = source

    if type(shopId) ~= "number" then
        return error("[t1ger_mechanic:server:createBill] Invalid shopId type. Must be a number")
    end
    
    if type(bill) ~= "table" or next(bill) == nil then
        return error("[t1ger_mechanic:server:createBill] Invalid bill type. Must be a non empty table")
    end

    -- validate target player:
    local targetPlayer = _API.Player.GetFromId(bill.player)
    if not targetPlayer then
        error("[t1ger_mechanic:server:createBill] player was not found from id: "..bill.player)
    end

    -- send client event:
    TriggerClientEvent("t1ger_mechanic:client:sendBill", bill.player, shopId, bill, src)
end)

-- Event to pay bill
RegisterNetEvent("t1ger_mechanic:server:payBill", function(shopId, bill, senderId, accepted)
    local receiverId = source

    if type(shopId) ~= "number" then
        return error("[t1ger_mechanic:server:createBill] Invalid shopId type. Must be a number")
    end
    
    if type(bill) ~= "table" or next(bill) == nil then
        return error("[t1ger_mechanic:server:createBill] Invalid bill type. Must be a non empty table")
    end

    -- validate sender:
    local senderPlayer = _API.Player.GetFromId(senderId)
    if not senderPlayer then
        error("[t1ger_mechanic:server:createBill] sender player was not found from id: "..senderId)
    end

    -- validate receiver:
    local receiverPlayer = _API.Player.GetFromId(receiverId)
    if not receiverPlayer then
        error("[t1ger_mechanic:server:createBill] receiver player was not found from id: "..receiverId)
    end
    
    -- return if not accepted to pay bill:
    if not accepted then
        local playerName = string.format("[%s] %s", receiverId, GetPlayerName(receiverId))
        return _API.SendNotification(senderId, string.format(locale("notification.player_declined_bill"), playerName, Config.Currency..tostring(math.groupdigits(bill.amount))), "inform", {})
    end

    -- sender character name:
    local sender = _API.Player.GetCharacterName(senderId)

    -- receiver character name:
    local reciver = _API.Player.GetCharacterName(receiverId)

    -- check bank account
    local bankAccount = _API.Player.GetMoney(receiverId, "bank") 
    local playerName = string.format("[%s] %s", receiverId, GetPlayerName(receiverId))
    if bankAccount >= bill.amount then
        -- remove bank money:
        _API.Player.RemoveMoney(receiverId, bill.amount, "bank")
        
        -- add shop account money: 
        Shops[shopId]:AddMoney(bill.amount)

        -- create bill:
        Shops[shopId]:CreateBill(_API.Player.GetIdentifier(senderId), _API.Player.GetIdentifier(receiverId), bill.amount, bill.note)

        -- notify:
        _API.SendNotification(senderId, string.format(locale("notification.player_paid_bill"), playerName, Config.Currency..tostring(math.groupdigits(bill.amount))), "inform", {})
    else
        -- notify receiver:
        _API.SendNotification(receiverId, locale("notification.not_enough_bank_money"), "inform", {})

        -- notify sender:
        _API.SendNotification(senderId, string.format(locale("notification.player_not_afford_bill"), playerName, Config.Currency..tostring(math.groupdigits(bill.amount))), "inform", {})
    end
end)