function SetupBlip(shopId, blip)
    local self = {}

    self.shopId = shopId
    self.handle = nil
    self.enable = blip.enable
    self.coords = blip.coords
    self.sprite = blip.sprite
    self.color = blip.color
    self.scale = blip.scale
    self.display = blip.display
    self.name = Config.Shops[self.shopId].name

    function self.remove()
        if self.handle ~= nil and DoesBlipExist(self.handle) then
            RemoveBlip(self.handle)
        end
    end

    function self.add()
        self.remove()
        if self.enable then 
            self.handle = AddBlipForCoord(self.coords.x, self.coords.y, self.coords.z)
            SetBlipSprite(self.handle, self.sprite)
            SetBlipDisplay(self.handle, self.display)
            SetBlipScale(self.handle, self.scale)
            SetBlipColour(self.handle, self.color)
            SetBlipAsShortRange(self.handle, true)
            BeginTextCommandSetBlipName("STRING")
            AddTextComponentString(self.name)
            EndTextCommandSetBlipName(self.handle)
        end
    end

    self.add()

    return self
end

function SetupMarker(shopId, class, markerId)
    local self = {}

    self.shopId = shopId
    self.class = class
    self.markerId = markerId
    self.job = Config.Shops[self.shopId].job
    self.marker = Config.Shops[self.shopId].markers[class][markerId]
    if Config.Markers[class] == nil then
        print('^1ERROR CODE: 7542^0 ^6-^0 ^3docs.t1ger.net^0', 'Shop ID: '..self.shopId, 'Class: '..class)
    end
    self.cfg = Config.Markers[class]
    self.interact = self.cfg.interact
    self.blip = self.blip or nil
    self.point = self.point or nil

    function self.remove()
        if self.blip ~= nil and DoesBlipExist(self.blip) then
            RemoveBlip(self.blip)
        end
        if self.point ~= nil then 
            self.point.remove(self.point)
        end
    end

    local function CreateBlip(marker, cfg)
        local blip = AddBlipForCoord(marker.coords.x, marker.coords.y, marker.coords.z)
        SetBlipSprite(blip, cfg.sprite)
        SetBlipDisplay(blip, cfg.display)
        SetBlipScale(blip, cfg.scale)
        SetBlipColour(blip, cfg.color)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(marker.name)
        EndTextCommandSetBlipName(blip)
        return blip
    end

    local function CanInteract(point)
        if point.data.class == 'customs' or point.data.class == 'tuningbay' then
            if not IsPedInAnyVehicle(player, false) then
                if lib.isTextUIOpen() then 
                    lib.hideTextUI()
                    point.textUi = false
                end
                return false
            end
        else
            if point.data.class ~= 'duty' then
                if point.data.job.name ~= Core.GetJob().name then 
                    if lib.isTextUIOpen() then 
                        lib.hideTextUI()
                        point.textUi = false
                    end
                    return false
                end
            end
        end
        return true
    end

    local function CanAccess(this)
        if this.class == 'customs' then
            return true 
        elseif this.class == 'boss' and (isTunerBoss or playerTunerId == this.shopId) then
            if this.job.grades[tostring(Core.GetJob().grade)] and this.job.grades[tostring(Core.GetJob().grade)].isboss then 
                return true
            end
        elseif this.class == 'duty' and (playerTunerId == this.shopId) then 
            return true 
        elseif this.class == 'garage' or this.class == 'storage' or this.class == 'workbench' or this.class == 'laptop' or this.class == 'tuningbay' then
            if Core.GetJob().name == this.job.name then
                return true 
            end
        end
        return false
    end

    function self.add()
        -- remove if exists:
        self.remove()
        -- check access:
        if not CanAccess(self) then 
            return
        end
        -- blip:
        if self.marker.blip then 
            self.blip = CreateBlip(self.marker, self.cfg.blip)
        end
        -- ox lib point:
        self.point = lib.points.new({
            coords = self.marker.coords,
            distance = self.interact.drawDist,
            textUi = false,
            shopId = self.shopId,
            class = self.class,
            markerId = self.markerId,
            data = self,

            onEnter = function(point)
                if Config.Debug then 
                    print("Tuner Shop ID: "..self.shopId.." | Shop Job: "..self.job.name.." | Player Job: "..Core.GetJob().name.." ("..Core.GetJob().grade..")")
                end
                lib.hideTextUI()
                point.textUi = false
            end,
    
            onExit = function(point)
                lib.hideTextUI()
                point.textUi = false
                usingMenu = false
            end,
    
            nearby = function(point)
                if CanInteract(point) then
                    DrawMarker(self.marker.type, point.coords.x, point.coords.y, point.coords.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.35, 0.35, 0.35, self.marker.color.r, self.marker.color.g, self.marker.color.b, self.marker.color.a, self.marker.bobUpAndDown, self.marker.faceCamera, 0, true, false, false, false)
        
                    if point.currentDistance < self.interact.dist then
                        
                        local isOpen, currentText = lib.isTextUIOpen()
                        if isOpen and currentText ~= self.interact.textUi then
                            lib.showTextUI(self.interact.textUi, {position = self.interact.position, icon = self.interact.icon, style = self.interact.style})
                            point.textUi = true
                        elseif not isOpen then 
                            lib.showTextUI(self.interact.textUi, {position = self.interact.position, icon = self.interact.icon, style = self.interact.style})
                            point.textUi = true
                        end
        
                        if IsControlJustReleased(0, self.interact.keybind) then 
                            lib.hideTextUI()
                            usingMenu = true

                            if point.data.class == 'duty' then
                                OpenDutyMenu(point)
                            elseif point.data.class == 'boss' then
                                OpenBossMenu(point)
                            elseif point.data.class == 'garage' then 
                                OpenGarageMenu(point)
                            elseif point.data.class == 'storage' then
                                OpenStorage(point)
                            elseif point.data.class == 'workbench' then
                                OpenWorkbenchMenu(point)
                            elseif point.data.class == 'laptop' then 
                                OpenLaptopMenu(point)
                            elseif point.data.class == 'customs' then
                                if mods_menu.inUse == nil then
                                    OpenModsMainMenu(point)
                                end
                            elseif point.data.class == 'tuningbay' then 
                                OpenTuningBayMain(point)
                            else
                                usingMenu = false
                            end
                        end
                    else
                        local isOpen, currentText = lib.isTextUIOpen()
                        if isOpen and currentText == self.interact.textUi then
                            lib.hideTextUI()
                            point.textUi = false
                        end
                    end
                end
            end,
        })
    end

    function self.update()
        self.add()
    end

    self.add()

    return self
end

CreateShopMarkers = function(id, shop)
    if shop.markers ~= nil and next(shop.markers) then
        for class,marker in pairs(shop.markers) do
            for markerId,data in pairs(marker) do
                TriggerEvent('tuningsystem:client:createMarker', id, class, tostring(markerId)) 
            end
        end
    end
end

RegisterNetEvent('tuningsystem:client:createShopMarkers')
AddEventHandler('tuningsystem:client:createShopMarkers', function(shops)
    if next(shops) then
        for id,shop in pairs(shops) do
            CreateShopMarkers(id,shop)
        end
    end
end)

RegisterNetEvent('tuningsystem:client:createMarker')
AddEventHandler('tuningsystem:client:createMarker', function(shopId, class, markerId)
    if shop_markers[shopId] == nil or next(shop_markers[shopId]) == nil then shop_markers[shopId] = {} end
    if shop_markers[shopId][class] == nil or next(shop_markers[shopId][class]) == nil then shop_markers[shopId][class] = {} end
    if shop_markers[shopId][class][markerId] ~= nil then
        shop_markers[shopId][class][markerId].update()
    else
        shop_markers[shopId][class][markerId] = SetupMarker(shopId, class, markerId)
    end
end)

RegisterNetEvent('tuningsystem:client:deleteMarker')
AddEventHandler('tuningsystem:client:deleteMarker', function(shopId, class, markerId)
    if shop_markers[shopId] == nil or next(shop_markers[shopId]) == nil then return end
    if shop_markers[shopId][class] == nil or next(shop_markers[shopId][class]) == nil then return end
    if shop_markers[shopId][class][markerId] == nil or next(shop_markers[shopId][class][markerId]) == nil then return end
    shop_markers[shopId][class][markerId].remove()
    shop_markers[shopId][class][markerId] = nil
end)

RegisterNetEvent('tuningsystem:client:createShopBlips')
AddEventHandler('tuningsystem:client:createShopBlips', function(shops)
    if next(shops) then
        for id,v in pairs(shops) do
            TriggerEvent('tuningsystem:client:createShopBlip', id, v)
        end
    end
end)

RegisterNetEvent('tuningsystem:client:createShopBlip')
AddEventHandler('tuningsystem:client:createShopBlip', function(id, shop)
    local attempts = 1000
    while Config.Shops[id] == nil and attempts > 0 do 
        Wait(100)
        attempts = attempts - 1
    end
    if Config.Shops[id] == nil then 
        return print("couldn't find shop data for shop id: "..id)
    end
    shop_blips[id] = SetupBlip(id, shop.blip)
end)

RegisterNetEvent('tuningsystem:client:deleteShopBlip')
AddEventHandler('tuningsystem:client:deleteShopBlip', function(id)
    if shop_blips[id] == nil then return end
    shop_blips[id].remove()
end)

local workflowProgress = nil
ModOrderWorkflow = function(shopId, plate, order)
    local self = {}

    self.shopId = shopId
    self.plate = plate
    self.props = order.props
    self.taskList = order.taskList
    self.progress = 0

    self.DrawUI = function()
        SetWorkflowTitle(Config.ModOrder.WorkflowTitle:format(self.plate))
        OpenWorkflowUI()
        SetWorkflowData(self.taskList)
    end

    self.CloseUI = function()
        CloseWorkflowUI()
    end

    self.GetProgress = function()
        while workflowProgress == nil do 
            Wait(10)
        end
        self.progress = workflowProgress
        workflowProgress = nil
        return self.progress ~= nil and self.progress or 0
    end

    self.BeginWork = function()
        TriggerServerEvent('tuningsystem:server:modOrderTakenStatus', self.shopId, self.plate, true)
        self.DrawUI()
        local progress = self.GetProgress()
        if progress ~= nil and progress >= 100 then
            self.StopWork(true)
        end
    end

    self.StopWork = function(tasksComplete)
        if tasksComplete then
            TriggerServerEvent('tuningsystem:server:removeModOrder', self.shopId, self.plate)
            Core.Notification({
                title = '',
                message = Lang['mod_order_completed'],
                type = 'inform'
            })
        else
            TriggerServerEvent('tuningsystem:server:modOrderTakenStatus', self.shopId, self.plate, false)
        end
        self.CloseUI()
    end

    self.UpdateTask = function(plate, modName, props, modValue, scrollIndex)
        if self.plate == plate then
            local modUpdated = false
            for k,v in ipairs(self.taskList) do
                if v.modName == modName then
                    if (props[modName] == self.props[modName]) or (props[modName] == v.modValue) or (json.encode(props[modName]) == json.encode(self.props[modName])) then
                        self.taskList[v.id].completed = true
                        UpdateWorkflowTask(v.id, true)
                        TriggerServerEvent('tuningsystem:server:setTaskCompleted', self.shopId, self.plate, v.id, true)
                        modUpdated = true
                    elseif modName == 'extras' then
                        if v.modValue == modValue then
                            if v.scrollIndex == scrollIndex then 
                                self.taskList[v.id].completed = true
                                UpdateWorkflowTask(v.id, true)
                                TriggerServerEvent('tuningsystem:server:setTaskCompleted', self.shopId, self.plate, v.id, true)
                            else
                                self.taskList[v.id].completed = false
                                UpdateWorkflowTask(v.id, false)
                                TriggerServerEvent('tuningsystem:server:setTaskCompleted', self.shopId, self.plate, v.id, false)
                            end
                            modUpdated = true
                        end
                    else
                        self.taskList[v.id].completed = false
                        UpdateWorkflowTask(v.id, false)
                        TriggerServerEvent('tuningsystem:server:setTaskCompleted', self.shopId, self.plate, v.id, false)
                        modUpdated = true
                    end
                end
            end
            Wait(500)
            if modUpdated then 
                local progress = self.GetProgress()
                if progress ~= nil and progress >= 100 then
                    self.StopWork(true)
                end
                print("mod-order progress: ", progress.."%")
            end
        end
    end
    
    self.BeginWork()

    return self
end

RegisterNetEvent('tuningsystem:client:updateModOrderProgress')
AddEventHandler('tuningsystem:client:updateModOrderProgress', function(percentage)
    workflowProgress = percentage
end)