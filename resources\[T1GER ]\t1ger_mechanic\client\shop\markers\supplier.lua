-- Opens the supplier menu for the mechanic shop
---@param shopId number unique shop identifier
---@param markerId string unique marker identifier
function SupplierMain(shopId, markerId)
    local catalogue = {}

    for categoryId, category in ipairs(Config.Shop.Supplier.catalogue) do
        if type(category.items) == "table" and next(category.items) then
            local items = {}
            
            -- populate items table/options
            for itemName, itemData in pairs(category.items) do
                local itemLabel = _API.Inventory.GetItemLabel(itemName)
                if itemLabel then
                    items[#items+1] = {
                        title = string.format("%s - %s%s", itemLabel, Config.Currency, math.groupdigits(itemData.price)),
                        icon = itemData.icon,
                        onSelect = function()
                            OrderSelectedItem(shopId, markerId, categoryId, itemName, itemLabel, itemData.price)
                        end
                    }
                end
            end
            
            -- sort table by title
            table.sort(items, function(a, b)
                return a.title < b.title
            end)

            -- register context
            lib.registerContext({
                id = "mechanic_supplier_menu"..tostring(categoryId),
                title = category.title,
                menu = "mechanic_supplier_menu",
                options = items,
            })

            -- populate catalogue options:
            if type(items) == "table" and next(items) then 
                catalogue[#catalogue + 1] = {
                    title = category.title,
                    icon = category.icon,
                    arrow = true,
                    menu = "mechanic_supplier_menu"..tostring(categoryId)
                }
            end

        end
    end

    -- register context:
    lib.registerContext({
        id = "mechanic_supplier_menu",
        title = locale("menu_title.supplier_main"),
        options = catalogue
    })
    -- show context:
    lib.showContext("mechanic_supplier_menu")
end

---Function to order the selected item
---@param shopId number unique shop identifier
---@param markerId string unique marker identifier
---@param categoryId number index for category
---@param itemName string name of item to order
---@param itemLabel string label of item to order
---@param itemPrice number unit price of the item to order
function OrderSelectedItem(shopId, markerId, categoryId, itemName, itemLabel, itemPrice)
    -- unit price:
    local unitPrice = string.format("%s%s", Config.Currency, math.groupdigits(itemPrice))

    -- input dialog:
    local quantity = lib.inputDialog(string.format(locale("input_title.supplier_order", itemLabel, unitPrice)), {
        {
            type = "number",
            label = locale("input_label.supplier_quantity"),
            description = string.format(locale("input_description.supplier_quantity"), itemLabel),
            placeholder = 1,
            required = true,
            min = 1,
            max = Config.Shop.Supplier.maxQuantity or 20
        }
    })

    -- validate input
    if not quantity or type(quantity[1]) ~= "number" or quantity[1] <= 0 then 
        return lib.showContext("mechanic_supplier_menu"..tostring(categoryId))
    end

    -- calculate total price:
    local totalPrice = (quantity[1] * itemPrice)
    local formattedPrice = string.format("%s%s", Config.Currency, math.groupdigits(totalPrice))

    -- confirm input dialogue: 
    local confirm = lib.inputDialog(string.format(locale("input_title.supplier_confirmation"), quantity[1], itemLabel, formattedPrice), {
        {
            type = "checkbox",
            label = locale("input_label.supplier_confirm"),
            checked = false,
            required = true
        }
    })

    -- validate input:
    if not confirm or not confirm[1] then
        return lib.showContext("mechanic_supplier_menu"..tostring(categoryId))
    end

    -- select storage (if enabled):
    local storageId = nil
    if Config.Shop.Supplier.receiveInStash then
        local shopStorages = {}

        -- populate shop storages:
        for markerId, markerData in pairs(Shops[shopId].markers["storage"]) do
            local index = #shopStorages + 1
            shopStorages[index] = {label = markerData.name, value = index, markerId = markerId}
        end

        -- storage selection input dialogue:
        local selectedStorage = lib.inputDialog(locale("input_title.supplier_storage"), {
            {
                type = "select",
                label = locale("input_label.supplier_storage"),
                description = locale("input_description.supplier_storage"),
                required = true,
                options = shopStorages,
                clearable = true,
                searchable = true
            } 
        })

        -- validate input:
        if not selectedStorage or not selectedStorage[1] then 
            return lib.showContext("mechanic_supplier_menu"..tostring(categoryId))
        end

        -- store storageId:
        storageId = shopStorages[selectedStorage[1]].markerId
    end

    print("place order: ", itemLabel, quantity[1], totalPrice, storageId)
    TriggerServerEvent("t1ger_mechanic:server:supplierOrder", shopId, markerId, itemName, itemLabel, quantity[1], totalPrice, storageId)

    -- show main context:
    Wait(200)
    lib.showContext("mechanic_supplier_menu")
end