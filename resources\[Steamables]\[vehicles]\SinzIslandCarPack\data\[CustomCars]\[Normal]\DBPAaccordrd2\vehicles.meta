<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims />
  <InitDatas>
    <Item>
      <modelName>DBPAaccordrd2</modelName>
      <txdName>DBPAaccordrd2</txdName>
      <handlingId>DBPAaccordrd2</handlingId>
      <gameName>Accord</gameName>
      <vehicleMakeName>FIVEMCAR.COM</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>weap_xs_vehicle_weapons</ptfxAssetName>
      <audioNameHash>KANJO</audioNameHash>
      <layout>LAYOUT_STD_EXITFIXUP</layout>
      <coverBoundOffsets>PREMIER_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_NEAR</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="-0.040000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.-020000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.035000" z="-0.045000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="-0.035000" z="-0.045000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.020000" z="0.000000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.020000" z="0.000000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="-0.060000" z="-0.030000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.138000" y="0.270000" z="0.535000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.213000" z="0.415000" />
	  <FirstPersonMobilePhoneSeatIKOffset>
		<Item>
			<Offset x="0.136000" y="0.176000" z="0.435000" />
			<SeatIndex value="2" />
		</Item>
		<Item>
			<Offset x="0.136000" y="0.176000" z="0.435000" />
			<SeatIndex value="3" />
		</Item>
	  </FirstPersonMobilePhoneSeatIKOffset> 
      <PovCameraOffset x="0.000000" y="-0.150000" z="0.675000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="-0.020000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="-0.020000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.236500" />
      <wheelScaleRear value="0.236500" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.850000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.850000" />
      <damageOffsetScale value="0.600000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        70.000000
        140.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.87" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="999" />
      <flags>FLAG_AVERAGE_CAR FLAG_HAS_INTERIOR_EXTRAS </flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
	  <dashboardType>VDT_TAILGATER</dashboardType>
      <vehicleClass>VC_SEDAN</vehicleClass>
      <wheelType>VWT_SPORT</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData>
        <Item>STD_PREMIER_FRONT_LEFT</Item>
        <Item>STD_PREMIER_FRONT_RIGHT</Item>
		<Item>STD_PREMIER_REAR_LEFT</Item>
		<Item>STD_PREMIER_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
  </InitDatas>
  <txdRelationships>
		<Item>
			<parent>vehicles_poltax_interior</parent>
			<child>DBPAaccordrd2</child>
		</Item>
  </txdRelationships>
</CVehicleModelInfo__InitDataList>