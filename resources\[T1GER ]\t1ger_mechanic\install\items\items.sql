INSERT IGNORE INTO `items` (`name`, `label`) VALUES
    -- body parts
    ('t1ger_vehicledoor', 'Vehicle Door'),
    ('t1ger_vehiclehood', 'Vehicle Hood'),
    ('t1ger_vehicletrunk', 'Vehicle Trunk'),
    ('t1ger_vehiclewheel', 'Vehicle Wheel'),
    ('t1ger_vehiclewindow', 'Vehicle Window'),
    -- core parts
    ('t1ger_alternator', 'Alternator'),
    ('t1ger_brakes', 'Brakes'),
    ('t1ger_electricmotor', 'Electric Motor'),
    ('t1ger_evbattery', 'EV Battery'),
    ('t1ger_fuelinjector', 'Fuel Injector'),
    ('t1ger_powersteeringpump', 'Power Steering Pump'),
    ('t1ger_radiator', 'Radiator'),
    ('t1ger_transmission', 'Transmission'),
    -- serivce parts
    ('t1ger_airfilter', 'Air Filter'),
    ('t1ger_batterycoolant', 'Battery Coolant'),
    ('t1ger_brakefluid', 'Brake Fluid'),
    ('t1ger_brakepad', 'Brake Pads'),
    ('t1ger_coolant', 'Coolant'),
    ('t1ger_drivebelt', 'Drive Belt'),
    ('t1ger_fuelfilter', 'Fuel Filter'),
    ('t1ger_hvwiring', 'High Voltage Wiring'),
    ('t1ger_oilfilter', 'Oil Filter'),
    ('t1ger_sparkplugs', 'Spark Plugs'),
    ('t1ger_steeringfluid', 'Steering Fluid'),
    ('t1ger_tires', 'Tires'),
    ('t1ger_transmissionfluid', 'Transmission Fluid'),
    -- kits
    ('t1ger_repairkit', 'Repair Kit'),
    ('t1ger_repairkit_adv', 'Advanced Repair Kit'),
    ('t1ger_carjack', 'Car Jack'),
    ('t1ger_patchkit', 'Patch Kit'),
    ('t1ger_fuelcan', 'Fuel Can'),
    ('t1ger_jumpstarter', 'Jump Starter'),
    ('t1ger_repairkit_tire', 'Tire Repair Kit'),
    ('t1ger_diagnostictool', 'Vehicle Diagnostic Tool'),
    ('t1ger_servicebook', 'Service Book')
    -- prop emotes
    ('t1ger_roadcone', 'Road Cone'),
    ('t1ger_toolstrolley', 'Tools Trolley'),
    ('t1ger_toolbox', 'Tool Box'),
    ('t1ger_consign', 'Con Sign'),
    ('t1ger_roadbarrier', 'Road Barrier'),
    -- materials
    ('scrap_metal', 'Scrap Metal'),
	('steel', 'Steel'),
	('aluminium', 'Aluminium'),
	('plastic', 'Plastic'),
	('rubber', 'Rubber'),
	('electric_scrap', 'Electric Scrap'),
	('glass', 'Glass'),
	('copper', 'Copper'),
	('carbon_fiber', 'Carbon Fiber'),
	('brass', 'Brass'),
	('synthetic_oil', 'Synthetic Oil'),
	('acid', 'Synthetic Acid');