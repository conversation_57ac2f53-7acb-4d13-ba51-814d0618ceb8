[{"heist_name": "store robbery vanila unicorn", "stages": [{"requiredWeapons": [], "selectedDoorName": "", "rewardItemQuantity": "", "minigameCheckbox": false, "xpRemoveAmount": "0", "rewardItems": [{"rewardChance": 45, "maxAmount": 1, "itemName": "salvagedlockpick", "minAmount": 1}], "requiresItem": false, "quantity": "", "lootableItems": [], "penaltyForFailing": false, "delayRewardCheckbox": false, "pedCoords": "24.17, -1345.60, 28.50", "animation": "1", "penaltyChance": "", "propName": "", "heading": "270.00", "unlockTimeCheckbox": false, "requireWeaponInHand": false, "sendAlert": false, "stepNumber": 1, "rewardMoneyMax": "1", "type": "robabble_object", "xpNeededAmount": "0", "targetText": "Search Cabinet", "difficulty": "", "selectedWeapon": "", "requireSimultaneous": false, "hackType": "none", "rewardMoney": false, "rewardItem": true, "propHeading": "0.00", "rewardMoneyType": "cash", "removeXpOnHit": false, "isRequiredNextStage": false, "rewardDelayTime": "1", "isOptional": false, "rewardMoneyMin": "1", "itemName": "", "blackoutDuration": "1", "dropWalletWithPasscode": false, "createExplosion": false, "stepName": "Cabinet 1", "penaltyType": "", "lasers": [], "unlockTime": "", "triggersBlackout": false, "rewardItemMax": "1", "guards": [], "xpOptions": false, "minigameType": "", "xpRewardAmount": "0", "items": [], "rewardItemName": "salvagedlockpick", "targetVector": "24.76, -1345.64, 28.95", "rewardItemMin": "1"}, {"requiredWeapons": [], "selectedDoorName": "", "rewardItemQuantity": "", "minigameCheckbox": false, "xpRemoveAmount": "0", "rewardItems": [{"rewardChance": 45, "maxAmount": 1, "itemName": "salvagedlockpick", "minAmount": 1}], "requiresItem": false, "quantity": "", "lootableItems": [], "penaltyForFailing": false, "delayRewardCheckbox": false, "pedCoords": "24.10, -1346.98, 28.50", "animation": "2", "penaltyChance": "", "propName": "", "heading": "274.00", "unlockTimeCheckbox": false, "requireWeaponInHand": false, "sendAlert": false, "stepNumber": 2, "rewardMoneyMax": "1", "type": "robabble_object", "xpNeededAmount": "0", "targetText": "Search Cabinet", "difficulty": "", "selectedWeapon": "", "requireSimultaneous": false, "hackType": "none", "rewardMoney": false, "rewardItem": true, "propHeading": "0.00", "rewardMoneyType": "cash", "removeXpOnHit": false, "isRequiredNextStage": false, "rewardDelayTime": "1", "isOptional": false, "rewardMoneyMin": "1", "itemName": "", "blackoutDuration": "1", "dropWalletWithPasscode": false, "createExplosion": false, "stepName": "Cabinet 2", "penaltyType": "", "lasers": [], "unlockTime": "", "triggersBlackout": false, "rewardItemMax": "1", "guards": [], "xpOptions": false, "minigameType": "", "xpRewardAmount": "0", "items": [], "rewardItemName": "salvagedlockpick", "targetVector": "24.76, -1347.00, 28.96", "rewardItemMin": "1"}, {"requiredWeapons": [], "selectedDoorName": "", "rewardItemQuantity": "", "minigameCheckbox": false, "xpRemoveAmount": "0", "rewardItems": [{"rewardChance": 45, "maxAmount": 1, "itemName": "salvagedlockpick", "minAmount": 1}], "requiresItem": false, "quantity": "", "lootableItems": [], "penaltyForFailing": false, "delayRewardCheckbox": false, "pedCoords": "24.38, -1347.02, 28.50", "animation": "2", "penaltyChance": "", "propName": "", "heading": "91.00", "unlockTimeCheckbox": false, "requireWeaponInHand": false, "sendAlert": false, "stepNumber": 3, "rewardMoneyMax": "1", "type": "robabble_object", "xpNeededAmount": "0", "targetText": "Search Cabinet", "difficulty": "", "selectedWeapon": "", "requireSimultaneous": false, "hackType": "none", "rewardMoney": false, "rewardItem": true, "propHeading": "0.00", "rewardMoneyType": "cash", "removeXpOnHit": false, "isRequiredNextStage": false, "rewardDelayTime": "1", "isOptional": false, "rewardMoneyMin": "1", "itemName": "", "blackoutDuration": "1", "dropWalletWithPasscode": false, "createExplosion": false, "stepName": "Cabinet 3", "penaltyType": "", "lasers": [], "unlockTime": "", "triggersBlackout": false, "rewardItemMax": "1", "guards": [], "xpOptions": false, "minigameType": "", "xpRewardAmount": "0", "items": [], "rewardItemName": "salvagedlockpick", "targetVector": "23.78, -1347.01, 29.12", "rewardItemMin": "1"}, {"requiredWeapons": [], "selectedDoorName": "", "rewardItemQuantity": "", "minigameCheckbox": false, "xpRemoveAmount": "0", "rewardItems": [{"rewardChance": 80, "maxAmount": 70, "itemName": "bandsofnotes", "minAmount": 25}, {"rewardChance": 50, "maxAmount": 70, "itemName": "stacksofcash", "minAmount": 25}], "requiresItem": true, "quantity": "1", "lootableItems": [], "penaltyForFailing": false, "delayRewardCheckbox": false, "pedCoords": "24.31, -1347.28, 28.50", "animation": "19", "penaltyChance": "", "propName": "", "heading": "289.00", "unlockTimeCheckbox": false, "requireWeaponInHand": false, "sendAlert": true, "stepNumber": 4, "rewardMoneyMax": "1", "type": "robabble_object", "xpNeededAmount": "0", "targetText": "Lockpick Register", "difficulty": "", "selectedWeapon": "", "requireSimultaneous": false, "hackType": "inspired-lockpick", "rewardMoney": false, "rewardItem": true, "propHeading": "0.00", "rewardMoneyType": "cash", "removeXpOnHit": false, "isRequiredNextStage": false, "rewardDelayTime": "1", "isOptional": false, "rewardMoneyMin": "1", "itemName": "advancedlockpick", "blackoutDuration": "1", "dropWalletWithPasscode": false, "createExplosion": false, "stepName": "register 1", "penaltyType": "", "lasers": [], "unlockTime": "", "triggersBlackout": false, "rewardItemMax": "70", "guards": [], "xpOptions": false, "minigameType": "", "xpRewardAmount": "0", "items": [{"loseChance": 30, "itemQuantity": 1, "itemName": "advancedlockpick"}, {"loseChance": 40, "itemQuantity": 1, "itemName": "salvagedlockpick"}], "rewardItemName": "bandsofnotes", "targetVector": "24.98, -1347.31, 29.67", "rewardItemMin": "25"}, {"requiredWeapons": [], "selectedDoorName": "", "rewardItemQuantity": "", "minigameCheckbox": false, "xpRemoveAmount": "0", "rewardItems": [], "targetVector": "", "quantity": "", "lootableItems": [], "walletDropChance": 70, "penaltyForFailing": false, "delayRewardCheckbox": false, "animation": "", "pedCoords": "", "penaltyChance": "", "sendAlert": false, "propName": "", "heading": "", "unlockTimeCheckbox": false, "requireWeaponInHand": false, "stepNumber": 5, "showEnemyBlip": true, "difficulty": "", "type": "guards", "triggersBlackout": false, "targetText": "", "hackType": "s_m_m_chemsec_01", "selectedWeapon": "", "minigameType": "s_m_m_chemsec_01", "rewardMoneyMax": "", "rewardMoney": false, "rewardItem": false, "propHeading": "", "rewardMoneyType": "", "removeXpOnHit": false, "isRequiredNextStage": false, "rewardDelayTime": "", "isOptional": false, "rewardMoneyMin": "", "itemName": "", "rewardItemMin": "", "dropWalletWithPasscode": true, "createExplosion": false, "lasers": [], "penaltyType": "", "stepName": "guard 1", "unlockTime": "", "xpNeededAmount": "0", "rewardItemMax": "", "guards": [{"heading": 85, "weapon": "weapon_pumpshotgun_mk2", "guardModel": "s_m_m_chemsec_01", "guardPosition": [33.63, -1351.19, 28.33]}], "xpOptions": false, "blackoutDuration": "", "xpRewardAmount": "0", "items": [], "rewardItemName": "", "requiresItem": false, "requireSimultaneous": false}, {"requiredWeapons": [], "selectedDoorName": "", "rewardItemQuantity": "", "minigameCheckbox": false, "xpRemoveAmount": "0", "rewardItems": [{"rewardChance": 80, "maxAmount": 70, "itemName": "bandsofnotes", "minAmount": 25}, {"rewardChance": 50, "maxAmount": 70, "itemName": "stacksofcash", "minAmount": 25}], "requiresItem": true, "quantity": "1", "lootableItems": [], "penaltyForFailing": false, "delayRewardCheckbox": false, "pedCoords": "24.40, -1345.04, 28.50", "animation": "19", "penaltyChance": "", "propName": "", "heading": "299.00", "unlockTimeCheckbox": false, "requireWeaponInHand": false, "sendAlert": true, "stepNumber": 6, "rewardMoneyMax": "1", "type": "robabble_object", "xpNeededAmount": "0", "targetText": "Lockpick Register", "difficulty": "", "selectedWeapon": "", "requireSimultaneous": false, "hackType": "inspired-lockpick", "rewardMoney": false, "rewardItem": true, "propHeading": "0.00", "rewardMoneyType": "cash", "removeXpOnHit": false, "isRequiredNextStage": false, "rewardDelayTime": "1", "isOptional": false, "rewardMoneyMin": "1", "itemName": "advancedlockpick", "blackoutDuration": "1", "dropWalletWithPasscode": false, "createExplosion": false, "stepName": "register 2", "penaltyType": "", "lasers": [], "unlockTime": "", "triggersBlackout": false, "rewardItemMax": "70", "guards": [], "xpOptions": false, "minigameType": "", "xpRewardAmount": "0", "items": [{"loseChance": 30, "itemQuantity": 1, "itemName": "advancedlockpick"}, {"loseChance": 40, "itemQuantity": 1, "itemName": "salvagedlockpick"}], "rewardItemName": "bandsofnotes", "targetVector": "24.96, -1344.91, 29.67", "rewardItemMin": "25"}, {"requiredWeapons": [], "selectedDoorName": "", "rewardItemQuantity": "", "minigameCheckbox": false, "xpRemoveAmount": "0", "rewardItems": [], "targetVector": "", "quantity": "", "lootableItems": [], "walletDropChance": 70, "penaltyForFailing": false, "delayRewardCheckbox": false, "animation": "", "pedCoords": "", "penaltyChance": "", "sendAlert": false, "propName": "", "heading": "", "unlockTimeCheckbox": false, "requireWeaponInHand": false, "stepNumber": 7, "showEnemyBlip": true, "difficulty": "", "type": "guards", "triggersBlackout": false, "targetText": "", "hackType": "s_m_m_chemsec_01", "selectedWeapon": "", "minigameType": "s_m_m_chemsec_01", "rewardMoneyMax": "", "rewardMoney": false, "rewardItem": false, "propHeading": "", "rewardMoneyType": "", "removeXpOnHit": false, "isRequiredNextStage": false, "rewardDelayTime": "", "isOptional": false, "rewardMoneyMin": "", "itemName": "", "rewardItemMin": "", "dropWalletWithPasscode": true, "createExplosion": false, "lasers": [], "penaltyType": "", "stepName": "guard 2", "unlockTime": "", "xpNeededAmount": "0", "rewardItemMax": "", "guards": [{"heading": 315, "weapon": "weapon_appistol", "guardModel": "s_m_m_chemsec_01", "guardPosition": [24.63, -1351.65, 28.33]}], "xpOptions": false, "blackoutDuration": "", "xpRewardAmount": "0", "items": [], "rewardItemName": "", "requiresItem": false, "requireSimultaneous": false}, {"requiredWeapons": [], "selectedDoorName": "store vu", "rewardItemQuantity": "", "minigameCheckbox": false, "xpRemoveAmount": "0", "rewardItems": [], "targetVector": "25.79, -1341.98, 30.05", "quantity": "", "lootableItems": [], "doorId": "store vu", "penaltyForFailing": false, "isRequiredNextStage": false, "animation": "8", "pedCoords": "25.38, -1342.39, 28.50", "penaltyChance": "100", "lasers": [], "propName": "prop_ld_keypad_01b", "heading": "331.00", "unlockTimeCheckbox": true, "requireWeaponInHand": false, "stepNumber": 8, "rewardMoneyType": "", "difficulty": "", "type": "hackable_door", "triggersBlackout": false, "targetText": "Enter Code", "hackType": "none", "selectedWeapon": "", "minigameType": "", "rewardMoneyMax": "", "rewardMoney": false, "rewardItem": false, "propHeading": "352.00", "delayRewardCheckbox": false, "removeXpOnHit": false, "rewardDelayTime": "", "rewardMoneyMin": "", "isOptional": false, "itemName": "", "doorPosition": "24.35, -1341.91, 29.63", "sendAlert": true, "dropWalletWithPasscode": false, "createExplosion": false, "stepName": "hackable door", "penaltyType": "", "xpNeededAmount": "0", "unlockTime": "5", "blackoutDuration": "1", "rewardItemMax": "", "guards": [], "xpOptions": false, "xpRewardAmount": "0", "rewardItemMin": "", "items": [], "rewardItemName": "", "requiresItem": false, "requireSimultaneous": false}, {"requiredWeapons": [], "selectedDoorName": "", "rewardItemQuantity": "", "minigameCheckbox": false, "xpRemoveAmount": "0", "rewardItems": [], "requiresItem": false, "quantity": "", "lootableItems": [], "penaltyForFailing": false, "delayRewardCheckbox": false, "pedCoords": "", "animation": "", "penaltyChance": "", "propName": "hei_v_ilev_bk_safegate_pris", "heading": "", "unlockTimeCheckbox": false, "requireWeaponInHand": false, "sendAlert": false, "stepNumber": 9, "rewardMoneyMax": "", "type": "custom_door", "xpNeededAmount": "0", "targetText": "store vu", "difficulty": "", "selectedWeapon": "", "requireSimultaneous": false, "hackType": "", "rewardMoney": false, "rewardItem": false, "propHeading": "180.00", "rewardMoneyType": "", "removeXpOnHit": false, "isRequiredNextStage": false, "rewardDelayTime": "", "isOptional": false, "rewardMoneyMin": "", "itemName": "", "blackoutDuration": "", "dropWalletWithPasscode": false, "createExplosion": false, "stepName": "custom door", "penaltyType": "", "lasers": [], "unlockTime": "", "triggersBlackout": false, "rewardItemMax": "", "guards": [], "xpOptions": false, "minigameType": "", "xpRewardAmount": "0", "items": [], "rewardItemName": "", "targetVector": "24.35, -1341.91, 29.63", "rewardItemMin": ""}, {"requiredWeapons": [], "selectedDoorName": "", "rewardItemQuantity": "", "minigameCheckbox": false, "xpRemoveAmount": "0", "rewardItems": [], "requiresItem": false, "quantity": "", "lootableItems": [], "penaltyForFailing": false, "delayRewardCheckbox": false, "pedCoords": "30.95, -1340.03, 28.50", "animation": "h4_prop_h4_safe_01a", "penaltyChance": "", "propName": "h4_prop_h4_safe_01a", "heading": "271.00", "unlockTimeCheckbox": false, "requireWeaponInHand": false, "sendAlert": true, "stepNumber": 10, "rewardMoneyMax": "1", "type": "robabble_object", "xpNeededAmount": "0", "targetText": "Open Safe", "difficulty": "", "selectedWeapon": "", "requireSimultaneous": false, "hackType": "wire_cut_game", "rewardMoney": false, "rewardItem": false, "propHeading": "271.00", "rewardMoneyType": "cash", "removeXpOnHit": false, "isRequiredNextStage": false, "rewardDelayTime": "1", "isOptional": false, "rewardMoneyMin": "1", "itemName": "", "blackoutDuration": "1", "dropWalletWithPasscode": false, "createExplosion": false, "stepName": "Big safe", "penaltyType": "", "lasers": [], "unlockTime": "", "triggersBlackout": false, "rewardItemMax": "", "guards": [], "xpOptions": false, "minigameType": "", "xpRewardAmount": "0", "items": [], "rewardItemName": "", "targetVector": "30.95, -1340.03, 28.50", "rewardItemMin": ""}, {"requiredWeapons": [], "selectedDoorName": "", "rewardItemQuantity": "", "minigameCheckbox": false, "xpRemoveAmount": "0", "rewardItems": [{"rewardChance": 40, "maxAmount": 1, "itemName": "drill", "minAmount": 1}], "requiresItem": false, "quantity": "", "lootableItems": [], "penaltyForFailing": false, "delayRewardCheckbox": false, "pedCoords": "29.93, -1339.94, 28.50", "animation": "16", "penaltyChance": "", "propName": "ch_prop_ch_heist_drill", "heading": "248.00", "unlockTimeCheckbox": false, "requireWeaponInHand": false, "sendAlert": false, "stepNumber": 11, "rewardMoneyMax": "1", "type": "robabble_object", "xpNeededAmount": "0", "targetText": "<PERSON><PERSON>", "difficulty": "", "selectedWeapon": "", "requireSimultaneous": false, "hackType": "none", "rewardMoney": false, "rewardItem": true, "propHeading": "324.00", "rewardMoneyType": "cash", "removeXpOnHit": false, "isRequiredNextStage": true, "rewardDelayTime": "1", "isOptional": false, "rewardMoneyMin": "1", "itemName": "", "blackoutDuration": "1", "dropWalletWithPasscode": false, "createExplosion": false, "stepName": "drill", "penaltyType": "", "lasers": [], "unlockTime": "", "triggersBlackout": false, "rewardItemMax": "1", "guards": [], "xpOptions": false, "minigameType": "", "xpRewardAmount": "0", "items": [], "rewardItemName": "drill", "targetVector": "30.95, -1339.98, 28.77", "rewardItemMin": "1"}, {"requiredWeapons": [], "selectedDoorName": "", "rewardItemQuantity": "", "minigameCheckbox": false, "rewardDelayTime": "", "rewardItems": [], "targetVector": "", "xpRewardAmount": "0", "lootableItems": [], "animation": "", "rewardMoneyType": "", "requiresItem": false, "penaltyForFailing": false, "penaltyChance": "", "xpNeededAmount": "0", "pedCoords": "", "stepNumber": 12, "rewardItemMin": "", "propName": "", "heading": "", "endingHeading": "186.00", "requireWeaponInHand": false, "difficulty": "", "hackType": "", "delayRewardCheckbox": false, "type": "ending_robbery", "triggersBlackout": false, "targetText": "", "rewardMoneyMax": "", "selectedWeapon": "", "requireSimultaneous": false, "rewardMoneyMin": "", "rewardMoney": false, "rewardItem": false, "propHeading": "", "isRequiredNextStage": false, "removeXpOnHit": false, "xpRemoveAmount": "0", "itemName": "", "isOptional": false, "endingTargetVector": "-833.11, -1281.65, 4.00", "lasers": [], "sendAlert": false, "dropWalletWithPasscode": false, "createExplosion": false, "minigameType": "", "penaltyType": "", "unlockTimeCheckbox": false, "unlockTime": "", "maxValue": 100, "rewardItemMax": "", "guards": [], "xpOptions": false, "quantity": "", "minValue": 0, "items": [], "rewardItemName": "", "blackoutDuration": "", "stepName": "ending robbery"}, {"requiredWeapons": [], "selectedDoorName": "", "rewardItemQuantity": "", "minigameCheckbox": false, "xpRemoveAmount": "0", "rewardItems": [], "requiresItem": false, "quantity": "", "lootableItems": [], "penaltyForFailing": false, "delayRewardCheckbox": false, "pedCoords": "30.34, -1339.98, 28.50", "animation": "15", "penaltyChance": "", "propName": "bkr_prop_bkr_cashpile_04", "heading": "235.00", "unlockTimeCheckbox": false, "requireWeaponInHand": false, "sendAlert": false, "stepNumber": 13, "rewardMoneyMax": "700", "type": "robabble_object", "xpNeededAmount": "0", "targetText": "Grab Money", "difficulty": "", "selectedWeapon": "", "requireSimultaneous": false, "hackType": "none", "rewardMoney": true, "rewardItem": false, "propHeading": "268.00", "rewardMoneyType": "cash", "removeXpOnHit": false, "isRequiredNextStage": true, "rewardDelayTime": "1", "isOptional": false, "rewardMoneyMin": "200", "itemName": "", "blackoutDuration": "1", "dropWalletWithPasscode": false, "createExplosion": false, "stepName": "Safe Money", "penaltyType": "", "lasers": [], "unlockTime": "", "triggersBlackout": false, "rewardItemMax": "", "guards": [], "xpOptions": false, "minigameType": "", "xpRewardAmount": "0", "items": [], "rewardItemName": "", "targetVector": "30.98, -1340.03, 29.53", "rewardItemMin": ""}, {"requiredWeapons": [], "selectedDoorName": "", "rewardItemQuantity": "", "minigameCheckbox": false, "xpRemoveAmount": "0", "rewardItems": [{"rewardChance": 10, "maxAmount": 1, "itemName": "unmarkedsimcard", "minAmount": 1}, {"rewardChance": 100, "maxAmount": 5, "itemName": "diamondring", "minAmount": 1}, {"rewardChance": 100, "maxAmount": 7, "itemName": "goldbracelet", "minAmount": 1}, {"rewardChance": 1, "maxAmount": 3, "itemName": "goldbar", "minAmount": 1}, {"rewardChance": 100, "maxAmount": 25, "itemName": "bandsofnotes", "minAmount": 5}], "requiresItem": false, "quantity": "", "lootableItems": [], "penaltyForFailing": false, "delayRewardCheckbox": true, "pedCoords": "28.15, -1339.55, 28.50", "animation": "2", "penaltyChance": "", "propName": "", "heading": "0.00", "unlockTimeCheckbox": false, "requireWeaponInHand": false, "sendAlert": true, "stepNumber": 14, "rewardMoneyMax": "1", "type": "robabble_object", "xpNeededAmount": "0", "targetText": "Open Safe", "difficulty": "", "selectedWeapon": "", "requireSimultaneous": false, "hackType": "none", "rewardMoney": false, "rewardItem": true, "propHeading": "0.00", "rewardMoneyType": "cash", "removeXpOnHit": false, "isRequiredNextStage": true, "rewardDelayTime": "20", "isOptional": false, "rewardMoneyMin": "1", "itemName": "", "blackoutDuration": "1", "dropWalletWithPasscode": false, "createExplosion": false, "stepName": "small safe", "penaltyType": "", "lasers": [], "unlockTime": "", "triggersBlackout": false, "rewardItemMax": "1", "guards": [], "xpOptions": false, "minigameType": "", "xpRewardAmount": "0", "items": [], "rewardItemName": "unmarkedsimcard", "targetVector": "28.16, -1338.88, 28.97", "rewardItemMin": "1"}], "settings": {"robberyCooldown": "25", "policeJobs": ["police", "leo", "lspd", "sasp", "ranger", "bcso", "trooper"], "policeAlert": "Armed store robbery", "blipColor": 0, "minutesUntilRobbery": "", "alertPriority": 1, "policeAlertHeader": "Armed store robbery", "cooldownHeists": ["store above big bank", "store near casino", "store burgershot", "store mirror park", "store south side"], "alertCode": "10-90", "blipScale": 1, "blipSprite": 59, "alertSound1": "robberysound", "policeRequired": "", "cooldownMinutes": 25, "alertIcon": "fas fa-bell", "blipFlash": false}}, {"heist_name": "store above big bank", "stages": [{"rewardDelayTime": "1", "selectedDoorName": "", "delayRewardCheckbox": false, "xpOptions": false, "penaltyType": "", "minigameCheckbox": false, "blackoutDuration": "1", "dropWalletWithPasscode": false, "heading": "255.00", "sendAlert": false, "items": [], "rewardMoneyMin": "1", "penaltyChance": "", "unlockTime": "", "selectedWeapon": "", "stepName": "Cabinet 1", "type": "robabble_object", "rewardItemMax": "1", "rewardItemQuantity": "", "requireSimultaneous": false, "unlockTimeCheckbox": false, "requiredWeapons": [], "pedCoords": "372.64, 328.06, 102.57", "animation": "2", "propHeading": "0.00", "xpRewardAmount": "0", "requiresItem": false, "minigameType": "", "requireWeaponInHand": false, "removeXpOnHit": false, "xpRemoveAmount": "0", "stepNumber": 1, "rewardItemName": "salvagedlockpick", "rewardMoneyMax": "1", "targetVector": "373.23, 327.92, 103.10", "isOptional": false, "lootableItems": [], "quantity": "", "rewardMoneyType": "cash", "difficulty": "", "guards": [], "propName": "", "createExplosion": false, "isRequiredNextStage": false, "lasers": [], "hackType": "none", "rewardMoney": false, "targetText": "Search Cabinet", "rewardItemMin": "1", "penaltyForFailing": false, "rewardItem": true, "rewardItems": [{"maxAmount": 1, "itemName": "salvagedlockpick", "minAmount": 1, "rewardChance": 45}], "itemName": "", "triggersBlackout": false, "xpNeededAmount": "0"}, {"rewardDelayTime": "1", "selectedDoorName": "", "delayRewardCheckbox": false, "xpOptions": false, "penaltyType": "", "minigameCheckbox": false, "blackoutDuration": "1", "dropWalletWithPasscode": false, "heading": "243.00", "sendAlert": false, "items": [], "rewardMoneyMin": "1", "penaltyChance": "", "unlockTime": "", "selectedWeapon": "", "stepName": "Cabinet 2", "type": "robabble_object", "rewardItemMax": "1", "rewardItemQuantity": "", "requireSimultaneous": false, "unlockTimeCheckbox": false, "requiredWeapons": [], "pedCoords": "372.32, 327.04, 102.57", "animation": "2", "propHeading": "0.00", "xpRewardAmount": "0", "requiresItem": false, "minigameType": "", "requireWeaponInHand": false, "removeXpOnHit": false, "xpRemoveAmount": "0", "stepNumber": 2, "rewardItemName": "salvagedlockpick", "rewardMoneyMax": "1", "targetVector": "372.91, 326.66, 103.08", "isOptional": false, "lootableItems": [], "quantity": "", "rewardMoneyType": "cash", "difficulty": "", "guards": [], "propName": "", "createExplosion": false, "isRequiredNextStage": false, "lasers": [], "hackType": "none", "rewardMoney": false, "targetText": "Search Cabinet", "rewardItemMin": "1", "penaltyForFailing": false, "rewardItem": true, "rewardItems": [{"maxAmount": 1, "itemName": "salvagedlockpick", "minAmount": 1, "rewardChance": 45}], "itemName": "", "triggersBlackout": false, "xpNeededAmount": "0"}, {"rewardDelayTime": "1", "selectedDoorName": "", "delayRewardCheckbox": false, "xpOptions": false, "penaltyType": "", "minigameCheckbox": false, "blackoutDuration": "1", "dropWalletWithPasscode": false, "heading": "85.00", "sendAlert": false, "items": [], "rewardMoneyMin": "1", "penaltyChance": "", "unlockTime": "", "selectedWeapon": "", "stepName": "Cabinet 3", "type": "robabble_object", "rewardItemMax": "1", "rewardItemQuantity": "", "requireSimultaneous": false, "unlockTimeCheckbox": false, "requiredWeapons": [], "pedCoords": "372.54, 326.69, 102.57", "animation": "2", "propHeading": "0.00", "xpRewardAmount": "0", "requiresItem": false, "minigameType": "", "requireWeaponInHand": false, "removeXpOnHit": false, "xpRemoveAmount": "0", "stepNumber": 3, "rewardItemName": "salvagedlockpick", "rewardMoneyMax": "1", "targetVector": "371.97, 326.91, 103.14", "isOptional": false, "lootableItems": [], "quantity": "", "rewardMoneyType": "cash", "difficulty": "", "guards": [], "propName": "", "createExplosion": false, "isRequiredNextStage": false, "lasers": [], "hackType": "none", "rewardMoney": false, "targetText": "Search Cabinet", "rewardItemMin": "1", "penaltyForFailing": false, "rewardItem": true, "rewardItems": [{"maxAmount": 1, "itemName": "salvagedlockpick", "minAmount": 1, "rewardChance": 45}], "itemName": "", "triggersBlackout": false, "xpNeededAmount": "0"}, {"rewardDelayTime": "1", "selectedDoorName": "", "delayRewardCheckbox": false, "xpOptions": false, "penaltyType": "", "minigameCheckbox": false, "blackoutDuration": "1", "dropWalletWithPasscode": false, "heading": "277.00", "sendAlert": true, "items": [{"loseChance": 30, "itemName": "advancedlockpick", "itemQuantity": 1}, {"loseChance": 40, "itemName": "salvagedlockpick", "itemQuantity": 1}], "rewardMoneyMin": "1", "penaltyChance": "", "unlockTime": "", "selectedWeapon": "", "stepName": "Register 1", "type": "robabble_object", "rewardItemMax": "70", "rewardItemQuantity": "", "requireSimultaneous": false, "unlockTimeCheckbox": false, "requiredWeapons": [], "pedCoords": "372.43, 326.50, 102.57", "animation": "19", "propHeading": "0.00", "xpRewardAmount": "0", "requiresItem": true, "minigameType": "", "requireWeaponInHand": false, "removeXpOnHit": false, "xpRemoveAmount": "0", "stepNumber": 4, "rewardItemName": "bandsofnotes", "rewardMoneyMax": "1", "targetVector": "373.07, 326.32, 103.74", "isOptional": false, "lootableItems": [], "quantity": "1", "rewardMoneyType": "cash", "difficulty": "", "guards": [], "propName": "", "createExplosion": false, "isRequiredNextStage": false, "lasers": [], "hackType": "inspired-lockpick", "rewardMoney": false, "targetText": "Lockpick Register", "rewardItemMin": "20", "penaltyForFailing": false, "rewardItem": true, "rewardItems": [{"maxAmount": 70, "itemName": "bandsofnotes", "minAmount": 20, "rewardChance": 80}, {"maxAmount": 70, "itemName": "stacksofcash", "minAmount": 20, "rewardChance": 50}], "itemName": "advancedlockpick", "triggersBlackout": false, "xpNeededAmount": "0"}, {"rewardDelayTime": "", "selectedDoorName": "", "delayRewardCheckbox": false, "xpOptions": false, "penaltyType": "", "minigameCheckbox": false, "walletDropChance": 70, "rewardItemMin": "", "dropWalletWithPasscode": true, "heading": "", "sendAlert": false, "items": [], "rewardMoneyMin": "", "penaltyChance": "", "unlockTime": "", "selectedWeapon": "", "stepName": "register 1 guard", "type": "guards", "rewardItemMax": "", "requiredWeapons": [], "rewardItemQuantity": "", "requireSimultaneous": false, "unlockTimeCheckbox": false, "propHeading": "", "pedCoords": "", "animation": "", "triggersBlackout": false, "xpRewardAmount": "0", "requiresItem": false, "minigameType": "s_m_m_chemsec_01", "requireWeaponInHand": false, "rewardMoneyType": "", "difficulty": "", "rewardItemName": "", "isOptional": false, "rewardMoneyMax": "", "xpNeededAmount": "0", "stepNumber": 5, "lootableItems": [], "removeXpOnHit": false, "targetVector": "", "xpRemoveAmount": "0", "guards": [{"guardPosition": [378.65, 320.67, 102.38], "guardModel": "s_m_m_chemsec_01", "weapon": "weapon_heavypistol", "heading": 48}], "propName": "", "lasers": [], "isRequiredNextStage": false, "quantity": "", "hackType": "s_m_m_chemsec_01", "rewardMoney": false, "targetText": "", "createExplosion": false, "penaltyForFailing": false, "rewardItem": false, "rewardItems": [], "itemName": "", "blackoutDuration": "", "showEnemyBlip": true}, {"rewardDelayTime": "1", "selectedDoorName": "", "delayRewardCheckbox": false, "xpOptions": false, "penaltyType": "", "minigameCheckbox": false, "blackoutDuration": "1", "dropWalletWithPasscode": false, "heading": "283.00", "sendAlert": true, "items": [{"loseChance": 30, "itemName": "advancedlockpick", "itemQuantity": 1}, {"loseChance": 40, "itemName": "salvagedlockpick", "itemQuantity": 1}], "rewardMoneyMin": "1", "penaltyChance": "", "unlockTime": "", "selectedWeapon": "", "stepName": "register 2", "type": "robabble_object", "rewardItemMax": "70", "rewardItemQuantity": "", "requireSimultaneous": false, "unlockTimeCheckbox": false, "requiredWeapons": [], "pedCoords": "373.00, 328.72, 102.57", "animation": "19", "propHeading": "0.00", "xpRewardAmount": "0", "requiresItem": true, "minigameType": "", "requireWeaponInHand": false, "removeXpOnHit": false, "xpRemoveAmount": "0", "stepNumber": 6, "rewardItemName": "bandsofnotes", "rewardMoneyMax": "1", "targetVector": "373.68, 328.52, 103.74", "isOptional": false, "lootableItems": [], "quantity": "1", "rewardMoneyType": "cash", "difficulty": "", "guards": [], "propName": "", "createExplosion": false, "isRequiredNextStage": false, "lasers": [], "hackType": "none", "rewardMoney": false, "targetText": "Lockpick Register", "rewardItemMin": "20", "penaltyForFailing": false, "rewardItem": true, "rewardItems": [{"maxAmount": 70, "itemName": "bandsofnotes", "minAmount": 20, "rewardChance": 80}, {"maxAmount": 70, "itemName": "stacksofcash", "minAmount": 20, "rewardChance": 50}], "itemName": "advancedlockpick", "triggersBlackout": false, "xpNeededAmount": "0"}, {"rewardDelayTime": "", "selectedDoorName": "", "delayRewardCheckbox": false, "xpOptions": false, "penaltyType": "", "minigameCheckbox": false, "walletDropChance": 70, "rewardItemMin": "", "dropWalletWithPasscode": true, "heading": "", "sendAlert": false, "items": [], "rewardMoneyMin": "", "penaltyChance": "", "unlockTime": "", "selectedWeapon": "", "stepName": "register 2 guard", "type": "guards", "rewardItemMax": "", "requiredWeapons": [], "rewardItemQuantity": "", "requireSimultaneous": false, "unlockTimeCheckbox": false, "propHeading": "", "pedCoords": "", "animation": "", "triggersBlackout": false, "xpRewardAmount": "0", "requiresItem": false, "minigameType": "s_m_m_chemsec_01", "requireWeaponInHand": false, "rewardMoneyType": "", "difficulty": "", "rewardItemName": "", "isOptional": false, "rewardMoneyMax": "", "xpNeededAmount": "0", "stepNumber": 7, "lootableItems": [], "removeXpOnHit": false, "targetVector": "", "xpRemoveAmount": "0", "guards": [{"guardPosition": [374.14, 322.23, 102.48], "guardModel": "s_m_m_chemsec_01", "weapon": "weapon_pistol_mk2", "heading": 258}], "propName": "", "lasers": [], "isRequiredNextStage": false, "quantity": "", "hackType": "s_m_m_chemsec_01", "rewardMoney": false, "targetText": "", "createExplosion": false, "penaltyForFailing": false, "rewardItem": false, "rewardItems": [], "itemName": "", "blackoutDuration": "", "showEnemyBlip": true}, {"rewardItemName": "", "selectedDoorName": "above big bank door", "delayRewardCheckbox": false, "xpOptions": false, "penaltyType": "", "minigameCheckbox": false, "rewardItemMin": "", "dropWalletWithPasscode": false, "heading": "329.00", "sendAlert": true, "requiredWeapons": [], "items": [], "rewardMoneyMin": "", "penaltyChance": "100", "unlockTime": "15", "selectedWeapon": "", "stepName": "door", "type": "hackable_door", "xpRemoveAmount": "0", "hackType": "none", "rewardItemQuantity": "", "requireSimultaneous": false, "unlockTimeCheckbox": true, "pedCoords": "374.63, 330.90, 102.57", "propHeading": "346.00", "animation": "8", "rewardDelayTime": "", "xpRewardAmount": "0", "requiresItem": false, "minigameType": "", "requireWeaponInHand": false, "rewardItemMax": "", "difficulty": "", "isOptional": false, "stepNumber": 8, "doorPosition": "373.77, 331.73, 103.72", "xpNeededAmount": "0", "rewardMoneyType": "", "lootableItems": [], "triggersBlackout": false, "rewardMoneyMax": "", "targetVector": "375.12, 331.28, 104.25", "guards": [], "propName": "prop_ld_keypad_01b", "lasers": [], "isRequiredNextStage": false, "quantity": "", "createExplosion": false, "rewardMoney": false, "targetText": "", "penaltyForFailing": false, "doorId": "above big bank door", "rewardItem": false, "rewardItems": [], "itemName": "", "removeXpOnHit": false, "blackoutDuration": "1"}, {"rewardDelayTime": "", "selectedDoorName": "", "delayRewardCheckbox": false, "xpOptions": false, "penaltyType": "", "minigameCheckbox": false, "blackoutDuration": "", "dropWalletWithPasscode": false, "heading": "", "sendAlert": false, "items": [], "rewardMoneyMin": "", "penaltyChance": "", "unlockTime": "", "selectedWeapon": "", "stepName": "custom door", "type": "custom_door", "rewardItemMax": "", "rewardItemQuantity": "", "requireSimultaneous": false, "unlockTimeCheckbox": false, "requiredWeapons": [], "pedCoords": "", "animation": "", "propHeading": "166.00", "xpRewardAmount": "0", "requiresItem": false, "minigameType": "", "requireWeaponInHand": false, "removeXpOnHit": false, "xpRemoveAmount": "0", "stepNumber": 9, "rewardItemName": "", "rewardMoneyMax": "", "targetVector": "373.77, 331.73, 103.72", "isOptional": false, "lootableItems": [], "quantity": "", "rewardMoneyType": "", "difficulty": "", "guards": [], "propName": "hei_v_ilev_bk_safegate_pris", "createExplosion": false, "isRequiredNextStage": false, "lasers": [], "hackType": "", "rewardMoney": false, "targetText": "above big bank door", "rewardItemMin": "", "penaltyForFailing": false, "rewardItem": false, "rewardItems": [], "itemName": "", "triggersBlackout": false, "xpNeededAmount": "0"}, {"rewardDelayTime": "1", "selectedDoorName": "", "delayRewardCheckbox": false, "xpOptions": false, "penaltyType": "", "minigameCheckbox": false, "blackoutDuration": "1", "dropWalletWithPasscode": false, "heading": "256.00", "sendAlert": false, "items": [], "rewardMoneyMin": "1", "penaltyChance": "", "unlockTime": "", "selectedWeapon": "", "stepName": "big safe", "type": "robabble_object", "rewardItemMax": "", "rewardItemQuantity": "", "requireSimultaneous": false, "unlockTimeCheckbox": false, "requiredWeapons": [], "pedCoords": "380.51, 331.88, 102.57", "animation": "h4_prop_h4_safe_01a", "propHeading": "256.00", "xpRewardAmount": "0", "requiresItem": false, "minigameType": "", "requireWeaponInHand": false, "removeXpOnHit": false, "xpRemoveAmount": "0", "stepNumber": 10, "rewardItemName": "", "rewardMoneyMax": "1", "targetVector": "380.51, 331.88, 102.57", "isOptional": false, "lootableItems": [], "quantity": "", "rewardMoneyType": "cash", "difficulty": "", "guards": [], "propName": "h4_prop_h4_safe_01a", "createExplosion": false, "isRequiredNextStage": false, "lasers": [], "hackType": "safe-cracker-easy", "rewardMoney": false, "targetText": "Open Safe", "rewardItemMin": "", "penaltyForFailing": false, "rewardItem": false, "rewardItems": [], "itemName": "", "triggersBlackout": false, "xpNeededAmount": "0"}, {"rewardItemName": "", "minValue": 0, "selectedDoorName": "", "delayRewardCheckbox": false, "endingHeading": "348.00", "xpOptions": false, "stepNumber": 11, "minigameCheckbox": false, "blackoutDuration": "", "dropWalletWithPasscode": false, "heading": "", "sendAlert": false, "pedCoords": "", "items": [], "rewardMoneyMin": "", "penaltyChance": "", "unlockTime": "", "requiredWeapons": [], "stepName": "ending robbery", "type": "ending_robbery", "triggersBlackout": false, "rewardItemMin": "", "rewardItemQuantity": "", "requireSimultaneous": false, "unlockTimeCheckbox": false, "propHeading": "", "rewardDelayTime": "", "animation": "", "rewardItemMax": "", "xpRewardAmount": "0", "requiresItem": false, "minigameType": "", "requireWeaponInHand": false, "isOptional": false, "difficulty": "", "endingTargetVector": "-1382.37, -1.01, 52.45", "rewardMoneyType": "", "rewardMoneyMax": "", "targetVector": "", "selectedWeapon": "", "lootableItems": [], "quantity": "", "createExplosion": false, "xpRemoveAmount": "0", "guards": [], "propName": "", "lasers": [], "isRequiredNextStage": false, "hackType": "", "maxValue": 100, "rewardMoney": false, "targetText": "", "penaltyType": "", "penaltyForFailing": false, "rewardItem": false, "rewardItems": [], "itemName": "", "xpNeededAmount": "0", "removeXpOnHit": false}, {"rewardDelayTime": "1", "selectedDoorName": "", "delayRewardCheckbox": false, "xpOptions": false, "penaltyType": "", "minigameCheckbox": false, "blackoutDuration": "1", "dropWalletWithPasscode": false, "heading": "238.00", "sendAlert": false, "items": [], "rewardMoneyMin": "1", "penaltyChance": "", "unlockTime": "", "selectedWeapon": "", "stepName": "drill", "type": "robabble_object", "rewardItemMax": "1", "rewardItemQuantity": "", "requireSimultaneous": false, "unlockTimeCheckbox": false, "requiredWeapons": [], "pedCoords": "379.64, 332.16, 102.57", "animation": "16", "propHeading": "293.00", "xpRewardAmount": "0", "requiresItem": false, "minigameType": "", "requireWeaponInHand": false, "removeXpOnHit": false, "xpRemoveAmount": "0", "stepNumber": 12, "rewardItemName": "drill", "rewardMoneyMax": "1", "targetVector": "380.58, 331.88, 102.85", "isOptional": false, "lootableItems": [], "quantity": "", "rewardMoneyType": "cash", "difficulty": "", "guards": [], "propName": "ch_prop_ch_heist_drill", "createExplosion": false, "isRequiredNextStage": true, "lasers": [], "hackType": "none", "rewardMoney": false, "targetText": "<PERSON><PERSON>", "rewardItemMin": "1", "penaltyForFailing": false, "rewardItem": true, "rewardItems": [{"maxAmount": 1, "itemName": "drill", "minAmount": 1, "rewardChance": 40}], "itemName": "", "triggersBlackout": false, "xpNeededAmount": "0"}, {"rewardDelayTime": "1", "selectedDoorName": "", "delayRewardCheckbox": false, "xpOptions": false, "penaltyType": "", "minigameCheckbox": false, "blackoutDuration": "1", "dropWalletWithPasscode": false, "heading": "232.00", "sendAlert": false, "items": [], "rewardMoneyMin": "1", "penaltyChance": "", "unlockTime": "", "selectedWeapon": "", "stepName": "cash pile", "type": "robabble_object", "rewardItemMax": "", "rewardItemQuantity": "", "requireSimultaneous": false, "unlockTimeCheckbox": false, "requiredWeapons": [], "pedCoords": "379.87, 331.99, 102.57", "animation": "15", "propHeading": "248.00", "xpRewardAmount": "0", "requiresItem": false, "minigameType": "", "requireWeaponInHand": false, "removeXpOnHit": false, "xpRemoveAmount": "0", "stepNumber": 13, "rewardItemName": "", "rewardMoneyMax": "1", "targetVector": "380.54, 331.91, 103.60", "isOptional": false, "lootableItems": [], "quantity": "", "rewardMoneyType": "cash", "difficulty": "", "guards": [], "propName": "bkr_prop_bkr_cashpile_04", "createExplosion": false, "isRequiredNextStage": true, "lasers": [], "hackType": "none", "rewardMoney": false, "targetText": "Grab Money", "rewardItemMin": "", "penaltyForFailing": false, "rewardItem": false, "rewardItems": [], "itemName": "", "triggersBlackout": false, "xpNeededAmount": "0"}, {"rewardDelayTime": "1", "selectedDoorName": "", "delayRewardCheckbox": false, "xpOptions": false, "penaltyType": "", "minigameCheckbox": false, "blackoutDuration": "1", "dropWalletWithPasscode": false, "heading": "337.00", "sendAlert": false, "items": [], "rewardMoneyMin": "1", "penaltyChance": "", "unlockTime": "", "selectedWeapon": "", "stepName": "small safe", "type": "robabble_object", "rewardItemMax": "1", "rewardItemQuantity": "", "requireSimultaneous": false, "unlockTimeCheckbox": false, "requiredWeapons": [], "pedCoords": "377.95, 332.99, 102.57", "animation": "1", "propHeading": "0.00", "xpRewardAmount": "0", "requiresItem": false, "minigameType": "", "requireWeaponInHand": false, "removeXpOnHit": false, "xpRemoveAmount": "0", "stepNumber": 14, "rewardItemName": "unmarkedsimcard", "rewardMoneyMax": "1", "targetVector": "378.23, 333.69, 103.15", "isOptional": false, "lootableItems": [], "quantity": "", "rewardMoneyType": "cash", "difficulty": "", "guards": [], "propName": "", "createExplosion": false, "isRequiredNextStage": true, "lasers": [], "hackType": "none", "rewardMoney": false, "targetText": "Open Safe", "rewardItemMin": "1", "penaltyForFailing": false, "rewardItem": true, "rewardItems": [{"maxAmount": 1, "itemName": "unmarkedsimcard", "minAmount": 1, "rewardChance": 10}, {"maxAmount": 7, "itemName": "diamondring", "minAmount": 1, "rewardChance": 100}, {"maxAmount": 5, "itemName": "diamondring", "minAmount": 1, "rewardChance": 100}, {"maxAmount": 7, "itemName": "goldbracelet", "minAmount": 1, "rewardChance": 100}, {"maxAmount": 3, "itemName": "goldbar", "minAmount": 1, "rewardChance": 100}], "itemName": "", "triggersBlackout": false, "xpNeededAmount": "0"}], "settings": {"cooldownHeists": ["store robbery vanila unicorn"], "blipColor": 0, "robberyCooldown": "25", "blipScale": 1, "policeJobs": ["police", "leo", "lspd", "sasp", "ranger", "bcso", "trooper"], "alertSound1": "robberysound", "alertPriority": 1, "policeRequired": "", "minutesUntilRobbery": "", "blipFlash": false, "blipSprite": 59, "alertIcon": "fas fa-bell", "alertCode": "10-90", "policeAlert": "Armed store robbery", "policeAlertHeader": "Armed store robbery", "cooldownMinutes": 25}}, {"heist_name": "store near casino", "stages": [{"penaltyChance": "", "rewardItemName": "salvagedlockpick", "guards": [], "animation": "1", "isRequiredNextStage": false, "lasers": [], "stepName": "Cabinet 1", "rewardMoney": false, "rewardDelayTime": "1", "propHeading": "0.00", "selectedWeapon": "", "removeXpOnHit": false, "rewardItemMax": "1", "pedCoords": "1165.43, -323.23, 68.21", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "cash", "requiresItem": false, "hackType": "none", "sendAlert": false, "isOptional": false, "unlockTime": "", "xpRewardAmount": "0", "rewardItems": [{"rewardChance": 45, "minAmount": 1, "maxAmount": 1, "itemName": "salvagedlockpick"}], "xpRemoveAmount": "0", "rewardItem": true, "targetVector": "1166.03, -323.23, 68.69", "requireSimultaneous": false, "rewardItemMin": "1", "requireWeaponInHand": false, "minigameCheckbox": false, "delayRewardCheckbox": false, "requiredWeapons": [], "items": [], "selectedDoorName": "", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "266.00", "rewardMoneyMin": "1", "propName": "", "blackoutDuration": "1", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "1", "difficulty": "", "quantity": "", "dropWalletWithPasscode": false, "type": "robabble_object", "createExplosion": false, "targetText": "Search Cabinet", "stepNumber": 1, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "salvagedlockpick", "guards": [], "animation": "2", "isRequiredNextStage": false, "lasers": [], "stepName": "Cabinet 2", "rewardMoney": false, "rewardDelayTime": "1", "propHeading": "0.00", "selectedWeapon": "", "removeXpOnHit": false, "rewardItemMax": "1", "pedCoords": "1165.43, -323.99, 68.21", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "cash", "requiresItem": false, "hackType": "none", "sendAlert": false, "isOptional": false, "unlockTime": "", "xpRewardAmount": "0", "rewardItems": [{"rewardChance": 45, "minAmount": 1, "maxAmount": 1, "itemName": "salvagedlockpick"}], "xpRemoveAmount": "0", "rewardItem": true, "targetVector": "1165.48, -324.76, 68.66", "requireSimultaneous": false, "rewardItemMin": "1", "requireWeaponInHand": false, "minigameCheckbox": false, "delayRewardCheckbox": false, "requiredWeapons": [], "items": [], "selectedDoorName": "", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "186.00", "rewardMoneyMin": "1", "propName": "", "blackoutDuration": "1", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "1", "difficulty": "", "quantity": "", "dropWalletWithPasscode": false, "type": "robabble_object", "createExplosion": false, "targetText": "Search Cabinet", "stepNumber": 2, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "salvagedlockpick", "guards": [], "animation": "2", "isRequiredNextStage": false, "lasers": [], "stepName": "Cabinet 3", "rewardMoney": false, "rewardDelayTime": "1", "propHeading": "351.00", "selectedWeapon": "", "removeXpOnHit": false, "rewardItemMax": "1", "pedCoords": "1165.11, -323.27, 68.21", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "cash", "requiresItem": false, "hackType": "none", "sendAlert": false, "isOptional": false, "unlockTime": "", "xpRewardAmount": "0", "rewardItems": [{"rewardChance": 45, "minAmount": 1, "maxAmount": 1, "itemName": "salvagedlockpick"}], "xpRemoveAmount": "0", "rewardItem": true, "targetVector": "1164.51, -323.52, 68.79", "requireSimultaneous": false, "rewardItemMin": "1", "requireWeaponInHand": false, "minigameCheckbox": false, "delayRewardCheckbox": false, "requiredWeapons": [], "items": [], "selectedDoorName": "", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "101.00", "rewardMoneyMin": "1", "propName": "", "blackoutDuration": "1", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "1", "difficulty": "", "quantity": "", "dropWalletWithPasscode": false, "type": "robabble_object", "createExplosion": false, "targetText": "Search Cabinet", "stepNumber": 3, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "bandsofnotes", "guards": [], "animation": "19", "isRequiredNextStage": false, "lasers": [], "stepName": "register 1", "rewardMoney": false, "rewardDelayTime": "1", "propHeading": "0.00", "selectedWeapon": "", "removeXpOnHit": false, "rewardItemMax": "70", "pedCoords": "1165.09, -324.40, 68.21", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "cash", "requiresItem": true, "hackType": "inspired-lockpick", "sendAlert": true, "isOptional": false, "unlockTime": "", "xpRewardAmount": "0", "rewardItems": [{"rewardChance": 80, "minAmount": 25, "maxAmount": 70, "itemName": "bandsofnotes"}, {"rewardChance": 50, "minAmount": 25, "maxAmount": 70, "itemName": "stacksofcash"}], "xpRemoveAmount": "0", "rewardItem": true, "targetVector": "1164.57, -324.71, 69.38", "requireSimultaneous": false, "rewardItemMin": "25", "requireWeaponInHand": false, "minigameCheckbox": false, "delayRewardCheckbox": false, "requiredWeapons": [], "items": [{"loseChance": 30, "itemName": "advancedlockpick", "itemQuantity": 1}, {"loseChance": 40, "itemName": "salvagedlockpick", "itemQuantity": 1}], "selectedDoorName": "", "itemName": "advancedlockpick", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "131.00", "rewardMoneyMin": "1", "propName": "", "blackoutDuration": "1", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "1", "difficulty": "", "quantity": "1", "dropWalletWithPasscode": false, "type": "robabble_object", "createExplosion": false, "targetText": "Lockpick Register", "stepNumber": 4, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "", "type": "guards", "animation": "", "isRequiredNextStage": false, "lasers": [], "stepName": "guard 1", "rewardMoney": false, "rewardDelayTime": "", "propHeading": "", "selectedWeapon": "", "removeXpOnHit": false, "hackType": "s_m_m_chemsec_01", "targetVector": "", "walletDropChance": 70, "pedCoords": "", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "", "requiresItem": false, "requiredWeapons": [], "sendAlert": false, "isOptional": false, "rewardItemMin": "", "xpRewardAmount": "0", "rewardItems": [], "xpRemoveAmount": "0", "rewardItem": false, "requireWeaponInHand": false, "requireSimultaneous": false, "rewardMoneyMin": "", "delayRewardCheckbox": false, "minigameCheckbox": false, "selectedDoorName": "", "quantity": "", "items": [], "minigameType": "s_m_m_chemsec_01", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "propName": "", "xpNeededAmount": "0", "heading": "", "blackoutDuration": "", "rewardItemQuantity": "", "rewardMoneyMax": "", "unlockTimeCheckbox": false, "showEnemyBlip": true, "unlockTime": "", "difficulty": "", "guards": [{"guardModel": "s_m_m_chemsec_01", "weapon": "weapon_combatshotgun", "heading": 292, "guardPosition": [1155.88, -328.29, 68.21]}], "createExplosion": false, "dropWalletWithPasscode": true, "rewardItemMax": "", "targetText": "", "stepNumber": 5, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "bandsofnotes", "guards": [], "animation": "19", "isRequiredNextStage": false, "lasers": [], "stepName": "register 2", "rewardMoney": false, "rewardDelayTime": "1", "propHeading": "0.00", "selectedWeapon": "", "removeXpOnHit": false, "rewardItemMax": "70", "pedCoords": "1164.75, -322.52, 68.21", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "cash", "requiresItem": true, "hackType": "inspired-lockpick", "sendAlert": true, "isOptional": false, "unlockTime": "", "xpRewardAmount": "0", "rewardItems": [{"rewardChance": 80, "minAmount": 25, "maxAmount": 70, "itemName": "bandsofnotes"}, {"rewardChance": 50, "minAmount": 25, "maxAmount": 70, "itemName": "stacksofcash"}], "xpRemoveAmount": "0", "rewardItem": true, "targetVector": "1164.15, -322.78, 69.38", "requireSimultaneous": false, "rewardItemMin": "25", "requireWeaponInHand": false, "minigameCheckbox": false, "delayRewardCheckbox": false, "requiredWeapons": [], "items": [{"loseChance": 30, "itemName": "advancedlockpick", "itemQuantity": 1}, {"loseChance": 40, "itemName": "salvagedlockpick", "itemQuantity": 1}], "selectedDoorName": "", "itemName": "advancedlockpick", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "124.00", "rewardMoneyMin": "1", "propName": "", "blackoutDuration": "1", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "1", "difficulty": "", "quantity": "1", "dropWalletWithPasscode": false, "type": "robabble_object", "createExplosion": false, "targetText": "Lockpick Register", "stepNumber": 6, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "", "type": "guards", "animation": "", "isRequiredNextStage": false, "lasers": [], "stepName": "guard 2", "rewardMoney": false, "rewardDelayTime": "", "propHeading": "", "selectedWeapon": "", "removeXpOnHit": false, "hackType": "s_m_m_chemsec_01", "targetVector": "", "walletDropChance": 70, "pedCoords": "", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "", "requiresItem": false, "requiredWeapons": [], "sendAlert": false, "isOptional": false, "rewardItemMin": "", "xpRewardAmount": "0", "rewardItems": [], "xpRemoveAmount": "0", "rewardItem": false, "requireWeaponInHand": false, "requireSimultaneous": false, "rewardMoneyMin": "", "delayRewardCheckbox": false, "minigameCheckbox": false, "selectedDoorName": "", "quantity": "", "items": [], "minigameType": "s_m_m_chemsec_01", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "propName": "", "xpNeededAmount": "0", "heading": "", "blackoutDuration": "", "rewardItemQuantity": "", "rewardMoneyMax": "", "unlockTimeCheckbox": false, "showEnemyBlip": true, "unlockTime": "", "difficulty": "", "guards": [{"guardModel": "s_m_m_chemsec_01", "weapon": "weapon_minismg", "heading": 107, "guardPosition": [1163.78, -327.85, 68.06]}], "createExplosion": false, "dropWalletWithPasscode": true, "rewardItemMax": "", "targetText": "", "stepNumber": 7, "penaltyType": ""}, {"penaltyChance": "100", "rewardItemName": "", "guards": [], "animation": "8", "isRequiredNextStage": false, "lasers": [], "stepName": "hackable door", "rewardMoney": false, "rewardDelayTime": "", "propHeading": "99.00", "selectedWeapon": "", "removeXpOnHit": false, "hackType": "none", "targetVector": "1162.82, -316.76, 69.55", "rewardItemMax": "", "pedCoords": "1163.49, -316.90, 68.21", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "", "requiresItem": false, "requiredWeapons": [], "sendAlert": true, "isOptional": false, "rewardItemMin": "", "xpRewardAmount": "0", "rewardItems": [], "xpRemoveAmount": "0", "rewardItem": false, "requireWeaponInHand": false, "requireSimultaneous": false, "minigameType": "", "delayRewardCheckbox": false, "minigameCheckbox": false, "selectedDoorName": "store casino door", "unlockTime": "5", "items": [], "itemName": "", "doorPosition": "1162.59, -316.53, 69.34", "triggersBlackout": false, "penaltyForFailing": false, "doorId": "store casino door", "xpNeededAmount": "0", "heading": "99.00", "rewardMoneyMin": "", "propName": "prop_ld_keypad_01b", "blackoutDuration": "1", "unlockTimeCheckbox": true, "rewardItemQuantity": "", "rewardMoneyMax": "", "difficulty": "", "quantity": "", "type": "hackable_door", "dropWalletWithPasscode": false, "createExplosion": false, "targetText": "Enter Code", "stepNumber": 8, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "", "guards": [], "animation": "", "isRequiredNextStage": false, "lasers": [], "stepName": "custom door", "rewardMoney": false, "rewardDelayTime": "", "propHeading": "281.00", "selectedWeapon": "", "removeXpOnHit": false, "rewardItemMax": "", "pedCoords": "", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "", "requiresItem": false, "hackType": "", "sendAlert": false, "isOptional": false, "unlockTime": "", "xpRewardAmount": "0", "rewardItems": [], "xpRemoveAmount": "0", "rewardItem": false, "targetVector": "1162.59, -316.53, 69.34", "requireSimultaneous": false, "rewardItemMin": "", "requireWeaponInHand": false, "minigameCheckbox": false, "delayRewardCheckbox": false, "requiredWeapons": [], "items": [], "selectedDoorName": "", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "", "rewardMoneyMin": "", "propName": "hei_v_ilev_bk_safegate_pris", "blackoutDuration": "", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "", "difficulty": "", "quantity": "", "dropWalletWithPasscode": false, "type": "custom_door", "createExplosion": false, "targetText": "store casino door", "stepNumber": 9, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "", "guards": [], "animation": "h4_prop_h4_safe_01a", "isRequiredNextStage": false, "lasers": [], "stepName": "Big safe", "rewardMoney": false, "rewardDelayTime": "1", "propHeading": "10.00", "selectedWeapon": "", "removeXpOnHit": false, "rewardItemMax": "", "pedCoords": "1160.92, -313.32, 68.21", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "cash", "requiresItem": false, "hackType": "safe-cracker-easy", "sendAlert": true, "isOptional": false, "unlockTime": "", "xpRewardAmount": "0", "rewardItems": [], "xpRemoveAmount": "0", "rewardItem": false, "targetVector": "1160.92, -313.32, 68.21", "requireSimultaneous": false, "rewardItemMin": "", "requireWeaponInHand": false, "minigameCheckbox": false, "delayRewardCheckbox": false, "requiredWeapons": [], "items": [], "selectedDoorName": "", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "10.00", "rewardMoneyMin": "1", "propName": "h4_prop_h4_safe_01a", "blackoutDuration": "1", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "1", "difficulty": "", "quantity": "", "dropWalletWithPasscode": false, "type": "robabble_object", "createExplosion": false, "targetText": "Open Safe", "stepNumber": 10, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "drill", "guards": [], "animation": "16", "isRequiredNextStage": true, "lasers": [], "stepName": "drill", "rewardMoney": false, "rewardDelayTime": "1", "propHeading": "51.00", "selectedWeapon": "", "removeXpOnHit": false, "rewardItemMax": "1", "pedCoords": "1160.92, -314.26, 68.21", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "cash", "requiresItem": false, "hackType": "none", "sendAlert": false, "isOptional": false, "unlockTime": "", "xpRewardAmount": "0", "rewardItems": [{"rewardChance": 40, "minAmount": 1, "maxAmount": 1, "itemName": "drill"}], "xpRemoveAmount": "0", "rewardItem": true, "targetVector": "1160.85, -313.27, 68.49", "requireSimultaneous": false, "rewardItemMin": "1", "requireWeaponInHand": false, "minigameCheckbox": false, "delayRewardCheckbox": false, "requiredWeapons": [], "items": [], "selectedDoorName": "", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "344.00", "rewardMoneyMin": "1", "propName": "ch_prop_ch_heist_drill", "blackoutDuration": "1", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "1", "difficulty": "", "quantity": "", "dropWalletWithPasscode": false, "type": "robabble_object", "createExplosion": false, "targetText": "<PERSON><PERSON>", "stepNumber": 11, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "", "guards": [], "animation": "", "isRequiredNextStage": false, "lasers": [], "hackType": "", "stepName": "ending robbery", "rewardMoney": false, "rewardDelayTime": "", "targetVector": "", "propHeading": "", "selectedWeapon": "", "removeXpOnHit": false, "endingTargetVector": "-1647.29, -200.76, 54.15", "delayRewardCheckbox": false, "rewardItemMax": "", "pedCoords": "", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "", "requiresItem": false, "maxValue": 100, "sendAlert": false, "isOptional": false, "rewardItemMin": "", "xpRewardAmount": "0", "rewardItems": [], "xpRemoveAmount": "0", "rewardItem": false, "requireWeaponInHand": false, "requireSimultaneous": false, "createExplosion": false, "minValue": 0, "minigameCheckbox": false, "endingHeading": "198.00", "requiredWeapons": [], "items": [], "selectedDoorName": "", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "", "rewardMoneyMin": "", "propName": "", "blackoutDuration": "", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "", "difficulty": "", "unlockTime": "", "quantity": "", "dropWalletWithPasscode": false, "type": "ending_robbery", "targetText": "", "stepNumber": 12, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "", "guards": [], "animation": "15", "isRequiredNextStage": true, "lasers": [], "stepName": "Safe Money", "rewardMoney": true, "rewardDelayTime": "1", "propHeading": "2.00", "selectedWeapon": "", "removeXpOnHit": false, "rewardItemMax": "", "pedCoords": "1161.10, -313.96, 68.21", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "cash", "requiresItem": false, "hackType": "none", "sendAlert": false, "isOptional": false, "unlockTime": "", "xpRewardAmount": "0", "rewardItems": [], "xpRemoveAmount": "0", "rewardItem": false, "targetVector": "1160.90, -313.26, 69.24", "requireSimultaneous": false, "rewardItemMin": "", "requireWeaponInHand": false, "minigameCheckbox": false, "delayRewardCheckbox": false, "requiredWeapons": [], "items": [], "selectedDoorName": "", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "343.00", "rewardMoneyMin": "200", "propName": "bkr_prop_bkr_cashpile_04", "blackoutDuration": "1", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "700", "difficulty": "", "quantity": "", "dropWalletWithPasscode": false, "type": "robabble_object", "createExplosion": false, "targetText": "Grab Money", "stepNumber": 13, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "unmarkedsimcard", "guards": [], "animation": "2", "isRequiredNextStage": true, "lasers": [], "stepName": "small safe", "rewardMoney": false, "rewardDelayTime": "300", "propHeading": "0.00", "selectedWeapon": "", "removeXpOnHit": false, "rewardItemMax": "1", "pedCoords": "1159.90, -314.33, 68.21", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "cash", "requiresItem": false, "hackType": "none", "sendAlert": true, "isOptional": false, "unlockTime": "", "xpRewardAmount": "0", "rewardItems": [{"rewardChance": 10, "minAmount": 1, "maxAmount": 1, "itemName": "unmarkedsimcard"}, {"rewardChance": 100, "minAmount": 1, "maxAmount": 5, "itemName": "diamondring"}, {"rewardChance": 100, "minAmount": 1, "maxAmount": 7, "itemName": "goldbracelet"}, {"rewardChance": 1, "minAmount": 1, "maxAmount": 3, "itemName": "goldbar"}, {"rewardChance": 100, "minAmount": 5, "maxAmount": 25, "itemName": "bandsofnotes"}], "xpRemoveAmount": "0", "rewardItem": true, "targetVector": "1159.21, -314.09, 68.66", "requireSimultaneous": false, "rewardItemMin": "1", "requireWeaponInHand": false, "minigameCheckbox": false, "delayRewardCheckbox": true, "requiredWeapons": [], "items": [], "selectedDoorName": "", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "67.00", "rewardMoneyMin": "1", "propName": "", "blackoutDuration": "1", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "1", "difficulty": "", "quantity": "", "dropWalletWithPasscode": false, "type": "robabble_object", "createExplosion": false, "targetText": "Open Safe", "stepNumber": 14, "penaltyType": ""}], "settings": {"blipScale": 1, "blipFlash": false, "alertSound1": "robberysound", "alertCode": "10-90", "alertPriority": 1, "alertIcon": "fas fa-bell", "blipColor": 0, "blipSprite": 59, "cooldownMinutes": 25, "policeRequired": "", "policeJobs": ["police", "leo", "lspd", "sasp", "ranger", "bcso", "trooper"], "robberyCooldown": "25", "policeAlert": "Armed store robbery", "policeAlertHeader": "Armed store robbery", "cooldownHeists": ["store south side", "store robbery vanila unicorn", "store above big bank"], "minutesUntilRobbery": ""}}, {"heist_name": "store burgershot", "stages": [{"penaltyChance": "", "rewardItemName": "salvagedlockpick", "guards": [], "animation": "2", "isRequiredNextStage": false, "lasers": [], "stepName": "Cabinet 1", "rewardMoney": false, "rewardDelayTime": "1", "propHeading": "0.00", "selectedWeapon": "", "removeXpOnHit": false, "rewardItemMax": "1", "pedCoords": "-1220.95, -908.00, 11.33", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "cash", "requiresItem": false, "hackType": "none", "sendAlert": false, "isOptional": false, "unlockTime": "", "xpRewardAmount": "0", "rewardItems": [{"rewardChance": 45, "minAmount": 1, "maxAmount": 1, "itemName": "salvagedlockpick"}], "xpRemoveAmount": "0", "rewardItem": true, "targetVector": "-1221.28, -907.47, 11.77", "requireSimultaneous": false, "rewardItemMin": "1", "requireWeaponInHand": false, "minigameCheckbox": false, "delayRewardCheckbox": false, "requiredWeapons": [], "items": [], "selectedDoorName": "", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "41.00", "rewardMoneyMin": "1", "propName": "", "blackoutDuration": "1", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "1", "difficulty": "", "quantity": "", "dropWalletWithPasscode": false, "type": "robabble_object", "createExplosion": false, "targetText": "Search Cabinet", "stepNumber": 1, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "salvagedlockpick", "guards": [], "animation": "2", "isRequiredNextStage": false, "lasers": [], "stepName": "Cabinet 2", "rewardMoney": false, "rewardDelayTime": "1", "propHeading": "0.00", "selectedWeapon": "", "removeXpOnHit": false, "rewardItemMax": "1", "pedCoords": "-1223.35, -909.27, 11.33", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "cash", "requiresItem": false, "hackType": "none", "sendAlert": false, "isOptional": false, "unlockTime": "", "xpRewardAmount": "0", "rewardItems": [{"rewardChance": 45, "minAmount": 1, "maxAmount": 1, "itemName": "salvagedlockpick"}], "xpRemoveAmount": "0", "rewardItem": true, "targetVector": "-1223.11, -909.98, 11.87", "requireSimultaneous": false, "rewardItemMin": "1", "requireWeaponInHand": false, "minigameCheckbox": false, "delayRewardCheckbox": false, "requiredWeapons": [], "items": [], "selectedDoorName": "", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "196.00", "rewardMoneyMin": "1", "propName": "", "blackoutDuration": "1", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "1", "difficulty": "", "quantity": "", "dropWalletWithPasscode": false, "type": "robabble_object", "createExplosion": false, "targetText": "Search Cabinet", "stepNumber": 2, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "salvagedlockpick", "guards": [], "animation": "2", "isRequiredNextStage": false, "lasers": [], "stepName": "Cabinet 3", "rewardMoney": false, "rewardDelayTime": "1", "propHeading": "0.00", "selectedWeapon": "", "removeXpOnHit": false, "rewardItemMax": "1", "pedCoords": "-1219.76, -907.88, 11.33", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "cash", "requiresItem": false, "hackType": "none", "sendAlert": false, "isOptional": false, "unlockTime": "", "xpRewardAmount": "0", "rewardItems": [{"rewardChance": 45, "minAmount": 1, "maxAmount": 1, "itemName": "salvagedlockpick"}], "xpRemoveAmount": "0", "rewardItem": true, "targetVector": "-1219.10, -907.57, 11.76", "requireSimultaneous": false, "rewardItemMin": "1", "requireWeaponInHand": false, "minigameCheckbox": false, "delayRewardCheckbox": false, "requiredWeapons": [], "items": [], "selectedDoorName": "", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "283.00", "rewardMoneyMin": "1", "propName": "", "blackoutDuration": "1", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "1", "difficulty": "", "quantity": "", "dropWalletWithPasscode": false, "type": "robabble_object", "createExplosion": false, "targetText": "Search Cabinet", "stepNumber": 3, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "bandsofnotes", "guards": [], "animation": "19", "isRequiredNextStage": false, "lasers": [], "stepName": "register 1", "rewardMoney": false, "rewardDelayTime": "1", "propHeading": "0.00", "selectedWeapon": "", "removeXpOnHit": false, "rewardItemMax": "70", "pedCoords": "-1222.07, -908.41, 11.33", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "cash", "requiresItem": true, "hackType": "inspired-lockpick", "sendAlert": true, "isOptional": false, "unlockTime": "", "xpRewardAmount": "0", "rewardItems": [{"rewardChance": 80, "minAmount": 25, "maxAmount": 70, "itemName": "bandsofnotes"}, {"rewardChance": 50, "minAmount": 25, "maxAmount": 70, "itemName": "stacksofcash"}], "xpRemoveAmount": "0", "rewardItem": true, "targetVector": "-1222.42, -907.75, 12.49", "requireSimultaneous": false, "rewardItemMin": "25", "requireWeaponInHand": false, "minigameCheckbox": false, "delayRewardCheckbox": false, "requiredWeapons": [], "items": [{"loseChance": 30, "itemName": "advancedlockpick", "itemQuantity": 1}, {"loseChance": 40, "itemName": "salvagedlockpick", "itemQuantity": 1}], "selectedDoorName": "", "itemName": "advancedlockpick", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "51.00", "rewardMoneyMin": "1", "propName": "", "blackoutDuration": "1", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "1", "difficulty": "", "quantity": "1", "dropWalletWithPasscode": false, "type": "robabble_object", "createExplosion": false, "targetText": "Lockpick Register", "stepNumber": 4, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "", "type": "guards", "animation": "", "isRequiredNextStage": false, "lasers": [], "stepName": "guard 1", "rewardMoney": false, "rewardDelayTime": "", "propHeading": "", "selectedWeapon": "", "removeXpOnHit": false, "hackType": "s_m_m_chemsec_01", "targetVector": "", "walletDropChance": 70, "pedCoords": "", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "", "requiresItem": false, "requiredWeapons": [], "sendAlert": false, "isOptional": false, "rewardItemMin": "", "xpRewardAmount": "0", "rewardItems": [], "xpRemoveAmount": "0", "rewardItem": false, "requireWeaponInHand": false, "requireSimultaneous": false, "rewardMoneyMin": "", "delayRewardCheckbox": false, "minigameCheckbox": false, "selectedDoorName": "", "quantity": "", "items": [], "minigameType": "s_m_m_chemsec_01", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "propName": "", "xpNeededAmount": "0", "heading": "", "blackoutDuration": "", "rewardItemQuantity": "", "rewardMoneyMax": "", "unlockTimeCheckbox": false, "showEnemyBlip": true, "unlockTime": "", "difficulty": "", "guards": [{"guardModel": "s_m_m_chemsec_01", "weapon": "weapon_combatshotgun", "heading": 310, "guardPosition": [-1229.73, -902.36, 11.19]}], "createExplosion": false, "dropWalletWithPasscode": true, "rewardItemMax": "", "targetText": "", "stepNumber": 5, "penaltyType": ""}, {"penaltyChance": "100", "rewardItemName": "", "guards": [], "animation": "8", "isRequiredNextStage": false, "lasers": [], "stepName": "hackable door", "rewardMoney": false, "rewardDelayTime": "", "propHeading": "216.00", "selectedWeapon": "", "removeXpOnHit": false, "hackType": "none", "targetVector": "-1223.62, -910.43, 12.76", "rewardItemMax": "", "pedCoords": "-1223.98, -909.82, 11.33", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "", "requiresItem": false, "requiredWeapons": [], "sendAlert": true, "isOptional": false, "rewardItemMin": "", "xpRewardAmount": "0", "rewardItems": [], "xpRemoveAmount": "0", "rewardItem": false, "requireWeaponInHand": false, "requireSimultaneous": false, "minigameType": "", "delayRewardCheckbox": false, "minigameCheckbox": false, "selectedDoorName": "store burgershot", "unlockTime": "5", "items": [], "itemName": "", "doorPosition": "-1224.70, -911.51, 12.42", "triggersBlackout": false, "penaltyForFailing": false, "doorId": "store burgershot", "xpNeededAmount": "0", "heading": "216.00", "rewardMoneyMin": "", "propName": "prop_ld_keypad_01b", "blackoutDuration": "1", "unlockTimeCheckbox": true, "rewardItemQuantity": "", "rewardMoneyMax": "", "difficulty": "", "quantity": "", "type": "hackable_door", "dropWalletWithPasscode": false, "createExplosion": false, "targetText": "Enter Code", "stepNumber": 6, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "", "guards": [], "animation": "", "isRequiredNextStage": false, "lasers": [], "stepName": "custom door", "rewardMoney": false, "rewardDelayTime": "", "propHeading": "213.00", "selectedWeapon": "", "removeXpOnHit": false, "rewardItemMax": "", "pedCoords": "", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "", "requiresItem": false, "hackType": "", "sendAlert": false, "isOptional": false, "unlockTime": "", "xpRewardAmount": "0", "rewardItems": [], "xpRemoveAmount": "0", "rewardItem": false, "targetVector": "-1224.70, -911.51, 12.42", "requireSimultaneous": false, "rewardItemMin": "", "requireWeaponInHand": false, "minigameCheckbox": false, "delayRewardCheckbox": false, "requiredWeapons": [], "items": [], "selectedDoorName": "", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "", "rewardMoneyMin": "", "propName": "hei_v_ilev_bk_safegate_pris", "blackoutDuration": "", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "", "difficulty": "", "quantity": "", "dropWalletWithPasscode": false, "type": "custom_door", "createExplosion": false, "targetText": "store burgershot", "stepNumber": 7, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "", "guards": [], "animation": "h4_prop_h4_safe_01a", "isRequiredNextStage": false, "lasers": [], "stepName": "Big safe", "rewardMoney": false, "rewardDelayTime": "1", "propHeading": "214.00", "selectedWeapon": "", "removeXpOnHit": false, "rewardItemMax": "", "pedCoords": "-1218.15, -915.74, 10.33", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "cash", "requiresItem": false, "hackType": "safe-cracker-easy", "sendAlert": true, "isOptional": false, "unlockTime": "", "xpRewardAmount": "0", "rewardItems": [], "xpRemoveAmount": "0", "rewardItem": false, "targetVector": "-1218.15, -915.74, 10.33", "requireSimultaneous": false, "rewardItemMin": "", "requireWeaponInHand": false, "minigameCheckbox": false, "delayRewardCheckbox": false, "requiredWeapons": [], "items": [], "selectedDoorName": "", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "214.00", "rewardMoneyMin": "1", "propName": "h4_prop_h4_safe_01a", "blackoutDuration": "1", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "1", "difficulty": "", "quantity": "", "dropWalletWithPasscode": false, "type": "robabble_object", "createExplosion": false, "targetText": "Open Safe", "stepNumber": 8, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "drill", "guards": [], "animation": "16", "isRequiredNextStage": true, "lasers": [], "stepName": "drill", "rewardMoney": false, "rewardDelayTime": "1", "propHeading": "248.00", "selectedWeapon": "", "removeXpOnHit": false, "rewardItemMax": "1", "pedCoords": "-1218.58, -914.91, 10.33", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "cash", "requiresItem": false, "hackType": "none", "sendAlert": false, "isOptional": false, "unlockTime": "", "xpRewardAmount": "0", "rewardItems": [{"rewardChance": 40, "minAmount": 1, "maxAmount": 1, "itemName": "drill"}], "xpRemoveAmount": "0", "rewardItem": true, "targetVector": "-1218.12, -915.80, 10.62", "requireSimultaneous": false, "rewardItemMin": "1", "requireWeaponInHand": false, "minigameCheckbox": false, "delayRewardCheckbox": false, "requiredWeapons": [], "items": [], "selectedDoorName": "", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "193.00", "rewardMoneyMin": "1", "propName": "ch_prop_ch_heist_drill", "blackoutDuration": "1", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "1", "difficulty": "", "quantity": "", "dropWalletWithPasscode": false, "type": "robabble_object", "createExplosion": false, "targetText": "<PERSON><PERSON>", "stepNumber": 9, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "", "guards": [], "animation": "", "isRequiredNextStage": false, "lasers": [], "hackType": "", "stepName": "ending robbery", "rewardMoney": false, "rewardDelayTime": "", "targetVector": "", "propHeading": "", "selectedWeapon": "", "removeXpOnHit": false, "endingTargetVector": "-833.37, -1280.21, 4.00", "delayRewardCheckbox": false, "rewardItemMax": "", "pedCoords": "", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "", "requiresItem": false, "maxValue": 100, "sendAlert": false, "isOptional": false, "rewardItemMin": "", "xpRewardAmount": "0", "rewardItems": [], "xpRemoveAmount": "0", "rewardItem": false, "requireWeaponInHand": false, "requireSimultaneous": false, "createExplosion": false, "minValue": 0, "minigameCheckbox": false, "endingHeading": "273.00", "requiredWeapons": [], "items": [], "selectedDoorName": "", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "", "rewardMoneyMin": "", "propName": "", "blackoutDuration": "", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "", "difficulty": "", "unlockTime": "", "quantity": "", "dropWalletWithPasscode": false, "type": "ending_robbery", "targetText": "", "stepNumber": 10, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "", "guards": [], "animation": "15", "isRequiredNextStage": true, "lasers": [], "stepName": "Safe Money", "rewardMoney": true, "rewardDelayTime": "1", "propHeading": "210.00", "selectedWeapon": "", "removeXpOnHit": false, "rewardItemMax": "", "pedCoords": "-1218.45, -915.32, 10.33", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "cash", "requiresItem": false, "hackType": "none", "sendAlert": false, "isOptional": false, "unlockTime": "", "xpRewardAmount": "0", "rewardItems": [], "xpRemoveAmount": "0", "rewardItem": false, "targetVector": "-1218.13, -915.78, 11.36", "requireSimultaneous": false, "rewardItemMin": "", "requireWeaponInHand": false, "minigameCheckbox": false, "delayRewardCheckbox": false, "requiredWeapons": [], "items": [], "selectedDoorName": "", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "177.00", "rewardMoneyMin": "200", "propName": "bkr_prop_bkr_cashpile_04", "blackoutDuration": "1", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "700", "difficulty": "", "quantity": "", "dropWalletWithPasscode": false, "type": "robabble_object", "createExplosion": false, "targetText": "Grab Money", "stepNumber": 11, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "unmarkedsimcard", "guards": [], "animation": "19", "isRequiredNextStage": true, "lasers": [], "stepName": "small safe", "rewardMoney": false, "rewardDelayTime": "300", "propHeading": "0.00", "selectedWeapon": "", "removeXpOnHit": false, "rewardItemMax": "1", "pedCoords": "-1220.46, -915.93, 10.33", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "cash", "requiresItem": false, "hackType": "none", "sendAlert": true, "isOptional": false, "unlockTime": "", "xpRewardAmount": "0", "rewardItems": [{"rewardChance": 10, "minAmount": 1, "maxAmount": 1, "itemName": "unmarkedsimcard"}, {"rewardChance": 100, "minAmount": 1, "maxAmount": 5, "itemName": "diamondring"}, {"rewardChance": 100, "minAmount": 1, "maxAmount": 7, "itemName": "goldbracelet"}, {"rewardChance": 1, "minAmount": 1, "maxAmount": 3, "itemName": "goldbar"}, {"rewardChance": 100, "minAmount": 5, "maxAmount": 25, "itemName": "bandsofnotes"}], "xpRemoveAmount": "0", "rewardItem": true, "targetVector": "-1221.08, -916.19, 11.33", "requireSimultaneous": false, "rewardItemMin": "1", "requireWeaponInHand": false, "minigameCheckbox": false, "delayRewardCheckbox": true, "requiredWeapons": [], "items": [], "selectedDoorName": "", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "135.00", "rewardMoneyMin": "1", "propName": "", "blackoutDuration": "1", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "1", "difficulty": "", "quantity": "", "dropWalletWithPasscode": false, "type": "robabble_object", "createExplosion": false, "targetText": "Open Safe", "stepNumber": 12, "penaltyType": ""}], "settings": {"blipScale": 1, "blipFlash": false, "alertSound1": "robberysound", "alertCode": "10-90", "alertPriority": 1, "alertIcon": "fas fa-bell", "blipColor": 0, "blipSprite": 59, "cooldownMinutes": 25, "policeRequired": "", "policeJobs": ["police", "leo", "lspd", "sasp", "ranger", "bcso", "trooper"], "robberyCooldown": "25", "policeAlert": "Armed store robbery", "policeAlertHeader": "Armed store robbery", "cooldownHeists": ["store south side", "store robbery vanila unicorn", "store above big bank", "store near casino"], "minutesUntilRobbery": ""}}, {"heist_name": "store mirror park", "stages": [{"penaltyChance": "", "rewardItemName": "salvagedlockpick", "guards": [], "animation": "2", "isRequiredNextStage": false, "lasers": [], "stepName": "Cabinet 1", "rewardMoney": false, "rewardDelayTime": "1", "propHeading": "0.00", "selectedWeapon": "", "removeXpOnHit": false, "rewardItemMax": "1", "pedCoords": "1133.85, -980.60, 45.42", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "cash", "requiresItem": false, "hackType": "none", "sendAlert": false, "isOptional": false, "unlockTime": "", "xpRewardAmount": "0", "rewardItems": [{"rewardChance": 45, "minAmount": 1, "maxAmount": 1, "itemName": "salvagedlockpick"}], "xpRemoveAmount": "0", "rewardItem": true, "targetVector": "1133.21, -980.66, 45.98", "requireSimultaneous": false, "rewardItemMin": "1", "requireWeaponInHand": false, "minigameCheckbox": false, "delayRewardCheckbox": false, "requiredWeapons": [], "items": [], "selectedDoorName": "", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "99.00", "rewardMoneyMin": "1", "propName": "", "blackoutDuration": "1", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "1", "difficulty": "", "quantity": "", "dropWalletWithPasscode": false, "type": "robabble_object", "createExplosion": false, "targetText": "Search Cabinet", "stepNumber": 1, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "salvagedlockpick", "guards": [], "animation": "2", "isRequiredNextStage": false, "lasers": [], "stepName": "Cabinet 2", "rewardMoney": false, "rewardDelayTime": "1", "propHeading": "0.00", "selectedWeapon": "", "removeXpOnHit": false, "rewardItemMax": "1", "pedCoords": "1134.05, -983.25, 45.42", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "cash", "requiresItem": false, "hackType": "none", "sendAlert": false, "isOptional": false, "unlockTime": "", "xpRewardAmount": "0", "rewardItems": [{"rewardChance": 45, "minAmount": 1, "maxAmount": 1, "itemName": "salvagedlockpick"}], "xpRemoveAmount": "0", "rewardItem": true, "targetVector": "1134.67, -983.45, 45.82", "requireSimultaneous": false, "rewardItemMin": "1", "requireWeaponInHand": false, "minigameCheckbox": false, "delayRewardCheckbox": false, "requiredWeapons": [], "items": [], "selectedDoorName": "", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "256.00", "rewardMoneyMin": "1", "propName": "", "blackoutDuration": "1", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "1", "difficulty": "", "quantity": "", "dropWalletWithPasscode": false, "type": "robabble_object", "createExplosion": false, "targetText": "Search Cabinet", "stepNumber": 2, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "salvagedlockpick", "guards": [], "animation": "2", "isRequiredNextStage": false, "lasers": [], "stepName": "Cabinet 3", "rewardMoney": false, "rewardDelayTime": "1", "propHeading": "0.00", "selectedWeapon": "", "removeXpOnHit": false, "rewardItemMax": "1", "pedCoords": "1133.68, -984.68, 45.42", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "cash", "requiresItem": false, "hackType": "none", "sendAlert": false, "isOptional": false, "unlockTime": "", "xpRewardAmount": "0", "rewardItems": [{"rewardChance": 45, "minAmount": 1, "maxAmount": 1, "itemName": "salvagedlockpick"}], "xpRemoveAmount": "0", "rewardItem": true, "targetVector": "1133.58, -985.38, 45.86", "requireSimultaneous": false, "rewardItemMin": "1", "requireWeaponInHand": false, "minigameCheckbox": false, "delayRewardCheckbox": false, "requiredWeapons": [], "items": [], "selectedDoorName": "", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "162.00", "rewardMoneyMin": "1", "propName": "", "blackoutDuration": "1", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "1", "difficulty": "", "quantity": "", "dropWalletWithPasscode": false, "type": "robabble_object", "createExplosion": false, "targetText": "Search Cabinet", "stepNumber": 3, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "bandsofnotes", "guards": [], "animation": "19", "isRequiredNextStage": false, "lasers": [], "stepName": "register 1", "rewardMoney": false, "rewardDelayTime": "1", "propHeading": "0.00", "selectedWeapon": "", "removeXpOnHit": false, "rewardItemMax": "70", "pedCoords": "1134.17, -982.45, 45.42", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "cash", "requiresItem": true, "hackType": "inspired-lockpick", "sendAlert": true, "isOptional": false, "unlockTime": "", "xpRewardAmount": "0", "rewardItems": [{"rewardChance": 80, "minAmount": 25, "maxAmount": 70, "itemName": "bandsofnotes"}, {"rewardChance": 50, "minAmount": 25, "maxAmount": 70, "itemName": "stacksofcash"}], "xpRemoveAmount": "0", "rewardItem": true, "targetVector": "1134.86, -982.30, 46.58", "requireSimultaneous": false, "rewardItemMin": "25", "requireWeaponInHand": false, "minigameCheckbox": false, "delayRewardCheckbox": false, "requiredWeapons": [], "items": [{"loseChance": 30, "itemName": "advancedlockpick", "itemQuantity": 1}, {"loseChance": 40, "itemName": "salvagedlockpick", "itemQuantity": 1}], "selectedDoorName": "", "itemName": "advancedlockpick", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "312.00", "rewardMoneyMin": "1", "propName": "", "blackoutDuration": "1", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "1", "difficulty": "", "quantity": "1", "dropWalletWithPasscode": false, "type": "robabble_object", "createExplosion": false, "targetText": "Lockpick Register", "stepNumber": 4, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "", "type": "guards", "animation": "", "isRequiredNextStage": false, "lasers": [], "stepName": "guard 1", "rewardMoney": false, "rewardDelayTime": "", "propHeading": "", "selectedWeapon": "", "removeXpOnHit": false, "hackType": "s_m_m_chemsec_01", "targetVector": "", "walletDropChance": 70, "pedCoords": "", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "", "requiresItem": false, "requiredWeapons": [], "sendAlert": false, "isOptional": false, "rewardItemMin": "", "xpRewardAmount": "0", "rewardItems": [], "xpRemoveAmount": "0", "rewardItem": false, "requireWeaponInHand": false, "requireSimultaneous": false, "rewardMoneyMin": "", "delayRewardCheckbox": false, "minigameCheckbox": false, "selectedDoorName": "", "quantity": "", "items": [], "minigameType": "s_m_m_chemsec_01", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "propName": "", "xpNeededAmount": "0", "heading": "", "blackoutDuration": "", "rewardItemQuantity": "", "rewardMoneyMax": "", "unlockTimeCheckbox": false, "showEnemyBlip": true, "unlockTime": "", "difficulty": "", "guards": [{"guardModel": "s_m_m_chemsec_01", "weapon": "weapon_appistol", "heading": 162, "guardPosition": [1143.36, -978.84, 45.3]}], "createExplosion": false, "dropWalletWithPasscode": true, "rewardItemMax": "", "targetText": "", "stepNumber": 5, "penaltyType": ""}, {"penaltyChance": "100", "rewardItemName": "", "guards": [], "animation": "8", "isRequiredNextStage": false, "lasers": [], "stepName": "hackable door", "rewardMoney": false, "rewardDelayTime": "", "propHeading": "96.00", "selectedWeapon": "", "removeXpOnHit": false, "hackType": "none", "targetVector": "1133.04, -980.10, 46.80", "rewardItemMax": "", "pedCoords": "1133.95, -980.05, 45.42", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "", "requiresItem": false, "requiredWeapons": [], "sendAlert": true, "isOptional": false, "rewardItemMin": "", "xpRewardAmount": "0", "rewardItems": [], "xpRemoveAmount": "0", "rewardItem": false, "requireWeaponInHand": false, "requireSimultaneous": false, "minigameType": "", "delayRewardCheckbox": false, "minigameCheckbox": false, "selectedDoorName": "store mirror park", "unlockTime": "5", "items": [], "itemName": "", "doorPosition": "1132.52, -978.63, 46.50", "triggersBlackout": false, "penaltyForFailing": false, "doorId": "store mirror park", "xpNeededAmount": "0", "heading": "96.00", "rewardMoneyMin": "", "propName": "prop_ld_keypad_01b", "blackoutDuration": "1", "unlockTimeCheckbox": true, "rewardItemQuantity": "", "rewardMoneyMax": "", "difficulty": "", "quantity": "", "type": "hackable_door", "dropWalletWithPasscode": false, "createExplosion": false, "targetText": "Enter Code", "stepNumber": 6, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "", "guards": [], "animation": "", "isRequiredNextStage": false, "lasers": [], "stepName": "custom door", "rewardMoney": false, "rewardDelayTime": "", "propHeading": "97.00", "selectedWeapon": "", "removeXpOnHit": false, "rewardItemMax": "", "pedCoords": "", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "", "requiresItem": false, "hackType": "", "sendAlert": false, "isOptional": false, "unlockTime": "", "xpRewardAmount": "0", "rewardItems": [], "xpRemoveAmount": "0", "rewardItem": false, "targetVector": "1132.52, -978.63, 46.50", "requireSimultaneous": false, "rewardItemMin": "", "requireWeaponInHand": false, "minigameCheckbox": false, "delayRewardCheckbox": false, "requiredWeapons": [], "items": [], "selectedDoorName": "", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "", "rewardMoneyMin": "", "propName": "hei_v_ilev_bk_safegate_pris", "blackoutDuration": "", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "", "difficulty": "", "quantity": "", "dropWalletWithPasscode": false, "type": "custom_door", "createExplosion": false, "targetText": "store mirror park", "stepNumber": 7, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "", "guards": [], "animation": "h4_prop_h4_safe_01a", "isRequiredNextStage": false, "lasers": [], "stepName": "Big safe", "rewardMoney": false, "rewardDelayTime": "1", "propHeading": "95.00", "selectedWeapon": "", "removeXpOnHit": false, "rewardItemMax": "", "pedCoords": "1125.88, -982.66, 44.42", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "cash", "requiresItem": false, "hackType": "safe-cracker-easy", "sendAlert": true, "isOptional": false, "unlockTime": "", "xpRewardAmount": "0", "rewardItems": [], "xpRemoveAmount": "0", "rewardItem": false, "targetVector": "1125.88, -982.66, 44.42", "requireSimultaneous": false, "rewardItemMin": "", "requireWeaponInHand": false, "minigameCheckbox": false, "delayRewardCheckbox": false, "requiredWeapons": [], "items": [], "selectedDoorName": "", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "95.00", "rewardMoneyMin": "1", "propName": "h4_prop_h4_safe_01a", "blackoutDuration": "1", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "1", "difficulty": "", "quantity": "", "dropWalletWithPasscode": false, "type": "robabble_object", "createExplosion": false, "targetText": "Open Safe", "stepNumber": 8, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "drill", "guards": [], "animation": "16", "isRequiredNextStage": true, "lasers": [], "stepName": "drill", "rewardMoney": false, "rewardDelayTime": "1", "propHeading": "140.00", "selectedWeapon": "", "removeXpOnHit": false, "rewardItemMax": "1", "pedCoords": "1126.83, -982.67, 44.42", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "cash", "requiresItem": false, "hackType": "none", "sendAlert": false, "isOptional": false, "unlockTime": "", "xpRewardAmount": "0", "rewardItems": [{"rewardChance": 40, "minAmount": 1, "maxAmount": 1, "itemName": "drill"}], "xpRemoveAmount": "0", "rewardItem": true, "targetVector": "1125.82, -982.69, 44.70", "requireSimultaneous": false, "rewardItemMin": "1", "requireWeaponInHand": false, "minigameCheckbox": false, "delayRewardCheckbox": false, "requiredWeapons": [], "items": [], "selectedDoorName": "", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "69.00", "rewardMoneyMin": "1", "propName": "ch_prop_ch_heist_drill", "blackoutDuration": "1", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "1", "difficulty": "", "quantity": "", "dropWalletWithPasscode": false, "type": "robabble_object", "createExplosion": false, "targetText": "<PERSON><PERSON>", "stepNumber": 9, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "", "guards": [], "animation": "", "isRequiredNextStage": false, "lasers": [], "hackType": "", "stepName": "ending robbery", "rewardMoney": false, "rewardDelayTime": "", "targetVector": "", "propHeading": "", "selectedWeapon": "", "removeXpOnHit": false, "endingTargetVector": "-163.46, -1076.45, 41.14", "delayRewardCheckbox": false, "rewardItemMax": "", "pedCoords": "", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "", "requiresItem": false, "maxValue": 100, "sendAlert": false, "isOptional": false, "rewardItemMin": "", "xpRewardAmount": "0", "rewardItems": [], "xpRemoveAmount": "0", "rewardItem": false, "requireWeaponInHand": false, "requireSimultaneous": false, "createExplosion": false, "minValue": 0, "minigameCheckbox": false, "endingHeading": "90.00", "requiredWeapons": [], "items": [], "selectedDoorName": "", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "", "rewardMoneyMin": "", "propName": "", "blackoutDuration": "", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "", "difficulty": "", "unlockTime": "", "quantity": "", "dropWalletWithPasscode": false, "type": "ending_robbery", "targetText": "", "stepNumber": 10, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "", "guards": [], "animation": "15", "isRequiredNextStage": true, "lasers": [], "stepName": "Safe Money", "rewardMoney": true, "rewardDelayTime": "1", "propHeading": "90.00", "selectedWeapon": "", "removeXpOnHit": false, "rewardItemMax": "", "pedCoords": "1126.49, -982.50, 44.42", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "cash", "requiresItem": false, "hackType": "none", "sendAlert": false, "isOptional": false, "unlockTime": "", "xpRewardAmount": "0", "rewardItems": [], "xpRemoveAmount": "0", "rewardItem": false, "targetVector": "1125.81, -982.69, 45.45", "requireSimultaneous": false, "rewardItemMin": "", "requireWeaponInHand": false, "minigameCheckbox": false, "delayRewardCheckbox": false, "requiredWeapons": [], "items": [], "selectedDoorName": "", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "72.00", "rewardMoneyMin": "200", "propName": "bkr_prop_bkr_cashpile_04", "blackoutDuration": "1", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "700", "difficulty": "", "quantity": "", "dropWalletWithPasscode": false, "type": "robabble_object", "createExplosion": false, "targetText": "Grab Money", "stepNumber": 11, "penaltyType": ""}, {"penaltyChance": "", "rewardItemName": "unmarkedsimcard", "guards": [], "animation": "19", "isRequiredNextStage": true, "lasers": [], "stepName": "small safe", "rewardMoney": false, "rewardDelayTime": "300", "propHeading": "0.00", "selectedWeapon": "", "removeXpOnHit": false, "rewardItemMax": "1", "pedCoords": "1126.71, -980.39, 44.42", "xpOptions": false, "lootableItems": [], "rewardMoneyType": "cash", "requiresItem": false, "hackType": "none", "sendAlert": true, "isOptional": false, "unlockTime": "", "xpRewardAmount": "0", "rewardItems": [{"rewardChance": 10, "minAmount": 1, "maxAmount": 1, "itemName": "unmarkedsimcard"}, {"rewardChance": 100, "minAmount": 1, "maxAmount": 5, "itemName": "diamondring"}, {"rewardChance": 100, "minAmount": 1, "maxAmount": 7, "itemName": "goldbracelet"}, {"rewardChance": 1, "minAmount": 1, "maxAmount": 3, "itemName": "goldbar"}, {"rewardChance": 100, "minAmount": 5, "maxAmount": 25, "itemName": "bandsofnotes"}], "xpRemoveAmount": "0", "rewardItem": true, "targetVector": "1126.79, -979.81, 45.33", "requireSimultaneous": false, "rewardItemMin": "1", "requireWeaponInHand": false, "minigameCheckbox": false, "delayRewardCheckbox": true, "requiredWeapons": [], "items": [], "selectedDoorName": "", "itemName": "", "triggersBlackout": false, "penaltyForFailing": false, "minigameType": "", "xpNeededAmount": "0", "heading": "11.00", "rewardMoneyMin": "1", "propName": "", "blackoutDuration": "1", "unlockTimeCheckbox": false, "rewardItemQuantity": "", "rewardMoneyMax": "1", "difficulty": "", "quantity": "", "dropWalletWithPasscode": false, "type": "robabble_object", "createExplosion": false, "targetText": "Open Safe", "stepNumber": 12, "penaltyType": ""}], "settings": {"blipScale": 1, "blipFlash": false, "alertSound1": "robberysound", "alertCode": "10-90", "alertPriority": 1, "alertIcon": "fas fa-bell", "blipColor": 0, "blipSprite": 59, "cooldownMinutes": 25, "policeRequired": "", "policeJobs": ["police", "leo", "lspd", "sasp", "ranger", "bcso", "trooper"], "robberyCooldown": "25", "policeAlert": "Armed store robbery", "policeAlertHeader": "Armed store robbery", "cooldownHeists": ["store south side", "store robbery vanila unicorn", "store above big bank", "store near casino", "store burgershot"], "minutesUntilRobbery": ""}}, {"heist_name": "bank fleeca legion", "stages": [{"lasers": [{"intensity": 100, "startVector": [144.8, -1042.7, 31.27], "movement": {"speed": 0.007, "amount": 2.4, "direction": "down"}, "endVector": [143.84, -1045.32, 31.31]}, {"startVector": [144.7, -1042.67, 31.33], "intensity": 100, "endVector": [147.19, -1046.54, 28.37]}, {"startVector": [148.16, -1043.97, 28.39], "intensity": 100, "endVector": [143.74, -1045.27, 31.36]}], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "1", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 1, "penaltyForFailing": false, "rewardItemMax": "", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [], "triggersBlackout": false, "rewardDelayTime": "", "guards": [], "rewardMoneyType": "", "rewardItemName": "", "stepName": "lasers", "rewardItem": false, "rewardItemMin": "", "pedCoords": "129.73, -1044.77, 28.28", "heading": "246.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "", "minigameCheckbox": false, "type": "lasers", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Shut Lasers Off", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "129.73, -1044.77, 28.28", "sendAlert": false, "createExplosion": false, "animation": "tr_prop_tr_elecbox_01a", "propHeading": "246.00", "propName": "tr_prop_tr_elecbox_01a", "hackType": "key_drop_game"}, {"lasers": [], "triggersBlackout": false, "unlockTimeCheckbox": true, "isOptional": false, "items": [{"itemName": "bankcard", "loseChance": 50, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "penaltyType": "electrocute", "minigameCheckbox": false, "rewardItemQuantity": "", "unlockTime": "5", "itemName": "bankcard", "selectedWeapon": "", "xpRemoveAmount": "0", "requiresItem": true, "stepNumber": 2, "penaltyForFailing": true, "xpOptions": false, "doorPosition": "148.03, -1044.36, 29.51", "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItem": false, "guards": [], "rewardMoneyType": "", "rewardItemName": "", "minigameType": "", "rewardItemMin": "", "pedCoords": "146.76, -1045.74, 28.37", "stepName": "vault door", "heading": "224.00", "xpNeededAmount": "0", "penaltyChance": "50", "rewardMoneyMin": "", "isRequiredNextStage": false, "type": "hackable_door", "selectedDoorName": "fleeca legion bank vault", "requiredWeapons": [], "delayRewardCheckbox": false, "targetText": "Hack <PERSON>", "rewardItemMax": "", "doorId": "1000", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "147.17, -1046.18, 29.57", "sendAlert": true, "createExplosion": false, "animation": "hei_prop_hei_securitypanel", "propHeading": "250.00", "propName": "hei_prop_hei_securitypanel", "hackType": "ultra-voltlab"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": true, "stepNumber": 3, "penaltyForFailing": false, "rewardItemMax": "150", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 150, "minAmount": 75}, {"itemName": "stacksofcash", "rewardChance": 50, "maxAmount": 145, "minAmount": 100}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "bandsofnotes", "stepName": "cash tray 1", "rewardItem": true, "rewardItemMin": "75", "pedCoords": "148.99, -1044.68, 28.35", "heading": "157.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Grab Money", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "148.99, -1044.68, 28.35", "sendAlert": false, "createExplosion": false, "animation": "ch_prop_cash_low_trolly_01a", "propHeading": "157.00", "propName": "ch_prop_cash_low_trolly_01a", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [{"itemName": "drill", "loseChance": 100, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 4, "penaltyForFailing": false, "rewardItemMax": "150", "requiresItem": true, "requireWeaponInHand": false, "rewardItems": [{"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 150, "minAmount": 50}, {"itemName": "stacksofcash", "rewardChance": 25, "maxAmount": 125, "minAmount": 50}, {"itemName": "purpleusb", "rewardChance": 25, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "bandsofnotes", "stepName": "drilling", "rewardItem": true, "rewardItemMin": "50", "pedCoords": "150.32, -1045.60, 28.35", "heading": "333.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Drill", "itemName": "drill", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "150.81, -1044.97, 29.42", "sendAlert": false, "createExplosion": false, "animation": "12", "propHeading": "0.00", "propName": "", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": true, "stepNumber": 5, "penaltyForFailing": false, "rewardItemMax": "150", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 150, "minAmount": 75}, {"itemName": "stacksofcash", "rewardChance": 50, "maxAmount": 145, "minAmount": 100}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "bandsofnotes", "stepName": "cash tray 2", "rewardItem": true, "rewardItemMin": "75", "pedCoords": "150.88, -1046.82, 28.35", "heading": "68.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Grab Money", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "150.88, -1046.82, 28.35", "sendAlert": false, "createExplosion": false, "animation": "ch_prop_cash_low_trolly_01a", "propHeading": "68.00", "propName": "ch_prop_cash_low_trolly_01a", "hackType": "none"}, {"lasers": [], "triggersBlackout": false, "unlockTimeCheckbox": true, "isOptional": false, "items": [{"itemName": "thermite", "loseChance": 25, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "penaltyType": "fire", "minigameCheckbox": false, "rewardItemQuantity": "", "unlockTime": "10", "itemName": "thermite", "selectedWeapon": "", "xpRemoveAmount": "0", "requiresItem": true, "stepNumber": 6, "penaltyForFailing": true, "xpOptions": false, "doorPosition": "150.29, -1047.63, 29.67", "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItem": false, "guards": [], "rewardMoneyType": "", "rewardItemName": "", "minigameType": "", "rewardItemMin": "", "pedCoords": "149.21, -1046.77, 28.35", "stepName": "thermite door", "heading": "159.00", "xpNeededAmount": "0", "penaltyChance": "50", "rewardMoneyMin": "", "isRequiredNextStage": true, "type": "hackable_door", "selectedDoorName": "fleeca legion gate", "requiredWeapons": [], "delayRewardCheckbox": false, "targetText": "Thermite Door", "rewardItemMax": "", "doorId": "1001", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "148.81, -1047.10, 29.61", "sendAlert": true, "createExplosion": false, "animation": "10", "propHeading": "0.00", "propName": "", "hackType": "inspired-thermite"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "", "removeXpOnHit": false, "lootableItems": [{"name": "armor", "chance": 25, "maxAmount": 3, "minAmount": 1}, {"name": "group6card", "chance": 15, "maxAmount": 2, "minAmount": 1}], "penaltyType": "", "rewardItemQuantity": "", "unlockTime": "", "sendAlert": false, "selectedWeapon": "", "xpRemoveAmount": "0", "rewardItemMax": "", "stepNumber": 7, "penaltyForFailing": false, "requiresItem": false, "showEnemyBlip": false, "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItemMin": "", "guards": [{"guardPosition": [150.93, -1048.72, 28.35], "heading": 79, "weapon": "weapon_snspistol", "guardModel": "s_m_y_cop_01"}], "itemName": "", "rewardItemName": "", "minigameType": "s_m_y_cop_01", "minigameCheckbox": false, "triggersBlackout": false, "delayRewardCheckbox": false, "heading": "", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "", "isRequiredNextStage": false, "type": "guards", "selectedDoorName": "", "requiredWeapons": [], "rewardItem": false, "targetText": "", "rewardMoneyType": "", "stepName": "guards", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "pedCoords": "", "targetVector": "", "quantity": "", "createExplosion": false, "animation": "", "propHeading": "", "propName": "", "hackType": "s_m_y_cop_01"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 8, "penaltyForFailing": false, "rewardItemMax": "7", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "goldbar", "rewardChance": 100, "maxAmount": 7, "minAmount": 2}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "goldbar", "stepName": "gold bars", "rewardItem": true, "rewardItemMin": "2", "pedCoords": "148.57, -1049.18, 29.20", "heading": "158.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Grab Gold Bars", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "148.57, -1049.18, 29.20", "sendAlert": true, "createExplosion": false, "animation": "h4_prop_h4_gold_stack_01a", "propHeading": "158.00", "propName": "h4_prop_h4_gold_stack_01a", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [{"itemName": "drill", "loseChance": 10, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 9, "penaltyForFailing": false, "rewardItemMax": "100", "requiresItem": true, "requireWeaponInHand": false, "rewardItems": [{"itemName": "stacksofcash", "rewardChance": 100, "maxAmount": 100, "minAmount": 50}, {"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 75, "minAmount": 25}, {"itemName": "goldwatch", "rewardChance": 100, "maxAmount": 3, "minAmount": 1}, {"itemName": "goldbar", "rewardChance": 50, "maxAmount": 2, "minAmount": 1}, {"itemName": "group6card", "rewardChance": 10, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "stacksofcash", "stepName": "drill 1", "rewardItem": true, "rewardItemMin": "50", "pedCoords": "150.07, -1049.82, 28.35", "heading": "254.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Drill", "itemName": "drill", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "150.58, -1049.91, 29.51", "sendAlert": true, "createExplosion": false, "animation": "12", "propHeading": "0.00", "propName": "", "hackType": "drilllaser"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [{"itemName": "drill", "loseChance": 10, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 10, "penaltyForFailing": false, "rewardItemMax": "100", "requiresItem": true, "requireWeaponInHand": false, "rewardItems": [{"itemName": "stacksofcash", "rewardChance": 100, "maxAmount": 100, "minAmount": 50}, {"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 75, "minAmount": 25}, {"itemName": "goldwatch", "rewardChance": 100, "maxAmount": 3, "minAmount": 1}, {"itemName": "goldbar", "rewardChance": 50, "maxAmount": 2, "minAmount": 1}, {"itemName": "group6card", "rewardChance": 10, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "stacksofcash", "stepName": "drill 2", "rewardItem": true, "rewardItemMin": "50", "pedCoords": "148.33, -1050.24, 28.35", "heading": "158.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Drill", "itemName": "drill", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "147.95, -1050.99, 29.34", "sendAlert": true, "createExplosion": false, "animation": "12", "propHeading": "0.00", "propName": "", "hackType": "drilllaser"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [{"itemName": "drill", "loseChance": 10, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 11, "penaltyForFailing": false, "rewardItemMax": "100", "requiresItem": true, "requireWeaponInHand": false, "rewardItems": [{"itemName": "stacksofcash", "rewardChance": 100, "maxAmount": 100, "minAmount": 50}, {"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 75, "minAmount": 25}, {"itemName": "goldwatch", "rewardChance": 100, "maxAmount": 3, "minAmount": 1}, {"itemName": "goldbar", "rewardChance": 50, "maxAmount": 2, "minAmount": 1}, {"itemName": "group6card", "rewardChance": 10, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "stacksofcash", "stepName": "drill 3", "rewardItem": true, "rewardItemMin": "50", "pedCoords": "147.27, -1048.53, 28.35", "heading": "59.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Drill", "itemName": "drill", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "146.52, -1048.50, 29.35", "sendAlert": true, "createExplosion": false, "animation": "12", "propHeading": "0.00", "propName": "", "hackType": "drilllaser"}], "settings": {"blipScale": 1, "blipFlash": false, "alertSound1": "robberysound", "alertCode": "10-90", "alertPriority": 1, "alertIcon": "fas fa-bell", "blipColor": 2, "blipSprite": 500, "cooldownMinutes": 15, "policeRequired": "", "policeJobs": ["police", "leo", "lspd", "sasp", "ranger", "bcso", "trooper"], "robberyCooldown": "35", "policeAlert": "Arned Bank Robbery", "policeAlertHeader": "Arned Bank Robbery", "cooldownHeists": ["store south side", "store robbery vanila unicorn", "store above big bank", "store near casino", "store burgershot", "store mirror park"], "minutesUntilRobbery": ""}}, {"heist_name": "bank fleeca above pillbox", "stages": [{"lasers": [{"intensity": 100, "startVector": [308.15, -283.68, 55.9], "movement": {"speed": 0.007, "amount": 2.4, "direction": "down"}, "endVector": [309.06, -281.04, 55.93]}, {"startVector": [308.07, -283.64, 56.15], "intensity": 100, "endVector": [312.49, -282.31, 53.19]}, {"startVector": [311.55, -284.89, 53.16], "intensity": 100, "endVector": [309.05, -281.04, 56.12]}], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "1", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 1, "penaltyForFailing": false, "rewardItemMax": "", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [], "triggersBlackout": false, "rewardDelayTime": "", "guards": [], "rewardMoneyType": "", "rewardItemName": "", "stepName": "lasers", "rewardItem": false, "rewardItemMin": "", "pedCoords": "287.55, -304.48, 48.87", "heading": "245.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "", "minigameCheckbox": false, "type": "lasers", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Shut Lasers Off", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "288.15, -304.78, 50.28", "sendAlert": false, "createExplosion": false, "animation": "ch_prop_fingerprint_scanner_01e", "propHeading": "248.00", "propName": "ch_prop_fingerprint_scanner_01e", "hackType": ""}, {"lasers": [], "triggersBlackout": false, "unlockTimeCheckbox": true, "isOptional": false, "items": [{"itemName": "bankcard", "loseChance": 50, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "penaltyType": "electrocute", "minigameCheckbox": false, "rewardItemQuantity": "", "unlockTime": "5", "itemName": "bankcard", "selectedWeapon": "", "xpRemoveAmount": "0", "requiresItem": true, "stepNumber": 2, "penaltyForFailing": true, "xpOptions": false, "doorPosition": "312.36, -282.73, 54.30", "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItem": false, "guards": [], "rewardMoneyType": "", "rewardItemName": "", "minigameType": "", "rewardItemMin": "", "pedCoords": "310.97, -284.46, 53.16", "stepName": "vault door", "heading": "249.00", "xpNeededAmount": "0", "penaltyChance": "50", "rewardMoneyMin": "", "isRequiredNextStage": true, "type": "hackable_door", "selectedDoorName": "bank fleeca above pillbox vault", "requiredWeapons": [], "delayRewardCheckbox": false, "targetText": "", "rewardItemMax": "", "doorId": "1004", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "311.50, -284.58, 54.36", "sendAlert": true, "createExplosion": false, "animation": "hei_prop_hei_securitypanel", "propHeading": "249.00", "propName": "hei_prop_hei_securitypanel", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": true, "stepNumber": 3, "penaltyForFailing": false, "rewardItemMax": "150", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 150, "minAmount": 75}, {"itemName": "stacksofcash", "rewardChance": 50, "maxAmount": 145, "minAmount": 100}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "bandsofnotes", "stepName": "cash tray 1", "rewardItem": true, "rewardItemMin": "75", "pedCoords": "313.38, -283.04, 53.14", "heading": "160.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Grab Money", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "313.38, -283.04, 53.14", "sendAlert": false, "createExplosion": false, "animation": "ch_prop_cash_low_trolly_01a", "propHeading": "160.00", "propName": "ch_prop_cash_low_trolly_01a", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [{"itemName": "drill", "loseChance": 100, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 4, "penaltyForFailing": false, "rewardItemMax": "150", "requiresItem": true, "requireWeaponInHand": false, "rewardItems": [{"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 150, "minAmount": 50}, {"itemName": "stacksofcash", "rewardChance": 25, "maxAmount": 125, "minAmount": 50}, {"itemName": "purpleusb", "rewardChance": 25, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "bandsofnotes", "stepName": "drilling", "rewardItem": true, "rewardItemMin": "50", "pedCoords": "314.79, -283.84, 53.14", "heading": "318.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Drill", "itemName": "drill", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "315.08, -283.31, 54.23", "sendAlert": false, "createExplosion": false, "animation": "12", "propHeading": "0.00", "propName": "", "hackType": "drilllaser"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": true, "stepNumber": 5, "penaltyForFailing": false, "rewardItemMax": "150", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 150, "minAmount": 75}, {"itemName": "stacksofcash", "rewardChance": 50, "maxAmount": 145, "minAmount": 100}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "bandsofnotes", "stepName": "cash tray 2", "rewardItem": true, "rewardItemMin": "75", "pedCoords": "315.24, -285.26, 53.14", "heading": "65.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Grab Money", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "315.24, -285.26, 53.14", "sendAlert": false, "createExplosion": false, "animation": "ch_prop_cash_low_trolly_01a", "propHeading": "65.00", "propName": "ch_prop_cash_low_trolly_01a", "hackType": "none"}, {"lasers": [], "triggersBlackout": false, "unlockTimeCheckbox": true, "isOptional": false, "items": [{"itemName": "thermite", "loseChance": 25, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "penaltyType": "fire", "minigameCheckbox": false, "rewardItemQuantity": "", "unlockTime": "10", "itemName": "thermite", "selectedWeapon": "", "xpRemoveAmount": "0", "requiresItem": true, "stepNumber": 6, "penaltyForFailing": true, "xpOptions": false, "doorPosition": "314.62, -285.99, 54.46", "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItem": false, "guards": [], "rewardMoneyType": "", "rewardItemName": "", "minigameType": "", "rewardItemMin": "", "pedCoords": "313.62, -285.16, 53.14", "stepName": "thermite door", "heading": "153.00", "xpNeededAmount": "0", "penaltyChance": "50", "rewardMoneyMin": "", "isRequiredNextStage": true, "type": "hackable_door", "selectedDoorName": "bank fleeca gate above pillbox", "requiredWeapons": [], "delayRewardCheckbox": false, "targetText": "Thermite Door", "rewardItemMax": "", "doorId": "1005", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "313.22, -285.46, 54.44", "sendAlert": true, "createExplosion": false, "animation": "10", "propHeading": "0.00", "propName": "", "hackType": "inspired-thermite"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "", "removeXpOnHit": false, "lootableItems": [{"name": "armor", "chance": 25, "maxAmount": 3, "minAmount": 1}, {"name": "group6card", "chance": 15, "maxAmount": 2, "minAmount": 1}], "penaltyType": "", "rewardItemQuantity": "", "unlockTime": "", "sendAlert": false, "selectedWeapon": "", "xpRemoveAmount": "0", "rewardItemMax": "", "stepNumber": 7, "penaltyForFailing": false, "requiresItem": false, "showEnemyBlip": false, "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItemMin": "", "guards": [{"guardPosition": [315, -286.74, 53.14], "heading": 70, "weapon": "weapon_minismg", "guardModel": "s_m_m_chemsec_01"}], "itemName": "", "rewardItemName": "", "minigameType": "s_m_m_chemsec_01", "minigameCheckbox": false, "triggersBlackout": false, "delayRewardCheckbox": false, "heading": "", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "", "isRequiredNextStage": false, "type": "guards", "selectedDoorName": "", "requiredWeapons": [], "rewardItem": false, "targetText": "", "rewardMoneyType": "", "stepName": "guards", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "pedCoords": "", "targetVector": "", "quantity": "", "createExplosion": false, "animation": "", "propHeading": "", "propName": "", "hackType": "s_m_m_chemsec_01"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 8, "penaltyForFailing": false, "rewardItemMax": "7", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "goldbar", "rewardChance": 100, "maxAmount": 7, "minAmount": 2}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "goldbar", "stepName": "gold bars", "rewardItem": true, "rewardItemMin": "2", "pedCoords": "312.84, -287.49, 53.99", "heading": "158.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Grab Gold Bard", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "312.84, -287.49, 53.99", "sendAlert": true, "createExplosion": false, "animation": "h4_prop_h4_gold_stack_01a", "propHeading": "158.00", "propName": "h4_prop_h4_gold_stack_01a", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [{"itemName": "drill", "loseChance": 10, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 9, "penaltyForFailing": false, "rewardItemMax": "100", "requiresItem": true, "requireWeaponInHand": false, "rewardItems": [{"itemName": "stacksofcash", "rewardChance": 100, "maxAmount": 100, "minAmount": 50}, {"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 75, "minAmount": 25}, {"itemName": "goldwatch", "rewardChance": 100, "maxAmount": 3, "minAmount": 1}, {"itemName": "goldbar", "rewardChance": 50, "maxAmount": 2, "minAmount": 1}, {"itemName": "group6card", "rewardChance": 10, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "stacksofcash", "stepName": "drill 1", "rewardItem": true, "rewardItemMin": "50", "pedCoords": "314.62, -288.27, 53.14", "heading": "240.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Drill", "itemName": "drill", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "315.19, -288.30, 54.16", "sendAlert": true, "createExplosion": false, "animation": "12", "propHeading": "0.00", "propName": "", "hackType": "drilllaser"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [{"itemName": "drill", "loseChance": 10, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 10, "penaltyForFailing": false, "rewardItemMax": "100", "requiresItem": true, "requireWeaponInHand": false, "rewardItems": [{"itemName": "stacksofcash", "rewardChance": 100, "maxAmount": 100, "minAmount": 50}, {"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 75, "minAmount": 25}, {"itemName": "goldwatch", "rewardChance": 100, "maxAmount": 3, "minAmount": 1}, {"itemName": "goldbar", "rewardChance": 50, "maxAmount": 2, "minAmount": 1}, {"itemName": "group6card", "rewardChance": 10, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "stacksofcash", "stepName": "drill 2", "rewardItem": true, "rewardItemMin": "50", "pedCoords": "312.32, -288.74, 53.14", "heading": "155.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Drill", "itemName": "drill", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "312.18, -289.31, 54.04", "sendAlert": true, "createExplosion": false, "animation": "12", "propHeading": "0.00", "propName": "", "hackType": "drilllaser"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [{"itemName": "drill", "loseChance": 10, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 11, "penaltyForFailing": false, "rewardItemMax": "100", "requiresItem": true, "requireWeaponInHand": false, "rewardItems": [{"itemName": "stacksofcash", "rewardChance": 100, "maxAmount": 100, "minAmount": 50}, {"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 75, "minAmount": 25}, {"itemName": "goldwatch", "rewardChance": 100, "maxAmount": 3, "minAmount": 1}, {"itemName": "goldbar", "rewardChance": 50, "maxAmount": 2, "minAmount": 1}, {"itemName": "group6card", "rewardChance": 10, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "stacksofcash", "stepName": "drill 3", "rewardItem": true, "rewardItemMin": "50", "pedCoords": "311.48, -286.91, 53.14", "heading": "72.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Drill", "itemName": "drill", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "310.93, -286.67, 54.02", "sendAlert": true, "createExplosion": false, "animation": "12", "propHeading": "0.00", "propName": "", "hackType": "drilllaser"}], "settings": {"blipScale": 1, "blipFlash": false, "alertSound1": "robberysound", "alertCode": "10-90", "alertPriority": 1, "alertIcon": "fas fa-bell", "blipColor": 2, "blipSprite": 500, "cooldownMinutes": 25, "policeRequired": "", "policeJobs": ["police", "leo", "lspd", "sasp", "ranger", "bcso", "trooper"], "robberyCooldown": "25", "policeAlert": "Armed Bank robbery", "policeAlertHeader": "Armed Bank robbery", "cooldownHeists": ["store south side", "store robbery vanila unicorn", "store above big bank", "store near casino", "store burgershot", "store mirror park", "bank fleeca legion"], "minutesUntilRobbery": ""}}, {"heist_name": "bank fleeca ls customs", "stages": [{"lasers": [{"intensity": 100, "startVector": [-356.03, -51.95, 50.96], "movement": {"speed": 0.007, "amount": 2.5, "direction": "down"}, "endVector": [-356.94, -54.59, 50.95]}, {"startVector": [-357, -54.57, 50.99], "intensity": 100, "endVector": [-352.65, -53.14, 48.07]}, {"startVector": [-353.5, -55.75, 48.07], "intensity": 100, "endVector": [-356.1, -51.94, 51.03]}], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "1", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 1, "penaltyForFailing": false, "rewardItemMax": "", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [], "triggersBlackout": false, "rewardDelayTime": "", "guards": [], "rewardMoneyType": "", "rewardItemName": "", "stepName": "lasers", "rewardItem": false, "rewardItemMin": "", "pedCoords": "-385.60, -49.97, 48.03", "heading": "155.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "", "minigameCheckbox": false, "type": "lasers", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Shut Lasers Off", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-385.94, -50.60, 49.43", "sendAlert": true, "createExplosion": false, "animation": "ch_prop_fingerprint_scanner_01e", "propHeading": "155.00", "propName": "ch_prop_fingerprint_scanner_01e", "hackType": "inspired-thermite"}, {"lasers": [], "triggersBlackout": false, "unlockTimeCheckbox": true, "isOptional": false, "items": [{"itemName": "bankcard", "loseChance": 50, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "penaltyType": "electrocute", "minigameCheckbox": false, "rewardItemQuantity": "", "unlockTime": "5", "itemName": "bankcard", "selectedWeapon": "", "xpRemoveAmount": "0", "requiresItem": true, "stepNumber": 2, "penaltyForFailing": true, "xpOptions": false, "doorPosition": "-352.74, -53.57, 49.18", "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItem": false, "guards": [], "rewardMoneyType": "", "rewardItemName": "", "minigameType": "", "rewardItemMin": "", "pedCoords": "-353.96, -55.12, 48.04", "stepName": "vault door", "heading": "246.00", "xpNeededAmount": "0", "penaltyChance": "50", "rewardMoneyMin": "", "isRequiredNextStage": false, "type": "hackable_door", "selectedDoorName": "bank fleeca vault ls customs", "requiredWeapons": [], "delayRewardCheckbox": false, "targetText": "Hack <PERSON>", "rewardItemMax": "", "doorId": "1006", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "-353.59, -55.41, 49.23", "sendAlert": true, "createExplosion": false, "animation": "hei_prop_hei_securitypanel", "propHeading": "246.00", "propName": "hei_prop_hei_securitypanel", "hackType": "inspired-thermite"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": true, "stepNumber": 3, "penaltyForFailing": false, "rewardItemMax": "150", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 150, "minAmount": 75}, {"itemName": "stacksofcash", "rewardChance": 50, "maxAmount": 145, "minAmount": 100}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "bandsofnotes", "stepName": "cash tray 1", "rewardItem": true, "rewardItemMin": "75", "pedCoords": "-351.77, -53.82, 48.01", "heading": "170.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Grab Money", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-351.77, -53.82, 48.01", "sendAlert": false, "createExplosion": false, "animation": "ch_prop_cash_low_trolly_01a", "propHeading": "170.00", "propName": "ch_prop_cash_low_trolly_01a", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [{"itemName": "drill", "loseChance": 100, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 4, "penaltyForFailing": false, "rewardItemMax": "150", "requiresItem": true, "requireWeaponInHand": false, "rewardItems": [{"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 150, "minAmount": 50}, {"itemName": "stacksofcash", "rewardChance": 25, "maxAmount": 125, "minAmount": 50}, {"itemName": "purpleusb", "rewardChance": 25, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "bandsofnotes", "stepName": "drilling", "rewardItem": true, "rewardItemMin": "50", "pedCoords": "-350.33, -54.76, 48.01", "heading": "343.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Drill", "itemName": "drill", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "-349.92, -54.13, 48.91", "sendAlert": false, "createExplosion": false, "animation": "12", "propHeading": "0.00", "propName": "", "hackType": "drilllaser"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": true, "stepNumber": 5, "penaltyForFailing": false, "rewardItemMax": "150", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 150, "minAmount": 75}, {"itemName": "stacksofcash", "rewardChance": 50, "maxAmount": 145, "minAmount": 100}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "bandsofnotes", "stepName": "cash tray 2", "rewardItem": true, "rewardItemMin": "75", "pedCoords": "-349.99, -56.05, 48.01", "heading": "66.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Grab Money", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-349.99, -56.05, 48.01", "sendAlert": false, "createExplosion": false, "animation": "ch_prop_cash_low_trolly_01a", "propHeading": "66.00", "propName": "ch_prop_cash_low_trolly_01a", "hackType": "none"}, {"lasers": [], "triggersBlackout": false, "unlockTimeCheckbox": true, "isOptional": false, "items": [{"itemName": "thermite", "loseChance": 25, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "penaltyType": "fire", "minigameCheckbox": false, "rewardItemQuantity": "", "unlockTime": "10", "itemName": "thermite", "selectedWeapon": "", "xpRemoveAmount": "0", "requiresItem": true, "stepNumber": 6, "penaltyForFailing": true, "xpOptions": false, "doorPosition": "-350.41, -56.80, 49.33", "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItem": false, "guards": [], "rewardMoneyType": "", "rewardItemName": "", "minigameType": "", "rewardItemMin": "", "pedCoords": "-351.35, -56.05, 48.01", "stepName": "thermite door", "heading": "150.00", "xpNeededAmount": "0", "penaltyChance": "50", "rewardMoneyMin": "", "isRequiredNextStage": true, "type": "hackable_door", "selectedDoorName": "bank fleeca gate ls customs", "requiredWeapons": [], "delayRewardCheckbox": false, "targetText": "Thermite Door", "rewardItemMax": "", "doorId": "1007", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "-351.83, -56.29, 49.28", "sendAlert": true, "createExplosion": false, "animation": "10", "propHeading": "0.00", "propName": "", "hackType": "inspired-thermite"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "", "removeXpOnHit": false, "lootableItems": [{"name": "armor", "chance": 25, "maxAmount": 3, "minAmount": 1}, {"name": "group6card", "chance": 15, "maxAmount": 2, "minAmount": 1}], "penaltyType": "", "rewardItemQuantity": "", "unlockTime": "", "sendAlert": false, "selectedWeapon": "", "xpRemoveAmount": "0", "rewardItemMax": "", "stepNumber": 7, "penaltyForFailing": false, "requiresItem": false, "showEnemyBlip": false, "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItemMin": "", "guards": [{"guardPosition": [-349.9, -57.66, 48.01], "heading": 76, "weapon": "weapon_appistol", "guardModel": "s_m_m_chemsec_01"}], "itemName": "", "rewardItemName": "", "minigameType": "s_m_m_chemsec_01", "minigameCheckbox": false, "triggersBlackout": false, "delayRewardCheckbox": false, "heading": "", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "", "isRequiredNextStage": false, "type": "guards", "selectedDoorName": "", "requiredWeapons": [], "rewardItem": false, "targetText": "", "rewardMoneyType": "", "stepName": "guards", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "pedCoords": "", "targetVector": "", "quantity": "", "createExplosion": false, "animation": "", "propHeading": "", "propName": "", "hackType": "s_m_m_chemsec_01"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 8, "penaltyForFailing": false, "rewardItemMax": "7", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "goldbar", "rewardChance": 100, "maxAmount": 7, "minAmount": 2}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "goldbar", "stepName": "gold bars", "rewardItem": true, "rewardItemMin": "2", "pedCoords": "-352.20, -58.30, 48.86", "heading": "165.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Grab Gold Bars", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-352.20, -58.30, 48.86", "sendAlert": true, "createExplosion": false, "animation": "h4_prop_h4_gold_stack_01a", "propHeading": "165.00", "propName": "h4_prop_h4_gold_stack_01a", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [{"itemName": "drill", "loseChance": 10, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 9, "penaltyForFailing": false, "rewardItemMax": "100", "requiresItem": true, "requireWeaponInHand": false, "rewardItems": [{"itemName": "stacksofcash", "rewardChance": 100, "maxAmount": 100, "minAmount": 50}, {"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 75, "minAmount": 25}, {"itemName": "goldwatch", "rewardChance": 100, "maxAmount": 3, "minAmount": 1}, {"itemName": "goldbar", "rewardChance": 50, "maxAmount": 2, "minAmount": 1}, {"itemName": "group6card", "rewardChance": 10, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "stacksofcash", "stepName": "drill 1", "rewardItem": true, "rewardItemMin": "50", "pedCoords": "-350.28, -59.11, 48.01", "heading": "249.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Drill", "itemName": "drill", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "-349.92, -59.42, 49.04", "sendAlert": true, "createExplosion": false, "animation": "12", "propHeading": "0.00", "propName": "", "hackType": "drilllaser"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [{"itemName": "drill", "loseChance": 10, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 10, "penaltyForFailing": false, "rewardItemMax": "100", "requiresItem": true, "requireWeaponInHand": false, "rewardItems": [{"itemName": "stacksofcash", "rewardChance": 100, "maxAmount": 100, "minAmount": 50}, {"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 75, "minAmount": 25}, {"itemName": "goldwatch", "rewardChance": 100, "maxAmount": 3, "minAmount": 1}, {"itemName": "goldbar", "rewardChance": 50, "maxAmount": 2, "minAmount": 1}, {"itemName": "group6card", "rewardChance": 10, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "stacksofcash", "stepName": "drill 2", "rewardItem": true, "rewardItemMin": "50", "pedCoords": "-352.41, -59.68, 48.01", "heading": "146.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Drill", "itemName": "drill", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "-352.82, -60.15, 48.88", "sendAlert": true, "createExplosion": false, "animation": "12", "propHeading": "0.00", "propName": "", "hackType": "drilllaser"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [{"itemName": "drill", "loseChance": 10, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 11, "penaltyForFailing": false, "rewardItemMax": "100", "requiresItem": true, "requireWeaponInHand": false, "rewardItems": [{"itemName": "stacksofcash", "rewardChance": 100, "maxAmount": 100, "minAmount": 50}, {"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 75, "minAmount": 25}, {"itemName": "goldwatch", "rewardChance": 100, "maxAmount": 3, "minAmount": 1}, {"itemName": "goldbar", "rewardChance": 50, "maxAmount": 2, "minAmount": 1}, {"itemName": "group6card", "rewardChance": 10, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "stacksofcash", "stepName": "drill 3", "rewardItem": true, "rewardItemMin": "50", "pedCoords": "-353.48, -57.92, 48.01", "heading": "69.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Drill", "itemName": "drill", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "-354.14, -57.65, 48.86", "sendAlert": true, "createExplosion": false, "animation": "12", "propHeading": "0.00", "propName": "", "hackType": "drilllaser"}], "settings": {"blipScale": 1, "blipFlash": false, "alertSound1": "robberysound", "alertCode": "10-90", "alertPriority": 1, "alertIcon": "fas fa-bell", "blipColor": 2, "blipSprite": 498, "cooldownMinutes": 25, "policeRequired": "", "policeJobs": ["police", "leo", "lspd", "sasp", "ranger", "bcso", "trooper"], "robberyCooldown": "25", "policeAlert": "Armed Bank robbery", "policeAlertHeader": "Armed Bank robbery", "cooldownHeists": ["store south side", "store robbery vanila unicorn", "store above big bank", "store near casino", "store burgershot", "store mirror park", "bank fleeca legion", "bank fleeca above pillbox"], "minutesUntilRobbery": ""}}, {"heist_name": "bank fleeca life invader", "stages": [{"lasers": [{"intensity": 100, "startVector": [-1214.69, -335.79, 39.72], "movement": {"speed": 0.007, "amount": 2.5, "direction": "down"}, "endVector": [-1213.48, -338.31, 39.72]}, {"startVector": [-1210.24, -336.63, 36.78], "intensity": 100, "endVector": [-1214.76, -335.85, 39.77]}, {"startVector": [-1211.47, -334.17, 36.79], "intensity": 100, "endVector": [-1213.52, -338.31, 39.76]}], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "1", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 1, "penaltyForFailing": false, "rewardItemMax": "", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [], "triggersBlackout": false, "rewardDelayTime": "", "guards": [], "rewardMoneyType": "", "rewardItemName": "", "stepName": "lasers", "rewardItem": false, "rewardItemMin": "", "pedCoords": "-1201.23, -338.10, 37.09", "heading": "25.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "", "minigameCheckbox": false, "type": "lasers", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Shut Lasers Off", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-1201.69, -337.53, 38.48", "sendAlert": true, "createExplosion": false, "animation": "ch_prop_fingerprint_scanner_01e", "propHeading": "25.00", "propName": "ch_prop_fingerprint_scanner_01e", "hackType": "var"}, {"lasers": [], "triggersBlackout": false, "unlockTimeCheckbox": true, "isOptional": false, "items": [{"itemName": "bankcard", "loseChance": 50, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "penaltyType": "electrocute", "minigameCheckbox": false, "rewardItemQuantity": "", "unlockTime": "5", "itemName": "bankcard", "selectedWeapon": "", "xpRemoveAmount": "0", "requiresItem": true, "stepNumber": 2, "penaltyForFailing": true, "xpOptions": false, "doorPosition": "-1211.26, -334.56, 37.92", "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItem": false, "guards": [], "rewardMoneyType": "", "rewardItemName": "", "minigameType": "", "rewardItemMin": "", "pedCoords": "-1210.94, -336.42, 36.78", "stepName": "vault door", "heading": "267.00", "xpNeededAmount": "0", "penaltyChance": "50", "rewardMoneyMin": "", "isRequiredNextStage": false, "type": "hackable_door", "selectedDoorName": "bank fleeca vault life invader", "requiredWeapons": [], "delayRewardCheckbox": false, "targetText": "Hack <PERSON>", "rewardItemMax": "", "doorId": "1008", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "-1210.54, -336.44, 37.98", "sendAlert": true, "createExplosion": false, "animation": "hei_prop_hei_securitypanel", "propHeading": "293.00", "propName": "hei_prop_hei_securitypanel", "hackType": "maze"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": true, "stepNumber": 3, "penaltyForFailing": false, "rewardItemMax": "150", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 150, "minAmount": 75}, {"itemName": "stacksofcash", "rewardChance": 50, "maxAmount": 145, "minAmount": 100}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "bandsofnotes", "stepName": "cash tray 1", "rewardItem": true, "rewardItemMin": "75", "pedCoords": "-1210.42, -334.03, 36.76", "heading": "208.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Grab Money", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-1210.42, -334.03, 36.76", "sendAlert": false, "createExplosion": false, "animation": "ch_prop_cash_low_trolly_01a", "propHeading": "208.00", "propName": "ch_prop_cash_low_trolly_01a", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [{"itemName": "drill", "loseChance": 100, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 4, "penaltyForFailing": false, "rewardItemMax": "150", "requiresItem": true, "requireWeaponInHand": false, "rewardItems": [{"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 150, "minAmount": 50}, {"itemName": "stacksofcash", "rewardChance": 25, "maxAmount": 125, "minAmount": 50}, {"itemName": "purpleusb", "rewardChance": 25, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "bandsofnotes", "stepName": "drilling", "rewardItem": true, "rewardItemMin": "50", "pedCoords": "-1208.80, -333.45, 36.76", "heading": "22.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Drill", "itemName": "drill", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "-1209.01, -332.98, 37.85", "sendAlert": false, "createExplosion": false, "animation": "12", "propHeading": "0.00", "propName": "", "hackType": "drilllaser"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": true, "stepNumber": 5, "penaltyForFailing": false, "rewardItemMax": "150", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 150, "minAmount": 75}, {"itemName": "stacksofcash", "rewardChance": 50, "maxAmount": 145, "minAmount": 100}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "bandsofnotes", "stepName": "cash tray 2", "rewardItem": true, "rewardItemMin": "75", "pedCoords": "-1207.49, -334.15, 36.76", "heading": "115.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Grab Money", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-1207.49, -334.15, 36.76", "sendAlert": false, "createExplosion": false, "animation": "ch_prop_cash_low_trolly_01a", "propHeading": "115.00", "propName": "ch_prop_cash_low_trolly_01a", "hackType": "none"}, {"lasers": [], "triggersBlackout": false, "unlockTimeCheckbox": true, "isOptional": false, "items": [{"itemName": "thermite", "loseChance": 25, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "penaltyType": "fire", "minigameCheckbox": false, "rewardItemQuantity": "", "unlockTime": "10", "itemName": "thermite", "selectedWeapon": "", "xpRemoveAmount": "0", "requiresItem": true, "stepNumber": 6, "penaltyForFailing": true, "xpOptions": false, "doorPosition": "-1207.33, -335.13, 38.08", "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItem": false, "guards": [], "rewardMoneyType": "", "rewardItemName": "", "minigameType": "", "rewardItemMin": "", "pedCoords": "-1208.63, -335.32, 36.76", "stepName": "thermite door", "heading": "202.00", "xpNeededAmount": "0", "penaltyChance": "50", "rewardMoneyMin": "", "isRequiredNextStage": true, "type": "hackable_door", "selectedDoorName": "bank fleeca gate life invader", "requiredWeapons": [], "delayRewardCheckbox": false, "targetText": "Thermite Door", "rewardItemMax": "", "doorId": "1009", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "-1207.52, -339.66, 37.00", "sendAlert": true, "createExplosion": false, "animation": "10", "propHeading": "0.00", "propName": "", "hackType": "inspired-thermite"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "", "removeXpOnHit": false, "lootableItems": [{"name": "armor", "chance": 25, "maxAmount": 3, "minAmount": 1}, {"name": "group6card", "chance": 15, "maxAmount": 2, "minAmount": 1}], "penaltyType": "", "rewardItemQuantity": "", "unlockTime": "", "sendAlert": false, "selectedWeapon": "", "xpRemoveAmount": "0", "rewardItemMax": "", "stepNumber": 7, "penaltyForFailing": false, "requiresItem": false, "showEnemyBlip": false, "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItemMin": "", "guards": [{"guardPosition": [-1206.55, -335.29, 36.76], "heading": 144, "weapon": "weapon_heavypistol", "guardModel": "s_m_m_chemsec_01"}], "itemName": "", "rewardItemName": "", "minigameType": "s_m_m_chemsec_01", "minigameCheckbox": false, "triggersBlackout": false, "delayRewardCheckbox": false, "heading": "", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "", "isRequiredNextStage": false, "type": "guards", "selectedDoorName": "", "requiredWeapons": [], "rewardItem": false, "targetText": "", "rewardMoneyType": "", "stepName": "guards", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "pedCoords": "", "targetVector": "", "quantity": "", "createExplosion": false, "animation": "", "propHeading": "", "propName": "", "hackType": "s_m_m_chemsec_01"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 8, "penaltyForFailing": false, "rewardItemMax": "7", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "goldbar", "rewardChance": 100, "maxAmount": 7, "minAmount": 2}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "goldbar", "stepName": "gold bars", "rewardItem": true, "rewardItemMin": "2", "pedCoords": "-1207.55, -337.46, 37.61", "heading": "207.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Grab Gold Bars", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-1207.55, -337.46, 37.61", "sendAlert": true, "createExplosion": false, "animation": "h4_prop_h4_gold_stack_01a", "propHeading": "207.00", "propName": "h4_prop_h4_gold_stack_01a", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [{"itemName": "drill", "loseChance": 10, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 9, "penaltyForFailing": false, "rewardItemMax": "100", "requiresItem": true, "requireWeaponInHand": false, "rewardItems": [{"itemName": "stacksofcash", "rewardChance": 100, "maxAmount": 100, "minAmount": 50}, {"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 75, "minAmount": 25}, {"itemName": "goldwatch", "rewardChance": 100, "maxAmount": 3, "minAmount": 1}, {"itemName": "goldbar", "rewardChance": 50, "maxAmount": 2, "minAmount": 1}, {"itemName": "group6card", "rewardChance": 10, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "stacksofcash", "stepName": "drill 1", "rewardItem": true, "rewardItemMin": "50", "pedCoords": "-1205.69, -336.57, 36.76", "heading": "294.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Drill", "itemName": "drill", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "-1205.16, -336.47, 37.72", "sendAlert": true, "createExplosion": false, "animation": "12", "propHeading": "0.00", "propName": "", "hackType": "drilllaser"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [{"itemName": "drill", "loseChance": 10, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 10, "penaltyForFailing": false, "rewardItemMax": "100", "requiresItem": true, "requireWeaponInHand": false, "rewardItems": [{"itemName": "stacksofcash", "rewardChance": 100, "maxAmount": 100, "minAmount": 50}, {"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 75, "minAmount": 25}, {"itemName": "goldwatch", "rewardChance": 100, "maxAmount": 3, "minAmount": 1}, {"itemName": "goldbar", "rewardChance": 50, "maxAmount": 2, "minAmount": 1}, {"itemName": "group6card", "rewardChance": 10, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "stacksofcash", "stepName": "drill 2", "rewardItem": true, "rewardItemMin": "50", "pedCoords": "-1206.57, -338.63, 36.76", "heading": "208.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Drill", "itemName": "drill", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "-1206.54, -339.17, 37.83", "sendAlert": true, "createExplosion": false, "animation": "12", "propHeading": "0.00", "propName": "", "hackType": "drilllaser"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [{"itemName": "drill", "loseChance": 10, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 11, "penaltyForFailing": false, "rewardItemMax": "100", "requiresItem": true, "requireWeaponInHand": false, "rewardItems": [{"itemName": "stacksofcash", "rewardChance": 100, "maxAmount": 100, "minAmount": 50}, {"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 75, "minAmount": 25}, {"itemName": "goldwatch", "rewardChance": 100, "maxAmount": 3, "minAmount": 1}, {"itemName": "goldbar", "rewardChance": 50, "maxAmount": 2, "minAmount": 1}, {"itemName": "group6card", "rewardChance": 10, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "stacksofcash", "stepName": "drill 3", "rewardItem": true, "rewardItemMin": "50", "pedCoords": "-1208.63, -338.06, 36.76", "heading": "106.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Drill", "itemName": "drill", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "-1209.33, -338.34, 37.70", "sendAlert": true, "createExplosion": false, "animation": "12", "propHeading": "0.00", "propName": "", "hackType": "drilllaser"}], "settings": {"blipScale": 1, "blipFlash": false, "alertSound1": "robberysound", "alertCode": "10-90", "alertPriority": 1, "alertIcon": "fas fa-bell", "blipColor": 2, "blipSprite": 500, "cooldownMinutes": 25, "policeRequired": "", "policeJobs": ["police", "leo", "lspd", "sasp", "ranger", "bcso", "trooper"], "robberyCooldown": "25", "policeAlert": "Armed Bank robbery", "policeAlertHeader": "Armed Bank robbery", "cooldownHeists": ["store south side", "store robbery vanila unicorn", "store above big bank", "store near casino", "store burgershot", "store mirror park", "bank fleeca legion", "bank fleeca above pillbox", "bank fleeca ls customs"], "minutesUntilRobbery": ""}}, {"heist_name": "bank fleeca ocean", "stages": [{"lasers": [{"intensity": 100, "startVector": [-2956.35, 478.55, 17.6], "movement": {"speed": 0.007, "amount": 2.5, "direction": "down"}, "endVector": [-2959.14, 478.76, 17.58]}, {"startVector": [-2958.9, 482.28, 14.72], "intensity": 100, "endVector": [-2956.39, 478.46, 17.61]}, {"startVector": [-2956.22, 482.17, 14.72], "intensity": 100, "endVector": [-2959.15, 478.58, 17.64]}], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "1", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 1, "penaltyForFailing": false, "rewardItemMax": "", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [], "triggersBlackout": false, "rewardDelayTime": "", "guards": [], "rewardMoneyType": "", "rewardItemName": "", "stepName": "lasers", "rewardItem": false, "rewardItemMin": "", "pedCoords": "-2947.74, 482.44, 14.45", "heading": "88.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "", "minigameCheckbox": false, "type": "lasers", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Shut Lasers Off", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-2948.17, 482.36, 15.86", "sendAlert": true, "createExplosion": false, "animation": "ch_prop_fingerprint_scanner_01e", "propHeading": "88.00", "propName": "ch_prop_fingerprint_scanner_01e", "hackType": "inspired-thermite"}, {"lasers": [], "triggersBlackout": false, "unlockTimeCheckbox": true, "isOptional": false, "items": [{"itemName": "bankcard", "loseChance": 50, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "penaltyType": "electrocute", "minigameCheckbox": false, "rewardItemQuantity": "", "unlockTime": "5", "itemName": "bankcard", "selectedWeapon": "", "xpRemoveAmount": "0", "requiresItem": true, "stepNumber": 2, "penaltyForFailing": true, "xpOptions": false, "doorPosition": "-2958.54, 482.27, 15.84", "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItem": false, "guards": [], "rewardMoneyType": "", "rewardItemName": "", "minigameType": "", "rewardItemMin": "", "pedCoords": "-2956.50, 482.06, 15.90", "stepName": "vault door", "heading": "0.00", "xpNeededAmount": "0", "penaltyChance": "50", "rewardMoneyMin": "", "isRequiredNextStage": false, "type": "hackable_door", "selectedDoorName": "bank fleeca vault ocean", "requiredWeapons": [], "delayRewardCheckbox": false, "targetText": "Hack <PERSON>", "rewardItemMax": "", "doorId": "1010", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "-2956.50, 482.06, 15.90", "sendAlert": true, "createExplosion": false, "animation": "hei_prop_hei_securitypanel", "propHeading": "357.54", "propName": "hei_prop_hei_securitypanel", "hackType": "var"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": true, "stepNumber": 3, "penaltyForFailing": false, "rewardItemMax": "150", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 150, "minAmount": 75}, {"itemName": "stacksofcash", "rewardChance": 50, "maxAmount": 145, "minAmount": 100}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "bandsofnotes", "stepName": "cash tray 1", "rewardItem": true, "rewardItemMin": "75", "pedCoords": "-2958.50, 483.26, 14.68", "heading": "269.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Grab Money", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-2958.50, 483.26, 14.68", "sendAlert": false, "createExplosion": false, "animation": "ch_prop_cash_low_trolly_01a", "propHeading": "269.00", "propName": "ch_prop_cash_low_trolly_01a", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [{"itemName": "drill", "loseChance": 100, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 4, "penaltyForFailing": false, "rewardItemMax": "150", "requiresItem": true, "requireWeaponInHand": false, "rewardItems": [{"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 150, "minAmount": 50}, {"itemName": "stacksofcash", "rewardChance": 25, "maxAmount": 125, "minAmount": 50}, {"itemName": "purpleusb", "rewardChance": 25, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "bandsofnotes", "stepName": "drilling", "rewardItem": true, "rewardItemMin": "50", "pedCoords": "-2958.16, 484.72, 14.68", "heading": "70.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Drill", "itemName": "drill", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "-2958.78, 485.11, 15.83", "sendAlert": false, "createExplosion": false, "animation": "12", "propHeading": "0.00", "propName": "", "hackType": "drilllaser"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": true, "stepNumber": 5, "penaltyForFailing": false, "rewardItemMax": "150", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 150, "minAmount": 75}, {"itemName": "stacksofcash", "rewardChance": 50, "maxAmount": 145, "minAmount": 100}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "bandsofnotes", "stepName": "cash tray 2", "rewardItem": true, "rewardItemMin": "75", "pedCoords": "-2956.94, 485.71, 14.68", "heading": "174.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Grab Money", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-2956.94, 485.71, 14.68", "sendAlert": false, "createExplosion": false, "animation": "ch_prop_cash_low_trolly_01a", "propHeading": "174.00", "propName": "ch_prop_cash_low_trolly_01a", "hackType": "none"}, {"lasers": [], "triggersBlackout": false, "unlockTimeCheckbox": true, "isOptional": false, "items": [{"itemName": "thermite", "loseChance": 25, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "penaltyType": "fire", "minigameCheckbox": false, "rewardItemQuantity": "", "unlockTime": "10", "itemName": "thermite", "selectedWeapon": "", "xpRemoveAmount": "0", "requiresItem": true, "stepNumber": 6, "penaltyForFailing": true, "xpOptions": false, "doorPosition": "-2956.12, 485.42, 16.00", "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItem": false, "guards": [], "rewardMoneyType": "", "rewardItemName": "", "minigameType": "", "rewardItemMin": "", "pedCoords": "-2956.56, 484.44, 14.68", "stepName": "thermite door", "heading": "261.00", "xpNeededAmount": "0", "penaltyChance": "50", "rewardMoneyMin": "", "isRequiredNextStage": true, "type": "hackable_door", "selectedDoorName": "bank fleeca gate ocean", "requiredWeapons": [], "delayRewardCheckbox": false, "targetText": "Thermite Door", "rewardItemMax": "", "doorId": "1011", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "-2956.20, 483.90, 15.95", "sendAlert": true, "createExplosion": false, "animation": "10", "propHeading": "0.00", "propName": "", "hackType": "inspired-thermite"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "", "removeXpOnHit": false, "lootableItems": [{"name": "armor", "chance": 25, "maxAmount": 3, "minAmount": 1}, {"name": "group6card", "chance": 15, "maxAmount": 2, "minAmount": 1}], "penaltyType": "", "rewardItemQuantity": "", "unlockTime": "", "sendAlert": false, "selectedWeapon": "", "xpRemoveAmount": "0", "rewardItemMax": "", "stepNumber": 7, "penaltyForFailing": false, "requiresItem": false, "showEnemyBlip": false, "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItemMin": "", "guards": [{"guardPosition": [-2955.53, 486.07, 14.68], "heading": 183, "weapon": "weapon_smg_mk2", "guardModel": "s_m_m_chemsec_01"}], "itemName": "", "rewardItemName": "", "minigameType": "s_m_m_chemsec_01", "minigameCheckbox": false, "triggersBlackout": false, "delayRewardCheckbox": false, "heading": "", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "", "isRequiredNextStage": false, "type": "guards", "selectedDoorName": "", "requiredWeapons": [], "rewardItem": false, "targetText": "", "rewardMoneyType": "", "stepName": "guards", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "pedCoords": "", "targetVector": "", "quantity": "", "createExplosion": false, "animation": "", "propHeading": "", "propName": "", "hackType": "s_m_m_chemsec_01"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 8, "penaltyForFailing": false, "rewardItemMax": "7", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "goldbar", "rewardChance": 100, "maxAmount": 7, "minAmount": 2}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "goldbar", "stepName": "gold bars", "rewardItem": true, "rewardItemMin": "2", "pedCoords": "-2954.14, 484.22, 15.52", "heading": "265.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Grab Gold Bars", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-2954.14, 484.22, 15.52", "sendAlert": true, "createExplosion": false, "animation": "h4_prop_h4_gold_stack_01a", "propHeading": "265.00", "propName": "h4_prop_h4_gold_stack_01a", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [{"itemName": "drill", "loseChance": 10, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 9, "penaltyForFailing": false, "rewardItemMax": "100", "requiresItem": true, "requireWeaponInHand": false, "rewardItems": [{"itemName": "stacksofcash", "rewardChance": 100, "maxAmount": 100, "minAmount": 50}, {"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 75, "minAmount": 25}, {"itemName": "goldwatch", "rewardChance": 100, "maxAmount": 3, "minAmount": 1}, {"itemName": "goldbar", "rewardChance": 50, "maxAmount": 2, "minAmount": 1}, {"itemName": "group6card", "rewardChance": 10, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "stacksofcash", "stepName": "drill 1", "rewardItem": true, "rewardItemMin": "50", "pedCoords": "-2954.35, 486.10, 14.68", "heading": "0.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Drill", "itemName": "drill", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "-2954.24, 486.64, 15.76", "sendAlert": true, "createExplosion": false, "animation": "12", "propHeading": "0.00", "propName": "", "hackType": "drilllaser"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [{"itemName": "drill", "loseChance": 10, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 10, "penaltyForFailing": false, "rewardItemMax": "100", "requiresItem": true, "requireWeaponInHand": false, "rewardItems": [{"itemName": "stacksofcash", "rewardChance": 100, "maxAmount": 100, "minAmount": 50}, {"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 75, "minAmount": 25}, {"itemName": "goldwatch", "rewardChance": 100, "maxAmount": 3, "minAmount": 1}, {"itemName": "goldbar", "rewardChance": 50, "maxAmount": 2, "minAmount": 1}, {"itemName": "group6card", "rewardChance": 10, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "stacksofcash", "stepName": "drill 2", "rewardItem": true, "rewardItemMin": "50", "pedCoords": "-2952.90, 484.51, 14.68", "heading": "249.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Drill", "itemName": "drill", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "-2952.23, 484.16, 15.67", "sendAlert": true, "createExplosion": false, "animation": "12", "propHeading": "0.00", "propName": "", "hackType": "drilllaser"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [{"itemName": "drill", "loseChance": 10, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 11, "penaltyForFailing": false, "rewardItemMax": "100", "requiresItem": true, "requireWeaponInHand": false, "rewardItems": [{"itemName": "stacksofcash", "rewardChance": 100, "maxAmount": 100, "minAmount": 50}, {"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 75, "minAmount": 25}, {"itemName": "goldwatch", "rewardChance": 100, "maxAmount": 3, "minAmount": 1}, {"itemName": "goldbar", "rewardChance": 50, "maxAmount": 2, "minAmount": 1}, {"itemName": "group6card", "rewardChance": 10, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "stacksofcash", "stepName": "drill 3", "rewardItem": true, "rewardItemMin": "50", "pedCoords": "-2954.08, 482.88, 14.68", "heading": "153.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Drill", "itemName": "drill", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "-2954.20, 482.12, 15.73", "sendAlert": true, "createExplosion": false, "animation": "12", "propHeading": "0.00", "propName": "", "hackType": "drilllaser"}], "settings": {"blipScale": 1, "blipFlash": false, "alertSound1": "robberysound", "alertCode": "10-90", "alertPriority": 1, "alertIcon": "fas fa-bell", "blipColor": 2, "blipSprite": 500, "cooldownMinutes": 25, "policeRequired": "", "policeJobs": ["police", "leo", "lspd", "sasp", "ranger", "bcso", "trooper"], "robberyCooldown": "25", "policeAlert": "Armed Bank robbery", "policeAlertHeader": "Armed Bank robbery", "cooldownHeists": ["store south side", "store robbery vanila unicorn", "store above big bank", "store near casino", "store burgershot", "store mirror park", "bank fleeca legion", "bank fleeca above pillbox", "bank fleeca ls customs", "bank fleeca life invader"], "minutesUntilRobbery": ""}}, {"heist_name": "store south side", "stages": [{"requiredWeapons": [], "removeXpOnHit": false, "targetVector": "-47.76, -1759.89, 28.94", "guards": [], "requireSimultaneous": false, "rewardItemMax": "1", "requiresItem": false, "minigameCheckbox": false, "rewardMoney": false, "penaltyChance": "", "sendAlert": false, "rewardItemName": "salvagedlockpick", "difficulty": "", "rewardItem": true, "items": [], "isOptional": false, "rewardMoneyType": "cash", "blackoutDuration": "1", "delayRewardCheckbox": false, "unlockTime": "", "pedCoords": "-47.37, -1759.37, 28.42", "rewardItemMin": "1", "stepName": "Cabinet 1", "xpRewardAmount": "0", "lootableItems": [], "stepNumber": 1, "animation": "2", "selectedWeapon": "", "propName": "", "rewardItemQuantity": "", "isRequiredNextStage": false, "dropWalletWithPasscode": false, "minigameType": "", "penaltyType": "", "xpRemoveAmount": "0", "rewardItems": [{"maxAmount": 1, "minAmount": 1, "rewardChance": 45, "itemName": "salvagedlockpick"}], "createExplosion": false, "unlockTimeCheckbox": false, "targetText": "Search Cabinet", "requireWeaponInHand": false, "lasers": [], "quantity": "", "triggersBlackout": false, "heading": "140.00", "rewardMoneyMax": "1", "xpOptions": false, "xpNeededAmount": "0", "hackType": "none", "selectedDoorName": "", "type": "robabble_object", "propHeading": "0.00", "penaltyForFailing": false, "rewardDelayTime": "1", "rewardMoneyMin": "1", "itemName": ""}, {"requiredWeapons": [], "removeXpOnHit": false, "targetVector": "-46.25, -1759.25, 29.00", "guards": [], "requireSimultaneous": false, "rewardItemMax": "1", "requiresItem": false, "minigameCheckbox": false, "rewardMoney": false, "penaltyChance": "", "sendAlert": false, "rewardItemName": "salvagedlockpick", "difficulty": "", "rewardItem": true, "items": [], "isOptional": false, "rewardMoneyType": "cash", "blackoutDuration": "1", "delayRewardCheckbox": false, "unlockTime": "", "pedCoords": "-46.69, -1758.81, 28.42", "rewardItemMin": "1", "stepName": "Cabinet 2", "xpRewardAmount": "0", "lootableItems": [], "stepNumber": 2, "animation": "1", "selectedWeapon": "", "propName": "", "rewardItemQuantity": "", "isRequiredNextStage": false, "dropWalletWithPasscode": false, "minigameType": "", "penaltyType": "", "xpRemoveAmount": "0", "rewardItems": [{"maxAmount": 1, "minAmount": 1, "rewardChance": 45, "itemName": "salvagedlockpick"}], "createExplosion": false, "unlockTimeCheckbox": false, "targetText": "Search Cabinet", "requireWeaponInHand": false, "lasers": [], "quantity": "", "triggersBlackout": false, "heading": "223.00", "rewardMoneyMax": "1", "xpOptions": false, "xpNeededAmount": "0", "hackType": "none", "selectedDoorName": "", "type": "robabble_object", "propHeading": "0.00", "penaltyForFailing": false, "rewardDelayTime": "1", "rewardMoneyMin": "1", "itemName": ""}, {"requiredWeapons": [], "removeXpOnHit": false, "targetVector": "-47.43, -1758.25, 28.97", "guards": [], "requireSimultaneous": false, "rewardItemMax": "1", "requiresItem": false, "minigameCheckbox": false, "rewardMoney": false, "penaltyChance": "", "sendAlert": false, "rewardItemName": "salvagedlockpick", "difficulty": "", "rewardItem": true, "items": [], "isOptional": false, "rewardMoneyType": "cash", "blackoutDuration": "1", "delayRewardCheckbox": false, "unlockTime": "", "pedCoords": "-46.94, -1758.73, 28.42", "rewardItemMin": "1", "stepName": "Cabinet 3", "xpRewardAmount": "0", "lootableItems": [], "stepNumber": 3, "animation": "2", "selectedWeapon": "", "propName": "", "rewardItemQuantity": "", "isRequiredNextStage": false, "dropWalletWithPasscode": false, "minigameType": "", "penaltyType": "", "xpRemoveAmount": "0", "rewardItems": [{"maxAmount": 1, "minAmount": 1, "rewardChance": 45, "itemName": "salvagedlockpick"}], "createExplosion": false, "unlockTimeCheckbox": false, "targetText": "Search Cabinet", "requireWeaponInHand": false, "lasers": [], "quantity": "", "triggersBlackout": false, "heading": "38.00", "rewardMoneyMax": "1", "xpOptions": false, "xpNeededAmount": "0", "hackType": "none", "selectedDoorName": "", "type": "robabble_object", "propHeading": "0.00", "penaltyForFailing": false, "rewardDelayTime": "1", "rewardMoneyMin": "1", "itemName": ""}, {"requiredWeapons": [], "removeXpOnHit": false, "targetVector": "-48.52, -1759.17, 29.60", "guards": [], "requireSimultaneous": false, "rewardItemMax": "70", "requiresItem": true, "minigameCheckbox": false, "rewardMoney": false, "penaltyChance": "", "sendAlert": true, "rewardItemName": "bandsofnotes", "difficulty": "", "rewardItem": true, "items": [{"itemQuantity": 1, "loseChance": 30, "itemName": "advancedlockpick"}, {"itemQuantity": 1, "loseChance": 40, "itemName": "salvagedlockpick"}], "isOptional": false, "rewardMoneyType": "cash", "blackoutDuration": "1", "delayRewardCheckbox": false, "unlockTime": "", "pedCoords": "-47.82, -1759.26, 28.42", "rewardItemMin": "25", "stepName": "register 1", "xpRewardAmount": "0", "lootableItems": [], "stepNumber": 4, "animation": "19", "selectedWeapon": "", "propName": "", "rewardItemQuantity": "", "isRequiredNextStage": false, "dropWalletWithPasscode": false, "minigameType": "", "penaltyType": "", "xpRemoveAmount": "0", "rewardItems": [{"maxAmount": 70, "minAmount": 25, "rewardChance": 80, "itemName": "bandsofnotes"}, {"maxAmount": 70, "minAmount": 25, "rewardChance": 50, "itemName": "stacksofcash"}], "createExplosion": false, "unlockTimeCheckbox": false, "targetText": "Lockpick Register", "requireWeaponInHand": false, "lasers": [], "quantity": "1", "triggersBlackout": false, "heading": "88.00", "rewardMoneyMax": "1", "xpOptions": false, "xpNeededAmount": "0", "hackType": "inspired-lockpick", "selectedDoorName": "", "type": "robabble_object", "propHeading": "0.00", "penaltyForFailing": false, "rewardDelayTime": "1", "rewardMoneyMin": "1", "itemName": "advancedlockpick"}, {"requiredWeapons": [], "removeXpOnHit": false, "targetVector": "", "selectedDoorName": "", "requireSimultaneous": false, "minigameType": "s_m_m_chemsec_01", "penaltyChance": "", "rewardItemMax": "", "isOptional": false, "minigameCheckbox": false, "rewardMoney": false, "rewardMoneyType": "", "sendAlert": false, "rewardItemName": "", "difficulty": "", "rewardItem": false, "items": [], "xpRemoveAmount": "0", "pedCoords": "", "requiresItem": false, "delayRewardCheckbox": false, "lootableItems": [], "createExplosion": false, "rewardItemMin": "", "animation": "", "xpRewardAmount": "0", "blackoutDuration": "", "stepNumber": 5, "isRequiredNextStage": false, "selectedWeapon": "", "propName": "", "rewardItemQuantity": "", "dropWalletWithPasscode": true, "guards": [{"heading": 91, "guardPosition": [-47.33, -1762.62, 28.41], "weapon": "weapon_combatpistol", "guardModel": "s_m_m_chemsec_01"}], "penaltyType": "", "rewardItems": [], "penaltyForFailing": false, "targetText": "", "unlockTime": "", "unlockTimeCheckbox": false, "stepName": "guard 1", "requireWeaponInHand": false, "lasers": [], "quantity": "", "triggersBlackout": false, "heading": "", "rewardMoneyMax": "", "xpOptions": false, "walletDropChance": 70, "hackType": "s_m_m_chemsec_01", "xpNeededAmount": "0", "type": "guards", "propHeading": "", "showEnemyBlip": true, "rewardDelayTime": "", "rewardMoneyMin": "", "itemName": ""}, {"requiredWeapons": [], "removeXpOnHit": false, "targetVector": "-47.02, -1757.61, 29.60", "guards": [], "requireSimultaneous": false, "rewardItemMax": "70", "requiresItem": true, "minigameCheckbox": false, "rewardMoney": false, "penaltyChance": "", "sendAlert": true, "rewardItemName": "bandsofnotes", "difficulty": "", "rewardItem": true, "items": [{"itemQuantity": 1, "loseChance": 30, "itemName": "advancedlockpick"}, {"itemQuantity": 1, "loseChance": 40, "itemName": "salvagedlockpick"}], "isOptional": false, "rewardMoneyType": "cash", "blackoutDuration": "1", "delayRewardCheckbox": false, "unlockTime": "", "pedCoords": "-46.56, -1757.90, 28.42", "rewardItemMin": "25", "stepName": "register 2", "xpRewardAmount": "0", "lootableItems": [], "stepNumber": 6, "animation": "19", "selectedWeapon": "", "propName": "", "rewardItemQuantity": "", "isRequiredNextStage": false, "dropWalletWithPasscode": false, "minigameType": "", "penaltyType": "", "xpRemoveAmount": "0", "rewardItems": [{"maxAmount": 70, "minAmount": 25, "rewardChance": 80, "itemName": "bandsofnotes"}, {"maxAmount": 70, "minAmount": 25, "rewardChance": 50, "itemName": "stacksofcash"}], "createExplosion": false, "unlockTimeCheckbox": false, "targetText": "Lockpick Register", "requireWeaponInHand": false, "lasers": [], "quantity": "1", "triggersBlackout": false, "heading": "80.00", "rewardMoneyMax": "1", "xpOptions": false, "xpNeededAmount": "0", "hackType": "inspired-lockpick", "selectedDoorName": "", "type": "robabble_object", "propHeading": "0.00", "penaltyForFailing": false, "rewardDelayTime": "1", "rewardMoneyMin": "1", "itemName": "advancedlockpick"}, {"requiredWeapons": [], "removeXpOnHit": false, "targetVector": "", "selectedDoorName": "", "requireSimultaneous": false, "minigameType": "s_m_m_chemsec_01", "penaltyChance": "", "rewardItemMax": "", "isOptional": false, "minigameCheckbox": false, "rewardMoney": false, "rewardMoneyType": "", "sendAlert": false, "rewardItemName": "", "difficulty": "", "rewardItem": false, "items": [], "xpRemoveAmount": "0", "pedCoords": "", "requiresItem": false, "delayRewardCheckbox": false, "lootableItems": [], "createExplosion": false, "rewardItemMin": "", "animation": "", "xpRewardAmount": "0", "blackoutDuration": "", "stepNumber": 7, "isRequiredNextStage": false, "selectedWeapon": "", "propName": "", "rewardItemQuantity": "", "dropWalletWithPasscode": true, "guards": [{"heading": 226, "guardPosition": [-60.23, -1753.14, 28.22], "weapon": "weapon_combatpistol", "guardModel": "s_m_m_chemsec_01"}], "penaltyType": "", "rewardItems": [], "penaltyForFailing": false, "targetText": "", "unlockTime": "", "unlockTimeCheckbox": false, "stepName": "guard 2", "requireWeaponInHand": false, "lasers": [], "quantity": "", "triggersBlackout": false, "heading": "", "rewardMoneyMax": "", "xpOptions": false, "walletDropChance": 70, "hackType": "s_m_m_chemsec_01", "xpNeededAmount": "0", "type": "guards", "propHeading": "", "showEnemyBlip": true, "rewardDelayTime": "", "rewardMoneyMin": "", "itemName": ""}, {"requiredWeapons": [], "removeXpOnHit": false, "targetVector": "-43.43, -1752.71, 29.85", "createExplosion": false, "requireSimultaneous": false, "doorId": "south side door", "penaltyChance": "100", "rewardItemMax": "", "isOptional": false, "minigameCheckbox": false, "rewardMoney": false, "rewardMoneyType": "", "sendAlert": true, "rewardItemName": "", "difficulty": "", "rewardItem": false, "items": [], "penaltyType": "", "lootableItems": [], "blackoutDuration": "1", "delayRewardCheckbox": false, "guards": [], "animation": "8", "rewardItemMin": "", "selectedWeapon": "", "xpRewardAmount": "0", "isRequiredNextStage": false, "stepNumber": 8, "dropWalletWithPasscode": false, "rewardItems": [], "propName": "prop_ld_keypad_01b", "rewardItemQuantity": "", "targetText": "Enter Code", "requiresItem": false, "xpNeededAmount": "0", "pedCoords": "-43.17, -1753.35, 28.47", "penaltyForFailing": false, "unlockTime": "5", "selectedDoorName": "south side door", "unlockTimeCheckbox": true, "stepName": "hackable door", "requireWeaponInHand": false, "lasers": [], "quantity": "", "triggersBlackout": false, "heading": "54.00", "rewardMoneyMax": "", "xpOptions": false, "doorPosition": "-43.34, -1752.35, 29.57", "hackType": "none", "minigameType": "", "type": "hackable_door", "propHeading": "47.00", "xpRemoveAmount": "0", "rewardDelayTime": "", "rewardMoneyMin": "", "itemName": ""}, {"requiredWeapons": [], "removeXpOnHit": false, "targetVector": "-43.34, -1752.35, 29.57", "guards": [], "requireSimultaneous": false, "rewardItemMax": "", "requiresItem": false, "minigameCheckbox": false, "rewardMoney": false, "penaltyChance": "", "sendAlert": false, "rewardItemName": "", "difficulty": "", "rewardItem": false, "items": [], "isOptional": false, "rewardMoneyType": "", "blackoutDuration": "", "delayRewardCheckbox": false, "unlockTime": "", "pedCoords": "", "rewardItemMin": "", "stepName": "custom door", "xpRewardAmount": "0", "lootableItems": [], "stepNumber": 9, "animation": "", "selectedWeapon": "", "propName": "hei_v_ilev_bk_safegate_pris", "rewardItemQuantity": "", "isRequiredNextStage": false, "dropWalletWithPasscode": false, "minigameType": "", "penaltyType": "", "xpRemoveAmount": "0", "rewardItems": [], "createExplosion": false, "unlockTimeCheckbox": false, "targetText": "south side door", "requireWeaponInHand": false, "lasers": [], "quantity": "", "triggersBlackout": false, "heading": "", "rewardMoneyMax": "", "xpOptions": false, "xpNeededAmount": "0", "hackType": "", "selectedDoorName": "", "type": "custom_door", "propHeading": "229.00", "penaltyForFailing": false, "rewardDelayTime": "", "rewardMoneyMin": "", "itemName": ""}, {"requiredWeapons": [], "removeXpOnHit": false, "targetVector": "-41.95, -1749.13, 28.42", "guards": [], "requireSimultaneous": false, "rewardItemMax": "", "requiresItem": false, "minigameCheckbox": false, "rewardMoney": false, "penaltyChance": "", "sendAlert": true, "rewardItemName": "", "difficulty": "", "rewardItem": false, "items": [], "isOptional": false, "rewardMoneyType": "cash", "blackoutDuration": "1", "delayRewardCheckbox": false, "unlockTime": "", "pedCoords": "-41.95, -1749.13, 28.42", "rewardItemMin": "", "stepName": "Big safe", "xpRewardAmount": "0", "lootableItems": [], "stepNumber": 10, "animation": "h4_prop_h4_safe_01a", "selectedWeapon": "", "propName": "h4_prop_h4_safe_01a", "rewardItemQuantity": "", "isRequiredNextStage": true, "dropWalletWithPasscode": false, "minigameType": "", "penaltyType": "", "xpRemoveAmount": "0", "rewardItems": [], "createExplosion": false, "unlockTimeCheckbox": false, "targetText": "Open Safe", "requireWeaponInHand": false, "lasers": [], "quantity": "", "triggersBlackout": false, "heading": "322.00", "rewardMoneyMax": "1", "xpOptions": false, "xpNeededAmount": "0", "hackType": "safe-cracker", "selectedDoorName": "", "type": "robabble_object", "propHeading": "322.00", "penaltyForFailing": false, "rewardDelayTime": "1", "rewardMoneyMin": "1", "itemName": ""}, {"requiredWeapons": [], "removeXpOnHit": false, "targetVector": "-41.93, -1749.04, 28.70", "guards": [], "requireSimultaneous": false, "rewardItemMax": "1", "requiresItem": false, "minigameCheckbox": false, "rewardMoney": false, "penaltyChance": "", "sendAlert": false, "rewardItemName": "drill", "difficulty": "", "rewardItem": true, "items": [], "isOptional": false, "rewardMoneyType": "cash", "blackoutDuration": "1", "delayRewardCheckbox": false, "unlockTime": "", "pedCoords": "-42.57, -1749.82, 28.42", "rewardItemMin": "1", "stepName": "drill", "xpRewardAmount": "0", "lootableItems": [], "stepNumber": 11, "animation": "16", "selectedWeapon": "", "propName": "ch_prop_ch_heist_drill", "rewardItemQuantity": "", "isRequiredNextStage": true, "dropWalletWithPasscode": false, "minigameType": "", "penaltyType": "", "xpRemoveAmount": "0", "rewardItems": [{"maxAmount": 1, "minAmount": 1, "rewardChance": 40, "itemName": "drill"}], "createExplosion": false, "unlockTimeCheckbox": false, "targetText": "<PERSON><PERSON>", "requireWeaponInHand": false, "lasers": [], "quantity": "", "triggersBlackout": false, "heading": "316.00", "rewardMoneyMax": "1", "xpOptions": false, "xpNeededAmount": "0", "hackType": "none", "selectedDoorName": "", "type": "robabble_object", "propHeading": "0.00", "penaltyForFailing": false, "rewardDelayTime": "1", "rewardMoneyMin": "1", "itemName": ""}, {"requiredWeapons": [], "animation": "", "removeXpOnHit": false, "penaltyChance": "", "targetVector": "", "guards": [], "requireSimultaneous": false, "isOptional": false, "rewardMoneyType": "", "rewardItemMax": "", "itemName": "", "minigameCheckbox": false, "rewardMoney": false, "lootableItems": [], "sendAlert": false, "rewardItemName": "", "difficulty": "", "rewardItem": false, "items": [], "minValue": 50, "penaltyForFailing": false, "requiresItem": false, "delayRewardCheckbox": false, "targetText": "", "selectedWeapon": "", "rewardItemMin": "", "isRequiredNextStage": false, "xpRewardAmount": "0", "createExplosion": false, "stepNumber": 12, "selectedDoorName": "", "rewardItems": [], "propName": "", "rewardItemQuantity": "", "dropWalletWithPasscode": false, "pedCoords": "", "maxValue": 150, "blackoutDuration": "", "xpRemoveAmount": "0", "stepName": "ending robbery", "penaltyType": "", "unlockTimeCheckbox": false, "rewardDelayTime": "", "requireWeaponInHand": false, "lasers": [], "quantity": "", "triggersBlackout": false, "heading": "", "rewardMoneyMax": "", "xpOptions": false, "minigameType": "", "hackType": "", "xpNeededAmount": "0", "type": "ending_robbery", "propHeading": "", "unlockTime": "", "endingHeading": "54.00", "rewardMoneyMin": "", "endingTargetVector": "783.43, -248.85, 65.11"}, {"requiredWeapons": [], "removeXpOnHit": false, "targetVector": "-41.89, -1749.13, 29.45", "guards": [], "requireSimultaneous": false, "rewardItemMax": "", "requiresItem": false, "minigameCheckbox": false, "rewardMoney": true, "penaltyChance": "", "sendAlert": false, "rewardItemName": "", "difficulty": "", "rewardItem": false, "items": [], "isOptional": false, "rewardMoneyType": "cash", "blackoutDuration": "1", "delayRewardCheckbox": false, "unlockTime": "", "pedCoords": "-42.27, -1749.59, 28.42", "rewardItemMin": "", "stepName": "Safe Money", "xpRewardAmount": "0", "lootableItems": [], "stepNumber": 13, "animation": "15", "selectedWeapon": "", "propName": "bkr_prop_bkr_cashpile_04", "rewardItemQuantity": "", "isRequiredNextStage": true, "dropWalletWithPasscode": false, "minigameType": "", "penaltyType": "", "xpRemoveAmount": "0", "rewardItems": [], "createExplosion": false, "unlockTimeCheckbox": false, "targetText": "Grab Money", "requireWeaponInHand": false, "lasers": [], "quantity": "", "triggersBlackout": false, "heading": "308.00", "rewardMoneyMax": "700", "xpOptions": false, "xpNeededAmount": "0", "hackType": "none", "selectedDoorName": "", "type": "robabble_object", "propHeading": "320.00", "penaltyForFailing": false, "rewardDelayTime": "1", "rewardMoneyMin": "200", "itemName": ""}, {"requiredWeapons": [], "removeXpOnHit": false, "targetVector": "-43.70, -1748.23, 28.92", "guards": [], "requireSimultaneous": false, "rewardItemMax": "1", "requiresItem": false, "minigameCheckbox": false, "rewardMoney": false, "penaltyChance": "", "sendAlert": true, "rewardItemName": "unmarkedsimcard", "difficulty": "", "rewardItem": true, "items": [], "isOptional": false, "rewardMoneyType": "cash", "blackoutDuration": "1", "delayRewardCheckbox": true, "unlockTime": "", "pedCoords": "-43.27, -1748.87, 28.42", "rewardItemMin": "1", "stepName": "small safe", "xpRewardAmount": "0", "lootableItems": [], "stepNumber": 14, "animation": "1", "selectedWeapon": "", "propName": "", "rewardItemQuantity": "", "isRequiredNextStage": true, "dropWalletWithPasscode": false, "minigameType": "", "penaltyType": "", "xpRemoveAmount": "0", "rewardItems": [{"maxAmount": 1, "minAmount": 1, "rewardChance": 10, "itemName": "unmarkedsimcard"}, {"maxAmount": 5, "minAmount": 1, "rewardChance": 100, "itemName": "diamondring"}, {"maxAmount": 7, "minAmount": 1, "rewardChance": 100, "itemName": "goldbracelet"}, {"maxAmount": 3, "minAmount": 1, "rewardChance": 1, "itemName": "goldbar"}, {"maxAmount": 25, "minAmount": 5, "rewardChance": 100, "itemName": "bandsofnotes"}], "createExplosion": false, "unlockTimeCheckbox": false, "targetText": "Open Safe", "requireWeaponInHand": false, "lasers": [], "quantity": "", "triggersBlackout": false, "heading": "28.00", "rewardMoneyMax": "1", "xpOptions": false, "xpNeededAmount": "0", "hackType": "none", "selectedDoorName": "", "type": "robabble_object", "propHeading": "0.00", "penaltyForFailing": false, "rewardDelayTime": "300", "rewardMoneyMin": "1", "itemName": ""}], "settings": {"policeAlert": "Armed store robbery", "alertCode": "10-90", "blipScale": 1, "minutesUntilRobbery": "", "cooldownHeists": ["store robbery vanila unicorn"], "policeRequired": "", "robberyCooldown": "25", "policeJobs": ["police", "leo", "lspd", "sasp", "ranger", "bcso", "trooper"], "policeAlertHeader": "Armed store robbery", "cooldownMinutes": 25, "alertPriority": 1, "blipFlash": false, "alertIcon": "fas fa-bell", "blipSprite": 59, "alertSound1": "robberysound", "blipColor": 0}}, {"heist_name": "bank fleeca harmony", "stages": [{"lasers": [{"startVector": [1179.19, 2710.41, 40.03], "intensity": 100, "endVector": [1179.15, 2713.2, 39.99]}, {"startVector": [1179.24, 2713.2, 40.07], "intensity": 100, "endVector": [1175.55, 2710.43, 37.1]}, {"startVector": [1175.58, 2713.17, 37.09], "intensity": 100, "endVector": [1179.24, 2710.41, 40.06]}], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "1", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 1, "penaltyForFailing": false, "rewardItemMax": "", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [], "triggersBlackout": false, "rewardDelayTime": "", "guards": [], "rewardMoneyType": "", "rewardItemName": "", "stepName": "lasers", "rewardItem": false, "rewardItemMin": "", "pedCoords": "1177.38, 2722.07, 37.00", "heading": "177.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "", "minigameCheckbox": false, "type": "lasers", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Shut Lasers Off", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "1177.38, 2722.07, 37.00", "sendAlert": true, "createExplosion": false, "animation": "tr_prop_tr_elecbox_01a", "propHeading": "177.00", "propName": "tr_prop_tr_elecbox_01a", "hackType": "key_drop_game"}, {"lasers": [], "triggersBlackout": false, "unlockTimeCheckbox": true, "isOptional": false, "items": [{"itemName": "bankcard", "loseChance": 50, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "penaltyType": "electrocute", "minigameCheckbox": false, "rewardItemQuantity": "", "unlockTime": "5", "itemName": "bankcard", "selectedWeapon": "", "xpRemoveAmount": "0", "requiresItem": true, "stepNumber": 2, "penaltyForFailing": true, "xpOptions": false, "doorPosition": "1175.54, 2710.86, 38.23", "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItem": false, "guards": [], "rewardMoneyType": "", "rewardItemName": "", "minigameType": "", "rewardItemMin": "", "pedCoords": "1176.32, 2712.62, 37.09", "stepName": "vault door", "heading": "64.00", "xpNeededAmount": "0", "penaltyChance": "50", "rewardMoneyMin": "", "isRequiredNextStage": false, "type": "hackable_door", "selectedDoorName": "bank fleeca harmony vault", "requiredWeapons": [], "delayRewardCheckbox": false, "targetText": "Hack <PERSON>", "rewardItemMax": "", "doorId": "1015", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "1175.74, 2712.87, 38.30", "sendAlert": true, "createExplosion": false, "animation": "hei_prop_hei_securitypanel", "propHeading": "84.00", "propName": "hei_prop_hei_securitypanel", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": true, "stepNumber": 3, "penaltyForFailing": false, "rewardItemMax": "150", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 150, "minAmount": 75}, {"itemName": "stacksofcash", "rewardChance": 50, "maxAmount": 145, "minAmount": 100}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "bandsofnotes", "stepName": "cash tray 1", "rewardItem": true, "rewardItemMin": "75", "pedCoords": "1174.22, 2710.78, 37.07", "heading": "0.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Grab Money", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "1174.22, 2710.78, 37.07", "sendAlert": false, "createExplosion": false, "animation": "ch_prop_cash_low_trolly_01a", "propHeading": "0.00", "propName": "ch_prop_cash_low_trolly_01a", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [{"itemName": "drill", "loseChance": 100, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 4, "penaltyForFailing": false, "rewardItemMax": "150", "requiresItem": true, "requireWeaponInHand": false, "rewardItems": [{"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 150, "minAmount": 50}, {"itemName": "stacksofcash", "rewardChance": 25, "maxAmount": 125, "minAmount": 50}, {"itemName": "purpleusb", "rewardChance": 25, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "bandsofnotes", "stepName": "drilling", "rewardItem": true, "rewardItemMin": "50", "pedCoords": "1173.07, 2711.26, 37.07", "heading": "180.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Drill", "itemName": "drill", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "1172.73, 2710.47, 38.13", "sendAlert": false, "createExplosion": false, "animation": "12", "propHeading": "0.00", "propName": "", "hackType": "drilllaser"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": true, "stepNumber": 5, "penaltyForFailing": false, "rewardItemMax": "150", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 150, "minAmount": 75}, {"itemName": "stacksofcash", "rewardChance": 50, "maxAmount": 145, "minAmount": 100}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "bandsofnotes", "stepName": "cash tray 2", "rewardItem": true, "rewardItemMin": "75", "pedCoords": "1171.91, 2712.16, 37.07", "heading": "272.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Grab Money", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "1171.91, 2712.16, 37.07", "sendAlert": false, "createExplosion": false, "animation": "ch_prop_cash_low_trolly_01a", "propHeading": "272.00", "propName": "ch_prop_cash_low_trolly_01a", "hackType": "none"}, {"lasers": [], "triggersBlackout": false, "unlockTimeCheckbox": true, "isOptional": false, "items": [{"itemName": "thermite", "loseChance": 25, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "penaltyType": "fire", "minigameCheckbox": false, "rewardItemQuantity": "", "unlockTime": "10", "itemName": "thermite", "selectedWeapon": "", "xpRemoveAmount": "0", "requiresItem": true, "stepNumber": 6, "penaltyForFailing": true, "xpOptions": false, "doorPosition": "1172.29, 2713.15, 38.39", "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItem": false, "guards": [], "rewardMoneyType": "", "rewardItemName": "", "minigameType": "", "rewardItemMin": "", "pedCoords": "1173.33, 2712.61, 37.07", "stepName": "thermite door", "heading": "354.00", "xpNeededAmount": "0", "penaltyChance": "50", "rewardMoneyMin": "", "isRequiredNextStage": false, "type": "hackable_door", "selectedDoorName": "bank fleeca harmony gate", "requiredWeapons": [], "delayRewardCheckbox": false, "targetText": "Thermite Door", "rewardItemMax": "", "doorId": "1016", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "1173.16, 2713.15, 38.37", "sendAlert": true, "createExplosion": false, "animation": "10", "propHeading": "0.00", "propName": "", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "", "removeXpOnHit": false, "lootableItems": [{"name": "purpleusb", "chance": 25, "maxAmount": 3, "minAmount": 1}, {"name": "group6card", "chance": 15, "maxAmount": 2, "minAmount": 1}], "penaltyType": "", "rewardItemQuantity": "", "unlockTime": "", "sendAlert": false, "selectedWeapon": "", "xpRemoveAmount": "0", "rewardItemMax": "", "stepNumber": 7, "penaltyForFailing": false, "requiresItem": false, "showEnemyBlip": false, "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItemMin": "", "guards": [{"guardPosition": [1171.64, 2713.7, 37.07], "heading": 270, "weapon": "weapon_smg", "guardModel": "s_m_m_chemsec_01"}], "itemName": "", "rewardItemName": "", "minigameType": "s_m_m_chemsec_01", "minigameCheckbox": false, "triggersBlackout": false, "delayRewardCheckbox": false, "heading": "", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "", "isRequiredNextStage": false, "type": "guards", "selectedDoorName": "", "requiredWeapons": [], "rewardItem": false, "targetText": "", "rewardMoneyType": "", "stepName": "guards", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "pedCoords": "", "targetVector": "", "quantity": "", "createExplosion": false, "animation": "", "propHeading": "", "propName": "", "hackType": "s_m_m_chemsec_01"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 8, "penaltyForFailing": false, "rewardItemMax": "85", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "stacksofcash", "rewardChance": 100, "maxAmount": 85, "minAmount": 25}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "stacksofcash", "stepName": "gold bars", "rewardItem": true, "rewardItemMin": "25", "pedCoords": "1173.44, 2715.11, 37.92", "heading": "0.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Grab Gold Bars", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "1173.44, 2715.11, 37.92", "sendAlert": true, "createExplosion": false, "animation": "h4_prop_h4_cash_stack_01a", "propHeading": "0.00", "propName": "h4_prop_h4_cash_stack_01a", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [{"itemName": "drill", "loseChance": 10, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 9, "penaltyForFailing": false, "rewardItemMax": "100", "requiresItem": true, "requireWeaponInHand": false, "rewardItems": [{"itemName": "stacksofcash", "rewardChance": 100, "maxAmount": 100, "minAmount": 50}, {"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 75, "minAmount": 25}, {"itemName": "goldwatch", "rewardChance": 100, "maxAmount": 3, "minAmount": 1}, {"itemName": "goldbar", "rewardChance": 50, "maxAmount": 2, "minAmount": 1}, {"itemName": "group6card", "rewardChance": 10, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "stacksofcash", "stepName": "drill 1", "rewardItem": true, "rewardItemMin": "50", "pedCoords": "1171.66, 2715.30, 37.07", "heading": "75.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Drill", "itemName": "drill", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "1170.96, 2715.36, 38.11", "sendAlert": true, "createExplosion": false, "animation": "12", "propHeading": "0.00", "propName": "", "hackType": "drilllaser"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [{"itemName": "drill", "loseChance": 10, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 10, "penaltyForFailing": false, "rewardItemMax": "100", "requiresItem": true, "requireWeaponInHand": false, "rewardItems": [{"itemName": "stacksofcash", "rewardChance": 100, "maxAmount": 100, "minAmount": 50}, {"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 75, "minAmount": 25}, {"itemName": "goldwatch", "rewardChance": 100, "maxAmount": 3, "minAmount": 1}, {"itemName": "goldbar", "rewardChance": 50, "maxAmount": 2, "minAmount": 1}, {"itemName": "group6card", "rewardChance": 10, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "stacksofcash", "stepName": "drill 2", "rewardItem": true, "rewardItemMin": "50", "pedCoords": "1173.33, 2716.58, 37.07", "heading": "356.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Drill", "itemName": "drill", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "1173.62, 2717.10, 37.90", "sendAlert": true, "createExplosion": false, "animation": "12", "propHeading": "0.00", "propName": "", "hackType": "drilllaser"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [{"itemName": "drill", "loseChance": 10, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 11, "penaltyForFailing": false, "rewardItemMax": "100", "requiresItem": true, "requireWeaponInHand": false, "rewardItems": [{"itemName": "stacksofcash", "rewardChance": 100, "maxAmount": 100, "minAmount": 50}, {"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 75, "minAmount": 25}, {"itemName": "goldwatch", "rewardChance": 100, "maxAmount": 3, "minAmount": 1}, {"itemName": "goldbar", "rewardChance": 50, "maxAmount": 2, "minAmount": 1}, {"itemName": "group6card", "rewardChance": 10, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "stacksofcash", "stepName": "drill 3", "rewardItem": true, "rewardItemMin": "50", "pedCoords": "1174.90, 2715.07, 37.07", "heading": "271.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Drill", "itemName": "drill", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "1175.53, 2715.21, 38.02", "sendAlert": true, "createExplosion": false, "animation": "12", "propHeading": "0.00", "propName": "", "hackType": "drilllaser"}], "settings": {"blipScale": 1, "blipFlash": false, "alertSound1": "robberysound", "alertCode": "10-90", "alertPriority": 1, "alertIcon": "fas fa-bell", "blipColor": 2, "blipSprite": 500, "cooldownMinutes": 25, "policeRequired": "", "policeJobs": ["police", "leo", "lspd", "sasp", "ranger", "bcso", "trooper"], "robberyCooldown": "25", "policeAlert": "Armed Bank robbery", "policeAlertHeader": "Armed Bank robbery", "cooldownHeists": ["store south side", "store robbery vanila unicorn", "store above big bank", "store near casino", "store burgershot", "store mirror park", "bank fleeca legion", "bank fleeca above pillbox", "bank fleeca ls customs", "bank fleeca life invader", "bank fleeca ocean"], "minutesUntilRobbery": ""}}, {"heist_name": "vangelico jewelry store", "stages": [{"lasers": [{"intensity": 100, "startVector": [-632.22, -236.44, 39.89], "movement": {"speed": 0.007, "amount": 3, "direction": "down"}, "endVector": [-630.64, -238.68, 39.93]}, {"intensity": 100, "startVector": [-632.18, -236.41, 37.11], "movement": {"speed": 0.007, "amount": 3, "direction": "up"}, "endVector": [-630.51, -238.59, 37.12]}, {"startVector": [-630.64, -238.57, 37.11], "intensity": 100, "endVector": [-632.45, -236.98, 41.45]}, {"startVector": [-632.13, -236.41, 37.04], "intensity": 100, "endVector": [-630.98, -238.61, 41.65]}], "triggersBlackout": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [{"itemName": "wirecutters", "loseChance": 85, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "penaltyType": "electrocute", "minigameCheckbox": false, "rewardItemQuantity": "", "unlockTime": "1", "itemName": "wirecutters", "selectedWeapon": "", "xpRemoveAmount": "0", "requiresItem": true, "stepNumber": 1, "penaltyForFailing": true, "xpOptions": false, "doorPosition": "-631.19, -237.39, 38.21", "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItem": false, "guards": [], "rewardMoneyType": "", "rewardItemName": "", "minigameType": "", "rewardItemMin": "", "pedCoords": "-612.72, -239.60, 50.32", "stepName": "roof hack", "heading": "27.00", "xpNeededAmount": "0", "penaltyChance": "100", "rewardMoneyMin": "", "isRequiredNextStage": false, "type": "hackable_door", "selectedDoorName": "Vangelico Jewelry Store Double Front Doors", "requiredWeapons": [], "delayRewardCheckbox": false, "targetText": "Cut Wires", "rewardItemMax": "", "doorId": "1017", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "-612.72, -239.60, 50.32", "sendAlert": true, "createExplosion": false, "animation": "tr_prop_tr_elecbox_01a", "propHeading": "27.00", "propName": "tr_prop_tr_elecbox_01a", "hackType": "inspired-chopping"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "", "removeXpOnHit": false, "lootableItems": [{"name": "weapon_advancedrifle", "chance": 1, "maxAmount": 1, "minAmount": 1}, {"name": "weapon_pumpshotgun", "chance": 1, "maxAmount": 1, "minAmount": 1}, {"name": "armor", "chance": 5, "maxAmount": 1, "minAmount": 1}, {"name": "paletobankcard", "chance": 1, "maxAmount": 1, "minAmount": 1}], "penaltyType": "", "rewardItemQuantity": "", "unlockTime": "", "sendAlert": false, "selectedWeapon": "", "xpRemoveAmount": "0", "rewardItemMax": "", "stepNumber": 2, "penaltyForFailing": false, "requiresItem": false, "showEnemyBlip": false, "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItemMin": "", "guards": [{"guardPosition": [-623.18, -225.75, 37.06], "heading": 222, "weapon": "weapon_mg", "guardModel": "s_m_m_chemsec_01"}, {"guardPosition": [-620.49, -225.1, 37.06], "heading": 175, "weapon": "weapon_carbinerifle", "guardModel": "s_m_m_chemsec_01"}, {"guardPosition": [-618.74, -228.27, 37.06], "heading": 131, "weapon": "weapon_carbinerifle", "guardModel": "s_m_m_chemsec_01"}, {"guardPosition": [-617.15, -231.24, 37.06], "heading": 97, "weapon": "weapon_bullpuprifle_mk2", "guardModel": "s_m_m_chemsec_01"}, {"guardPosition": [-617.45, -234.11, 37.06], "heading": 43, "weapon": "weapon_pumpshotgun", "guardModel": "s_m_m_chemsec_01"}, {"guardPosition": [-622.08, -235.05, 37.06], "heading": 78, "weapon": "weapon_bullpuprifle", "guardModel": "s_m_m_chemsec_01"}], "itemName": "", "rewardItemName": "", "minigameType": "s_m_m_chemsec_01", "minigameCheckbox": false, "triggersBlackout": false, "delayRewardCheckbox": false, "heading": "", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "", "isRequiredNextStage": false, "type": "guards", "selectedDoorName": "", "requiredWeapons": [], "rewardItem": false, "targetText": "", "rewardMoneyType": "", "stepName": "guards", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "pedCoords": "", "targetVector": "", "quantity": "", "createExplosion": false, "animation": "", "propHeading": "", "propName": "", "hackType": "s_m_m_chemsec_01"}, {"lasers": [], "triggersBlackout": false, "unlockTimeCheckbox": true, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "penaltyType": "electrocute", "minigameCheckbox": false, "rewardItemQuantity": "", "unlockTime": "1", "itemName": "", "selectedWeapon": "", "xpRemoveAmount": "0", "requiresItem": false, "stepNumber": 3, "penaltyForFailing": true, "xpOptions": false, "doorPosition": "-629.13, -230.15, 38.21", "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItem": false, "guards": [], "rewardMoneyType": "", "rewardItemName": "", "minigameType": "", "rewardItemMin": "", "pedCoords": "-629.14, -231.06, 37.06", "stepName": "side door", "heading": "35.00", "xpNeededAmount": "0", "penaltyChance": "100", "rewardMoneyMin": "", "isRequiredNextStage": true, "type": "hackable_door", "selectedDoorName": "Vangelico Jewelry Store Inside Single Door", "requiredWeapons": [], "delayRewardCheckbox": false, "targetText": "Enter Code", "rewardItemMax": "", "doorId": "1018", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-629.54, -230.54, 38.52", "sendAlert": true, "createExplosion": false, "animation": "8", "propHeading": "35.00", "propName": "prop_ld_keypad_01b", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "", "removeXpOnHit": false, "lootableItems": [], "penaltyType": "", "rewardItemQuantity": "", "unlockTime": "", "type": "hidden_passcode", "selectedWeapon": "", "xpRemoveAmount": "0", "minigameCheckbox": false, "stepNumber": 4, "penaltyForFailing": false, "requiresItem": false, "triggersBlackout": false, "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItemMin": "", "guards": [], "itemName": "", "rewardItemName": "", "minigameType": "", "rewardItemMax": "", "pedCoords": "", "isRequiredNextStage": false, "heading": "", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "", "rewardItem": false, "passcodes": [{"position": [-617.8, -236.49, 37.93], "heading": 53}, {"position": [-617.71, -228.79, 37.88], "heading": 127}, {"position": [-619.95, -225.71, 37.97], "heading": 125}, {"position": [-626.79, -239.45, 37.56], "heading": 302}, {"position": [-627.57, -239.97, 40.39], "heading": 252}, {"position": [-621.32, -232.76, 37.21], "heading": 126}, {"position": [-623.05, -228.71, 37.17], "heading": 34}], "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "stepName": "hidden passcode", "targetText": "", "rewardMoneyType": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "sendAlert": false, "targetVector": "", "quantity": "", "createExplosion": false, "animation": "", "propHeading": "", "propName": "", "hackType": ""}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": true, "stepNumber": 5, "penaltyForFailing": false, "rewardItemMax": "25", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "stacksofcash", "rewardChance": 90, "maxAmount": 25, "minAmount": 5}, {"itemName": "bandsofnotes", "rewardChance": 100, "maxAmount": 74, "minAmount": 25}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "stacksofcash", "stepName": "money", "rewardItem": true, "rewardItemMin": "5", "pedCoords": "-629.15, -227.96, 37.06", "heading": "124.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Grab Money", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-629.15, -227.96, 37.06", "sendAlert": true, "createExplosion": false, "animation": "ch_prop_cash_low_trolly_01a", "propHeading": "124.00", "propName": "ch_prop_cash_low_trolly_01a", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 6, "penaltyForFailing": false, "rewardItemMax": "1", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "pincracker", "rewardChance": 100, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "pincracker", "stepName": "pc hack", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "-631.07, -230.08, 37.06", "heading": "189.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Hack Computer", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-631.13, -230.55, 38.00", "sendAlert": false, "createExplosion": false, "animation": "7", "propHeading": "0.00", "propName": "", "hackType": "inspired-pincracker"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [{"itemName": "pincracker", "loseChance": 100, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 7, "penaltyForFailing": false, "rewardItemMax": "3", "requiresItem": true, "requireWeaponInHand": false, "rewardItems": [{"itemName": "goldbar", "rewardChance": 50, "maxAmount": 3, "minAmount": 1}, {"itemName": "diamondring", "rewardChance": 45, "maxAmount": 5, "minAmount": 1}, {"itemName": "humaneusb", "rewardChance": 75, "maxAmount": 2, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "goldbar", "stepName": "pincrack drawer", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "-631.14, -229.43, 37.06", "heading": "52.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Crack Open Drawer", "itemName": "pincracker", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "-631.77, -229.00, 37.93", "sendAlert": false, "createExplosion": false, "animation": "3", "propHeading": "0.00", "propName": "", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 8, "penaltyForFailing": false, "rewardItemMax": "1", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "paintingart", "rewardChance": 100, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "paintingart", "stepName": "painting art", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "-622.68, -225.15, 38.18", "heading": "342.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Cut Painting", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-622.68, -225.15, 38.18", "sendAlert": false, "createExplosion": false, "animation": "ch_prop_vault_painting_01g", "propHeading": "342.00", "propName": "ch_prop_vault_painting_01g", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 9, "penaltyForFailing": false, "rewardItemMax": "1", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "paintingcity", "rewardChance": 100, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "paintingcity", "stepName": "painting city", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "-617.01, -233.07, 38.19", "heading": "268.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Cut Painting", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-617.01, -233.07, 38.19", "sendAlert": false, "createExplosion": false, "animation": "ch_prop_vault_painting_01f", "propHeading": "268.00", "propName": "ch_prop_vault_painting_01f", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 10, "penaltyForFailing": false, "rewardItemMax": "1", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "<PERSON><PERSON><PERSON><PERSON>", "rewardChance": 100, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "<PERSON><PERSON><PERSON><PERSON>", "stepName": "ruby necklase vase", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "-617.90, -227.71, 37.06", "heading": "306.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Drill Vase", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-617.90, -227.71, 37.06", "sendAlert": false, "createExplosion": false, "animation": "h4_prop_h4_glass_disp_01a", "propHeading": "306.00", "propName": "h4_prop_h4_glass_disp_01a", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "weapon_assaultshotgun", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 11, "penaltyForFailing": false, "rewardItemMax": "4", "requiresItem": false, "requireWeaponInHand": true, "rewardItems": [{"itemName": "goldbracelet", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldwatch", "rewardChance": 40, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldchain", "rewardChance": 10, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondring", "rewardChance": 30, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondnecklace", "rewardChance": 15, "maxAmount": 4, "minAmount": 1}, {"itemName": "silverring", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "goldbracelet", "stepName": "vase 1", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "-623.96, -230.73, 37.65", "heading": "0.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": ["weapon_assaultshotgun", "weapon_autoshotgun", "weapon_bullpupshotgun", "weapon_pumpshotgun", "weapon_assaultrifle", "weapon_carbinerifle", "weapon_advancedrifle", "weapon_combatmg", "weapon_assaultsmg", "weapon_microsmg", "weapon_smg", "weapon_gusenberg"], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Smash Vase", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-623.96, -230.73, 37.65", "sendAlert": false, "createExplosion": false, "animation": "des_jewel_cab4_start", "propHeading": "216.00", "propName": "des_jewel_cab4_start", "hackType": "none"}, {"lasers": [], "triggersBlackout": false, "unlockTimeCheckbox": false, "isOptional": false, "maxValue": 250, "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "", "removeXpOnHit": false, "penaltyType": "", "quantity": "", "lootableItems": [], "rewardItem": false, "rewardItemMin": "", "rewardItemQuantity": "", "unlockTime": "", "itemName": "", "selectedWeapon": "", "xpRemoveAmount": "0", "items": [], "stepNumber": 12, "penaltyForFailing": false, "minValue": 50, "xpOptions": false, "requireWeaponInHand": false, "rewardItems": [], "delayRewardCheckbox": false, "endingTargetVector": "284.56, -2207.47, 8.37", "guards": [], "rewardMoneyType": "", "rewardItemName": "", "minigameType": "", "isRequiredNextStage": false, "requiresItem": false, "stepName": "ending robbery", "heading": "", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "", "rewardItemMax": "", "type": "ending_robbery", "selectedDoorName": "", "requiredWeapons": [], "targetText": "", "endingHeading": "89.00", "xpRewardAmount": "0", "rewardDelayTime": "", "pedCoords": "", "rewardMoney": false, "difficulty": "", "minigameCheckbox": false, "targetVector": "", "sendAlert": false, "createExplosion": false, "animation": "", "propHeading": "", "propName": "", "hackType": ""}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "weapon_assaultshotgun", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 13, "penaltyForFailing": false, "rewardItemMax": "4", "requiresItem": false, "requireWeaponInHand": true, "rewardItems": [{"itemName": "goldbracelet", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldwatch", "rewardChance": 40, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldchain", "rewardChance": 10, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondring", "rewardChance": 30, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondnecklace", "rewardChance": 15, "maxAmount": 4, "minAmount": 1}, {"itemName": "silverring", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "goldbracelet", "stepName": "vase 2", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "-627.21, -234.89, 37.65", "heading": "0.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": ["weapon_assaultshotgun", "weapon_autoshotgun", "weapon_bullpupshotgun", "weapon_pumpshotgun", "weapon_assaultrifle", "weapon_carbinerifle", "weapon_advancedrifle", "weapon_combatmg", "weapon_assaultsmg", "weapon_microsmg", "weapon_smg", "weapon_gusenberg"], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Smash Vase", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-627.21, -234.89, 37.65", "sendAlert": false, "createExplosion": false, "animation": "des_jewel_cab3_start", "propHeading": "36.00", "propName": "des_jewel_cab3_start", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "weapon_assaultshotgun", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 14, "penaltyForFailing": false, "rewardItemMax": "4", "requiresItem": false, "requireWeaponInHand": true, "rewardItems": [{"itemName": "goldbracelet", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldwatch", "rewardChance": 40, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldchain", "rewardChance": 10, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondring", "rewardChance": 30, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondnecklace", "rewardChance": 15, "maxAmount": 4, "minAmount": 1}, {"itemName": "silverring", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "goldbracelet", "stepName": "vase 3", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "-627.59, -234.37, 37.65", "heading": "0.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": ["weapon_assaultshotgun", "weapon_autoshotgun", "weapon_bullpupshotgun", "weapon_pumpshotgun", "weapon_assaultrifle", "weapon_carbinerifle", "weapon_advancedrifle", "weapon_combatmg", "weapon_assaultsmg", "weapon_microsmg", "weapon_smg", "weapon_gusenberg"], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Smash Vase", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-627.59, -234.37, 37.65", "sendAlert": false, "createExplosion": false, "animation": "des_jewel_cab_start", "propHeading": "216.00", "propName": "des_jewel_cab_start", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "weapon_assaultshotgun", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 15, "penaltyForFailing": false, "rewardItemMax": "4", "requiresItem": false, "requireWeaponInHand": true, "rewardItems": [{"itemName": "goldbracelet", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldwatch", "rewardChance": 40, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldchain", "rewardChance": 10, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondring", "rewardChance": 30, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondnecklace", "rewardChance": 15, "maxAmount": 4, "minAmount": 1}, {"itemName": "silverring", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "goldbracelet", "stepName": "vase 4", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "-626.54, -233.60, 37.65", "heading": "0.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": ["weapon_assaultshotgun", "weapon_autoshotgun", "weapon_bullpupshotgun", "weapon_pumpshotgun", "weapon_assaultrifle", "weapon_carbinerifle", "weapon_advancedrifle", "weapon_combatmg", "weapon_assaultsmg", "weapon_microsmg", "weapon_smg", "weapon_gusenberg"], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Smash Vase", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-626.54, -233.60, 37.65", "sendAlert": false, "createExplosion": false, "animation": "des_jewel_cab_start", "propHeading": "216.00", "propName": "des_jewel_cab_start", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "weapon_assaultshotgun", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 16, "penaltyForFailing": false, "rewardItemMax": "4", "requiresItem": false, "requireWeaponInHand": true, "rewardItems": [{"itemName": "goldbracelet", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldwatch", "rewardChance": 40, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldchain", "rewardChance": 10, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondring", "rewardChance": 30, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondnecklace", "rewardChance": 15, "maxAmount": 4, "minAmount": 1}, {"itemName": "silverring", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "goldbracelet", "stepName": "vase 5", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "-626.32, -239.05, 37.65", "heading": "0.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": ["weapon_assaultshotgun", "weapon_autoshotgun", "weapon_bullpupshotgun", "weapon_pumpshotgun", "weapon_assaultrifle", "weapon_carbinerifle", "weapon_advancedrifle", "weapon_combatmg", "weapon_assaultsmg", "weapon_microsmg", "weapon_smg", "weapon_gusenberg"], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Smash Vase", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-626.32, -239.05, 37.65", "sendAlert": false, "createExplosion": false, "animation": "des_jewel_cab2_start", "propHeading": "216.00", "propName": "des_jewel_cab2_start", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "weapon_assaultshotgun", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 17, "penaltyForFailing": false, "rewardItemMax": "4", "requiresItem": false, "requireWeaponInHand": true, "rewardItems": [{"itemName": "goldbracelet", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldwatch", "rewardChance": 40, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldchain", "rewardChance": 10, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondring", "rewardChance": 30, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondnecklace", "rewardChance": 15, "maxAmount": 4, "minAmount": 1}, {"itemName": "silverring", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "goldbracelet", "stepName": "vase 6", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "-625.28, -238.29, 37.65", "heading": "0.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": ["weapon_assaultshotgun", "weapon_autoshotgun", "weapon_bullpupshotgun", "weapon_pumpshotgun", "weapon_assaultrifle", "weapon_carbinerifle", "weapon_advancedrifle", "weapon_combatmg", "weapon_assaultsmg", "weapon_microsmg", "weapon_smg", "weapon_gusenberg"], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Smash Vase", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-625.28, -238.29, 37.65", "sendAlert": false, "createExplosion": false, "animation": "des_jewel_cab3_start", "propHeading": "216.00", "propName": "des_jewel_cab3_start", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "weapon_assaultshotgun", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 18, "penaltyForFailing": false, "rewardItemMax": "4", "requiresItem": false, "requireWeaponInHand": true, "rewardItems": [{"itemName": "goldbracelet", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldwatch", "rewardChance": 40, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldchain", "rewardChance": 10, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondring", "rewardChance": 30, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondnecklace", "rewardChance": 15, "maxAmount": 4, "minAmount": 1}, {"itemName": "silverring", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "goldbracelet", "stepName": "vase 7", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "-622.62, -232.56, 37.65", "heading": "0.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": ["weapon_assaultshotgun", "weapon_autoshotgun", "weapon_bullpupshotgun", "weapon_pumpshotgun", "weapon_assaultrifle", "weapon_carbinerifle", "weapon_advancedrifle", "weapon_combatmg", "weapon_assaultsmg", "weapon_microsmg", "weapon_smg", "weapon_gusenberg"], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Smash Vase", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-622.62, -232.56, 37.65", "sendAlert": false, "createExplosion": false, "animation": "des_jewel_cab_start", "propHeading": "306.00", "propName": "des_jewel_cab_start", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "weapon_assaultshotgun", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 19, "penaltyForFailing": false, "rewardItemMax": "4", "requiresItem": false, "requireWeaponInHand": true, "rewardItems": [{"itemName": "goldbracelet", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldwatch", "rewardChance": 40, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldchain", "rewardChance": 10, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondring", "rewardChance": 30, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondnecklace", "rewardChance": 15, "maxAmount": 4, "minAmount": 1}, {"itemName": "silverring", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "goldbracelet", "stepName": "vase 8", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "-619.85, -234.91, 37.65", "heading": "0.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": ["weapon_assaultshotgun", "weapon_autoshotgun", "weapon_bullpupshotgun", "weapon_pumpshotgun", "weapon_assaultrifle", "weapon_carbinerifle", "weapon_advancedrifle", "weapon_combatmg", "weapon_assaultsmg", "weapon_microsmg", "weapon_smg", "weapon_gusenberg"], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Smash Vase", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-619.85, -234.91, 37.65", "sendAlert": false, "createExplosion": false, "animation": "des_jewel_cab_start", "propHeading": "216.00", "propName": "des_jewel_cab_start", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "weapon_assaultshotgun", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 20, "penaltyForFailing": false, "rewardItemMax": "4", "requiresItem": false, "requireWeaponInHand": true, "rewardItems": [{"itemName": "goldbracelet", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldwatch", "rewardChance": 40, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldchain", "rewardChance": 10, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondring", "rewardChance": 30, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondnecklace", "rewardChance": 15, "maxAmount": 4, "minAmount": 1}, {"itemName": "silverring", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "goldbracelet", "stepName": "vase 9", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "-626.16, -234.13, 37.67", "heading": "0.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": ["weapon_assaultshotgun", "weapon_autoshotgun", "weapon_bullpupshotgun", "weapon_pumpshotgun", "weapon_assaultrifle", "weapon_carbinerifle", "weapon_advancedrifle", "weapon_combatmg", "weapon_assaultsmg", "weapon_microsmg", "weapon_smg", "weapon_gusenberg"], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Smash Vase", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-626.16, -234.13, 37.67", "sendAlert": false, "createExplosion": false, "animation": "des_jewel_cab4_start", "propHeading": "306.00", "propName": "des_jewel_cab4_start", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "weapon_assaultshotgun", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 21, "penaltyForFailing": false, "rewardItemMax": "4", "requiresItem": false, "requireWeaponInHand": true, "rewardItems": [{"itemName": "goldbracelet", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldwatch", "rewardChance": 40, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldchain", "rewardChance": 10, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondring", "rewardChance": 30, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondnecklace", "rewardChance": 15, "maxAmount": 4, "minAmount": 1}, {"itemName": "silverring", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "goldbracelet", "stepName": "vase 10", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "-618.80, -234.15, 37.65", "heading": "0.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": ["weapon_assaultshotgun", "weapon_autoshotgun", "weapon_bullpupshotgun", "weapon_pumpshotgun", "weapon_assaultrifle", "weapon_carbinerifle", "weapon_advancedrifle", "weapon_combatmg", "weapon_assaultsmg", "weapon_microsmg", "weapon_smg", "weapon_gusenberg"], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Smash Vase", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-618.80, -234.15, 37.65", "sendAlert": false, "createExplosion": false, "animation": "des_jewel_cab3_start", "propHeading": "216.00", "propName": "des_jewel_cab3_start", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "weapon_assaultshotgun", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 22, "penaltyForFailing": false, "rewardItemMax": "4", "requiresItem": false, "requireWeaponInHand": true, "rewardItems": [{"itemName": "goldbracelet", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldwatch", "rewardChance": 40, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldchain", "rewardChance": 10, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondring", "rewardChance": 30, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondnecklace", "rewardChance": 15, "maxAmount": 4, "minAmount": 1}, {"itemName": "silverring", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "goldbracelet", "stepName": "vase 11", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "-620.52, -232.88, 37.65", "heading": "0.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": ["weapon_assaultshotgun", "weapon_autoshotgun", "weapon_bullpupshotgun", "weapon_pumpshotgun", "weapon_assaultrifle", "weapon_carbinerifle", "weapon_advancedrifle", "weapon_combatmg", "weapon_assaultsmg", "weapon_microsmg", "weapon_smg", "weapon_gusenberg"], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Smash Vase", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-620.52, -232.88, 37.65", "sendAlert": false, "createExplosion": false, "animation": "des_jewel_cab4_start", "propHeading": "306.00", "propName": "des_jewel_cab4_start", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "weapon_assaultshotgun", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 23, "penaltyForFailing": false, "rewardItemMax": "4", "requiresItem": false, "requireWeaponInHand": true, "rewardItems": [{"itemName": "goldbracelet", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldwatch", "rewardChance": 40, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldchain", "rewardChance": 10, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondring", "rewardChance": 30, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondnecklace", "rewardChance": 15, "maxAmount": 4, "minAmount": 1}, {"itemName": "silverring", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "goldbracelet", "stepName": "vase 12", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "-617.09, -230.16, 37.65", "heading": "0.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": ["weapon_assaultshotgun", "weapon_autoshotgun", "weapon_bullpupshotgun", "weapon_pumpshotgun", "weapon_assaultrifle", "weapon_carbinerifle", "weapon_advancedrifle", "weapon_combatmg", "weapon_assaultsmg", "weapon_microsmg", "weapon_smg", "weapon_gusenberg"], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Smash Vase", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-617.09, -230.16, 37.65", "sendAlert": false, "createExplosion": false, "animation": "des_jewel_cab2_start", "propHeading": "306.00", "propName": "des_jewel_cab2_start", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "weapon_assaultshotgun", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 24, "penaltyForFailing": false, "rewardItemMax": "4", "requiresItem": false, "requireWeaponInHand": true, "rewardItems": [{"itemName": "goldbracelet", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldwatch", "rewardChance": 40, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldchain", "rewardChance": 10, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondring", "rewardChance": 30, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondnecklace", "rewardChance": 15, "maxAmount": 4, "minAmount": 1}, {"itemName": "silverring", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "goldbracelet", "stepName": "vase 13", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "-617.85, -229.11, 37.65", "heading": "0.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": ["weapon_assaultshotgun", "weapon_autoshotgun", "weapon_bullpupshotgun", "weapon_pumpshotgun", "weapon_assaultrifle", "weapon_carbinerifle", "weapon_advancedrifle", "weapon_combatmg", "weapon_assaultsmg", "weapon_microsmg", "weapon_smg", "weapon_gusenberg"], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Smash Vase", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-617.85, -229.11, 37.65", "sendAlert": false, "createExplosion": false, "animation": "des_jewel_cab3_start", "propHeading": "306.00", "propName": "des_jewel_cab3_start", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "weapon_assaultshotgun", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 25, "penaltyForFailing": false, "rewardItemMax": "4", "requiresItem": false, "requireWeaponInHand": true, "rewardItems": [{"itemName": "goldbracelet", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldwatch", "rewardChance": 40, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldchain", "rewardChance": 10, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondring", "rewardChance": 30, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondnecklace", "rewardChance": 15, "maxAmount": 4, "minAmount": 1}, {"itemName": "silverring", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "goldbracelet", "stepName": "vase 14", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "-620.18, -230.79, 37.65", "heading": "0.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": ["weapon_assaultshotgun", "weapon_autoshotgun", "weapon_bullpupshotgun", "weapon_pumpshotgun", "weapon_assaultrifle", "weapon_carbinerifle", "weapon_advancedrifle", "weapon_combatmg", "weapon_assaultsmg", "weapon_microsmg", "weapon_smg", "weapon_gusenberg"], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Smash Vase", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-620.18, -230.79, 37.65", "sendAlert": false, "createExplosion": false, "animation": "des_jewel_cab_start", "propHeading": "126.00", "propName": "des_jewel_cab_start", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "weapon_assaultshotgun", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 26, "penaltyForFailing": false, "rewardItemMax": "4", "requiresItem": false, "requireWeaponInHand": true, "rewardItems": [{"itemName": "goldbracelet", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldwatch", "rewardChance": 40, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldchain", "rewardChance": 10, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondring", "rewardChance": 30, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondnecklace", "rewardChance": 15, "maxAmount": 4, "minAmount": 1}, {"itemName": "silverring", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "goldbracelet", "stepName": "vase 15", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "-619.20, -227.25, 37.65", "heading": "0.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": ["weapon_assaultshotgun", "weapon_autoshotgun", "weapon_bullpupshotgun", "weapon_pumpshotgun", "weapon_assaultrifle", "weapon_carbinerifle", "weapon_advancedrifle", "weapon_combatmg", "weapon_assaultsmg", "weapon_microsmg", "weapon_smg", "weapon_gusenberg"], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Smash Vase", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-619.20, -227.25, 37.65", "sendAlert": false, "createExplosion": false, "animation": "des_jewel_cab2_start", "propHeading": "306.00", "propName": "des_jewel_cab2_start", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "weapon_assaultshotgun", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 27, "penaltyForFailing": false, "rewardItemMax": "4", "requiresItem": false, "requireWeaponInHand": true, "rewardItems": [{"itemName": "goldbracelet", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldwatch", "rewardChance": 40, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldchain", "rewardChance": 10, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondring", "rewardChance": 30, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondnecklace", "rewardChance": 15, "maxAmount": 4, "minAmount": 1}, {"itemName": "silverring", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "goldbracelet", "stepName": "vase 16", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "-619.97, -226.20, 37.65", "heading": "0.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": ["weapon_assaultshotgun", "weapon_autoshotgun", "weapon_bullpupshotgun", "weapon_pumpshotgun", "weapon_assaultrifle", "weapon_carbinerifle", "weapon_advancedrifle", "weapon_combatmg", "weapon_assaultsmg", "weapon_microsmg", "weapon_smg", "weapon_gusenberg"], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Smash Vase", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-619.97, -226.20, 37.65", "sendAlert": false, "createExplosion": false, "animation": "des_jewel_cab_start", "propHeading": "306.00", "propName": "des_jewel_cab_start", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "weapon_assaultshotgun", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 28, "penaltyForFailing": false, "rewardItemMax": "4", "requiresItem": false, "requireWeaponInHand": true, "rewardItems": [{"itemName": "goldbracelet", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldwatch", "rewardChance": 40, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldchain", "rewardChance": 10, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondring", "rewardChance": 30, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondnecklace", "rewardChance": 15, "maxAmount": 4, "minAmount": 1}, {"itemName": "silverring", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "goldbracelet", "stepName": "vase 17", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "-621.52, -228.95, 37.65", "heading": "0.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": ["weapon_assaultshotgun", "weapon_autoshotgun", "weapon_bullpupshotgun", "weapon_pumpshotgun", "weapon_assaultrifle", "weapon_carbinerifle", "weapon_advancedrifle", "weapon_combatmg", "weapon_assaultsmg", "weapon_microsmg", "weapon_smg", "weapon_gusenberg"], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Smash Vase", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-621.52, -228.95, 37.65", "sendAlert": false, "createExplosion": false, "animation": "des_jewel_cab3_start", "propHeading": "126.00", "propName": "des_jewel_cab3_start", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "weapon_assaultshotgun", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 29, "penaltyForFailing": false, "rewardItemMax": "4", "requiresItem": false, "requireWeaponInHand": true, "rewardItems": [{"itemName": "goldbracelet", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldwatch", "rewardChance": 40, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldchain", "rewardChance": 10, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondring", "rewardChance": 30, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondnecklace", "rewardChance": 15, "maxAmount": 4, "minAmount": 1}, {"itemName": "silverring", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "goldbracelet", "stepName": "vase 18", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "-624.28, -226.61, 37.65", "heading": "0.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": ["weapon_assaultshotgun", "weapon_autoshotgun", "weapon_bullpupshotgun", "weapon_pumpshotgun", "weapon_assaultrifle", "weapon_carbinerifle", "weapon_advancedrifle", "weapon_combatmg", "weapon_assaultsmg", "weapon_microsmg", "weapon_smg", "weapon_gusenberg"], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Smash Vase", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-624.28, -226.61, 37.65", "sendAlert": false, "createExplosion": false, "animation": "des_jewel_cab4_start", "propHeading": "306.00", "propName": "des_jewel_cab4_start", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "weapon_assaultshotgun", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 30, "penaltyForFailing": false, "rewardItemMax": "4", "requiresItem": false, "requireWeaponInHand": true, "rewardItems": [{"itemName": "goldbracelet", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldwatch", "rewardChance": 40, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldchain", "rewardChance": 10, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondring", "rewardChance": 30, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondnecklace", "rewardChance": 15, "maxAmount": 4, "minAmount": 1}, {"itemName": "silverring", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "goldbracelet", "stepName": "vase 19", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "-625.33, -227.37, 37.65", "heading": "0.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": ["weapon_assaultshotgun", "weapon_autoshotgun", "weapon_bullpupshotgun", "weapon_pumpshotgun", "weapon_assaultrifle", "weapon_carbinerifle", "weapon_advancedrifle", "weapon_combatmg", "weapon_assaultsmg", "weapon_microsmg", "weapon_smg", "weapon_gusenberg"], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Smash Vase", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-625.33, -227.37, 37.65", "sendAlert": false, "createExplosion": false, "animation": "des_jewel_cab3_start", "propHeading": "36.00", "propName": "des_jewel_cab3_start", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "weapon_assaultshotgun", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 31, "penaltyForFailing": false, "rewardItemMax": "4", "requiresItem": false, "requireWeaponInHand": true, "rewardItems": [{"itemName": "goldbracelet", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldwatch", "rewardChance": 40, "maxAmount": 4, "minAmount": 1}, {"itemName": "goldchain", "rewardChance": 10, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondring", "rewardChance": 30, "maxAmount": 4, "minAmount": 1}, {"itemName": "diamondnecklace", "rewardChance": 15, "maxAmount": 4, "minAmount": 1}, {"itemName": "silverring", "rewardChance": 20, "maxAmount": 4, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "goldbracelet", "stepName": "vase 20", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "-623.61, -228.62, 37.65", "heading": "0.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": ["weapon_assaultshotgun", "weapon_autoshotgun", "weapon_bullpupshotgun", "weapon_pumpshotgun", "weapon_assaultrifle", "weapon_carbinerifle", "weapon_advancedrifle", "weapon_combatmg", "weapon_assaultsmg", "weapon_microsmg", "weapon_smg", "weapon_gusenberg"], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Smash Vase", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "-623.61, -228.62, 37.65", "sendAlert": false, "createExplosion": false, "animation": "des_jewel_cab2_start", "propHeading": "216.00", "propName": "des_jewel_cab2_start", "hackType": "none"}], "settings": {"blipScale": 1, "alertIcon": "fas fa-bell", "minutesUntilRobbery": "", "blipFlash": false, "alertPriority": 1, "alertSound1": "robberysound", "cooldownHeists": ["store south side", "store robbery vanila unicorn", "store above big bank", "store near casino", "store burgershot", "store mirror park", "bank fleeca legion", "bank fleeca above pillbox", "bank fleeca ls customs", "bank fleeca life invader", "bank fleeca ocean", "bank fleeca harmony"], "policeJobs": ["police", "leo", "lspd", "sasp", "ranger", "bcso", "trooper"], "policeAlert": "Arned Vangelico Jewelry Store Robbery", "blipSprite": 617, "policeRequired": "", "alertCode": "10-90", "cooldownMinutes": 25, "policeAlertHeader": "Arned Vangelico Jewelry Store Robbery", "robberyCooldown": "45", "blipColor": 5}}, {"heist_name": "bank paleto", "stages": [{"difficulty": "", "hackType": "none", "stepName": "lasers", "rewardItemMin": "", "stepNumber": 1, "items": [{"itemQuantity": 1, "itemName": "wirecutters", "loseChance": 100}], "itemName": "wirecutters", "minigameCheckbox": false, "rewardMoneyType": "", "requireSimultaneous": false, "triggersBlackout": false, "unlockTime": "1", "rewardItem": false, "lootableItems": [], "lasers": [{"startVector": [-106.01, 6471.5, 30.64], "endVector": [-102.39, 6467.86, 33.41], "intensity": 100}, {"startVector": [-106.02, 6471.48, 33.3], "endVector": [-101.61, 6467.11, 31.25], "intensity": 100}, {"startVector": [-102.38, 6467.87, 33.37], "movement": {"direction": "down", "amount": 2.5, "speed": 0.007}, "endVector": [-106.04, 6471.47, 33.29], "intensity": 100}, {"startVector": [-106.04, 6471.47, 30.66], "movement": {"direction": "up", "amount": 2.5, "speed": 0.007}, "endVector": [-101.81, 6467.33, 30.84], "intensity": 100}], "unlockTimeCheckbox": false, "rewardDelayTime": "", "xpOptions": false, "animation": "tr_prop_tr_elecbox_01a", "isRequiredNextStage": false, "delayRewardCheckbox": false, "rewardItems": [], "pedCoords": "-100.94, 6479.33, 30.43", "blackoutDuration": "", "removeXpOnHit": false, "xpRemoveAmount": "0", "sendAlert": true, "dropWalletWithPasscode": false, "requiredWeapons": [], "selectedDoorName": "", "rewardItemName": "", "penaltyType": "", "isOptional": false, "rewardMoneyMin": "", "minigameType": "", "penaltyChance": "", "rewardMoneyMax": "", "type": "lasers", "requiresItem": true, "targetVector": "-100.94, 6479.33, 30.43", "requireWeaponInHand": false, "rewardMoney": false, "rewardItemMax": "", "heading": "135.00", "targetText": "Cut Wires", "propName": "tr_prop_tr_elecbox_01a", "quantity": "1", "guards": [], "selectedWeapon": "", "createExplosion": false, "xpRewardAmount": "0", "propHeading": "135.00", "rewardItemQuantity": "", "penaltyForFailing": false, "xpNeededAmount": "0"}, {"difficulty": "", "hackType": "s_m_m_chemsec_01", "stepName": "guards", "rewardItemMin": "", "xpOptions": false, "items": [], "triggersBlackout": false, "itemName": "", "minigameCheckbox": false, "rewardMoneyType": "", "requireSimultaneous": false, "lasers": [], "unlockTime": "", "rewardItem": false, "lootableItems": [], "rewardDelayTime": "", "unlockTimeCheckbox": false, "showEnemyBlip": false, "requireWeaponInHand": false, "animation": "", "isRequiredNextStage": false, "delayRewardCheckbox": false, "xpRewardAmount": "0", "pedCoords": "", "rewardItemMax": "", "removeXpOnHit": false, "xpRemoveAmount": "0", "sendAlert": false, "dropWalletWithPasscode": false, "requiredWeapons": [], "selectedDoorName": "", "rewardItemName": "", "penaltyType": "", "isOptional": false, "rewardMoneyMin": "", "minigameType": "s_m_m_chemsec_01", "penaltyChance": "", "rewardMoneyMax": "", "type": "guards", "requiresItem": false, "targetVector": "", "rewardItems": [], "rewardMoney": false, "selectedWeapon": "", "targetText": "", "blackoutDuration": "", "propName": "", "quantity": "", "guards": [{"weapon": "weapon_carbinerifle", "guardPosition": [-115.99, 6472.04, 30.63], "heading": 227, "guardModel": "s_m_m_chemsec_01"}, {"weapon": "weapon_bullpupshotgun", "guardPosition": [-117.65, 6469.43, 30.63], "heading": 249, "guardModel": "s_m_m_chemsec_01"}, {"weapon": "weapon_advancedrifle", "guardPosition": [-113.85, 6473.17, 30.63], "heading": 229, "guardModel": "s_m_m_chemsec_01"}, {"weapon": "weapon_mg", "guardPosition": [-99.84, 6461.66, 30.63], "heading": 66, "guardModel": "s_m_m_chemsec_01"}], "heading": "", "createExplosion": false, "stepNumber": 2, "propHeading": "", "rewardItemQuantity": "", "penaltyForFailing": false, "xpNeededAmount": "0"}, {"rewardDelayTime": "", "hackType": "maze", "doorPosition": "-104.60, 6473.44, 31.80", "rewardItemMin": "", "xpOptions": false, "animation": "13", "triggersBlackout": false, "items": [{"itemQuantity": 1, "itemName": "paletobankcard", "loseChance": 100}], "itemName": "paletobankcard", "minigameCheckbox": false, "rewardMoneyType": "", "requireSimultaneous": false, "lasers": [{"startVector": [-105.97, 6471.95, 30.63], "endVector": [-104.23, 6473.66, 32.95], "intensity": 100}, {"startVector": [-105.96, 6471.92, 32.94], "endVector": [-104.27, 6473.67, 30.63], "intensity": 100}, {"startVector": [-105.89, 6471.85, 32.89], "movement": {"direction": "down", "amount": 2.3, "speed": 0.007}, "endVector": [-104.1, 6473.52, 32.85], "intensity": 100}, {"startVector": [-105.88, 6471.84, 30.76], "movement": {"direction": "up", "amount": 2.3, "speed": 0.007}, "endVector": [-104.22, 6473.64, 30.72], "intensity": 100}], "unlockTime": "5", "rewardItem": false, "lootableItems": [], "targetText": "Hack Keypad", "unlockTimeCheckbox": true, "rewardItemMax": "", "requireWeaponInHand": false, "isRequiredNextStage": false, "stepName": "vault door", "delayRewardCheckbox": false, "rewardItems": [], "pedCoords": "-103.03, 6472.24, 30.63", "difficulty": "", "removeXpOnHit": false, "xpRemoveAmount": "0", "sendAlert": true, "dropWalletWithPasscode": false, "requiredWeapons": [], "selectedDoorName": "bank paleto vault", "rewardItemName": "", "penaltyType": "electrocute", "isOptional": false, "xpRewardAmount": "0", "minigameType": "", "blackoutDuration": "1", "rewardMoneyMax": "", "type": "hackable_door", "requiresItem": true, "rewardMoneyMin": "", "targetVector": "-102.67, 6472.44, 32.04", "rewardMoney": false, "selectedWeapon": "", "penaltyChance": "50", "heading": "315.00", "propName": "hei_prop_hei_securitypanel", "quantity": "1", "guards": [], "doorId": "1013", "createExplosion": false, "stepNumber": 3, "propHeading": "315.00", "rewardItemQuantity": "", "penaltyForFailing": true, "xpNeededAmount": "0"}, {"difficulty": "", "hackType": "none", "stepName": "gold bar tray", "rewardItemMin": "25", "stepNumber": 4, "items": [], "itemName": "", "minigameCheckbox": false, "rewardMoneyType": "cash", "requireSimultaneous": false, "triggersBlackout": false, "unlockTime": "", "rewardItem": true, "lootableItems": [], "lasers": [], "unlockTimeCheckbox": false, "rewardDelayTime": "1", "xpOptions": false, "animation": "ch_prop_gold_trolly_01a", "isRequiredNextStage": true, "delayRewardCheckbox": false, "rewardItems": [{"minAmount": 25, "maxAmount": 50, "rewardChance": 100, "itemName": "goldbar"}], "pedCoords": "-106.91, 6473.34, 30.63", "blackoutDuration": "1", "removeXpOnHit": false, "xpRemoveAmount": "0", "sendAlert": false, "dropWalletWithPasscode": false, "requiredWeapons": [], "selectedDoorName": "", "rewardItemName": "goldbar", "penaltyType": "", "isOptional": false, "rewardMoneyMin": "1", "minigameType": "", "penaltyChance": "", "rewardMoneyMax": "1", "type": "robabble_object", "requiresItem": false, "targetVector": "-106.91, 6473.34, 30.63", "requireWeaponInHand": false, "rewardMoney": false, "rewardItemMax": "50", "heading": "316.00", "targetText": "Grab Gold Bars", "propName": "ch_prop_gold_trolly_01a", "quantity": "", "guards": [], "selectedWeapon": "", "createExplosion": false, "xpRewardAmount": "0", "propHeading": "316.00", "rewardItemQuantity": "", "penaltyForFailing": false, "xpNeededAmount": "0"}, {"difficulty": "", "hackType": "none", "stepName": "drilling", "rewardItemMin": "50", "stepNumber": 5, "items": [{"itemQuantity": 1, "itemName": "drill", "loseChance": 100}], "itemName": "drill", "minigameCheckbox": false, "rewardMoneyType": "cash", "requireSimultaneous": false, "triggersBlackout": false, "unlockTime": "", "rewardItem": true, "lootableItems": [], "lasers": [], "unlockTimeCheckbox": false, "rewardDelayTime": "1", "xpOptions": false, "animation": "12", "isRequiredNextStage": false, "delayRewardCheckbox": false, "rewardItems": [{"minAmount": 50, "maxAmount": 150, "rewardChance": 100, "itemName": "bandsofnotes"}, {"minAmount": 50, "maxAmount": 125, "rewardChance": 25, "itemName": "stacksofcash"}, {"minAmount": 1, "maxAmount": 1, "rewardChance": 25, "itemName": "purpleusb"}, {"minAmount": 1, "maxAmount": 1, "rewardChance": 15, "itemName": "bigbankcard"}], "pedCoords": "-107.51, 6474.23, 30.63", "blackoutDuration": "1", "removeXpOnHit": false, "xpRemoveAmount": "0", "sendAlert": false, "dropWalletWithPasscode": false, "requiredWeapons": [], "selectedDoorName": "", "rewardItemName": "bandsofnotes", "penaltyType": "", "isOptional": false, "rewardMoneyMin": "1", "minigameType": "", "penaltyChance": "", "rewardMoneyMax": "1", "type": "robabble_object", "requiresItem": true, "targetVector": "-107.99, 6473.96, 31.61", "requireWeaponInHand": false, "rewardMoney": false, "rewardItemMax": "150", "heading": "130.00", "targetText": "Drill", "propName": "", "quantity": "1", "guards": [], "selectedWeapon": "", "createExplosion": false, "xpRewardAmount": "0", "propHeading": "0.00", "rewardItemQuantity": "", "penaltyForFailing": false, "xpNeededAmount": "0"}, {"difficulty": "", "hackType": "none", "stepName": "silver bar tray", "rewardItemMin": "15", "stepNumber": 6, "items": [], "itemName": "", "minigameCheckbox": false, "rewardMoneyType": "cash", "requireSimultaneous": false, "triggersBlackout": false, "unlockTime": "", "rewardItem": true, "lootableItems": [], "lasers": [], "unlockTimeCheckbox": false, "rewardDelayTime": "1", "xpOptions": false, "animation": "ch_prop_diamond_trolly_01a", "isRequiredNextStage": true, "delayRewardCheckbox": false, "rewardItems": [{"minAmount": 15, "maxAmount": 30, "rewardChance": 100, "itemName": "silverbar"}], "pedCoords": "-107.34, 6475.77, 30.63", "blackoutDuration": "1", "removeXpOnHit": false, "xpRemoveAmount": "0", "sendAlert": false, "dropWalletWithPasscode": false, "requiredWeapons": [], "selectedDoorName": "", "rewardItemName": "silverbar", "penaltyType": "", "isOptional": false, "rewardMoneyMin": "1", "minigameType": "", "penaltyChance": "", "rewardMoneyMax": "1", "type": "robabble_object", "requiresItem": false, "targetVector": "-107.34, 6475.77, 30.63", "requireWeaponInHand": false, "rewardMoney": false, "rewardItemMax": "30", "heading": "223.00", "targetText": "Grab Silver Bars", "propName": "ch_prop_diamond_trolly_01a", "quantity": "", "guards": [], "selectedWeapon": "", "createExplosion": false, "xpRewardAmount": "0", "propHeading": "223.00", "rewardItemQuantity": "", "penaltyForFailing": false, "xpNeededAmount": "0"}, {"rewardDelayTime": "", "hackType": "inspired-terminal", "doorPosition": "-106.47, 6476.16, 31.95", "rewardItemMin": "", "xpOptions": false, "animation": "10", "triggersBlackout": false, "items": [{"itemQuantity": 1, "itemName": "thermite", "loseChance": 55}], "itemName": "thermite", "minigameCheckbox": false, "rewardMoneyType": "", "requireSimultaneous": false, "lasers": [], "unlockTime": "10", "rewardItem": false, "lootableItems": [], "targetText": "Thermite Door", "unlockTimeCheckbox": true, "rewardItemMax": "", "requireWeaponInHand": false, "isRequiredNextStage": true, "stepName": "thermite door", "delayRewardCheckbox": false, "rewardItems": [], "pedCoords": "-105.98, 6475.05, 30.63", "difficulty": "", "removeXpOnHit": false, "xpRemoveAmount": "0", "sendAlert": true, "dropWalletWithPasscode": false, "requiredWeapons": [], "selectedDoorName": "bank paleto gate", "rewardItemName": "", "penaltyType": "fire", "isOptional": false, "xpRewardAmount": "0", "minigameType": "", "blackoutDuration": "1", "rewardMoneyMax": "", "type": "hackable_door", "requiresItem": true, "rewardMoneyMin": "", "targetVector": "-105.52, 6475.13, 31.95", "rewardMoney": false, "selectedWeapon": "", "penaltyChance": "50", "heading": "312.00", "propName": "", "quantity": "1", "guards": [], "doorId": "1014", "createExplosion": false, "stepNumber": 7, "propHeading": "0.00", "rewardItemQuantity": "", "penaltyForFailing": true, "xpNeededAmount": "0"}, {"difficulty": "", "hackType": "s_m_m_chemsec_01", "stepName": "guards", "rewardItemMin": "", "xpOptions": false, "items": [], "triggersBlackout": false, "itemName": "", "minigameCheckbox": false, "rewardMoneyType": "", "requireSimultaneous": false, "lasers": [], "unlockTime": "", "rewardItem": false, "lootableItems": [{"minAmount": 1, "chance": 25, "name": "armor", "maxAmount": 3}, {"minAmount": 1, "chance": 15, "name": "group6card", "maxAmount": 2}], "rewardDelayTime": "", "unlockTimeCheckbox": false, "showEnemyBlip": false, "requireWeaponInHand": false, "animation": "", "isRequiredNextStage": false, "delayRewardCheckbox": false, "xpRewardAmount": "0", "pedCoords": "", "rewardItemMax": "", "removeXpOnHit": false, "xpRemoveAmount": "0", "sendAlert": false, "dropWalletWithPasscode": false, "requiredWeapons": [], "selectedDoorName": "", "rewardItemName": "", "penaltyType": "", "isOptional": false, "rewardMoneyMin": "", "minigameType": "s_m_m_chemsec_01", "penaltyChance": "", "rewardMoneyMax": "", "type": "guards", "requiresItem": false, "targetVector": "", "rewardItems": [], "rewardMoney": false, "selectedWeapon": "", "targetText": "", "blackoutDuration": "", "propName": "", "quantity": "", "guards": [{"weapon": "weapon_specialcarbine", "guardPosition": [-106.69, 6477.61, 30.63], "heading": 227, "guardModel": "s_m_m_chemsec_01"}, {"weapon": "weapon_advancedrifle", "guardPosition": [-103.44, 6475.08, 30.67], "heading": 61, "guardModel": "s_m_m_chemsec_01"}, {"weapon": "weapon_carbinerifle", "guardPosition": [-102.13, 6476.69, 30.63], "heading": 102, "guardModel": "s_m_m_chemsec_01"}], "heading": "", "createExplosion": false, "stepNumber": 8, "propHeading": "", "rewardItemQuantity": "", "penaltyForFailing": false, "xpNeededAmount": "0"}, {"difficulty": "", "hackType": "none", "stepName": "money stack", "rewardItemMin": "200", "stepNumber": 9, "items": [], "itemName": "", "minigameCheckbox": false, "rewardMoneyType": "cash", "requireSimultaneous": false, "triggersBlackout": false, "unlockTime": "", "rewardItem": true, "lootableItems": [], "lasers": [], "unlockTimeCheckbox": false, "rewardDelayTime": "1", "xpOptions": false, "animation": "h4_prop_h4_cash_stack_01a", "isRequiredNextStage": false, "delayRewardCheckbox": false, "rewardItems": [{"minAmount": 200, "maxAmount": 200, "rewardChance": 100, "itemName": "stacksofcash"}], "pedCoords": "-104.63, 6477.30, 31.48", "blackoutDuration": "1", "removeXpOnHit": false, "xpRemoveAmount": "0", "sendAlert": true, "dropWalletWithPasscode": false, "requiredWeapons": [], "selectedDoorName": "", "rewardItemName": "stacksofcash", "penaltyType": "", "isOptional": false, "rewardMoneyMin": "1", "minigameType": "", "penaltyChance": "", "rewardMoneyMax": "1", "type": "robabble_object", "requiresItem": false, "targetVector": "-104.63, 6477.30, 31.48", "requireWeaponInHand": false, "rewardMoney": false, "rewardItemMax": "200", "heading": "316.00", "targetText": "Grab Money", "propName": "h4_prop_h4_cash_stack_01a", "quantity": "", "guards": [], "selectedWeapon": "", "createExplosion": false, "xpRewardAmount": "0", "propHeading": "316.00", "rewardItemQuantity": "", "penaltyForFailing": false, "xpNeededAmount": "0"}, {"difficulty": "", "hackType": "drilllaser", "stepName": "drill 1", "rewardItemMin": "50", "stepNumber": 10, "items": [{"itemQuantity": 1, "itemName": "drill", "loseChance": 10}], "itemName": "drill", "minigameCheckbox": false, "rewardMoneyType": "cash", "requireSimultaneous": false, "triggersBlackout": false, "unlockTime": "", "rewardItem": true, "lootableItems": [], "lasers": [], "unlockTimeCheckbox": false, "rewardDelayTime": "1", "xpOptions": false, "animation": "12", "isRequiredNextStage": false, "delayRewardCheckbox": false, "rewardItems": [{"minAmount": 50, "maxAmount": 100, "rewardChance": 100, "itemName": "stacksofcash"}, {"minAmount": 25, "maxAmount": 75, "rewardChance": 100, "itemName": "bandsofnotes"}, {"minAmount": 1, "maxAmount": 3, "rewardChance": 100, "itemName": "goldwatch"}, {"minAmount": 1, "maxAmount": 2, "rewardChance": 50, "itemName": "goldbar"}, {"minAmount": 1, "maxAmount": 1, "rewardChance": 10, "itemName": "group6card"}, {"minAmount": 1, "maxAmount": 1, "rewardChance": 1, "itemName": "bigbankcard"}], "pedCoords": "-105.75, 6478.42, 30.63", "blackoutDuration": "1", "removeXpOnHit": false, "xpRemoveAmount": "0", "sendAlert": true, "dropWalletWithPasscode": false, "requiredWeapons": [], "selectedDoorName": "", "rewardItemName": "stacksofcash", "penaltyType": "", "isOptional": false, "rewardMoneyMin": "1", "minigameType": "", "penaltyChance": "", "rewardMoneyMax": "1", "type": "robabble_object", "requiresItem": true, "targetVector": "-106.10, 6478.77, 31.77", "requireWeaponInHand": false, "rewardMoney": false, "rewardItemMax": "100", "heading": "53.00", "targetText": "Drill", "propName": "", "quantity": "1", "guards": [], "selectedWeapon": "", "createExplosion": false, "xpRewardAmount": "0", "propHeading": "0.00", "rewardItemQuantity": "", "penaltyForFailing": false, "xpNeededAmount": "0"}, {"difficulty": "", "hackType": "drilllaser", "stepName": "drill 2", "rewardItemMin": "50", "stepNumber": 11, "items": [{"itemQuantity": 1, "itemName": "drill", "loseChance": 10}], "itemName": "drill", "minigameCheckbox": false, "rewardMoneyType": "cash", "requireSimultaneous": false, "triggersBlackout": false, "unlockTime": "", "rewardItem": true, "lootableItems": [], "lasers": [], "unlockTimeCheckbox": false, "rewardDelayTime": "1", "xpOptions": false, "animation": "12", "isRequiredNextStage": false, "delayRewardCheckbox": false, "rewardItems": [{"minAmount": 50, "maxAmount": 100, "rewardChance": 100, "itemName": "stacksofcash"}, {"minAmount": 25, "maxAmount": 75, "rewardChance": 100, "itemName": "bandsofnotes"}, {"minAmount": 1, "maxAmount": 3, "rewardChance": 100, "itemName": "goldwatch"}, {"minAmount": 1, "maxAmount": 2, "rewardChance": 50, "itemName": "goldbar"}, {"minAmount": 1, "maxAmount": 1, "rewardChance": 10, "itemName": "group6card"}, {"minAmount": 1, "maxAmount": 1, "rewardChance": 1, "itemName": "bigbankcard"}], "pedCoords": "-103.39, 6477.83, 30.63", "blackoutDuration": "1", "removeXpOnHit": false, "xpRemoveAmount": "0", "sendAlert": true, "dropWalletWithPasscode": false, "requiredWeapons": [], "selectedDoorName": "", "rewardItemName": "stacksofcash", "penaltyType": "", "isOptional": false, "rewardMoneyMin": "1", "minigameType": "", "penaltyChance": "", "rewardMoneyMax": "1", "type": "robabble_object", "requiresItem": true, "targetVector": "-102.81, 6478.19, 31.66", "requireWeaponInHand": false, "rewardMoney": false, "rewardItemMax": "100", "heading": "302.00", "targetText": "Drill", "propName": "", "quantity": "1", "guards": [], "selectedWeapon": "", "createExplosion": false, "xpRewardAmount": "0", "propHeading": "0.00", "rewardItemQuantity": "", "penaltyForFailing": false, "xpNeededAmount": "0"}, {"difficulty": "", "hackType": "drilllaser", "stepName": "drill 3", "rewardItemMin": "50", "stepNumber": 12, "items": [{"itemQuantity": 1, "itemName": "drill", "loseChance": 10}], "itemName": "drill", "minigameCheckbox": false, "rewardMoneyType": "cash", "requireSimultaneous": false, "triggersBlackout": false, "unlockTime": "", "rewardItem": true, "lootableItems": [], "lasers": [], "unlockTimeCheckbox": false, "rewardDelayTime": "1", "xpOptions": false, "animation": "12", "isRequiredNextStage": false, "delayRewardCheckbox": false, "rewardItems": [{"minAmount": 50, "maxAmount": 100, "rewardChance": 100, "itemName": "stacksofcash"}, {"minAmount": 25, "maxAmount": 75, "rewardChance": 100, "itemName": "bandsofnotes"}, {"minAmount": 1, "maxAmount": 3, "rewardChance": 100, "itemName": "goldwatch"}, {"minAmount": 1, "maxAmount": 2, "rewardChance": 50, "itemName": "goldbar"}, {"minAmount": 1, "maxAmount": 1, "rewardChance": 10, "itemName": "group6card"}, {"minAmount": 1, "maxAmount": 1, "rewardChance": 1, "itemName": "bigbankcard"}], "pedCoords": "-103.21, 6476.01, 30.65", "blackoutDuration": "1", "removeXpOnHit": false, "xpRemoveAmount": "0", "sendAlert": true, "dropWalletWithPasscode": false, "requiredWeapons": [], "selectedDoorName": "", "rewardItemName": "stacksofcash", "penaltyType": "", "isOptional": false, "rewardMoneyMin": "1", "minigameType": "", "penaltyChance": "", "rewardMoneyMax": "1", "type": "robabble_object", "requiresItem": true, "targetVector": "-102.76, 6475.27, 31.60", "requireWeaponInHand": false, "rewardMoney": false, "rewardItemMax": "100", "heading": "225.00", "targetText": "Drill", "propName": "", "quantity": "1", "guards": [], "selectedWeapon": "", "createExplosion": false, "xpRewardAmount": "0", "propHeading": "0.00", "rewardItemQuantity": "", "penaltyForFailing": false, "xpNeededAmount": "0"}], "settings": {"blipFlash": false, "blipColor": 2, "blipSprite": 500, "policeJobs": ["police", "leo", "lspd", "sasp", "ranger", "bcso", "trooper"], "policeRequired": "", "minutesUntilRobbery": "", "blipScale": 1, "robberyCooldown": "45", "policeAlertHeader": "Armed Paleto Bank robbery", "cooldownHeists": ["store south side", "store robbery vanila unicorn", "store above big bank", "store near casino", "store burgershot", "store mirror park", "bank fleeca legion", "bank fleeca above pillbox", "bank fleeca ls customs", "bank fleeca life invader", "bank fleeca ocean", "bank fleeca harmony", "vangelico jewelry store"], "cooldownMinutes": 25, "alertPriority": 1, "alertCode": "10-90", "alertIcon": "fas fa-bell", "alertSound1": "robberysound", "policeAlert": "Armed Paleto Bank robbery"}}, {"heist_name": "humane labs", "stages": [{"lasers": [], "triggersBlackout": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [{"itemName": "humaneusb", "loseChance": 100, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "penaltyType": "electrocute", "minigameCheckbox": false, "rewardItemQuantity": "", "unlockTime": "1", "itemName": "humaneusb", "selectedWeapon": "", "xpRemoveAmount": "0", "requiresItem": true, "stepNumber": 1, "penaltyForFailing": true, "xpOptions": false, "doorPosition": "", "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItem": false, "guards": [], "rewardMoneyType": "", "rewardItemName": "", "minigameType": "", "rewardItemMin": "", "pedCoords": "3527.64, 3703.11, 19.99", "stepName": "usb door 1", "heading": "264.00", "xpNeededAmount": "0", "penaltyChance": "100", "rewardMoneyMin": "", "isRequiredNextStage": false, "type": "hackable_door", "selectedDoorName": "No door selected", "requiredWeapons": [], "delayRewardCheckbox": false, "targetText": "Inser USB", "rewardItemMax": "", "doorId": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "3528.30, 3702.99, 21.39", "sendAlert": false, "createExplosion": false, "animation": "ch_prop_fingerprint_scanner_01e", "propHeading": "264.00", "propName": "ch_prop_fingerprint_scanner_01e", "hackType": "openterminal"}, {"lasers": [], "triggersBlackout": false, "unlockTimeCheckbox": true, "isOptional": false, "items": [{"itemName": "humaneusb", "loseChance": 90, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": true, "rewardMoneyMax": "", "blackoutDuration": "1", "removeXpOnHit": false, "penaltyType": "electrocute", "lootableItems": [], "requiresItem": true, "rewardItemMin": "", "rewardItemQuantity": "", "unlockTime": "10", "delayRewardCheckbox": false, "selectedWeapon": "", "xpRemoveAmount": "0", "quantity": "1", "stepNumber": 2, "penaltyForFailing": true, "doorPosition": "3526.02, 3702.24, 21.34", "rewardDelayTime": "", "requireWeaponInHand": false, "rewardItems": [], "stepName": "usb door 2", "minigameCheckbox": false, "guards": [], "itemName": "humaneusb", "rewardItemName": "", "minigameType": "", "xpOptions": false, "pedCoords": "3524.33, 3703.18, 19.99", "isRequiredNextStage": false, "heading": "78.00", "xpNeededAmount": "0", "penaltyChance": "100", "rewardMoneyMin": "", "rewardMoneyType": "", "type": "hackable_door", "selectedDoorName": "humane labs door 1", "requiredWeapons": [], "simultaneousStepNumber": 1, "targetText": "Inser USB", "rewardItemMax": "", "doorId": "1019", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "rewardItem": false, "targetVector": "3523.83, 3703.27, 21.39", "sendAlert": true, "createExplosion": false, "animation": "ch_prop_fingerprint_scanner_01e", "propHeading": "78.00", "propName": "ch_prop_fingerprint_scanner_01e", "hackType": "openterminal"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "", "removeXpOnHit": false, "lootableItems": [{"name": "WEAPON_ASSAULTRIFLE", "chance": 2, "maxAmount": 1, "minAmount": 1}, {"name": "WEAPON_CARBINERIFLE", "chance": 2, "maxAmount": 1, "minAmount": 1}, {"name": "gasmask", "chance": 5, "maxAmount": 1, "minAmount": 1}], "penaltyType": "", "rewardItemQuantity": "", "unlockTime": "", "sendAlert": false, "selectedWeapon": "", "xpRemoveAmount": "0", "rewardItemMax": "", "stepNumber": 3, "penaltyForFailing": false, "requiresItem": false, "showEnemyBlip": false, "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItemMin": "", "guards": [{"guardPosition": [3529.29, 3698.45, 19.99], "heading": 90, "weapon": "weapon_assaultrifle_mk2", "guardModel": "s_m_m_chemsec_01"}, {"guardPosition": [3527.54, 3693.17, 19.99], "heading": 23, "weapon": "weapon_carbinerifle", "guardModel": "s_m_m_chemsec_01"}, {"guardPosition": [3523.31, 3689.09, 19.99], "heading": 0, "weapon": "weapon_carbinerifle", "guardModel": "s_m_m_chemsec_01"}, {"guardPosition": [3522.64, 3673.92, 19.99], "heading": 30, "weapon": "weapon_carbinerifle", "guardModel": "s_m_m_chemsec_01"}, {"guardPosition": [3539.91, 3671.31, 19.99], "heading": 95, "weapon": "weapon_carbinerifle", "guardModel": "s_m_m_chemsec_01"}], "itemName": "", "rewardItemName": "", "minigameType": "s_m_m_chemsec_01", "minigameCheckbox": false, "triggersBlackout": false, "delayRewardCheckbox": false, "heading": "", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "", "isRequiredNextStage": false, "type": "guards", "selectedDoorName": "", "requiredWeapons": [], "rewardItem": false, "targetText": "", "rewardMoneyType": "", "stepName": "guards", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "pedCoords": "", "targetVector": "", "quantity": "", "createExplosion": false, "animation": "", "propHeading": "", "propName": "", "hackType": "s_m_m_chemsec_01"}, {"lasers": [], "triggersBlackout": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "penaltyType": "electrocute", "minigameCheckbox": false, "rewardItemQuantity": "", "unlockTime": "1", "itemName": "", "selectedWeapon": "", "xpRemoveAmount": "0", "requiresItem": false, "stepNumber": 4, "penaltyForFailing": true, "xpOptions": false, "doorPosition": "3540.28, 3673.43, 19.99", "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItem": false, "guards": [], "rewardMoneyType": "", "rewardItemName": "", "minigameType": "", "rewardItemMin": "", "pedCoords": "3538.06, 3672.61, 19.99", "stepName": "slide double door 1", "heading": "350.00", "xpNeededAmount": "0", "penaltyChance": "100", "rewardMoneyMin": "", "isRequiredNextStage": true, "type": "hackable_door", "selectedDoorName": "Humane Double Door 1", "requiredWeapons": [], "delayRewardCheckbox": false, "targetText": "Hack <PERSON>", "rewardItemMax": "", "doorId": "1021", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "3538.20, 3673.17, 21.41", "sendAlert": true, "createExplosion": false, "animation": "hei_prop_hei_securitypanel", "propHeading": "350.00", "propName": "hei_prop_hei_securitypanel", "hackType": "key_drop_game"}, {"lasers": [], "xpOptions": false, "rewardItem": false, "isOptional": false, "items": [], "dropWalletWithPasscode": true, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "", "removeXpOnHit": false, "lootableItems": [{"name": "gasmask", "chance": 1, "maxAmount": 1, "minAmount": 1}], "penaltyType": "", "rewardItemMax": "", "rewardItemQuantity": "", "unlockTime": "", "sendAlert": false, "selectedWeapon": "", "xpRemoveAmount": "0", "requiresItem": false, "stepNumber": 5, "penaltyForFailing": false, "showEnemyBlip": false, "rewardDelayTime": "", "requireWeaponInHand": false, "rewardItems": [], "walletDropChance": 100, "rewardItemMin": "", "guards": [{"guardPosition": [3527.63, 3673.4, 27.12], "heading": 266, "weapon": "weapon_minigun", "guardModel": "s_m_m_chemsec_01"}], "itemName": "", "rewardItemName": "", "minigameType": "s_m_m_chemsec_01", "minigameCheckbox": false, "triggersBlackout": false, "delayRewardCheckbox": false, "heading": "", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "", "isRequiredNextStage": false, "type": "guards", "selectedDoorName": "", "requiredWeapons": [], "stepName": "guards", "targetText": "", "rewardMoneyType": "", "unlockTimeCheckbox": false, "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "pedCoords": "", "targetVector": "", "quantity": "", "createExplosion": false, "animation": "", "propHeading": "", "propName": "", "hackType": "s_m_m_chemsec_01"}, {"lasers": [], "triggersBlackout": false, "unlockTimeCheckbox": true, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "penaltyType": "electrocute", "minigameCheckbox": false, "rewardItemQuantity": "", "unlockTime": "5", "itemName": "", "selectedWeapon": "", "xpRemoveAmount": "0", "requiresItem": false, "stepNumber": 6, "penaltyForFailing": true, "xpOptions": false, "doorPosition": "3531.81, 3670.84, 27.12", "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItem": false, "guards": [], "rewardMoneyType": "", "rewardItemName": "", "minigameType": "", "rewardItemMin": "", "pedCoords": "3533.67, 3671.38, 27.12", "stepName": "slide double door 2", "heading": "173.00", "xpNeededAmount": "0", "penaltyChance": "100", "rewardMoneyMin": "", "isRequiredNextStage": true, "type": "hackable_door", "selectedDoorName": "Humane Double Door 2", "requiredWeapons": [], "delayRewardCheckbox": false, "targetText": "", "rewardItemMax": "", "doorId": "1020", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "3533.50, 3670.71, 28.43", "sendAlert": true, "createExplosion": false, "animation": "8", "propHeading": "173.00", "propName": "prop_ld_keypad_01b", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "", "removeXpOnHit": false, "lootableItems": [], "penaltyType": "", "rewardItemQuantity": "", "unlockTime": "", "sendAlert": false, "selectedWeapon": "", "xpRemoveAmount": "0", "rewardItemMax": "", "stepNumber": 7, "penaltyForFailing": false, "requiresItem": false, "showEnemyBlip": false, "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItemMin": "", "guards": [{"guardPosition": [3540.44, 3667.46, 27.12], "heading": 110, "weapon": "weapon_carbinerifle", "guardModel": "s_m_m_chemsec_01"}, {"guardPosition": [3533.42, 3660.5, 27.12], "heading": 0, "weapon": "weapon_carbinerifle", "guardModel": "s_m_m_chemsec_01"}, {"guardPosition": [3527.61, 3648.61, 26.52], "heading": 0, "weapon": "weapon_carbinerifle", "guardModel": "s_m_m_chemsec_01"}, {"guardPosition": [3537.57, 3646.58, 27.12], "heading": 83, "weapon": "weapon_carbinerifle", "guardModel": "s_m_m_chemsec_01"}], "itemName": "", "rewardItemName": "", "minigameType": "s_m_m_chemsec_01", "minigameCheckbox": false, "triggersBlackout": false, "delayRewardCheckbox": false, "heading": "", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "", "isRequiredNextStage": false, "type": "guards", "selectedDoorName": "", "requiredWeapons": [], "rewardItem": false, "targetText": "", "rewardMoneyType": "", "stepName": "guards", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "pedCoords": "", "targetVector": "", "quantity": "", "createExplosion": false, "animation": "", "propHeading": "", "propName": "", "hackType": "s_m_m_chemsec_01"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 8, "penaltyForFailing": false, "rewardItemMax": "1", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "humaneco2", "rewardChance": 100, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "humaneco2", "stepName": "vile 1", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "3536.25, 3662.13, 28.12", "heading": "335.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "<PERSON><PERSON>", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "3536.25, 3662.13, 28.12", "sendAlert": false, "createExplosion": false, "animation": "p_chem_vial_02b_s", "propHeading": "335.00", "propName": "p_chem_vial_02b_s", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 9, "penaltyForFailing": false, "rewardItemMax": "1", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "humaneco2", "rewardChance": 100, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "humaneco2", "stepName": "vile 2", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "3560.51, 3672.63, 28.12", "heading": "148.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "<PERSON><PERSON>", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "3560.51, 3672.63, 28.12", "sendAlert": false, "createExplosion": false, "animation": "p_chem_vial_02b_s", "propHeading": "148.00", "propName": "p_chem_vial_02b_s", "hackType": "none"}, {"lasers": [{"startVector": [3595.21, 3719.1, 29.72], "intensity": 100, "endVector": [3591.49, 3719.77, 29.67]}, {"startVector": [3591.49, 3719.77, 30.43], "intensity": 100, "endVector": [3595.21, 3719.11, 30.48]}, {"startVector": [3596.24, 3715.18, 29.03], "intensity": 100, "endVector": [3589.06, 3716.59, 29.26]}, {"intensity": 100, "startVector": [3589.09, 3716.79, 29.06], "movement": {"speed": 0.02, "amount": 9, "direction": "E"}, "endVector": [3588.09, 3717.01, 32.19]}, {"intensity": 100, "startVector": [3597.11, 3713.55, 32.11], "movement": {"speed": 0.02, "amount": 9, "direction": "WNW"}, "endVector": [3597.21, 3713.54, 28.69]}, {"startVector": [3588.63, 3713.01, 28.69], "intensity": 100, "endVector": [3598, 3718.47, 32.19]}, {"startVector": [3596.61, 3709.91, 32.16], "intensity": 100, "endVector": [3588.51, 3720.25, 28.71]}, {"startVector": [3595.22, 3719.18, 28.73], "intensity": 100, "endVector": [3591.51, 3719.85, 31.46]}, {"startVector": [3595.22, 3719.17, 31.48], "intensity": 100, "endVector": [3591.5, 3719.83, 28.72]}, {"intensity": 100, "startVector": [3595.22, 3719.17, 31.47], "movement": {"speed": 0.009, "amount": 2.7, "direction": "down"}, "endVector": [3591.5, 3719.83, 31.49]}, {"intensity": 100, "startVector": [3591.51, 3719.85, 28.88], "movement": {"speed": 0.009, "amount": 2.7, "direction": "up"}, "endVector": [3595.23, 3719.2, 28.86]}], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [{"itemName": "wirecutters", "loseChance": 100, "itemQuantity": 1}], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "1", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 10, "penaltyForFailing": false, "rewardItemMax": "", "requiresItem": true, "requireWeaponInHand": false, "rewardItems": [], "triggersBlackout": false, "rewardDelayTime": "", "guards": [], "rewardMoneyType": "", "rewardItemName": "", "stepName": "lasers", "rewardItem": false, "rewardItemMin": "", "pedCoords": "3590.62, 3719.78, 28.69", "heading": "349.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "", "minigameCheckbox": false, "type": "lasers", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Shut Lasers Off", "itemName": "wirecutters", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "1", "targetVector": "3590.62, 3719.78, 28.69", "sendAlert": false, "createExplosion": false, "animation": "tr_prop_tr_elecbox_01a", "propHeading": "349.00", "propName": "tr_prop_tr_elecbox_01a", "hackType": "fingerprint"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "", "removeXpOnHit": false, "lootableItems": [], "penaltyType": "", "rewardItemQuantity": "", "unlockTime": "", "sendAlert": false, "selectedWeapon": "", "xpRemoveAmount": "0", "rewardItemMax": "", "stepNumber": 11, "penaltyForFailing": false, "requiresItem": false, "showEnemyBlip": false, "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItemMin": "", "guards": [{"guardPosition": [3596.34, 3702.16, 28.69], "heading": 0, "weapon": "weapon_carbinerifle", "guardModel": "s_m_m_chemsec_01"}, {"guardPosition": [3608.21, 3712.19, 28.69], "heading": 147, "weapon": "weapon_bullpuprifle", "guardModel": "s_m_m_chemsec_01"}], "itemName": "", "rewardItemName": "", "minigameType": "s_m_m_chemsec_01", "minigameCheckbox": false, "triggersBlackout": false, "delayRewardCheckbox": false, "heading": "", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "", "isRequiredNextStage": false, "type": "guards", "selectedDoorName": "", "requiredWeapons": [], "rewardItem": false, "targetText": "", "rewardMoneyType": "", "stepName": "guards", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "pedCoords": "", "targetVector": "", "quantity": "", "createExplosion": false, "animation": "", "propHeading": "", "propName": "", "hackType": "s_m_m_chemsec_01"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 12, "penaltyForFailing": false, "rewardItemMax": "1", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "gasmask", "rewardChance": 100, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "gasmask", "stepName": "Mask", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "3558.02, 3662.28, 27.12", "heading": "259.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "<PERSON>rab <PERSON>", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "3558.71, 3662.16, 28.12", "sendAlert": false, "createExplosion": false, "animation": "16", "propHeading": "259.00", "propName": "v_ret_gc_gasmask", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 13, "penaltyForFailing": false, "rewardItemMax": "1", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "gasmask", "rewardChance": 100, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "gasmask", "stepName": "Mask 2", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "3562.56, 3676.11, 27.12", "heading": "314.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "<PERSON>rab <PERSON>", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "3563.18, 3676.62, 27.18", "sendAlert": false, "createExplosion": false, "animation": "16", "propHeading": "314.00", "propName": "v_ret_gc_gasmask", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 14, "penaltyForFailing": false, "rewardItemMax": "1", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "gasmask", "rewardChance": 100, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "gasmask", "stepName": "Mask 3", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "3561.52, 3681.33, 27.12", "heading": "166.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "<PERSON>rab <PERSON>", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "3561.40, 3680.93, 27.91", "sendAlert": false, "createExplosion": false, "animation": "16", "propHeading": "166.00", "propName": "v_ret_gc_gasmask", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 15, "penaltyForFailing": false, "rewardItemMax": "1", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "gasmask", "rewardChance": 100, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "gasmask", "stepName": "mask 4", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "3589.15, 3713.58, 28.69", "heading": "125.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "<PERSON>rab <PERSON>", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "3588.52, 3712.94, 28.70", "sendAlert": false, "createExplosion": false, "animation": "16", "propHeading": "152.00", "propName": "v_ret_gc_gasmask", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 16, "penaltyForFailing": false, "rewardItemMax": "1", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "WEAPON_MICROSMG", "rewardChance": 50, "maxAmount": 1, "minAmount": 1}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "WEAPON_MICROSMG", "stepName": "hidden smg", "rewardItem": true, "rewardItemMin": "1", "pedCoords": "3539.47, 3659.57, 27.12", "heading": "228.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Gab SMG", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "3539.95, 3659.06, 27.40", "sendAlert": false, "createExplosion": false, "animation": "16", "propHeading": "216.00", "propName": "w_sb_microsmg", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "1", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "rewardItemQuantity": "", "unlockTime": "", "penaltyType": "", "selectedWeapon": "", "xpRemoveAmount": "0", "isRequiredNextStage": false, "stepNumber": 17, "penaltyForFailing": false, "rewardItemMax": "200", "requiresItem": false, "requireWeaponInHand": false, "rewardItems": [{"itemName": "cokebaggy", "rewardChance": 100, "maxAmount": 200, "minAmount": 100}], "triggersBlackout": false, "rewardDelayTime": "1", "guards": [], "rewardMoneyType": "cash", "rewardItemName": "cokebaggy", "stepName": "cocain tray", "rewardItem": true, "rewardItemMin": "100", "pedCoords": "3541.30, 3668.28, 27.12", "heading": "146.00", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "1", "minigameCheckbox": false, "type": "robabble_object", "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "minigameType": "", "targetText": "Grab Coke", "itemName": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "3541.30, 3668.28, 27.12", "sendAlert": false, "createExplosion": false, "animation": "imp_prop_impexp_coke_trolly", "propHeading": "146.00", "propName": "imp_prop_impexp_coke_trolly", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "", "removeXpOnHit": false, "lootableItems": [], "penaltyType": "", "rewardItemQuantity": "", "unlockTime": "", "type": "hidden_passcode", "selectedWeapon": "", "xpRemoveAmount": "0", "minigameCheckbox": false, "stepNumber": 18, "penaltyForFailing": false, "requiresItem": false, "triggersBlackout": false, "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItemMin": "", "guards": [], "itemName": "", "rewardItemName": "", "minigameType": "", "rewardItemMax": "", "pedCoords": "", "isRequiredNextStage": false, "heading": "", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "", "rewardItem": false, "passcodes": [{"position": [3559.47, 3661.4, 27.37], "heading": 261}, {"position": [3553.77, 3655.29, 29.56], "heading": 257}, {"position": [3549.05, 3640.06, 27.62], "heading": 260}, {"position": [3550.02, 3645.87, 29.84], "heading": 351}, {"position": [3544.97, 3647.13, 29.73], "heading": 355}, {"position": [3532.56, 3650.02, 29.89], "heading": 260}, {"position": [3531.87, 3657.11, 29.93], "heading": 354}, {"position": [3533.5, 3667.6, 30.19], "heading": 83}, {"position": [3539.28, 3661.55, 27.62], "heading": 79}, {"position": [3532.46, 3658.91, 28.27], "heading": 169}], "selectedDoorName": "", "requiredWeapons": [], "delayRewardCheckbox": false, "stepName": "hidden passcode", "targetText": "", "rewardMoneyType": "", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "sendAlert": false, "targetVector": "", "quantity": "", "createExplosion": false, "animation": "", "propHeading": "", "propName": "", "hackType": ""}, {"lasers": [], "triggersBlackout": false, "unlockTimeCheckbox": true, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "1", "removeXpOnHit": false, "lootableItems": [], "penaltyType": "", "minigameCheckbox": false, "rewardItemQuantity": "", "unlockTime": "5", "itemName": "", "selectedWeapon": "", "xpRemoveAmount": "0", "requiresItem": false, "stepNumber": 19, "penaltyForFailing": false, "xpOptions": false, "doorPosition": "3554.15, 3665.03, 27.12", "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItem": false, "guards": [], "rewardMoneyType": "", "rewardItemName": "", "minigameType": "", "rewardItemMin": "", "pedCoords": "3552.57, 3664.54, 27.12", "stepName": "double slide door 3", "heading": "355.00", "xpNeededAmount": "0", "penaltyChance": "100", "rewardMoneyMin": "", "isRequiredNextStage": false, "type": "hackable_door", "selectedDoorName": "Humane labs double slide door 3", "requiredWeapons": [], "delayRewardCheckbox": false, "targetText": "Enter Code", "rewardItemMax": "", "doorId": "1022", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "quantity": "", "targetVector": "3552.58, 3665.15, 28.47", "sendAlert": false, "createExplosion": false, "animation": "8", "propHeading": "355.00", "propName": "prop_ld_keypad_01b", "hackType": "none"}, {"lasers": [], "xpOptions": false, "unlockTimeCheckbox": false, "isOptional": false, "items": [], "dropWalletWithPasscode": false, "requireSimultaneous": false, "rewardMoneyMax": "", "blackoutDuration": "", "removeXpOnHit": false, "lootableItems": [], "penaltyType": "", "rewardItemQuantity": "", "unlockTime": "", "sendAlert": false, "selectedWeapon": "", "xpRemoveAmount": "0", "rewardItemMax": "", "stepNumber": 20, "penaltyForFailing": false, "requiresItem": false, "showEnemyBlip": false, "requireWeaponInHand": false, "rewardItems": [], "rewardDelayTime": "", "rewardItemMin": "", "guards": [{"guardPosition": [3561.9, 3664.56, 27.12], "heading": 81, "weapon": "weapon_assaultsmg", "guardModel": "s_m_m_chemsec_01"}, {"guardPosition": [3560.56, 3668.68, 27.12], "heading": 47, "weapon": "weapon_mg", "guardModel": "s_m_m_chemsec_01"}, {"guardPosition": [3563.02, 3678.71, 27.12], "heading": 94, "weapon": "weapon_carbinerifle", "guardModel": "s_m_m_chemsec_01"}, {"guardPosition": [3557.18, 3685.3, 27.12], "heading": 191, "weapon": "weapon_specialcarbine_mk2", "guardModel": "s_m_m_chemsec_01"}, {"guardPosition": [3566.72, 3681.78, 27.12], "heading": 88, "weapon": "weapon_carbinerifle_mk2", "guardModel": "s_m_m_chemsec_01"}, {"guardPosition": [3563.3, 3691.1, 27.12], "heading": 209, "weapon": "weapon_carbinerifle", "guardModel": "s_m_m_chemsec_01"}], "itemName": "", "rewardItemName": "", "minigameType": "s_m_m_chemsec_01", "minigameCheckbox": false, "triggersBlackout": false, "delayRewardCheckbox": false, "heading": "", "xpNeededAmount": "0", "penaltyChance": "", "rewardMoneyMin": "", "isRequiredNextStage": false, "type": "guards", "selectedDoorName": "", "requiredWeapons": [], "rewardItem": false, "targetText": "", "rewardMoneyType": "", "stepName": "guards", "xpRewardAmount": "0", "rewardMoney": false, "difficulty": "", "pedCoords": "", "targetVector": "", "quantity": "", "createExplosion": false, "animation": "", "propHeading": "", "propName": "", "hackType": "s_m_m_chemsec_01"}], "settings": {"cooldownMinutes": 25, "policeAlert": "Armed Humane Labs Robbery", "minutesUntilRobbery": "", "cooldownHeists": ["store south side", "store robbery vanila unicorn", "store above big bank", "store near casino", "store burgershot", "store mirror park", "bank fleeca legion", "bank fleeca above pillbox", "bank fleeca ls customs", "bank fleeca life invader", "bank fleeca ocean", "bank fleeca harmony", "vangelico jewelry store"], "blipFlash": false, "robberyCooldown": "45", "policeJobs": ["police", "leo", "lspd", "sasp", "ranger", "bcso", "trooper"], "policeRequired": "", "blipColor": 2, "alertIcon": "fas fa-bell", "alertPriority": 1, "blipSprite": 499, "alertCode": "10-90", "alertSound1": "robberysound", "blipScale": 1, "policeAlertHeader": "Armed Humane Labs Robbery"}}, {"heist_name": "big bank robbery", "stages": [{"stepNumber": 1, "rewardItems": [], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "", "targetVector": "697.86, 157.94, 81.16", "animation": "10", "penaltyForFailing": true, "xpNeededAmount": "0", "heading": "152.00", "requiresItem": true, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "requireSimultaneous": false, "sendAlert": false, "itemName": "c4", "rewardDelayTime": "", "lasers": [], "doorId": "", "targetText": "Place Device", "rewardItemMax": "", "requireWeaponInHand": false, "requiredWeapons": [], "rewardItemMin": "", "rewardItemName": "", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "guards": [], "quantity": "1", "propHeading": "0.00", "xpRemoveAmount": "0", "hackType": "none", "stepName": "c4 power grid 1", "createExplosion": true, "rewardMoneyMax": "", "penaltyType": "explode", "items": [{"itemName": "c4", "loseChance": 100, "itemQuantity": 1}], "doorPosition": "", "difficulty": "", "selectedDoorName": "No door selected", "rewardMoneyType": "", "type": "hackable_door", "rewardMoney": false, "delayRewardCheckbox": false, "propName": "", "penaltyChance": "30", "unlockTimeCheckbox": true, "selectedWeapon": "", "pedCoords": "698.20, 158.24, 79.95", "unlockTime": "10", "rewardItem": false, "xpRewardAmount": "0", "lootableItems": []}, {"stepNumber": 2, "rewardItems": [], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "", "targetVector": "686.65, 144.49, 81.17", "animation": "10", "penaltyForFailing": true, "xpNeededAmount": "0", "heading": "148.00", "requiresItem": true, "isRequiredNextStage": true, "rewardItemQuantity": "", "minigameCheckbox": false, "requireSimultaneous": false, "sendAlert": false, "itemName": "c4", "rewardDelayTime": "", "lasers": [], "doorId": "", "targetText": "Place Device", "rewardItemMax": "", "requireWeaponInHand": false, "requiredWeapons": [], "rewardItemMin": "", "rewardItemName": "", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "guards": [], "quantity": "1", "propHeading": "0.00", "xpRemoveAmount": "0", "hackType": "none", "stepName": "c4 power grid 2", "createExplosion": true, "rewardMoneyMax": "", "penaltyType": "explode", "items": [{"itemName": "c4", "loseChance": 100, "itemQuantity": 1}], "doorPosition": "", "difficulty": "", "selectedDoorName": "No door selected", "rewardMoneyType": "", "type": "hackable_door", "rewardMoney": false, "delayRewardCheckbox": false, "propName": "", "penaltyChance": "30", "unlockTimeCheckbox": true, "selectedWeapon": "", "pedCoords": "687.03, 144.75, 79.94", "unlockTime": "10", "rewardItem": false, "xpRewardAmount": "0", "lootableItems": []}, {"stepNumber": 3, "rewardItems": [], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "", "targetVector": "673.83, 149.84, 80.84", "animation": "10", "penaltyForFailing": true, "xpNeededAmount": "0", "heading": "143.00", "requiresItem": true, "isRequiredNextStage": true, "rewardItemQuantity": "", "minigameCheckbox": false, "requireSimultaneous": false, "sendAlert": false, "itemName": "c4", "rewardDelayTime": "", "lasers": [], "doorId": "", "targetText": "Place Device", "rewardItemMax": "", "requireWeaponInHand": false, "requiredWeapons": [], "rewardItemMin": "", "rewardItemName": "", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "guards": [], "quantity": "1", "propHeading": "0.00", "xpRemoveAmount": "0", "hackType": "none", "stepName": "c4 power grid 3", "createExplosion": true, "rewardMoneyMax": "", "penaltyType": "", "items": [{"itemName": "c4", "loseChance": 100, "itemQuantity": 1}], "doorPosition": "", "difficulty": "", "selectedDoorName": "No door selected", "rewardMoneyType": "", "type": "hackable_door", "rewardMoney": false, "delayRewardCheckbox": false, "propName": "", "penaltyChance": "30", "unlockTimeCheckbox": true, "selectedWeapon": "", "pedCoords": "674.11, 150.07, 79.93", "unlockTime": "10", "rewardItem": false, "xpRewardAmount": "0", "lootableItems": []}, {"stepNumber": 4, "rewardItems": [], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "", "targetVector": "664.43, 130.52, 81.18", "animation": "10", "penaltyForFailing": true, "xpNeededAmount": "0", "heading": "351.00", "requiresItem": true, "isRequiredNextStage": true, "rewardItemQuantity": "", "minigameCheckbox": false, "requireSimultaneous": false, "sendAlert": false, "itemName": "c4", "rewardDelayTime": "", "lasers": [], "doorId": "", "targetText": "Place Device", "rewardItemMax": "", "requireWeaponInHand": false, "requiredWeapons": [], "rewardItemMin": "", "rewardItemName": "", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "guards": [], "quantity": "1", "propHeading": "0.00", "xpRemoveAmount": "0", "hackType": "none", "stepName": "c4 power grid 4", "createExplosion": true, "rewardMoneyMax": "", "penaltyType": "", "items": [{"itemName": "c4", "loseChance": 100, "itemQuantity": 1}], "doorPosition": "", "difficulty": "", "selectedDoorName": "No door selected", "rewardMoneyType": "", "type": "hackable_door", "rewardMoney": false, "delayRewardCheckbox": false, "propName": "", "penaltyChance": "30", "unlockTimeCheckbox": true, "selectedWeapon": "", "pedCoords": "664.19, 130.12, 79.96", "unlockTime": "10", "rewardItem": false, "xpRewardAmount": "0", "lootableItems": []}, {"stepNumber": 5, "rewardItems": [], "isOptional": false, "triggersBlackout": true, "removeXpOnHit": false, "rewardMoneyMin": "", "targetVector": "703.73, 107.39, 81.09", "animation": "10", "penaltyForFailing": true, "xpNeededAmount": "0", "heading": "347.00", "requiresItem": true, "isRequiredNextStage": true, "rewardItemQuantity": "", "minigameCheckbox": false, "requireSimultaneous": false, "sendAlert": false, "itemName": "c4", "rewardDelayTime": "", "lasers": [], "doorId": "", "targetText": "Place Device", "rewardItemMax": "", "requireWeaponInHand": false, "requiredWeapons": [], "rewardItemMin": "", "rewardItemName": "", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "70", "minigameType": "", "guards": [], "quantity": "1", "propHeading": "0.00", "xpRemoveAmount": "0", "hackType": "none", "stepName": "c4 power grid 5", "createExplosion": true, "rewardMoneyMax": "", "penaltyType": "", "items": [{"itemName": "c4", "loseChance": 100, "itemQuantity": 1}], "doorPosition": "", "difficulty": "", "selectedDoorName": "No door selected", "rewardMoneyType": "", "type": "hackable_door", "rewardMoney": false, "delayRewardCheckbox": false, "propName": "", "penaltyChance": "30", "unlockTimeCheckbox": true, "selectedWeapon": "", "pedCoords": "703.35, 107.05, 79.94", "unlockTime": "10", "rewardItem": false, "xpRewardAmount": "0", "lootableItems": []}, {"stepNumber": 6, "rewardItems": [], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "", "targetVector": "236.00, 228.30, 106.68", "animation": "hei_prop_hei_securitypanel", "penaltyForFailing": true, "xpNeededAmount": "0", "heading": "339.00", "requiresItem": true, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": true, "selectedDoorName": "Big Bank Door Side", "sendAlert": true, "itemName": "bigbankcard", "rewardDelayTime": "", "lasers": [], "rewardItemMax": "", "targetText": "Open Door", "requireSimultaneous": false, "requireWeaponInHand": false, "doorId": "1028", "rewardItemMin": "", "rewardItemName": "", "dropWalletWithPasscode": false, "xpOptions": false, "rewardMoney": false, "minigameType": "", "hackType": "maze", "quantity": "1", "propHeading": "339.00", "xpRemoveAmount": "0", "stepName": "side door", "penaltyType": "electrocute", "createExplosion": false, "rewardMoneyMax": "", "guards": [], "type": "hackable_door", "doorPosition": "237.77, 227.87, 106.43", "difficulty": "", "rewardMoneyType": "", "requireBlackout": true, "items": [{"itemName": "bigbankcard", "loseChance": 50, "itemQuantity": 1}], "delayRewardCheckbox": false, "blackoutDuration": "1", "unlockTime": "5", "penaltyChance": "100", "requiredWeapons": [], "selectedWeapon": "", "pedCoords": "235.79, 227.74, 105.29", "propName": "hei_prop_hei_securitypanel", "rewardItem": false, "xpRewardAmount": "0", "lootableItems": []}, {"stepNumber": 7, "rewardItems": [], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "", "targetVector": "", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "", "requiresItem": false, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "", "rewardDelayTime": "", "lasers": [], "targetText": "", "animation": "", "requireWeaponInHand": false, "rewardItemMax": "", "rewardItemMin": "", "rewardItemName": "", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "", "minigameType": "", "type": "hidden_passcode", "quantity": "", "propHeading": "", "delayRewardCheckbox": false, "guards": [], "hackType": "", "createExplosion": false, "lootableItems": [], "stepName": "hidden passcode", "items": [], "penaltyType": "", "difficulty": "", "passcodes": [{"heading": 302, "position": [235.28, 227.71, 113.92]}, {"heading": 332, "position": [236.76, 221.5, 113.81]}, {"heading": 301, "position": [232.24, 220.39, 110.24]}, {"heading": 296, "position": [232.02, 217.11, 109.78]}, {"heading": 346, "position": [239.3, 211.38, 109.78]}, {"heading": 22, "position": [242.16, 210.24, 110.26]}, {"heading": 163, "position": [241.18, 209.7, 109.78]}, {"heading": 85, "position": [240.85, 210.84, 110.55]}, {"heading": 336, "position": [252.42, 210.47, 112.88]}, {"heading": 80, "position": [245.22, 209.01, 113.78]}, {"heading": 159, "position": [256.54, 204.78, 113.46]}, {"heading": 184, "position": [258.69, 204.64, 113.83]}, {"heading": 338, "position": [261.78, 218.21, 113.81]}, {"heading": 106, "position": [265.66, 213.51, 109.79]}, {"heading": 245, "position": [267.18, 215.89, 111.07]}, {"heading": 70, "position": [262.85, 215.53, 112.7]}, {"heading": 338, "position": [263.57, 211.88, 109.54]}, {"heading": 340, "position": [264.15, 209.34, 111.48]}, {"heading": 72, "position": [260.1, 207.93, 109.92]}, {"heading": 341, "position": [260.65, 204.65, 110.33]}], "rewardMoneyType": "", "rewardMoneyMax": "", "penaltyChance": "", "rewardMoney": false, "propName": "", "requireSimultaneous": false, "requiredWeapons": [], "selectedWeapon": "", "pedCoords": "", "unlockTime": "", "rewardItem": false, "xpRewardAmount": "0", "xpRemoveAmount": "0"}, {"stepNumber": 8, "rewardItems": [], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "", "targetVector": "256.96, 207.25, 110.22", "animation": "10", "penaltyForFailing": true, "xpNeededAmount": "0", "heading": "246.00", "requiresItem": true, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": true, "selectedDoorName": "Big Bank Door Top", "sendAlert": true, "itemName": "thermite", "rewardDelayTime": "", "lasers": [], "rewardItemMax": "", "targetText": "Thermite", "requireSimultaneous": false, "requireWeaponInHand": false, "doorId": "1029", "rewardItemMin": "", "rewardItemName": "", "dropWalletWithPasscode": false, "xpOptions": false, "rewardMoney": false, "minigameType": "", "hackType": "key_drop_game", "quantity": "1", "propHeading": "0.00", "xpRemoveAmount": "0", "stepName": "top door", "penaltyType": "fire", "createExplosion": false, "rewardMoneyMax": "", "guards": [], "type": "hackable_door", "doorPosition": "256.62, 206.15, 110.43", "difficulty": "", "rewardMoneyType": "", "requireBlackout": true, "items": [{"itemName": "thermite", "loseChance": 100, "itemQuantity": 1}], "delayRewardCheckbox": false, "blackoutDuration": "1", "unlockTime": "5", "penaltyChance": "100", "requiredWeapons": [], "selectedWeapon": "", "pedCoords": "256.58, 207.53, 109.28", "propName": "", "rewardItem": false, "xpRewardAmount": "0", "lootableItems": []}, {"stepNumber": 9, "rewardItems": [], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "", "targetVector": "255.56, 220.58, 106.80", "animation": "8", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "344.00", "requiresItem": false, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "requireSimultaneous": false, "sendAlert": false, "itemName": "", "rewardDelayTime": "", "lasers": [{"startVector": [241.2, 226.2, 112.76], "endVector": [259.81, 219.48, 108.6], "intensity": 100}, {"startVector": [259.73, 219.41, 112.33], "endVector": [240.51, 226.88, 108.67], "intensity": 100}, {"startVector": [259.85, 219.56, 112.24], "endVector": [240.79, 226.52, 112.29], "intensity": 100, "movement": {"amount": 4, "speed": 0.01, "direction": "down"}}, {"startVector": [260.02, 219.03, 109.26], "endVector": [240.4, 226.57, 109.15], "intensity": 100, "movement": {"amount": 4, "speed": 0.01, "direction": "up"}}, {"startVector": [262.63, 224.05, 105.29], "endVector": [261, 219.88, 108.36], "intensity": 100}, {"startVector": [262.64, 224.06, 108.3], "endVector": [261.05, 219.88, 105.28], "intensity": 100}, {"startVector": [262.5, 224.11, 108.23], "endVector": [260.96, 219.9, 108.4], "intensity": 100, "movement": {"amount": 3, "speed": 0.007, "direction": "down"}}, {"startVector": [262.65, 224.03, 105.39], "endVector": [260.93, 219.91, 105.46], "intensity": 100, "movement": {"amount": 3, "speed": 0.007, "direction": "up"}}], "doorId": "1023", "targetText": "Enter Pass Code", "rewardItemMax": "", "requireWeaponInHand": false, "requiredWeapons": [], "rewardItemMin": "", "rewardItemName": "", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "guards": [], "quantity": "", "propHeading": "344.00", "xpRemoveAmount": "0", "hackType": "none", "stepName": "main door", "createExplosion": false, "rewardMoneyMax": "", "penaltyType": "", "items": [], "doorPosition": "256.31, 220.66, 106.43", "difficulty": "", "selectedDoorName": "Big Bank Main Door", "rewardMoneyType": "", "type": "hackable_door", "rewardMoney": false, "delayRewardCheckbox": false, "propName": "prop_ld_keypad_01b", "penaltyChance": "100", "unlockTimeCheckbox": false, "selectedWeapon": "", "pedCoords": "255.35, 219.99, 105.29", "unlockTime": "1", "rewardItem": false, "xpRewardAmount": "0", "lootableItems": []}, {"stepNumber": 10, "rewardItems": [], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "", "targetVector": "", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "", "requiresItem": false, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "", "rewardDelayTime": "", "lasers": [], "targetText": "", "animation": "", "requireWeaponInHand": false, "rewardItemMax": "", "rewardItemMin": "", "rewardItemName": "", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "", "minigameType": "", "type": "toxic_gas", "quantity": "", "propHeading": "", "delayRewardCheckbox": false, "penaltyType": "", "hackType": "", "createExplosion": false, "rewardMoneyMax": "", "stepName": "toxic gas", "items": [], "toxicGas": [{"gasSize": 2.4, "gasVector": "263.64, 220.87, 100.68", "gasDamage": 5}, {"gasSize": 2.3, "gasVector": "253.40, 227.76, 100.68", "gasDamage": 5}, {"gasSize": 2.5, "gasVector": "254.44, 226.46, 105.29", "gasDamage": 5}, {"gasSize": 2.2, "gasVector": "262.18, 222.95, 106.39", "gasDamage": 5}], "difficulty": "", "guards": [], "rewardMoneyType": "", "lootableItems": [], "rewardMoney": false, "propName": "", "requiredWeapons": [], "requireSimultaneous": false, "penaltyChance": "", "selectedWeapon": "", "pedCoords": "", "unlockTime": "", "rewardItem": false, "xpRewardAmount": "0", "xpRemoveAmount": "0"}, {"stepNumber": 11, "rewardItems": [], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "", "targetVector": "262.22, 223.05, 106.64", "animation": "hei_prop_hei_securitypanel", "penaltyForFailing": true, "xpNeededAmount": "0", "heading": "0.00", "requiresItem": false, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "selectedDoorName": "Big Bank Door 2", "sendAlert": true, "itemName": "", "rewardDelayTime": "", "lasers": [], "rewardItemMax": "", "targetText": "Enter Card", "requireSimultaneous": false, "requireWeaponInHand": false, "doorId": "1024", "rewardItemMin": "", "rewardItemName": "", "dropWalletWithPasscode": false, "xpOptions": false, "rewardMoney": false, "minigameType": "", "hackType": "scrambler", "quantity": "", "propHeading": "250.00", "xpRemoveAmount": "0", "stepName": "door 2", "penaltyType": "electrocute", "createExplosion": false, "rewardMoneyMax": "", "guards": [], "type": "hackable_door", "doorPosition": "262.20, 222.52, 106.43", "difficulty": "", "rewardMoneyType": "", "requireBlackout": true, "items": [], "delayRewardCheckbox": false, "blackoutDuration": "1", "unlockTime": "1", "penaltyChance": "100", "requiredWeapons": [], "selectedWeapon": "", "pedCoords": "262.22, 223.05, 106.64", "propName": "hei_prop_hei_securitypanel", "rewardItem": false, "xpRewardAmount": "0", "lootableItems": []}, {"stepNumber": 12, "rewardItems": [], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "", "targetVector": "", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "", "requiresItem": false, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "", "rewardDelayTime": "", "lasers": [], "targetText": "", "animation": "", "requireWeaponInHand": false, "rewardItemMax": "", "rewardItemMin": "", "rewardItemName": "", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "", "minigameType": "s_m_m_chemsec_01", "type": "guards", "quantity": "", "propHeading": "", "delayRewardCheckbox": false, "penaltyType": "", "showEnemyBlip": false, "createExplosion": false, "rewardMoneyMax": "", "hackType": "s_m_m_chemsec_01", "items": [], "stepName": "guards", "difficulty": "", "guards": [{"weapon": "weapon_assaultrifle_mk2", "guardPosition": [269.35, 223.43, 102.48], "heading": 164, "guardModel": "s_m_m_chemsec_01"}, {"weapon": "weapon_carbinerifle", "guardPosition": [262.48, 225.78, 100.68], "heading": 222, "guardModel": "s_m_m_chemsec_01"}, {"weapon": "weapon_combatshotgun", "guardPosition": [262.87, 220.46, 100.68], "heading": 0, "guardModel": "s_m_m_chemsec_01"}, {"weapon": "weapon_minigun", "guardPosition": [254.11, 228.71, 100.68], "heading": 241, "guardModel": "s_m_m_chemsec_01"}], "rewardMoneyType": "", "lootableItems": [{"minAmount": 1, "chance": 80, "maxAmount": 2, "name": "bankidcard"}], "rewardMoney": false, "propName": "", "requiredWeapons": [], "requireSimultaneous": false, "penaltyChance": "", "selectedWeapon": "", "pedCoords": "", "unlockTime": "", "rewardItem": false, "xpRewardAmount": "0", "xpRemoveAmount": "0"}, {"stepNumber": 13, "rewardItems": [{"minAmount": 1, "maxAmount": 1, "itemName": "paintingrocket", "rewardChance": 100}], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "1", "targetVector": "257.66, 228.15, 101.81", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "340.00", "requiresItem": false, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "", "rewardDelayTime": "1", "lasers": [], "targetText": "Cut Paiting", "requireWeaponInHand": false, "animation": "ch_prop_vault_painting_01d", "rewardItemMin": "1", "rewardItemName": "paintingrocket", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "rewardItemMax": "1", "quantity": "", "propHeading": "340.00", "delayRewardCheckbox": false, "rewardMoneyMax": "1", "penaltyType": "", "createExplosion": false, "lootableItems": [], "hackType": "none", "type": "robabble_object", "stepName": "painting rocket", "difficulty": "", "guards": [], "rewardMoneyType": "cash", "items": [], "rewardMoney": false, "propName": "ch_prop_vault_painting_01d", "requireSimultaneous": false, "requiredWeapons": [], "penaltyChance": "", "selectedWeapon": "", "pedCoords": "257.66, 228.15, 101.81", "unlockTime": "", "rewardItem": true, "xpRewardAmount": "0", "xpRemoveAmount": "0"}, {"stepNumber": 14, "rewardItems": [{"minAmount": 1, "maxAmount": 1, "itemName": "paintingpig", "rewardChance": 100}], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "1", "targetVector": "263.53, 218.73, 101.87", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "160.00", "requiresItem": false, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "", "rewardDelayTime": "1", "lasers": [], "targetText": "Cut Painting", "requireWeaponInHand": false, "animation": "ch_prop_vault_painting_01b", "rewardItemMin": "1", "rewardItemName": "paintingpig", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "rewardItemMax": "1", "quantity": "", "propHeading": "160.00", "delayRewardCheckbox": false, "rewardMoneyMax": "1", "penaltyType": "", "createExplosion": false, "lootableItems": [], "hackType": "none", "type": "robabble_object", "stepName": "painting pig", "difficulty": "", "guards": [], "rewardMoneyType": "cash", "items": [], "rewardMoney": false, "propName": "ch_prop_vault_painting_01b", "requireSimultaneous": false, "requiredWeapons": [], "penaltyChance": "", "selectedWeapon": "", "pedCoords": "263.53, 218.73, 101.87", "unlockTime": "", "rewardItem": true, "xpRewardAmount": "0", "xpRemoveAmount": "0"}, {"stepNumber": 15, "rewardItems": [], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "", "targetVector": "252.92, 228.53, 102.09", "animation": "hei_prop_hei_securitypanel", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "0.00", "requiresItem": true, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": true, "selectedDoorName": "Big Bank Lower Vault", "sendAlert": true, "itemName": "bankidcard", "rewardDelayTime": "", "lasers": [], "rewardItemMax": "", "targetText": "", "requireSimultaneous": false, "requireWeaponInHand": false, "doorId": "1025", "rewardItemMin": "", "rewardItemName": "", "dropWalletWithPasscode": false, "xpOptions": false, "rewardMoney": false, "minigameType": "", "hackType": "inspired-thermite", "quantity": "1", "propHeading": "70.00", "xpRemoveAmount": "0", "stepName": "Lower Vault", "penaltyType": "", "createExplosion": false, "rewardMoneyMax": "", "guards": [], "type": "hackable_door", "doorPosition": "255.23, 223.98, 102.39", "difficulty": "", "rewardMoneyType": "", "requireBlackout": true, "items": [{"itemName": "bankidcard", "loseChance": 100, "itemQuantity": 1}], "delayRewardCheckbox": false, "blackoutDuration": "1", "unlockTime": "25", "penaltyChance": "100", "requiredWeapons": [], "selectedWeapon": "", "pedCoords": "252.92, 228.53, 102.09", "propName": "hei_prop_hei_securitypanel", "rewardItem": false, "xpRewardAmount": "0", "lootableItems": []}, {"stepNumber": 16, "rewardItems": [], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "", "targetVector": "", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "", "requiresItem": false, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "", "rewardDelayTime": "", "lasers": [], "targetText": "", "animation": "", "requireWeaponInHand": false, "rewardItemMax": "", "rewardItemMin": "", "rewardItemName": "", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "", "minigameType": "s_m_m_chemsec_01", "type": "guards", "quantity": "", "propHeading": "", "delayRewardCheckbox": false, "penaltyType": "", "showEnemyBlip": false, "createExplosion": false, "rewardMoneyMax": "", "hackType": "s_m_m_chemsec_01", "items": [], "stepName": "guards", "difficulty": "", "guards": [{"weapon": "weapon_combatpdw", "guardPosition": [250.9, 221.88, 100.68], "heading": 314, "guardModel": "s_m_m_chemsec_01"}, {"weapon": "weapon_bullpuprifle_mk2", "guardPosition": [254.43, 220.56, 100.68], "heading": 46, "guardModel": "s_m_m_chemsec_01"}], "rewardMoneyType": "", "lootableItems": [], "rewardMoney": false, "propName": "", "requiredWeapons": [], "requireSimultaneous": false, "penaltyChance": "", "selectedWeapon": "", "pedCoords": "", "unlockTime": "", "rewardItem": false, "xpRewardAmount": "0", "xpRemoveAmount": "0"}, {"stepNumber": 17, "rewardItems": [], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "", "targetVector": "253.00, 220.70, 101.75", "animation": "10", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "168.00", "requiresItem": true, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": true, "selectedDoorName": "Big Bank Lower Gate", "sendAlert": false, "itemName": "thermite", "rewardDelayTime": "", "lasers": [], "rewardItemMax": "", "targetText": "Place Thermite", "requireSimultaneous": false, "requireWeaponInHand": false, "doorId": "1026", "rewardItemMin": "", "rewardItemName": "", "dropWalletWithPasscode": false, "xpOptions": false, "rewardMoney": false, "minigameType": "", "hackType": "thermite", "quantity": "1", "propHeading": "0.00", "xpRemoveAmount": "0", "stepName": "lower gate 1", "penaltyType": "", "createExplosion": false, "rewardMoneyMax": "", "guards": [], "type": "hackable_door", "doorPosition": "251.86, 221.07, 101.83", "difficulty": "", "rewardMoneyType": "", "requireBlackout": true, "items": [{"itemName": "thermite", "loseChance": 75, "itemQuantity": 1}], "delayRewardCheckbox": false, "blackoutDuration": "1", "unlockTime": "10", "penaltyChance": "100", "requiredWeapons": [], "selectedWeapon": "", "pedCoords": "253.31, 221.04, 100.68", "propName": "", "rewardItem": false, "xpRewardAmount": "0", "lootableItems": []}, {"stepNumber": 18, "rewardItems": [], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "", "targetVector": "", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "", "requiresItem": false, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "", "rewardDelayTime": "", "lasers": [], "targetText": "", "animation": "", "requireWeaponInHand": false, "rewardItemMax": "", "rewardItemMin": "", "rewardItemName": "", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "", "minigameType": "s_m_m_chemsec_01", "type": "guards", "quantity": "", "propHeading": "", "delayRewardCheckbox": false, "penaltyType": "", "showEnemyBlip": false, "createExplosion": false, "rewardMoneyMax": "", "hackType": "s_m_m_chemsec_01", "items": [], "stepName": "guards", "difficulty": "", "guards": [{"weapon": "weapon_carbinerifle", "guardPosition": [261.53, 216.87, 100.68], "heading": 85, "guardModel": "s_m_m_chemsec_01"}, {"weapon": "weapon_microsmg", "guardPosition": [260.29, 213.84, 100.68], "heading": 53, "guardModel": "s_m_m_chemsec_01"}], "rewardMoneyType": "", "lootableItems": [], "rewardMoney": false, "propName": "", "requiredWeapons": [], "requireSimultaneous": false, "penaltyChance": "", "selectedWeapon": "", "pedCoords": "", "unlockTime": "", "rewardItem": false, "xpRewardAmount": "0", "xpRemoveAmount": "0"}, {"stepNumber": 19, "rewardItems": [{"minAmount": 1, "maxAmount": 1, "itemName": "thermite", "rewardChance": 100}], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "", "targetVector": "261.67, 215.65, 101.78", "animation": "10", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "243.00", "requiresItem": false, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": true, "selectedDoorName": "Big Bank Lower Gate 2", "sendAlert": false, "itemName": "", "rewardDelayTime": "", "lasers": [], "rewardItemMax": "1", "targetText": "Place Thermite", "requireSimultaneous": false, "requireWeaponInHand": false, "doorId": "1027", "rewardItemMin": "1", "rewardItemName": "thermite", "dropWalletWithPasscode": false, "xpOptions": false, "rewardMoney": false, "minigameType": "", "hackType": "inspired-roofrunning", "quantity": "", "propHeading": "0.00", "xpRemoveAmount": "0", "stepName": "lower Gate 2", "penaltyType": "", "createExplosion": false, "rewardMoneyMax": "", "guards": [], "type": "hackable_door", "doorPosition": "261.30, 214.51, 101.83", "difficulty": "", "rewardMoneyType": "", "requireBlackout": true, "items": [], "delayRewardCheckbox": false, "blackoutDuration": "1", "unlockTime": "15", "penaltyChance": "100", "requiredWeapons": [], "selectedWeapon": "", "pedCoords": "261.45, 216.04, 100.68", "propName": "", "rewardItem": true, "xpRewardAmount": "0", "lootableItems": []}, {"stepNumber": 20, "rewardItems": [{"minAmount": 20, "maxAmount": 50, "itemName": "goldbar", "rewardChance": 100}, {"minAmount": 1, "maxAmount": 5, "itemName": "goldbarstack", "rewardChance": 100}], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "1", "targetVector": "251.13, 222.82, 100.68", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "251.00", "requiresItem": false, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "", "rewardDelayTime": "1", "lasers": [], "targetText": "Grab Gold", "animation": "ch_prop_gold_trolly_01a", "requireWeaponInHand": false, "rewardItemMax": "50", "rewardItemMin": "20", "rewardItemName": "goldbar", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "type": "robabble_object", "quantity": "", "propHeading": "251.00", "delayRewardCheckbox": false, "guards": [], "hackType": "none", "createExplosion": false, "rewardMoneyMax": "1", "stepName": "gold bar tray 1", "items": [], "penaltyType": "", "difficulty": "", "rewardMoneyType": "cash", "requireBlackout": true, "penaltyChance": "", "rewardMoney": false, "propName": "ch_prop_gold_trolly_01a", "requiredWeapons": [], "requireSimultaneous": false, "lootableItems": [], "selectedWeapon": "", "pedCoords": "251.13, 222.82, 100.68", "unlockTime": "", "rewardItem": true, "xpRewardAmount": "0", "xpRemoveAmount": "0"}, {"stepNumber": 21, "rewardItems": [{"minAmount": 100, "maxAmount": 250, "itemName": "stacksofcash", "rewardChance": 100}], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "1", "targetVector": "254.82, 221.58, 100.68", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "74.00", "requiresItem": false, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "", "rewardDelayTime": "1", "lasers": [], "targetText": "Grab Money", "animation": "ch_prop_cash_low_trolly_01a", "requireWeaponInHand": false, "rewardItemMax": "250", "rewardItemMin": "100", "rewardItemName": "stacksofcash", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "type": "robabble_object", "quantity": "", "propHeading": "74.00", "delayRewardCheckbox": false, "guards": [], "hackType": "none", "createExplosion": false, "rewardMoneyMax": "1", "stepName": "money tray 1", "items": [], "penaltyType": "", "difficulty": "", "rewardMoneyType": "cash", "requireBlackout": true, "penaltyChance": "", "rewardMoney": false, "propName": "ch_prop_cash_low_trolly_01a", "requiredWeapons": [], "requireSimultaneous": false, "lootableItems": [], "selectedWeapon": "", "pedCoords": "254.82, 221.58, 100.68", "unlockTime": "", "rewardItem": true, "xpRewardAmount": "0", "xpRemoveAmount": "0"}, {"stepNumber": 22, "rewardItems": [{"minAmount": 100, "maxAmount": 250, "itemName": "stacksofcash", "rewardChance": 100}], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "1", "targetVector": "259.68, 217.98, 100.68", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "161.00", "requiresItem": false, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "", "rewardDelayTime": "1", "lasers": [], "targetText": "Grab Money", "animation": "ch_prop_cash_low_trolly_01a", "requireWeaponInHand": false, "rewardItemMax": "250", "rewardItemMin": "100", "rewardItemName": "stacksofcash", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "type": "robabble_object", "quantity": "", "propHeading": "161.00", "delayRewardCheckbox": false, "guards": [], "hackType": "none", "createExplosion": false, "rewardMoneyMax": "1", "stepName": "money tray 2", "items": [], "penaltyType": "", "difficulty": "", "rewardMoneyType": "cash", "requireBlackout": true, "penaltyChance": "", "rewardMoney": false, "propName": "ch_prop_cash_low_trolly_01a", "requiredWeapons": [], "requireSimultaneous": false, "lootableItems": [], "selectedWeapon": "", "pedCoords": "259.68, 217.98, 100.68", "unlockTime": "", "rewardItem": true, "xpRewardAmount": "0", "xpRemoveAmount": "0"}, {"stepNumber": 23, "rewardItems": [{"minAmount": 100, "maxAmount": 250, "itemName": "stacksofcash", "rewardChance": 100}], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "1", "targetVector": "258.42, 214.17, 100.68", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "341.00", "requiresItem": false, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "", "rewardDelayTime": "1", "lasers": [], "targetText": "Grab Money", "animation": "ch_prop_cash_low_trolly_01a", "requireWeaponInHand": false, "rewardItemMax": "250", "rewardItemMin": "100", "rewardItemName": "stacksofcash", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "type": "robabble_object", "quantity": "", "propHeading": "341.00", "delayRewardCheckbox": false, "guards": [], "hackType": "none", "createExplosion": false, "rewardMoneyMax": "1", "stepName": "money tray 3", "items": [], "penaltyType": "", "difficulty": "", "rewardMoneyType": "cash", "requireBlackout": true, "penaltyChance": "", "rewardMoney": false, "propName": "ch_prop_cash_low_trolly_01a", "requiredWeapons": [], "requireSimultaneous": false, "lootableItems": [], "selectedWeapon": "", "pedCoords": "258.42, 214.17, 100.68", "unlockTime": "", "rewardItem": true, "xpRewardAmount": "0", "xpRemoveAmount": "0"}, {"stepNumber": 24, "rewardItems": [{"minAmount": 1, "maxAmount": 2, "itemName": "harddrive", "rewardChance": 50}], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "1", "targetVector": "249.41, 219.65, 100.68", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "72.00", "requiresItem": false, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "", "rewardDelayTime": "1", "lasers": [], "targetText": "Grab Hard Drive", "animation": "xm_base_cia_server_01", "requireWeaponInHand": false, "rewardItemMax": "2", "rewardItemMin": "1", "rewardItemName": "harddrive", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "type": "robabble_object", "quantity": "", "propHeading": "72.00", "delayRewardCheckbox": false, "guards": [], "hackType": "none", "createExplosion": false, "rewardMoneyMax": "1", "stepName": "hard drive", "items": [], "penaltyType": "", "difficulty": "", "rewardMoneyType": "cash", "requireBlackout": true, "penaltyChance": "", "rewardMoney": false, "propName": "xm_base_cia_server_01", "requiredWeapons": [], "requireSimultaneous": false, "lootableItems": [], "selectedWeapon": "", "pedCoords": "249.41, 219.65, 100.68", "unlockTime": "", "rewardItem": true, "xpRewardAmount": "0", "xpRemoveAmount": "0"}, {"stepNumber": 25, "rewardItems": [{"minAmount": 1, "maxAmount": 2, "itemName": "paintingclown", "rewardChance": 50}], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "1", "targetVector": "249.29, 221.06, 101.81", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "67.00", "requiresItem": false, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "", "rewardDelayTime": "1", "lasers": [], "targetText": "Cut Painting", "animation": "ch_prop_vault_painting_01c", "requireWeaponInHand": false, "rewardItemMax": "2", "rewardItemMin": "1", "rewardItemName": "paintingclown", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "type": "robabble_object", "quantity": "", "propHeading": "67.00", "delayRewardCheckbox": false, "guards": [], "hackType": "none", "createExplosion": false, "rewardMoneyMax": "1", "stepName": "painting clown", "items": [], "penaltyType": "", "difficulty": "", "rewardMoneyType": "cash", "requireBlackout": true, "penaltyChance": "", "rewardMoney": false, "propName": "ch_prop_vault_painting_01c", "requiredWeapons": [], "requireSimultaneous": false, "lootableItems": [], "selectedWeapon": "", "pedCoords": "249.29, 221.06, 101.81", "unlockTime": "", "rewardItem": true, "xpRewardAmount": "0", "xpRemoveAmount": "0"}, {"stepNumber": 26, "rewardItems": [{"minAmount": 1, "maxAmount": 2, "itemName": "paintingart", "rewardChance": 50}], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "1", "targetVector": "248.39, 218.59, 101.83", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "69.00", "requiresItem": false, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "", "rewardDelayTime": "1", "lasers": [], "targetText": "", "animation": "ch_prop_vault_painting_01g", "requireWeaponInHand": false, "rewardItemMax": "2", "rewardItemMin": "1", "rewardItemName": "paintingart", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "type": "robabble_object", "quantity": "", "propHeading": "69.00", "delayRewardCheckbox": false, "guards": [], "hackType": "none", "createExplosion": false, "rewardMoneyMax": "1", "stepName": "painting art", "items": [], "penaltyType": "", "difficulty": "", "rewardMoneyType": "cash", "requireBlackout": true, "penaltyChance": "", "rewardMoney": false, "propName": "ch_prop_vault_painting_01g", "requiredWeapons": [], "requireSimultaneous": false, "lootableItems": [], "selectedWeapon": "", "pedCoords": "248.39, 218.59, 101.83", "unlockTime": "", "rewardItem": true, "xpRewardAmount": "0", "xpRemoveAmount": "0"}, {"stepNumber": 27, "rewardItems": [{"minAmount": 100, "maxAmount": 250, "itemName": "bandsofnotes", "rewardChance": 100}], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "1", "targetVector": "255.83, 219.06, 100.68", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "338.00", "requiresItem": false, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "", "rewardDelayTime": "1", "lasers": [], "targetText": "Grab Money", "animation": "15", "requireWeaponInHand": false, "rewardItemMax": "250", "rewardItemMin": "100", "rewardItemName": "bandsofnotes", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "type": "robabble_object", "quantity": "", "propHeading": "338.00", "delayRewardCheckbox": false, "guards": [], "hackType": "none", "createExplosion": false, "rewardMoneyMax": "1", "stepName": "cash pile 1", "items": [], "penaltyType": "", "difficulty": "", "rewardMoneyType": "cash", "requireBlackout": true, "penaltyChance": "", "rewardMoney": false, "propName": "bkr_prop_bkr_cashpile_05", "requiredWeapons": [], "requireSimultaneous": false, "lootableItems": [], "selectedWeapon": "", "pedCoords": "255.35, 218.25, 100.68", "unlockTime": "", "rewardItem": true, "xpRewardAmount": "0", "xpRemoveAmount": "0"}, {"stepNumber": 28, "rewardItems": [{"minAmount": 100, "maxAmount": 250, "itemName": "bandsofnotes", "rewardChance": 100}], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "1", "targetVector": "250.11, 216.42, 101.58", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "191.00", "requiresItem": false, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "", "rewardDelayTime": "1", "lasers": [], "targetText": "Grab Money", "animation": "15", "requireWeaponInHand": false, "rewardItemMax": "250", "rewardItemMin": "100", "rewardItemName": "bandsofnotes", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "type": "robabble_object", "quantity": "", "propHeading": "147.00", "delayRewardCheckbox": false, "guards": [], "hackType": "none", "createExplosion": false, "rewardMoneyMax": "1", "stepName": "cash pile 2", "items": [], "penaltyType": "", "difficulty": "", "rewardMoneyType": "cash", "requireBlackout": true, "penaltyChance": "", "rewardMoney": false, "propName": "bkr_prop_bkr_cashpile_05", "requiredWeapons": [], "requireSimultaneous": false, "lootableItems": [], "selectedWeapon": "", "pedCoords": "249.82, 217.23, 100.68", "unlockTime": "", "rewardItem": true, "xpRewardAmount": "0", "xpRemoveAmount": "0"}, {"stepNumber": 29, "rewardItems": [{"minAmount": 1, "maxAmount": 1, "itemName": "paintingpaddle", "rewardChance": 100}], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "1", "targetVector": "255.55, 215.21, 101.81", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "162.00", "requiresItem": false, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "", "rewardDelayTime": "1", "lasers": [], "targetText": "Cut Painting", "requireWeaponInHand": false, "animation": "ch_prop_vault_painting_01a", "rewardItemMin": "1", "rewardItemName": "paintingpaddle", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "rewardItemMax": "1", "quantity": "", "propHeading": "162.00", "delayRewardCheckbox": false, "rewardMoneyMax": "1", "penaltyType": "", "createExplosion": false, "lootableItems": [], "hackType": "none", "type": "robabble_object", "stepName": "painting padel", "difficulty": "", "guards": [], "rewardMoneyType": "cash", "items": [], "rewardMoney": false, "propName": "ch_prop_vault_painting_01a", "requireSimultaneous": false, "requiredWeapons": [], "penaltyChance": "", "selectedWeapon": "", "pedCoords": "255.55, 215.21, 101.81", "unlockTime": "", "rewardItem": true, "xpRewardAmount": "0", "xpRemoveAmount": "0"}, {"stepNumber": 30, "rewardItems": [{"minAmount": 1, "maxAmount": 1, "itemName": "paintingnative", "rewardChance": 100}], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "1", "targetVector": "254.14, 215.72, 101.82", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "159.00", "requiresItem": false, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "", "rewardDelayTime": "1", "lasers": [], "targetText": "Cut Painting", "requireWeaponInHand": false, "animation": "ch_prop_vault_painting_01i", "rewardItemMin": "1", "rewardItemName": "paintingnative", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "rewardItemMax": "1", "quantity": "", "propHeading": "159.00", "delayRewardCheckbox": false, "rewardMoneyMax": "1", "penaltyType": "", "createExplosion": false, "lootableItems": [], "hackType": "none", "type": "robabble_object", "stepName": "painting native", "difficulty": "", "guards": [], "rewardMoneyType": "cash", "items": [], "rewardMoney": false, "propName": "ch_prop_vault_painting_01i", "requireSimultaneous": false, "requiredWeapons": [], "penaltyChance": "", "selectedWeapon": "", "pedCoords": "254.14, 215.72, 101.82", "unlockTime": "", "rewardItem": true, "xpRewardAmount": "0", "xpRemoveAmount": "0"}, {"stepNumber": 31, "rewardItems": [{"minAmount": 1, "maxAmount": 1, "itemName": "paintinglady", "rewardChance": 100}], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "1", "targetVector": "255.18, 219.60, 101.81", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "341.00", "requiresItem": false, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "", "rewardDelayTime": "1", "lasers": [], "targetText": "Cut Painting", "requireWeaponInHand": false, "animation": "ch_prop_vault_painting_01e", "rewardItemMin": "1", "rewardItemName": "paintinglady", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "rewardItemMax": "1", "quantity": "", "propHeading": "341.00", "delayRewardCheckbox": false, "rewardMoneyMax": "1", "penaltyType": "", "createExplosion": false, "lootableItems": [], "hackType": "none", "type": "robabble_object", "stepName": "painting lady", "difficulty": "", "guards": [], "rewardMoneyType": "cash", "items": [], "rewardMoney": false, "propName": "ch_prop_vault_painting_01e", "requireSimultaneous": false, "requiredWeapons": [], "penaltyChance": "", "selectedWeapon": "", "pedCoords": "255.18, 219.60, 101.81", "unlockTime": "", "rewardItem": true, "xpRewardAmount": "0", "xpRemoveAmount": "0"}, {"stepNumber": 32, "rewardItems": [{"minAmount": 1, "maxAmount": 1, "itemName": "<PERSON><PERSON><PERSON>", "rewardChance": 100}], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "1", "targetVector": "256.72, 219.03, 101.80", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "340.00", "requiresItem": false, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "", "rewardDelayTime": "1", "lasers": [], "targetText": "Cut Painting", "requireWeaponInHand": false, "animation": "ch_prop_vault_painting_01h", "rewardItemMin": "1", "rewardItemName": "<PERSON><PERSON><PERSON>", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "rewardItemMax": "1", "quantity": "", "propHeading": "340.00", "delayRewardCheckbox": false, "rewardMoneyMax": "1", "penaltyType": "", "createExplosion": false, "lootableItems": [], "hackType": "none", "type": "robabble_object", "stepName": "painting guys", "difficulty": "", "guards": [], "rewardMoneyType": "cash", "items": [], "rewardMoney": false, "propName": "ch_prop_vault_painting_01h", "requireSimultaneous": false, "requiredWeapons": [], "penaltyChance": "", "selectedWeapon": "", "pedCoords": "256.72, 219.03, 101.80", "unlockTime": "", "rewardItem": true, "xpRewardAmount": "0", "xpRemoveAmount": "0"}, {"stepNumber": 33, "rewardItems": [{"minAmount": 1, "maxAmount": 1, "itemName": "paintingfamily", "rewardChance": 100}], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "1", "targetVector": "252.25, 215.35, 101.81", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "157.00", "requiresItem": false, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "", "rewardDelayTime": "1", "lasers": [], "targetText": "Cut Painting", "requireWeaponInHand": false, "animation": "ch_prop_vault_painting_01j", "rewardItemMin": "1", "rewardItemName": "paintingfamily", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "rewardItemMax": "1", "quantity": "", "propHeading": "157.00", "delayRewardCheckbox": false, "rewardMoneyMax": "1", "penaltyType": "", "createExplosion": false, "lootableItems": [], "hackType": "none", "type": "robabble_object", "stepName": "painting family", "difficulty": "", "guards": [], "rewardMoneyType": "cash", "items": [], "rewardMoney": false, "propName": "ch_prop_vault_painting_01j", "requireSimultaneous": false, "requiredWeapons": [], "penaltyChance": "", "selectedWeapon": "", "pedCoords": "252.25, 215.35, 101.81", "unlockTime": "", "rewardItem": true, "xpRewardAmount": "0", "xpRemoveAmount": "0"}, {"stepNumber": 34, "rewardItems": [{"minAmount": 1, "maxAmount": 1, "itemName": "paintingcity", "rewardChance": 100}], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "1", "targetVector": "249.01, 216.53, 101.81", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "160.00", "requiresItem": false, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "", "rewardDelayTime": "1", "lasers": [], "targetText": "Cut Painting", "requireWeaponInHand": false, "animation": "ch_prop_vault_painting_01f", "rewardItemMin": "1", "rewardItemName": "paintingcity", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "rewardItemMax": "1", "quantity": "", "propHeading": "160.00", "delayRewardCheckbox": false, "rewardMoneyMax": "1", "penaltyType": "", "createExplosion": false, "lootableItems": [], "hackType": "none", "type": "robabble_object", "stepName": "painting city", "difficulty": "", "guards": [], "rewardMoneyType": "cash", "items": [], "rewardMoney": false, "propName": "ch_prop_vault_painting_01f", "requireSimultaneous": false, "requiredWeapons": [], "penaltyChance": "", "selectedWeapon": "", "pedCoords": "249.01, 216.53, 101.81", "unlockTime": "", "rewardItem": true, "xpRewardAmount": "0", "xpRemoveAmount": "0"}, {"stepNumber": 35, "rewardItems": [{"minAmount": 200, "maxAmount": 300, "itemName": "stacksofcash", "rewardChance": 100}], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "1", "targetVector": "264.23, 213.68, 101.53", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "251.00", "requiresItem": false, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "", "rewardDelayTime": "1", "lasers": [], "targetText": "Grab Money", "animation": "h4_prop_h4_cash_stack_01a", "requireWeaponInHand": false, "rewardItemMax": "300", "rewardItemMin": "200", "rewardItemName": "stacksofcash", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "type": "robabble_object", "quantity": "", "propHeading": "251.00", "delayRewardCheckbox": false, "guards": [], "hackType": "none", "createExplosion": false, "rewardMoneyMax": "1", "stepName": "Money Table", "items": [], "penaltyType": "", "difficulty": "", "rewardMoneyType": "cash", "requireBlackout": true, "penaltyChance": "", "rewardMoney": false, "propName": "h4_prop_h4_cash_stack_01a", "requiredWeapons": [], "requireSimultaneous": false, "lootableItems": [], "selectedWeapon": "", "pedCoords": "264.23, 213.68, 101.53", "unlockTime": "", "rewardItem": true, "xpRewardAmount": "0", "xpRemoveAmount": "0"}, {"stepNumber": 36, "rewardItems": [{"minAmount": 1, "maxAmount": 3, "itemName": "goldbar", "rewardChance": 85}, {"minAmount": 5, "maxAmount": 15, "itemName": "goldwatch", "rewardChance": 85}, {"minAmount": 10, "maxAmount": 25, "itemName": "goldbracelet", "rewardChance": 85}, {"minAmount": 4, "maxAmount": 15, "itemName": "silverring", "rewardChance": 85}, {"minAmount": 1, "maxAmount": 5, "itemName": "silverbar", "rewardChance": 85}], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "2000", "targetVector": "264.80, 216.45, 101.69", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "333.00", "requiresItem": true, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "drill", "rewardDelayTime": "1", "lasers": [], "targetText": "Drill", "animation": "12", "requireWeaponInHand": false, "rewardItemMax": "3", "rewardItemMin": "1", "rewardItemName": "goldbar", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "type": "robabble_object", "quantity": "1", "propHeading": "0.00", "delayRewardCheckbox": false, "guards": [], "hackType": "drilllaser", "createExplosion": false, "rewardMoneyMax": "5000", "stepName": "drill 1", "items": [{"itemName": "drill", "loseChance": 10, "itemQuantity": 1}], "penaltyType": "", "difficulty": "", "rewardMoneyType": "cash", "requireBlackout": true, "penaltyChance": "", "rewardMoney": true, "propName": "", "requiredWeapons": [], "requireSimultaneous": false, "lootableItems": [], "selectedWeapon": "", "pedCoords": "264.51, 215.73, 100.68", "unlockTime": "", "rewardItem": true, "xpRewardAmount": "0", "xpRemoveAmount": "0"}, {"stepNumber": 37, "rewardItems": [{"minAmount": 1, "maxAmount": 3, "itemName": "goldbar", "rewardChance": 85}, {"minAmount": 5, "maxAmount": 15, "itemName": "goldwatch", "rewardChance": 85}, {"minAmount": 10, "maxAmount": 25, "itemName": "goldbracelet", "rewardChance": 85}, {"minAmount": 4, "maxAmount": 15, "itemName": "silverring", "rewardChance": 85}, {"minAmount": 1, "maxAmount": 5, "itemName": "silverbar", "rewardChance": 85}], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "2000", "targetVector": "266.27, 213.17, 101.60", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "264.00", "requiresItem": true, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "drill", "rewardDelayTime": "1", "lasers": [], "targetText": "Drill", "animation": "12", "requireWeaponInHand": false, "rewardItemMax": "3", "rewardItemMin": "1", "rewardItemName": "goldbar", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "type": "robabble_object", "quantity": "1", "propHeading": "0.00", "delayRewardCheckbox": false, "guards": [], "hackType": "none", "createExplosion": false, "rewardMoneyMax": "5000", "stepName": "drill 2", "items": [{"itemName": "drill", "loseChance": 10, "itemQuantity": 1}], "penaltyType": "", "difficulty": "", "rewardMoneyType": "cash", "requireBlackout": true, "penaltyChance": "", "rewardMoney": true, "propName": "", "requiredWeapons": [], "requireSimultaneous": false, "lootableItems": [], "selectedWeapon": "", "pedCoords": "265.56, 213.46, 100.68", "unlockTime": "", "rewardItem": true, "xpRewardAmount": "0", "xpRemoveAmount": "0"}, {"stepNumber": 38, "rewardItems": [{"minAmount": 1, "maxAmount": 3, "itemName": "goldbar", "rewardChance": 85}, {"minAmount": 5, "maxAmount": 15, "itemName": "goldwatch", "rewardChance": 85}, {"minAmount": 10, "maxAmount": 25, "itemName": "goldbracelet", "rewardChance": 85}, {"minAmount": 4, "maxAmount": 15, "itemName": "silverring", "rewardChance": 85}, {"minAmount": 1, "maxAmount": 5, "itemName": "silverbar", "rewardChance": 85}], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "2000", "targetVector": "263.18, 212.05, 101.67", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "165.00", "requiresItem": true, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "drill", "rewardDelayTime": "1", "lasers": [], "targetText": "", "animation": "12", "requireWeaponInHand": false, "rewardItemMax": "3", "rewardItemMin": "1", "rewardItemName": "goldbar", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "type": "robabble_object", "quantity": "1", "propHeading": "0.00", "delayRewardCheckbox": false, "guards": [], "hackType": "none", "createExplosion": false, "rewardMoneyMax": "5000", "stepName": "drill 3", "items": [{"itemName": "drill", "loseChance": 10, "itemQuantity": 1}], "penaltyType": "", "difficulty": "", "rewardMoneyType": "cash", "requireBlackout": true, "penaltyChance": "", "rewardMoney": true, "propName": "", "requiredWeapons": [], "requireSimultaneous": false, "lootableItems": [], "selectedWeapon": "", "pedCoords": "263.57, 212.73, 100.68", "unlockTime": "", "rewardItem": true, "xpRewardAmount": "0", "xpRemoveAmount": "0"}, {"stepNumber": 39, "rewardItems": [], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "1", "targetVector": "266.34, 214.85, 100.68", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "252.00", "requiresItem": false, "isRequiredNextStage": false, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "", "rewardDelayTime": "1", "lasers": [], "targetText": "", "animation": "h4_prop_h4_safe_01a", "requireWeaponInHand": false, "rewardItemMax": "", "rewardItemMin": "", "rewardItemName": "", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "type": "robabble_object", "quantity": "", "propHeading": "252.00", "delayRewardCheckbox": false, "guards": [], "hackType": "safe-cracker", "createExplosion": false, "rewardMoneyMax": "1", "stepName": "safe", "items": [], "penaltyType": "", "difficulty": "", "rewardMoneyType": "cash", "requireBlackout": true, "penaltyChance": "", "rewardMoney": false, "propName": "h4_prop_h4_safe_01a", "requiredWeapons": [], "requireSimultaneous": false, "lootableItems": [], "selectedWeapon": "", "pedCoords": "266.34, 214.85, 100.68", "unlockTime": "", "rewardItem": false, "xpRewardAmount": "0", "xpRemoveAmount": "0"}, {"stepNumber": 40, "rewardItems": [{"minAmount": 100, "maxAmount": 199, "itemName": "stacksofcash", "rewardChance": 100}], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "1", "targetVector": "266.31, 214.85, 101.71", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "248.00", "requiresItem": false, "isRequiredNextStage": true, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "", "rewardDelayTime": "1", "lasers": [], "targetText": "", "requireWeaponInHand": false, "animation": "h4_prop_h4_cash_stack_01a", "rewardItemMin": "100", "rewardItemName": "stacksofcash", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "rewardItemMax": "199", "quantity": "", "propHeading": "248.00", "delayRewardCheckbox": false, "rewardMoneyMax": "1", "penaltyType": "", "createExplosion": false, "lootableItems": [], "hackType": "none", "type": "robabble_object", "stepName": "safe cash", "difficulty": "", "guards": [], "rewardMoneyType": "cash", "items": [], "rewardMoney": false, "propName": "h4_prop_h4_cash_stack_01a", "requireSimultaneous": false, "requiredWeapons": [], "penaltyChance": "", "selectedWeapon": "", "pedCoords": "266.31, 214.85, 101.71", "unlockTime": "", "rewardItem": true, "xpRewardAmount": "0", "xpRemoveAmount": "0"}, {"stepNumber": 41, "rewardItems": [{"minAmount": 1, "maxAmount": 1, "itemName": "WEAPON_MICROSMG", "rewardChance": 100}], "isOptional": false, "triggersBlackout": false, "removeXpOnHit": false, "rewardMoneyMin": "1", "targetVector": "266.55, 214.75, 101.19", "selectedDoorName": "", "penaltyForFailing": false, "xpNeededAmount": "0", "heading": "241.00", "requiresItem": false, "isRequiredNextStage": true, "rewardItemQuantity": "", "minigameCheckbox": false, "unlockTimeCheckbox": false, "sendAlert": false, "itemName": "", "rewardDelayTime": "1", "lasers": [], "targetText": "", "requireWeaponInHand": false, "animation": "16", "rewardItemMin": "1", "rewardItemName": "WEAPON_MICROSMG", "dropWalletWithPasscode": false, "xpOptions": false, "blackoutDuration": "1", "minigameType": "", "rewardItemMax": "1", "quantity": "", "propHeading": "68.00", "delayRewardCheckbox": false, "rewardMoneyMax": "1", "penaltyType": "", "createExplosion": false, "lootableItems": [], "hackType": "none", "type": "robabble_object", "stepName": "uzi safe", "difficulty": "", "guards": [], "rewardMoneyType": "cash", "items": [], "rewardMoney": false, "propName": "w_sb_microsmg", "requireSimultaneous": false, "requiredWeapons": [], "penaltyChance": "", "selectedWeapon": "", "pedCoords": "265.55, 215.12, 100.68", "unlockTime": "", "rewardItem": true, "xpRewardAmount": "0", "xpRemoveAmount": "0"}], "settings": {"policeJobs": ["police", "leo", "lspd", "sasp", "ranger", "bcso", "trooper"], "blipSprite": 500, "alertPriority": 1, "alertSound1": "robberysound", "minutesUntilRobbery": "", "robberyCooldown": "90", "policeAlertHeader": "PACIFIC BANK ROBBERY", "alertCode": "10-90", "blipColor": 2, "cooldownHeists": ["store robbery vanila unicorn", "store above big bank", "store near casino", "store burgershot", "store mirror park", "bank fleeca legion", "bank fleeca above pillbox", "bank fleeca ls customs", "bank fleeca life invader", "bank fleeca ocean", "store south side", "bank fleeca harmony", "vangelico jewelry store", "bank paleto", "humane labs"], "policeAlert": "PACIFIC BANK ROBBERY", "blipFlash": false, "alertIcon": "fas fa-bell", "blipScale": 1, "policeRequired": "", "cooldownMinutes": 60}}]