Config = {}

Config.Framework = 'standalone' -- qb-core or qbcore or standalone
Config.UsePed = true
Config.UseTarget = false
Config.UnLoadEventName = ''
Config.Menu = 'ludo-menu' -- or qb-menu

--Commands
Config.GiftCommand = 'Gift'
Config.CloseGameCommand = 'ff'
Config.GiftCommandDealy = 30000
Config.GameData = {
    Ped = 'a_m_m_soucent_03',
    Coords = vector4(-1759.52, 185.91, 64.38, 299.68)
}

if not IsDuplicityVersion() then
    RegisterNetEvent("3dme:ludo", function(text, source)
        local ped = PlayerPedId()
        local sec = 5000
        local Coords = GetEntityCoords(GetPlayerPed(GetPlayerFromServerId(source)))
        if #(GetEntityCoords(ped) - vector3(Coords.x, Coords.y, Coords.z)) < 30.0 then 
            CreateThread(function()
                while true do
                    Wait(0) 
                    sec -= 10
                    local Coords = GetEntityCoords(GetPlayerPed(GetPlayerFromServerId(source)))
                    if #(GetEntityCoords(ped) - vector3(Coords.x, Coords.y, Coords.z)) < 30.0 then 
                        if sec > 100 then 
                            DrawText3D(Coords.x, Coords.y, Coords.z, text)
                        end
                    end
                end
            end)
        end
    end)
    
    Config.IsDead = function()
        if Config.Framework == 'qbcore' or Config.Framework == 'qb-core' then
            return IsEntityDead(PlayerPedId())
        else
            return IsEntityDead(PlayerPedId())
        end
    end
    Config.Revive = function()
        if Config.Framework == 'qbcore' or Config.Framework == 'qb-core' then
            TriggerEvent('hospital:client:Revive')
        else
            SetEntityHealth(PlayerPedId(), 200)
        end
    end
    if Config.UseTarget then 
        Config.AddTarget = function(id, pos, options)
            return exports["ox_target"]:addBoxZone({
                coords = vec3(pos.x, pos.y, pos.z),
                size = vec3(0.5, 0.5, 2),
                rotation = 45,
                debug = false,
                options = options
            })
        end
    end
    Config.PedSpawner = function()
        if Config.UsePed then 
        
            local pedModel = GetHashKey(Config.GameData.Ped)
            local pedCoords = Config.GameData.Coords
    
            RequestModel(pedModel)
            while not HasModelLoaded(pedModel) do
                Wait(1)
            end
            local pedId = CreatePed(4, pedModel, pedCoords.x, pedCoords.y, pedCoords.z - 1, pedCoords.w, false, true)
            SetEntityAsMissionEntity(pedId, true, true)
            SetBlockingOfNonTemporaryEvents(pedId, true)
            SetPedFleeAttributes(pedId, 0, 0)
            SetPedCombatAttributes(pedId, 17, true)
            SetPedSeeingRange(pedId, 0.0)
            SetPedHearingRange(pedId, 0.0)
            SetPedAlertness(pedId, 0)
            SetPedKeepTask(pedId, true)
            SetPedDropsWeaponsWhenDead(pedId, false)
            SetPedDiesWhenInjured(pedId, false)
            SetEntityInvincible(pedId, true)
            FreezeEntityPosition(pedId, true)
    
            if Config.UseTarget then 
                Config.AddTarget("pedId", pedCoords, {
                    {
                        label = 'See Games',
                        type = 'client',
                        distance = 2,
                        onSelect = function(entity)   
                            TriggerEvent('ludo:client:menu')
                        end,
                    },
                })
            else
                CreateThread(function()
                   while true do 
                    Wait(0)
                    if #(GetEntityCoords(PlayerPedId()) - vector3(pedCoords.x, pedCoords.y, pedCoords.z)) < 1.5 then 
                        DrawText3D(pedCoords.x, pedCoords.y, pedCoords.z, 'See Games [E]')
                        if IsControlJustPressed(0, 38)  then
                            TriggerEvent('ludo:client:menu')
                        end
                    end
                   end
                end)
            end
        else

        end
    end
end

Config.GiftSpot = {
    [1] = vector3(-1746.97, 171.15, 64.41),
    [2] = vector3(-1754.38, 171.3, 64.41),
    [3] = vector3(-1749.84, 179.95, 64.41),
    [4] = vector3(-1743.26, 175.7, 64.41),
}

Config.KeyRandom = {
    {
        type = 'red',
        name = '4 Steps Back'
    },
    {
        type = 'White',
        name = 'Double Move'
    },
    {
        type = 'red',
        name = 'Switch Places'
    },
    {
        type = 'White',
        name = 'Team Revive'
    },
    {
        type = 'red',
        name = 'Stop 1 Round'
    },
    {
        type = 'White',
        name = 'Shielded'
    },
    {
        type = 'red',
        name = 'Don\'t Cry'
    },
    {
        type = 'White',
        name = 'Knock Knock'
    },
    {
        type = 'red',
        name = 'Unshielded'
    },
    {
        type = 'White',
        name = 'Together'
    },
}

Config.CountToEnd = 0

Config.Teams = {
    anyGameStart = false,
    CountPlayers = 0,
    MaxPlayers = 16,
    ['blue'] = {
        isJoin = false,
        gCountPlayers = 0,
        gMaxPlayers = 5,
        Players = {},
        Spawns = {
            [1] = vector4(-1751.12, 165.68, 64.51, 36.98),
            [2] = vector4(-1748.61, 167.4, 64.51, 38.43),
            [3] = vector4(-1747.06, 164.88, 64.51, 30.54),
            [4] = vector4(-1749.31, 163.17, 64.51, 45.45),
        },
    },
    ['yellow'] = {
        isJoin = false,
        gCountPlayers = 0,
        gMaxPlayers = 5,
        Players = {},
        Spawns = {
            [1] = vector4(-1740.51, 173.06, 64.51, 19.66),
            [2] = vector4(-1738.11, 174.54, 64.51, 349.15),
            [3] = vector4(-1736.3, 172.17, 64.51, 27.55),
            [4] = vector4(-1738.87, 170.56, 64.51, 116.24),
        },
    },
    ['green'] = {
        isJoin = false,
        gCountPlayers = 0,
        gMaxPlayers = 5,
        Players = {},
        Spawns = {
            [1] = vector4(-1745.67, 185.55, 64.51, 214.2),
            [2] = vector4(-1748.05, 183.86, 64.51, 242.15),
            [3] = vector4(-1746.26, 181.3, 64.51, 215.3),
            [4] = vector4(-1743.78, 182.95, 64.51, 321.71),
        },
    },
    ['red'] = {
        isJoin = false,
        gCountPlayers = 0,
        gMaxPlayers = 5,
        Players = {},
        Spawns = {
            [1] = vector4(-1754.42, 175.54, 64.51, 126.72),
            [2] = vector4(-1756.78, 173.88, 64.51, 115.09),
            [3] = vector4(-1758.53, 176.16, 64.51, 301.74),
            [4] = vector4(-1756.32, 177.97, 64.51, 282.6),
        },
    },
}


DrawText3D = function(x, y, z, text)
    local onScreen,_x,_y=World3dToScreen2d(x,y,z)
    local px,py,pz=table.unpack(GetGameplayCamCoords())
    local dist = GetDistanceBetweenCoords(px,py,pz, x,y,z, 1)
 
    local scale = (1/dist)*2
    local fov = (1/GetGameplayCamFov())*100
    local scale = scale*fov
   
    if onScreen then
        SetTextScale(0.0*scale, 0.55*scale)
        SetTextFont(0)
        SetTextProportional(1)
        -- SetTextScale(0.0, 0.55)
        SetTextColour(255, 255, 255, 255)
        SetTextDropshadow(0, 0, 0, 0, 255)
        SetTextEdge(2, 0, 0, 0, 150)
        SetTextDropShadow()
        SetTextOutline()
        SetTextEntry("STRING")
        SetTextCentre(1)
        AddTextComponentString(text)
        DrawText(_x,_y)
    end
end