-- Registers the admin menu context for managing mechanic shops.
lib.registerContext({
    id = "admin_mechanic_menu",
    title = locale("menu_title.admin_menu"),
    options = {
        -- view shops:
        {
            title = locale("menu_title.view_shops"),
            icon = "list",
            onSelect = function()
                ViewShops("admin_mechanic_menu")
            end
        },
        {
            title = locale("menu_title.create_shop"),
            icon = "plus",
            onSelect = function()
                CreateShop("admin_mechanic_menu")
            end
        }
    },
})

-- Enable or disable mechanic admin menu
RegisterCommand(Config.Shop.Admin.menu.command.str, function()
    -- Check if feature is enabled
    if not Config.Shop.Admin.menu.enable then return end

    -- Check if player is admin
    if not _API.Player.isAdmin then
        print("Only admins can use this command!")
        return
    end

    -- Show admin menu
    lib.showContext("admin_mechanic_menu")
end, false)

-- Only register the keybind if enabled in config
if Config.Shop.Admin.menu.keybind.enable then
    RegisterKeyMapping(Config.Shop.Admin.menu.command.str, Config.Shop.Admin.menu.keybind.description, "keyboard", Config.Shop.Admin.menu.keybind.default)
end

-- Enable / disable command to open shop creator interface:
if Config.Shop.Creation.command.enable then
    RegisterCommand(Config.Shop.Creation.command.str, function(source, args, rawCommand)
        if _API.Player.isAdmin then
            CreateShop()
        else
            print("Only admins can use this command!")
        end
    end, false)
end

-- Enable / disable command to open shop management interface:
if Config.Shop.Management.command.enable then
    RegisterCommand(Config.Shop.Management.command.str, function(source, args, rawCommand)
        if _API.Player.isAdmin then
            ViewShops()
        else
            print("Only admins can use this command!")
        end
    end, false)
end

--- Function to create a mechanic shop:
--- @param returnMenu string (optional) provide ox_lib context menuId to return
function CreateShop(returnMenu)
    if not _API.Player.isAdmin then 
        return print("Only admins can create shop")
    end

    local ped = PlayerPedId()
    local pedCoords = GetEntityCoords(ped)
    local blipCoords = vector3(pedCoords.x, pedCoords.y, pedCoords.z)

    -- Function to validate and sanitize job name
    --- @param input string entered job name
    local function SanitizeJobName(input)
        -- Convert to lowercase
        input = input:lower()
        -- Remove any characters that are NOT letters or numbers (removes spaces, special characters, and underscores)
        input = input:gsub("[^a-z0-9]", "")
        return input
    end

    -- Define input fields for shop creation
    local inputFields = {
        {type = "input", label = locale("input_label.shop_name"), required = true, description = locale("input_description.shop_name")},
        {type = "input", label = locale("input_label.job_name"), required = true, description = locale("input_description.job_name")},
        {type = "input", label = locale("input_label.job_label"), required = true, description = locale("input_description.job_label")},
        {type = "number", label = locale("input_label.account_start_balance"), required = true, default = 0, min = 0, description = locale("input_description.account_start_balance")},
    }

    -- blip enable?
    table.insert(inputFields, {type = "checkbox", label = locale("input_label.create_blip"), checked = true})

    -- blip input?
    if Config.Shop.Creation.blip.input then
        table.insert(inputFields, {type = "number", label = locale("input_label.blip_sprite"), required = true, default = Config.Shop.Creation.blip.sprite, min = 0, max = 826, description = locale("input_description.blip_sprite")})
        table.insert(inputFields, {type = "number", label = locale("input_label.blip_color"), required = true, default = Config.Shop.Creation.blip.color, min = 0, max = 85, description = locale("input_description.blip_color")})
    end


    -- if shop sale is enabled
    if Config.Shop.Sale then 
        table.insert(inputFields, {type = "number", label = locale("input_label.sale_price"), required = true, default = 0, min = 0, description = locale("input_description.sale_price")})
        table.insert(inputFields, {type = "checkbox", label = locale("input_label.for_sale"), checked = true})
    end

    -- Open input dialog
    local input = lib.inputDialog(locale("input_title.shop_creation"), inputFields)
    -- If canceled, exit function
    if not input then
        if returnMenu then 
            return lib.showContext(returnMenu)
        else
            return 
        end
    end

    local sprite = Config.Shop.Creation.blip.input and input[6] or Config.Shop.Creation.blip.sprite
    local color = Config.Shop.Creation.blip.input and input[7] or Config.Shop.Creation.blip.color

    local sale_price = Config.Shop.Sale and input[8] or nil
    local for_sale = Config.Shop.Sale and input[9] or false

    -- Construct shop data payload
    local shop = {
        name = input[1],
        job = {
            name = SanitizeJobName(input[2]),
            label = input[3]
        },
        account = input[4],
        sale_price = sale_price,
        for_sale = for_sale,
        blip = {
            enable = input[5],
            coords = blipCoords,
            sprite = sprite,
            color = color,
            scale = Config.Shop.Creation.blip.scale,
            display = Config.Shop.Creation.blip.display
        },
    }

    -- Trigger server event to create shop
    TriggerServerEvent("t1ger_mechanic:server:createShop", shop)
end
exports("CreateShop", CreateShop)

-- Function to register context for viewing shops
function RegisterViewShopsContext(returnMenu)
    -- Ensure Shops exists and is valid
    if not Shops or not next(Shops) then return end

    local menuOptions = {}

    for id, shop in pairs(Shops) do
        local ownerName = nil

        -- Find the owners character name
        if shop.employees then
            for _, v in pairs(shop.employees) do
                if v.identifier == shop.owner then
                    ownerName = v.name
                    break -- Stop searching once owner is found
                end
            end
        end

        -- Ensure shop.job exists before accessing label and name
        local jobLabel = shop.job and shop.job.label or locale("menu_metadata.not_answered")
        local jobName = shop.job and shop.job.name or locale("menu_metadata.not_answered")

        local metadata = {
            {label = locale("menu_metadata.id"), value = id},
            {label = locale("menu_metadata.job"), value = ("%s [%s]"):format(jobLabel, jobName)},
        }

        if ownerName then
            metadata[#metadata+1] = {label = locale("menu_metadata.owner"), value = ownerName}
            metadata[#metadata+1] = {label = locale("menu_metadata.owner_id"), value = shop.owner or locale("menu_metadata.not_answered")}
        end

        -- Insert shop option into menu
        table.insert(menuOptions, {
            title = shop.name,
            icon = "people-group",
            args = shop.id,
            metadata = metadata,
            onSelect = function(args)
                ManageShop(id, returnMenu)
            end
        })
    end

    -- Prepare context data
    local context = {
        id = "view_mechanic_shops",
        title = locale("menu_title.view_shops"),
        options = menuOptions
    }

    if returnMenu then
        context.menu = returnMenu
    end

    -- Register the menu once
    lib.registerContext(context)
end

--- Function to display the list of mechanic shops in a menu.
--- @param returnMenu (string) Menu ID to return to if entered from a menu.
function ViewShops(returnMenu)
    if type(Shops) == 'table' and next(Shops) then 
        RegisterViewShopsContext(returnMenu)
        lib.showContext("view_mechanic_shops")
    else
        _API.ShowNotification(locale("notification.no_mechanic_shops"), "inform", {})
        if returnMenu then
            lib.showContext(returnMenu)
        end
    end
end
exports("ViewShops", ViewShops)

-- Function to register context for manage shop:
function RegisterManageShopContext(shopId, returnMenu)
    if not Shops[shopId] or not next(Shops[shopId]) then
        print("[RegisterManageShopContext] Could not find shop data for shop id: ", shopId)
        return lib.showContext("view_mechanic_shops")
    end

    -- get shop account
    local shopAccount = lib.callback.await("t1ger_mechanic:server:getShopAccount", false, shopId)

    -- menu options
    local menuOptions = {
        -- set owner:
        {
            title = locale("menu_title.set_owner"),
            description = locale("menu_description.set_owner"),
            icon = "crown",
            onSelect = function()
                SetOwner(shopId, returnMenu)
            end
        },
        -- set account:
        {
            title = locale("menu_title.set_account_bal"),
            description = locale("menu_description.set_account_bal"):format(math.groupdigits(shopAccount)),
            icon = "dollar",
            onSelect = function()
                SetAccount(shopId, shopAccount, returnMenu)
            end
        },
        -- marker management:
        {
            title = locale("menu_title.marker_management"),
            description = locale("menu_description.marker_management"),
            icon = "arrow-pointer",
            arrow = true,
            onSelect = function()
                MarkerManagement(shopId, returnMenu)
            end
        },
        -- delete shop:
        {
            title = locale("menu_title.delete_shop"),
            description = locale("menu_description.delete_shop"),
            icon = "trash",
            onSelect = function()
                DeleteShop(shopId, "manage_mechanic_shop")
            end
        },
    }

    if Config.Shop.Sale then
        local optionTitle, optionDescription, canCancel = locale("menu_metadata.not_answered"), locale("menu_metadata.not_answered"), false
        if Shops[shopId].for_sale and type(Shops[shopId].sale_price) == "number" then
            optionTitle = locale("menu_title.cancel_sale")
            optionDescription = string.format(locale("menu_description.cancel_sale"), Config.Currency..tostring(math.groupdigits(Shops[shopId].sale_price)))
            canCancel = true
        else
            optionTitle = locale("menu_title.list_for_sale")
            optionDescription = locale("menu_description.list_for_sale")
        end
        menuOptions[#menuOptions+1] = {
            title = optionTitle,
            icon = "tags",
            description = optionDescription,
            onSelect = function()
                if canCancel then
                    TriggerServerEvent("t1ger_mechanic:server:cancelShopSale", shopId)
                    Shops[shopId].for_sale = false
                    Shops[shopId].sale_price = nil
                    Wait(100)
                    RegisterManageShopContext(shopId, returnMenu)
                    return lib.showContext("manage_mechanic_shop")
                else
                    -- Show input dialog for setting shop account balance
                    local input = lib.inputDialog(locale("input_title.list_for_sale"), {
                        {type = "number", label = locale("input_label.sale_price"), icon = "money-bill", min = 0, default = 0, required = true, placeholder = 100, description = locale("input_description.sale_price")}
                    })

                    -- If the user closes or cancels the dialog
                    if not input then
                        return lib.showContext("manage_mechanic_shop")
                    end

                    -- Ensure the entered amount is a valid number and greater than 0
                    local amount = tonumber(input[1])
                    if not amount or amount < 0 then
                        _API.ShowNotification(locale("notification.min_sale_price"), "inform", {})
                        return lib.showContext("manage_mechanic_shop")
                    end

                    TriggerServerEvent("t1ger_mechanic:server:listShopForSale", shopId, amount)

                    Shops[shopId].for_sale = true
                    Shops[shopId].sale_price = amount
                    Wait(100)
                    RegisterManageShopContext(shopId, returnMenu)
                    return lib.showContext("manage_mechanic_shop")
                end
            end
        }
    end

    local context = {
        id = "manage_mechanic_shop",
        title = locale("menu_title.manage_shop"):format(Shops[shopId].name, Shops[shopId].id),
        menu = "view_mechanic_shops",
        onBack = function()
            RegisterViewShopsContext(returnMenu)
        end,
        options = menuOptions
    }

    --- Registers the admin menu context for managing mechanic shops.
    lib.registerContext(context)
end

-- function to manage a shop
function ManageShop(shopId, returnMenu)
    RegisterManageShopContext(shopId, returnMenu)
    lib.showContext("manage_mechanic_shop")
end

--- Function to set or manage a shop owner.
--- @param shopId (number) The ID of the shop.
--- @param returnMenu (string) The menu to return to after selection.
function SetOwner(shopId, returnMenu)
    local players = {}

    -- If the shop already has a owner, allow removal
    local ownerId = Shops[shopId].owner
    if ownerId then
        local ownerName = locale("menu_metadata.not_answered")

        -- Find the owner character name (optimized loop)
        if Shops[shopId].employees then
            for _, v in pairs(Shops[shopId].employees) do
                if v.identifier == ownerId then
                    ownerName = v.name
                    break -- Stop loop early once the owner is found
                end
            end
        end

        -- Add option to remove the current owner
        players[1] = {
            title = locale("menu_title.remove_current_owner"),
            icon = "trash",
            description = locale("menu_description.remove_current_owner"),
            metadata = {
                {label = locale("menu_metadata.owner"), value = ownerName},
                {label = locale("menu_metadata.owner_id"), value = ownerId},
            },
            onSelect = function()
                TriggerServerEvent("t1ger_mechanic:server:setOwner", shopId, ownerId, true)
                Wait(100)
                RegisterManageShopContext(shopId, returnMenu)
                return lib.showContext("manage_mechanic_shop")
            end
        }
    end

    -- Fetch online players
    local results = lib.callback.await("t1ger_mechanic:server:getOnlinePlayers", false)
    if results and next(results) then
        for _, player in pairs(results) do
            table.insert(players, {
                title = player.name,
                icon = "user",
                metadata = {
                    {label = locale("menu_metadata.player_id"), value = player.source},
                    {label = locale("menu_metadata.player_name"), value = player.name},
                },
                onSelect = function()
                    TriggerServerEvent("t1ger_mechanic:server:setOwner", shopId, player)
                    Wait(100)
                    RegisterManageShopContext(shopId, returnMenu)
                    return lib.showContext("manage_mechanic_shop")
                end
            })
        end
    end

    if #players <= 0 then
        _API.ShowNotification(locale("notification.no_online_players"), "inform", {})
        return lib.showContext("manage_mechanic_shop")
    end

    -- Register and show the context menu for setting the owner
    lib.registerContext({
        id = "set_shop_owner",
        title = locale("menu_title.set_owner"),
        menu = "manage_mechanic_shop",
        onBack = function()
            RegisterManageShopContext(shopId, returnMenu)
        end,
        options = players
    })
    lib.showContext("set_shop_owner")
end

--- Function to set the mechanic shop"s account balance.
--- @param shopId number The ID of the shop.
--- @param shopAccount number the current shop account balance
--- @param returnMenu string The menu to return to after setting the balance.
function SetAccount(shopId, shopAccount, returnMenu)
    -- Show input dialog for setting shop account balance
    local input = lib.inputDialog(locale("input_title.set_account_balance"), {
        {type = "input", label = locale("input_label.account_current_balance"), icon = "sack-dollar", disabled = true, default = tostring(math.groupdigits(shopAccount))},
        {type = "number", label = locale("input_label.account_enter_amount"), icon = "money-bill-trend-up", min = 1, required = true, placeholder = 100, description = locale("input_description.account_enter_amount")}
    })

    -- If the user closes or cancels the dialog
    if not input then
        return lib.showContext("manage_mechanic_shop")
    end

    -- Ensure the entered amount is a valid number and greater than 0
    local amount = tonumber(input[2])
    if not amount or amount <= 0 then
        _API.ShowNotification(locale("notification.input_amount_higher_0"), "success", {})
        return lib.showContext("manage_mechanic_shop")
    end

    -- Send the updated balance to the server
    TriggerServerEvent("t1ger_mechanic:server:setAccountMoney", shopId, amount)
    RegisterManageShopContext(shopId, returnMenu)
    return lib.showContext("manage_mechanic_shop")
end

--- Function to manage markers for a specific mechanic shop.
--- @param shopId number The ID of the shop for which markers are being managed.
--- @param returnMenu string The menu to return to after managing markers (optional).
function MarkerManagement(shopId, returnMenu)
    -- Define menu options for marker management
    local menuOptions = {
        -- Create new marker option
        {
            title = locale("menu_title.create_marker"),
            icon = "plus",
            description = locale("menu_description.create_marker"),
            onSelect = function()
                CreateMarker(shopId, returnMenu)
            end
        }
    }

    -- Check if the shop has existing markers
    if Shops[shopId].markers and next(Shops[shopId].markers) then
        local hasMarkers = false

        for class, _ in pairs(Config.Shop.Markers) do
            if Shops[shopId].markers[class] and next(Shops[shopId].markers[class]) then
                hasMarkers = true
                break
            end
        end

        -- If markers exist, provide the option to view/edit them
        if hasMarkers then
            table.insert(menuOptions, {
                title = locale("menu_title.view_markers"),
                icon = "list",
                description = locale("menu_description.view_markers"),
                onSelect = function()
                    ViewMarkers(shopId, returnMenu)
                end
            })
        end
    end

    -- Register and display the marker management menu
    lib.registerContext({
        id = "marker_management",
        title = locale("menu_title.marker_management"),
        menu = "manage_mechanic_shop",
        onBack = function()
            RegisterManageShopContext(shopId, returnMenu)
        end,
        options = menuOptions
    })
    lib.showContext("marker_management")
end

-- Function to register view markers context options:
local function RegisterViewMarkersContext(shopId, returnMenu)
    local menuOptions = {}
    if not Shops[shopId].markers or not next(Shops[shopId].markers) then
        return lib.showContext("marker_management")
    end

    for class,markers in pairs(Shops[shopId].markers) do
        for markerId,marker in pairs(markers) do
            menuOptions[#menuOptions + 1] = {
                title = marker.name or marker.id,
                icon = Config.Shop.Markers[class].icon,
                onSelect = function()
                    ManageMarker(shopId, class, marker.id, marker, returnMenu)
                end
            }
        end
    end

    if next(menuOptions) <= 0 then 
        return lib.showContext("marker_management")
    end

    lib.registerContext({
        id = "view_shop_markers",
        title = locale("menu_title.view_markers"),
        menu = "marker_management",
        options = menuOptions
    })
end

--- Function to view markers for a given mechanic shop.
--- @param shopId (number) The ID of the shop whose markers are being viewed.
--- @param returnMenu (string) The menu to return to after viewing markers (optional).
function ViewMarkers(shopId, returnMenu)
    -- Register the marker management context
    RegisterViewMarkersContext(shopId, returnMenu)
    -- Show the marker management menu
    lib.showContext("view_shop_markers")
end

--- Function to edit an existing marker for a mechanic shop.
--- @param shopId (number) The ID of the shop to which the marker belongs.
--- @param class (string) The class/type of the marker (e.g., "storage", "workbench").
--- @param markerKey (number|string) The unique identifier of the marker.
--- @param marker (table) The existing marker data (coordinates, name, properties).
--- @param returnMenu (string) The menu to return to after editing the marker (optional).
local function EditMarker(shopId, class, markerKey, marker, returnMenu)
    local ped = PlayerPedId()
    local pedCoords = GetEntityCoords(ped)
    local markerCoords = vector3(pedCoords.x, pedCoords.y, pedCoords.z)

    local inputFields = {
        {type = "select", label = locale("input_label.marker_class"), options = {{label = locale("menu_title."..tostring(class).."_main"), value = 1}}, disabled = true, default = 1, description = locale("input_description.marker_class")},
        {type = "input", label = locale("input_label.marker_name"), required = true, description = locale("input_description.marker_name")},
        {type = "number", label = locale("input_label.marker_type"), default = 20, required = true, description = locale("input_description.marker_type"), min = 0, max = 43},
        {type = "color", label = locale("input_label.marker_color"), required = true, default = 'rgba(199, 30, 30, 1)', description = locale("input_description.marker_color"), format = "rgba"},
        {type = "checkbox", label = locale("input_label.marker_bobupdown")},
        {type = "checkbox", label = locale("input_label.marker_facecamera"), checked = true},
        {type = "checkbox", label = locale("input_label.marker_createblip"), checked = true},
    }

    if class == "storage" then 
        table.insert(inputFields, {type = "number", label = locale("input_label.marker_stash_slots"), required = true, min = 1, max = Config.Shop.Storage.maxSlots, description = locale("input_description.marker_stash_slots")})
        table.insert(inputFields, {type = "number", label = locale("input_label.marker_stash_weight"), required = true, min = 1, max = Config.Shop.Storage.maxWeight, description = locale("input_description.marker_stash_weight")})
    end

    local input = lib.inputDialog(locale("input_title.marker_edit"), inputFields)

    if not input then
        return lib.showContext("manage_marker")
    end
    
    local rgba = math.torgba(input[4])

    -- Construct marker data payload
    local data = {
        class = class,
        name = input[2],
        coords = {x = math.round(markerCoords.x, 2), y =math.round(markerCoords.y, 2), z =math.round(markerCoords.z, 2)},
        blip = {
            enable = input[7],
            sprite = Config.Shop.Markers[class].sprite,
            color = Config.Shop.Markers[class].color,
            scale = Config.Shop.Markers[class].scale,
            display = Config.Shop.Markers[class].display
        },
        type = input[3],
        color = {r = rgba.x, g = rgba.y, b = rgba.z, a = (rgba.w * 255)},
        bobUpAndDown = input[5],
        faceCamera = input[6],
        markerId = marker.id
    }

    -- If the marker class is storage, assign stash inputs or fallback to default stash settings
    if class == "storage" then
        data.stash = {
            slots = input[8] or Config.Shop.Storage.slots,
            weight = input[9] or Config.Shop.Storage.weight
        }
    end

    -- Trigger server event to save marker data
    TriggerServerEvent("t1ger_mechanic:server:editMarker", shopId, class, marker.id, data)

    -- Wait briefly to ensure the update is processed
    Wait(250)

    -- Return to the marker management menu
    MarkerManagement(shopId, returnMenu)
end

--- Function to manage a specific marker within a mechanic shop.
--- @param shopId (number) The ID of the shop that owns the marker.
--- @param class (string) The class/type of the marker (e.g., "storage", "workbench").
--- @param markerKey (number|string) The unique identifier of the marker.
--- @param marker (table) The marker data (coordinates, name, properties).
--- @param returnMenu (string) The menu to return to after managing the marker (optional).
function ManageMarker(shopId, class, markerKey, marker, returnMenu)
    local menuOptions = {}

    -- Option to teleport to the marker (if enabled in config)
    if Config.Shop.Management.markerTeleport then
        menuOptions[#menuOptions + 1] = {
            title = locale("menu_title.marker_teleport"),
            icon = "plane",
            description = locale("menu_description.marker_teleport"),
            onSelect = function()
                local player = PlayerPedId()
                SetEntityCoords(player, marker.coords.x, marker.coords.y, marker.coords.z, 0.0, 0.0, 0.0, false)
                Wait(100)
                lib.showContext("manage_marker")
            end
        }
    end

    -- Option to edit the marker (if enabled in config)
    if Config.Shop.Management.markerEdit then
        menuOptions[#menuOptions + 1] = {
            title = locale("menu_title.marker_edit"),
            icon = "pen-to-square",
            description = locale("menu_description.marker_edit"),
            onSelect = function()
                EditMarker(shopId, class, markerKey, marker, returnMenu)
            end
        }
    end

    -- Option to delete the marker
    menuOptions[#menuOptions + 1] = {
        title = locale("menu_title.marker_delete"),
        icon = "trash",
        description = locale("menu_description.marker_delete"),
        onSelect = function()
            TriggerServerEvent("t1ger_mechanic:server:deleteMarker", shopId, class, marker.id)
            Wait(250)
            MarkerManagement(shopId, returnMenu)
            local isOpen, text = lib.isTextUIOpen()
            if isOpen and text == locale("textui."..class.."_marker") then 
                lib.hideTextUI()
            end
        end
    }

    -- Register and display the marker management menu
    lib.registerContext({
        id = "manage_marker",
        title = string.format(locale("menu_title.manage_marker", marker.name or markerKey)),
        menu = "view_shop_markers",
        onBack = function()
            RegisterViewMarkersContext(shopId, returnMenu)
        end,
        options = menuOptions
    })
    lib.showContext("manage_marker")
end

--- Function to create a new marker for a mechanic shop.
--- @param shopId (number) The ID of the shop where the marker will be created.
function CreateMarker(shopId, returnMenu)
    local ped = PlayerPedId()
    local pedCoords = GetEntityCoords(ped)
    local markerCoords = vector3(pedCoords.x, pedCoords.y, pedCoords.z)

    -- Prepare available marker classes from Config.Shop.Markers
    local markerClasses = {}
    for class, marker in pairs(Config.Shop.Markers) do
        if marker.enable then
            table.insert(markerClasses, {label = marker.title, value = #markerClasses + 1, class = tostring(class)})
        end
    end

    -- Open input dialog for marker creation
    local input = lib.inputDialog(locale("input_title.marker_creation"), {
        {type = "select", label = locale("input_label.marker_class"), required = true, clearable = true, options = markerClasses, description = locale("input_description.marker_class")},
        {type = "input", label = locale("input_label.marker_name"), required = true, description = locale("input_description.marker_name")},
        {type = "number", label = locale("input_label.marker_type"), default = 20, required = true, description = locale("input_description.marker_type"), min = 0, max = 43},
        {type = "color", label = locale("input_label.marker_color"), required = true, default = 'rgba(199, 30, 30, 1)', description = locale("input_description.marker_color"), format = "rgba"},
        {type = "checkbox", label = locale("input_label.marker_bobupdown")},
        {type = "checkbox", label = locale("input_label.marker_facecamera"), checked = true},
        {type = "checkbox", label = locale("input_label.marker_createblip"), checked = true},
    })

    -- If input is canceled, return to the previous menu
    if not input then
        return lib.showContext("marker_management")
    end

    -- Extract marker class from selected option
    local class = markerClasses[input[1]].class
    local rgba = math.torgba(input[4])

    -- Construct marker data payload
    local data = {
        class = class,
        name = input[2],
        coords = {x = math.round(markerCoords.x, 2), y =math.round(markerCoords.y, 2), z =math.round(markerCoords.z, 2)},
        blip = {
            enable = input[7],
            sprite = Config.Shop.Markers[class].sprite,
            color = Config.Shop.Markers[class].color,
            scale = Config.Shop.Markers[class].scale,
            display = Config.Shop.Markers[class].display
        },
        type = input[3],
        color = {r = rgba.x, g = rgba.y, b = rgba.z, a = (rgba.w * 255)},
        bobUpAndDown = input[5],
        faceCamera = input[6]
    }

    -- If the marker class is storage, assign default stash settings
    if class == "storage" then
        data.stash = {
            slots = Config.Shop.Storage.slots,
            weight = Config.Shop.Storage.weight
        }
    end

    -- Trigger server event to save marker data
    TriggerServerEvent("t1ger_mechanic:server:createMarker", shopId, data)

    Wait(250)
    MarkerManagement(shopId, returnMenu)
end

--- Function to delete a mechanic shop after admin confirmation.
--- @param shopId (number) The ID of the shop to be deleted.
--- @param returnMenu (string) The menu to return to if deletion is canceled (optional).
function DeleteShop(shopId, returnMenu)
    -- Show confirmation dialog before deleting the shop
    local alert = lib.alertDialog({
        header = locale("alert.header.delete_shop"), -- Header text
        content = string.format(locale("alert.content.delete_shop"), Shops[shopId].name, shopId), -- Confirmation message
        centered = true, -- Centers the alert text
        cancel = true -- Allows the user to cancel the action
    })

    -- If the user confirms, send a request to delete the shop on the server
    if alert == "confirm" then
        TriggerServerEvent("t1ger_mechanic:server:deleteShop", shopId)
    else
        -- If canceled, return to the previous menu
        return lib.showContext(returnMenu or "manage_mechanic_shop")
    end
end