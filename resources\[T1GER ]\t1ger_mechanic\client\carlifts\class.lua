CarLiftHandle = {}
CarLiftHandle.__index = CarLiftHandle

---Creates a new CarLiftHandle instance
---@param id number carlift id
---@param data table carlift data (id, coords, rotation, baseNetId, armsNetId)
---@return CarLiftHandle table CarLiftHandle object
function CarLiftHandle:New(id, data)
    local self = setmetatable({}, CarLiftHandle)

    self.id = id
    self.coords = data.coords
    self.rotation = data.rotation
    self.baseNetId = data.baseNetId
    self.armNetId = data.armNetId

    return self
end

---Controls the carlifts in terms of animations. Up, Down or Stop
---@param method string control method (up, down, stop)
function CarLiftHandle:Control(method)
    local liftArms = NetworkGetEntityFromNetworkId(self.armNetId)
    local ent = Entity(liftArms).state
    ent:set("t1ger_mechanic:carLiftControl", {
        id = self.id,
        netId = self.baseNetId,
        method = method
    }, true)
end

---Delete a carlift. Optional param to delete permanently
---@param permanent boolean (optional) `true` deletes carlift permanently. `false` deletes for the session only
function CarLiftHandle:Delete()
    TriggerServerEvent("t1ger_mechanic:server:deleteCarLift", self.id)
end