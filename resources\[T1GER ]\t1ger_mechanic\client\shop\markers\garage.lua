-- Opens the main garage menu for the mechanic shop
---@param shopId number unique shop identifier
---@param markerId string unique marker identifier
function GarageMain(shopId, markerId)
    local menuOption = {}
    local vehicle = GetVehiclePedIsIn(player, false)
    if not vehicle or vehicle == 0 then
        table.insert(menuOption, {
            title = locale("menu_title.garage_get_vehicle"),
            icon = "car",
            arrow = true,
            onSelect = function()
                ViewStoredVehicles(shopId, markerId)
            end
        })
    else
        table.insert(menuOption, {
            title = locale("menu_title.garage_store_vehicle"),
                icon = "square-parking",
                onSelect = function()
                    StoreVehicle(shopId, markerId)
                end
        })
    end
    lib.registerContext({
        id = "mechanic_garage_menu",
        title = locale("menu_title.garage_main"),
        options = menuOption
    })
    lib.showContext("mechanic_garage_menu")
end

-- Store an owned vehicle inside the mechanic garage
---@param shopId number unique shop identifier
---@param markerId string unique marker identifier
function StoreVehicle(shopId, markerId)
    -- check if inside vehicle:
    local vehicle = GetVehiclePedIsIn(player, false)
    if not vehicle or vehicle == 0 then 
        _API.ShowNotification(locale("notification.must_be_inside_vehicle"), "inform", {})
        return lib.showContext("mechanic_garage_menu")
    end

    -- get vehicle properties
    local props = _API.GetVehicleProperties(vehicle)

    -- check if vehicle is owned? else return
    local isOwned = lib.callback.await("t1ger_mechanic:server:doesVehiclePlateExist", false, props.plate)
    if not isOwned then 
        _API.ShowNotification(locale("notification.vehicle_not_owned"), "inform", {})
        return lib.showContext("mechanic_garage_menu")
    end

    -- store vehicle & update database
    TriggerServerEvent("t1ger_mechanic:server:vehicleIn", markerId, props)

    -- remove keys?
    _API.RemoveVehicleKeys(vehicle)

    -- delete vehicle:
    Wait(250)
    _API.DeleteVehicle(vehicle)
end

-- View parked owned vehicles inside the mechanic garage
---@param shopId number unique shop identifier
---@param markerId string unique marker identifier
function ViewStoredVehicles(shopId, markerId)
    local playerVehicles = {}
    local results = lib.callback.await("t1ger_mechanic:server:getOwnedVehicles", false)

    -- validate results:
    if type(results) ~= "table" or #results == 0 then
        _API.ShowNotification(locale("notification.no_owned_vehicles"), "inform", {})
        return lib.showContext("mechanic_garage_menu")
    end

    -- loop through owned vehicles:
    for _, vehicle in ipairs(results) do
        -- Check if the vehicle belongs to this garage
        if vehicle.garage == markerId then
            local isStored = false
    
            -- Handle different storage formats (number-based and boolean-based)
            if type(vehicle.stored) == "number" then
                isStored = (vehicle.stored == 1) -- 1 = stored in garage
            elseif type(vehicle.stored) == "boolean" then
                isStored = vehicle.stored -- true = stored
            end
    
            -- If the vehicle is stored, add it to the menu
            if isStored then
                -- vehicle props:
                local props = json.decode(vehicle.props)
                -- display name of the vehicle:
                local modelName = GetLabelText(GetDisplayNameFromVehicleModel(props.model))
                -- model make:
                local makeName = GetLabelText(GetMakeNameFromVehicleModel(props.model))

                -- metaoptions:
                local metaOptions = {
                    {label = locale("menu_metadata.vehicle_make"), value = makeName},
                    {label = locale("menu_metadata.vehicle_model"), value = modelName},
                    {label = locale("menu_metadata.vehicle_plate"), value = vehicle.plate},
                }

                if props.engineHealth ~= nil then
                    table.insert(metaOptions, {label = locale("menu_metadata.vehicle_engine"), value = (props.engineHealth/10).."%"})
                end

                if props.bodyHealth ~= nil then
                    table.insert(metaOptions, {label = locale("menu_metadata.vehicle_body"), value = (props.bodyHealth/10).."%"})
                end

                if props.fuelLevel ~= nil then
                    table.insert(metaOptions, {label = locale("menu_metadata.vehicle_fuel"), value = props.fuelLevel.."%"})
                end

                -- create context menu option:
                playerVehicles[#playerVehicles + 1] = {
                    title = makeName.." "..modelName.." ["..vehicle.plate.."]",
                    icon = "car",
                    metadata = metaOptions,
                    onSelect = function()
                        _API.SpawnVehicle(props.model, coords, GetEntityHeading(player), true, function(spawnedVehicle)
                            TriggerServerEvent("t1ger_mechanic:server:vehicleOut", markerId, props)
                            _API.SetVehicleProperties(spawnedVehicle, props)
                            SetPedIntoVehicle(player, spawnedVehicle, -1)
                            _API.GiveVehicleKeys(spawnedVehicle)
                        end, true)
                    end
                }
            end
        end
    end

    -- validate menu options:
    if #playerVehicles <= 0 then 
        _API.ShowNotification(locale("notification.garage_no_vehicles"), "inform", {})
        return lib.showContext("mechanic_garage_menu")
    end

    -- register context:
    lib.registerContext({
        id = "mechanic_garage_view",
        menu = "mechanic_garage_menu",
        title = locale("menu_title.garage_get_vehicle"),
        options = playerVehicles
    })

    -- show context:
    lib.showContext("mechanic_garage_view")
end