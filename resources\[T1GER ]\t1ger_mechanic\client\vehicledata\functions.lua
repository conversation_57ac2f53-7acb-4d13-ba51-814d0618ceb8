--- Retrieves the stored vehicle data from the statebag.
--- @param vehicle integer The vehicle entity handle.
--- @return table|nil #The vehicle data table if found, otherwise nil.
function GetVehicleData(vehicle)
    -- Validate that the vehicle entity exists
    if not vehicle or not DoesEntityExist(vehicle) then
        return nil -- Return nil if the vehicle is invalid
    end

    -- Access the entity's statebag and retrieve the vehicle data
    local vehicleState = Entity(vehicle).state
    local vehicleData = vehicleState["t1ger_mechanic:vehicleData"]

    return vehicleData -- Return the retrieved vehicle data
end
exports("GetVehicleData", GetVehicleData)

--- Sets or updates vehicle data for a given vehicle using statebag
--- @param vehicle integer The vehicle entity handle.
--- @param data table The vehicle data to merge and set.
--- @param replicate boolean whether to replicate with server or not
function SetVehicleData(vehicle, data, replicate)
    if not vehicle or not DoesEntityExist(vehicle) then return false end

    if type(data) ~= "table" or next(data) == nil then 
        return error("[SetVehicleData] Invalid type for data. Must be a table containing: {mileage<number>, core_parts<table>, service_parts<table>}")
    end

    if type(data.mileage) ~= "number" then
        return error("[SetVehicleData] Invalid type for mileage. Must be a number.")
    end

    if type(data["core_parts"]) ~= "table" or next(data["core_parts"]) == nil then
        return error("[SetVehicleData] Invalid type for core_parts. Must be a table indexed by partName<string> with value being health<number>")
    end

    if type(data["service_parts"]) ~= "table" or next(data["service_parts"]) == nil then
        return error("[SetVehicleData] Invalid type for service_parts. Must be a table indexed by partName<string> with value being mileage<number>")
    end

    -- Access the entity's statebag and set the vehicle data
    local vehicleState = Entity(vehicle).state
    vehicleState:set("t1ger_mechanic:vehicleData", data, replicate or true)
    return true
end
exports("SetVehicleData", SetVehicleData)

--- Returns the current mileage of a given vehicle entity.
--- @param vehicle integer The vehicle entity handle.
--- @return number #The mileage of the vehicle in km or mi depending on config.
function GetVehicleMileage(vehicle)
    if not DoesEntityExist(vehicle) then return 0 end
    local vehicleData = GetVehicleData(vehicle)
    return (vehicleData and tonumber(vehicleData.mileage)) or 0
end
exports("GetVehicleMileage", GetVehicleMileage)

--- Sets the vehicle's mileage safely
--- @param vehicle integer The vehicle entity handle
--- @param mileage number New mileage value (must be >= 0)
--- @return boolean success
function SetVehicleMileage(vehicle, mileage)
    if not DoesEntityExist(vehicle) then return false end

    if type(mileage) ~= "number" then
        return error("[SetVehicleMileage] Invalid type for mileage. Must be a number.")
    end

    mileage = math.max(0, mileage) -- clamp to 0 if negative

    local vehicleState = Entity(vehicle).state
    local data = vehicleState["t1ger_mechanic:vehicleData"]
    if not data then return false end

    data.mileage = math.round(mileage, 2)
    vehicleState:set("t1ger_mechanic:vehicleData", data, true)
    return true
end
exports("SetVehicleMileage", SetVehicleMileage)

--- Returns the health of a core part on a given vehicle. 
--- @param vehicle integer The vehicle entity handle
--- @param part string The name of the core part (e.g., `"radiator"`, `"brakes"`).
--- @return number #Returns the part health as a number, or -1 if the part is not found.
function GetCorePartHealth(vehicle, part)
    if type(part) ~= "string" or part == "" then
        return error("[GetCorePartHealth] Invalid type for 'part'. Must be a non-empty string")
    end

    if not Config.CoreParts[part] then
        return error("[GetCorePartHealth] Part '%s' does not exist in Config.CoreParts")
    end

    if not vehicle or not DoesEntityExist(vehicle) then
        return -1
    end

    -- Retrieve vehicle data from statebag
    local vehicleData = GetVehicleData(vehicle)

    -- Validate if vehicle data and the requested core part exist
    if not vehicleData or not vehicleData.core_parts or not vehicleData.core_parts[part] then 
        return -1 -- Return -1 if part does not exist
    end

    return vehicleData.core_parts[part] -- Return the health value of the specified core part
end
exports("GetCorePartHealth", GetCorePartHealth)

--- Sets the health of a specific core part
--- @param vehicle integer The vehicle entity handle
--- @param part string Part name (must exist in Config.CoreParts)
--- @param health number New health value (clamped between 0 and 100)
--- @return boolean success
function SetCorePartHealth(vehicle, part, health)
    if not vehicle or not DoesEntityExist(vehicle) then return false end

    if type(part) ~= "string" or part == "" then
        return error("[SetCorePartHealth] Invalid type for 'part'. Must be a non-empty string")
    end

    if type(health) ~= "number" then
        return error("[SetCorePartHealth] Invalid type for 'health'. Must be a number between 0 and 100")
    end

    if not Config.CoreParts[part] then
        return error("[SetCorePartHealth] Part '%s' does not exist in Config.CoreParts")
    end

    local clampedHealth = math.min(100.0, math.max(0.0, health))

    local vehicleState = Entity(vehicle).state
    local data = vehicleState["t1ger_mechanic:vehicleData"]
    if not data or not data.core_parts then return false end

    data.core_parts[part] = math.round(clampedHealth, 2)
    vehicleState:set("t1ger_mechanic:vehicleData", data, true)
    return true
end
exports("SetCorePartHealth", SetCorePartHealth)

--- Sets all core parts of the vehicle by setting their health to a specified value. Only parts with lower health than the given value are updated
--- @param vehicle integer The vehicle entity handle
--- @param value number New health value to apply (clamped between 0.0 and 100.0)
function SetAllCorePartsHealth(vehicle, value)
    if not DoesEntityExist(vehicle) then return false end

    if type(value) ~= "number" then
        return error("[SetAllCorePartsHealth] Invalid type for 'value'. Must be a number between 0 and 100")
    end

    local vehicleState = Entity(vehicle).state
    local data = vehicleState["t1ger_mechanic:vehicleData"]
    if not data or not data.core_parts then return false end

    for partName, health in pairs(data.core_parts) do
        local newHealth = math.clamp(value, 0.0, 100.0)
        if health < newHealth then
            data.core_parts[partName] = math.round(newHealth, 2)
        end
    end

    vehicleState:set("t1ger_mechanic:vehicleData", data, true)
    return true
end
exports("SetAllCorePartsHealth", SetAllCorePartsHealth)

--- Returns the condition stage based on current core part health
--- @param currentHealth number The current health value of the core part
--- @return table #The condition stage (percent, label, index(1 = optimal, 2 = worn, 3 = critical, 4 = failed)
function GetCorePartCondition(currentHealth)
    if type(currentHealth) ~= "number" then
        return errpr("[GetCorePartCondition] Invalid type for currentHealth. Must be a number!")
    end

    local stages = Config.PartConditions["core_parts"]
    if not stages then 
        return error("[GetCorePartCondition] Could not find core_parts conditions inside Config.PartConditions")
    end

    for i, stage in ipairs(stages) do
        if currentHealth >= stage.percent then
            stage.index = i
            return stage
        end
    end

    return nil
end

--- Returns table with core part info for a given part with current health.
--- @param partName string The name of the part
--- @param health number The current health of the part
--- @return table?
function GetCorePartInfo(partName, health)
    if type(partName) ~= "string" then return nil end
    if type(health) ~= "number" then return nil end

    -- get part data from config
    local partData = Config.CoreParts[partName]
    if not partData then return nil end

    -- get condition and stage/index
    local condition = GetCorePartCondition(health)
    if not condition then return end

    -- associated parts in comma-separated list
    local formatted = GetAssociatedPartsFormatted(partData.associated, "service_parts")

    -- Return a table containing all relevant service part data
    return {
        label = partData.label,             -- Display name of the core part (e.g., "Radiator")
        icon = partData.icon,               -- Icon URL for the core part
        item = partData.item,               -- Associated inventory item (used during replacements)
        associated = partData.associated,   -- The associated service parts
        type = partData.type,               -- The type of part "electric", "gas", "shared"
        price = partData.price,             -- The price of the part
        associatedList = formatted,         -- Comma-separated list of associated service parts
        condition = condition,              -- Condition status (e.g., "Optimal", "Worn", "Critical")
        isFailured = condition.index == 4,  -- Checks part condition stage and compares with failed (4)
        isDegraded = condition.index > 1,   -- Checks if part condition is degraded (index > 1 (worn, critical or failed))
        health = health,                    -- The health in between 0.0 and 100.0
    }
end

--- Returns the mileage of a service part on a given vehicle.
--- @param vehicle integer The vehicle entity handle
--- @param part string The name of the core part (e.g., `"oil_filter"`, `"brake_fluid"`).
--- @return integer #Returns the part mileage as a number, or -1 if the part is not found.
function GetServicePartMileage(vehicle, part)
    if type(part) ~= "string" or part == "" then
        return error("[GetServicePartMileage] part type must be a non-empty string")
    end

    if not Config.ServiceParts[part] then
        return error("[GetServicePartMileage] part '%s' does not exist in Config.ServiceParts")
    end

    if not vehicle or not DoesEntityExist(vehicle) then
        return -1
    end

    -- Retrieve vehicle data from statebag
    local vehicleData = GetVehicleData(vehicle)

    -- Validate if vehicle data and the requested service part exist
    if not vehicleData or not vehicleData.service_parts or not vehicleData.service_parts[part] then 
        return -1 -- Return -1 if part does not exist
    end

    return vehicleData.service_parts[part] -- Return the mileage value of the specified service part
end
exports("GetServicePartMileage", GetServicePartMileage)

--- Sets the driven mileage of a specific service part
--- @param vehicle integer The vehicle entity handle
--- @param part string Part name (must exist in Config.ServiceParts)
--- @param mileage number New mileage value (negative values will be set to 0)
--- @return boolean success
function SetServicePartMileage(vehicle, part, mileage)
    if not vehicle or not DoesEntityExist(vehicle) then return false end

    if type(part) ~= "string" or part == "" then
        return error("[SetServicePartMileage] Invalid type for 'part'. Must be a non-empty string")
    end

    if type(mileage) ~= "number" then
        return error("[SetServicePartMileage] Invalid type for 'mileage'. Must be a non-negative number")
    end

    if not Config.ServiceParts[part] then
        return error("[SetServicePartMileage] Part '%s' does not exist in Config.ServiceParts")
    end

    mileage = math.max(0, mileage) -- clamp to 0 if negative

    local vehicleState = Entity(vehicle).state
    local data = vehicleState["t1ger_mechanic:vehicleData"]
    if not data or not data.service_parts then return false end

    data.service_parts[part] = math.round(mileage, 2)
    vehicleState:set("t1ger_mechanic:vehicleData", data, true)
    return true
end
exports("SetServicePartMileage", SetServicePartMileage)

--- Sets all service parts of the vehicle by resetting their mileage to a specified value. Only parts with higher mileage than the given value are updated
--- @param vehicle integer The vehicle entity handle
--- @param value number New mileage value to apply (must be >= 0)
function SetAllServicePartsMileage(vehicle, value)
    if not DoesEntityExist(vehicle) then return false end

    if type(value) ~= "number" then
        return error("[SetAllServicePartsMileage] Invalid type for 'value'. Must be a number between 0 and 100")
    end

    local vehicleState = Entity(vehicle).state
    local data = vehicleState["t1ger_mechanic:vehicleData"]
    if not data or not data.service_parts then return false end

    for partName, mileage in pairs(data.service_parts) do
        local newMileage = (value < 0) and 0 or value
        if mileage > newMileage then
            data.service_parts[partName] = math.round(newMileage, 2)
        end
    end

    vehicleState:set("t1ger_mechanic:vehicleData", data, true)
    return true
end
exports("SetAllServicePartsMileage", SetAllServicePartsMileage)

--- Returns the condition stage based on current service part mileage and interval
--- @param mileage number The current mileage driven for this service part
--- @param interval number The configured interval for this service part
--- @return table #The condition stage (percent, label, index(1 = OK, 2 = Due, 3 = Overdue)
function GetServicePartCondition(mileage, interval)
    if type(interval) ~= "number" then
        return errpr("[GetServicePartCondition] Invalid type for interval. Must be a number!")
    end

    local stages = Config.PartConditions["service_parts"]
    if not stages then
        return error("[GetServicePartCondition] Could not find service_parts conditions inside Config.PartConditions")
    end

    if not mileage or not interval or interval <= 0 then
        return nil
    end

    -- Calculate remaining percent of lifespan
    local remainingPercent = ((interval - mileage) / interval) * 100

    for i, stage in ipairs(stages) do
        if remainingPercent >= stage.percent then
            stage.index = i
            return stage
        end
    end

    return nil
end

--- Returns table with service part info for a given part with current mileage. 
--- @param vehicle number The vehicle entity handle
--- @param partName string The name of the part
--- @param mileage number The current mileage of the part
--- @return table?
function GetServicePartInfo(vehicle, partName, mileage)
    if type(partName) ~= "string" then return nil end
    if type(mileage) ~= "number" then return nil end

    -- get part data from config
    local partData = Config.ServiceParts[partName]
    if not partData then return nil end

    -- get condition and stage/index
    local serviceInterval = GetVehicleServiceInterval(vehicle, partName)
    local condition = GetServicePartCondition(mileage, serviceInterval)
    if not condition then return end

    -- Calculate the remaining mileage before the next service and the condition percentage
    local remainingMileage = serviceInterval - mileage
    local health = math.round(((remainingMileage / serviceInterval) * 100), 2)  -- Allow negative percentages if overdue

    -- associated parts in comma-separated list
    local formatted = GetAssociatedPartsFormatted(partData.associated, "core_parts")

    -- Return a table containing all relevant service part data
    return {
        label = partData.label,             -- Display name of the service part (e.g., "Oil + Filter")
        icon = partData.icon,               -- Icon URL for the service part
        item = partData.item,               -- Associated inventory item (used during replacements)
        interval = serviceInterval,       -- The total mileage interval before the part needs servicing
        associated = partData.associated,   -- The associated core parts
        type = partData.type,               -- The type of part "electric", "gas", "shared"
        price = partData.price,             -- The price of the part
        associatedList = formatted,         -- Comma-separated list of associated core parts
        condition = condition,              -- The service status (e.g., "Service OK", "Service Due", "Service Overdue")
        health = health,                    -- The percentage of the service interval remaining (negative if overdue)
        mileage = mileage,                  -- The current mileage on the part (driven mileage)
        remaining = remainingMileage        -- The mileage left before the next service is due (negative if overdue)
    }
end

--- Returns comma-separated list of associated parts
--- @param parts table Associated parts table
--- @param componentType string Component type: "core_parts" or "service_parts"
--- @return string
function GetAssociatedPartsFormatted(parts, componentType)
    local labels = {}

    for _, partName in ipairs(parts or {}) do
        local partData = componentType == "core_parts" and Config.CoreParts[partName] or componentType == "service_parts" and Config.ServiceParts[partName]
        if partData and partData.label then
            table.insert(labels, partData.label)
        end
    end

    if next(labels) then
        return table.concat(labels, ", ")
    else
        return locale("menu_metadata.not_answered")
    end
end

--- Returns whether a vehicle entity is electric operated or not. If gamebuild > 3258 uses native, else function looks up in config for defined electric vehicles.
--- @param vehicle integer The vehicle entity handle
--- @return boolean #Returns whether the given vehicle is electric operated or not.
function IsVehicleElectric(vehicle)
    local vehicleModel = GetEntityModel(vehicle)
    if GetGameBuildNumber() >= 3258 then
        if GetIsVehicleElectric(vehicleModel) then
            return true
        end
    else
        for _,model in pairs(Config.ElectricVehicles) do
            if vehicleModel == GetHashKey(model) then
                return true
            end
        end
    end
    return false
end
exports("IsVehicleElectric", IsVehicleElectric)

--- Returns the mechanic-specific vehicle type: "gas" or "electric"
--- @param vehicle integer The vehicle entity handle
--- @return string #Returns "electric" or "gas"
function GetMechanicVehicleType(vehicle)
    return IsVehicleElectric(vehicle) and "electric" or "gas"
end
exports("GetMechanicVehicleType", GetMechanicVehicleType)

--- Checks if a given vehicle is trackable for mileage, service, and degradation.
--- @param vehicle integer The vehicle entity handle
--- @return boolean #True if vehicle is trackable; false if it's excluded (e.g. boats, aircraft, trailers).
function IsTrackableVehicle(vehicle)
    if not DoesEntityExist(vehicle) then return false end

    local vehicleModel = GetEntityModel(vehicle)

    for _, modelName in pairs(Config.BlacklistTracking) do
        local hash = GetHashKey(modelName)
        if vehicleModel == hash then
            return false
        end
    end

    local vehicleClass = GetVehicleClass(vehicle)

    local allowedClasses = {
        [0] = true,  -- Compacts
        [1] = true,  -- Sedans
        [2] = true,  -- SUVs
        [3] = true,  -- Coupes
        [4] = true,  -- Muscle
        [5] = true,  -- Sports Classics
        [6] = true,  -- Sports
        [7] = true,  -- Super
        [8] = true,  -- Motorcycles
        [9] = true,  -- Off-road
        [10] = true, -- Industrial
        [11] = true, -- Utility
        [12] = true, -- Vans
        [13] = false, -- Cycles (bicycles)
        [14] = false, -- Boats
        [15] = false, -- Helicopters
        [16] = false, -- Planes
        [17] = true, -- Service
        [18] = true, -- Emergency
        [19] = true, -- Military
        [20] = true, -- Commercial (e.g., large trucks)
        [21] = false, -- Trains
        [22] = true  -- Open Wheel
    }

    return allowedClasses[vehicleClass] or false
end
exports("IsTrackableVehicle", IsTrackableVehicle)

--- Returns the mileage interval for service parts for given vehicle based on its class
--- @param vehicle integer The vehicle entity handle
--- @param partName string The service part name
--- @return number #The mileage interval
function GetVehicleServiceInterval(vehicle, partName)
    if not Config.ServiceParts[partName] then
        return error(string.format("[GetVehicleServiceInterval] partname: '%s' not found in Config.ServiceParts", partName))
    end

    local baseInterval = Config.ServiceParts[partName].interval
    local vehicleClass = GetVehicleClass(vehicle)
    local multiplier = Config.ServiceIntervalMultipliers[vehicleClass] or 1.0

    -- Clamp multiplier
    multiplier = math.max(0.1, math.min(multiplier, 3.0))

    -- Calculate and floor the interval
    return math.floor(baseInterval * multiplier)
end

--- Returns whether all core parts for a given vehicle are intact (health = 100%)
--- @param vehicle integer The vehiclel entity handle
--- @return boolean #Returns `true` if intact, otherwise `false`
function AreAllCorePartsIntact(vehicle)
    if not DoesEntityExist(vehicle) then return false end
    
    -- get vehicle state and vehicle data:
    local vehicleState = Entity(vehicle).state
    local vehicleData = vehicleState["t1ger_mechanic:vehicleData"]
    if not vehicleData or type(vehicleData.core_parts) ~= "table" or next(vehicleData.core_parts) == nil then return false end

    -- get vehicle type (gas / electric)
    local vehicleType = GetMechanicVehicleType(vehicle)

    -- loop core parts
    for partName, health in pairs(vehicleData.core_parts) do
        local partInfo = GetCorePartInfo(partName, health)
        if partInfo and (partInfo.type == vehicleType or partInfo.type == "shared") then
            if partInfo.health < 100 then
                return false
            end
        end
    end

    -- return true if all core parts are >= 100.0 in health
    return true
end

--- Check if a vehicle has any core parts in "failured" condition
--- @param vehicle integer The vehicle entity handle
--- @return boolean #Whether the vehicle has at least one failed core part
function DoesVehicleHaveFailuredParts(vehicle)
    if not DoesEntityExist(vehicle) then return end
    
    -- get vehicle state and vehicle data:
    local vehicleState = Entity(vehicle).state
    local vehicleData = vehicleState["t1ger_mechanic:vehicleData"]
    if not vehicleData or type(vehicleData.core_parts) ~= "table" or next(vehicleData.core_parts) == nil then return end

    -- get vehicle type (gas / electric)
    local vehicleType = GetMechanicVehicleType(vehicle)

    -- loop core parts
    for partName, health in pairs(vehicleData.core_parts) do
        local partInfo = GetCorePartInfo(partName, health)
        if partInfo and (partInfo.type == vehicleType or partInfo.type == "shared") then
            if partInfo.isFailured then
                return true
            end
        end
    end

    -- return false if no failured parts
    return false
end
exports("DoesVehicleHaveFailuredParts", DoesVehicleHaveFailuredParts)

if Config.Debug then
    RegisterCommand("setparthealth", function(source, args, rawCommand)
        if not _API.Player.isAdmin then return end

        local vehicle = GetVehiclePedIsIn(player, false)

        if type(vehicle) ~= "number" or vehicle == 0 then
            vehicle = lib.getClosestVehicle(coords, 3.0, false)
        end

        if not DoesEntityExist(vehicle) then
            return print("[setparthealth] No vehicle found nearby.")
        end

        local partName = args[1]
        local newHealth = tonumber(args[2])

        if not partName or not newHealth then
            return print("[setparthealth] Usage: /setparthealth <partName> <newHealth>")
        end

        if not Config.CoreParts[partName] then
            return print("[setparthealth] entered partname does not exist in Config.CoreParts")
        end
        
        exports["t1ger_mechanic"]:SetCorePartHealth(vehicle, partName, newHealth)

        if Config.Debug then
            print(("[setparthealth] Core part '%s' health set to %s."):format(partName, newHealth))
        end
    end, false)

    RegisterCommand("setpartmileage", function(source, args, rawCommand)
        if not _API.Player.isAdmin then return end

        local vehicle = GetVehiclePedIsIn(player, false)

        if type(vehicle) ~= "number" or vehicle == 0 then
            vehicle = lib.getClosestVehicle(coords, 3.0, false)
        end

        if not DoesEntityExist(vehicle) then
            return print("[setpartmileage] No vehicle found nearby.")
        end

        local partName = args[1]
        local newMileage = tonumber(args[2])

        if not partName or not newMileage then
            return print("[setpartmileage] Usage: /setpartmileage <partName> <newMileage>")
        end

        if not Config.ServiceParts[partName] then
            return print("[setpartmileage] entered partname does not exist in Config.ServiceParts")
        end
        
        exports["t1ger_mechanic"]:SetServicePartMileage(vehicle, partName, newMileage)

        if Config.Debug then
            print(("[setpartmileage] Service part '%s' mileage set to %s %s."):format(partName, newMileage, Config.Mileage.Unit))
        end
    end, false)

    RegisterCommand("setvehiclemileage", function(source, args, rawCommand)
        if not _API.Player.isAdmin then return end

        local vehicle = GetVehiclePedIsIn(player, false)

        if type(vehicle) ~= "number" or vehicle == 0 then
            vehicle = lib.getClosestVehicle(coords, 3.0, false)
        end

        if not DoesEntityExist(vehicle) then
            return print("[setvehiclemileage] No vehicle found nearby.")
        end

        local newMileage = tonumber(args[1])

        if not newMileage then
            return print("[setvehiclemileage] Usage: /setvehiclemileage <mileage>")
        end
        
        exports["t1ger_mechanic"]:SetVehicleMileage(vehicle, newMileage)

        if Config.Debug then
            print(("[setvehiclemileage] Vehicle mileage set to '%s' %s."):format(newMileage, Config.Mileage.Unit))
        end
    end, false)
end