Core, Cfg, Framework = exports['t1ger_lib']:GetLib()
Lib = exports['t1ger_lib']:GetUtils()

isPlayerAdmin, playerTunerId, isTunerBoss, tuner_shops, shop_blips, shop_markers, mod_stations, mods_menu, itemMod, modOrder, browsingMods, usingMenu = false, 0, false, {}, {}, {}, {}, {}, {}, {}, false, false

Citizen.CreateThread(function()
    if Core == nil or Core.FrameworkReady() == nil then 
        return error("please start t1ger_lib resource before you start t1ger_tuningsystem")
    end
    while not Core.FrameworkReady() do Wait(1000); end
    Core.GetJob()
    Wait(1000)
	TriggerServerEvent('tuningsystem:server:playerLoaded')
    Wait(1500)
    SetupModStations()
    SetupRefillPoints()
end)

IsPlayerTuner = function()
    local job = Core.GetJob()
    local isTuner, shopId = IsTunerJob(job.name)
    if isTuner then 
        return true, shopId
    else
        return false
    end
end

IsTunerJob = function(jobName)
    if Config.Shops and next(Config.Shops) then
        for k,v in pairs(Config.Shops) do
            if v.job.name == jobName then 
                return true, v.id
            end
        end
    end
    return false
end

IsTunerBoss = function(shopId, playerGrade)
    if Config.Shops[shopId] and next(Config.Shops[shopId]) then
        if Config.Shops[shopId].job and next(Config.Shops[shopId].job) then
            if Config.Shops[shopId].job.grades[(tostring(playerGrade))] ~= nil then
                if Config.Shops[shopId].job.grades[(tostring(playerGrade))].isboss ~= nil and Config.Shops[shopId].job.grades[(tostring(playerGrade))].isboss == true then 
                    return true 
                end
            end
        end
    end
    return false
end

ReloadModStations = function()
    if mod_stations ~= nil and next(mod_stations) then
        for stationId, station in pairs(mod_stations) do
            if mod_stations[stationId].blip ~= nil and DoesBlipExist(mod_stations[stationId].blip) then
                RemoveBlip(mod_stations[stationId].blip)
            end
            if mod_stations[stationId].point ~= nil then 
                mod_stations[stationId].point.remove(mod_stations[stationId].point)
            end 
        end
    end
    SetupModStations()
end

CreateObjectProp = function(model, pos)
    local mod = Config.Mods[modName]
    local object, netId = nil, nil

    local pos = vector3(pos.x or pos[1], pos.y or pos[2], pos.z or pos[3])

    Lib.LoadModel(model)
    local object = CreateObjectNoOffset(GetHashKey(model), pos.x, pos.y, pos.z, true, false, false)
    while not DoesEntityExist(object) do 
        Wait(10)
    end
    local netId = ObjToNet(object)

    return object, netId
end

GetClosestPropEntity = function(propName, dist)
    local nearbyObjects = lib.getNearbyObjects(coords, dist)
    if nearbyObjects ~= nil and next(nearbyObjects) ~= nil then 
        for objectId, objectData in pairs(nearbyObjects) do
            if GetEntityModel(objectData.object) == GetHashKey(propName) then
                return objectData.object
            end
        end
    end
    return nil
end

GetVehicleModelName = function(vehicle)
    local vehicleModel = GetEntityModel(vehicle)
    local modelName = GetDisplayNameFromVehicleModel(vehicleModel)
    if modelName ~= nil or modelName ~= '' or modelName ~= 'CARNOTFOUND' then 
        modelName = string.lower(modelName)
    else
        modelName = nil
    end
    return modelName
end

OnJobUpdate = function(job)
    local currentJob = Core.GetJob()

    local newGrade = Framework == 'ESX' and job.grade or Framework == 'QB' and job.grade.level
    while Core.GetJob().name ~= job.name or Core.GetJob().grade ~= newGrade do 
        Wait(1)
    end

    if Config.Shops == nil or (type(Config.Shops) == 'table' and next(Config.Shops) == nil) or type(Config.Shops) ~= 'table' then
        local data = lib.callback.await('tuningsystem:server:getShopsConfig', false)
        local attempts = 1000
        while data == nil or attempts > 0 do
            Wait(10)
            attempts = attempts - 1
        end
        Config.Shops = data
    end

    local isTunerJob, shopId = IsTunerJob(Core.GetJob().name)

    if isTunerJob then
        if currentJob.name == Core.GetJob().name then -- applying same job
            if currentJob.grade == Core.GetJob().grade then -- error, same job & grade
                playerTunerId = shopId
                isTunerBoss = IsTunerBoss(shopId, Core.GetJob().grade)
                return
            else
                -- update player's job grade on the same job:
                TriggerServerEvent('tuningsystem:server:updateEmployee', shopId, Core.GetJob().grade, false)
            end
        else
            if playerTunerId ~= nil and (playerTunerId > 0 and playerTunerId ~= shopId) then
                TriggerServerEvent('tuningsystem:server:removeEmployee', playerTunerId, false)
            end
            TriggerServerEvent('tuningsystem:server:addEmployee', shopId, Core.GetJob().grade, false)
        end
        playerTunerId = shopId
        isTunerBoss = IsTunerBoss(shopId, Core.GetJob().grade)
        if Config.Debug then
            print("Job Updated | Player Tuner ID: "..playerTunerId..' | Job Name: '..Core.GetJob().name..'('..Core.GetJob().grade..') | Is Boss: ', isTunerBoss)
        end
    end

    ReloadModStations()
    ReloadRefillPoints()

    TriggerEvent('tuningsystem:client:createShopMarkers', Config.Shops)
end

GetCraftingRecipe = function(point, cat)
    local recipes = {}

    for outputItem, recipe in pairs(cat.recipe) do
        local output_item = GetItemInfo(outputItem)
        local metaOptions, materials = {}, {}
        for inputItem, amount in pairs(recipe) do
            local input_item = GetItemInfo(inputItem)
            metaOptions[#metaOptions + 1] = {label = input_item.label, value = amount..'x'}
            materials[#materials + 1] = {name = input_item.name, label = input_item.label, amount = amount}
        end
        recipes[#recipes + 1] = {
            title = output_item.label,
            icon = output_item.icon,
            metadata = metaOptions,
            args = {input = materials, output = output_item},
            onSelect = function(args)
                Core.TriggerCallback('tuningsystem:server:hasMaterials', function(hasMaterials, missingItems)
                    if hasMaterials then
                        local success = nil
                        if Config.Workbench.SkillCheck.enable then
                            success = SkillCheck(Config.Workbench.SkillCheck.difficulty, Config.Workbench.SkillCheck.inputs)
                        else
                            success = true
                        end
                        if success then
                            if lib.progressBar({
                                duration = Config.Workbench.Duration,
                                label = Config.Workbench.ProgressLabel:format(args.output.label),
                                useWhileDead = false,
                                canCancel = true,
                                anim = {
                                    dict = Config.Workbench.Anim.dict,
                                    clip = Config.Workbench.Anim.name,
                                    flag = Config.Workbench.Anim.flag,
                                    blendIn = Config.Workbench.Anim.blendIn,
                                    blendOut = Config.Workbench.Anim.blendOut
                                },
                                disable = {
                                    move = true,
                                    combat = true
                                }
                            }) then
                                TriggerServerEvent('tuningsystem:server:craftItem', args.input, args.output)
                            end
                        end
                        Wait(100)
                        OpenWorkbenchMenu(point)
                    else
                        for k,v in pairs(missingItems) do
                            if v.invAmount == 0 then
                                Core.Notification({
                                    title = '',
                                    message = Lang['you_dont_have_x_item']:format(v.label, v.reqAmount),
                                    type = 'error'
                                })
                            else
                                Core.Notification({
                                    title = '',
                                    message = Lang['you_need_x_amount_item']:format(v.invAmount, v.label, v.reqAmount),
                                    type = 'error'
                                })
                            end
                        end
                        lib.showContext('workbench_menu_recipes')
                    end
                end, args.input)
            end,
        }
    end

    return recipes
end

StorageWithdrawOptions = function(point, storage)
    local options = {}

    for k,v in pairs(storage) do
        table.insert(options, {
            title = v.label..' ['..v.count..'x]',
            icon = 'plus',
            onSelect = function()
                local input = lib.inputDialog(Lang['input_title_withdraw']:format(v.label, v.count), {
                    {type = 'number', label = Lang['input_label_storage_withdraw_amount'], min = 1, max = v.count, icon = 'bars', placeholder = 100}
                })

                if not input then
                    return lib.showContext('storage_withdraw')
                end

                if input[1] == nil or input[1] == '' or input[1] == ' ' then
                    Core.Notification({
                        title = '',
                        message = Lang['input_required'],
                        type = 'error'
                    })
                    return lib.showContext('storage_withdraw')
                end

                if input[1] <= 0 then 
                    Core.Notification({
                        title = '',
                        message = Lang['input_amount_higher_0'],
                        type = 'error'
                    })
                    return lib.showContext('storage_withdraw')
                end

                TriggerServerEvent('tuningsystem:server:withdrawStorage', point.shopId, point.markerId, v.name, input[1])
                Wait(100)
                OpenStorage(point)
            end,
        })
    end

    return options
end

StorageDepositOptions = function(point, results)
    local options = {}

    for k,v in pairs(results) do
        table.insert(options, {
            title = v.label..' ['..v.count..'x]',
            icon = 'minus',
            onSelect = function()
                local input = lib.inputDialog(Lang['input_title_deposit']:format(v.label, v.count), {
                    {type = 'number', label = Lang['input_label_storage_deposit_amount'], min = 1, max = v.count, icon = 'bars', placeholder = 100}
                })

                if not input then
                    return lib.showContext('storage_deposit')
                end

                if input[1] == nil or input[1] == '' or input[1] == ' ' then
                    Core.Notification({
                        title = '',
                        message = Lang['input_required'],
                        type = 'error'
                    })
                    return lib.showContext('storage_deposit')
                end

                if tonumber(input[1]) <= 0 then 
                    Core.Notification({
                        title = '',
                        message = Lang['input_amount_higher_0'],
                        type = 'error'
                    })
                    return lib.showContext('storage_deposit')
                end

                if input[1] > v.count then 
                    Core.Notification({
                        title = '',
                        message = Lang['storage_max_deposit_amount_x']:format(v.count),
                        type = 'error'
                    })
                    return lib.showContext('storage_deposit')
                end
                
                TriggerServerEvent('tuningsystem:server:depositStorage', point.shopId, point.markerId, v.name, input[1])
                Wait(100)
                OpenStorage(point)
            end,
        })
    end

    return options
end

LaptopOrder = function(point, k, item)
    local quantity = lib.inputDialog(Lang['input_title_order_parts']:format(item.label, GetFormattedPrice(item.price)), {
        {type = 'number', label = Lang['input_label_order_quantity'], description = Lang['input_desc_order_quantity']:format(item.label), placeholder = 1, required = true, min = 1, max = 20},
    })
    if not quantity then
        return lib.showContext('laptop_menu_'..tostring(k))
    end

    quantity = quantity[1]
    local total = quantity * item.price
    local confirm = lib.inputDialog(Lang['input_title_confirm_order_parts']:format(quantity, item.label, GetFormattedPrice(total)), {
        {type = 'checkbox', label = Lang['input_label_order_confirm'], checked = false, required = true},
    })
    if not confirm then
        return lib.showContext('laptop_menu_'..tostring(k))
    else
        local storageId = nil
        if Config.OrderedItemsToStash == true then
            local storages = {}
            for k,v in pairs(Config.Shops[point.shopId].markers['storage']) do
                storages[#storages + 1] = {label = v.name, value = #storages + 1, markerId = v.id}
            end
            local selectStash = lib.inputDialog(Lang['input_title_laptop_select_storage'], {
                {type = 'select', label = Lang['input_label_order_select_storage'], required = true, options = storages, clearable = true, searchable = true} 
            })
            if not selectStash then 
                return lib.showContext('laptop_menu_'..tostring(k))
            else
                storageId = storages[selectStash[1]].markerId
            end
        end
        Core.TriggerCallback('tuningsystem:server:laptopOrder', function(paid)
            if paid then 
                PlaySoundFrontend(-1, 'ROBBERY_MONEY_TOTAL', 'HUD_FRONTEND_CUSTOM_SOUNDSET', true)
            else
                Core.Notification({
                    title = '',
                    message = Lang['order_parts_insufficient_funds'],
                    type = 'error'
                })
            end
            lib.showContext('laptop_menu_main')
        end, item.name, total, quantity, point.shopId, storageId)
    end
end

GetItemInfo = function(itemName)
    for category,items in pairs(Config.Items) do
        for k,v in pairs(items) do 
            if v.name == itemName then 
                return v
            end
        end
    end
    return false
end

IsNearObjectProp = function(entity)
    if Lib.IsEntityValid(entity) and GetEntityType(entity) ~= 0 then 
        local entCoords = GetEntityCoords(entity)
        local entDistance = #(coords - entCoords)
        
        if entDistance <= 2.0 then
            return true
        end
    end
    return false
end

IsNearVehicle = function(entity)
    if Lib.IsEntityValid(entity) and GetEntityType(entity) ~= 0 then 
        local vehCoords = GetEntityCoords(entity)
        local vehDistance = #(coords - vehCoords)
        
        if vehDistance <= 5.0 then
            return true
        end
    end
    return false
end

IsNearVehicleEngine = function(entity)
    if Lib.IsEntityValid(entity) and GetEntityType(entity) ~= 0 then 
        local engineCoords = nil

        local boneIndex = GetEntityBoneIndexByName(entity, 'engine')
        if boneIndex and boneIndex > 0 then
            engineCoords = GetWorldPositionOfEntityBone(entity, boneIndex)
        else
            local min,max = GetModelDimensions(GetEntityModel(entity))
            engineCoords = GetOffsetFromEntityInWorldCoords(entity, 0.0, (max.y - 1.0), 0.0)
        end

        if #(coords - engineCoords) <= 1.5 then
            return true 
        end

    end
    return false
end

GetVehicleEngineCoords = function(vehicle)
    local modelHash = GetEntityModel(vehicle)
    local min,max = GetModelDimensions(modelHash)
    local boneIndex = GetEntityBoneIndexByName(vehicle, 'engine')
    if boneIndex and boneIndex > 0 then
        return GetWorldPositionOfEntityBone(vehicle, boneIndex)
    else
        return GetOffsetFromEntityInWorldCoords(vehicle, 0.0, (max.y - 1.0), 0.0)
    end
end

GetNearbyPlayersOption = function()
    local options = {}

    local players = Lib.GetPlayersInArea(GetEntityCoords(PlayerPedId()), Config.RecruitMember.Distance)

    for i = 1, #players do
        local playerId = GetPlayerServerId(players[i])
        local fullName = Core.GetFullName(playerId)
        local title = fullName..' ['..playerId..']'
        if Config.RecruitMember.ShowFullName == false then
            title = '['..playerId..']'
        end
        table.insert(options, {label = title, value = playerId})
    end

    if Config.Debug then
        table.insert(options, {label = 'T1GER Scripts [1]', value = 1})
    end

    if options == nil or next(options) == nil then
        Core.Notification({
            title = '',
            message = Lang['no_players_nearby'],
            type = 'inform'
        })
        return false
    end

    return options
end

RepairSound = function()
    PlaySoundFromEntity(GetSoundId(), 'Hydraulics_Down', player, 'Lowrider_Super_Mod_Garage_Sounds', true, 0)
    Wait(200)
    PlaySoundFromEntity(GetSoundId(), 'Hydraulics_Up', player, 'Lowrider_Super_Mod_Garage_Sounds', true, 0)
end

PlayUpgradeSound = function()
    PlaySoundFromEntity(GetSoundId(), 'Hydraulics_Down', player, 'Lowrider_Super_Mod_Garage_Sounds', true, 0)
    Wait(200)
    PlaySoundFromEntity(GetSoundId(), 'Hydraulics_Up', player, 'Lowrider_Super_Mod_Garage_Sounds', true, 0)
end

DoesModExist = function(vehicle, modName, props)
    SetVehicleModKit(vehicle, 0)
    if modName == 'tyreSmokeColor' or modName == 'plateIndex' or modName == 'windowTint' or modName == 'color1' or modName == 'color2' or modName == 'pearlescentColor' or modName == 'wheelColor' or modName == 'dashboardColor' or modName == 'interiorColor' then
        return true
    elseif modName == 'xenonColor' or modName == 'customXenon' then 
        if props['modXenon'] ~= nil and (props['modXenon'] == true or props['modXenon'] == 1) then 
            return true 
        end
    elseif modName == 'neonColor' then
        if props['neonEnabled'] ~= nil then
            for k,v in pairs(props['neonEnabled']) do
                if v == 1 or v == true then 
                    return true 
                end
            end
        end
    elseif modName == 'modLivery' then 
        if GetVehicleLivery(vehicle) >= 0 or GetVehicleLiveryCount(vehicle) > 0 or GetNumVehicleMods(vehicle, Config.Mods[modName].modType) ~= nil and GetNumVehicleMods(vehicle, Config.Mods[modName].modType) > 0 then
            return true
        else
            local modCount = GetNumVehicleMods(vehicle, Config.Mods[modName].modType)
            if modCount ~= nil and modCount > 0 then 
                return true
            end
        end
    elseif modName == 'modRoofLivery' then 
        if GetVehicleRoofLivery(vehicle) >= 0 or GetVehicleRoofLiveryCount(vehicle) > 0 then
            return true
        end
    elseif modName == 'extras' then 
        for i = 0, 15 do
            if DoesExtraExist(vehicle, i) then
                return true
            end
        end
    elseif modName == 'bulletProofTyres' or modName == 'driftTyres' then -- make sure that these can't be done from menus, only through item
        return false
    else
        local modCount = GetNumVehicleMods(vehicle, Config.Mods[modName].modType)
        if modCount ~= nil and modCount > 0 then
            if modName == 'modSpeakers' or modName == 'modShifterLeavers' then
                for i = 0, modCount, 1 do
                    if IsVehicleModHswExclusive(vehicle, Config.Mods[modName].modType, i) then
                        return false
                    end
                end
            end
            return true
        else
            if modName == 'modTurbo' or modName == 'modXenon' or modName == 'neonEnabled' then 
                return true 
            end
        end
    end
    return false
end

GetVehicleModSlotName = function(vehicle, modName)
    local mod = Config.Mods[modName]
    local modSlotName = nil
    if type(mod.modType) == 'number' then
        modSlotName = GetModSlotName(vehicle, type(mod.modType) == 'number' and mod.modType)
    end
    if modSlotName == nil or modSlotName == '' then 
        return Config.Mods[modName].label
    else
        local modSlotLabel = GetLabelText(modSlotName)
        if modSlotLabel == 'NULL' then
            if modSlotName == 'CMOD_ROOF_ACC' then 
                return 'Roof Accessories'
            else
                return modSlotName 
            end
        else
            return modSlotLabel
        end
    end
end

GetVehicleModVariantLabel = function(vehicle, modName, index)
    local mod = Config.Mods[modName]
    local modLabel = mod.label
    local modText = type(mod.modType) == 'number' and GetModTextLabel(vehicle, mod.modType, index)
    if modText ~= nil and type(modText) == 'string' then
        modLabel = GetLabelText(modText)
    else
        if modName == 'modFrontWheels' or modName == 'modBackWheels' then 
            modLabel = Lang['title_stock_variant_label']..' '..GetVehicleModSlotName(vehicle, modName)
        elseif modName == 'modLivery' or modName == 'modRoofLivery' then 
            if index == -1 then 
                modLabel = Lang['title_stock_variant_label']..' '..GetVehicleModSlotName(vehicle, modName)
            else
                if index == 0 and modName == 'modRoofLivery' then 
                    modLabel = Lang['title_stock_variant_label']..' '..GetVehicleModSlotName(vehicle, modName)
                else
                    local liveryName = GetLiveryName(vehicle, index)
                    modLabel = liveryName ~= nil and GetLabelText(liveryName) or mod.label..' '..index
                end
            end
        elseif mod.variants ~= nil and next(mod.variants) then
            if mod.variants[tostring(index)] ~= nil then
                return mod.variants[tostring(index)].label
            end
        elseif index == -1 then 
            modLabel = Lang['title_stock_variant_label']..' '..GetVehicleModSlotName(vehicle, modName)
        end
    end
    return modLabel
end

GetVehicleModVariants = function(vehicle, modName, modType)
    local modVariants = {}
    local modCount = GetNumVehicleMods(vehicle, modType)
    local negativeIndex = false

    local GetModCountFromConfig = function(table)
        local count = 0
        for k,v in pairs(table) do
            count = count + 1
        end
        return count
    end

    if modName == 'plateIndex' then
        modCount = GetNumberOfVehicleNumberPlates() - 1
    elseif modName == 'windowTint' then
        modCount = GetNumVehicleWindowTints()
    elseif modName == 'extras' then
        modCount = 14
    elseif modName == 'modTurbo' or modName == 'modXenon' then 
        modCount = 1
    elseif modName == 'xenonColor' or modName == 'neonEnabled' or modName == 'neonColor' or modName == 'modFrontWheels' or modName == 'modBackWheels' or modName == 'tyreSmokeColor' then
        if modName == 'xenonColor' or modName == 'neonColor' then 
            negativeIndex = true
        end
        modCount = GetModCountFromConfig(Config.Mods[modName].variants) - 1
    elseif modName == 'modLivery' then
        local newCount = GetVehicleLiveryCount(vehicle)
        if newCount ~= -1 then
            if type(modCount) == "number" and modCount > 0 then
                modCount = newCount
            else
                modCount = modCount + newCount
            end
        end
        negativeIndex = true
        if newCount > 0 then 
            negativeIndex = true
        end
    elseif modName == 'modRoofLivery' then 
        modCount = GetVehicleRoofLiveryCount(vehicle) - 1
    else
        negativeIndex = true
    end

    if modCount ~= nil and modCount > 0 then
        for i = 0, modCount, 1 do
            local index = negativeIndex and tonumber(i-1) or i

            if modName == 'extras' and not DoesExtraExist(vehicle, index) then
                goto skipLoop
            end
            
            local modLabel = ((modName == 'modFrontWheels' or modName == 'modBackWheels') and Config.Mods[modName].variants[tostring(index)].label)
            or modName == 'extras' and GetVehicleModSlotName(vehicle, modName)
            or GetVehicleModVariantLabel(vehicle, modName, index)

            local modValue = index
            if not IsVehicleModHswExclusive(vehicle, modType, index) then
                modVariants[#modVariants + 1] = {
                    modName = modName,
                    modType = modType,
                    modLabel = modLabel,
                    modValue = modValue
                }
            end

            ::skipLoop::
        end
    end
    return modVariants
end

GetVehicleColorVariants = function(vehicle, modName)
    local variants = {}
    for i = 1, #Config.ColorCategories do
        variants[#variants + 1] = {
            modName = modName,
            modType = Config.Mods[modName].modType,
            modLabel = Config.ColorCategories[i].label,
            modValue = Config.ColorCategories[i].name,
            modPaintType = Config.ColorCategories[i].paintType,
            modPrice = Config.ColorCategories[i].price,
        }
    end
    return variants
end

ModPreviewOpenDoors = function(table, vehicle, modName)
    local matchFound = false
    for k,v in pairs(table) do
        if v.args.modName == modName then
            if modName == 'dashboardColor' or modName == 'interiorColor' then
                for i = 0, 3, 1 do 
                    local valid, isOpen = GetIsDoorValid(vehicle, i), IsVehicleDoorFullyOpen(vehicle, i)
                    if valid or not isOpen then
                        SetVehicleDoorOpen(vehicle, i, false, false)
                    end
                end
                matchFound = true
            elseif modName == 'modXenon' or modName == 'xenonColor' or modName == 'customXenon' or modName == 'neonEnabled' or modName == 'neonColor' then 
                SetVehicleDoorOpen(vehicle, 4, false, false)
                matchFound = true
            end
            local label = v.args.labels
            if label ~= nil and type(label) == 'string' then
                local doors = string.match(label, 'Cage') or string.match(label, 'Roll') or string.match(label, 'Chassis')
                local modType = v.args.modType
                local interior = modType == 27 or modType == 28 or modType == 29 or modType == 30 or modType == 31 or modType == 32 or modType == 33 or modType == 34 or modType == 35 or modType == 36 or modType == 37
                if doors or interior then
                    for i = 0, 3, 1 do 
                        if GetIsDoorValid(vehicle, i) or not IsVehicleDoorFullyOpen(vehicle, i) then 
                            SetVehicleDoorOpen(vehicle, i, false, false)
                        end
                        matchFound = true
                    end 
                end
                local hood = string.match(label, 'Engine') or string.match(label, 'Intercooler') or string.match(label, 'Strut') or string.match(label, 'Brace') or string.match(label, 'Filter') or string.match(label, 'Block') or string.match(label, 'Rail Cover') or string.match(label, 'Cambelt')
                if hood ~= nil then
                    SetVehicleDoorOpen(vehicle, 4, false, false)
                    matchFound = true
                end
                local trunk = string.match(label, 'Subwoofer') or string.match(label, 'Speaker') or string.match(label, 'Hydro') or string.match(label, 'Hydraul')
                if trunk ~= nil then
                    SetVehicleDoorOpen(vehicle, 5, false, false)
                    matchFound = true
                end
            end
            if matchFound then 
                break 
            end
        end
    end
end


