Citizen.CreateThread(function ()
    while GetResourceState('t1ger_lib') ~= 'started' do Citizen.Wait(0) end
    if GetResourceState('t1ger_lib') == 'started' then
        InitializeResource()
    end
end)

RegisterServerEvent('tuningsystem:server:playerLoaded')
AddEventHandler('tuningsystem:server:playerLoaded', function()
    local src = Core.Player.GetSource(source)
    local identifier = Core.Player.GetIdentifier(src)
    local playerTunerId = IsEmployeeInShop(identifier)
    local isAdmin = Core.Player.IsAdmin(src)
    TriggerClientEvent('tuningsystem:client:isAdmin', src, isAdmin)
    if Config.Shops[playerTunerId] ~= nil and (Config.Shops[playerTunerId].boss == identifier or Config.Shops[playerTunerId].getBossGrade() == Core.Player.GetPlayerJob(src).grade) then 
        TriggerClientEvent('tuningsystem:client:setPlayerTunerId', src, playerTunerId, true)
    else
        TriggerClientEvent('tuningsystem:client:setPlayerTunerId', src, playerTunerId, false)
    end
    TriggerClientEvent('tuningsystem:client:updateShops', src, Config.Shops)
    TriggerClientEvent('tuningsystem:client:createShopBlips', src, Config.Shops)
    TriggerClientEvent('tuningsystem:client:createShopMarkers', src, Config.Shops)

    -- Useable Items:
    CreateUseableItems()
end)

lib.callback.register('tuningsystem:server:getShopsConfig', function(source)
    return Config.Shops
end)

CreateUseableItems = function()
    -- For mods installation:
    for k,v in pairs( Config.Items['mods']) do
        Core.UseableItem(v.name, function(src, item)
            if item == nil then
                RconPrint('[^1ERROR #5383^0 UseableItem ('..v.name..') does not exists in your items.\n')
            else
                TriggerClientEvent('tuningsystem:client:useableModItem', src, v.name, v.label)
            end
        end)
    end
    for k,v in pairs(Config.Items['kits']) do
        Core.UseableItem(v.name, function(src, item)
            if item == nil then
                RconPrint('[^1ERROR #5383^0 UseableItem ('..v.name..') does not exists in your items.\n')
            else
                if k == 1 then 
                    TriggerClientEvent('tuningsystem:client:tunerTablet', src, k, v)
                elseif k == 2 then 
                    TriggerClientEvent('tuningsystem:client:useRepairKit', src, k, v)
                elseif k == 3 then 
                    TriggerClientEvent('tuningsystem:client:useEngineHoist', src, k, v)
                elseif k == 4 then 
                    TriggerClientEvent('tuningsystem:client:useNitrousBottle', src, k, v)
                elseif k == 5 then 
                    TriggerClientEvent('tuningsystem:client:refillNitrousBottle', src, k, v)
                elseif k == 6 then 
                    TriggerClientEvent('tuningsystem:client:useNitrousPurgeDye', src, k, v)
                end
            end
        end)
    end
end

Core.RegisterCallback('tuningsystem:server:isBoss', function(src, cb, id)
    local isBoss = false
    local src = Core.Player.GetSource(source)
    local identifier = Core.Player.GetIdentifier(src)
    if Config.Shops[id].boss == identifier then
        isBoss = true 
    end
    cb(isBoss)
end)

RegisterServerEvent('tuningsystem:server:createShop')
AddEventHandler('tuningsystem:server:createShop', function(data)
    local src = source

    local job = {
        name = data.job_name,
        label = data.job_label,
        defaultDuty = Config.ShopCreator.DefaultDuty,
        offDutyPay = Config.ShopCreator.OffDutyPay,
        grades = {},
    }

    for k,v in pairs(Config.ShopCreator.Grades) do
        job.grades[tostring(v.grade)] = {name = v.name, label = v.label, grade = v.grade, salary = v.salary, isboss = (v.isboss or nil)}
    end

    local callback = Core.CreateJob(job)

    if callback ~= nil and type(callback) == 'table' and next(callback) then
        job.label = callback.label
        if Framework == 'QB' then
            for k,v in pairs(callback.grades) do 
                job.grades[tostring(k)] = {name = v.name, label = v.name, grade = k, salary = v.payment, isboss = (v.isboss or nil)}
            end
        else
            local boss_grade = 0
            for k,v in pairs(callback.grades) do
                if v.grade > boss_grade then
                    boss_grade = v.grade
                end
                if v.isboss == 0 then
                    v.isboss = nil
                end
                job.grades[tostring(v.grade)] = {name = v.name, label = v.label, grade = v.grade, salary = v.salary, isboss = (v.isboss or nil)}
            end
            job.grades[tostring(boss_grade)].isboss = true
        end
    end

    -- Create Society Account:
    if Config.ShopCreator.UseSocietyAccounts then
        local account = Core.CreateSharedAccount({name = job.name, label = job.label}, data.account)
    end
    
    local startBalance = 0
    if Config.ShopCreator.UseSocietyAccounts then
        startBalance = Core.GetSharedAccountBalance(job.name)
        if startBalance == 0 and data.account ~= nil and data.account > 0 then
            startBalance = data.account
            Core.AddSharedAccountMoney(job.name, startBalance)
        end
    else
        startBalance = data.account or 0
    end

    if Config.Debug then 
        print("Account initialized with starting balance: "..startBalance.." for: "..job.name)
    end

    local employees, storage, billing, categories, orders = {}, {}, {}, data.categories, {}

    local blip_cfg = Config.ShopCreator.Blip
    local blip = { enable = data.blip_use, coords = data.blip_coords, sprite = data.blip_sprite or blip_cfg.sprite, display = blip_cfg.display, scale = blip_cfg.scale, color = data.blip_color or blip_cfg.color }

    local markers = {}
    for k,v in pairs(Config.Markers) do
        if v.enable then
            markers[tostring(k)] = {}
        end
    end

    MySQL.Async.insert('INSERT INTO '..database_table..' (name, account, job, blip, employees, markers, categories, storage, billing, orders) VALUES (@name, @account, @job, @blip, @employees, @markers, @categories, @storage, @billing, @orders)', {
        ['@name'] = data.shop_name,
        ['@account'] = startBalance,
        ['@job'] = json.encode(job),
        ['@blip'] = json.encode(blip),
        ['@employees'] = json.encode(employees),
        ['@markers'] = json.encode(markers),
        ['@categories'] = json.encode(categories),
        ['@storage'] = json.encode(storage),
        ['@billing'] = json.encode(billing),
        ['@orders'] = json.encode(orders),
    }, function(insertId)
        local shopId = insertId
        local args = {id = shopId, name = data.shop_name, account = startBalance, boss = nil, job = job, blip = blip, employees = employees, markers = markers, categories = categories, storage = storage, billing = billing, markup = Config.Markup.Default, orders = orders}
        Config.Shops[shopId] = CreateShopClass(args)
        TriggerClientEvent('tuningsystem:client:updateShopConfig', -1, shopId, Config.Shops[shopId])
        TriggerClientEvent('tuningsystem:client:createShopBlip', -1, shopId, Config.Shops[shopId])
        Core.Notification(src, {
            title = '',
            message = Lang['you_created_tuner_shop']:format(data.shop_name),
            type = 'success'
        })
        Core.Notification(src, {
            title = '',
            message = Lang['you_created_new_job']:format(job.name, job.label),
            type = 'success'
        })
    end)
end)

RegisterServerEvent('tuningsystem:server:deleteShop')
AddEventHandler('tuningsystem:server:deleteShop', function(id)
    local src, shop = source, Config.Shops[id]

    if not Core.Player.IsAdmin(src) then
        local identifier = Core.Player.GetIdentifier(src)
        return print("player: "..identifier..' tried deleting a shop')
    end

    -- update jobs for online players:
    local onlinePlayers = Core.GetOnlinePlayers()
    for id, player in pairs(onlinePlayers) do
        if player.job.name == shop.job.name then
            Core.Player.SetPlayerJob(player.source, 'unemployed', 0)
        end
    end

    -- delete the shop:
    Config.Shops[shop.id] = nil
    MySQL.Async.execute('DELETE FROM '..database_table..' WHERE id = @id', {['@id'] = shop.id}, function(affectedRows)
        TriggerClientEvent('tuningsystem:client:deleteShopBlip', -1, shop.id)
        TriggerClientEvent('tuningsystem:client:updateShops', -1, Config.Shops)
        Core.Notification(src, {
            title = '',
            message = Lang['you_deleted_shop_x']:format(shop.name),
            type = 'inform'
        })
    end)
end)

RegisterServerEvent('tuningsystem:server:setAccount')
AddEventHandler('tuningsystem:server:setAccount', function(shopId, amount)
    Config.Shops[shopId].setMoney(amount)
    Core.Notification(source, {
        title = '',
        message = Lang['you_sat_account_balance']:format(amount),
        type = 'success'
    })
end)

RegisterServerEvent('tuningsystem:server:setCategories')
AddEventHandler('tuningsystem:server:setCategories', function(shopId, categories)
    Config.Shops[shopId].setCategories(categories)
    Core.Notification(source, {
        title = '',
        message = Lang['you_sat_shop_categories'],
        type = 'success'
    })
end)

RegisterServerEvent('tuningsystem:server:setDeliveryCoords')
AddEventHandler('tuningsystem:server:setDeliveryCoords', function(shopId, pos)
    Config.Shops[shopId].setDelivery(pos)
    Core.Notification(source, {
        title = '',
        message = Lang['you_sat_shop_delivery_pos'],
        type = 'success'
    })
end)

RegisterServerEvent('tuningsystem:server:setBoss')
AddEventHandler('tuningsystem:server:setBoss', function(shopId, player, remove)
    local src = source
    -- Remove Current Boss:
    if remove ~= nil then
        Config.Shops[shopId].removeEmployee(player)
        Config.Shops[shopId].setBoss(nil)
        return Core.Notification(src, {
            title = '',
            message = Lang['you_removed_boss'],
            type = 'inform'
        })
    end
    -- Get Boss Grade:
    local boss_grade = Config.Shops[shopId].getBossGrade()
    -- remove old boss:
    Config.Shops[shopId].removeEmployee(Config.Shops[shopId].boss)
    -- check if boss is already hired in another shop:
    local isAlreadyBoss, hiredShopId = IsPlayerAlreadyBoss(player.identifier)
    if isAlreadyBoss then
        Config.Shops[hiredShopId].removeEmployee(player.identifier)
    end
    -- hire new boss
    local tryHire = Config.Shops[shopId].addEmployee(player.source, boss_grade)
    if not tryHire then
        local tryUpdate = Config.Shops[shopId].updateEmployee(player.identifier, boss_grade)
        if not tryUpdate then 
            error('something wtih setBoss is totally wrong: ', shopId, player.identifier)
        end
    end
    -- update new boss:
    Config.Shops[shopId].setBoss(player.identifier)
    -- msg notification:
    Core.Notification(src, {
        title = '',
        message = Lang['you_assigned_new_boss']:format(Core.Player.GetFullName(player.source), Config.Shops[shopId].name),
        type = 'inform'
    })
end)

RegisterServerEvent('tuningsystem:server:toggleDuty')
AddEventHandler('tuningsystem:server:toggleDuty', function(onDuty, shopId)
    local src = Core.Player.GetSource(source)
    local playerJob = Core.Player.GetPlayerJob(src)
    local identifier = Core.Player.GetIdentifier(src)
    if onDuty then 
        Core.Player.SetPlayerJob(src, 'unemployed', 0)
        Core.Notification(src, {
            title = '',
            message = Lang['you_clocked_off_duty'],
            type = 'inform'
        })
    else
        if Config.Shops[shopId] ~= nil and next(Config.Shops[shopId]) then
            local employee, callback = Config.Shops[shopId].getEmployee(identifier)
            if employee then
                Core.Player.SetPlayerJob(src, Config.Shops[shopId].job.name, callback.grade)
                Core.Notification(src, {
                    title = '',
                    message = Lang['you_clocked_on_duty'],
                    type = 'success'
                })
            end
        end
    end
end)

RegisterServerEvent('tuningsystem:server:depositAccount')
AddEventHandler('tuningsystem:server:depositAccount', function(shopId, amount, remove)
    local src = Core.Player.GetSource(source)
    local playerMoney = Core.Player.GetMoney(source)
    if playerMoney >= amount then
        Core.Player.RemoveMoney(src, amount)
        Config.Shops[shopId].addMoney(amount)
        Core.Notification(src, {
            title = '',
            message = Lang['money_deposited']:format(amount),
            type = 'success'
        })
    else
        return Core.Notification(src, {
            title = '',
            message = Lang['not_enough_money'],
            type = 'error'
        })
    end
end)

RegisterServerEvent('tuningsystem:server:withdrawAccount')
AddEventHandler('tuningsystem:server:withdrawAccount', function(shopId, amount)
    local src = Core.Player.GetSource(source)
    Core.Player.AddMoney(src, amount)
    Config.Shops[shopId].removeMoney(amount)
    Core.Notification(src, {
        title = '',
        message = Lang['money_withdrawn']:format(amount),
        type = 'success'
    })
end)

RegisterServerEvent('tuningsystem:server:fireEmployee')
AddEventHandler('tuningsystem:server:fireEmployee', function(shopId, player)
    Config.Shops[shopId].removeEmployee(player.identifier)
    Core.Notification(source, {
        title = '',
        message = Lang['fired_employee']:format(player.name),
        type = 'success'
    })
end)

RegisterServerEvent('tuningsystem:server:promoteEmployee')
AddEventHandler('tuningsystem:server:promoteEmployee', function(shopId, player, grade)
    Config.Shops[shopId].updateEmployee(player.identifier, grade)
    Core.Notification(source, {
        title = '',
        message = Lang['employee_grade_updated']:format(player.name),
        type = 'success'
    })
end)

RegisterServerEvent('tuningsystem:server:tryRecruit')
AddEventHandler('tuningsystem:server:tryRecruit', function(point, args)
    local src = Core.Player.GetSource(source)
    local targetSrc = Core.Player.GetSource(args.serverId)
    local targetIdentifier = Core.Player.GetIdentifier(targetSrc)

    -- check if player is already hired in another shop:
    local hiredShopId = IsEmployeeInShop(targetIdentifier)
    if hiredShopId > 0 then
        return Core.Notification(src, {
            title = '',
            message = Lang['employee_already_hired']:format(Config.Shops[hiredShopId].name),
            type = 'error'
        })
    end

    TriggerClientEvent('tuningsystem:client:sendRecruitment', targetSrc, point, args, src)
    Core.Notification(src, {
        title = '',
        message = Lang['employee_recruit_sent']:format(Core.Player.GetName(targetSrc)),
        type = 'inform'
    })
end)

RegisterServerEvent('tuningsystem:server:recruitmentRespond')
AddEventHandler('tuningsystem:server:recruitmentRespond', function(point, args, bool, playerSrc)
    local src = Core.Player.GetSource(playerSrc)
    local targetSrc = Core.Player.GetSource(source)
    local targetIdentifier = Core.Player.GetIdentifier(targetSrc)

    if bool == true then

        local tryRecruit = Config.Shops[point.shopId].addEmployee(targetSrc, 0)
        if not tryRecruit then
            local tryUpdate = Config.Shops[point.shopId].updateEmployee(targetIdentifier, 0)
            if not tryUpdate then 
                error('something wtih setBoss is totally wrong: ', point.shopId, targetIdentifier)
            end
        end

        Core.Notification(src, {
            title = '',
            message = Lang['recruitment_accepted2'],
            type = 'success'
        })
    else 
        Core.Notification(src, {
            title = '',
            message = Lang['recruitment_declined2'],
            type = 'inform'
        })
    end
end)

Core.RegisterCallback('tuningsystem:server:getPlayerVehicles', function(src, cb)
	local playerSrc = Core.Player.GetSource(src)
    local vehicles = Core.Player.GetAllVehicles(playerSrc)
    cb(vehicles)
end)

RegisterServerEvent('tuningsystem:server:depositStorage')
AddEventHandler('tuningsystem:server:depositStorage', function(shopId, markerId, itemName, amount)
    local src = Core.Player.GetSource(source)
    Core.Player.RemoveItem(src, itemName, amount)
    Config.Shops[shopId].depositStorage(markerId, itemName, amount)
    Core.Notification(source, {
        title = '',
        message = Lang['deposit_item_success']:format(amount, itemName),
        type = 'success'
    })
end)

RegisterServerEvent('tuningsystem:server:withdrawStorage')
AddEventHandler('tuningsystem:server:withdrawStorage', function(shopId, markerId, itemName, amount)
    local src = Core.Player.GetSource(source)
    Core.Player.AddItem(src, itemName, amount)
    Config.Shops[shopId].withdrawStorage(markerId, itemName, amount)
    Core.Notification(source, {
        title = '',
        message = Lang['withdraw_item_success']:format(amount, itemName),
        type = 'success'
    })
end)


Core.RegisterCallback('tuningsystem:server:hasMaterials', function(src, cb, materials)
    local missingItems = {}

    for k,v in pairs(materials) do
        local hasItem, count = Core.Player.HasItem(src, v.name, v.amount)
        if not hasItem then 
            missingItems[#missingItems + 1] = {name = v.name, label = v.label, invAmount = count, reqAmount = v.amount, diffAmount = (v.amount - count)}
        end
    end

    if #missingItems > 0 then
        cb(false, missingItems)
    else
        cb(true, nil)
    end

end)

RegisterServerEvent('tuningsystem:server:craftItem')
AddEventHandler('tuningsystem:server:craftItem', function(materials, output)
    local src = Core.Player.GetSource(source)

    for i = 1, #materials do 
        Core.Player.RemoveItem(src, materials[i].name, materials[i].amount)
        Wait(10)
    end

    Core.Player.AddItem(src, output.name, 1)
    Core.Notification(src, {
        title = '',
        message = Lang['you_crafted_x_item']:format(1, output.label),
        type = 'success'
    })
end)

Core.RegisterCallback('tuningsystem:server:laptopOrder', function(src, cb, itemName, total, quantity, shopId, storageId)
    local shopAccount = Config.Shops[shopId].getAccount()

    if shopAccount >= total then
        Config.Shops[shopId].removeMoney(total)
        if Config.OrderedItemsToStash == true then
            if Cfg.Inventory == 'default' and Framework == 'ESX' then
                Config.Shops[shopId].depositStorage(storageId, itemName, quantity)
                Core.Notification(src, {
                    title = '',
                    message = Lang['parts_sent_to_storage'],
                    type = 'success'
                })
            else
                local success, response = Core.StashAddItem(storageId, itemName, quantity)
                if not success then
                    if Config.Debug then
                        print("item not added to stash, instead added to player inventory")
                    end
                    Core.Player.AddItem(src, itemName, quantity)
                else
                    Core.Notification(src, {
                        title = '',
                        message = Lang['parts_sent_to_storage'],
                        type = 'success'
                    })
                end
            end
        else
            Core.Player.AddItem(src, itemName, quantity)
        end
        cb(true)
    else
        cb(false)
    end
end)

local usingModsMenu = {}
RegisterServerEvent('tuningsystem:server:usingModsMenu')
AddEventHandler('tuningsystem:server:usingModsMenu', function(netId, props)
    usingModsMenu[tostring(source)] = { netId = netId, props = props }
end)

RegisterServerEvent('tuningsystem:server:resetModsMenu')
AddEventHandler('tuningsystem:server:resetModsMenu', function()
    if usingModsMenu[tostring(source)] ~= nil then
        usingModsMenu[tostring(source)] = nil
    end
end)

AddEventHandler('playerDropped', function (reason)
    local src = Core.Player.GetSource(source)
    if usingModsMenu[tostring(source)] ~= nil then
        local netId = usingModsMenu[tostring(source)].netId
        local props = usingModsMenu[tostring(source)].props
        TriggerClientEvent('tuningsystem:client:resetVehicleMods', -1, netId, props)
        local entity = NetworkGetEntityFromNetworkId(netId)
        DeleteEntity(entity)

        local identifier = Core.Player.GetIdentifier(source)
        print('Player: ' .. GetPlayerName(source) .. ' ['..identifier..'] dropped while using T1GER Mods Menu - Deleted vehicle /w plate: '..props.plate)
        usingModsMenu[tostring(source)] = nil
    end
    local identifier = Core.Player.GetIdentifier(src)
    local playerTunerId = IsEmployeeInShop(identifier)
    if Config.Shops[playerTunerId] ~= nil then
        local orders = Config.Shops[playerTunerId].getOrders()
        if orders ~= nil and next(orders) then
            for plate,data in pairs(orders) do
                if data.tuner ~= nil and data.tuner == identifier then
                    Config.Shops[playerTunerId].orderTakenStatus(plate, false)
                end
            end
        end
    end
end)

RegisterServerEvent('tuningsystem:server:deleteEntity')
AddEventHandler('tuningsystem:server:deleteEntity', function(netId)
    local entity = NetworkGetEntityFromNetworkId(netId)
    DeleteEntity(entity)
    TriggerClientEvent('tuningsystem:client:deleteEntity', -1, netId)
end)


RegisterServerEvent('tuningsystem:server:removeMoney')
AddEventHandler('tuningsystem:server:removeMoney', function(amount, account)
    local src = Core.Player.GetSource(source)
    Core.Player.RemoveMoney(src, amount, account)
end)

Core.RegisterCallback('tuningsystem:server:getShops', function(src, cb)
    cb(Config.Shops)
end)

RegisterServerEvent('tuningsystem:server:createBill')
AddEventHandler('tuningsystem:server:createBill', function(shopId, input)
    local src = Core.Player.GetSource(source)
    local targetSrc = Core.Player.GetSource(input[1])
    local name = Core.Player.GetFullName(targetSrc)

    if Core.Player.GetMoney(targetSrc, 'bank') >= input[2] then
        TriggerClientEvent('tuningsystem:client:sendBill', targetSrc, shopId, input, src)
        Core.Notification(src, {
            title = '',
            message = Lang['bill_sent_to_x']:format(input[2], name),
            type = 'inform'
        })
    else
        Core.Notification(src, {
            title = '', 
            message = Lang['bill_x_no_money']:format(name),
            type = 'inform'
        })
    end
end)

RegisterServerEvent('tuningsystem:server:payBill')
AddEventHandler('tuningsystem:server:payBill', function(shopId, input, bool, playerSrc)
    local src = Core.Player.GetSource(playerSrc)
    local srcIdentifier = Core.Player.GetIdentifier(src)
    local targetSrc = Core.Player.GetSource(source)
    local targetIdentifier = Core.Player.GetIdentifier(targetSrc)
    local targetName = Core.Player.GetFullName(targetSrc)

    if bool == true then 
        Core.Player.RemoveMoney(targetSrc, input[2], 'bank')
        Config.Shops[shopId].addMoney(tonumber(input[2]))
        Config.Shops[shopId].createBill(srcIdentifier, targetIdentifier, tonumber(input[2]), input[3])
        Core.Notification(src, {
            title = '',
            message = Lang['bill_paid_by_x']:format(targetName, input[2]),
            type = 'success'
        })
    else 
        Core.Notification(src, {
            title = '',
            message = Lang['bill_declined_by_x']:format(targetName, input[2]),
            type = 'inform'
        })
    end
end)

RegisterServerEvent('tuningsystem:server:salvageReward')
AddEventHandler('tuningsystem:server:salvageReward', function(part)
    local src = Core.Player.GetSource(source)
    local items = Config.SalvageJob.Reward[part]
    for itemName, v in pairs(items) do
        math.randomseed(GetGameTimer())
        local randomizedChance = math.random(0,100)
        if randomizedChance <= v.chance then 
            math.randomseed(GetGameTimer())
            local amount = math.random(v.min,v.max)
            Core.Player.AddItem(src, itemName, amount)
        end
        Wait(10)
    end
end)

RegisterServerEvent('tuningsystem:server:mobileTuningReward')
AddEventHandler('tuningsystem:server:mobileTuningReward', function(amount)
    Core.Player.AddMoney(source, amount)
end)

RegisterServerEvent('tuningsystem:server:addJobCooldown')
AddEventHandler('tuningsystem:server:addJobCooldown', function(time)
    local src = Core.Player.GetSource(source)
    table.insert(jobCooldown, {identifier = Core.Player.GetIdentifier(src), time = (time * 60000)})
end)

RegisterServerEvent('tuningsystem:server:setEngineSound')
AddEventHandler('tuningsystem:server:setEngineSound', function(netId, soundName, vehicleModel)
    SetEngineSound(netId, soundName, vehicleModel)
end)

RegisterServerEvent('tuningsystem:server:refillNitrousBottle')
AddEventHandler('tuningsystem:server:refillNitrousBottle', function(num)
    local src = Core.Player.GetSource(source)
    -- Check Item:
    local hasItem, count = Core.Player.HasItem(src, Config.Items['kits'][5].name, 1)
    if hasItem then 
        -- Remove Empty Bottle:
        Core.Player.RemoveItem(src, Config.Items['kits'][5].name, 1)
        -- Update Refill Station:
        Config.Nitrous.Refill.points[num].size = (Config.Nitrous.Refill.points[num].size - 1)
        -- Add NOS Bottle:
        Core.Player.AddItem(src, Config.Items['kits'][4].name, 1)
        -- update client
        TriggerClientEvent('tuningsystem:client:updateRefillPoint', -1, num, Config.Nitrous.Refill.points[num])
    else
        return print("couldn\'t find empty nos bottle in inventory")
    end
end)

RegisterServerEvent('tuningsystem:server:nitrousCooldown')
AddEventHandler('tuningsystem:server:nitrousCooldown', function(plate, netId)
    StartNitrousCooldown(plate, netId)
end)

lib.callback.register('tuningsystem:server:hasItem', function(source, itemName, amount)
    local src = Core.Player.GetSource(source)
    local hasItem, count = Core.Player.HasItem(src, itemName, amount)
    return hasItem, count
end)

Core.RegisterCallback('tuningsystem:server:checkJobCooldown', function(src, cb)
    local identifier = Core.Player.GetIdentifier(src)
    if HasCooldown(identifier) then
        local seconds, time = GetCooldownTime(identifier)
        cb(true, seconds, time)
    else
        cb(false)
    end
end)

StartDatabaseSync = function()
    function SaveData()
        SyncTunerShops()
        SetTimeout(Config.SyncToDatabase * 60 * 1000, SaveData)
    end
    SetTimeout(Config.SyncToDatabase * 60 * 1000, SaveData)
end
StartDatabaseSync()
