if not Config.ImpoundVehicle.Enable then 
    return 
end

-- function to impound closest vehicle:
function ImpoundVehicle()
    if not Config.ImpoundVehicle.Enable then return end 
    
    local isMechanic, shopId = IsPlayerMechanic()
    if not isMechanic then return end
    
	-- ray cast:
	local hit, entityHit, endCoords, surfaceNormal, materialHash = lib.raycast.fromCoords(coords, GetOffsetFromEntityInWorldCoords(player, 0.0, 5.0, 0.0), 2)
	if not hit or GetEntityType(entityHit) ~= 2 or not IsEntityAVehicle(entityHit) or not DoesEntityExist(entityHit) then 
		return _API.ShowNotification(locale("notification.no_vehicle_in_direction"), "inform", {})
	end

	local impounded = false 
    local min, max = GetModelDimensions(GetEntityModel(entityHit))
    local interactCoords = GetOffsetFromEntityInWorldCoords(entityHit, min.x-0.2,0.0,0.0)

    while not impounded do
        Wait(1)

        local distance = #(coords - vector3(interactCoords.x, interactCoords.y, interactCoords.z))
        if distance < Config.ImpoundVehicle.DrawDist then
            -- draw text
            Draw3DText(interactCoords.x, interactCoords.y, interactCoords.z, locale("drawtext.impound_vehicle"))

            -- key press
            if IsControlJustReleased(0, Config.ImpoundVehicle.Keybind) then
                if distance <= 1.0 then
                    TaskTurnPedToFaceEntity(player, entityHit, 1.0)
                    Wait(500)
                    SetCurrentPedWeapon(player, GetHashKey("WEAPON_UNARMED"), true)
                    Wait(300)

                    if ProgressBar({
                        duration = Config.ImpoundVehicle.Duration,
                        label = locale("progressbar.impound_vehicle"),
                        useWhileDead = false,
                        canCancel = true,
                        anim = {
                            scenario = Config.ImpoundVehicle.Scenario
                        },
                        disable = {
                            move = true,
                            combat = true
                        }
                    }) then
                        ClearPedTasks(player)
                        impounded = true
                        break
                    end

                else
                    _API.ShowNotification(locale("notification.move_closer_to_interact"), "inform", {})
                end
            end
        end
    end
    
    -- complete?
    if impounded then
        _API.ImpoundVehicle(entityHit)
    end
end

if Config.ImpoundVehicle.Command.enable == true then
    RegisterCommand(Config.ImpoundVehicle.Command.str, function()
        ImpoundVehicle()
    end, false)
end