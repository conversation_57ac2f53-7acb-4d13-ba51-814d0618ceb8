local isWorkflowOpen, isFocusCursor = false, false

-- Function to update UI Focus:
local UpdateFocus = function()
  if isWorkflowOpen then
    SetNuiFocus(true, true)
    SetNuiFocusKeepInput(true)
  else
    SetNuiFocus(false, false)
    SetNuiFocusKeepInput(true)
  end
end

-- Function to update cursor focus:
local ToggleWorkflowCursor = function()
  if isWorkflowOpen then 
    if isFocusCursor then
      SetNuiFocus(false, false)
      SetNuiFocusKeepInput(true)
      isFocusCursor = false
    else
      SetNuiFocus(true, true)
      SetNuiFocusKeepInput(true)
      isFocusCursor = true
    end
  else
    UpdateFocus()
    isFocusCursor = false
  end
end

RegisterCommand('workflow:toggleCursor', function()
  ToggleWorkflowCursor()
end, false)

-- KeyMapping for enable/disable cursor for UI:
RegisterKeyMapping('workflow:toggleCursor', Config.ModOrder.Keymap.description, 'keyboard', Config.ModOrder.Keymap.key)

-- Function to open UI
OpenWorkflowUI = function()
  isWorkflowOpen = true
  UpdateFocus()
  SendNUIMessage({
    action = 'toggle:workflow',
    data = {
      state = true,
    }
  })
end

-- Comamnd to open workflow UI:
RegisterCommand('workflow:open', function()
  OpenWorkflowUI()
end, false)
RegisterKeyMapping('workflow:open', 'Open WorkFlow', 'keyboard', '')
-- Function to close UI
CloseWorkflowUI = function()
  isWorkflowOpen = false
  UpdateFocus()
  SendNUIMessage({
    action = 'toggle:workflow',
    data = {
      state = false,
    }
  })
end

-- Comamnd to close workflow UI:
RegisterCommand('workflow:close', function()
  CloseWorkflowUI()
end, false)
RegisterKeyMapping('workflow:close', 'Close WorkFlow', 'keyboard', '')
-- Function to set data on UI
SetWorkflowData = function(data, title)
  SendNUIMessage({
    action = 'set:workflow',
    data = data
  })
end

-- Function to set workflow title/header
SetWorkflowTitle = function(string)
  SendNUIMessage({
    action = 'set:workflowTitle',
    data = string
  })
end

-- Function to update task complete state:
UpdateWorkflowTask = function(id, state)
  SendNUIMessage({
    action = 'update:workflow',
    data = {
      id = id,
      completed = state
    }
  })
end

-- Function to remove a task from workflow:
RemoveWorkflowTask = function(id)
  SendNUIMessage({
    action = 'remove:workflow',
    data = {
      id = id
    }
  })
end

-- Function to clear the entire workflow
ClearWorkflowTasks = function()
  SendNUIMessage({
    action = 'clear:workflow'
  })
end

-- Callback function for closing the workflow UI
RegisterNUICallback('nui:close:workflow', function()
  isWorkflowOpen = false
  UpdateFocus()
end)

-- Callback to get progress:
RegisterNUICallback('nui:client:workflow:setProgress', function(newValue, cb)
  TriggerEvent('tuningsystem:client:updateModOrderProgress', newValue)
  cb('ok')
end)