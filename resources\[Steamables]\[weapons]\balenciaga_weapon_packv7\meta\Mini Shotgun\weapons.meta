<?xml version="1.0" encoding="UTF - 8"?>

<CWeaponInfoBlob>
	<SlotNavigateOrder>
		<Item>
			<WeaponSlots>
				<Item>
					<OrderNumber value="402"/>
					<Entry>SLOT_WEAPON_REMINGTONB</Entry>
				</Item>
			</WeaponSlots>
		</Item>
	</SlotNavigateOrder>
	<Infos>
		<Item>
			<Infos>
				<Item type="CWeaponInfo">
					<Name>WEAPON_REMINGTONB</Name>
					<Model>w_sg_remingtonb</Model>
					<Audio>AUDIO_ITEM_SAWNOFFSHOTGUN</Audio>
					<Slot>SLOT_WEAPON_REMINGTONB</Slot>
					<DamageType>BULLET</DamageType>
					<Explosion>
						<Default>DONTCARE</Default>
						<HitCar>DONTCARE</HitCar>
						<HitTruck>DONTCARE</HitTruck>
						<HitBike>DONTCARE</HitBike>
						<HitBoat>DONTCARE</HitBoat>
						<HitPlane>DONTCARE</HitPlane>
					</Explosion>
					<FireType>INSTANT_HIT</FireType>
					<WheelSlot>WHEEL_SHOTGUN</WheelSlot>
					<Group>GROUP_SHOTGUN</Group>
					<AmmoInfo ref="AMMO_SHOTGUN"/>
					<AimingInfo ref="RIFLE_LO_PUMP_STRAFE"/>
					<ClipSize value="8"/>
					<AccuracySpread value="2.500000"/>
					<AccurateModeAccuracyModifier value="0.500000"/>
					<RunAndGunAccuracyModifier value="2.000000"/>
					<RunAndGunAccuracyMaxModifier value="1.000000"/>
					<RecoilAccuracyMax value="1.000000"/>
					<RecoilErrorTime value="0.000000"/>
					<RecoilRecoveryRate value="1.000000"/>
					<RecoilAccuracyToAllowHeadShotAI value="1000.000000"/>
					<MinHeadShotDistanceAI value="1000.000000"/>
					<MaxHeadShotDistanceAI value="1000.000000"/>
					<HeadShotDamageModifierAI value="1000.000000"/>
					<RecoilAccuracyToAllowHeadShotPlayer value="0.175000"/>
					<MinHeadShotDistancePlayer value="5.000000"/>
					<MaxHeadShotDistancePlayer value="40.000000"/>
					<HeadShotDamageModifierPlayer value="18.000000"/>
					<Damage value="40.000000"/>
					<DamageTime value="0.000000"/>
					<DamageTimeInVehicle value="0.000000"/>
					<DamageTimeInVehicleHeadShot value="0.000000"/>
					<HitLimbsDamageModifier value="0.500000"/>
					<NetworkHitLimbsDamageModifier value="0.800000"/>
					<LightlyArmouredDamageModifier value="0.750000"/>
					<Force value="14.000000"/>
					<ForceHitPed value="50.000000"/>
					<ForceHitVehicle value="350.000000"/>
					<ForceHitFlyingHeli value="120.000000"/>
					<OverrideForces>
						<Item>
							<BoneTag>BONETAG_HEAD</BoneTag>
							<ForceFront value="25.000000"/>
							<ForceBack value="15.000000"/>
						</Item>
						<Item>
							<BoneTag>BONETAG_NECK</BoneTag>
							<ForceFront value="30.000000"/>
							<ForceBack value="20.000000"/>
						</Item>
						<Item>
							<BoneTag>BONETAG_R_CLAVICLE</BoneTag>
							<ForceFront value="20.000000"/>
							<ForceBack value="20.000000"/>
						</Item>
						<Item>
							<BoneTag>BONETAG_L_CLAVICLE</BoneTag>
							<ForceFront value="20.000000"/>
							<ForceBack value="20.000000"/>
						</Item>
					</OverrideForces>
					<ForceMaxStrengthMult value="1.000000"/>
					<ForceFalloffRangeStart value="0.000000"/>
					<ForceFalloffRangeEnd value="50.000000"/>
					<ForceFalloffMin value="1.000000"/>
					<ProjectileForce value="0.000000"/>
					<FragImpulse value="500.000000"/>
					<Penetration value="0.010000"/>
					<VerticalLaunchAdjustment value="0.000000"/>
					<DropForwardVelocity value="0.000000"/>
					<Speed value="2000.000000"/>
					<BulletsInBatch value="8"/>
					<BatchSpread value="0.100000"/>
					<ReloadTimeMP value="-1.000000"/>
					<ReloadTimeSP value="-1.000000"/>
					<VehicleReloadTime value="1.500000"/>
					<AnimReloadRate value="1.000000"/>
					<BulletsPerAnimLoop value="2"/>
					<TimeBetweenShots value="1.000000"/>
					<TimeLeftBetweenShotsWhereShouldFireIsCached value="-1.000000"/>
					<SpinUpTime value="0.000000"/>
					<SpinTime value="0.000000"/>
					<SpinDownTime value="0.000000"/>
					<AlternateWaitTime value="-1.000000"/>
					<BulletBendingNearRadius value="0.000000"/>
					<BulletBendingFarRadius value="0.000000"/>
					<BulletBendingZoomedRadius value="0.375000"/>
					<Fx>
						<EffectGroup>WEAPON_EFFECT_GROUP_SHOTGUN</EffectGroup>
						<FlashFx>muz_shotgun</FlashFx>
						<FlashFxAlt/>
						<MuzzleSmokeFx>muz_smoking_barrel_shotgun</MuzzleSmokeFx>
						<MuzzleSmokeFxFP>muz_smoking_barrel_fp</MuzzleSmokeFxFP>
						<MuzzleSmokeFxMinLevel value="0.300000"/>
						<MuzzleSmokeFxIncPerShot value="0.100000"/>
						<MuzzleSmokeFxDecPerSec value="0.200000"/>
						<ShellFx>eject_shotgun</ShellFx>
						<ShellFxFP>eject_shotgun_fp</ShellFxFP>
						<TracerFx/>
						<PedDamageHash>ShotgunLarge</PedDamageHash>
						<TracerFxChanceSP value="0.150000"/>
						<TracerFxChanceMP value="0.750000"/>
						<FlashFxChanceSP value="1.000000"/>
						<FlashFxChanceMP value="1.000000"/>
						<FlashFxAltChance value="0.000000"/>
						<FlashFxScale value="0.700000"/>
						<FlashFxLightEnabled value="true"/>
						<FlashFxLightCastsShadows value="false"/>
						<FlashFxLightOffsetDist value="0.000000"/>
						<FlashFxLightRGBAMin x="255.000000" y="93.000000" z="25.000000"/>
						<FlashFxLightRGBAMax x="255.000000" y="100.000000" z="50.000000"/>
						<FlashFxLightIntensityMinMax x="1.000000" y="2.000000"/>
						<FlashFxLightRangeMinMax x="2.00000" y="2.500000"/>
						<FlashFxLightFalloffMinMax x="1024.000000" y="1536.000000"/>
						<GroundDisturbFxEnabled value="false"/>
						<GroundDisturbFxDist value="5.000000"/>
						<GroundDisturbFxNameDefault/>
						<GroundDisturbFxNameSand/>
						<GroundDisturbFxNameDirt/>
						<GroundDisturbFxNameWater/>
						<GroundDisturbFxNameFoliage/>
					</Fx>
					<InitialRumbleDuration value="0"/>
					<InitialRumbleIntensity value="0.000000"/>
					<InitialRumbleIntensityTrigger value="0.950000"/>
					<RumbleDuration value="200"/>
					<RumbleIntensity value="1.000000"/>
					<RumbleIntensityTrigger value="0.950000"/>
					<RumbleDamageIntensity value="1.000000"/>
					<InitialRumbleDurationFps value="250"/>
					<InitialRumbleIntensityFps value="1.000000"/>
					<RumbleDurationFps value="250"/>
					<RumbleIntensityFps value="0.900000"/>
					<NetworkPlayerDamageModifier value="1.000000"/>
					<NetworkPedDamageModifier value="1.000000"/>
					<NetworkHeadShotPlayerDamageModifier value="1.000000"/>
					<LockOnRange value="35.000000"/>
					<WeaponRange value="40.000000"/>
					<BulletDirectionOffsetInDegrees value="0.000000"/>
					<AiSoundRange value="-1.000000"/>
					<AiPotentialBlastEventRange value="-1.000000"/>
					<DamageFallOffRangeMin value="10.000000"/>
					<DamageFallOffRangeMax value="40.000000"/>
					<DamageFallOffModifier value="0.300000"/>
					<VehicleWeaponHash/>
					<DefaultCameraHash>SHOTGUN_AIM_CAMERA</DefaultCameraHash>
					<CoverCameraHash>SHOTGUN_AIM_IN_COVER_CAMERA</CoverCameraHash>
					<CoverReadyToFireCameraHash/>
					<RunAndGunCameraHash>SHOTGUN_RUN_AND_GUN_CAMERA</RunAndGunCameraHash>
					<CinematicShootingCameraHash>SHOTGUN_CINEMATIC_SHOOTING_CAMERA</CinematicShootingCameraHash>
					<AlternativeOrScopedCameraHash/>
					<RunAndGunAlternativeOrScopedCameraHash/>
					<CinematicShootingAlternativeOrScopedCameraHash/>
					<CameraFov value="45.000000"/>
					<FirstPersonScopeFov value="30.00000"/>
					<FirstPersonRNGOffset x="0.000000" y="0.000000" z="0.000000"/>
					<FirstPersonRNGRotationOffset x="0.000000" y="0.000000" z="0.000000"/>
					<FirstPersonLTOffset x="0.000000" y="0.000000" z="0.000000"/>
					<FirstPersonLTRotationOffset x="0.000000" y="0.000000" z="0.000000"/>
					<FirstPersonScopeOffset x="0.00000" y="0.1500" z="0.0170"/>
					<FirstPersonScopeAttachmentOffset x="0.00000" y="0.0000" z="0.0150"/>
					<FirstPersonScopeRotationOffset x="-1.00000" y="0.0000" z="0.0000"/>
					<FirstPersonScopeAttachmentRotationOffset x="0.00000" y="0.0000" z="0.0000"/>
					<FirstPersonAsThirdPersonIdleOffset x="-0.050000" y="0.000000" z="-0.050000"/>
					<FirstPersonAsThirdPersonRNGOffset x="0.000000" y="0.000000" z="-0.075000"/>
					<FirstPersonAsThirdPersonLTOffset x="0.000000" y="0.000000" z="-0.0500000"/>
					<FirstPersonAsThirdPersonScopeOffset x="0.050" y="0.000000" z="-0.0500000"/>
					<FirstPersonAsThirdPersonWeaponBlockedOffset x="-0.1000000" y="0.000000" z="-0.100000"/>
					<FirstPersonDofSubjectMagnificationPowerFactorNear value="1.025000"/>
					<FirstPersonDofMaxNearInFocusDistance value="0.000000"/>
					<FirstPersonDofMaxNearInFocusDistanceBlendLevel value="0.300000"/>
					<ZoomFactorForAccurateMode value="1.000000"/>
					<RecoilShakeHash>SHOTGUN_RECOIL_SHAKE</RecoilShakeHash>
					<RecoilShakeHashFirstPerson>FPS_SHOTGUN_RECOIL_SHAKE</RecoilShakeHashFirstPerson>
					<AccuracyOffsetShakeHash/>
					<MinTimeBetweenRecoilShakes value="150"/>
					<RecoilShakeAmplitude value="2.250000"/>
					<ExplosionShakeAmplitude value="-1.000000"/>
					<ReticuleHudPosition x="0.000000" y="0.000000"/>
					<AimOffsetMin x="0.235000" y="0.125000" z="0.500000"/>
					<AimProbeLengthMin value="0.365000"/>
					<AimOffsetMax x="0.185000" y="0.000000" z="0.575000"/>
					<AimProbeLengthMax value="0.230000"/>
					<AimOffsetMinFPSIdle x="0.162000" y="0.225000" z="0.052000"/>
					<AimOffsetMedFPSIdle x="0.187000" y="0.197000" z="0.321000"/>
					<AimOffsetMaxFPSIdle x="0.155000" y="0.038000" z="0.364000"/>
					<AimOffsetEndPosMinFPSIdle x="-0.284000" y="0.612000" z="-0.205000"/>
					<AimOffsetEndPosMedFPSIdle x="-0.178000" y="0.639000" z="0.616000"/>
					<AimOffsetEndPosMaxFPSIdle x="-0.21700" y="-0.096000" z="0.887000"/>
					<AimOffsetMinFPSLT x="0.180000" y="0.231000" z="0.669000"/>
					<AimOffsetMaxFPSLT x="0.048000" y="-0.225000" z="0.409000"/>
					<AimOffsetMinFPSRNG x="0.120000" y="0.275000" z="0.509000"/>
					<AimOffsetMaxFPSRNG x="0.138000" y="-0.212000" z="0.518000"/>
					<AimOffsetMinFPSScope x="0.090000" y="0.078000" z="0.531000"/>
					<AimOffsetMaxFPSScope x="0.006000" y="-0.059000" z="0.694000"/>
					<TorsoAimOffset x="-1.300000" y="0.550000"/>
					<TorsoCrouchedAimOffset x="0.150000" y="0.050000"/>
					<LeftHandIkOffset x="0.200000" y="0.050000" z="-0.050000"/>
					<ReticuleMinSizeStanding value="1.000000"/>
					<ReticuleMinSizeCrouched value="1.000000"/>
					<ReticuleScale value="0.000000"/>
					<ReticuleStyleHash>WEAPONTYPE_SHOTGUN</ReticuleStyleHash>
					<FirstPersonReticuleStyleHash/>
					<PickupHash>PICKUP_WEAPON_SAWNOFFSHOTGUN</PickupHash>
					<MPPickupHash>PICKUP_AMMO_BULLET_MP</MPPickupHash>
					<HumanNameHash>WEAPON_REMINGTONB</HumanNameHash>
					<MovementModeConditionalIdle>MMI_2Handed</MovementModeConditionalIdle>
					<StatName>SAWNOFF</StatName>
					<KnockdownCount value="-1"/>
					<KillshotImpulseScale value="1.000000"/>
					<NmShotTuningSet>Shotgun</NmShotTuningSet>
					<AttachPoints/>
					<GunFeedBone/>
					<TargetSequenceGroup/>
					<WeaponFlags>CarriedInHand ApplyBulletForce Gun CanLockonOnFoot CanLockonInVehicle CanFreeAim TwoHanded AnimReload AnimCrouchFire UsableOnFoot UsableInCover NoLeftHandIKWhenBlocked AllowCloseQuarterKills NeedsGunCockingInCover HasLowCoverReloads HasLowCoverSwaps ProcessGripAnim UseFPSAimIK UseFPSSecondaryMotion</WeaponFlags>
					<TintSpecValues ref="TINT_DEFAULT"/>
					<FiringPatternAliases ref="FIRING_PATTERN_PUMPSHOTGUN"/>
					<ReloadUpperBodyFixupExpressionData ref="default"/>
					<AmmoDiminishingRate value="3"/>
					<AimingBreathingAdditiveWeight value="1.000000"/>
					<FiringBreathingAdditiveWeight value="1.000000"/>
					<StealthAimingBreathingAdditiveWeight value="1.000000"/>
					<StealthFiringBreathingAdditiveWeight value="1.000000"/>
					<AimingLeanAdditiveWeight value="1.000000"/>
					<FiringLeanAdditiveWeight value="1.000000"/>
					<StealthAimingLeanAdditiveWeight value="1.000000"/>
					<StealthFiringLeanAdditiveWeight value="1.000000"/>
					<ExpandPedCapsuleRadius value="0.000000"/>
					<AudioCollisionHash/>
					<HudDamage value="96"/>
					<HudSpeed value="20"/>
					<HudCapacity value="10"/>
					<HudAccuracy value="20"/>
					<HudRange value="15"/>
				</Item>
			</Infos>
		</Item>
	</Infos>
	<Name>AR</Name>
</CWeaponInfoBlob>

