local isCarryingComponent = false
local componentProp = nil
local currentPropConfig = nil
local currentPropAnim = nil
local installingComponent = false

--- Returns whether the player is currently carrying a component prop
--- @return boolean #Is player carrying a component? `true` if carrying component. `false` otherwise
function IsCarryingComponent()
    if isCarryingComponent or componentProp ~= nil then 
        return true 
    else 
        return false 
    end
end
exports("IsCarryingComponent", IsCarryingComponent)

--- Returns the part type (core/service) based on the given part name.
--- @param part string The name of the part (e.g., "radiator", "oil_filter").
--- @return string|nil #The component type: "core_parts" or "service_parts", or nil if not found.
local function GetComponentType(part)
    if Config.CoreParts[part] then
        return "core_parts"
    elseif Config.ServiceParts[part] then
        return "service_parts"
    else
        return nil
    end
end
exports("GetComponentType", GetComponentType)

--- Play animation to carry the given prop model
--- @param prop table Prop data
local function CarryPropAnimation()
    if currentPropConfig.boneId == 60309 then
        currentPropAnim = {dict = "anim@heists@humane_labs@finale@keycards", clip = "ped_a_enter_loop", blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49}
    elseif currentPropConfig.boneId == 28422 then
        currentPropAnim = {dict = "anim@heists@box_carry@", clip = "idle", blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49}
        if currentPropConfig.small ~= nil and currentPropConfig.small == true then
            currentPropAnim = {dict = "anim@heists@narcotics@trash", clip = "idle", blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49}
        end
    end
    local anim = currentPropAnim
    lib.requestAnimDict(anim.dict)
    TaskPlayAnim(player, anim.dict, anim.clip , anim.blendIn, anim.blendOut, anim.duration, anim.flags, 0, false, false, false)
end

--- Carry the component prop: play anim and attach prop to player
local function CarryComponentProp()
    -- carry prop anim:
    CarryPropAnimation()
    Wait(250)

    local propCfg = currentPropConfig

    componentProp = CreateProp(propCfg.model, coords)

    -- attach prop to player:
    local boneIndex = GetPedBoneIndex(player, propCfg.boneId)
    AttachEntityToEntity(componentProp, player, boneIndex, propCfg.pos.x, propCfg.pos.y, propCfg.pos.z, propCfg.rot.x, propCfg.rot.y, propCfg.rot.z, true, true, false, true, 1, true)
end

--- Updates the workflow task for given component replacement
--- @param vehicle number The vehicle entity handle
--- @param partName string The name of the part
--- @param partLabel string The label of the part
local function UpdateComponentWorkflowTask(vehicle, partName, partLabel)
    local vehState = Entity(vehicle).state
    local workflowTasks = vehState["t1ger_mechanic:workflowTasks"]
    if not workflowTasks then return end
    local taskUpdated = false

    -- Mark the matching task as completed
    for taskId, task in pairs(workflowTasks) do
        if task.partName == partName and task.partLabel == partLabel and not task.completed then
            task.completed = true
            UpdateWorkflowTask(taskId, true)
            taskUpdated = true
        end
    end

    -- Save updated task list if any changes were made
    if taskUpdated then
        vehState:set("t1ger_mechanic:workflowTasks", workflowTasks, true)
    end
    
    -- Check if ALL tasks are completed
    local allTasksDone = true
    for _, task in pairs(workflowTasks or {}) do
        if not task.completed then
            allTasksDone = false
            break
        end
    end

    if allTasksDone then
        vehState:set("t1ger_mechanic:workflowTasks", nil, true)
    end
end

--- Fully restores a vehicle component by determining its type automatically.
--- - If the component is a **core part**, its health will be set to `100.0` via `SetCorePartHealth`.
--- - If it's a **service part**, its mileage will be reset to `0.0` using `SetServicePartMileage`.
--- - If `partType` is not provided, the function attempts to determine it using `GetComponentType`.
--- @param vehicle number The vehicle entity handle.
--- @param partName string The name of the part to restore (e.g., `"radiator"`, `"oil_filter"`).
--- @param partType? string Optionally specify `"core_parts"` or `"service_parts"`. If `nil`, it will be detected automatically.
local function SetComponentFixed(vehicle, partName, partType)
    -- Determine the part type dynamically if not provided
    local componentType = type(partType) == "string" and partType or GetComponentType(partName)
    if not componentType then 
        return print("[SetComponentFixed] Could not determine part type for: " .. partName)
    end

    local plate = GetVehicleNumberPlateText(vehicle)
    local vehicleMileage = GetVehicleMileage(vehicle)

    -- Fully restore the component based on its type
    if componentType == "core_parts" then
        local success = SetCorePartHealth(vehicle, partName, 100.0)
        if success then
            local partLabel = Config.CoreParts[partName].label
            UpdateComponentWorkflowTask(vehicle, partName, partLabel)
            TriggerServerEvent("t1ger_mechanic:server:addServiceHistory", plate, partLabel, vehicleMileage)
            _API.ShowNotification(string.format(locale("notification.component_replaced"), partLabel), "success", {duration = 5000})
        end
    elseif componentType == "service_parts" then
        local success = SetServicePartMileage(vehicle, partName, 0.0)
        if success then
            local partLabel = Config.ServiceParts[partName].label
            UpdateComponentWorkflowTask(vehicle, partName, partLabel)
            TriggerServerEvent("t1ger_mechanic:server:addServiceHistory", plate, partLabel, vehicleMileage)
            _API.ShowNotification(string.format(locale("notification.component_replaced"), partLabel), "success", {duration = 5000})
        end
    end
end
exports("SetComponentFixed", SetComponentFixed)

--- Returns table with salvaged materials when replacing a part.
--- Material amounts are scaled by part condition (0-100%) or randomized depending on config.
--- @param partName string The technical name of the part (e.g., "brakes", "coolant")
--- @param partType string The party type ("core_parts" or "service_parts")
--- @param condition number The current condition in % (0.0 to 100.0)
--- @return table<string, number> # Material name keys with amount values
local function GetReturnedMaterials(partName, partType, condition)
    local partConfig, categoryIndex = nil, nil

    -- get partConfig and recipe category index
    if partType == "core_parts" then
        partConfig = Config.CoreParts[partName]
        categoryIndex = 2
    elseif partType == "service_parts" then
        partConfig = Config.ServiceParts[partName]
        categoryIndex = 3
    end

    -- get part item recipe
    local category = Config.Shop.Workbench.categories[categoryIndex]
    local recipe = category and category.recipe and category.recipe[partConfig.item]
    if not recipe or type(recipe.materials) ~= "table" then return {} end

    local returnedMaterials = {}

    -- Clamp condition here (0.0 - 100.0), just extra safety
    local safeCondition = math.clamp(condition, 0.0, 100.0)

    for material, recipeAmount in pairs(recipe.materials or {}) do
        local amount = 0

        if Config.Components.Repair.materialReturn.scaleWithCondition then
            -- Divide by 100 to get 0.0-1.0 scale
            amount = math.floor(recipeAmount * (safeCondition / 100.0))
        else
            local minAmount = Config.Components.Repair.materialReturn.minAmount or 0
            local maxAmount = math.min(recipeAmount, Config.Components.Repair.materialReturn.maxAmount or recipeAmount)
            amount = math.random(minAmount, maxAmount)
        end

        if amount > 0 then
            returnedMaterials[material] = amount
        end
    end

    return returnedMaterials, recipe
end

--- Creates target on given vehicle for the repair of the component
--- @param vehicle number The vehicle entity handle
--- @param partName string The name of the part (e.g., "radiator", "oil_filter").
--- @param partType string The type of part `core_parts` or `service_parts`
local function CreateComponentRepairTarget(vehicle, partName, partType)

    --- Returns whether the player is carrying component and is near the repair vehicle
    --- @param entity number The vehicle entity handle
    --- @return boolean
    local function IsNearRepairVehicle(entity)
        if IsEntityValid(entity) and GetEntityType(entity) ~= 0 then
            local vehCoords = GetEntityCoords(entity)
            local vehDistance = #(coords - vehCoords)
            
            if vehDistance <= 7.0 and IsCarryingComponent() then
                return true
            end
        end
        return false
    end

    --- Returns cloest repair point and notification message (to be used if not found) for given part
    --- @param entity number The vehicle entity handle 
    --- @return table?
    --- @return string
    local function FindClosestComponentRepairPoint(entity, part)
        if part == "brake_pad" or part == "brakes" or part == "tires" then
            return GetVehicleClosestWheel(entity, 1.2, false), locale("notification.must_be_near_wheel")
        else
            return GetVehicleClosestHood(entity, 0.8, false), locale("notification.must_be_near_engine_hood")
        end
    end
    
    --- Formats returned materials into a string for notifications
    --- @param materials table<string, number> Table of {materialName = amount}
    --- @return string
    local function FormatReturnedMaterials(materials)
        local parts = {}

        for material, amount in pairs(materials) do
            table.insert(parts, string.format("%dx %s", amount, Config.Materials[material] or material))
        end

        return table.concat(parts, ", ")
    end

    -- Add target on the repair vehicle entity
    _API.Target.AddLocalEntity(vehicle, {
        {
            name = "t1ger_mechanic:repair:component",
            icon = Config.Components.Repair.targetIcon,
            label = locale("target.component_repair"),
            canInteract = IsNearRepairVehicle,
            distance =  7.0,
            onSelect = function(entity)
                local isWheelRepair = (partName == "brake_pad" or partName == "brakes" or partName == "tires")
                local partConfig = partType == "core_parts" and Config.CoreParts or partType == "service_parts" and Config.ServiceParts
                
                -- Get closest repair point
                local point, notify = FindClosestComponentRepairPoint(entity, partName)
                if not point then
                    return _API.ShowNotification(notify, "inform", {})
                end

                installingComponent = true

                -- Face vehicle
                if not point.isFallback then
                    TaskTurnPedToFaceCoord(player, point.defaultBoneCoords.x, point.defaultBoneCoords.y, point.defaultBoneCoords.z, 1000)
                else
                    TaskTurnPedToFaceEntity(player, entity, 1000)
                    Wait(700)
                end

                if not isWheelRepair then
                    -- open hood
                    if not point.isFallback then
                        VehicleHoodAnimation(entity, false)
                    end

                    ClearPedTasks(player)
                    -- Play anim:
                    local anim = {dict = "mini@repair", clip = "fixing_a_player", blendIn = 2.0, blendOut = 2.0, flag = 1}
                    lib.requestAnimDict(anim.dict)
                    TaskPlayAnim(player, anim.dict, anim.clip, anim.blendIn, anim.blendOut, -1, anim.flag, 0, 0, 0, 0)
                else
                    Wait(1000)
                    ClearPedTasks(player)
                    -- Play anim:
                    local anim = {dict = "anim@amb@clubhouse@tutorial@bkr_tut_ig3@", clip = "machinic_loop_mechandplayer", duration = -1, blendIn = 2.0, blendOut = 2.0, flag = 1}
                    lib.requestAnimDict(anim.dict)
                    TaskPlayAnim(player, anim.dict, anim.clip, anim.blendIn, anim.blendOut, -1, anim.flag, 0, 0, 0, 0)
                end

                -- Skillcheck
                local skillcheck = false
                if Config.Components.Repair.skillcheck.enable then
                    skillcheck = SkillCheck(Config.Components.Repair.skillcheck.difficulty, Config.Components.Repair.skillcheck.inputs)
                else
                    skillcheck = true 
                end

                -- Return if not skillcheck success
                if not skillcheck then
                    ClearPedTasks(player)
                    if not isWheelRepair then
                        -- close hood
                        if not point.isFallback then
                            VehicleHoodAnimation(entity, true)
                        end
                    end
                    installingComponent = false
                    CarryPropAnimation()
                    return
                end

                -- Progressbar
                local success = ProgressBar({
                    duration = Config.Components.Repair.duration,
                    label = locale("progressbar.component_repair"),
                    useWhileDead = false,
                    canCancel = true,
                    disable = {move = true, car = true, combat = true, sprint = true},
                })
                
                -- Return if not progressbar success
                if not success then
                    ClearPedTasks(player)
                    if not isWheelRepair then
                        -- close hood
                        if not point.isFallback then
                            VehicleHoodAnimation(entity, true)
                        end
                    end
                    installingComponent = false
                    CarryPropAnimation()
                    return
                end

                -- Remove Target
                _API.Target.RemoveLocalEntity(entity, {names = {"t1ger_mechanic:repair:component"}, labels = {locale("target.component_repair")}})

                -- Remove item
                TriggerServerEvent("t1ger_mechanic:server:removeItem", partConfig[partName].item, 1)

                -- get current part value:
                local currentPartValue = 0
                if partType == "core_parts" then
                    currentPartValue = GetCorePartHealth(entity, partName)
                elseif partType == "service_parts" then
                    currentPartValue = GetServicePartMileage(entity, partName)
                end

                -- get serivce interval
                local serviceInterval = partType == "service_parts" and GetVehicleServiceInterval(entity, partName)

                -- fallback to lowest value if not found
                if not currentPartValue or currentPartValue == -1 then
                    currentPartValue = partType == "core_parts" and 0.0 or partType == "service_parts" and serviceInterval
                end
                
                -- get current condition in percentage
                local conditionPercent = 0.0
                if partType == "core_parts" then
                    conditionPercent = math.clamp(currentPartValue, 0.0, 100.0)
                elseif partType == "service_parts" then
                    conditionPercent = math.clamp(100.0 - ((currentPartValue / serviceInterval) * 100.0), 0.0, 100.0)
                end

                -- Set component fixed if success
                if success then 
                    SetComponentFixed(entity, partName, partType)
                end

                -- fix engine if all components are fully functional:
                if AreAllCorePartsIntact(vehicle) then
                    SetVehicleEngineHealth(vehicle, 1000.0)
                end

                -- Play repair sound
                RepairSound()

                -- Return materials from the replaced part
                local returnedMaterials = GetReturnedMaterials(partName, partType, conditionPercent)
                if returnedMaterials and next(returnedMaterials) then
                    local formattedText = FormatReturnedMaterials(returnedMaterials)
                    TriggerServerEvent("t1ger_mechanic:server:returnComponentMaterials", returnedMaterials, partName, partType, formattedText)
                end

                -- Delete prop and clear tasks
                DeleteEntity(componentProp)
                ClearPedTasks(player)

                -- Close vehicle hood:
                if not isWheelRepair then
                    -- close hood
                    if not point.isFallback then
                        VehicleHoodAnimation(entity, true)
                    end
                end

                installingComponent = false

                -- Cleanup
                isCarryingComponent = false
                componentProp = nil
                currentPropConfig = nil
                currentPropAnim = nil
            end
        }
    })
end

--- Function to trigger the useable item for the given part
--- @param partName string The name of the part (e.g., "radiator", "oil_filter").
--- @param partType? string (optional) the type of part: `"core_parts"` or `"service_parts"`
local function UseComponentItem(partName, partType)
    if not IsPlayerMechanic() then return end

    -- Check if the player is already carrying a component
    if IsCarryingComponent() then
        return _API.ShowNotification(locale("notification.is_carrying_component"), "inform", {})
    end

    -- Get the closest vehicle
    local vehicle, vehicleDist = lib.getClosestVehicle(coords, 5.0, false)
	if not vehicle or not DoesEntityExist(vehicle) or not IsEntityAVehicle(vehicle) then
        return _API.ShowNotification(locale("notification.no_vehicle_nearby"), "inform", {})
    end

    -- Get Part Type:
    local componentType = (type(partType) == "string" and partType) or GetComponentType(partName)
    if not componentType then 
        return print("[UseComponentItem] Could not determine part type for: " .. partName)
    end

    -- Config lookup for the part details
    local partConfig = componentType == "core_parts" and Config.CoreParts[partName] or componentType == "service_parts" and Config.ServiceParts[partName] or nil
    if not partConfig then
        return print("[UseComponentItem] Part configuration (" .. componentType .. ") not found for: " .. partName)
    end

    -- is electric?
    local vehicleType = exports["t1ger_mechanic"]:GetMechanicVehicleType(vehicle)
    if partConfig.type ~= "shared" and partConfig.type ~= vehicleType then
        return _API.ShowNotification(locale("notification.component_not_compat"), "inform", {})
    end

    -- Determine if the part is already in good condition
    if componentType == "core_parts" then
        local health = GetCorePartHealth(vehicle, partName)
        if health and health ~= -1 and health >= 100.0 then
            return _API.ShowNotification(string.format(locale("notification.component_is_new"), partConfig.label), "inform", {})
        end
    elseif componentType == "service_parts" then
        local mileage = GetServicePartMileage(vehicle, partName)
        if mileage and mileage ~= -1 and mileage <= 0.0 then
            return _API.ShowNotification(string.format(locale("notification.component_is_new"), partConfig.label), "inform", {})
        end
    end

    -- Carry and attach the body part
    isCarryingComponent = true
    currentPropConfig = partConfig.prop
    CarryComponentProp()

    CreateComponentRepairTarget(vehicle, partName, componentType)

    while IsCarryingComponent() do
        Wait(1000)
        if currentPropAnim then
            if not IsEntityPlayingAnim(player, currentPropAnim.dict, currentPropAnim.clip, 1) then
                if not usingHoodAnim and not installingComponent then
                    TaskPlayAnim(player, currentPropAnim.dict, currentPropAnim.clip , currentPropAnim.blendIn, currentPropAnim.blendOut, currentPropAnim.duration, currentPropAnim.flags, 0, false, false, false)
                end
            end
        end
    end

    CancelComponentRepair()
end

--- Cancels a component repair and removes prop and clears tasks
function CancelComponentRepair()
    ClearPedTasks(player)
    if componentProp and DoesEntityExist(componentProp) then
        DeleteEntity(componentProp)
    end
    isCarryingComponent = false
    componentProp = nil
    currentPropConfig = nil
    currentPropAnim = nil
    installingComponent = false
end
exports("CancelComponentRepair", CancelComponentRepair)

--- Command if enabled to cancel a component repair
if Config.Components.Repair.cancelCommand.enable then
    RegisterCommand(Config.Components.Repair.cancelCommand.name, function(source, args, rawCommand)
        CancelComponentRepair()
    end, false)
end

--- Event for using component (service/core) part item:
RegisterNetEvent("t1ger_mechanic:client:useComponentItem", function(partName, partType)
    UseComponentItem(partName, partType)
end)