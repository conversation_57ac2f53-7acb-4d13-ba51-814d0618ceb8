DROP TABLE IF EXISTS `t1ger_tunershops`;
CREATE TABLE `t1ger_tunershops` (
	`id` INT(11) NOT NULL AUTO_INCREMENT,
	`name` VARCHAR(100) NOT NULL,
	`account` INT(11) NOT NULL DEFAULT 0,
	`boss` VARCHAR(100) DEFAULT NULL,
	`markup` INT(11) NOT NULL DEFAULT 0,
	`job` LONGTEXT NOT NULL DEFAULT ('[]'),
	`blip` LONGTEXT NOT NULL DEFAULT ('[]'),
	`employees` LONGTEXT NOT NULL DEFAULT ('[]'),
	`markers` LONGTEXT NOT NULL DEFAULT ('[]'),
	`categories` LONGTEXT NOT NULL DEFAULT ('[]'),
	`storage` LONGTEXT NOT NULL DEFAULT ('[]'),
	`billing` LONGTEXT NOT NULL DEFAULT ('[]'),
	`orders` LONGTEXT NOT NULL DEFAULT ('[]'),
	`delivery` LONGTEXT NULL DEFAULT NULL,
	PRIMARY KEY (`id`)
);

DROP TABLE IF EXISTS `t1ger_engineswaps`;
CREATE TABLE `t1ger_engineswaps` (
	`plate` VARCHAR(12) NOT NULL,
	`vehicle` VARCHAR(60) NOT NULL,
	`engine` VARCHAR(60) NOT NULL,
	PRIMARY KEY (`plate`)
);

DROP TABLE IF EXISTS `t1ger_nitrous`;
CREATE TABLE `t1ger_nitrous` (
	`plate` VARCHAR(12) NOT NULL,
	`size` INT(11) NOT NULL,
	`shots` INT(11) NOT NULL DEFAULT 0,
	`burst` INT(11) NOT NULL,
	`color` VARCHAR(255) NOT NULL,
	PRIMARY KEY (`plate`)
);

DROP TABLE IF EXISTS `t1ger_dyno`;
CREATE TABLE `t1ger_dyno` (
	`plate` VARCHAR(12) NOT NULL,
	`torque` FLOAT(11) NOT NULL,
	`power` FLOAT(11) NOT NULL,
	`brakes` FLOAT(11) NOT NULL,
	PRIMARY KEY (`plate`)
);