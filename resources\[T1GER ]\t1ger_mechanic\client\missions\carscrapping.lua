---Creates target options for mission ped
---@param missionPed entity the entity handle for the mission ped
---@param missionType string type of mission
---@param missionData table mission data/config for the given type
local function CreateMissionPedTarget(missionPed, missionType, missionData)
    
    --- Checks if player is near the mission entity
    ---@param entity number ped entity handle
    ---@return boolean IsNearMissionPed `true` if near mission ped. `false` otherwise
    local function IsNearMissionPed(entity)
        if IsEntityValid(entity) and GetEntityType(entity) ~= 0 then 
            if not IsPedInAnyVehicle(player, false) and not IsPedInAnyVehicle(player, true) then 
                return true
            end
        end
        return false
    end

    local targetOptions = {
        {
            name = "t1ger_mechanic:"..missionType..":ped",
            icon = "fa-solid fa-comment",
            label = locale("target.missions_ped_interact"),
            canInteract = IsNearMissionPed,
            distance = 2.0,
            onSelect = function(entity)
                if curMission.collected then return end

                -- check if vehicle is in zone:
                if not curMission.vehicleInZone then
                    return _API.ShowNotification(locale("notification.carscrapping_veh_not_in_zone"), "inform", {})
                end

                -- check for npc is inspecting:
                if curMission.inspecting then
                    return _API.ShowNotification(locale("notification.carscrapping_npc_is_inspecting"), "inform", {})
                end

                -- move npc to inspection coords:
                if not curMission.inspected and not curMission.inspecting then
                    curMission.inspecting = true

                    -- task npc to the inspection point:
                    FreezeEntityPosition(entity, false)
                    SetEntityInvincible(entity, true)
                    TaskGoToCoordAnyMeans(entity, missionData.scrapyard.inspect.x, missionData.scrapyard.inspect.y, missionData.scrapyard.inspect.z, 1.0, 0, 0, 786603, 0xbf800000)
                    SetEntityHeading(entity, missionData.scrapyard.inspect.w)
                    Wait(6000)
                    FreezeEntityPosition(entity, true)
                    TaskStartScenarioInPlace(entity, missionData.scrapyard.scenarios["inspect"], 0, false)
                    Wait(5000)

                    -- update attributes:
                    curMission.inspected = true
                    curMission.inspecting = false

                    -- notify:
                    return _API.ShowNotification(locale("notification.carscrapping_inspect_complete"), "inform", {})
                end

                -- reward:
                if curMission.inspected and not curMission.collected then
                    curMission.collected = true
                    local taskAnim = {dict = "mp_common", clip = "givetake2_a", flag = 49, blendIn = 4.0, blendOut = -4.0}
                    local pedCoords = GetEntityCoords(entity)

                    ClearPedTasksImmediately(entity)
                    ClearAreaOfObjects(pedCoords.x, pedCoords.y, pedCoords.z, 2.0, 0)

                    -- play anim on ped:
                    lib.requestAnimDict(taskAnim.dict)
                    TaskPlayAnim(entity, taskAnim.dict, taskAnim.clip, taskAnim.blendIn, taskAnim.blendOut, -1, taskAnim.flag, 1, 0, 0, 0)

                    -- progressbar callback:
                    local success = ProgressBar({
                        duration = 2000,
                        label = locale("progressbar.mission_reward"),
                        useWhileDead = false,
                        canCancel = true,
                        anim = taskAnim,
                        disable = {
                            move = true,
                            combat = true
                        }
                    })

                    if not success then
                        curMission.collected = false
                        return
                    end

                    -- Remove Target
                    _API.Target.RemoveLocalEntity(entity, {names = {"t1ger_mechanic:"..curMission.type..":ped"}, labels = {locale("target.missions_ped_interact")}})
                    
                    -- reward:
                    TriggerServerEvent("t1ger_mechanic:server:missionScrap", curMission.type, NetworkGetNetworkIdFromEntity(entity), missionData.scrapyard.inspect)

                    -- clean up:
                    RemoveBlip(curMission.blip)
                    ClearPedTasksImmediately(entity)
                    FreezeEntityPosition(entity, false)
                    SetBlockingOfNonTemporaryEvents(entity, true)
                    
                    Wait(500)
                    TaskGoToCoordAnyMeans(entity, missionData.scrapyard.ped.coords.x, missionData.scrapyard.ped.coords.y, missionData.scrapyard.ped.coords.z, 1.0, 0, 0, 786603, 0xbf800000)
                    SetEntityHeading(entity, missionData.scrapyard.ped.coords.w)

                    Citizen.Wait(5000)
                    DeleteEntity(entity)
                    curMission.cancel = true
                end
            end
        },
    }

    _API.Target.AddLocalEntity(missionPed, targetOptions)
end

--- CarScrapping mission
function CarScrapping()
    local mission = Config.Missions.Types[curMission.type]
    local location = Config.Missions.Locations[curMission.type][curMission.index]
    local complete = false

    -- create blip for npc vehicle:
    curMission.blip = CreateMissionBlip(location.pos, mission.blip)

    -- advanced notification:
    if Config.Missions.UseAdvancedNotify then
        local notifyIcon = Config.Missions.NotifyIcon
        local title, subtitle, message = locale("advanced_notification.carscrapping_title"), locale("advanced_notification.carscrapping_subtitle"), locale("advanced_notification.carscrapping_message")
        _API.ShowAdvancedNotification(notifyIcon, notifyIcon, 6, title, false, subtitle, message)
    else
        local string = locale("advanced_notification.carscrapping_title").."\n\n"..locale("advanced_notification.carscrapping_subtitle").."\n\n"..locale("advanced_notification.carscrapping_message")
        -- Calculate dynamic duration (e.g., 50ms per character)
        local perChar = 60
        local minDuration = 3000
        local maxDuration = 10000
        local dynamicDuration = math.min(maxDuration, math.max(minDuration, #string * perChar))
        _API.ShowNotification(string, "inform", {duration = dynamicDuration})
    end

    --- loop
    while not complete do
        Wait(1)

        if Config.Missions.Locations[curMission.type][curMission.index].inUse then

            local missionDist = #(coords - vector3(location.pos.x, location.pos.y, location.pos.z))

            -- create mission npc vehicle:
            if missionDist < 100.0 and not curMission.vehicle then
				curMission.vehicle = CreateMissionVehicle(location.pos)
				SetEntityAsMissionEntity(curMission.vehicle, true, true)
                
                -- remove current blip:
                RemoveBlip(curMission.blip)

                -- add blip for entity:
                curMission.blip = CreateMissionEntityBlip(curMission.vehicle, mission.blip, true)
            end

            local dropoffDist = #(coords - mission.scrapyard.dropoff)

            -- npc vehicle attached updater:
            if curMission.vehicle ~= nil then

                -- if attached:
                if IsEntityAttachedToAnyVehicle(curMission.vehicle) and not curMission.attached then 
                    curMission.attached = true
                    
                    -- remove current blip:
                    RemoveBlip(curMission.blip)

                    -- create blip for npc vehicle:
                    curMission.blip = CreateMissionBlip(mission.scrapyard.dropoff, mission.blip)

                    -- message:
                    _API.ShowNotification(locale("notification.carscrapping_head_to_dropoff"), "inform", {})
                end

                -- if detached:
                if not IsEntityAttachedToAnyVehicle(curMission.vehicle) and curMission.attached then
                    curMission.attached = false
                    
                    -- remove current blip:
                    RemoveBlip(curMission.blip)

                    -- create blip for npc vehicle:
                    curMission.blip = CreateMissionEntityBlip(curMission.vehicle, mission.blip, true)

                    -- message:
                    if dropoffDist > 15.0 then
                        _API.ShowNotification(locale("notification.carscrapping_pickup_vehicle"), "inform", {})
                    end
                end
            end


            if curMission.attached and dropoffDist <= 80.0 and not curMission.ped then
				curMission.ped = CreateMissionPed(mission.scrapyard.ped.coords, mission.scrapyard.scenarios.idle)
				SetEntityAsMissionEntity(curMission.ped, true, true)

                -- create target:
                CreateMissionPedTarget(curMission.ped, curMission.type, mission)
            end

            if curMission.attached and dropoffDist <= 80.0 and not curMission.dropZone then
                -- marker settings:
                local dropoffMarker = lib.marker.new({
                    type = 36,
                    coords = mission.scrapyard.dropoff,
                    color = {r = 255, g = 0, b = 0, a = 200},
                })
                -- create drop off zone: 
                curMission.dropZone = lib.zones.sphere({
                    coords = mission.scrapyard.dropoff,
                    radius = 15.0,
                    debug = false,
                    inside = function(zone)
                        dropoffMarker:draw()
                        if not IsEntityAttachedToAnyVehicle(curMission.vehicle) then
                            local vehicleCoords = GetEntityCoords(curMission.vehicle)
                            if #(vehicleCoords - mission.scrapyard.dropoff) <= 3.0 then
                                -- remove current blip:
                                RemoveBlip(curMission.blip)

                                -- create blip for scrapyard ped:
                                curMission.blip = CreateMissionEntityBlip(curMission.ped, mission.blip, false)

                                -- update attribute:
                                curMission.vehicleInZone = true

                                -- remove zone/marker:
                                curMission.dropZone:remove()

                                -- notify
                                _API.ShowNotification(locale("notification.carscrapping_start_inspection"), "inform", {})
                            end
                        end
                    end
                })
            end

        end

        if curMission.cancel then
            complete = true
        end

    end
    
    -- clean up entities and blip
    if DoesEntityExist(curMission.vehicle) then DeleteVehicle(curMission.vehicle) end
    if DoesEntityExist(curMission.ped) then DeleteEntity(curMission.ped) end
    if DoesBlipExist(curMission.blip) then RemoveBlip(curMission.blip) end
    
    -- sync inUse state:
    TriggerServerEvent("t1ger_mechanic:server:missionInUse", curMission.type, curMission.index, false)

    curMission = {}
end