<?xml version="1.0" encoding="UTF-8"?>

<CWeaponComponentInfoBlob>
    <Infos>
        <Item type="CWeaponComponentFlashLightInfo">
            <Name>g48beamgripb</Name>
            <Model>w_at_pi_flsh_pdluxe_g17beamgripb</Model>
            <LocName>WCT_FLASH</LocName>
            <LocDesc>WCD_FLASH</LocDesc>
            <AttachBone>AAPFlsh</AttachBone> <!-- Ensure correct positioning in the model -->
            <WeaponAttachBone>WAPFlshLasr</WeaponAttachBone> <!-- Ensure correct positioning -->
            <AccuracyModifier type="NULL"/>
            <DamageModifier type="NULL"/>
            <bShownOnWheel value="true"/>
            <CreateObject value="true"/>
            <HudDamage value="0"/>
            <HudSpeed value="0"/>
            <HudCapacity value="0"/>
            <HudAccuracy value="0"/>
            <HudRange value="0"/>
            <MainLightIntensity value="15.000000"/>
            <MainLightColor value="0xFFFF0000"/>
        	<MainLightRange value="30.000000"/>
			<MainLightFalloffExponent value="32.000000"/>
			<MainLightInnerAngle value="0.000000"/>
			<MainLightOuterAngle value="20.000000"/>
			<MainLightCoronaIntensity value="3.000000"/>
			<MainLightCoronaSize value="0.200000"/>
			<MainLightVolumeIntensity value="0.300000"/>
			<MainLightVolumeSize value="0.100000"/>
			<MainLightVolumeExponent value="70.000000"/>
			<MainLightVolumeOuterColor value="0xFFFF0000"/>
			<MainLightShadowFadeDistance value="10.000000"/>
			<MainLightSpecularFadeDistance value="10.000000"/>
			<SecondaryLightIntensity value="4.000000"/>
			<SecondaryLightColor value="0xFFFF0000"/>
			<SecondaryLightRange value="6.000000"/>
			<SecondaryLightFalloffExponent value="64.000000"/>
			<SecondaryLightInnerAngle value="0.000000"/>
			<SecondaryLightOuterAngle value="40.000000"/>
			<SecondaryLightVolumeIntensity value="0.300000"/>
			<SecondaryLightVolumeSize value="0.400000"/>
			<SecondaryLightVolumeExponent value="24.000000"/>
			<SecondaryLightVolumeOuterColor value="0xFFFF0000"/>
			<SecondaryLightFadeDistance value="10.000000"/>
			<fTargetDistalongAimCamera value="8.000000"/>
            <FlashLightBone>Gun_FLMuzzle</FlashLightBone>
            <FlashLightBoneBulbOn>Bulb_On</FlashLightBoneBulbOn>
            <FlashLightBoneBulbOff>Bulb_Off</FlashLightBoneBulbOff>
        </Item>
    </Infos>
    <InfoBlobName/>
</CWeaponComponentInfoBlob>
