local locationBlips = {}
local battleRoyaleLocations = {}
RegisterNetEvent("Pug:client:CreateBlip", function(Data)
    battleRoyaleLocations = Config.BlipLocations[ChosenMap]
    -- Clear any previous heli pads or other blips
    for _, location in pairs(Data) do
        table.insert(battleRoyaleLocations, {
            name = location.name or "HELIPAD",
            coords = location.coords,
            sprite = location.sprite or 43,
            color = location.color or 38,
            scale = location.scale or 0.7
        })
    end

    -- Create blips based on updated table
    CreateBattleRoyaleBlips()
end)


function CreateBattleRoyaleBlips()
    RemoveBattleRoyaleBlips()
    
    for _, location in ipairs(battleRoyaleLocations) do
        local blip = AddBlipForCoord(location.coords.x, location.coords.y, 0.0)
        
        SetBlipSprite(blip, location.sprite or 10)
        SetBlipColour(blip, location.color or 1)
        SetBlipScale(blip, location.scale or 0.8)
        SetBlipAsShortRange(blip, false)
        
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(location.name)
        EndTextCommandSetBlipName(blip)
        
        table.insert(locationBlips, blip)
    end
    
    return #locationBlips
end

function RemoveBattleRoyaleBlips()
    for _, blip in ipairs(locationBlips) do
        RemoveBlip(blip)
    end
    
    locationBlips = {}
    
    return true
end

-- CreateThread(function()
--     Wait(500)
--     CreateBattleRoyaleBlips()
-- end)



function CreatePlanePath(startCoords, endCoords)
    local dirX = endCoords.x - startCoords.x
    local dirY = endCoords.y - startCoords.y
    
    local distance = math.sqrt(dirX * dirX + dirY * dirY)
    
    local numPoints = 20 
    
    dirX = dirX / distance
    dirY = dirY / distance
    
    local stepSize = distance / numPoints
    
    local lineBlips = {}
    
    for i = 0, numPoints do
        local x = startCoords.x + dirX * stepSize * i
        local y = startCoords.y + dirY * stepSize * i
        
        local blip = AddBlipForCoord(x, y, startCoords.z or 0.0)
        
        if i == 0 then
            -- Start point
            SetBlipSprite(blip, 1) -- Standard blip
            SetBlipColour(blip, 38) -- Blue color
            SetBlipScale(blip, 1.0) -- Slightly larger
        elseif i == numPoints then
            -- End point
            SetBlipSprite(blip, 1) -- Standard blip
            SetBlipColour(blip, 38) -- Blue color
            SetBlipScale(blip, 1.0) -- Slightly larger
        else
            -- Line points
            SetBlipSprite(blip, 1) -- Standard blip
            SetBlipColour(blip, 38) -- Blue color
            SetBlipScale(blip, 0.5) -- Smaller for line points
        end
        SetBlipAsShortRange(blip, true)
        table.insert(lineBlips, blip)
    end
    
    CreateThread(function()
        local animationDuration = 35000 -- 35 seconds in milliseconds
        local startTime = GetGameTimer()
        local rippleSpeed = 150 -- Controls how fast the ripple moves along the path (lower = faster)
        local fadeSpeed = 10 -- How quickly individual blips fade (higher = faster)
        local rippleWidth = 5 -- How many blips are affected by the ripple at once
        
        while GetGameTimer() - startTime < animationDuration do
            Wait(135) -- Update rate of the animation
            
            local currentTime = GetGameTimer() - startTime
            local ripplePosition = (currentTime % rippleSpeed) / rippleSpeed * #lineBlips
            
            for i, blip in ipairs(lineBlips) do
                if DoesBlipExist(blip) then
                    local distance = math.abs(i - ripplePosition)
                    if distance > #lineBlips / 2 then
                        distance = #lineBlips - distance -- Handle wrapping around
                    end
                    
                    if distance < rippleWidth then
                        local alpha = 255 - (distance / rippleWidth) * 175 -- Fade from 255 to 80
                        SetBlipAlpha(blip, alpha)
                    else
                        SetBlipAlpha(blip, 80)
                    end
                end
            end
        end
        
        for k, blip in ipairs(lineBlips) do
            if DoesBlipExist(blip) then
                RemoveBlip(blip)
                lineBlips[k] = nil
            end
        end
    end)
    
    return true
end

function CreatePlanePathArea(startCoords, endCoords)
    CreatePlanePath(startCoords, endCoords)
    
    local midX = (startCoords.x + endCoords.x) / 2
    local midY = (startCoords.y + endCoords.y) / 2
    
    local heading = math.atan2(endCoords.y - startCoords.y, endCoords.x - startCoords.x) * 180 / math.pi
    
    local areaBlip = AddBlipForArea(midX, midY, 0, 
    math.sqrt((endCoords.x - startCoords.x)^2 + (endCoords.y - startCoords.y)^2) / 2, 
    75.0) 
    
    SetBlipColour(areaBlip, 38)
    SetBlipAlpha(areaBlip, 80)
    SetBlipRotation(areaBlip, heading)
    
    table.insert(locationBlips, areaBlip)
    
    return true
end
