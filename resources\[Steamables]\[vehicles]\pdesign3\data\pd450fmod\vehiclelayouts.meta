<?xml version="1.0" encoding="UTF-8"?>

<CVehicleMetadataMgr>
  <AnimRateSets />
  <ClipSetMaps>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_BOAT_DINGHY5_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@boat@dinghy@rds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_BOAT_DINGHY5_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@boat@dinghy@rds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_HELI_ANNIHILATOR2_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@helicopter@swift2@rds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_HELI_ANNIHILATOR2_REAR_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@helicopter@swift2@rps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_PLANE_ALKONOST_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@plane@alkonost@front@ds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_PLANE_ALKONOST_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@plane@alkonost@front@ds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_PLANE_ALKONOST_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@plane@alkonost@front@ps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_PLANE_ALKONOST_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@plane@alkonost@front@ps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>COMMON_CLIPSET_MAP_PLANE_ALKONOST</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@plane@alkonost@common@enter_exit</ClipSetId>
          <VarClipSetId>CommonVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>COMMON_CLIPSET_MAP_STD_DINGHY5_LEFT_TURRET</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@common@in_water@ds</ClipSetId>
          <VarClipSetId>CommonVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
        <Item>
          <ClipSetId>anim@veh@std@technical@aqua@turret_left@align_onveh</ClipSetId>
          <VarClipSetId>CommonVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
        <Item>
          <ClipSetId>anim@veh@std@technical@aqua@turret_left@align_onvehjack</ClipSetId>
          <VarClipSetId>CommonVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_STD_DINGHY5_LEFT_TURRET</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@boat@dinghy5@front_turret@enter_exit_left</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_STD_DINGHY5_LEFT_TURRET</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@boat@dinghy5@front_turret@enter_exit_left</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>COMMON_CLIPSET_MAP_STD_DINGHY5_RIGHT_TURRET</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@veh@common@in_water@ps</ClipSetId>
          <VarClipSetId>CommonVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
        <Item>
          <ClipSetId>anim@veh@std@technical@aqua@turret_right@align_onveh</ClipSetId>
          <VarClipSetId>CommonVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
        <Item>
          <ClipSetId>anim@veh@std@technical@aqua@turret_right@align_onvehjack</ClipSetId>
          <VarClipSetId>CommonVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_STD_DINGHY5_RIGHT_TURRET</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@boat@dinghy5@front_turret@enter_exit_right</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_STD_DINGHY5_RIGHT_TURRET</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@boat@dinghy5@front_turret@enter_exit_right</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>INSIDE_CLIPSET_MAP_DINGHY5_TURRET</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@std@barrage@rear@base</ClipSetId>
          <VarClipSetId>InsideVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>INSIDE_CLIPSET_SQUADDIE_REAR_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@truck@squaddie@rps@base</ClipSetId>
          <VarClipSetId>InsideVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_HELI_SEASPARROW2_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@helicopter@seasparrow2@ds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_HELI_SEASPARROW2_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@helicopter@seasparrow2@ds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_HELI_SEASPARROW2_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@helicopter@seasparrow2@ps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_HELI_SEASPARROW2_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@helicopter@seasparrow2@ps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_JEEP_WINKY_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@jeep@winky@ds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_JEEP_WINKY_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@jeep@winky@ds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_JEEP_WINKY_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@jeep@winky@ps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_JEEP_WINKY_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@jeep@winky@ps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_JEEP_WINKY_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@jeep@winky@rds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_JEEP_WINKY_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@jeep@winky@rds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_JEEP_WINKY_REAR_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@jeep@winky@rps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_JEEP_WINKY_REAR_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@jeep@winky@rps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>INSIDE_CLIPSET_MAP_LOW_TOREADOR_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@car@toreador@ds@base</ClipSetId>
          <VarClipSetId>InsideVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>INSIDE_CLIPSET_MAP_LOW_TOREADOR_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@car@toreador@ps@base</ClipSetId>
          <VarClipSetId>InsideVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>INSIDE_CLIPSET_MAP_LOW_TOREADOR_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@car@toreador@rds@base</ClipSetId>
          <VarClipSetId>InsideVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>INSIDE_CLIPSET_MAP_LOW_TOREADOR_REAR_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@car@toreador@rps@base</ClipSetId>
          <VarClipSetId>InsideVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_LOW_TOREADOR_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@car@toreador@ds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_LOW_TOREADOR_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@car@toreador@ds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_LOW_TOREADOR_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@car@toreador@ps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_LOW_TOREADOR_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@car@toreador@ps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_LOW_TOREADOR_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@car@toreador@rds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_LOW_TOREADOR_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@car@toreador@rds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_LOW_TOREADOR_REAR_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@car@toreador@rps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_LOW_TOREADOR_REAR_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@car@toreador@rps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_SLAMTRUCK_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@truck@slamtruck@ds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_SLAMTRUCK_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@truck@slamtruck@ds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_SLAMTRUCK_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@truck@slamtruck@ps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_SLAMTRUCK_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@truck@slamtruck@ps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_SQUADDIE_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@van@btype2@ds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_SQUADDIE_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@van@btype2@ds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_SQUADDIE_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@van@btype2@ps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_SQUADDIE_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@van@btype2@ps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_SQUADDIE_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@truck@squaddie@rds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_SQUADDIE_REAR_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@truck@squaddie@rds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_SQUADDIE_REAR_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@truck@squaddie@rps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_SQUADDIE_REAR_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@truck@squaddie@rps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_QUAD_VERUS_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@bike@quad@verus@front@ds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_QUAD_VERUS_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@bike@quad@verus@front@ds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_QUAD_VERUS_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@bike@quad@verus@front@ps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_QUAD_VERUS_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@bike@quad@verus@front@ps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_QUAD_VERUS_REAR</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@bike@quad@verus@rear@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_QUAD_VERUS_REAR</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@bike@quad@verus@rear@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>INSIDE_CLIPSET_MAP_QUAD_VERUS_REAR</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@bike@quad@verus@rear@base</ClipSetId>
          <VarClipSetId>InsideVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>INSIDE_CLIPSET_MAP_WINKY_REAR</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@jeep@winky@rds@base</ClipSetId>
          <VarClipSetId>InsideVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_TRUCK_VETIR_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@truck@vetir@ds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_TRUCK_VETIR_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@truck@vetir@ps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_TRUCK_VETIR_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@truck@vetir@ds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_TRUCK_VETIR_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@truck@vetir@ps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_VETO_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@gokart@generic@ds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_VETO_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@gokart@generic@ds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>INSIDE_CLIPSET_MAP_VETO</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@gokart@generic@base</ClipSetId>
          <VarClipSetId>InsideVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_VETO_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@gokart@generic@ps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_VETO_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@gokart@generic@ps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>INSIDE_CLIPSET_MAP_STD_WEEVIL_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@std@issi3@ds@base</ClipSetId>
          <VarClipSetId>InsideVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_STD_WEEVIL_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@car@weevil@ds@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_STD_WEEVIL_FRONT_LEFT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@car@weevil@ds@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>INSIDE_CLIPSET_MAP_STD_WEEVIL_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>anim@veh@std@issi3@ps@base</ClipSetId>
          <VarClipSetId>InsideVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>ENTRY_CLIPSET_MAP_STD_WEEVIL_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@car@weevil@ps@enter_exit</ClipSetId>
          <VarClipSetId>EntryVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
    <Item type="CClipSetMap">
      <Name>EXIT_CLIPSET_MAP_STD_WEEVIL_FRONT_RIGHT</Name>
      <Maps>
        <Item>
          <ClipSetId>clipset@anim@veh@car@weevil@ps@enter_exit</ClipSetId>
          <VarClipSetId>ExitVarClipSet</VarClipSetId>
          <ConditionFlags />
          <InformationFlags />
        </Item>
      </Maps>
    </Item>
  </ClipSetMaps>
  <VehicleCoverBoundOffsetInfos>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>ANNIHILATOR2_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.100000" />
      <ExtraForwardOffset value="1.100000" />
      <ExtraBackwardOffset value="0.000000" />
      <ExtraZOffset value="1.000000" />
      <CoverBoundInfos>
        <Item>
          <Name>NOSE</Name>
          <Position x="0.007500" y="4.547500" z="0.100000" />
          <Length value="1.470000" />
          <Width value="1.665000" />
          <Height value="1.800000" />
          <ActiveBoundExclusions>
            <Item>BODY</Item>
            <Item>TAIL</Item>
          </ActiveBoundExclusions>
        </Item>
        <Item>
          <Name>HARDPOINTS</Name>
          <Position x="-0.000000" y="3.325000" z="0.140000" />
          <Length value="1.195000" />
          <Width value="2.155000" />
          <Height value="1.800000" />
          <ActiveBoundExclusions>
            <Item>BODY</Item>
            <Item>TAIL</Item>
          </ActiveBoundExclusions>
        </Item>
        <Item>
          <Name>HARDPOINTS_1</Name>
          <Position x="0.000000" y="1.865000" z="0.190000" />
          <Length value="1.835000" />
          <Width value="2.595000" />
          <Height value="1.800000" />
          <ActiveBoundExclusions>
            <Item>BODY</Item>
            <Item>TAIL</Item>
          </ActiveBoundExclusions>
        </Item>
        <Item>
          <Name>Door</Name>
          <Position x="-0.005000" y="-0.090000" z="0.517500" />
          <Length value="2.255000" />
          <Width value="1.880000" />
          <Height value="0.290000" />
          <ActiveBoundExclusions>
            <Item>NOSE</Item>
            <Item>HARDPOINTS</Item>
          </ActiveBoundExclusions>
        </Item>
        <Item>
          <Name>BODY</Name>
          <Position x="0.000000" y="-2.400000" z="0.430000" />
          <Length value="2.380000" />
          <Width value="2.715000" />
          <Height value="1.780000" />
          <ActiveBoundExclusions>
            <Item>NOSE</Item>
            <Item>HARDPOINTS</Item>
          </ActiveBoundExclusions>
        </Item>
        <Item>
          <Name>TAIL</Name>
          <Position x="0.000000" y="-5.692500" z="0.450000" />
          <Length value="4.180000" />
          <Width value="2.390000" />
          <Height value="1.780000" />
          <ActiveBoundExclusions>
            <Item>NOSE</Item>
            <Item>HARDPOINTS</Item>
          </ActiveBoundExclusions>
        </Item>
      </CoverBoundInfos>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>BRIOSO2_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="-0.030000" />
      <ExtraForwardOffset value="-0.020000" />
      <ExtraBackwardOffset value="-0.130000" />
      <ExtraZOffset value="-0.380000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>VETO_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.210000" />
      <ExtraForwardOffset value="0.800000" />
      <ExtraBackwardOffset value="0.800000" />
      <ExtraZOffset value="-0.500000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>MANCHEZ2_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.100000" />
      <ExtraForwardOffset value="-0.200000" />
      <ExtraBackwardOffset value="-0.100000" />
      <ExtraZOffset value="0.100000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>WINKY_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.050000" />
      <ExtraForwardOffset value="0.000000" />
      <ExtraBackwardOffset value="-0.200000" />
      <ExtraZOffset value="-0.200000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>SEASPARROW2_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.050000" />
      <ExtraForwardOffset value="0.000000" />
      <ExtraBackwardOffset value="-0.200000" />
      <ExtraZOffset value="-0.200000" />
      <CoverBoundInfos>
        <Item>
          <Name>Front</Name>
          <Position x="-0.012000" y="1.562500" z="-0.650000" />
          <Length value="0.625000" />
          <Width value="1.415000" />
          <Height value="1.700000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>MIDDLE</Name>
          <Position x="0.018000" y="0.445000" z="-0.650000" />
          <Length value="1.665000" />
          <Width value="2.305000" />
          <Height value="1.700000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>REAR</Name>
          <Position x="0.018000" y="-0.670000" z="-1.130000" />
          <Length value="0.675000" />
          <Width value="2.095000" />
          <Height value="0.585000" />
          <ActiveBoundExclusions />
        </Item>
      </CoverBoundInfos>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>SLAMTRUCK_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.100000" />
      <ExtraForwardOffset value="-0.200000" />
      <ExtraBackwardOffset value="-0.250000" />
      <ExtraZOffset value="1.000000" />
      <CoverBoundInfos>
        <Item>
          <Name>Front</Name>
          <Position x="0.000000" y="1.800000" z="0.000000" />
          <Length value="1.800000" />
          <Width value="2.400000" />
          <Height value="2.100000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>MIDDLE</Name>
          <Position x="0.000000" y="0.150000" z="0.000000" />
          <Length value="1.450000" />
          <Width value="2.400000" />
          <Height value="2.100000" />
          <ActiveBoundExclusions />
        </Item>
      </CoverBoundInfos>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>SQUADDIE_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.050000" />
      <ExtraForwardOffset value="0.000000" />
      <ExtraBackwardOffset value="-0.200000" />
      <ExtraZOffset value="-0.200000" />
      <CoverBoundInfos>
        <Item>
          <Name>Front</Name>
          <Position x="0.000000" y="1.800000" z="0.000000" />
          <Length value="1.820000" />
          <Width value="2.400000" />
          <Height value="1.105000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>MIDDLE</Name>
          <Position x="0.000000" y="0.150000" z="0.000000" />
          <Length value="4.500000" />
          <Width value="2.400000" />
          <Height value="1.095000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>REAR</Name>
          <Position x="0.000000" y="-1.000000" z="-0.300000" />
          <Length value="3.385000" />
          <Width value="2.400000" />
          <Height value="1.690000" />
          <ActiveBoundExclusions />
        </Item>
      </CoverBoundInfos>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>VETIR_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.100000" />
      <ExtraForwardOffset value="-0.200000" />
      <ExtraBackwardOffset value="-0.250000" />
      <ExtraZOffset value="1.000000" />
      <CoverBoundInfos>
        <Item>
          <Name>Front</Name>
          <Position x="0.000000" y="2.250000" z="0.000000" />
          <Length value="1.700000" />
          <Width value="2.400000" />
          <Height value="2.100000" />
          <ActiveBoundExclusions />
        </Item>
        <Item>
          <Name>REAR</Name>
          <Position x="0.000000" y="-1.000000" z="-0.300000" />
          <Length value="4.800000" />
          <Width value="2.400000" />
          <Height value="1.600000" />
          <ActiveBoundExclusions />
        </Item>
      </CoverBoundInfos>
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>ITALIRSX_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.010000" />
      <ExtraForwardOffset value="-0.300000" />
      <ExtraBackwardOffset value="0.200000" />
      <ExtraZOffset value="-0.400000" />
      <CoverBoundInfos />
    </Item>
    <Item type="CVehicleCoverBoundOffsetInfo">
      <Name>TOREADOR_COVER_OFFSET_INFO</Name>
      <ExtraSideOffset value="0.000000" />
      <ExtraForwardOffset value="-0.200000" />
      <ExtraBackwardOffset value="-0.100000" />
      <ExtraZOffset value="-0.400000" />
      <CoverBoundInfos />
    </Item>
  </VehicleCoverBoundOffsetInfos>
  <BicycleInfos />
  <POVTuningInfos />
  <EntryAnimVariations />
  <VehicleExtraPointsInfos>
    <Item type="CVehicleExtraPointsInfo">
      <Name>EXTRA_VEHICLE_POINTS_PLANE_ALKONOST</Name>
      <ExtraVehiclePoints>
        <Item>
          <LocationType>DRIVER_SEAT_RELATIVE</LocationType>
          <PointType>GET_IN</PointType>
          <Position x="0.444000" y="-1.059000" z="0.539000" />
          <Heading value="-0.428000" />
          <Pitch value="0.000000" />
          <BoneName />
        </Item>
        <Item>
          <LocationType>DRIVER_SEAT_RELATIVE</LocationType>
          <PointType>GET_OUT</PointType>
          <Position x="0.144000" y="-0.865000" z="-0.679000" />
          <Heading value="2.500000" />
          <Pitch value="0.000000" />
          <BoneName />
        </Item>
      </ExtraVehiclePoints>
    </Item>
    <Item type="CVehicleExtraPointsInfo">
      <Name>EXTRA_VEHICLE_POINTS_STD_DINGHY5_LEFT_TURRET</Name>
      <ExtraVehiclePoints>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>GET_IN</PointType>
          <Position x="0.000000" y="-0.200000" z="0.100000" />
          <Heading value="0.000000" />
          <Pitch value="0.000000" />
          <BoneName />
        </Item>
        <Item>
          <LocationType>ENTITY_RELATIVE</LocationType>
          <PointType>CLIMB_UP_FIXUP_POINT</PointType>
          <Position x="-0.360000" y="1.700000" z="1.300000" />
          <Heading value="0.000000" />
          <Pitch value="0.000000" />
          <BoneName />
        </Item>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>GET_OUT</PointType>
          <Position x="0.000000" y="0.000000" z="0.500000" />
          <Heading value="0.000000" />
          <Pitch value="0.000000" />
          <BoneName />
        </Item>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>ON_BOARD_JACK</PointType>
          <Position x="0.000000" y="-0.400000" z="0.000000" />
          <Heading value="0.000000" />
          <Pitch value="0.000000" />
          <BoneName />
        </Item>
      </ExtraVehiclePoints>
    </Item>
    <Item type="CVehicleExtraPointsInfo">
      <Name>EXTRA_VEHICLE_POINTS_DINGHY5_REAR_LEFT</Name>
      <ExtraVehiclePoints>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>GET_IN</PointType>
          <Position x="0.320000" y="-0.600000" z="0.625000" />
          <Heading value="0.000000" />
          <Pitch value="0.000000" />
          <BoneName />
        </Item>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>GET_IN_2</PointType>
          <Position x="-1.330000" y="0.185000" z="0.007000" />
          <Heading value="-1.694000" />
          <Pitch value="0.000000" />
          <BoneName />
        </Item>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>GET_IN_3</PointType>
          <Position x="-1.020000" y="0.242000" z="1.178000" />
          <Heading value="-2.670000" />
          <Pitch value="0.000000" />
          <BoneName />
        </Item>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>GET_IN_4</PointType>
          <Position x="-0.881000" y="-0.621000" z="0.624000" />
          <Heading value="0.000000" />
          <Pitch value="0.000000" />
          <BoneName />
        </Item>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>GET_OUT</PointType>
          <Position x="-0.450000" y="-0.400000" z="0.625000" />
          <Heading value="0.000000" />
          <Pitch value="0.000000" />
          <BoneName />
        </Item>
      </ExtraVehiclePoints>
    </Item>
    <Item type="CVehicleExtraPointsInfo">
      <Name>EXTRA_VEHICLE_POINTS_STD_DINGHY5_RIGHT_TURRET</Name>
      <ExtraVehiclePoints>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>GET_IN</PointType>
          <Position x="0.000000" y="-0.200000" z="0.100000" />
          <Heading value="0.000000" />
          <Pitch value="0.000000" />
          <BoneName />
        </Item>
        <Item>
          <LocationType>ENTITY_RELATIVE</LocationType>
          <PointType>CLIMB_UP_FIXUP_POINT</PointType>
          <Position x="0.360000" y="1.700000" z="1.300000" />
          <Heading value="0.000000" />
          <Pitch value="0.000000" />
          <BoneName />
        </Item>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>GET_OUT</PointType>
          <Position x="0.000000" y="-0.000000" z="0.500000" />
          <Heading value="0.000000" />
          <Pitch value="0.000000" />
          <BoneName />
        </Item>
        <Item>
          <LocationType>SEAT_RELATIVE</LocationType>
          <PointType>ON_BOARD_JACK</PointType>
          <Position x="0.000000" y="-0.400000" z="0.000000" />
          <Heading value="0.000000" />
          <Pitch value="0.000000" />
          <BoneName />
        </Item>
      </ExtraVehiclePoints>
    </Item>
  </VehicleExtraPointsInfos>
  <DrivebyWeaponGroups />
  <VehicleDriveByAnimInfos>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>JEEP_WINKY_DB_ANIM_INFO_ONE_HANDED_DS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_ONE_HANDED" />
      <DriveByClipSet>clipset@anim@veh@drivebyjeep@winky@ds</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@driver@1h</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets>
        <Item>
          <Weapons>
            <Item>WEAPON_MICROSMG</Item>
          </Weapons>
          <ClipSet>driveby@first_person@driver@smg</ClipSet>
        </Item>
      </AltFirstPersonDriveByClipSets>
      <RestrictedDriveByClipSet>drive_by@restricted@VAN_DS</RestrictedDriveByClipSet>
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>STD_CAR_DRIVEBY</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>JEEP_WINKY_DB_ANIM_INFO_ONE_HANDED_PS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_ONE_HANDED" />
      <DriveByClipSet>clipset@anim@veh@drivebyjeep@winky@ps</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@passenger_right_handed@1h</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets>
        <Item>
          <Weapons>
            <Item>WEAPON_MICROSMG</Item>
          </Weapons>
          <ClipSet>driveby@first_person@passenger_right_handed@smg</ClipSet>
        </Item>
      </AltFirstPersonDriveByClipSets>
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>STD_CAR_DRIVEBY</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>JEEP_WINKY_DB_ANIM_INFO_UNARMED_RDS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_UNARMED" />
      <DriveByClipSet>clipset@anim@veh@drivebyjeep@winky@rds_0h</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@passenger_left_handed@unarmed</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets />
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>STD_CAR_DRIVEBY</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>JEEP_WINKY_DB_ANIM_INFO_ONE_HANDED_RDS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_ONE_HANDED" />
      <DriveByClipSet>clipset@anim@veh@drivebyjeep@winky@rds</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@passenger_rear_right_handed@1h</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets>
        <Item>
          <Weapons>
            <Item>WEAPON_MICROSMG</Item>
          </Weapons>
          <ClipSet>driveby@first_person@passenger_rear_right_handed@smg</ClipSet>
        </Item>
      </AltFirstPersonDriveByClipSets>
      <RestrictedDriveByClipSet>drive_by@restricted@TRUCK_RDS</RestrictedDriveByClipSet>
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>STD_CAR_DRIVEBY</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>JEEP_WINKY_DB_ANIM_INFO_THROW_RDS</Name>
      <WeaponGroup ref="DRIVEBY_THROW" />
      <DriveByClipSet>clipset@anim@veh@drivebyjeep@winky@rds_throw</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@passenger_right_handed@throw</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets />
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>MOUNTED_THROW</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>LOW_ITALIRSX_DB_ANIM_INFO_UNARMED_DS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_UNARMED" />
      <DriveByClipSet>anim@veh@drivebyraptor@ds_unarmed</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@driver@unarmed</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets />
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>STD_CAR_DRIVEBY</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>LOW_ITALIRSX_DB_ANIM_INFO_THROW_DS</Name>
      <WeaponGroup ref="DRIVEBY_THROW" />
      <DriveByClipSet>anim@veh@drivebyraptor@ds_grenade</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@driver@throw</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets />
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>MOUNTED_THROW</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>LOW_ITALIRSX_DB_ANIM_INFO_UNARMED_PS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_UNARMED" />
      <DriveByClipSet>anim@veh@drivebyraptor@ps_unarmed</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@driver@unarmed</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets />
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>STD_CAR_DRIVEBY</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>LOW_ITALIRSX_DB_ANIM_INFO_THROW_PS</Name>
      <WeaponGroup ref="DRIVEBY_THROW" />
      <DriveByClipSet>anim@veh@drivebyraptor@ps_grenade</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@passenger_right_handed@throw</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets />
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>MOUNTED_THROW</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>LOW_VETO_DB_ANIM_INFO_UNARMED_DS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_UNARMED" />
      <DriveByClipSet>clipset@anim@veh@drivebygokart@generic@ds_unarmed</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@driver@unarmed</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets />
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>STD_CAR_DRIVEBY</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>LOW_VETO_DB_ANIM_INFO_ONE_HANDED_DS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_ONE_HANDED" />
      <DriveByClipSet>clipset@anim@veh@drivebygokart@generic@ds</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@driver@1h</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets>
        <Item>
          <Weapons>
            <Item>WEAPON_MICROSMG</Item>
          </Weapons>
          <ClipSet>driveby@first_person@driver@smg</ClipSet>
        </Item>
      </AltFirstPersonDriveByClipSets>
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>STD_CAR_DRIVEBY</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>LOW_VETO_DB_ANIM_INFO_THROW_DS</Name>
      <WeaponGroup ref="DRIVEBY_THROW" />
      <DriveByClipSet>clipset@anim@veh@drivebygokart@generic@ds_throw</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@passenger_right_handed@throw</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets />
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>MOUNTED_THROW</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>QUAD_VERUS_DB_ANIM_INFO_UNARMED_DS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_UNARMED" />
      <DriveByClipSet>clipset@anim@veh@drivebybike@quad@verus@rear@ds_0h</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@passenger_right_handed@unarmed</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets />
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>STD_CAR_DRIVEBY</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>QUAD_VERUS_DB_ANIM_INFO_ONE_HANDED_DS</Name>
      <WeaponGroup ref="DRIVEBY_BIKE_ONE_HANDED" />
      <DriveByClipSet>clipset@anim@veh@drivebybike@quad@verus@rear@ds</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@passenger_right_handed@1h</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets>
        <Item>
          <Weapons>
            <Item>WEAPON_COMPACTRIFLE</Item>
            <Item>WEAPON_MICROSMG</Item>
            <Item>WEAPON_SMG_MK2</Item>
          </Weapons>
          <ClipSet>driveby@first_person@passenger_right_handed@smg</ClipSet>
        </Item>
        <Item>
          <Weapons>
            <Item>WEAPON_DBSHOTGUN</Item>
            <Item>WEAPON_SAWNOFFSHOTGUN</Item>
          </Weapons>
          <ClipSet>driveby@first_person@bike@passenger@sawnoff</ClipSet>
        </Item>
      </AltFirstPersonDriveByClipSets>
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>STD_CAR_DRIVEBY</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>QUAD_VERUS_DB_ANIM_INFO_THROW_DS</Name>
      <WeaponGroup ref="DRIVEBY_MOUNTED_THROW" />
      <DriveByClipSet>clipset@anim@veh@drivebybike@quad@verus@rear@ds_throw</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@passenger_right_handed@throw</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets />
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>MOUNTED_THROW</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>STD_WEEVIL_DB_ANIM_INFO_ONE_HANDED_DS_RESTRICTED</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_ONE_HANDED" />
      <DriveByClipSet>drive_by@restricted@STD_DS</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@driver@1h</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets>
        <Item>
          <Weapons>
            <Item>WEAPON_MICROSMG</Item>
          </Weapons>
          <ClipSet>driveby@first_person@driver@smg</ClipSet>
        </Item>
      </AltFirstPersonDriveByClipSets>
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>STD_CAR_DRIVEBY</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>STD_WEEVIL_DB_ANIM_INFO_ONE_HANDED_PS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_ONE_HANDED" />
      <DriveByClipSet>clipset@anim@veh@drivebycar@weevil@ps</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@passenger_left_handed@1h</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets>
        <Item>
          <Weapons>
            <Item>WEAPON_MICROSMG</Item>
          </Weapons>
          <ClipSet>driveby@first_person@passenger_left_handed@smg</ClipSet>
        </Item>
      </AltFirstPersonDriveByClipSets>
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>STD_CAR_DRIVEBY</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>TRUCK_VETIR_DB_ANIM_INFO_ONE_HANDED_DS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_ONE_HANDED" />
      <DriveByClipSet>clipset@anim@veh@drivebytruck@vetir@front@@ds</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@driver@1h</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets>
        <Item>
          <Weapons>
            <Item>WEAPON_MICROSMG</Item>
          </Weapons>
          <ClipSet>driveby@first_person@driver@smg</ClipSet>
        </Item>
      </AltFirstPersonDriveByClipSets>
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>STD_CAR_DRIVEBY</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>TRUCK_VETIR_DB_ANIM_INFO_ONE_HANDED_PS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_ONE_HANDED" />
      <DriveByClipSet>clipset@anim@veh@drivebytruck@vetir@front@@ps</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@passenger_left_handed@1h</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets>
        <Item>
          <Weapons>
            <Item>WEAPON_MICROSMG</Item>
          </Weapons>
          <ClipSet>driveby@first_person@passenger_left_handed@smg</ClipSet>
        </Item>
      </AltFirstPersonDriveByClipSets>
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>STD_CAR_DRIVEBY</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>TRUCK_VETIR_DB_ANIM_INFO_THROW_DS</Name>
      <WeaponGroup ref="DRIVEBY_THROW" />
      <DriveByClipSet>clipset@anim@veh@drivebytruck@vetir@front@@ds_grenade</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@driver@throw</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets />
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>STD_CAR_DRIVEBY</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>TRUCK_VETIR_DB_ANIM_INFO_THROW_PS</Name>
      <WeaponGroup ref="DRIVEBY_THROW" />
      <DriveByClipSet>clipset@anim@veh@drivebytruck@vetir@front@@ps_grenade</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@passenger_right_handed@throw</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets />
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>STD_CAR_DRIVEBY</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>SLAMTRUCK_DB_ANIM_INFO_ONE_HANDED_DS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_ONE_HANDED" />
      <DriveByClipSet>drive_by@riot_van_ds</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@driver@1h</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets>
        <Item>
          <Weapons>
            <Item>WEAPON_MICROSMG</Item>
          </Weapons>
          <ClipSet>driveby@first_person@driver@smg</ClipSet>
        </Item>
      </AltFirstPersonDriveByClipSets>
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>STD_CAR_DRIVEBY</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
    <Item type="CVehicleDriveByAnimInfo">
      <Name>SLAMTRUCK_DB_ANIM_INFO_ONE_HANDED_PS</Name>
      <WeaponGroup ref="DRIVEBY_DEFAULT_ONE_HANDED" />
      <DriveByClipSet>drive_by@riot_van_ps</DriveByClipSet>
      <FirstPersonDriveByClipSet>driveby@first_person@passenger_left_handed@1h</FirstPersonDriveByClipSet>
      <AltFirstPersonDriveByClipSets>
        <Item>
          <Weapons>
            <Item>WEAPON_MICROSMG</Item>
          </Weapons>
          <ClipSet>driveby@first_person@passenger_left_handed@smg</ClipSet>
        </Item>
      </AltFirstPersonDriveByClipSets>
      <RestrictedDriveByClipSet />
      <VehicleMeleeClipSet />
      <FirstPersonVehicleMeleeClipSet />
      <Network>STD_CAR_DRIVEBY</Network>
      <UseOverrideAngles value="false" />
      <OverrideAnglesInThirdPersonOnly value="false" />
      <OverrideMinAimAngle value="0.000000" />
      <OverrideMaxAimAngle value="0.000000" />
      <OverrideMinRestrictedAimAngle value="0.000000" />
      <OverrideMaxRestrictedAimAngle value="0.000000" />
    </Item>
  </VehicleDriveByAnimInfos>
  <VehicleDriveByInfos>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_WEEVIL_STANDARD_FRONT_LEFT</Name>
      <MinAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxAimSweepHeadingAngleDegs value="190.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="190.000000" />
      <MinSmashWindowAngleDegs value="-180.000000" />
      <MaxSmashWindowAngleDegs value="-17.500000" />
      <MinSmashWindowAngleFirstPersonDegs value="-180.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="-35.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-0.175000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="3.140000" />
      <DriveByAnimInfos>
        <Item ref="STD_DB_ANIM_INFO_UNARMED_DS" />
        <Item ref="STD_WEEVIL_DB_ANIM_INFO_ONE_HANDED_DS_RESTRICTED" />
        <Item ref="STD_DB_ANIM_INFO_THROW_DS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <PovDriveByCamera />
      <DriveByFlags>UseMountedProjectileTask LeftHandedProjctiles LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_WEEVIL_STANDARD_FRONT_RIGHT</Name>
      <MinAimSweepHeadingAngleDegs value="-225.000000" />
      <MaxAimSweepHeadingAngleDegs value="225.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-225.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="225.000000" />
      <MinSmashWindowAngleDegs value="17.500000" />
      <MaxSmashWindowAngleDegs value="180.000000" />
      <MinSmashWindowAngleFirstPersonDegs value="35.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="180.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="0.925000" />
      <DriveByAnimInfos>
        <Item ref="STD_DB_ANIM_INFO_UNARMED_PS" />
        <Item ref="STD_WEEVIL_DB_ANIM_INFO_ONE_HANDED_PS" />
        <Item ref="STD_DB_ANIM_INFO_THROW_PS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <PovDriveByCamera />
      <DriveByFlags>UseMountedProjectileTask LeftHandedFirstPersonAnims LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_WINKY_FRONT_LEFT</Name>
      <MinAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxAimSweepHeadingAngleDegs value="190.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="190.000000" />
      <MinSmashWindowAngleDegs value="-180.000000" />
      <MaxSmashWindowAngleDegs value="-17.500000" />
      <MinSmashWindowAngleFirstPersonDegs value="-180.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="-17.500000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="3.140000" />
      <DriveByAnimInfos>
        <Item ref="VAN_DB_ANIM_INFO_UNARMED_DS" />
        <Item ref="JEEP_WINKY_DB_ANIM_INFO_ONE_HANDED_DS" />
        <Item ref="JEEP_BODHI_DB_ANIM_INFO_THROW_DS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <PovDriveByCamera />
      <DriveByFlags>UseMountedProjectileTask UseThreeAnimThrow LeftHandedProjctiles LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_WINKY_FRONT_RIGHT</Name>
      <MinAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxAimSweepHeadingAngleDegs value="190.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="190.000000" />
      <MinSmashWindowAngleDegs value="17.500000" />
      <MaxSmashWindowAngleDegs value="180.000000" />
      <MinSmashWindowAngleFirstPersonDegs value="17.500000" />
      <MaxSmashWindowAngleFirstPersonDegs value="180.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="3.140000" />
      <DriveByAnimInfos>
        <Item ref="VAN_DB_ANIM_INFO_UNARMED_PS" />
        <Item ref="JEEP_WINKY_DB_ANIM_INFO_ONE_HANDED_PS" />
        <Item ref="JEEP_BODHI_DB_ANIM_INFO_THROW_PS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <PovDriveByCamera />
      <DriveByFlags>UseMountedProjectileTask UseThreeAnimThrow LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_WINKY_REAR</Name>
      <MinAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxAimSweepHeadingAngleDegs value="190.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="190.000000" />
      <MinSmashWindowAngleDegs value="17.500000" />
      <MaxSmashWindowAngleDegs value="180.000000" />
      <MinSmashWindowAngleFirstPersonDegs value="17.500000" />
      <MaxSmashWindowAngleFirstPersonDegs value="180.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="3.140000" />
      <DriveByAnimInfos>
        <Item ref="JEEP_WINKY_DB_ANIM_INFO_UNARMED_RDS" />
        <Item ref="JEEP_WINKY_DB_ANIM_INFO_ONE_HANDED_RDS" />
        <Item ref="JEEP_WINKY_DB_ANIM_INFO_THROW_RDS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <PovDriveByCamera />
      <DriveByFlags>UseMountedProjectileTask UseThreeAnimThrow LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_LOW_ITALIRSX_FRONT_LEFT</Name>
      <MinAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxAimSweepHeadingAngleDegs value="190.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="190.000000" />
      <MinSmashWindowAngleDegs value="-180.000000" />
      <MaxSmashWindowAngleDegs value="0.000000" />
      <MinSmashWindowAngleFirstPersonDegs value="-180.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="-25.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-0.800000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="3.140000" />
      <DriveByAnimInfos>
        <Item ref="LOW_ITALIRSX_DB_ANIM_INFO_UNARMED_DS" />
        <Item ref="LOW_TIGHT_DB_ANIM_INFO_ONE_HANDED_DS" />
        <Item ref="LOW_ITALIRSX_DB_ANIM_INFO_THROW_DS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <PovDriveByCamera />
      <DriveByFlags>UseMountedProjectileTask LeftHandedProjctiles LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_LOW_ITALIRSX_FRONT_RIGHT</Name>
      <MinAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxAimSweepHeadingAngleDegs value="190.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="190.000000" />
      <MinSmashWindowAngleDegs value="0.000000" />
      <MaxSmashWindowAngleDegs value="180.000000" />
      <MinSmashWindowAngleFirstPersonDegs value="25.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="180.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="0.800000" />
      <DriveByAnimInfos>
        <Item ref="LOW_ITALIRSX_DB_ANIM_INFO_UNARMED_PS" />
        <Item ref="LOW_TIGHT_DB_ANIM_INFO_ONE_HANDED_PS" />
        <Item ref="LOW_ITALIRSX_DB_ANIM_INFO_THROW_PS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <PovDriveByCamera />
      <DriveByFlags>UseMountedProjectileTask LeftHandedFirstPersonAnims LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_LOW_VETO_FRONT</Name>
      <MinAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxAimSweepHeadingAngleDegs value="190.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="190.000000" />
      <MinSmashWindowAngleDegs value="-180.000000" />
      <MaxSmashWindowAngleDegs value="0.000000" />
      <MinSmashWindowAngleFirstPersonDegs value="-180.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="-25.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-0.800000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="3.140000" />
      <DriveByAnimInfos>
        <Item ref="LOW_VETO_DB_ANIM_INFO_UNARMED_DS" />
        <Item ref="LOW_VETO_DB_ANIM_INFO_ONE_HANDED_DS" />
        <Item ref="LOW_VETO_DB_ANIM_INFO_THROW_DS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <PovDriveByCamera />
      <DriveByFlags>UseMountedProjectileTask LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_QUAD_VERUS_REAR</Name>
      <MinAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxAimSweepHeadingAngleDegs value="190.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="190.000000" />
      <MinSmashWindowAngleDegs value="-180.000000" />
      <MaxSmashWindowAngleDegs value="0.000000" />
      <MinSmashWindowAngleFirstPersonDegs value="-180.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="0.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="3.140000" />
      <DriveByAnimInfos>
        <Item ref="QUAD_VERUS_DB_ANIM_INFO_UNARMED_DS" />
        <Item ref="QUAD_VERUS_DB_ANIM_INFO_ONE_HANDED_DS" />
        <Item ref="QUAD_VERUS_DB_ANIM_INFO_THROW_DS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <PovDriveByCamera />
      <DriveByFlags>UseMountedProjectileTask</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_TRUCK_VETIR_FRONT_LEFT</Name>
      <MinAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxAimSweepHeadingAngleDegs value="190.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="190.000000" />
      <MinSmashWindowAngleDegs value="-180.000000" />
      <MaxSmashWindowAngleDegs value="-35.000000" />
      <MinSmashWindowAngleFirstPersonDegs value="-180.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="-35.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-0.400000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="3.140000" />
      <DriveByAnimInfos>
        <Item ref="TRUCK_DB_ANIM_INFO_UNARMED_DS" />
        <Item ref="TRUCK_VETIR_DB_ANIM_INFO_ONE_HANDED_DS" />
        <Item ref="TRUCK_VETIR_DB_ANIM_INFO_THROW_DS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <PovDriveByCamera />
      <DriveByFlags>UseThreeAnimIntroOutro UseMountedProjectileTask UseThreeAnimThrow LeftHandedProjctiles LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_TRUCK_VETIR_FRONT_RIGHT</Name>
      <MinAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxAimSweepHeadingAngleDegs value="190.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="190.000000" />
      <MinSmashWindowAngleDegs value="35.000000" />
      <MaxSmashWindowAngleDegs value="180.000000" />
      <MinSmashWindowAngleFirstPersonDegs value="35.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="180.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="0.400000" />
      <DriveByAnimInfos>
        <Item ref="TRUCK_DB_ANIM_INFO_UNARMED_PS" />
        <Item ref="TRUCK_VETIR_DB_ANIM_INFO_ONE_HANDED_PS" />
        <Item ref="TRUCK_VETIR_DB_ANIM_INFO_THROW_PS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <PovDriveByCamera />
      <DriveByFlags>UseThreeAnimIntroOutro UseMountedProjectileTask UseThreeAnimThrow LeftHandedFirstPersonAnims LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_STD_DINGY5_TURRET</Name>
      <MinAimSweepHeadingAngleDegs value="-60.000000" />
      <MaxAimSweepHeadingAngleDegs value="60.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-180.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="180.000000" />
      <MinSmashWindowAngleDegs value="0.000000" />
      <MaxSmashWindowAngleDegs value="0.000000" />
      <MinSmashWindowAngleFirstPersonDegs value="0.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="0.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="3.140000" />
      <DriveByAnimInfos />
      <DriveByCamera>V_HELI_REAR_LEFT_AIM_CAMERA</DriveByCamera>
      <PovDriveByCamera />
      <DriveByFlags>DampenRecoil UseThreeAnimIntroOutro</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_SLAMTRUCK_FRONT_LEFT</Name>
      <MinAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxAimSweepHeadingAngleDegs value="190.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="190.000000" />
      <MinSmashWindowAngleDegs value="-180.000000" />
      <MaxSmashWindowAngleDegs value="-25.000000" />
      <MinSmashWindowAngleFirstPersonDegs value="-180.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="-35.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-0.400000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="3.140000" />
      <DriveByAnimInfos>
        <Item ref="VAN_DB_ANIM_INFO_UNARMED_DS" />
        <Item ref="SLAMTRUCK_DB_ANIM_INFO_ONE_HANDED_DS" />
        <Item ref="VAN_DB_ANIM_INFO_THROW_DS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <PovDriveByCamera />
      <DriveByFlags>UseMountedProjectileTask UseThreeAnimThrow LeftHandedProjctiles LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
    <Item type="CVehicleDriveByInfo">
      <Name>DRIVEBY_SLAMTRUCK_FRONT_RIGHT</Name>
      <MinAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxAimSweepHeadingAngleDegs value="190.000000" />
      <FirstPersonMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonMaxAimSweepHeadingAngleDegs value="180.000000" />
      <FirstPersonUnarmedMinAimSweepHeadingAngleDegs value="-180.000000" />
      <FirstPersonUnarmedMaxAimSweepHeadingAngleDegs value="180.000000" />
      <MinRestrictedAimSweepHeadingAngleDegs value="-190.000000" />
      <MaxRestrictedAimSweepHeadingAngleDegs value="190.000000" />
      <MinSmashWindowAngleDegs value="25.000000" />
      <MaxSmashWindowAngleDegs value="180.000000" />
      <MinSmashWindowAngleFirstPersonDegs value="35.000000" />
      <MaxSmashWindowAngleFirstPersonDegs value="180.000000" />
      <MaxSpeedParam value="0.500000" />
      <MaxLongitudinalLeanBlendWeightDelta value="0.250000" />
      <MaxLateralLeanBlendWeightDelta value="0.250000" />
      <ApproachSpeedToWithinMaxBlendDelta value="1.000000" />
      <SpineAdditiveBlendInDelay value="0.050000" />
      <SpineAdditiveBlendInDurationStill value="0.150000" />
      <SpineAdditiveBlendInDuration value="0.750000" />
      <SpineAdditiveBlendOutDelay value="0.000000" />
      <SpineAdditiveBlendOutDuration value="0.500000" />
      <MinUnarmedDrivebyYawIfWindowRolledUp value="-3.140000" />
      <MaxUnarmedDrivebyYawIfWindowRolledUp value="0.400000" />
      <DriveByAnimInfos>
        <Item ref="VAN_DB_ANIM_INFO_UNARMED_PS" />
        <Item ref="SLAMTRUCK_DB_ANIM_INFO_ONE_HANDED_PS" />
        <Item ref="VAN_DB_ANIM_INFO_THROW_PS" />
      </DriveByAnimInfos>
      <DriveByCamera />
      <PovDriveByCamera />
      <DriveByFlags>UseMountedProjectileTask UseThreeAnimThrow LeftHandedFirstPersonAnims LeftHandedUnarmedFirstPersonAnims</DriveByFlags>
    </Item>
  </VehicleDriveByInfos>
  <VehicleSeatInfos>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_BOAT_DINGHY5_REAR_LEFT</Name>
      <SeatBoneName>seat_dside_r</SeatBoneName>
      <ShuffleLink>SEAT_BOAT_DINGHY5_REAR_RIGHT</ShuffleLink>
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsIdleSeat</SeatFlags>
      <HairScale value="0.000000" />
      <ShuffleLink2 />
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_BOAT_DINGHY5_REAR_RIGHT</Name>
      <SeatBoneName>seat_pside_r</SeatBoneName>
      <ShuffleLink>SEAT_BOAT_DINGHY5_REAR_LEFT</ShuffleLink>
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsIdleSeat</SeatFlags>
      <HairScale value="0.000000" />
      <ShuffleLink2 />
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_BOAT_DINGHY5_FRONT_TURRET</Name>
      <SeatBoneName>seat_dside_r1</SeatBoneName>
      <ShuffleLink />
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsIdleSeat PedInSeatTargetable KeepOnHeadProp UseSweepLoopsForTurret CameraSkipWaitingForTurretAlignment</SeatFlags>
      <HairScale value="0.000000" />
      <ShuffleLink2 />
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_LOW_TOREADOR_FRONT_LEFT</Name>
      <SeatBoneName>seat_dside_f</SeatBoneName>
      <ShuffleLink>SEAT_LOW_TOREADOR_FRONT_RIGHT</ShuffleLink>
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsFrontSeat</SeatFlags>
      <HairScale value="-0.325000" />
      <ShuffleLink2 />
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_LOW_TOREADOR_FRONT_RIGHT</Name>
      <SeatBoneName>seat_pside_f</SeatBoneName>
      <ShuffleLink>SEAT_LOW_TOREADOR_FRONT_LEFT</ShuffleLink>
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsFrontSeat</SeatFlags>
      <HairScale value="-0.325000" />
      <ShuffleLink2 />
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_SQUADDIE_REAR_LEFT</Name>
      <SeatBoneName>seat_dside_r</SeatBoneName>
      <ShuffleLink />
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>KeepOnHeadProp</SeatFlags>
      <HairScale value="0.000000" />
      <ShuffleLink2 />
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_SQUADDIE_REAR_RIGHT</Name>
      <SeatBoneName>seat_pside_r</SeatBoneName>
      <ShuffleLink />
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>KeepOnHeadProp</SeatFlags>
      <HairScale value="0.000000" />
      <ShuffleLink2 />
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_WINKY_FRONT_LEFT</Name>
      <SeatBoneName>seat_dside_f</SeatBoneName>
      <ShuffleLink>SEAT_WINKY_FRONT_RIGHT</ShuffleLink>
      <RearSeatLink>SEAT_WINKY_REAR</RearSeatLink>
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsFrontSeat PedInSeatTargetable KeepOnHeadProp</SeatFlags>
      <HairScale value="0.000000" />
      <ShuffleLink2 />
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_WINKY_FRONT_RIGHT</Name>
      <SeatBoneName>seat_pside_f</SeatBoneName>
      <ShuffleLink>SEAT_WINKY_FRONT_LEFT</ShuffleLink>
      <RearSeatLink>SEAT_WINKY_REAR</RearSeatLink>
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsFrontSeat PedInSeatTargetable KeepOnHeadProp</SeatFlags>
      <HairScale value="0.000000" />
      <ShuffleLink2 />
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_WINKY_REAR</Name>
      <SeatBoneName>seat_dside_r</SeatBoneName>
      <ShuffleLink />
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>PedInSeatTargetable KeepOnHeadProp</SeatFlags>
      <HairScale value="0.000000" />
      <ShuffleLink2 />
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_QUAD_VERUS_REAR</Name>
      <SeatBoneName>seat_dside_r</SeatBoneName>
      <ShuffleLink>SEAT_SINGLE_FRONT</ShuffleLink>
      <RearSeatLink>SEAT_QUAD_VERUS_REAR</RearSeatLink>
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags />
      <HairScale value="0.000000" />
      <ShuffleLink2 />
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_VETO_FRONT</Name>
      <SeatBoneName>seat_dside_f</SeatBoneName>
      <ShuffleLink />
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsFrontSeat KeepOnHeadProp</SeatFlags>
      <HairScale value="0.000000" />
      <ShuffleLink2 />
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_LOW_ITALIRSX_FRONT_LEFT</Name>
      <SeatBoneName>seat_dside_f</SeatBoneName>
      <ShuffleLink>SEAT_LOW_ITALIRSX_FRONT_RIGHT</ShuffleLink>
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsFrontSeat DisallowHeadProp</SeatFlags>
      <HairScale value="-0.370000" />
      <ShuffleLink2 />
    </Item>
    <Item type="CVehicleSeatInfo">
      <Name>SEAT_LOW_ITALIRSX_FRONT_RIGHT</Name>
      <SeatBoneName>seat_pside_f</SeatBoneName>
      <ShuffleLink>SEAT_LOW_ITALIRSX_FRONT_LEFT</ShuffleLink>
      <RearSeatLink />
      <DefaultCarTask>TASK_DRIVE_WANDER</DefaultCarTask>
      <SeatFlags>IsFrontSeat DisallowHeadProp</SeatFlags>
      <HairScale value="-0.370000" />
      <ShuffleLink2 />
    </Item>
  </VehicleSeatInfos>
  <VehicleSeatAnimInfos>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_BIKE_MANCHEZ2_FRONT</Name>
      <DriveByInfo ref="DRIVEBY_BIKE_DIRT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_DIRT_FRONT" />
      <PanicClipSet />
      <AgitatedClipSet />
	  <DuckedClipSet />
      <FemaleClipSet />
	  <LowriderLeanClipSet>clipset@veh@bike@dirt@front@base</LowriderLeanClipSet>
	  <AltLowriderLeanClipSet>anim@veh@sit_variations@dirt@front@idle_a</AltLowriderLeanClipSet>
      <LowLODIdleAnim>DIRT_BIKE_FRONT_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>ON_BIKE_DIRT_MANCHEZ2</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>CanDetachViaRagdoll UseBikeInVehicleAnims SupportsInAirState UseRestrictedTorsoLeanIK FallsOutWhenDead</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId />
      <FemaleGestureClipSetId />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_DINGHY5_FRONT_TURRET</Name>
      <DriveByInfo ref="DRIVEBY_STD_DINGY5_TURRET" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_DINGHY5_TURRET" />
      <PanicClipSet />
      <AgitatedClipSet />
      <DuckedClipSet />
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>TECHNICAL_TURRET_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_TURRET_SEATED</InVehicleMoveNetwork>
      <SeatAnimFlags>CanDetachViaRagdoll UseStandardInVehicleAnims RagdollWhenVehicleUpsideDown NoShunts</SeatAnimFlags>
      <SteeringSmoothing value="0.040000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId />
      <FemaleGestureClipSetId />
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_HELI_SEASPARROW2_FRONT_RIGHT</Name>
      <DriveByInfo ref="NULL" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_PLANE_MAMMATUS_FRONT_RIGHT" />
      <PanicClipSet />
      <AgitatedClipSet />
      <DuckedClipSet />
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>STD_CAR_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>WeaponAttachedToLeftHand UseStandardInVehicleAnims</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId />
      <FemaleGestureClipSetId />
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_LOW_ITALIRSX_FRONT_LEFT</Name>
      <DriveByInfo ref="DRIVEBY_LOW_ITALIRSX_FRONT_LEFT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_LOW_FRONT_LEFT" />
      <PanicClipSet>clipset@veh@low@ds@idle_panic</PanicClipSet>
      <AgitatedClipSet>clipset@veh@low@ds@idle_agitated</AgitatedClipSet>
      <DuckedClipSet>clipset@veh@low@ds@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>LOW_CAR_FRONT_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_CAR_LOW</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims HasPanicAnims UseCloseDoorBlendAnims</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_LOW_DS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_LOW_DS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="2.000000" />
      <FPSMaxSteeringRateOverride value="9.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_LOW_ITALIRSX_FRONT_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_LOW_ITALIRSX_FRONT_RIGHT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_LOW_FRONT_RIGHT" />
      <PanicClipSet>clipset@veh@low@ps@idle_panic</PanicClipSet>
      <AgitatedClipSet />
      <DuckedClipSet>clipset@veh@low@ps@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>LOW_CAR_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_CAR_LOW</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>WeaponAttachedToLeftHand UseStandardInVehicleAnims HasPanicAnims UseCloseDoorBlendAnims PreventShuffleJack</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_LOW_PS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_LOW_PS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_LOW_TOREADOR_FRONT_LEFT</Name>
      <DriveByInfo ref="NULL" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_LOW_TOREADOR_FRONT_LEFT" />
      <PanicClipSet>clipset@veh@low@toreador@ds@idle_panic</PanicClipSet>
      <AgitatedClipSet>clipset@veh@low@ds@idle_agitated</AgitatedClipSet>
      <DuckedClipSet>clipset@anim@veh@car@toreador@ds@duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>LOW_CAR_FRONT_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims HasPanicAnims UseCloseDoorBlendAnims</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_LOW_DS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_LOW_DS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="2.000000" />
      <FPSMaxSteeringRateOverride value="9.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_LOW_TOREADOR_FRONT_RIGHT</Name>
      <DriveByInfo ref="NULL" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_LOW_TOREADOR_FRONT_RIGHT" />
      <PanicClipSet>clipset@veh@low@toreador@ps@idle_panic</PanicClipSet>
      <AgitatedClipSet />
      <DuckedClipSet>clipset@veh@low@ps@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>LOW_CAR_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>WeaponAttachedToLeftHand UseStandardInVehicleAnims HasPanicAnims UseCloseDoorBlendAnims PreventShuffleJack</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_LOW_PS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_LOW_PS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_LOW_TOREADOR_REAR_LEFT</Name>
      <DriveByInfo ref="NULL" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_LOW_TOREADOR_REAR_LEFT" />
      <PanicClipSet>clipset@veh@low@toreador@rds@idle_panic</PanicClipSet>
      <AgitatedClipSet />
      <DuckedClipSet>clipset@veh@low@ps@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>LOW_CAR_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>WeaponAttachedToLeftHand UseStandardInVehicleAnims HasPanicAnims UseCloseDoorBlendAnims PreventShuffleJack</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_LOW_PS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_LOW_PS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_LOW_TOREADOR_REAR_RIGHT</Name>
      <DriveByInfo ref="NULL" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_LOW_TOREADOR_REAR_RIGHT" />
      <PanicClipSet>clipset@veh@low@toreador@rps@idle_panic</PanicClipSet>
      <AgitatedClipSet />
      <DuckedClipSet>clipset@veh@low@ps@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>LOW_CAR_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>WeaponAttachedToLeftHand UseStandardInVehicleAnims HasPanicAnims UseCloseDoorBlendAnims PreventShuffleJack</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_LOW_PS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_LOW_PS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_SQUADDIE_FRONT_LEFT</Name>
      <DriveByInfo ref="DRIVEBY_RANGER_4x4_FRONT_LEFT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_VAN_FRONT_LEFT" />
      <PanicClipSet>clipset@veh@van@ds@idle_panic</PanicClipSet>
      <AgitatedClipSet>clipset@veh@van@ds@idle_agitated</AgitatedClipSet>
      <DuckedClipSet>clipset@veh@van@ds@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>VAN_FRONT_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims UseCloseDoorBlendAnims</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName>MESA</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_VAN_DS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_VAN_DS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_SQUADDIE_FRONT_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_RANGER_4x4_FRONT_RIGHT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_VAN_FRONT_RIGHT" />
      <PanicClipSet>clipset@veh@van@ps@idle_panic</PanicClipSet>
      <AgitatedClipSet />
      <DuckedClipSet>clipset@veh@van@ps@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>VAN_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>WeaponAttachedToLeftHand UseStandardInVehicleAnims UseCloseDoorBlendAnims PreventShuffleJack</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName>MESA</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_VAN_PS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_VAN_PS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_SQUADDIE_REAR_LEFT</Name>
      <DriveByInfo ref="DRIVEBY_BARRACKS_REAR_LEFT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_VAN_REAR_LEFT" />
      <PanicClipSet />
      <AgitatedClipSet />
      <DuckedClipSet>clipset@veh@bus@passenger@common@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>VAN_REAR_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>CanDetachViaRagdoll UseStandardInVehicleAnims UseCloseDoorBlendAnims PreventShuffleJack RagdollWhenVehicleUpsideDown RagdollAtExtremeVehicleOrientation</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName>MESA</ExitToAimInfoName>
      <MaleGestureClipSetId />
      <FemaleGestureClipSetId />
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_SQUADDIE_REAR_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_BARRACKS_REAR_RIGHT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_SQUADDIE_REAR_RIGHT" />
      <PanicClipSet />
      <AgitatedClipSet />
      <DuckedClipSet>clipset@veh@bus@passenger@common@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>VAN_REAR_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>CanDetachViaRagdoll UseStandardInVehicleAnims UseCloseDoorBlendAnims PreventShuffleJack RagdollWhenVehicleUpsideDown RagdollAtExtremeVehicleOrientation</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName>MESA</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_VAN_PS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_VAN_PS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_STD_WEEVIL_FRONT_LEFT</Name>
      <DriveByInfo ref="DRIVEBY_WEEVIL_STANDARD_FRONT_LEFT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_STD_WEEVIL_FRONT_LEFT" />
      <PanicClipSet>anim@veh@std@issi3@ds@idle_panic</PanicClipSet>
      <AgitatedClipSet>anim@veh@std@issi3@ds@idle_agitated</AgitatedClipSet>
      <DuckedClipSet>anim@veh@std@issi3@ds@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>STD_CAR_FRONT_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims UseCloseDoorBlendAnims</SeatAnimFlags>
      <SteeringSmoothing value="0.040000" />
      <ExitToAimInfoName>STANDARD</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_STD_DS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_STD_DS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_STD_WEEVIL_FRONT_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_WEEVIL_STANDARD_FRONT_RIGHT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_STD_WEEVIL_FRONT_RIGHT" />
      <PanicClipSet>anim@veh@std@issi3@ps@idle_panic</PanicClipSet>
      <AgitatedClipSet />
      <DuckedClipSet>anim@veh@std@issi3@ps@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>STD_CAR_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims HasPanicAnims UseCloseDoorBlendAnims PreventShuffleJack</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName>STANDARD</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_STD_PS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_STD_PS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_WINKY_FRONT_LEFT</Name>
      <DriveByInfo ref="DRIVEBY_WINKY_FRONT_LEFT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_VAN_FRONT_LEFT" />
      <PanicClipSet>clipset@veh@van@ds@idle_panic</PanicClipSet>
      <AgitatedClipSet>clipset@veh@van@ds@idle_agitated</AgitatedClipSet>
      <DuckedClipSet>clipset@veh@forklift@ds@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>VAN_FRONT_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims UseCloseDoorBlendAnims WarpIntoSeatIfStoodOnIt</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName>MESA</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_VAN_DS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_VAN_DS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_WINKY_FRONT_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_WINKY_FRONT_RIGHT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_VAN_FRONT_RIGHT" />
      <PanicClipSet>clipset@veh@van@ps@idle_panic</PanicClipSet>
      <AgitatedClipSet />
      <DuckedClipSet>clipset@veh@van@ps@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>VAN_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_VAN</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims UseCloseDoorBlendAnims PreventShuffleJack WarpIntoSeatIfStoodOnIt</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName>MESA</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_VAN_PS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_VAN_PS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_WINKY_REAR</Name>
      <DriveByInfo ref="DRIVEBY_WINKY_REAR" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_WINKY_REAR" />
      <PanicClipSet>clipset@veh@jeep@winky@rds_rear@idle_panic</PanicClipSet>
      <AgitatedClipSet />
      <DuckedClipSet>clipset@veh@van@ps@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>VAN_REAR_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims UseCloseDoorBlendAnims WarpIntoSeatIfStoodOnIt</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName>MESA</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_VAN_DS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_VAN_DS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_QUAD_VERUS_FRONT</Name>
      <DriveByInfo ref="DRIVEBY_QUAD" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_QUAD_FRONT" />
      <PanicClipSet />
      <AgitatedClipSet />
      <DuckedClipSet />
      <FemaleClipSet />
      <LowriderLeanClipSet>clipset@veh@bike@quad@front@base</LowriderLeanClipSet>
      <AltLowriderLeanClipSet>anim@veh@sit_variations@quad@front@idle_a</AltLowriderLeanClipSet>
      <LowLODIdleAnim>BIKE_QUAD_FRONT_IDLE</LowLODIdleAnim>
      <SeatAmbientContext />
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseBikeInVehicleAnims PreventShuffleJack UseTorsoLeanIK</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId />
      <FemaleGestureClipSetId />
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_QUAD_VERUS_REAR</Name>
      <DriveByInfo ref="DRIVEBY_QUAD_VERUS_REAR" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_QUAD_VERUS_REAR" />
      <PanicClipSet />
      <AgitatedClipSet />
      <DuckedClipSet />
      <FemaleClipSet />
      <LowriderLeanClipSet>clipset@anim@veh@bike@quad@verus@rear@base</LowriderLeanClipSet>
      <AltLowriderLeanClipSet>anim@veh@sit_variations@quad@front@idle_a</AltLowriderLeanClipSet>
      <LowLODIdleAnim>BIKE_QUAD_FRONT_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>ON_BIKE_QUAD</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseBikeInVehicleAnims PreventShuffleJack FallsOutWhenDead UseTorsoLeanIK</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId />
      <FemaleGestureClipSetId />
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_VETO_FRONT</Name>
      <DriveByInfo ref="DRIVEBY_LOW_VETO_FRONT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_VETO" />
      <PanicClipSet />
      <AgitatedClipSet />
      <DuckedClipSet>clipset@anim@veh@gokart@generic@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>LOW_CAR_FRONT_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_KART</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_LOW_DS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_LOW_DS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="2.000000" />
      <FPSMaxSteeringRateOverride value="9.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_TRUCK_VETIR_FRONT_LEFT</Name>
      <DriveByInfo ref="DRIVEBY_TRUCK_VETIR_FRONT_LEFT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_TRUCK_FRONT_LEFT" />
      <PanicClipSet>clipset@veh@truck@ds@idle_panic</PanicClipSet>
      <AgitatedClipSet>clipset@veh@truck@ds@idle_agitated</AgitatedClipSet>
      <DuckedClipSet>clipset@veh@truck@ds@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>TRUCK_FRONT_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_TRUCK</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims HasPanicAnims</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_TRUCK_DS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_TRUCK_DS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_TRUCK_VETIR_FRONT_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_TRUCK_VETIR_FRONT_RIGHT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_TRUCK_FRONT_RIGHT" />
      <PanicClipSet>clipset@veh@truck@ps@idle_panic</PanicClipSet>
      <AgitatedClipSet />
      <DuckedClipSet>clipset@veh@truck@ps@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>TRUCK_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_TRUCK</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>WeaponAttachedToLeftHand UseStandardInVehicleAnims HasPanicAnims PreventShuffleJack</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName />
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_TRUCK_PS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_TRUCK_PS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_SLAMTRUCK_FRONT_LEFT</Name>
      <DriveByInfo ref="DRIVEBY_SLAMTRUCK_FRONT_LEFT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_VAN_FRONT_LEFT" />
      <PanicClipSet>clipset@veh@van@ds@idle_panic</PanicClipSet>
      <AgitatedClipSet>clipset@veh@van@ds@idle_agitated</AgitatedClipSet>
      <DuckedClipSet>clipset@veh@van@ds@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>VAN_FRONT_DS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_VAN</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>UseStandardInVehicleAnims UseCloseDoorBlendAnims</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName>MESA</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_VAN_DS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_VAN_DS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
    <Item type="CVehicleSeatAnimInfo">
      <Name>SEAT_ANIM_SLAMTRUCK_FRONT_RIGHT</Name>
      <DriveByInfo ref="DRIVEBY_SLAMTRUCK_FRONT_RIGHT" />
      <InsideClipSetMap ref="INSIDE_CLIPSET_MAP_VAN_FRONT_RIGHT" />
      <PanicClipSet>clipset@veh@van@ps@idle_panic</PanicClipSet>
      <AgitatedClipSet />
      <DuckedClipSet>clipset@veh@van@ps@idle_duck</DuckedClipSet>
      <FemaleClipSet />
      <LowriderLeanClipSet />
      <AltLowriderLeanClipSet />
      <LowLODIdleAnim>VAN_FRONT_PS_IDLE</LowLODIdleAnim>
      <SeatAmbientContext>IN_VAN</SeatAmbientContext>
      <InVehicleMoveNetwork>VEHICLE_DEFAULT</InVehicleMoveNetwork>
      <SeatAnimFlags>WeaponAttachedToLeftHand UseStandardInVehicleAnims UseCloseDoorBlendAnims PreventShuffleJack</SeatAnimFlags>
      <SteeringSmoothing value="0.100000" />
      <ExitToAimInfoName>MESA</ExitToAimInfoName>
      <MaleGestureClipSetId>ANIM_GROUP_GESTURE_M_CAR_VAN_PS</MaleGestureClipSetId>
      <FemaleGestureClipSetId>ANIM_GROUP_GESTURE_F_CAR_VAN_PS</FemaleGestureClipSetId>
      <FPSMinSteeringRateOverride value="-1.000000" />
      <FPSMaxSteeringRateOverride value="-1.000000" />
      <SeatCollisionBoundsOffset x="0.000000" y="0.000000" z="0.000000" />
    </Item>
  </VehicleSeatAnimInfos>
  <VehicleEntryPointInfos>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_BOAT_DINGHY5_TURRET_FRONT_LEFT</Name>
      <DoorBoneName />
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_BOAT_DINGHY5_FRONT_TURRET" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_DINGHY5_LEFT_TURRET" />
      <Flags>BlockJackReactionUntilJackerIsReady</Flags>
      <BlockJackReactionSides>
        <Item>SIDE_LEFT</Item>
      </BlockJackReactionSides>
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_BOAT_DINGHY5_TURRET_FRONT_RIGHT</Name>
      <DoorBoneName />
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_BOAT_DINGHY5_FRONT_TURRET" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_DINGHY5_RIGHT_TURRET" />
      <Flags>BlockJackReactionUntilJackerIsReady</Flags>
      <BlockJackReactionSides>
        <Item>SIDE_RIGHT</Item>
      </BlockJackReactionSides>
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_BOAT_DINGHY5_REAR_LEFT</Name>
      <DoorBoneName />
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_BOAT_DINGHY5_REAR_LEFT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_DINGHY5_REAR_LEFT" />
      <Flags />
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_BOAT_DINGHY5_REAR_RIGHT</Name>
      <DoorBoneName />
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_BOAT_DINGHY5_REAR_RIGHT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_DINGHY_RIGHT" />
      <Flags />
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_LOW_TOREADOR_FRONT_LEFT</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_dside_f</DoorHandleBoneName>
      <WindowId>FRONT_LEFT</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_LOW_TOREADOR_FRONT_LEFT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_LEFT" />
      <Flags />
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_LOW_TOREADOR_FRONT_RIGHT</Name>
      <DoorBoneName>door_pside_f</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_pside_f</DoorHandleBoneName>
      <WindowId>FRONT_RIGHT</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_LOW_TOREADOR_FRONT_RIGHT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_RIGHT" />
      <Flags />
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_PLANE_ALKONOST_FRONT_LEFT</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_dside_f</DoorHandleBoneName>
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_STANDARD_FRONT_LEFT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_PLANE_ALKONOST" />
      <Flags>IgnoreSmashWindowCheck</Flags>
      <BlockJackReactionSides />
      <BreakoutTestPoint>GET_IN</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_PLANE_ALKONOST_FRONT_RIGHT</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_dside_f</DoorHandleBoneName>
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_STANDARD_FRONT_RIGHT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_PLANE_ALKONOST" />
      <Flags>IgnoreSmashWindowCheck BlockJackReactionUntilJackerIsReady</Flags>
      <BlockJackReactionSides />
      <BreakoutTestPoint>GET_IN</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_MP_ALKONOST_WARP_REAR_LEFT</Name>
      <DoorBoneName>boot</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_STANDARD_NO_SHUFFLE_REAR_LEFT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags>MPWarpInOut ForceSkyDiveExitInAir</Flags>
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_MP_ALKONOST_WARP_REAR_RIGHT</Name>
      <DoorBoneName>boot</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_STANDARD_NO_SHUFFLE_REAR_RIGHT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags>MPWarpInOut ForceSkyDiveExitInAir</Flags>
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_MP_ALKONOST_WARP_REAR_EXTRA_LEFT</Name>
      <DoorBoneName>boot</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_1" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags>MPWarpInOut ForceSkyDiveExitInAir</Flags>
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_MP_ALKONOST_WARP_REAR_EXTRA_RIGHT</Name>
      <DoorBoneName>boot</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_1" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags>MPWarpInOut ForceSkyDiveExitInAir</Flags>
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_MP_ALKONOST_WARP_REAR_EXTRA_LEFT_1</Name>
      <DoorBoneName />
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_2" />
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_3" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags>MPWarpInOut</Flags>
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_MP_ALKONOST_WARP_REAR_EXTRA_RIGHT_1</Name>
      <DoorBoneName />
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_2" />
        <Item ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_3" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags>MPWarpInOut</Flags>
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_JEEP_WINKY_FRONT_LEFT</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_dside_f</DoorHandleBoneName>
      <WindowId>FRONT_LEFT</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_WINKY_FRONT_LEFT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_LEFT" />
      <Flags>BlockJackReactionUntilJackerIsReady</Flags>
      <BlockJackReactionSides>
        <Item>SIDE_LEFT</Item>
      </BlockJackReactionSides>
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_JEEP_WINKY_FRONT_RIGHT</Name>
      <DoorBoneName>door_pside_f</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_pside_f</DoorHandleBoneName>
      <WindowId>FRONT_RIGHT</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_WINKY_FRONT_RIGHT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_RIGHT" />
      <Flags>BlockJackReactionUntilJackerIsReady</Flags>
      <BlockJackReactionSides>
        <Item>SIDE_RIGHT</Item>
      </BlockJackReactionSides>
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_JEEP_WINKY_REAR_LEFT</Name>
      <DoorBoneName />
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_WINKY_REAR" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags />
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_JEEP_WINKY_REAR_RIGHT</Name>
      <DoorBoneName />
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_WINKY_REAR" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags />
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_QUAD_VERUS_REAR</Name>
      <DoorBoneName />
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_REAR</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_QUAD_VERUS_REAR" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags>IsPassengerOnlyEntry</Flags>
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_VETO_FRONT_LEFT</Name>
      <DoorBoneName />
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_VETO_FRONT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags />
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_VETO_FRONT_RIGHT</Name>
      <DoorBoneName />
      <SecondDoorBoneName />
      <DoorHandleBoneName />
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_VETO_FRONT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags />
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_SQUADDIE_REAR_LEFT</Name>
      <DoorBoneName>boot</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_dside_r</DoorHandleBoneName>
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_REAR</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_SQUADDIE_REAR_LEFT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags />
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_SQUADDIE_REAR_RIGHT</Name>
      <DoorBoneName>boot</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_dside_r</DoorHandleBoneName>
      <WindowId>INVALID</WindowId>
      <VehicleSide>SIDE_REAR</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_SQUADDIE_REAR_RIGHT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="NULL" />
      <Flags />
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_LOW_ITALIRSX_FRONT_LEFT</Name>
      <DoorBoneName>door_dside_f</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_dside_f</DoorHandleBoneName>
      <WindowId>FRONT_LEFT</WindowId>
      <VehicleSide>SIDE_LEFT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_LOW_ITALIRSX_FRONT_LEFT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_LEFT" />
      <Flags />
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
    <Item type="CVehicleEntryPointInfo">
      <Name>ENTRY_POINT_LOW_ITALIRSX_FRONT_RIGHT</Name>
      <DoorBoneName>door_pside_f</DoorBoneName>
      <SecondDoorBoneName />
      <DoorHandleBoneName>handle_pside_f</DoorHandleBoneName>
      <WindowId>FRONT_RIGHT</WindowId>
      <VehicleSide>SIDE_RIGHT</VehicleSide>
      <AccessableSeats>
        <Item ref="SEAT_LOW_ITALIRSX_FRONT_RIGHT" />
      </AccessableSeats>
      <VehicleExtraPointsInfo ref="EXTRA_VEHICLE_POINTS_STD_RIGHT" />
      <Flags />
      <BlockJackReactionSides />
      <BreakoutTestPoint>POINT_TYPE_MAX</BreakoutTestPoint>
    </Item>
  </VehicleEntryPointInfos>
  <VehicleEntryPointAnimInfos>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_HELI_ANNIHILATOR2_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_HELI_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_HELI_ANNIHILATOR_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.466000" y="-0.296000" z="0.050000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForEnter UsesNoDoorTransitionForExit UseGetUpAfterJack DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_HELICOPTER_ANNIHILATOR_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_HELI_ANNIHILATOR2_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_HELI_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_HELI_ANNIHILATOR_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.466000" y="-0.296000" z="0.050000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForEnter UsesNoDoorTransitionForExit UseGetUpAfterJack DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_HELICOPTER_ANNIHILATOR_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_HELI_ANNIHILATOR2_REAR_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_HELI_REAR_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_HELI_REAR_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.169200" y="-0.376600" z="0.200000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_HELICOPTER_REAR_LEFT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_HELI_ANNIHILATOR2_REAR_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_HELI_REAR_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_HELI_REAR_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.061200" y="-0.232100" z="0.150000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_HELICOPTER_REAR_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_HELI_ANNIHILATOR2_REAR_LEFT2</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_HELI_FROGGER_REAR_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_HELI_ANNIHILATOR2_REAR_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.791000" y="0.129000" z="-0.150000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit FixUpMoverToEntryPointOnExit UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_HELI_ANNIHILATOR2_REAR_RIGHT2</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_HELI_FROGGER_REAR_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_HELI_ANNIHILATOR2_REAR_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.791000" y="0.129000" z="-0.150000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit FixUpMoverToEntryPointOnExit UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_BOAT_DINGHY5_REAR_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_BOAT_DINGHY_FRONT_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_BOAT_DINGHY5_REAR_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_BOAT_DINGHY5_REAR_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.525000" y="0.195000" z="-0.126000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>HasGetInFromWater JackIncludesGetIn HasOnVehicleEntry UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn UseStandOnGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_BOAT_DINGHY5_REAR_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_BOAT_DINGHY_FRONT_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_BOAT_DINGHY_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_BOAT_DINGHY_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.466000" y="0.195000" z="-0.126000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>HasGetInFromWater JackIncludesGetIn HasOnVehicleEntry UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn UseStandOnGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_BOAT_DINGHY5_TURRET_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_STD_DINGHY5_LEFT_TURRET" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_DINGHY5_LEFT_TURRET" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_DINGHY5_LEFT_TURRET" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-2.100000" y="1.600000" z="0.626000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>UseVehicleRelativeEntryPosition HasClimbUp JackIncludesGetIn HasGetOutToVehicle UseNewPlaneSystem DontCloseDoorInside PreventJackInterrupt HasOnVehicleEntry UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_BOAT_DINGHY5_TURRET_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_STD_DINGHY5_RIGHT_TURRET" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_DINGHY5_RIGHT_TURRET" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_DINGHY5_RIGHT_TURRET" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="2.100000" y="1.600000" z="0.626000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>UseVehicleRelativeEntryPosition HasClimbUp JackIncludesGetIn HasGetOutToVehicle UseNewPlaneSystem DontCloseDoorInside PreventJackInterrupt HasOnVehicleEntry UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_BOAT_DINGHY5_TURRET_FRONT_LEFT_CLIMBUP</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_STD_DINGHY5_LEFT_TURRET" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_DINGHY5_LEFT_TURRET" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_DINGHY5_LEFT_TURRET" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-2.100000" y="1.600000" z="-0.174000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>UseVehicleRelativeEntryPosition HasClimbUp HasGetInFromWater JackIncludesGetIn HasGetOutToVehicle UseNewPlaneSystem DontCloseDoorInside PreventJackInterrupt HasOnVehicleEntry HasClimbUpFromWater UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_BOAT_DINGHY5_TURRET_FRONT_RIGHT_CLIMBUP</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_STD_DINGHY5_RIGHT_TURRET" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_DINGHY5_RIGHT_TURRET" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_DINGHY5_RIGHT_TURRET" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="2.100000" y="1.600000" z="-0.174000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>UseVehicleRelativeEntryPosition HasClimbUp HasGetInFromWater JackIncludesGetIn HasGetOutToVehicle UseNewPlaneSystem DontCloseDoorInside PreventJackInterrupt HasOnVehicleEntry HasClimbUpFromWater UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_HELI_SEASPARROW2_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_HELI_SEASPARROW2_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_HELI_SEASPARROW2_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.500000" y="-0.043000" z="-0.350000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForEnter DontCloseDoorOutside UseGetUpAfterJack DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_HELICOPTER_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_HELI_SEASPARROW2_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_HELI_SEASPARROW2_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_HELI_SEASPARROW2_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.500000" y="-0.143000" z="-0.350000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForEnter DontCloseDoorOutside UseGetUpAfterJack DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_HELICOPTER_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_JEEP_WINKY_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_JEEP_WINKY_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_JEEP_WINKY_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId>van_perp_ds_a</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>van_victim_ds_a</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.300000" y="-0.400000" z="0.420000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForEnter UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_VAN_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet>SLOWTRUCK</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_JEEP_WINKY_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_JEEP_WINKY_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_JEEP_WINKY_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId>van_perp_ps_a</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>van_victim_ps_a</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.300000" y="-0.400000" z="0.420000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForEnter UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_VAN_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_JEEP_WINKY_REAR_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_JEEP_WINKY_REAR_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_JEEP_WINKY_REAR_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.580000" y="0.160000" z="0.200000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForEnter UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_VAN_REAR_LEFT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_JEEP_WINKY_REAR_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_JEEP_WINKY_REAR_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_JEEP_WINKY_REAR_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.580000" y="0.160000" z="0.200000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForEnter UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_VAN_REAR_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_LOW_TOREADOR_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_LOW_TOREADOR_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_LOW_TOREADOR_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-0.931000" y="-0.803000" z="0.610300" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet>LOWCAR</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_LOW_TOREADOR_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_LOW_TOREADOR_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_LOW_TOREADOR_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.075000" y="-0.840000" z="0.610000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet>LOWCAR</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_LOW_TOREADOR_REAR_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_LOW_TOREADOR_REAR_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_LOW_TOREADOR_REAR_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-0.980600" y="-0.753000" z="0.610300" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet>LOWCAR</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_LOW_TOREADOR_REAR_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_LOW_TOREADOR_REAR_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_LOW_TOREADOR_REAR_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.875000" y="-0.800000" z="0.610000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet>LOWCAR</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_ALKONOST_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_ALKONOST" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_ALKONOST_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_ALKONOST_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.558000" y="-1.236000" z="-2.993000" />
      <OpenDoorTranslation x="-0.475000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UseNewPlaneSystem PreventJackInterrupt DontCloseDoorOutside JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_PLANE_SHAMAL_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_ALKONOST_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_ALKONOST" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_ALKONOST_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_ALKONOST_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-2.658000" y="-1.236000" z="-2.993000" />
      <OpenDoorTranslation x="-0.475000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UseNewPlaneSystem DontCloseDoorInside PreventJackInterrupt DontCloseDoorOutside JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_PLANE_SHAMAL_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_ALKONOST_REAR_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_ALKONOST" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_ALKONOST_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_ALKONOST_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.800000" y="-24.000000" z="-5.500000" />
      <OpenDoorTranslation x="-0.475000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>UseVehicleRelativeEntryPosition JackIncludesGetIn UseNewPlaneSystem DontCloseDoorInside DontCloseDoorOutside NavigateToWarpEntryPoint JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_PLANE_SHAMAL_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_ALKONOST_REAR_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_ALKONOST" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_ALKONOST_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_ALKONOST_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="2.000000" y="-24.000000" z="-5.500000" />
      <OpenDoorTranslation x="-0.475000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="1.500000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>UseVehicleRelativeEntryPosition JackIncludesGetIn UseNewPlaneSystem DontCloseDoorInside DontCloseDoorOutside NavigateToWarpEntryPoint JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_PLANE_SHAMAL_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_ALKONOST_REAR_EXTRA_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_ALKONOST" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_ALKONOST_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_ALKONOST_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.200000" y="-27.000000" z="-5.000000" />
      <OpenDoorTranslation x="-0.475000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="-1.500000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>UseVehicleRelativeEntryPosition JackIncludesGetIn UseNewPlaneSystem DontCloseDoorInside DontCloseDoorOutside NavigateToWarpEntryPoint JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_PLANE_SHAMAL_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_PLANE_ALKONOST_REAR_EXTRA_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_PLANE_ALKONOST" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_PLANE_ALKONOST_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_PLANE_ALKONOST_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.500000" y="-27.000000" z="-5.000000" />
      <OpenDoorTranslation x="-0.475000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="1.500000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>UseVehicleRelativeEntryPosition JackIncludesGetIn UseNewPlaneSystem DontCloseDoorInside DontCloseDoorOutside NavigateToWarpEntryPoint JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_PLANE_SHAMAL_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_SLAMTRUCK_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_SLAMTRUCK_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_SLAMTRUCK_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.150000" y="-0.275000" z="0.200000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet>SLOWTRUCK</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_SLAMTRUCK_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_SLAMTRUCK_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_SLAMTRUCK_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.150000" y="-0.275000" z="0.200000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_SQUADDIE_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_SQUADDIE_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_SQUADDIE_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId>van_perp_ds_a</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>van_victim_ds_a</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.200000" y="-0.550000" z="-0.200000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims FixUpMoverToEntryPointOnExit UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_VAN_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet>SLOWTRUCK</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_SQUADDIE_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_SQUADDIE_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_SQUADDIE_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId>van_perp_ps_a</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>van_victim_ps_a</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.200000" y="-0.600000" z="-0.200000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims FixUpMoverToEntryPointOnExit UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_VAN_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet>SLOWTRUCK</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_SQUADDIE_REAR_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_SQUADDIE_REAR_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_SQUADDIE_REAR_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.520000" y="0.275000" z="-0.510000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="1.600000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UseOpenDoorBlendAnims UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_VAN_REAR_LEFT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_SQUADDIE_REAR_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_SQUADDIE_REAR_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_SQUADDIE_REAR_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.580000" y="0.475000" z="-0.510000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="-1.500000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UseOpenDoorBlendAnims UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_VAN_REAR_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_QUAD_VERUS_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_BIKE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_QUAD_VERUS_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_QUAD_VERUS_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-0.821700" y="0.355400" z="0.134700" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn FixUpMoverToEntryPointOnExit UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet>MOTOBIKE</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_QUAD_VERUS_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_BIKE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_QUAD_VERUS_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_QUAD_VERUS_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.830000" y="0.377800" z="0.135300" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn FixUpMoverToEntryPointOnExit UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_QUAD_VERUS_REAR</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_BIKE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_QUAD_VERUS_REAR" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_QUAD_VERUS_REAR" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.000000" y="-0.650000" z="0.000000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="0.000000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet>MOTOBIKE</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_TRUCK_VETIR_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_TRUCK_VETIR_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_TRUCK_VETIR_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.440000" y="-0.280000" z="-0.764600" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn ForcedEntryIncludesGetIn UsesNoDoorTransitionForEnter UsesNoDoorTransitionForExit UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_TRUCK_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet>SLOWTRUCK</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_TRUCK_VETIR_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_TRUCK_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_TRUCK_VETIR_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_TRUCK_VETIR_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.440000" y="-0.280000" z="-0.764600" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="0.000000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn ForcedEntryIncludesGetIn UsesNoDoorTransitionForEnter UsesNoDoorTransitionForExit UseGetUpAfterJack JackVariationsIncludeGetIn DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_VETO_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_VETO_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_VETO_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-1.016100" y="-0.100000" z="1.300000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.000000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet>LOWCAR</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_VETO_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_VETO_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_VETO_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId />
      <AlternateForcedEntryClipId />
      <AlternateJackFromOutSideClipId />
      <AlternateBeJackedFromOutSideClipId />
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="1.016100" y="-0.100000" z="1.300000" />
      <OpenDoorTranslation x="0.000000" y="0.000000" />
      <OpenDoorHeadingChange value="-1.000000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack DeadJackIncludesGetIn</EntryPointFlags>
      <EntryAnimVariations ref="NULL" />
      <NMJumpFromVehicleTuningSet />
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_WEEVIL_FRONT_LEFT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_LEFT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_WEEVIL_FRONT_LEFT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_WEEVIL_FRONT_LEFT" />
      <AlternateTryLockedDoorClipId>STD_Locked_DS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>STD_Force_Entry_DS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId>std_perp_ds</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>std_victim_ds</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="-0.842000" y="-0.551000" z="0.500000" />
      <OpenDoorTranslation x="-0.050000" y="0.150000" />
      <OpenDoorHeadingChange value="1.570000" />
      <EntryHeadingChange value="-1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack JackVariationsIncludeGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_FRONT_LEFT" />
      <NMJumpFromVehicleTuningSet>STANDARD</NMJumpFromVehicleTuningSet>
    </Item>
    <Item type="CVehicleEntryPointAnimInfo">
      <Name>ENTRY_POINT_ANIM_WEEVIL_FRONT_RIGHT</Name>
      <CommonClipSetMap ref="COMMON_CLIPSET_MAP_AUTOMOBILE_RIGHT" />
      <EntryClipSetMap ref="ENTRY_CLIPSET_MAP_STD_WEEVIL_FRONT_RIGHT" />
      <ExitClipSetMap ref="EXIT_CLIPSET_MAP_STD_WEEVIL_FRONT_RIGHT" />
      <AlternateTryLockedDoorClipId>STD_Locked_PS</AlternateTryLockedDoorClipId>
      <AlternateForcedEntryClipId>STD_Force_Entry_PS</AlternateForcedEntryClipId>
      <AlternateJackFromOutSideClipId>std_perp_ps</AlternateJackFromOutSideClipId>
      <AlternateBeJackedFromOutSideClipId>std_victim_ps</AlternateBeJackedFromOutSideClipId>
      <AlternateEntryPointClipSetId />
      <EnterVehicleMoveNetwork>ENTER_VEHICLE_STD</EnterVehicleMoveNetwork>
      <EntryTranslation x="0.822600" y="-0.551700" z="0.500000" />
      <OpenDoorTranslation x="0.050000" y="0.150000" />
      <OpenDoorHeadingChange value="-1.570000" />
      <EntryHeadingChange value="1.570000" />
      <ExtraZForMPPlaneWarp value="-2.000000" />
      <EntryPointFlags>JackIncludesGetIn UsesNoDoorTransitionForExit UseOpenDoorBlendAnims UseGetUpAfterJack JackVariationsIncludeGetIn HasCombatEntry</EntryPointFlags>
      <EntryAnimVariations ref="ENTRY_ANIM_VARIATIONS_STANDARD_FRONT_RIGHT" />
      <NMJumpFromVehicleTuningSet />
    </Item>
  </VehicleEntryPointAnimInfos>
  <VehicleExplosionInfos>
    <Item type="CVehicleExplosionInfo">
      <Name>EXPLOSION_INFO_KOSATKA</Name>
      <ExplosionData>
        <Item>
          <ExplosionTag>EXP_TAG_SUBMARINE_BIG</ExplosionTag>
          <PositionAtPetrolTank value="false" />
          <PositionInBoundingBox value="false" />
          <DelayTimeMs value="1100" />
          <Scale value="1.000000" />
          <PositionOffset x="0.000000" y="-3.000000" z="6.500000" />
        </Item>
        <Item>
          <ExplosionTag>EXP_TAG_SUBMARINE_BIG</ExplosionTag>
          <PositionAtPetrolTank value="false" />
          <PositionInBoundingBox value="false" />
          <DelayTimeMs value="100" />
          <Scale value="1.000000" />
          <PositionOffset x="0.000000" y="20.000000" z="8.000000" />
        </Item>
        <Item>
          <ExplosionTag>EXP_TAG_SUBMARINE_BIG</ExplosionTag>
          <PositionAtPetrolTank value="false" />
          <PositionInBoundingBox value="false" />
          <DelayTimeMs value="600" />
          <Scale value="1.000000" />
          <PositionOffset x="0.000000" y="41.000000" z="2.500000" />
        </Item>
        <Item>
          <ExplosionTag>EXP_TAG_SUBMARINE_BIG</ExplosionTag>
          <PositionAtPetrolTank value="false" />
          <PositionInBoundingBox value="false" />
          <DelayTimeMs value="1550" />
          <Scale value="1.000000" />
          <PositionOffset x="0.000000" y="-26.000000" z="5.000000" />
        </Item>
        <Item>
          <ExplosionTag>EXP_TAG_SUBMARINE_BIG</ExplosionTag>
          <PositionAtPetrolTank value="false" />
          <PositionInBoundingBox value="false" />
          <DelayTimeMs value="2000" />
          <Scale value="1.000000" />
          <PositionOffset x="0.000000" y="-48.000000" z="3.000000" />
        </Item>
      </ExplosionData>
      <AdditionalPartVelocityMinAngle value="0.000000" />
      <AdditionalPartVelocityMaxAngle value="0.000000" />
      <AdditionalPartVelocityMinMagnitude value="0.000000" />
      <AdditionalPartVelocityMaxMagnitude value="0.000000" />
      <VehicleExplosionLODs>
        <Item>
          <Radius value="0.000000" />
          <PartDeletionChance value="0.000000" />
        </Item>
        <Item>
          <Radius value="5.910000" />
          <PartDeletionChance value="0.400000" />
        </Item>
        <Item>
          <Radius value="8.360000" />
          <PartDeletionChance value="0.800000" />
        </Item>
      </VehicleExplosionLODs>
    </Item>
  </VehicleExplosionInfos>
  <VehicleLayoutInfos>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_HELI_ANNIHILATOR2</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_HELI_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_HELI_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_HELI_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_HELI_REAR_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_LEFT_1" />
          <SeatAnimInfo ref="SEAT_ANIM_HELI_FROGGER_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_RIGHT_1" />
          <SeatAnimInfo ref="SEAT_ANIM_HELI_FROGGER_REAR_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_ANNIHILATOR2_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_ANNIHILATOR2_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_HELI_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_ANNIHILATOR2_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_HELI_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_ANNIHILATOR2_REAR_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_EXTRA_LEFT_1" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_ANNIHILATOR2_REAR_LEFT2" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_EXTRA_RIGHT_1" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_ANNIHILATOR2_REAR_RIGHT2" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseFinerAlignTolerance Use2DBodyBlend</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="NULL" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="25.000000" />
      <BodyLeanXApproachSpeed value="15.000000" />
      <BodyLeanXSmallDelta value="0.100000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets />
      <FirstPersonRoadRageClipSets />
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_BIKE_MANCHEZ2</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_BIKE_FRONT" />
          <SeatAnimInfo ref="SEAT_ANIM_BIKE_MANCHEZ2_FRONT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_BIKE_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_BIKE_DIRT_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_BIKE_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_BIKE_DIRT_FRONT_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims BikeLeansUnlessMoving UsePickUpPullUp UseStillToSitTransition UseFinerAlignTolerance DisableFastPoseWhenDrivebying</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <HandsUpClipSetId>busted_vehicle_bike_dirt</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_BOAT_DINGHY5</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_BOAT_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_BOAT_DINGHY_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_BOAT_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_BOAT_DINGHY_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_BOAT_DINGHY5_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_BOAT_DINGHY_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_BOAT_DINGHY5_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_BOAT_DINGHY_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_BOAT_DINGHY5_FRONT_TURRET" />
          <SeatAnimInfo ref="SEAT_ANIM_DINGHY5_FRONT_TURRET" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_BOAT_DINGHY_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_BOAT_DINGHY_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_BOAT_DINGHY_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_BOAT_DINGHY_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_BOAT_DINGHY5_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_BOAT_DINGHY5_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_BOAT_DINGHY5_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_BOAT_DINGHY5_REAR_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_BOAT_DINGHY_REARCLIMB_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_BOAT_DINGHY_REARCLIMB_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_BOAT_DINGHY_REARCLIMB_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_BOAT_DINGHY_REARCLIMB_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_BOAT_DINGHY5_TURRET_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_BOAT_DINGHY5_TURRET_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_BOAT_DINGHY5_TURRET_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_BOAT_DINGHY5_TURRET_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_BOAT_DINGHY5_TURRET_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_BOAT_DINGHY5_TURRET_FRONT_LEFT_CLIMBUP" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_BOAT_DINGHY5_TURRET_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_BOAT_DINGHY5_TURRET_FRONT_RIGHT_CLIMBUP" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseLeanSteerAnims DisableJackingAndBusting</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="BOAT_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="25.000000" />
      <BodyLeanXApproachSpeed value="15.000000" />
      <BodyLeanXSmallDelta value="0.100000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets />
      <FirstPersonRoadRageClipSets />
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_JEEP_WINKY</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_WINKY_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_WINKY_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_WINKY_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_WINKY_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_WINKY_REAR" />
          <SeatAnimInfo ref="SEAT_ANIM_WINKY_REAR" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_JEEP_WINKY_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_JEEP_WINKY_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_JEEP_WINKY_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_JEEP_WINKY_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_JEEP_WINKY_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_JEEP_WINKY_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_JEEP_WINKY_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_JEEP_WINKY_REAR_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseVanOpenDoorBlendParams UseDoorOscillation NoArmIkOnInsideCloseDoor UseLeanSteerAnims UseFinerAlignTolerance</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="NULL" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="3.000000" />
      <BodyLeanXApproachSpeed value="10.000000" />
      <BodyLeanXSmallDelta value="0.100000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@van@ds@idle_a</Item>
        <Item>clipset@veh@van@ds@idle_b</Item>
        <Item>clipset@veh@van@ds@idle_c</Item>
        <Item>clipset@veh@van@ds@idle_d</Item>
        <Item>clipset@veh@van@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@van@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@van@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@van@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_LOW_TOREADOR</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_LOW_TOREADOR_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_LOW_TOREADOR_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_LOW_TOREADOR_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_LOW_TOREADOR_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_LOW_TOREADOR_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_LOW_TOREADOR_REAR_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_LOW_TOREADOR_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_LOW_TOREADOR_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_LOW_TOREADOR_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_LOW_TOREADOR_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_LOW_TOREADOR_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_LOW_TOREADOR_REAR_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims NoArmIkOnInsideCloseDoor NoArmIkOnOutsideCloseDoor UseLeanSteerAnims UseSteeringWheelIk UseFinerAlignTolerance AutomaticCloseDoor</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_low</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="15.000000" />
      <BodyLeanXApproachSpeed value="1.000000" />
      <BodyLeanXSmallDelta value="0.400000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@low@ds@idle_a</Item>
        <Item>clipset@veh@low@ds@idle_b</Item>
        <Item>clipset@veh@low@ds@idle_c</Item>
        <Item>clipset@veh@low@ds@idle_d</Item>
        <Item>clipset@veh@low@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@low@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@low@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@low@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_LOW_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_PLANE_ALKONOST</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_JET_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_JET_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_NO_SHUFFLE_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_NO_SHUFFLE_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_1" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_1" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_2" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_2" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_LEFT_3" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_NO_SHUFFLE_RIGHT_3" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_TITAN_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_ALKONOST_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_ALKONOST_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_PLANE_ALKONOST_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_ALKONOST_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_MP_ALKONOST_WARP_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_ALKONOST_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_MP_ALKONOST_WARP_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_ALKONOST_REAR_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_MP_ALKONOST_WARP_REAR_EXTRA_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_ALKONOST_REAR_EXTRA_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_MP_ALKONOST_WARP_REAR_EXTRA_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_ALKONOST_REAR_EXTRA_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_MP_ALKONOST_WARP_REAR_EXTRA_LEFT_1" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_ALKONOST_REAR_EXTRA_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_MP_ALKONOST_WARP_REAR_EXTRA_RIGHT_1" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_PLANE_ALKONOST_REAR_EXTRA_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims AllowEarlyDoorAndSeatUnreservation DisableJackingAndBusting ClimbUpAfterOpenDoor UseFinerAlignTolerance</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="NULL" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="25.000000" />
      <BodyLeanXApproachSpeed value="15.000000" />
      <BodyLeanXSmallDelta value="0.100000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets />
      <FirstPersonRoadRageClipSets />
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_SLAMTRUCK</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_VAN_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_SLAMTRUCK_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_VAN_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_SLAMTRUCK_FRONT_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_VAN_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_SLAMTRUCK_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_VAN_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_SLAMTRUCK_FRONT_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseVanOpenDoorBlendParams UseDoorOscillation UseLeanSteerAnims UseLowerDoorBlockTest</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="NULL" />
      <HandsUpClipSetId>busted_vehicle_van</HandsUpClipSetId>
      <SteeringWheelOffset x="0.006700" y="0.330000" z="0.270000" />
      <MaxXAcceleration value="4.000000" />
      <BodyLeanXApproachSpeed value="5.000000" />
      <BodyLeanXSmallDelta value="0.300000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@van@ds@idle_a</Item>
        <Item>clipset@veh@van@ds@idle_b</Item>
        <Item>clipset@veh@van@ds@idle_c</Item>
        <Item>clipset@veh@van@ds@idle_d</Item>
        <Item>clipset@veh@van@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@van@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@van@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@van@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_SQUADDIE</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_SQUADDIE_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_SQUADDIE_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_SQUADDIE_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_SQUADDIE_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_SQUADDIE_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_SQUADDIE_REAR_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_SQUADDIE_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_SQUADDIE_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_SQUADDIE_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_SQUADDIE_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_SQUADDIE_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_SQUADDIE_REAR_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseVanOpenDoorBlendParams UseDoorOscillation UseLeanSteerAnims UseFinerAlignTolerance</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="NULL" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="4.000000" />
      <BodyLeanXApproachSpeed value="5.000000" />
      <BodyLeanXSmallDelta value="0.300000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@van@ds@idle_a</Item>
        <Item>clipset@veh@van@ds@idle_b</Item>
        <Item>clipset@veh@van@ds@idle_c</Item>
        <Item>clipset@veh@van@ds@idle_d</Item>
        <Item>clipset@veh@van@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@van@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@van@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@van@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_TRUCK_VETIR</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_TRUCK_VETIR_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_TRUCK_VETIR_FRONT_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_BARRACKS_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_REAR_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_BARRACKS_REAR_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_LEFT_1" />
          <SeatAnimInfo ref="SEAT_ANIM_BARRACKS_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_RIGHT_1" />
          <SeatAnimInfo ref="SEAT_ANIM_BARRACKS_REAR_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_LEFT_2" />
          <SeatAnimInfo ref="SEAT_ANIM_BARRACKS_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_RIGHT_2" />
          <SeatAnimInfo ref="SEAT_ANIM_BARRACKS_REAR_RIGHT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_LEFT_3" />
          <SeatAnimInfo ref="SEAT_ANIM_BARRACKS_REAR_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_EXTRA_RIGHT_3" />
          <SeatAnimInfo ref="SEAT_ANIM_BARRACKS_REAR_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_TRUCK_VETIR_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_TRUCK_VETIR_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_BARRACKS_REAR_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_BARRACKS_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_BARRACKS_REAR_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_BARRACKS_REAR_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_EXTRA_LEFT_1" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_BARRACKS_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_EXTRA_RIGHT_1" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_BARRACKS_REAR_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_EXTRA_LEFT_2" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_BARRACKS_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_EXTRA_RIGHT_2" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_BARRACKS_REAR_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_EXTRA_LEFT_3" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_BARRACKS_REAR_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_EXTRA_RIGHT_3" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_BARRACKS_REAR_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseLeanSteerAnims UseFinerAlignTolerance</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="NULL" />
      <HandsUpClipSetId>busted_vehicle_truck</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="4.000000" />
      <BodyLeanXApproachSpeed value="5.000000" />
      <BodyLeanXSmallDelta value="0.300000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@truck@ds@idle_a</Item>
        <Item>clipset@veh@truck@ds@idle_b</Item>
        <Item>clipset@veh@truck@ds@idle_c</Item>
        <Item>clipset@veh@truck@ds@idle_d</Item>
        <Item>clipset@veh@truck@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@truck@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@truck@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@truck@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_HELI_SEASPARROW2</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_PLANE_MAMMATUS_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_HELI_SEASPARROW2_FRONT_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_SEASPARROW2_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_HELI_SEASPARROW2_FRONT_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims NoArmIkOnOutsideOpenDoor UseFinerAlignTolerance Use2DBodyBlend</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="NULL" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="25.000000" />
      <BodyLeanXApproachSpeed value="15.000000" />
      <BodyLeanXSmallDelta value="0.100000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets />
      <FirstPersonRoadRageClipSets />
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_BIKE_QUAD_VERUS</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_SINGLE_FRONT" />
          <SeatAnimInfo ref="SEAT_ANIM_QUAD_VERUS_FRONT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_QUAD_VERUS_REAR" />
          <SeatAnimInfo ref="SEAT_ANIM_QUAD_VERUS_REAR" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_SINGLE_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_QUAD_VERUS_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_SINGLE_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_QUAD_VERUS_FRONT_RIGHT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_QUAD_VERUS_REAR" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_QUAD_VERUS_REAR" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseFinerAlignTolerance</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="NULL" />
      <HandsUpClipSetId>busted_vehicle_bike_police</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="25.000000" />
      <BodyLeanXApproachSpeed value="15.000000" />
      <BodyLeanXSmallDelta value="0.100000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets />
      <FirstPersonRoadRageClipSets />
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_VETO</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_VETO_FRONT" />
          <SeatAnimInfo ref="SEAT_ANIM_VETO_FRONT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_VETO_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VETO_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_VETO_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_VETO_FRONT_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseLeanSteerAnims UseSteeringWheelIk</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_low</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="15.000000" />
      <BodyLeanXApproachSpeed value="10.000000" />
      <BodyLeanXSmallDelta value="0.400000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@low@ds@idle_a</Item>
        <Item>clipset@veh@low@ds@idle_b</Item>
        <Item>clipset@veh@low@ds@idle_c</Item>
        <Item>clipset@veh@low@ds@idle_d</Item>
        <Item>clipset@veh@low@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@low@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@low@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@low@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_STD_WEEVIL</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_STD_WEEVIL_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_STANDARD_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_STD_WEEVIL_FRONT_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_WEEVIL_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_STANDARD_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_WEEVIL_FRONT_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk UseFinerAlignTolerance</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_std</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="4.000000" />
      <BodyLeanXApproachSpeed value="5.000000" />
      <BodyLeanXSmallDelta value="0.300000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@std@ds@idle_a</Item>
        <Item>clipset@veh@std@ds@idle_b</Item>
        <Item>clipset@veh@std@ds@idle_c</Item>
        <Item>clipset@veh@std@ds@idle_d</Item>
        <Item>clipset@veh@std@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@std@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@std@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
    <Item type="CVehicleLayoutInfo">
      <Name>LAYOUT_LOW_ITALIRSX</Name>
      <Seats>
        <Item>
          <SeatInfo ref="SEAT_LOW_ITALIRSX_FRONT_LEFT" />
          <SeatAnimInfo ref="SEAT_ANIM_LOW_ITALIRSX_FRONT_LEFT" />
        </Item>
        <Item>
          <SeatInfo ref="SEAT_LOW_ITALIRSX_FRONT_RIGHT" />
          <SeatAnimInfo ref="SEAT_ANIM_LOW_ITALIRSX_FRONT_RIGHT" />
        </Item>
      </Seats>
      <EntryPoints>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_LOW_ITALIRSX_FRONT_LEFT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_LOW_FRONT_LEFT" />
        </Item>
        <Item>
          <EntryPointInfo ref="ENTRY_POINT_LOW_ITALIRSX_FRONT_RIGHT" />
          <EntryPointAnimInfo ref="ENTRY_POINT_ANIM_LOW_FRONT_RIGHT" />
        </Item>
      </EntryPoints>
      <LayoutFlags>StreamAnims UseDoorOscillation UseLeanSteerAnims UseSteeringWheelIk</LayoutFlags>
      <BicycleInfo ref="NULL" />
      <AnimRateSet ref="STD_LOW_ANIM_RATE_SET" />
      <HandsUpClipSetId>busted_vehicle_low</HandsUpClipSetId>
      <SteeringWheelOffset x="0.000000" y="0.350000" z="0.320000" />
      <MaxXAcceleration value="15.000000" />
      <BodyLeanXApproachSpeed value="10.000000" />
      <BodyLeanXSmallDelta value="0.400000" />
      <LookBackApproachSpeedScale value="1.000000" />
      <FirstPersonAdditiveIdleClipSets>
        <Item>clipset@veh@low@ds@idle_a</Item>
        <Item>clipset@veh@low@ds@idle_b</Item>
        <Item>clipset@veh@low@ds@idle_c</Item>
        <Item>clipset@veh@low@ds@idle_d</Item>
        <Item>clipset@veh@low@ds@idle_e</Item>
      </FirstPersonAdditiveIdleClipSets>
      <FirstPersonRoadRageClipSets>
        <Item>clipset@veh@low@ds@hit_wheel@idle_a</Item>
        <Item>clipset@veh@low@ds@hit_wheel@idle_b</Item>
        <Item>clipset@veh@low@ds@hit_wheel@idle_c</Item>
      </FirstPersonRoadRageClipSets>
      <CellphoneClipsets>
        <CellphoneClipsetDS>ANIM_GROUP_CELLPHONE_IN_CAR_DS</CellphoneClipsetDS>
        <CellphoneClipsetPS>ANIM_GROUP_CELLPHONE_IN_CAR_PS</CellphoneClipsetPS>
        <CellphoneClipsetDS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetDS_FPS>
        <CellphoneClipsetPS_FPS>ANIM_GROUP_CELLPHONE_IN_CAR_DS_FPS</CellphoneClipsetPS_FPS>
      </CellphoneClipsets>
    </Item>
  </VehicleLayoutInfos>
  <VehicleScenarioLayoutInfos />
  <SeatOverrideAnimInfos />
  <InVehicleOverrideInfos />
  <FirstPersonDriveByLookAroundData>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_BRIOSO2_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-186.000000" y="130.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.200000" />
            <AngleToBlendInOffset x="80.000000" y="150.000000" />
          </Item>
          <Item>
            <Offset value="0.120000" />
            <AngleToBlendInOffset x="30.000000" y="150.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="150.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="-5.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="150.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.050000" />
            <AngleToBlendInOffset x="50.000000" y="186.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="95.000000" />
          </Item>
          <Item>
            <Offset value="0.080000" />
            <AngleToBlendInOffset x="50.000000" y="95.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-6.000000" y="-16.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="150.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.000000" y="0.300000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.065000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.060000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_BRIOSO2_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-186.000000" y="150.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="50.000000" y="186.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="95.000000" />
          </Item>
          <Item>
            <Offset value="0.080000" />
            <AngleToBlendInOffset x="50.000000" y="95.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-6.000000" y="-16.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="150.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="60.000000" y="150.000000" />
          </Item>
          <Item>
            <Offset value="0.120000" />
            <AngleToBlendInOffset x="30.000000" y="150.000000" />
          </Item>
          <Item>
            <Offset value="-0.000000" />
            <AngleToBlendInOffset x="0.000000" y="150.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="-5.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="150.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.000000" y="0.300000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.065000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.060000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>JEEP_WINKY_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-195.000000" y="165.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.300000" />
            <AngleToBlendInOffset x="40.000000" y="165.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.090000" />
            <AngleToBlendInOffset x="5.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-17.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="5.000000" y="70.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.090000" />
            <AngleToBlendInOffset x="30.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="20.000000" />
        <AngleToBlendInExtraPitch x="30.000000" y="90.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.020000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>JEEP_WINKY_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-195.000000" y="165.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="-0.200000" />
            <AngleToBlendInOffset x="0.000000" y="195.000000" />
          </Item>
          <Item>
            <Offset value="0.090000" />
            <AngleToBlendInOffset x="30.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="20.000000" />
        <AngleToBlendInExtraPitch x="30.000000" y="90.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.300000" />
            <AngleToBlendInOffset x="40.000000" y="165.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="5.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-17.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="5.000000" y="70.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>JEEP_WINKY_REAR</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-190.000000" y="150.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="100.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="-0.010000" />
            <AngleToBlendInOffset x="100.000000" y="190.000000" />
          </Item>
          <Item>
            <Offset value="0.125000" />
            <AngleToBlendInOffset x="-90.000000" y="10.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="20.000000" />
        <AngleToBlendInExtraPitch x="25.000000" y="50.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="100.000000" y="150.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.125000" />
            <AngleToBlendInOffset x="-90.000000" y="10.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="20.000000" />
        <AngleToBlendInExtraPitch x="25.000000" y="50.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>PLANE_ALKONOST_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-90.000000" y="90.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="0.000000" y="70.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-3.000000" y="-11.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.350000" />
            <AngleToBlendInOffset x="5.000000" y="50.000000" />
          </Item>
          <Item>
            <Offset value="0.080000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-5.000000" y="-11.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>SLAMTRUCK_FRONT_LEFT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-92.000000" y="125.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.200000" />
            <AngleToBlendInOffset x="70.000000" y="125.000000" />
          </Item>
          <Item>
            <Offset value="0.170000" />
            <AngleToBlendInOffset x="40.000000" y="125.000000" />
          </Item>
          <Item>
            <Offset value="0.020000" />
            <AngleToBlendInOffset x="30.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-20.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="125.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.020000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="50.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-8.000000" y="-10.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="100.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.280000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.600000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>SLAMTRUCK_FRONT_RIGHT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-97.000000" y="125.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.020000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="50.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="-11.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="70.000000" y="125.000000" />
          </Item>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="40.000000" y="125.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="-11.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="0.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.280000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.600000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>SQUADDIE_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-175.000000" y="170.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.480000" />
            <AngleToBlendInOffset x="40.000000" y="170.000000" />
          </Item>
          <Item>
            <Offset value="0.250000" />
            <AngleToBlendInOffset x="40.000000" y="170.000000" />
          </Item>
          <Item>
            <Offset value="0.020000" />
            <AngleToBlendInOffset x="10.000000" y="40.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="5.000000" y="50.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.300000" />
            <AngleToBlendInOffset x="70.000000" y="170.000000" />
          </Item>
          <Item>
            <Offset value="0.300000" />
            <AngleToBlendInOffset x="70.000000" y="170.000000" />
          </Item>
          <Item>
            <Offset value="0.035000" />
            <AngleToBlendInOffset x="70.000000" y="170.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-3.000000" y="-4.000000" />
        <AngleToBlendInExtraPitch x="140.000000" y="170.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>SQUADDIE_FRONT_RIGHT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-180.000000" y="170.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.300000" />
            <AngleToBlendInOffset x="40.000000" y="170.000000" />
          </Item>
          <Item>
            <Offset value="0.300000" />
            <AngleToBlendInOffset x="40.000000" y="170.000000" />
          </Item>
          <Item>
            <Offset value="0.035000" />
            <AngleToBlendInOffset x="0.100000" y="40.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-3.000000" y="-4.000000" />
        <AngleToBlendInExtraPitch x="170.000000" y="110.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.400000" />
            <AngleToBlendInOffset x="55.000000" y="170.000000" />
          </Item>
          <Item>
            <Offset value="0.300000" />
            <AngleToBlendInOffset x="40.000000" y="170.000000" />
          </Item>
          <Item>
            <Offset value="0.015000" />
            <AngleToBlendInOffset x="0.000000" y="55.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="5.000000" y="50.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>TRUCK_VETIR_FRONT_LEFT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-95.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.400000" />
            <AngleToBlendInOffset x="60.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="45.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.120000" />
            <AngleToBlendInOffset x="20.000000" y="60.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-20.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="50.000000" y="160.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.120000" />
            <AngleToBlendInOffset x="20.000000" y="60.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="0.000000" />
        <AngleToBlendInExtraPitch x="60.000000" y="160.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>TRUCK_VETIR_FRONT_RIGHT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-95.000000" y="160.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-2.000000" y="2.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="80.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.400000" />
            <AngleToBlendInOffset x="60.000000" y="160.000000" />
          </Item>
          <Item>
            <Offset value="0.170000" />
            <AngleToBlendInOffset x="45.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.120000" />
            <AngleToBlendInOffset x="0.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-20.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="10.000000" y="160.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>TRUCK_VETIR_REAR_LEFT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-150.000000" y="140.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.250000" />
            <AngleToBlendInOffset x="0.000000" y="140.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="45.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.010000" />
            <AngleToBlendInOffset x="15.000000" y="60.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-20.000000" y="2.000000" />
        <AngleToBlendInExtraPitch x="5.000000" y="55.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="0.000000" />
        <AngleToBlendInExtraPitch x="15.000000" y="90.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>TRUCK_VETIR_REAR_RIGHT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-150.000000" y="130.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="0.000000" />
        <AngleToBlendInExtraPitch x="15.000000" y="90.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="5.000000" y="55.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>TRUCK_VETIR_REAR_EXTRA_LEFT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-150.000000" y="150.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.050000" />
            <AngleToBlendInOffset x="120.000000" y="140.000000" />
          </Item>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="45.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.010000" />
            <AngleToBlendInOffset x="15.000000" y="60.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-20.000000" y="-5.000000" />
        <AngleToBlendInExtraPitch x="90.000000" y="140.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="-0.100000" />
            <AngleToBlendInOffset x="0.000000" y="50.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="-5.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="50.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>TRUCK_VETIR_REAR_EXTRA_RIGHT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-150.000000" y="130.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="-5.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="50.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="50.000000" />
          </Item>
          <Item>
            <Offset value="0.020000" />
            <AngleToBlendInOffset x="0.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="-5.000000" />
        <AngleToBlendInExtraPitch x="90.000000" y="140.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>TRUCK_VETIR_REAR_EXTRA_LEFT_1</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-150.000000" y="140.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.050000" />
            <AngleToBlendInOffset x="120.000000" y="140.000000" />
          </Item>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="45.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.010000" />
            <AngleToBlendInOffset x="15.000000" y="60.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-20.000000" y="-5.000000" />
        <AngleToBlendInExtraPitch x="90.000000" y="140.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="-5.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="60.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>TRUCK_VETIR_REAR_EXTRA_RIGHT_1</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-150.000000" y="130.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="-5.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="40.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="50.000000" />
          </Item>
          <Item>
            <Offset value="0.020000" />
            <AngleToBlendInOffset x="0.000000" y="50.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="-5.000000" />
        <AngleToBlendInExtraPitch x="90.000000" y="140.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_ITALIRSX_FRONT_LEFT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-95.000000" y="140.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.200000" />
            <AngleToBlendInOffset x="0.000000" y="70.000000" />
          </Item>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-5.000000" y="2.000000" />
        <AngleToBlendInExtraPitch x="50.000000" y="120.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.020000" />
            <AngleToBlendInOffset x="50.000000" y="80.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="0.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-3.000000" y="-17.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="120.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.340000" y="0.400000" />
        <HeadingLimitsRight x="0.450000" y="0.500000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.065000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.000000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_ITALIRSX_FRONT_RIGHT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-95.000000" y="140.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.020000" />
            <AngleToBlendInOffset x="50.000000" y="80.000000" />
          </Item>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="0.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-3.000000" y="-17.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="120.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="0.000000" y="70.000000" />
          </Item>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="0.000000" y="120.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-5.000000" y="2.000000" />
        <AngleToBlendInExtraPitch x="50.000000" y="120.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.340000" y="0.400000" />
        <HeadingLimitsRight x="0.450000" y="0.500000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.065000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.000000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>LOW_VETO_FRONT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-140.000000" y="150.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.200000" />
            <AngleToBlendInOffset x="100.000000" y="140.000000" />
          </Item>
          <Item>
            <Offset value="0.250000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="70.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.200000" />
            <AngleToBlendInOffset x="100.000000" y="140.000000" />
          </Item>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="0.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="0.000000" y="140.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="10.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="50.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.340000" y="0.400000" />
        <HeadingLimitsRight x="0.450000" y="0.500000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.065000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.000000" y="0.020000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>BOAT_DINGHY5_FRONT_LEFT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-180.000000" y="180.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.050000" />
            <AngleToBlendInOffset x="5.000000" y="50.000000" />
          </Item>
          <Item>
            <Offset value="0.100000" />
            <AngleToBlendInOffset x="25.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-12.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="30.000000" y="80.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="-0.130000" />
            <AngleToBlendInOffset x="0.000000" y="45.000000" />
          </Item>
          <Item>
            <Offset value="-0.100000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-12.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="30.000000" y="80.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>BOAT_DINGHY5_FRONT_RIGHT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-180.000000" y="180.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.130000" />
            <AngleToBlendInOffset x="0.000000" y="45.000000" />
          </Item>
          <Item>
            <Offset value="-0.100000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-12.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="30.000000" y="80.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-12.000000" y="5.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="70.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_WEEVIL_FRONT_LEFT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-95.000000" y="105.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.050000" />
            <AngleToBlendInOffset x="0.000000" y="80.000000" />
          </Item>
          <Item>
            <Offset value="0.230000" />
            <AngleToBlendInOffset x="50.000000" y="105.000000" />
          </Item>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="0.000000" y="60.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="-13.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="105.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-7.500000" y="-5.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="60.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>STD_WEEVIL_FRONT_RIGHT</Name>
      <AllowLookback value="false" />
      <HeadingLimits x="-95.000000" y="105.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.050000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.150000" />
            <AngleToBlendInOffset x="45.000000" y="90.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-7.500000" y="-5.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="60.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="0.000000" y="60.000000" />
          </Item>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="50.000000" y="105.000000" />
          </Item>
          <Item>
            <Offset value="0.040000" />
            <AngleToBlendInOffset x="0.000000" y="60.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-13.000000" y="-10.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="105.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>BIKE_QUAD_VERUS_FRONT</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-150.000000" y="150.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="0.000000" y="100.000000" />
          </Item>
          <Item>
            <Offset value="-0.150000" />
            <AngleToBlendInOffset x="100.000000" y="135.000000" />
          </Item>
          <Item>
            <Offset value="0.300000" />
            <AngleToBlendInOffset x="0.000000" y="30.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="15.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="30.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.400000" />
            <AngleToBlendInOffset x="0.000000" y="135.000000" />
          </Item>
          <Item>
            <Offset value="0.025000" />
            <AngleToBlendInOffset x="100.000000" y="135.000000" />
          </Item>
          <Item>
            <Offset value="0.300000" />
            <AngleToBlendInOffset x="0.000000" y="30.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-15.000000" y="15.000000" />
        <AngleToBlendInExtraPitch x="0.000000" y="30.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
    <Item type="CFirstPersonDriveByLookAroundData">
      <Name>BIKE_QUAD_VERUS_REAR</Name>
      <AllowLookback value="true" />
      <HeadingLimits x="-170.000000" y="180.000000" />
      <DataLeft>
        <Offsets>
          <Item>
            <Offset value="-0.200000" />
            <AngleToBlendInOffset x="0.000000" y="30.000000" />
          </Item>
          <Item>
            <Offset value="0.000000" />
            <AngleToBlendInOffset x="0.000000" y="0.000000" />
          </Item>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="0.000000" y="30.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="0.000000" />
        <AngleToBlendInExtraPitch x="10.000000" y="180.000000" />
      </DataLeft>
      <DataRight>
        <Offsets>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="0.000000" y="20.000000" />
          </Item>
          <Item>
            <Offset value="-0.300000" />
            <AngleToBlendInOffset x="0.000000" y="180.000000" />
          </Item>
          <Item>
            <Offset value="0.200000" />
            <AngleToBlendInOffset x="0.000000" y="30.000000" />
          </Item>
        </Offsets>
        <ExtraRelativePitch x="-10.000000" y="0.000000" />
        <AngleToBlendInExtraPitch x="10.000000" y="180.000000" />
      </DataRight>
      <WheelClipInfo>
        <HeadingLimitsLeft x="0.380000" y="0.430000" />
        <HeadingLimitsRight x="0.480000" y="0.530000" />
        <PitchLimits x="0.470000" y="0.530000" />
        <PitchOffset x="0.000000" y="0.050000" />
        <WheelAngleLimits x="-0.700000" y="0.000000" />
        <WheelAngleOffset x="0.010000" y="0.040000" />
        <MaxWheelOffsetY value="0.170000" />
        <WheelClipLerpInRate value="0.150000" />
        <WheelClipLerpOutRate value="0.180000" />
      </WheelClipInfo>
    </Item>
  </FirstPersonDriveByLookAroundData>
</CVehicleMetadataMgr>