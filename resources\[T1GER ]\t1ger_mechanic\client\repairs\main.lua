local repairStations = {}
usingHoodAnim = false

--- Fully repairs the vehicle ped is inside
local function QuickRepair()
    local vehicle = GetVehiclePedIsIn(player, false)
    if not vehicle or not DoesEntityExist(vehicle) then return end

    SetVehicleFixed(vehicle)
    SetVehicleBodyHealth(vehicle, 1000.0)
    SetVehicleEngineHealth(vehicle, 1000.0)

    if Config.AdminRepair.Refuel then
        _API.SetVehicleFuel(vehicle, 100.0)
    end

    if Config.AdminRepair.Parts then
        -- core parts:
        SetAllCorePartsHealth(vehicle, 100.0)

        -- service parts:
        SetAllServicePartsMileage(vehicle, 0)
    end
    RepairSound()
end
exports("QuickRepair", QuickRepair)

--- Command to fix vehicle:
if Config.AdminRepair.Enable then
    RegisterCommand(Config.AdminRepair.Command, function(source, args, rawCommand)
        if _API.Player.isAdmin then
            QuickRepair()
        end
    end, false)
end

--- Player animation to close vehicle hood
--- @param vehicle number The vehicle entity handle
--- @param close boolean If `true` closes hood, otherwise `false` opens hood
function VehicleHoodAnimation(vehicle, close)
    usingHoodAnim = true
    if close then
        if GetIsDoorValid(vehicle, 4) and not IsVehicleDoorDamaged(vehicle, 4) then
            local anim = { dict = 'anim@heists@fleeca_bank@scope_out@return_case', clip = 'trevor_action', rot = vector3(0.0, 0.0, GetEntityHeading(player)), blendIn = 2.0, blendOut = 2.0, duration = 1000, flags = 49, time = 0.25 }
            lib.requestAnimDict(anim.dict)
            TaskPlayAnimAdvanced(player, anim.dict, anim.clip, coords.x, coords.y, coords.z, anim.rot.x, anim.rot.y, anim.rot.z, anim.blendIn, anim.blendOut, anim.duration, anim.flags, anim.time, 0, 0)
            Wait(1000)
            SetVehicleDoorShut(vehicle, 4, false)
            Wait(1000)
        end
        ClearPedTasks(player)
    else
        if GetIsDoorValid(vehicle, 4) and not IsVehicleDoorDamaged(vehicle, 4) and GetVehicleDoorAngleRatio(vehicle, 4) <= 0.05 and not IsVehicleDoorFullyOpen(vehicle, 4) then
            local anim = { dict = 'anim@heists@fleeca_bank@scope_out@return_case', clip = 'trevor_action', rot = vector3(0.0, 0.0, GetEntityHeading(player)), blendIn = 2.0, blendOut = 2.0, duration = 1000, flags = 49, time = 0.25 }
            lib.requestAnimDict(anim.dict)
            TaskPlayAnimAdvanced(player, anim.dict, anim.clip, coords.x, coords.y, coords.z, anim.rot.x, anim.rot.y, anim.rot.z, anim.blendIn, anim.blendOut, anim.duration, anim.flags, anim.time, 0, 0)
            Wait(200)
            SetVehicleDoorOpen(vehicle, 4, false, false)
            Wait(1000)
        end
    end
    usingHoodAnim = false
end

--- Use repair kit with given repairType
--- @param repairType string `"basic"` or `"advanced"`
local function UseRepairKit(repairType)
    local repairKit = Config.RepairKits.Types[repairType]
    if not repairKit then return end

    local function IsAllowedToUseRepairKit(mechanicOnly, whitelistedJobs)
        if type(mechanicOnly) == "boolean" and mechanicOnly == true then
            local isMechanic, shopId = IsPlayerMechanic()
            if isMechanic then
                return true
            end
        else
            return true
        end
    
        if type(whitelistedJobs) == "table" then
            local playerJob = _API.Player:GetJob()
            for _, job in pairs(whitelistedJobs or {}) do
                if playerJob.name == job then
                    return true
                end
            end
        end
    
        return false
    end

    -- check if allowed on advanced
    if repairType == "advanced" and not IsAllowedToUseRepairKit(repairKit.mechanicOnly, repairKit.whitelitedJobs) then
        return _API.ShowNotification(locale("notification.repairkit_advanced_not_allowed"), "inform", {})
    end

    -- Get the closest vehicle
    local vehicle, vehicleDist = lib.getClosestVehicle(coords, 5.0, false)
	if not vehicle or not DoesEntityExist(vehicle) or not IsEntityAVehicle(vehicle) then
        return _API.ShowNotification(locale("notification.no_vehicle_nearby"), "inform", {})
    end

    -- Is near the hood/engine?
    local hood = GetVehicleClosestHood(vehicle, 0.8, false)
    if not hood then
        return _API.ShowNotification(locale("notification.must_be_near_engine_hood"), "inform", {})
    end

    -- Face vehicle
    TaskTurnPedToFaceEntity(player, vehicle, 1000)

    -- Open hood if exists
    VehicleHoodAnimation(vehicle, false)

    -- Play anim:
    local anim = {dict = "mini@repair", clip = "fixing_a_player", blendIn = 2.0, blendOut = 2.0, flag = 1}
    lib.requestAnimDict(anim.dict)
    TaskPlayAnim(player, anim.dict, anim.clip, anim.blendIn, anim.blendOut, -1, anim.flag, 0, 0, 0, 0)

    -- Skillcheck
    local skillcheck = false
    if Config.RepairKits.Skillcheck.enable then
        skillcheck = SkillCheck(Config.RepairKits.Skillcheck.difficulty, Config.RepairKits.Skillcheck.inputs)
    else
        skillcheck = true 
    end
    if not skillcheck then
        ClearPedTasks(player)
        VehicleHoodAnimation(vehicle, true)
        return
    end

    -- Progress bar
    local success = ProgressBar({
        duration = Config.RepairKits.Duration,
        label = locale("progressbar.repair_kit_"..tostring(repairType)),
        useWhileDead = false,
        canCancel = true,
        disable = { move = true, combat = true }
    })
    if not success then
        ClearPedTasks(player)
        VehicleHoodAnimation(vehicle, true)
        return
    end

    -- Remove item
    TriggerServerEvent("t1ger_mechanic:server:removeItem", repairKit.item, 1)

    -- Fix engine
    local engineHealth = math.clamp(repairKit.engineHealth, 250.0, 1000.0)
    SetVehicleEngineHealth(vehicle, engineHealth)

    -- Fix body
    if repairKit.repairBody then
        SetVehicleFixed(vehicle)
        SetVehicleBodyHealth(vehicle, 1000.0)
        SetVehicleDoorOpen(vehicle, 4, false, true)
    end

    -- Repair sounds
    RepairSound()
    
    -- Clear Tasks
    ClearPedTasks(player)
    VehicleHoodAnimation(vehicle, true)

    -- Notification
    _API.ShowNotification(locale("notification.repairkit_"..tostring(repairType).."_success"), "success", {})
end

--- Event for using repair kit item:
RegisterNetEvent("t1ger_mechanic:client:useRepairKit", function(repairType)
    UseRepairKit(repairType)
end)

--- Refreshes all repair stations
function RefreshRepairStations()
    if repairStations and next(repairStations) then
        for id, station in pairs(repairStations) do
            -- remove blip:
            if station.blip and DoesBlipExist(station.blip) then
                RemoveBlip(station.blip)
            end
            -- remove point:
            if station.point then 
                station.point.remove(station.point)
            end
        end
    end
    CreateRepairStations()
end

--- Returns whether player has access to given repair station
--- @param jobs table|nil Required jobs to check for. If table empty or nil it will return true.
--- @return boolean #returns whether the player can access the repair station or not
local function HasAccessToRepairStation(jobs)
    if not jobs or (type(jobs) == "table" and next(jobs) == nil) then return true end

    local playerJob = _API.Player:GetJob()
    if not playerJob or not playerJob.name then return false end

    for _, jobName in pairs(jobs) do 
        if playerJob.name == jobName then
            return true 
        end
    end

    return false
end

--- Creates a blip for the repair station
--- @param pos vector3 The coords to create the blip at
--- @return integer #Returns the blip handle
local function CreateRepairStationBlip(pos)
    local blip = AddBlipForCoord(pos.x, pos.y, pos.z)
    SetBlipSprite(blip, Config.RepairStations.Blip.sprite)
    SetBlipDisplay(blip, 4)
    SetBlipScale(blip, Config.RepairStations.Blip.scale)
    SetBlipColour(blip, Config.RepairStations.Blip.color)
    SetBlipAsShortRange(blip, true)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString(Config.RepairStations.Blip.name)
    EndTextCommandSetBlipName(blip)
    return blip
end

--- Creates repair stations
function CreateRepairStations()
    for id, station in ipairs(Config.RepairStations.Locations) do

        -- check if has access
        if HasAccessToRepairStation(station.jobs) then
            repairStations[id] = {}

            -- create blip
            if type(station.blip) == "boolean" and station.blip == true then
                repairStations[id].blip = CreateRepairStationBlip(station.coords)
            end

            local mk = Config.RepairStations.Marker
            local textUI = Config.RepairStations.TextUi

            -- create ox point:
            repairStations[id].point = lib.points.new({
                coords = station.coords,
                distance = 5.0,
                jobs = station.jobs,
                price = station.price,

                onExit = function(point)
                    local isOpen, text = lib.isTextUIOpen()
                    if isOpen and text == locale("textui.repair_station") then 
                        lib.hideTextUI()
                    end
                end,

                nearby = function(point)
                    if cache.vehicle and cache.seat == -1 then
                        -- draw marker:
                        DrawMarker(mk.type, point.coords.x, point.coords.y, point.coords.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, mk.scale.x, mk.scale.y, mk.scale.z, mk.color.r, mk.color.g, mk.color.b, mk.color.a, false, true, 0, true, false, false, false)
                        
                        -- distance to interact:
                        if point.currentDistance <= 1.5 then
                            -- text ui:
                            local isOpen, currentText = lib.isTextUIOpen()
                            if isOpen and currentText ~= locale("textui.repair_station") then
                                lib.showTextUI(locale("textui.repair_station"), {position = textUI.position, icon = textUI.icon, style = textUI.style})
                            else
                                lib.showTextUI(locale("textui.repair_station"), {position = textUI.position, icon = textUI.icon, style = textUI.style})
                            end
            
                            -- keybind:
                            if IsControlJustReleased(0, Config.RepairStations.Keybind) then
                                lib.hideTextUI()
                                OpenRepairStation(station)
                            end
                        else
                            local isOpen, currentText = lib.isTextUIOpen()
                            if isOpen and currentText == locale("textui.repair_station") then 
                                lib.hideTextUI()
                            end
                        end
                    end
                end
            })
        end
    end
end

--- Repairs specific parts of the vehicle based on the repair type
--- @param vehicle number The vehicle entity to repair
--- @param repairType string The type of repair to perform ("engine", "body", "core_parts", "service_parts")
--- @param repairValue number The repair value to apply (clamped internally if needed)
local function RepairStationFixVehicle(vehicle, repairType, repairValue)
    if repairType == "engine" then
        local engineHealth = math.clamp(repairValue, 400.0, 1000.0)
        SetVehicleEngineHealth(vehicle, engineHealth)

    elseif repairType == "body" then
        SetVehicleFixed(vehicle)
        local bodyHealth = math.clamp(repairValue, 900.0, 1000.0)
        SetVehicleBodyHealth(vehicle, bodyHealth)

    elseif repairType == "core_parts" then
        SetAllCorePartsHealth(vehicle, repairValue)

    elseif repairType == "service_parts" then
        SetAllServicePartsMileage(vehicle, repairValue)
    end
end

--- Handles repair payment, progressbar, and invokes a callback upon successful repair
--- @param vehicle number The vehicle entity being repaired
--- @param price number The repair cost to deduct from the player
--- @param duration number The duration (in ms) for the repair progressbar
--- @param label string The label text to display in the progressbar
--- @param cb function Callback function to execute upon successful repair
local function StationRepairHandle(vehicle, price, duration, label, cb)
    SetVehicleUndriveable(vehicle, true)

    -- check if needs to pay
    if price > 0 then
        local paid = lib.callback.await("t1ger_mechanic:server:repairStationPay", false, price)
        if not paid then
            SetVehicleUndriveable(vehicle, false)
            return _API.ShowNotification(locale("notification.not_enough_bank_money"), "inform", {})
        end
    end

    -- Progress bar
    local success = ProgressBar({
        duration = duration,
        label = label,
        useWhileDead = false,
        canCancel = true,
        disable = { move = true, combat = true }
    })

    if success and cb then
        cb()
    end

    -- Always re-enable vehicle AFTER repair is done (or cancelled)
    SetVehicleUndriveable(vehicle, false)
end

--- Opens the main repair station menu
--- @param currentStation table Current station data, parsed when open menu from marker
function OpenRepairStation(currentStation)
    local skipMechanicOnlineCheck = false

    if not cache.vehicle or cache.seat ~= -1 then
        return
    end

    local vehicle = cache.vehicle
    if not DoesEntityExist(vehicle) then return end

    -- If station is whitelisted (has jobs) and RestrictOnWhitelistedStations is false, skip mechanic check
    if (type(currentStation.jobs) == "table" and next(currentStation.jobs)) and not Config.RepairStations.RestrictOnWhitelistedStations then
        skipMechanicOnlineCheck = true
    end

    -- if check mechanic online
    if not skipMechanicOnlineCheck then
        local mechanicsOnline = lib.callback.await("t1ger_mechanic:server:getOnlineMechanicCount", false)
        
        if mechanicsOnline >= Config.RepairStations.AllowIfMechanicCountBelow then
            return _API.ShowNotification(string.format(locale("notification.repair_station_closed"), mechanicsOnline), "error", {})
        end
    end

    local menuOptions = {}

    if Config.RepairStations.Mode == "full" then
        menuOptions[#menuOptions+1] = {
            title = locale("menu_title.repair_station_type_full").." - "..Config.Currency..tostring(Config.RepairStations.FullRepairPrice), 
            description = string.format(locale("menu_description.repair_station_type"), Config.RepairStations.FullRepairDuration/1000),
            icon = Config.RepairStations.FullRepairIcon,
            onSelect = function()
                local price = Config.RepairStations.FullRepairPrice
                if type(currentStation.free) == "boolean" and currentStation.free == true then
                    price = 0
                end
                StationRepairHandle(vehicle, price, Config.RepairStations.FullRepairDuration, locale("progressbar.repair_station_type_full"), function()
                    for repairType, repair in pairs(Config.RepairStations.Repairs) do
                        if repair.enable then
                            RepairStationFixVehicle(vehicle, repairType, repair.value)
                        end
                    end
                    RepairSound()
                end)
            end,
        }
    else
        for repairType, repair in pairs(Config.RepairStations.Repairs) do
            if repair.enable then
                menuOptions[#menuOptions+1] = {
                    title = locale("menu_title.repair_station_type_"..repairType).." - "..Config.Currency..tostring(math.groupdigits(repair.price)),
                    description = string.format(locale("menu_description.repair_station_type"), repair.duration/1000),
                    icon = repair.icon,
                    onSelect = function()
                        local price = repair.price
                        if type(currentStation.free) == "boolean" and currentStation.free == true then
                            price = 0
                        end
                        StationRepairHandle(vehicle, price, repair.duration, locale("progressbar.repair_station_type_"..repairType), function()
                            RepairStationFixVehicle(vehicle, repairType, repair.value)
                            RepairSound()
                        end)
                    end,
                }
            end
        end
    end

    -- sort options by title:
    table.sort(menuOptions, function(a, b)
        return a.title < b.title
    end)

    lib.registerContext({
        id = "repair_station_main",
        title = locale("menu_title.repair_station_main"),
        options = menuOptions,
    })

    lib.showContext("repair_station_main")
end