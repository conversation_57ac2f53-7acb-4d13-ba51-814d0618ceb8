local vehicle_data = {}
local tracked_vehicles = {}

--- Validates service parts parsed from a vehicle
--- @param serviceParts table the service parts data from the vehicle 
--- @return table #Returns validated service parts
local function ValidateServiceParts(serviceParts)
    -- check for missing or invalid parts
    for part, mileage in pairs(serviceParts) do
        if not Config.ServiceParts[part] then
            serviceParts[part] = nil  -- Remove invalid part
        end
    end
    -- add any missing service parts from Config
    for part, data in pairs(Config.ServiceParts) do
        if not serviceParts[part] then
            serviceParts[part] = 0  -- Set to 0 because it's a fresh new part
        end
    end
    return serviceParts
end

--- Validates core parts parsed from a vehicle
--- @param coreParts table the core parts data from the vehicle 
--- @return table #Returns validated core parts
local function ValidateCoreParts(coreParts)
    -- check for missing or invalid parts
    for part, mileage in pairs(coreParts) do
        if not Config.CoreParts[part] then
            coreParts[part] = nil  -- Remove invalid part
        end
    end
    -- add any missing service parts from Config
    for part, data in pairs(Config.CoreParts) do
        if not coreParts[part] then
            coreParts[part] = 100  -- Set to default health
        end
    end
    return coreParts
end

--- Returns default vehicle data
--- @param randomize boolean? (optional) Whether to use randomized values
--- @return table #table(mileage, service_parts, core_parts)
local function GetDefaultVehicleData(randomize)
    -- Random total mileage (in km), if enabled
    local mileage = 0
    if randomize then
        local minM, maxM = Config.RandomVehicleDefaults.MileageRange[1], Config.RandomVehicleDefaults.MileageRange[2]
        mileage = math.random(minM, maxM)
    end

    local function GetMinimumServiceMultiplier()
        local min = 1.0
        for _, v in pairs(Config.ServiceIntervalMultipliers) do
            if type(v) == "number" and v < min then
                min = v
            end
        end
        return math.max(min, 0.1) -- enforce a sensible lower limit
    end

    -- Initialize service parts with driven mileage
    local service_parts = {}
    local lowestMultiplier = GetMinimumServiceMultiplier()
    for part, cfg in pairs(Config.ServiceParts) do
        if randomize and cfg.interval then
            local interval = math.floor(cfg.interval * lowestMultiplier)
            local range = Config.RandomVehicleDefaults.ServicePartRange
            local randomPercent = math.random() * (range[2] - range[1]) + range[1]
            service_parts[part] = math.floor(interval * randomPercent)
        else
            service_parts[part] = 0
        end
    end

    -- Initialize core parts with health
    local core_parts = {}
    for part, _ in pairs(Config.CoreParts) do
        if randomize then
            local minH, maxH = Config.RandomVehicleDefaults.CorePartHealthRange[1], Config.RandomVehicleDefaults.CorePartHealthRange[2]
            -- Clamp between 0 and 100 even if config is wrong
            minH = math.max(0, math.min(100, minH))
            maxH = math.max(0, math.min(100, maxH))

            core_parts[part] = math.random(minH, maxH)
        else
            core_parts[part] = 100
        end
    end

    -- Validate configuration (optional)
    if next(service_parts) == nil or next(core_parts) == nil then
        error("Service parts or core parts configuration is invalid.")
    end

    return {
        mileage = mileage,
        service_parts = service_parts,
        core_parts = core_parts
    }
end

--- Initializes default data for a vehicle with the given plate
--- @param plate string The vehicle number plate text
local function RegisterVehicleData(plate)
    -- Ensure the plate is valid
    if type(plate) ~= "string" or not plate:match("%S") then
        error("[RegisterVehicleData] Invalid plate type. Must be a non-empty string")
    end

    -- trim plate
    local trimmedPlate = exports["t1ger_mechanic"]:TrimPlate(plate)

    -- get default data
    local defaultData = GetDefaultVehicleData(false)

    -- Insert the vehicle's initial data into the database
    local affectedRows = MySQL.insert.await("INSERT INTO t1ger_vehicledata (plate, mileage, service_parts, core_parts) VALUES (?, ?, ?, ?)", {
        trimmedPlate,                           -- Vehicle plate
        defaultData.mileage,                    -- Initial mileage set to 0
        json.encode(defaultData.service_parts), -- Default service parts JSON
        json.encode(defaultData.core_parts)     -- Default core parts JSON
    })

    -- Check the result of the insertion
    if not affectedRows then
        error(("[RegisterVehicleData] Failed to insert data for plate: %s"):format(trimmedPlate))
    end

    -- sync to server-table:
    vehicle_data[trimmedPlate] = defaultData
    tracked_vehicles[trimmedPlate] = true

    -- Log successful registration
    if Config.Debug then
        print(("Successfully registered vehicle data for plate: %s"):format(trimmedPlate))
    end
end
exports("RegisterVehicleData", RegisterVehicleData)

--- Adds a service history entry to the database.
--- @param plate string Vehicle plate (required)
--- @param part string Label of the part that was serviced (required)
--- @param mileage number Vehicle mileage at time of service (required)
--- @param mechanic string Mechanic's identifier (required)
--- @param shop string Shop or job label (required)
--- @return boolean success True if successfully inserted
local function AddServiceHistory(plate, part, mileage, mechanic, shop)
    -- Validate parameters
    if type(plate) ~= "string" or not plate:match("%S") then
        print("[AddServiceHistory] Invalid plate")
        return false
    end

    if type(part) ~= "string" or part == "" then
        print("[AddServiceHistory] Invalid part label")
        return false
    end

    if type(mileage) ~= "number" then
        print("[AddServiceHistory] Invalid mileage")
        return false
    end

    if type(mechanic) ~= "string" or mechanic == "" then
        print("[AddServiceHistory] Invalid mechanic identifier")
        return false
    end

    if type(shop) ~= "string" or shop == "" then
        print("[AddServiceHistory] Invalid shop label")
        return false
    end

    -- Confirm vehicle is owned
    if not _API.IsVehicleOwned(plate) then
        if Config.Debug then
            print("[AddServiceHistory] Plate not owned: " .. plate)
        end
        return false
    end

    -- Get trimmed plate:
    local trimmedPlate = exports["t1ger_mechanic"]:TrimPlate(plate)

    -- Date:
    local date = os.date("%Y-%m-%d")

    -- Insert service entry
    local inserted = MySQL.insert.await("INSERT INTO t1ger_servicehistory (plate, part, mileage, mechanic, shop, date) VALUES (?, ?, ?, ?, ?, ?)", {
        trimmedPlate,
        part,
        mileage,
        mechanic,
        shop,
        date
    })

    if not inserted then
        print(("[AddServiceHistory] Failed to insert service history for plate: %s"):format(trimmedPlate))
        return false
    end

    return true
end
exports("AddServiceHistory", AddServiceHistory)

--- Retrieves the service history for a specific vehicle by plate.
--- @param plate string Vehicle plate (required)
--- @return table|nil history A table of service history entries or nil if none found
local function GetVehicleServiceHistory(plate)
    -- Validate plate
    if type(plate) ~= "string" or not plate:match("%S") then
        print("[GetVehicleServiceHistory] Invalid plate type. Must be a non-empty string.")
        return nil
    end

    -- Confirm vehicle is owned
    if not _API.IsVehicleOwned(plate) then
        if Config.Debug then
            print("[GetVehicleServiceHistory] Plate not owned: " .. plate)
        end
        return nil
    end

    -- Get trimmed plate
    local trimmedPlate = exports["t1ger_mechanic"]:TrimPlate(plate)

    -- Query service history, most recent first
    local result = MySQL.query.await("SELECT id, part, mileage, mechanic, shop, date FROM t1ger_servicehistory WHERE plate = ? ORDER BY id DESC", { trimmedPlate })

    if not result or #result == 0 then
        if Config.Debug then
            print("[GetVehicleServiceHistory] No history found for plate: " .. trimmedPlate)
        end
        return nil
    end

    -- Check if `date` is not a string (e.g., still stored as DATE or TIMESTAMP)
    if type(result[1].date) ~= "string" then
        print("[GetVehicleServiceHistory] Detected non-string date column. Attempting to alter column to VARCHAR(10)...")

        -- Attempt to alter the column
        local success = pcall(function()
            MySQL.query.await("ALTER TABLE t1ger_servicehistory MODIFY COLUMN date VARCHAR(10) NOT NULL")
        end)

        if success then
            print("[GetVehicleServiceHistory] Successfully altered `date` column to VARCHAR(10).")
            result = MySQL.query.await("SELECT id, part, mileage, mechanic, shop, date FROM t1ger_servicehistory WHERE plate = ? ORDER BY id DESC", { trimmedPlate })
            if not result or #result == 0 then
                if Config.Debug then
                    print("[GetVehicleServiceHistory] No history found for plate: " .. trimmedPlate)
                end
                return nil
            end
        else
            error("[GetVehicleServiceHistory] Failed to alter `date` column in t1ger_servicehistory table. Please do it manually via your database.")
        end
    end

    return result
end
exports("GetVehicleServiceHistory", GetVehicleServiceHistory)

--- Callback to load vehicle data from vehicle netId
lib.callback.register("t1ger_mechanic:server:getVehicleServiceHistory", function(source, plate)
    return GetVehicleServiceHistory(plate)
end)

--- Initialize vehicle data for all owned vehicles on resource start:
AddEventHandler("onResourceStart", function(resourceName)
    if resourceName == GetCurrentResourceName() then
        -- Fetch all existing vehicle data from t1ger_vehicledata
        local results = MySQL.query.await("SELECT * FROM t1ger_vehicledata")
        if type(results) == "table" and next(results) ~= nil then
            for i = 1, #results do
                local plate = tostring(results[i].plate)

                -- validate plate
                if type(plate) == "string" and plate:match("%S") then
                    -- get trimmed plate:
                    local trimmedPlate = exports["t1ger_mechanic"]:TrimPlate(plate)
    
                    -- validate service_parts & core_parts
                    local service_parts = ValidateServiceParts(json.decode(results[i].service_parts))
                    local core_parts = ValidateCoreParts(json.decode(results[i].core_parts))
    
                    -- cache:
                    vehicle_data[trimmedPlate] = {
                        mileage = results[i].mileage,
                        service_parts = service_parts,
                        core_parts = core_parts
                    }
                end
            end
        end
    
        -- Fetch all owned vehicles from database:
        local owned_vehicles = _API.GetAllOwnedVehicles()
        if owned_vehicles and next(owned_vehicles) then
            for i = 1, #owned_vehicles do
                local plate = tostring(owned_vehicles[i].plate)
                
                -- validate plate
                if type(plate) == "string" and plate:match("%S") then
                    -- get trimmed plate:
                    local trimmedPlate = exports["t1ger_mechanic"]:TrimPlate(plate)

                    -- If the vehicle is missing, initialize it in the database
                    if not vehicle_data[trimmedPlate] then
                        local success, err = pcall(RegisterVehicleData, trimmedPlate)
                        if not success then
                            print(("Error initializing vehicle data for plate %s: %s"):format(trimmedPlate, err))
                        end
                    end
                end
            end
        end
    
        -- set statebag on vehicles that are spawned already:
        for _,vehicle in ipairs(GetAllVehicles()) do
            local plate = GetVehicleNumberPlateText(vehicle)
            
            -- validate plate
            if type(plate) == "string" and plate:match("%S") then
                -- get trimmed plate:
                local trimmedPlate = exports["t1ger_mechanic"]:TrimPlate(plate)

                local ent = Entity(vehicle).state
                
                -- if vehicle data exists, initate statebag:
                if vehicle_data[trimmedPlate] then
                    ent:set("t1ger_mechanic:vehicleData", vehicle_data[trimmedPlate], true)
                    tracked_vehicles[trimmedPlate] = true
                end

                if ent["t1ger_mechanic:malfunctionActive"] then
                    ent:set("t1ger_mechanic:malfunctionActive", false, true)
                end
            end
        end
	end
end)

--- Callback to load vehicle data from vehicle netId
lib.callback.register("t1ger_mechanic:server:loadVehicleData", function(source, netId)
    local vehicle = NetworkGetEntityFromNetworkId(netId)
    if not DoesEntityExist(vehicle) then return false end

    -- get vehicle state:
    local vehicleState = Entity(vehicle).state

    -- Fetch plate with dynamic waiting
    local plate = GetVehicleNumberPlateText(vehicle)
    local attempts = 20  -- 20 attempts * 100ms = max 2 seconds total
    local initialPlate = plate

    -- Phase 1: Wait for non-empty plate
    while (type(plate) ~= "string" or not plate:match("%S")) and attempts > 0 do
        Wait(100)
        plate = GetVehicleNumberPlateText(vehicle)
        attempts -= 1
    end

    -- Phase 2: Wait for updated plate if same placeholder plate detected
    if plate == initialPlate and attempts > 0 then
        while attempts > 0 do
            Wait(100)
            local newPlate = GetVehicleNumberPlateText(vehicle)

            if type(newPlate) == "string" and newPlate:match("%S") and newPlate ~= initialPlate then
                plate = newPlate
                break
            end

            attempts -= 1
        end
    end

    -- Now we can safely trim the plate
    local trimmedPlate = exports["t1ger_mechanic"]:TrimPlate(plate)

    -- Get saved or default data
    local data = vehicle_data[trimmedPlate] or GetDefaultVehicleData(true)

    -- Track if owned vehicle
    if vehicle_data[trimmedPlate] then
        tracked_vehicles[trimmedPlate] = true
    end

    if Config.Debug then
        print(("Vehicle data loaded by %s for plate: %s"):format(GetPlayerName(source), trimmedPlate))
    end

    return data
end)

--- Saves vehicledata for a given vehicle entity in t1ger_vehicledata table in database
--- @param vehicle number The vehicle entity handle
--- @return boolean #Success
local function SaveVehicleData(vehicle)
    local plate = GetVehicleNumberPlateText(vehicle)
    if type(plate) == "string" and plate:match("%S") then
        local trimmedPlate = exports["t1ger_mechanic"]:TrimPlate(plate)
        if tracked_vehicles[trimmedPlate] then
            
            -- get vehicle entity statebag
            local ent = Entity(vehicle).state

            -- get vehicle data
            local vehicleData = ent["t1ger_mechanic:vehicleData"]
            if not vehicleData then return false end

            -- sync vehicle_data table with statebag values
            vehicle_data[trimmedPlate] = vehicleData
        
            -- insert to database
            MySQL.prepare.await("INSERT INTO t1ger_vehicledata (plate, mileage, service_parts, core_parts) VALUES (?, ?, ?, ?) ON DUPLICATE KEY UPDATE mileage = ?, service_parts = ?, core_parts = ?", {
                trimmedPlate, vehicleData.mileage, json.encode(vehicleData.service_parts), json.encode(vehicleData.core_parts), vehicleData.mileage, json.encode(vehicleData.service_parts), json.encode(vehicleData.core_parts)
            })

            -- removed from tracked vehicles table
            tracked_vehicles[trimmedPlate] = nil
        
            -- print:
            if Config.Debug then
                print("SAVED VEHICLE DATA FOR PLATE ["..trimmedPlate.."]")
            end

            return true
        end
    end
    return false
end
exports("SaveVehicleData", SaveVehicleData)

--- Save vehicle data on vehicle entity removal
AddEventHandler("entityRemoved", function(entity)
    local vehicle = entity
    if vehicle and vehicle ~= 0 and GetEntityType(vehicle) == 2 then
        local success = SaveVehicleData(vehicle)
    end
end)

--- Updates plate in vehicle data table to a new plate. To be used with plate changer scripts etc.
--- @param currentPlate string The current plate registered in the database
--- @param newPlate string The new plate to replace the current
--- @return boolean #Success
local function UpdateVehicleDataPlate(currentPlate, newPlate)
    if type(currentPlate) ~= "string" or currentPlate == "" then
        return error("[UpdateVehicleDataPlate] Invalid type for currentPlate. Must be a non-empty string")
    end

    if type(newPlate) ~= "string" or newPlate == "" then
        return error("[UpdateVehicleDataPlate] Invalid type for newPlate. Must be a non-empty string")
    end

    local newTrimmedPlate = exports["t1ger_mechanic"]:TrimPlate(newPlate)
    local currentTrimmedPlate = exports["t1ger_mechanic"]:TrimPlate(currentPlate)

    -- Check if the new plate already exists in the database
    local exists = MySQL.scalar.await("SELECT 1 FROM t1ger_vehicledata WHERE plate = ? or plate = ?", {newPlate, newTrimmedPlate})
    if exists then
        print(("[UpdateVehicleDataPlate] New plate '%s' already exists. Cannot update."):format(newTrimmedPlate))
        return false
    end

    -- update in database
    local affectedRows = MySQL.update.await("UPDATE t1ger_vehicledata SET `plate` = ? WHERE plate = ? OR plate = ?", {
        newTrimmedPlate,
        currentPlate,
        currentTrimmedPlate
    })

    return affectedRows > 0
end
exports("UpdateVehicleDataPlate", UpdateVehicleDataPlate)

--- Saves all vehicles' vehicle data from their statebag
local function SaveAllVehicleData()
    for _,vehicle in ipairs(GetAllVehicles()) do

        -- get trimmed plate
        local plate = GetVehicleNumberPlateText(vehicle)
        if type(plate) ~= "string" or not plate:match("%S") then return end
        local trimmedPlate = exports["t1ger_mechanic"]:TrimPlate(plate)

        -- is vehicle owned?
        if not vehicle_data[trimmedPlate] then return end

        -- get vehicle data:
        local vehState = Entity(vehicleEntity).state
        if not vehState then return end

        local vehicleData = vehState["t1ger_mechanic:vehicleData"]
        if not vehicleData or type(vehicleData) ~= "table" then return end

        -- update cache table:
        vehicle_data[trimmedPlate] = vehicleData

        -- insert into database
        MySQL.prepare("INSERT INTO t1ger_vehicledata (plate, mileage, service_parts, core_parts) VALUES (?, ?, ?, ?) ON DUPLICATE KEY UPDATE mileage = ?, service_parts = ?, core_parts = ?", {
            trimmedPlate, vehicleData.mileage, json.encode(vehicleData.service_parts), json.encode(vehicleData.core_parts), vehicleData.mileage, json.encode(vehicleData.service_parts), json.encode(vehicleData.core_parts)
        })
    end
end

--- Save vehicle data on server Shutting Down:
AddEventHandler("txAdmin:events:serverShuttingDown", function()
	SaveAllVehicleData()
end)

--- Save vehicle data on scheduled Restart:
AddEventHandler("txAdmin:events:scheduledRestart", function(eventData)
    if eventData.secondsRemaining ~= 60 then return end
	SaveAllVehicleData()
end)

--- Save vehicle data on resource stop:
AddEventHandler("onResourceStop", function(resourceName)
	if resourceName == GetCurrentResourceName()  then
		SaveAllVehicleData()
	end
end)