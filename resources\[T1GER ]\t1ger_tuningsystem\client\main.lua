player, coords = nil, {}
Citizen.CreateThread(function()
    while true do
        player = PlayerPedId()
        coords = GetEntityCoords(player)
        Wait(500)
    end
end)

RegisterNetEvent(Cfg.Triggers[Framework].job)
AddEventHandler(Cfg.Triggers[Framework].job, function(job)
    OnJobUpdate(job)
end)

RegisterNetEvent('tuningsystem:client:isAdmin')
AddEventHandler('tuningsystem:client:isAdmin', function(isAdmin)
    if Config.Debug then
        print("isAdmin:", isAdmin)
    end
    isPlayerAdmin = isAdmin
end)

RegisterNetEvent('tuningsystem:client:setPlayerTunerId')
AddEventHandler('tuningsystem:client:setPlayerTunerId', function(id, isBoss)
    if Config.Debug then
        print("playerTunerId: ", id)
    end
    playerTunerId = id
    isTunerBoss = (isBoss ~= nil and isBoss ~= 0) and isBoss
end)

RegisterNetEvent('tuningsystem:client:updateShops')
AddEventHandler('tuningsystem:client:updateShops', function(data)
    Config.Shops = data
end)

RegisterNetEvent('tuningsystem:client:updateShopConfig')
AddEventHandler('tuningsystem:client:updateShopConfig', function(id, data)
    if Config.Shops == nil or next(Config.Shops) == nil then
        local data = lib.callback.await('tuningsystem:server:getShopsConfig', false)
        Config.Shops = data
    end
    Config.Shops[id] = data
end)

GetFormattedPrice = function(price)
    return Config.Currency..Lib.CommaValue(Lib.RoundNumber(price))
end

SkillCheck = function(difficulty, inputs)
    local success = lib.skillCheck(difficulty, inputs) 
    return success
end

ProgressBar = function(options)
    local success = lib.progressBar(options)
    return success
end

OpenDutyMenu = function(point)
    local onDuty, stateLabel = false, Lang['title_no']

    if Core.GetJob().name == Config.Shops[point.shopId].job.name then 
        onDuty = true
        stateLabel = Lang['title_yes']
    end

    lib.registerContext({
        id = 'toggle_duty_menu',
        title = Lang['title_toggle_duty_menu'],
        onExit = function()
            usingMenu = false
            point.textUi = false
        end,
        options = {
            {
                title = Lang['title_on_duty_state']:format(stateLabel),
                icon = 'spinner',
                disabled = true
            },
            {
                title = Lang['title_clock_in_out'],
                icon = 'toggle-on',
                onSelect = function()
                    TriggerServerEvent('tuningsystem:server:toggleDuty', onDuty, point.shopId)
                    Wait(100)
                    OpenDutyMenu(point)
                end,
            },
        },
    })
    lib.showContext('toggle_duty_menu')
end

OpenGarageMenu = function(point)
    if Config.UseBuiltInGarage == true then
        lib.registerContext({
            id = 'garage_menu',
            title = Lang['title_garage_menu'],
            onExit = function()
                usingMenu = false
                point.textUi = false
            end,
            options = {
                {
                    title = Lang['title_garage_get_veh'],
                    icon = 'car',
                    args = {shopId = point.shopId, class = point.class, garageId = point.markerId, point = point},
                    event = 'tuningsystem:client:getPlayerVehicles'
                },
                {
                    title = Lang['title_garage_store_veh'],
                    icon = 'square-parking',
                    onSelect = function()
                        local vehicle = GetVehiclePedIsIn(player, false)
                        if vehicle ~= 0 then 
                            Lib.GetControlOfEntity(vehicle)
                            local props = Core.GetVehicleProperties(vehicle)
                            Core.DeleteVehicle(vehicle)
                            TriggerServerEvent('t1ger_lib:server:updateOwnedVehicle', true, point.markerId, props)
                        end
                        usingMenu = false
                        point.textUi = false
                    end,
                },
            },
        })
        lib.showContext('garage_menu')
    else
        -- add function for your garage here:
    end
end

RegisterNetEvent('tuningsystem:client:getPlayerVehicles', function(args)
    local menuOptions = {}
    Core.TriggerCallback('tuningsystem:server:getPlayerVehicles', function(results)
        if results and next(results) then
            for k, vehicle in pairs(results) do
                -- plate, props, type, job, stored, garage, impound, fuel, engine, body, model
                if vehicle.garage == nil or args.garageId == vehicle.garage then
                    if vehicle.stored  == nil or (vehicle.stored == true or vehicle.stored == 1) then 
                        local props = json.decode(vehicle.props)
                        local name = GetLabelText(GetDisplayNameFromVehicleModel(props.model))
                        local make = GetLabelText(GetMakeNameFromVehicleModel(props.model))
                        local metaOptions = {
                            {label = Lang['meta_veh_make'], value = make},
                            {label = Lang['meta_veh_model'], value = name},
                            {label = Lang['meta_veh_plate'], value = vehicle.plate},
                        }
                        if props.engineHealth ~= nil or vehicle.engine ~= nil then
                            table.insert(metaOptions, {label = Lang['meta_option_engine'], value = (props.engineHealth/10)..'%' or (props.engine/10)..'%'})
                        end
                        if props.bodyHealth ~= nil or vehicle.body ~= nil then
                            table.insert(metaOptions, {label = Lang['meta_option_body'], value = (props.bodyHealth/10)..'%' or (props.body/10)..'%'})
                        end
                        if props.fuelLevel ~= nil or vehicle.fuel ~= nil then
                            table.insert(metaOptions, {label = Lang['meta_option_fuel'], value = props.fuelLevel..'%' or vehicle.fuel..'%'})
                        end
                        table.insert(menuOptions, {
                            title = make..' '..name..' ['..vehicle.plate..']',
                            icon = 'car',
                            metadata = metaOptions,
                            onSelect = function()
                                Core.SpawnVehicle(props.model, coords, GetEntityHeading(player), function(spawnedVehicle)
                                    Lib.GetControlOfEntity(spawnedVehicle)
                                    Core.SetVehicleProperties(spawnedVehicle, props)
                                    SetPedIntoVehicle(player, spawnedVehicle, -1)
                                    TriggerServerEvent('t1ger_lib:server:updateOwnedVehicle', false, args.garageId, props)
                                end, true)
                                usingMenu = false
                                args.point.textUi = false
                            end,
                        })
                    end
                end
            end
            if #menuOptions <= 0 then
                usingMenu = false
                args.point.textUi = false
                return Core.Notification({
                    title = '',
                    message = Lang['no_owned_vehicles_in_this_garage'],
                    type = 'inform'
                })
            end
            lib.registerContext({
                id = 'get_vehicles',
                title = Lang['title_garage_vehicles'],
                onExit = function()
                    usingMenu = false
                    args.point.textUi = false
                end,
                menu = 'garage_menu',
                options = menuOptions,
            })
            lib.showContext('get_vehicles')
        else
            Core.Notification({
                title = '',
                message = Lang['no_owned_vehicles'],
                type = 'inform'
            })
            usingMenu = false
            args.point.textUi = false
        end
    end)
end)

OpenStorage = function(point)
    if Cfg.Inventory == 'default' and Framework == 'ESX' then
        lib.registerContext({
            id = 'storage_menu',
            title = Lang['title_storage'],
            onExit = function()
                usingMenu = false
                point.textUi = false
            end,
            options = {
                {
                    title = Lang['title_withdraw_storage'],
                    description = Lang['desc_withdraw_storage'],
                    icon = 'minus',
                    arrow = true,
                    onSelect = function()
                        if Config.Shops[point.shopId].storage[point.markerId] and next(Config.Shops[point.shopId].storage[point.markerId]) then
                            local curStorage = Config.Shops[point.shopId].storage[point.markerId]
                            local menuOptions = StorageWithdrawOptions(point, curStorage)
                            lib.registerContext({
                                id = 'storage_withdraw',
                                title = Lang['title_withdraw_storage'],
                                onExit = function()
                                    usingMenu = false
                                    point.textUi = false
                                end,
                                menu = 'storage_menu',
                                options = menuOptions,
                            })
                            lib.showContext('storage_withdraw')
                        else
                            Core.Notification({
                                title = '',
                                message = Lang['this_storage_empty'],
                                type = 'inform'
                            })
                            return OpenStorage(point)
                        end
                    end,
                },
                {
                    title = Lang['title_deposit_storage'],
                    description = Lang['desc_deposit_storage'],
                    icon = 'plus',
                    arrow = true,
                    onSelect = function()
                        Core.TriggerCallback('t1ger_lib:getPlayerInventory', function(results)
                            if next(results) ~= nil then
                                local menuOptions = StorageDepositOptions(point, results)
                                
                                lib.registerContext({
                                    id = 'storage_deposit',
                                    title = Lang['title_deposit_storage'],
                                    onExit = function()
                                        usingMenu = false
                                        point.textUi = false
                                    end,
                                    menu = 'storage_menu',
                                    options = menuOptions,
                                })
                                lib.showContext('storage_deposit')
                            else
                                Core.Notification({
                                    title = '',
                                    message = Lang['your_inventory_empty'],
                                    type = 'inform'
                                })
                                return OpenStorage(point)
                            end
                        end)
                    end,
                },
            },
        })
        lib.showContext('storage_menu')
    else
        local storage = Config.Shops[point.shopId].markers[point.class][point.markerId].stash
        Core.OpenStash(storage.id, storage.label, storage.slots, storage.weight, nil)
        usingMenu = false
        point.textUi = false
    end
end

OpenWorkbenchMenu = function(point)
    if Config.UseBuiltInCrafting == true then
        local categories = {}
        for category, cat in ipairs(Config.Workbench.Categories) do
            table.insert(categories, {
                title = cat.label,
                description = cat.description,
                icon = cat.icon,
                onSelect = function()
                    local recipes = GetCraftingRecipe(point, cat)
                    lib.registerContext({
                        id = 'workbench_menu_recipes',
                        title = cat.label,
                        menu = 'workbench_menu_categories',
                        onExit = function()
                            usingMenu = false
                            point.textUi = false
                        end,
                        options = recipes,
                    })
                    lib.showContext('workbench_menu_recipes')
                end
            })
        end
        lib.registerContext({
            id = 'workbench_menu_categories',
            title = Lang['title_workbench'],
            onExit = function()
                usingMenu = false
                point.textUi = false
            end,
            options = categories,
        })
        lib.showContext('workbench_menu_categories')
    else
        -- add function/export to open your own crafting
    end
end

OpenLaptopMenu = function(point)
    local categories = {}
    for k,v in ipairs(Config.LaptopItems) do
        if v.items ~= nil and type(v.items) == 'table' and next(v.items) then
            local items = {}
            for _,itemName in pairs(v.items) do
                local item = GetItemInfo(itemName)
                if item then
                    items[#items + 1] = {
                        title = item.label..' - '..GetFormattedPrice(item.price),
                        icon = item.icon,
                        onSelect = function()
                            LaptopOrder(point, k, item)
                        end
                    }
                end
            end
            lib.registerContext({
                id = 'laptop_menu_'..tostring(k),
                title = v.category,
                menu = 'laptop_menu_main',
                onExit = function()
                    usingMenu = false
                    point.textUi = false
                end,
                options = items,
            })

            if #items > 0 then 
                categories[#categories + 1] = {
                    title = v.category,
                    icon = v.icon,
                    arrow = true,
                    menu = 'laptop_menu_'..tostring(k)
                }
            end
        end
    end

    if categories == nil or next(categories) == nil then 
        return Core.Notification({
            title = '',
            message = Lang['no_laptop_shop_items'],
            type = 'error'
        })
    end

    lib.registerContext({
        id = 'laptop_menu_main',
        title = Lang['title_laptop'],
        onExit = function()
            usingMenu = false
            point.textUi = false
        end,
        options = categories,
    })
    lib.showContext('laptop_menu_main')
end

RegisterNetEvent('tuningsystem:client:deleteEntity')
AddEventHandler('tuningsystem:client:deleteEntity', function(netId)
    local entity = NetworkGetEntityFromNetworkId(netId)
    if DoesEntityExist(entity) then
        SetEntityAsMissionEntity(entity, true, true)
        DeleteEntity(entity) 
    end
end)

RegisterNetEvent('tuningsystem:client:useRepairKit', function(itemId, itemData)
    local isTunerJob, shopId = IsTunerJob(Core.GetJob().name)

    if not isTunerJob then
        return Core.Notification({
            title = '',
            message = Lang['need_tuner_job'],
            type = 'inform'
        }) 
    end

    local vehicle, closestDist = Lib.GetClosestVehicle(coords, 5.0, false)
    if vehicle == nil or not DoesEntityExist(vehicle) then 
        return Core.Notification({
            title = '',
            message = Lang['no_vehicle_nearby'],
            type = 'inform'
        })
    end
    TaskTurnPedToFaceEntity(player, vehicle, 1000)
    Wait(1000)
    TaskStartScenarioInPlace(player, 'WORLD_HUMAN_WELDING', 0, true)
    Wait(3000)
    ClearPedTasks(player)
    SetVehicleEngineHealth(vehicle, 1000.0)
    SetVehicleBodyHealth(vehicle, 1000.0)
    SetVehicleFixed(vehicle)
    RepairSound()
end)


RegisterNetEvent('tuningsystem:client:tunerTablet', function(itemId, itemData)
    local isTunerJob, shopId = IsTunerJob(Core.GetJob().name)

    if not isTunerJob then
        return Core.Notification({
            title = '',
            message = Lang['need_tuner_job'],
            type = 'inform'
        }) 
    end

    if Config.Debug then
        print("playTunerId: "..playerTunerId.." | job name: "..Core.GetJob().name.." | shop id: "..shopId)
    end

    local tabletOptions = {}

    local vehicle = GetVehiclePedIsIn(player, false)
    if vehicle ~= nil and vehicle > 0 then
        tabletOptions = {
            {
                title = Lang['title_tablet_vehicle_info'],
                icon = 'circle-info',
                readOnly = true,
                metadata = {
                    {label = Lang['meta_option_engine'], value = Lib.RoundNumber(GetVehicleEngineHealth(vehicle)/10, 2)..'%'},
                    {label = Lang['meta_option_body'], value = Lib.RoundNumber(GetVehicleBodyHealth(vehicle)/10, 2)..'%'},
                    {label = Lang['meta_option_fuel'], value = Lib.RoundNumber(Core.GetVehicleFuelLevel(vehicle), 2)..'%'},
                    {label = Lang['meta_veh_plate'], value = GetVehicleNumberPlateText(vehicle)..''},
                },
            },
            {
                title = Lang['title_tablet_examine_performance'],
                icon = 'magnifying-glass',
                event = 'tuningsystem:client:examinePerformanceParts',
                description = Lang['desc_tablet_examine_performance'],
                arrow = true,
                args = {vehicle = vehicle, itemId = itemId, itemData = itemData}
            },
            {
                title = Lang['title_tablet_dyno_tuning'],
                icon = 'chart-line',
                event = 'tuningsystem:client:dynoTuningMainMenu',
                description = 'Ready from next update, stay tuned!',
                arrow = true,
                args = {vehicle = vehicle}
            },
        }
    else
        tabletOptions = {
            {title = Lang['title_billing'], icon = 'file-invoice-dollar', arrow = true, event = 'tuningsystem:client:billing'}, -- billing:
        }

        if Config.SalvageJob.Enable == true or Config.MobileTuningJob.Enable == true then 
            tabletOptions[#tabletOptions + 1] = {title = Lang['title_npc_jobs'], icon = 'comment-dots', arrow = true, event = 'tuningsystem:client:jobs_menu'} -- npc jobs:
        end
    end

    tabletOptions[#tabletOptions + 1] = {
        title = Lang['title_tablet_mod_orders'],
        icon = 'cart-shopping',
        description = Lang['desc_tablet_mod_orders'],
        event = 'tuningsystem:client:modOrders',
        arrow = true,
        args = {vehicle = vehicle, shopId = shopId}
    }
    
    lib.registerContext({
        id = 'tuner_tablet_main',
        title = Lang['title_tuner_tablet'],
        options = tabletOptions
    })
    lib.showContext('tuner_tablet_main')
end)

RegisterNetEvent('tuningsystem:client:jobs_menu', function(data)
    local job_options = {}

    if npc_job ~= nil and next(npc_job) then
        job_options = {
            {
                title = Lang['title_npc_job_cancel_job_x']:format(npc_job.menu.title) or '', 
                icon = npc_job.menu.icon or nil,
                description = Lang['desc_npc_job_cancel'],
                onSelect = function()
                    cancel_job = true
                end
            }
        }
    else
        local done = false
        Core.TriggerCallback('tuningsystem:server:checkJobCooldown', function(hasCooldown, seconds, time)
            if not hasCooldown then 
                if Config.SalvageJob.Enable == true then
                    job_options[#job_options + 1] = {
                        title = Config.SalvageJob.Menu.title or '', 
                        icon = Config.SalvageJob.Menu.icon or nil,
                        description = Config.SalvageJob.Menu.description or nil,
                        onSelect = function()
                            StartSalvageVehicleJob()
                        end
                    }
                end
                if Config.MobileTuningJob.Enable == true then
                    job_options[#job_options + 1] = {
                        title = Config.MobileTuningJob.Menu.title or '', 
                        icon = Config.MobileTuningJob.Menu.icon or nil,
                        description = Config.MobileTuningJob.Menu.description or nil,
                        onSelect = function()
                            StartMobileTuningJob()
                        end
                    }
                end
            else
                if seconds then
                    return Core.Notification({
                        title = '',
                        message = Lang['job_cooldown_1']:format(time),
                        type = 'inform'
                    })
                else
                    return Core.Notification({
                        title = '',
                        message = Lang['job_cooldown_2']:format(time),
                        type = 'inform'
                    })
                end
            end
            done = true
        end)
        while not done do 
            Wait(10)
        end
    end

    if job_options == nil or next(job_options) == nil then
        Core.Notification({
            title = '',
            message = Lang['no_jobs_available'],
            type = 'inform'
        })
        return lib.showContext('tuner_tablet_main')
    end

    table.sort(job_options, function(a, b)
        return a.title < b.title
    end)
    
    lib.registerContext({
        id = 'tuner_npc_jobs_menu',
        title = Lang['title_npc_jobs'],
        menu = 'tuner_tablet_main',
        options = job_options
    })

    lib.showContext('tuner_npc_jobs_menu')
end)

RegisterNetEvent('tuningsystem:client:modOrders', function(data)
    if Config.Shops[data.shopId].orders == nil or next(Config.Shops[data.shopId].orders) == nil then
        Core.Notification({
            title = '',
            message = Lang['no_mod_orders_to_show'],
            type = 'inform'
        })
        return lib.showContext('tuner_tablet_main')
    end

    local orders = {}

    for plate,order in pairs(Config.Shops[data.shopId].orders) do
        local orderId = #orders + 1
        orders[orderId] = {
            title = Lang['title_mod_order_list']:format(orderId, plate),
            icon = 'car',
            menu = 'tuner_tablet_view_mod_order_'..plate,
            arrow = true,
        }

        local list_options = {
            {title = Lang['title_mod_order_customer']:format(order.player.name), icon = Config.ModOrder.Icons.customer, readOnly = true},
            {title = Lang['title_mod_order_paid_amount']:format(GetFormattedPrice(math.floor(order.paidAmount))), icon = Config.ModOrder.Icons.paidAmount, readOnly = true, metadata = {}},
            {title = Lang['title_mod_order_refund'], icon = Config.ModOrder.Icons.refund, onSelect = function()
                TriggerServerEvent('tuningsystem:server:cancelModOrder', data.shopId, plate)
                Core.Notification({
                    title = '',
                    message = Lang['mod_order_has_been_refunded']:format(GetFormattedPrice(math.floor(order.paidAmount)), order.player.name),
                    type = 'success'
                })
            end},
            {title = ' ', disabled = true, readOnly = true},
        }

        if Config.ModOrder.RefundRequiredGrade ~= nil then
            if Core.GetJob().grade < Config.ModOrder.RefundRequiredGrade then
                list_options[3].readOnly = true
            end
        end

        if order.taken == true then
            list_options[3].disable = true
            list_options[3].readOnly = true
        end

        for taskId,taskData in ipairs(order.taskList) do
            list_options[#list_options+1] = {
                title = taskData.variantLabel..' ('..taskData.modLabel..')',
                readOnly = true,
                icon = taskData.completed and Config.ModOrder.Icons.taskComplete or Config.ModOrder.Icons.taskIncomplete,
                progress = taskData.completed and 100 or 0,
                colorScheme = taskData.completed and 'green',
                metadata = {
                    {label = Lang['meta_mod_order_item_price'], value = GetFormattedPrice(math.floor(taskData.invoice.item))},
                    {label = Lang['meta_mod_order_labor_charge'], value = GetFormattedPrice(math.floor(taskData.invoice.base + taskData.invoice.tiered))},
                    {label = Lang['meta_mod_order_total'], value = GetFormattedPrice(math.floor(taskData.price))},
                }
            }
        end

        list_options[2].metadata = {
            {label = Lang['meta_mod_order_part_acquisition'], value = GetFormattedPrice(math.floor(order.partAcquisition))},
            {label = Lang['meta_mod_order_labor_charge'], value = GetFormattedPrice(math.floor(order.laborCharge))},
            {label = Lang['meta_mod_order_markup_x']:format(Config.Shops[data.shopId].markup), value = GetFormattedPrice(math.floor(order.markupAmount))},
            {label = Lang['meta_mod_order_total'], value = GetFormattedPrice(math.floor(order.paidAmount))},
        }

        list_options[3].metadata = {
            {label = Lang['meta_mod_order_customer_credit'], value = GetFormattedPrice(math.floor(order.paidAmount))},
            {label = Lang['meta_mod_order_shop_debit'], value = GetFormattedPrice(math.floor(order.markupAmount + order.partAcquisition))},
        }

        lib.registerContext({
            id = 'tuner_tablet_view_mod_order_'..plate,
            title = Lang['title_mod_order_list']:format(orderId, plate),
            menu = 'tuner_tablet_mod_orders',
            options = list_options
        })
    end
    
    lib.registerContext({
        id = 'tuner_tablet_mod_orders',
        title = Lang['title_tablet_mod_orders'],
        menu = 'tuner_tablet_main',
        options = orders
    })

    lib.showContext('tuner_tablet_mod_orders')
end)

RegisterNetEvent('tuningsystem:client:examinePerformanceParts', function(data)
    local menuOptions = {}
    local vehicleProperties = Core.GetVehicleProperties(data.vehicle)

    for modName,v in pairs(Config.Mods) do
        if v.category == 'performance' then
            -- get current mod value:
            local currentModValue = vehicleProperties[modName]
            if type(currentModValue) == 'boolean' then
                currentModValue = currentModValue == true and 1 or currentModValue == false and 0
            end
            -- create menu option:
            menuOptions[#menuOptions + 1] = {
                title = GetVehicleModVariantLabel(data.vehicle, modName, currentModValue),
                icon = v.icon or nil,
                readOnly = true,
            }
        end
    end
    
    lib.registerContext({
        id = 'examine_performance_parts',
        title = Lang['title_tablet_examine_performance'],
        menu = 'tuner_tablet_main',
        options = menuOptions
    })
    lib.showContext('examine_performance_parts')
end)





-- WIP / WIP / WIP / WIP / WIP --
tuningBay = {}
OpenTuningBayMain = function(point)

    local vehicle = GetVehiclePedIsIn(player, false)
    if vehicle == nil and vehicle <= 0 or not DoesEntityExist(vehicle) then
        usingMenu = false
        point.textUi = false
        return Core.Notification({
            title = '',
            message = Lang['must_be_inside_veh'],
            type = 'error'
        })
    end

    tuningBay.vehicle = vehicle
    tuningBay.vehiclePlate = GetVehicleNumberPlateText(tuningBay.vehicle)
    tuningBay.vehicleClass = GetVehicleClass(tuningBay.vehicle)
    tuningBay.vehicleModel = GetEntityModel(tuningBay.vehicle)

    local menuOptions = {}

    -- engine swap menu:

    local modelName = GetVehicleModelName(tuningBay.vehicle)
    if Config.Engines[tostring(tuningBay.vehicleClass)] ~= nil then
        local engines = {}
        for k,v in ipairs(Config.Engines[tostring(tuningBay.vehicleClass)]) do
            local index = (#engines + 1)

            engines[index] = {
                title = v.label..' - '..GetFormattedPrice(v.price),
                icon = v.icon ~= nil and v.icon or Config.EngineSwaps.DefaultIcon,
                args = {label = v.label, price = v.price, soundName = v.soundName or nil, icon = v.icon, default = modelName},
                onSelect = function(args)
                    EngineSwapAlertDialog(point.shopId, tuningBay.vehicle, args)
                end
            }

            -- check if modelName not nil and add stock soundName option:
            if v.stock ~= nil and v.stock == true then
                if modelName ~= nil then
                    engines[index].args.soundName = modelName
                else
                    engines[index] = nil
                end
            end

        end
        lib.registerContext({
            id = 'engine_swap_main',
            title = Lang['title_engine_swap'],
            menu = 'tuning_bay_main_menu',
            options = engines
        })
        if Config.Shops[point.shopId].delivery ~= nil then
            menuOptions[#menuOptions + 1] = {
                title = Lang['title_engine_swap'],
                icon = 'gears',
                description = Lang['desc_tuningbay_engine_swap'],
                arrow = true,
                menu = 'engine_swap_main'
            }
        else
            print("skipped engine swap menu; shop havent set a delivery point from admin menu.")
        end
    end

    -- add mod order if exists:
    if Config.Shops[point.shopId].orders ~= nil and next(Config.Shops[point.shopId].orders) then
        if Config.Shops[point.shopId].orders[tuningBay.vehiclePlate] ~= nil and next(Config.Shops[point.shopId].orders[tuningBay.vehiclePlate]) then
            local order = Config.Shops[point.shopId].orders[tuningBay.vehiclePlate]

            local ViewSelectedModOrder = function()
                local list_options = {
                    {title = Lang['title_mod_order_customer']:format(order.player.name), icon = Config.ModOrder.Icons.customer, readOnly = true},
                    {title = Lang['title_mod_order_paid_amount']:format(GetFormattedPrice(math.floor(order.paidAmount))), icon = Config.ModOrder.Icons.paidAmount, readOnly = true, metadata = {}},
                    {title = Lang['title_mod_order_begin_work'], icon = Config.ModOrder.Icons.beginWork, args = {taken = order.taken}, onSelect = function(args)
                        if args.taken == false then
                            modOrder = ModOrderWorkflow(point.shopId, tuningBay.vehiclePlate, order)
                        else
                            modOrder.StopWork(false)
                        end
                    end},
                    {title = ' ', disabled = true, readOnly = true},
                }
            
                if order.taken == true then
                    list_options[3].title = Lang['title_mod_order_stop_work']
                    list_options[3].icon = Config.ModOrder.Icons.stopWork
                end
            
                for taskId,taskData in ipairs(order.taskList) do
                    list_options[#list_options+1] = {
                        title = taskData.variantLabel..' ('..taskData.modLabel..')',
                        readOnly = true,
                        icon = taskData.completed and Config.ModOrder.Icons.taskComplete or Config.ModOrder.Icons.taskIncomplete,
                        progress = taskData.completed and 100 or 0,
                        colorScheme = taskData.completed and 'green',
                        metadata = {
                            {label = Lang['meta_mod_order_item_price'], value = GetFormattedPrice(math.floor(taskData.invoice.item))},
                            {label = Lang['meta_mod_order_labor_charge'], value = GetFormattedPrice(math.floor(taskData.invoice.base + taskData.invoice.tiered))},
                            {label = Lang['meta_mod_order_total'], value = GetFormattedPrice(math.floor(taskData.price))},
                        }
                    }
                end
            
                list_options[2].metadata = {
                    {label = Lang['meta_mod_order_part_acquisition'], value = GetFormattedPrice(math.floor(order.partAcquisition))},
                    {label = Lang['meta_mod_order_labor_charge'], value = GetFormattedPrice(math.floor(order.laborCharge))},
                    {label = Lang['meta_mod_order_markup_x']:format(Config.Shops[point.shopId].markup), value = GetFormattedPrice(math.floor(order.markupAmount))},
                    {label = Lang['meta_mod_order_total'], value = GetFormattedPrice(math.floor(order.paidAmount))},
                }
            
                lib.registerContext({
                    id = 'tuner_bay_view_mod_order'..tuningBay.vehiclePlate,
                    title = Lang['title_tuningbay_mod_order']:format(tuningBay.vehiclePlate),
                    menu = 'tuning_bay_main_menu',
                    options = list_options
                })
            end

            ViewSelectedModOrder()

            menuOptions[#menuOptions+1] = {
                title = Lang['title_tuningbay_view_mod_order'],
                icon = 'receipt',
                description = Lang['desc_tuningbay_view_mod_orders'],
                arrow = true,
                menu = 'tuner_bay_view_mod_order'..tuningBay.vehiclePlate
            }
        end
    end

    -- NITROUS MAIN MENU:
    menuOptions[#menuOptions + 1] = {
        title = Lang['title_nos_main_menu'],
        icon = 'fire-flame-simple',
        event = 'tuningsystem:client:nitrousMainMenu',
        description = Lang['desc_tuningbay_nitrous'],
        arrow = true,
        args = {shopId = point.shopId, vehicle = tuningBay.vehicle}
    }

    lib.registerContext({
        id = 'tuning_bay_main_menu',
        title = Lang['title_tuningbay2']:format(tuningBay.vehiclePlate),
        onExit = function()
            usingMenu = false
            point.textUi = false
        end,
        options = menuOptions,
    })
    lib.showContext('tuning_bay_main_menu')
end








RegisterNetEvent('tuningsystem:client:dynoTuning', function(data)
    local vehicleCache, itemId, itemData = data.vehicle, data.itemId, data.itemData
    local isTunerJob, shopId = IsTunerJob(Core.GetJob().name)

    if Config.T1GER_MechanicSystem and not isTunerJob then
        if Config.Debug then 
            print("not hired in tuner shop, so checking mechanic shops")
        end
        isTunerJob = IsPlayerMechanic()
    end

    if not isTunerJob then
        return Core.Notification({
            title = '',
            message = Lang['need_tuner_job'],
            type = 'inform'
        })
    end

    local vehicle = GetVehiclePedIsIn(player, false)
    if vehicle == nil or vehicle == 0 then 
        return Core.Notification({
            title = '',
            message = Lang['must_be_inside_veh'],
            type = 'inform'
        })
    elseif vehicle ~= vehicleCache then 
        return Core.Notification({
            title = '',
            message = 'Conenct the Tuner Tablet again!',
            type = 'inform'
        })
    end
    
    print("function for dyno tuning")

    
end)
