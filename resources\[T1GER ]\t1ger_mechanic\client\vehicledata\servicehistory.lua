--- Function called when using the service book item or command
local function UseServiceBook()
    local vehicle = GetVehiclePedIsIn(player, false)
    if not vehicle or not DoesEntityExist(vehicle) then return end

    local plate = GetVehicleNumberPlateText(vehicle)

    local serviceHistory = lib.callback.await("t1ger_mechanic:server:getVehicleServiceHistory", false, plate)
    if not serviceHistory then return end

    local grouped = {}
    -- Step 1: Group by unique key (date + mileage)
    for _, entry in ipairs(serviceHistory) do
        local key = entry.date .. "_" .. entry.mileage
        if not grouped[key] then
            grouped[key] = {
                id = entry.id, -- use ID for sorting
                mileage = entry.mileage,
                date = entry.date,
                shop = entry.shop or entry.mechanic or locale("menu_metadata.not_answered"),
                parts = {}
            }
        end
        -- Update ID if this entry has a newer ID (so latest grouped set gets the highest one)
        if entry.id > grouped[key].id then
            grouped[key].id = entry.id
        end
        table.insert(grouped[key].parts, entry.part)
    end

    -- Step 2: Convert to list and sort by ID descending
    local groupedList = {}
    for _, entry in pairs(grouped) do
        table.insert(groupedList, entry)
    end

    table.sort(groupedList, function(a, b)
        if a.date == b.date then
            -- Sort by mileage (converted to number) descending
            return tonumber(a.mileage) > tonumber(b.mileage)
        else
            return a.date > b.date
        end
    end)    

    -- Step 3: Build menu options
    local menuOptions = {}

    for _, entry in ipairs(groupedList) do
        menuOptions[#menuOptions+1] = {
            title = string.format(locale("menu_title.service_book_log"), entry.date, math.groupdigits(entry.mileage), Config.Mileage.Unit),
            description = string.format(locale("menu_description.service_book_log"), entry.shop),
            icon = "screwdriver-wrench",
            metadata = entry.parts
        }
    end

    -- Register and show
    lib.registerContext({
        id = "service_book",
        title = locale("menu_title.service_book_main"),
        options = menuOptions
    })
    lib.showContext("service_book")
end

--- Command if enabled to open service book menu
if Config.ServiceHistory.Command.enable then
    RegisterCommand(Config.ServiceHistory.Command.name, function(source, args, rawCommand)
        UseServiceBook()
    end, false)
end

--- Event for using service book item:
RegisterNetEvent("t1ger_mechanic:client:useServiceBook", function()
    UseServiceBook()
end)