<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims />
  <InitDatas>
	    <Item>
      <modelName>DBGODzMBG900</modelName>
      <txdName>DBGODzMBG900</txdName>
      <handlingId>DBGODzMBG900</handlingId>
      <gameName>MBG 900</gameName>
      <vehicleMakeName>FIVEMCAR.COM</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>DUBSTA</audioNameHash>
      <layout>LAYOUT_BISON</layout>
      <coverBoundOffsets>BISON_COVER_OFFSET_INFO</coverBoundOffsets>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>DEFAULT_FOLLOW_VEHICLE_CAMERA</cameraName>
      <aimCameraName>DEFAULT_THIRD_PERSON_VEHICLE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>VEHICLE_BONNET_CAMERA_MID_NEAR</bonnetCameraName>
      <povCameraName>DEFAULT_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.040000" y="-0.060000" z="-0.030000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.075000" y="-0.128000" z="-0.020000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.058000" z="-0.058000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="-0.058000" z="-0.055000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="-0.058000" z="-0.055000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.015000" z="-0.040000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="-0.043000" y="-0.043000" z="-0.055000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.015000" z="-0.040000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.143000" y="0.278000" z="0.565000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.204000" y="0.176000" z="0.490000" />
      <FirstPersonMobilePhoneSeatIKOffset>
        <Item>
			<Offset x="0.204000" y="0.131000" z="0.490000" />
			<SeatIndex value="2" />
		</Item>
        <Item>
			<Offset x="0.204000" y="0.131000" z="0.490000" />
			<SeatIndex value="3" />
		</Item>
		<Item>
			<Offset x="0.469000" y="0.416000" z="0.493000" />
			<SeatIndex value="4" />
		</Item>
		<Item>
			<Offset x="0.159000" y="0.576000" z="0.553000" />
			<SeatIndex value="5" />
		</Item>
      </FirstPersonMobilePhoneSeatIKOffset>
      <PovCameraOffset x="0.000000" y="-0.170000" z="0.600000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.020000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.020000" />
      <vfxInfoName>VFXVEHICLEINFO_CAR_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="true" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="true" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.300000" />
      <wheelScaleRear value="0.300000" />
      <dirtLevelMin value="0.300000" />
      <dirtLevelMax value="0.850000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.600000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="1.000000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        15.000000
        30.000000
        70.000000
        140.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="0.931" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="1.000000" />
      <frequency value="100" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="999" />
      <flags>FLAG_EXTRAS_ALL FLAG_HAS_LIVERY FLAG_IS_OFFROAD_VEHICLE FLAG_IS_BULKY</flags>
      <type>VEHICLE_TYPE_CAR</type>
      <plateType>VPT_FRONT_AND_BACK_PLATES</plateType>
	  <dashboardType>VDT_TRUCKDIGI</dashboardType>
      <vehicleClass>VC_VAN</vehicleClass>
      <wheelType>VWT_SUV</wheelType>
      <trailers>
        <Item>boattrailer</Item>
        <Item>trailersmall</Item>
      </trailers>
      <additionalTrailers>
        <Item>trailersmall2</Item>
      </additionalTrailers>
      <drivers>
		<Item>
          <driverName>S_M_M_AutoShop_01</driverName>
          <npcName />
        </Item>
		<Item>
          <driverName>S_M_M_AutoShop_02</driverName>
          <npcName />
        </Item>
		<Item>
          <driverName>S_M_M_Trucker_01</driverName>
          <npcName />
        </Item>
      </drivers>
      <extraIncludes>
         <Item>EXTRA_1 EXTRA_2</Item>
    </extraIncludes> 
      <doorsWithCollisionWhenClosed>
        <Item>VEH_EXT_BOOT</Item>
      </doorsWithCollisionWhenClosed>
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras>
	  <Item>EXTRA_1 EXTRA_2</Item>
	  </requiredExtras>
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet>Truck</NmBraceOverrideSet>
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
      <firstPersonDrivebyData>
        <Item>RANGER_BISON_FRONT_LEFT</Item>
        <Item>VAN_RUMPO_FRONT_RIGHT</Item>
        <Item>RANGER_BISON_REAR_LEFT</Item>
        <Item>RANGER_BISON_REAR_RIGHT</Item>
        <Item>VAN_BODHI_REAR_LEFT</Item>
        <Item>VAN_BODHI_REAR_RIGHT</Item>
      </firstPersonDrivebyData>
    </Item>
  </InitDatas>
  <txdRelationships>
	    <Item>
      <parent>vehicles_bob_brown_interior</parent>
      <child>DBGODzMBG900</child>
    </Item>
  </txdRelationships>
</CVehicleModelInfo__InitDataList>