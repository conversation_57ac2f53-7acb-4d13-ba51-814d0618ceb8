-- Opens the main duty menu for the mechanic shop
---@param shopId number unique shop identifier
---@param markerId string unique marker identifier
function DutyMain(shopId, markerId)
    local onDuty, dutyLabel = false, locale("menu_title.no")
    local shopData = Shops[shopId] -- get shop data
    local playerJob = _API.Player:GetJob() -- get player job

    -- check if on job or is on duty?
    if playerJob.name == shopData.job.name then
        onDuty = true 
        dutyLabel = locale("menu_title.yes")
    end

    -- register context:
    lib.registerContext({
        id = "mechanic_duty_menu",
        title = locale("menu_title.duty_main"),
        options = {
            {
                title = string.format(locale("menu_title.duty_state"), dutyLabel),
                icon = "spinner",
                disabled = true
            },
            {
                title = locale("menu_title.duty_clock_in_out"),
                icon = "toggle-on",
                onSelect = function()
                    TriggerServerEvent("t1ger_mechanic:server:toggleDuty", onDuty, shopId)
                    Wait(100)
                    DutyMain(shopId, markerId)
                end
            }
        }
    })
    -- show context:
    lib.showContext("mechanic_duty_menu")
end