ParkingConfig = {
        ParkingSpots = {

        -- legionSq Parking, These can be iffy because the gates and pathing in the area. It is what it is, mlos that change the are only add to complications.
        vector4(216.5536, -773.5043, 30.4210, 68.5943),
        vector4(215.7879, -776.1614, 30.4259, 70.2589),
        vector4(214.7778, -778.6617, 30.4364, 70.0988),
        vector4(214.0412, -781.2578, 30.4436, 67.8297),
        vector4(213.2212, -783.8340, 30.4528, 69.4862),
        vector4(212.2259, -786.3449, 30.4670, 67.9735),
        vector4(211.2191, -788.7724, 30.4813, 68.4553),
        vector4(210.2605, -791.1945, 30.4969, 69.6803),
        vector4(210.2605, -791.1945, 30.4969, 69.6803),
        vector4(209.3122, -793.6119, 30.5151, 71.4489),

        vector4(218.2334, -796.6345, 30.3452, 248.8618),
        vector4(219.5647, -791.5513, 30.3419, 248.8551),
        vector4(220.3498, -789.0867, 30.3485, 248.8456),
        vector4(221.2847, -786.5503, 30.3514, 249.8745),
        vector4(222.3132, -784.1670, 30.3454, 248.7818),
        vector4(223.9967, -778.9752, 30.3415, 249.2326),
        vector4(225.0811, -776.3969, 30.3496, 248.1942),

        vector4(207.96, -795.82, 30.55, 248.81),
        vector4(206.91, -798.68, 30.57, 248.97),
        vector4(205.45, -800.74, 30.6, 249.38),
        vector4(234.07, -771.23, 30.33, 249.64),
        vector4(232.09, -776.31, 30.3, 248.31),
        vector4(230.94, -778.87, 30.29, 248.97),
        vector4(230.7, -781.41, 30.28, 250.6),
        vector4(229.02, -783.81, 30.28, 250.06),
        vector4(227.64, -789.2, 30.26, 247.99),
        vector4(226.28, -791.42, 30.26, 248.95),
        vector4(225.72, -794.21, 30.24, 249.28),
        vector4(224.66, -796.59, 30.24, 247.01),
        vector4(223.2, -798.92, 30.25, 248.79),
        vector4(222.61, -801.4, 30.25, 248.1),
        vector4(221.77, -804.1, 30.26, 245.92),
        vector4(221.6, -806.74, 30.25, 247.77),
        vector4(228.63, -768.88, 30.36, 69.31),
        vector4(227.76, -771.74, 30.35, 71.5),
        vector4(226.39, -773.88, 30.35, 68.62),
        vector4(224.35, -778.81, 30.34, 64.93),
        vector4(224.25, -781.78, 30.33, 70.11),
        vector4(222.81, -784.16, 30.34, 72.1),
        vector4(221.16, -786.48, 30.35, 68.66),
        vector4(221.04, -789.3, 30.34, 68.07),
        vector4(219.76, -791.48, 30.34, 70.29),
        vector4(218.97, -794.38, 30.34, 68.04),
        vector4(218.34, -796.97, 30.34, 66.59),
        vector4(217.56, -799.45, 30.35, 69.51),
        vector4(216.23, -801.69, 30.37, 70.17),
        vector4(215.69, -804.56, 30.38, 68.56),
        vector4(228.66, -786.48, 30.27, 248.6),
        vector4(233.41, -774.07, 30.31, 248.68),
        vector4(236.32, -812.55, 29.88, 248.26),
        vector4(237.41, -809.93, 29.89, 244.26),
        vector4(238.45, -807.4, 29.9, 244.04),
        vector4(238.94, -804.82, 29.93, 248.53),
        vector4(240.36, -802.39, 29.94, 246.63),
        vector4(241.52, -799.87, 29.96, 245.68),
        vector4(242.03, -797.37, 29.98, 246.08),
        vector4(243.06, -794.79, 30.01, 249.35),
        vector4(244.27, -792.42, 30.03, 247.07),
        vector4(245.14, -789.87, 30.06, 247.13),
        vector4(246.18, -787.38, 30.08, 248.58),
        vector4(246.61, -784.52, 30.12, 248.32),
        vector4(248.17, -782.46, 30.14, 248.95),
        vector4(249.12, -779.63, 30.18, 249.01),
        vector4(249.32, -776.78, 30.22, 247.42),
        vector4(250.19, -774.3, 30.26, 248.86),
        vector4(244.75, -772.24, 30.29, 69.17),
        vector4(228.66, -786.48, 30.27, 248.6),
        vector4(233.41, -774.07, 30.31, 248.68),
        vector4(231.42, -810.44, 30.01, 70.18),
        vector4(231.62, -807.79, 30.03, 68.27),
        vector4(232.96, -805.51, 30.02, 66.62),
        vector4(234.88, -803.0, 30.02, 69.62),
        vector4(235.38, -800.43, 30.04, 68.23),
        vector4(236.58, -797.83, 30.05, 69.89),
        vector4(236.68, -795.08, 30.08, 66.13),
        vector4(237.88, -792.75, 30.08, 66.76),
        vector4(238.84, -790.11, 30.11, 68.34),
        vector4(240.15, -787.59, 30.13, 67.55),
        vector4(241.01, -785.27, 30.15, 69.84),
        vector4(244.51, -775.07, 30.26, 70.7),
        vector4(243.65, -777.69, 30.22, 67.25),
        vector4(242.44, -779.84, 30.2, 68.04),
        vector4(241.32, -782.41, 30.18, 69.19),
        -- lower power -- ty Joey
        vector4(48.46, -600.58, 31.05, 339.83),
        vector4(45.27, -598.81, 31.05, 337.94),
        vector4(41.67, -597.67, 31.05, 340.45),
        vector4(38.56, -596.16, 31.05, 338.12),
        vector4(34.99, -595.47, 31.05, 340.89),
        vector4(31.2, -593.92, 31.05, 339.65),
        vector4(27.64, -592.9, 31.05, 341.86),
        vector4(7.72, -596.58, 31.05, 251.56),
        vector4(9.52, -592.97, 31.05, 249.91),
        vector4(10.79, -589.67, 31.05, 248.15),
        vector4(11.64, -586.28, 31.05, 250.38),
        vector4(13.02, -582.73, 31.05, 250.56),
        vector4(14.43, -579.28, 31.05, 250.48),
        vector4(15.79, -575.91, 31.05, 250.57),
        vector4(16.99, -572.2, 31.05, 250.35),
        vector4(32.27, -579.78, 31.05, 338.59),
        vector4(36.28, -581.01, 31.05, 342.11),
        vector4(39.79, -581.82, 31.05, 339.93),
        vector4(42.7, -583.86, 31.05, 338.52),
        vector4(46.25, -584.88, 31.05, 343.14),
        vector4(49.66, -586.2, 31.05, 341.91),
        vector4(53.64, -586.94, 31.05, 341.75),
        vector4(57.03, -588.71, 31.05, 340.63),
        vector4(64.95, -576.19, 31.05, 163.06),
        vector4(68.08, -577.66, 31.05, 154.95),
        vector4(72.16, -578.61, 31.05, 155.89),
        vector4(73.41, -586.19, 31.05, 69.64),
        vector4(72.48, -589.82, 31.05, 67.8),
        vector4(70.89, -592.9, 31.05, 68.41),
        vector4(69.46, -596.76, 31.05, 68.6),
        vector4(67.89, -600.17, 31.05, 69.91),
        vector4(67.24, -603.68, 31.05, 67.92),
        vector4(65.89, -607.19, 31.05, 68.69),
        vector4(64.22, -611.22, 31.1, 68.4),
        vector4(54.68, -638.55, 31.09, 246.21),
        vector4(53.23, -641.75, 31.06, 69.7),
        vector4(52.06, -645.06, 31.05, 70.13),
        vector4(50.58, -648.56, 31.05, 67.35),
        vector4(49.61, -652.33, 31.05, 69.85),
        vector4(48.33, -655.49, 31.05, 68.67),
        vector4(47.37, -659.23, 31.05, 69.73),
        vector4(45.67, -662.47, 31.05, 66.96),
        --Alta Apartments
        vector4(-297.71, -990.11, 30.76, 338.79),
        vector4(-301.09, -988.82, 30.76, 339.11),
        vector4(-304.64, -987.72, 30.76, 339.36),
        vector4(-308.09, -986.34, 30.76, 339.47),
        vector4(-311.46, -985.08, 30.76, 339.5),
        vector4(-315.07, -983.98, 30.76, 339.18),
        vector4(-318.71, -982.49, 30.76, 338.43),
        vector4(-285.76, -888.04, 30.76, 168.44),
        vector4(-289.39, -887.34, 30.76, 168.59),
        vector4(-292.99, -886.36, 30.76, 167.4),
        vector4(-296.78, -885.82, 30.75, 167.93),
        vector4(-300.35, -885.14, 30.76, 167.76),
        vector4(-303.82, -884.06, 30.76, 167.76),
        vector4(-307.59, -883.44, 30.76, 167.24),
        vector4(-311.16, -882.7, 30.76, 166.92),
        vector4(-314.74, -881.99, 30.75, 166.91),
        vector4(-318.34, -881.19, 30.75, 167.5),
        vector4(-322.02, -880.47, 30.75, 167.75),
        vector4(-325.62, -879.65, 30.75, 168.31),
        vector4(-329.11, -878.9, 30.75, 168.35),
        vector4(-332.88, -878.22, 30.75, 167.35),
        vector4(-336.55, -877.38, 30.75, 168.02),
        vector4(-340.1, -876.67, 30.75, 167.45),
        vector4(-343.78, -875.91, 30.75, 167.01),
        vector4(-352.86, -874.08, 30.75, 0.76),
        vector4(-360.26, -889.43, 30.75, 269.23),
        vector4(-360.46, -893.17, 30.75, 268.23),
        vector4(-360.24, -896.83, 30.75, 270.26),
        vector4(-360.37, -900.58, 30.75, 268.61),
        vector4(-360.29, -904.27, 30.75, 269.75),
        vector4(-360.14, -908.01, 30.75, 270.4),
        vector4(-360.5, -911.66, 30.76, 269.55),
        vector4(-360.12, -915.4, 30.76, 269.63),
        vector4(-360.28, -919.07, 30.76, 270.11),
        vector4(-360.56, -922.77, 30.75, 268.41),
        vector4(-360.46, -926.49, 30.76, 270.43),
        vector4(-360.37, -930.12, 30.76, 269.72),
        vector4(-360.22, -933.88, 30.76, 270.34),
        vector4(-360.28, -937.58, 30.76, 269.99),
        vector4(-360.47, -941.31, 30.75, 269.54),
        vector4(-360.28, -944.99, 30.76, 270.14),
        vector4(-360.32, -948.72, 30.76, 269.82),
        vector4(-360.38, -952.44, 30.75, 269.95),
        vector4(-360.57, -956.16, 30.76, 270.31),
        vector4(-322.02, -981.29, 30.76, 339.92),
        vector4(-325.56, -980.1, 30.76, 340.1),
        vector4(-329.0, -978.69, 30.76, 338.49),
        vector4(-332.49, -977.59, 30.76, 339.58),
        vector4(-335.9, -976.3, 30.76, 339.7),
        vector4(-339.43, -975.08, 30.76, 339.47),
        vector4(-342.7, -973.45, 30.76, 338.85),
        vector4(-326.58, -956.4, 30.75, 250.37),
        vector4(-325.37, -952.84, 30.76, 250.47),
        vector4(-324.03, -949.43, 30.76, 250.37),
        vector4(-322.69, -945.96, 30.75, 249.91),
        vector4(-321.47, -942.43, 30.76, 250.29),
        vector4(-320.23, -939.06, 30.76, 250.38),
        vector4(-318.8, -935.56, 30.76, 249.46),
        vector4(-317.66, -932.03, 30.76, 250.73),
        vector4(-316.5, -928.43, 30.76, 250.15),
        vector4(-345.07, -932.2, 30.76, 69.27),
        vector4(-343.99, -928.61, 30.76, 70.11),
        vector4(-342.58, -925.18, 30.76, 70.69),
        vector4(-341.39, -921.67, 30.76, 69.83),
        vector4(-327.34, -924.44, 30.76, 69.82),
        vector4(-328.81, -927.89, 30.76, 69.43),
        vector4(-330.09, -931.33, 30.76, 70.81),
        vector4(-331.29, -934.83, 30.76, 70.29),
        vector4(-332.71, -938.34, 30.76, 69.75),
        vector4(-333.91, -941.81, 30.76, 69.46),
        vector4(-335.02, -945.3, 30.75, 71.24),
        vector4(-336.56, -948.74, 30.75, 70.65),
        vector4(-337.69, -952.22, 30.76, 70.31),
        vector4(-340.74, -902.45, 30.75, 167.89),
        vector4(-337.18, -903.24, 30.75, 167.65),
        vector4(-333.64, -903.98, 30.75, 167.05),
        vector4(-329.93, -904.61, 30.75, 167.95),
        vector4(-326.38, -905.62, 30.75, 168.51),
        vector4(-322.65, -906.2, 30.75, 167.96),
        vector4(-318.98, -906.94, 30.75, 168.06),
        vector4(-315.39, -907.89, 30.75, 166.81),
        vector4(-311.81, -908.81, 30.75, 167.5),
        vector4(-308.14, -909.33, 30.75, 167.34),
        vector4(-285.56, -921.9, 30.76, 70.14),
        vector4(-283.75, -918.52, 30.76, 70.23),
        vector4(-282.97, -914.81, 30.75, 69.93),
        vector4(-281.65, -911.4, 30.76, 69.49),
        vector4(-280.5, -908.04, 30.76, 69.51),
        vector4(-279.28, -904.45, 30.76, 70.18),
        vector4(-302.07, -933.44, 30.75, 69.9),
        vector4(-303.19, -937.09, 30.76, 70.71),
        vector4(-304.56, -940.34, 30.76, 70.04),
        vector4(-305.74, -943.95, 30.76, 70.49),
        vector4(-307.19, -947.34, 30.76, 69.04),
        vector4(-308.26, -950.95, 30.76, 70.24),
        vector4(-309.63, -954.35, 30.76, 68.9),
        vector4(-310.83, -957.88, 30.76, 69.56),
        vector4(-312.07, -961.38, 30.76, 70.21),
        vector4(-313.39, -964.8, 30.76, 68.92),
        vector4(-298.26, -899.82, 30.66, 346.23),
        vector4(-302.47, -898.86, 30.66, 348.7),
        vector4(-305.9, -898.52, 30.66, 351.35),
        vector4(-309.58, -897.38, 30.66, 347.24),
        vector4(-313.04, -896.37, 30.65, 349.98),
        vector4(-316.74, -895.46, 30.65, 347.83),
        vector4(-320.36, -894.95, 30.65, 348.97),
        vector4(-324.05, -893.86, 30.65, 348.71),
        vector4(-327.67, -893.18, 30.65, 347.61),
        vector4(-331.02, -892.69, 30.65, 346.03),
        vector4(-334.83, -891.72, 30.65, 350.2),
        vector4(-338.6, -891.08, 30.65, 348.34),
        --cityhall
        vector4(-475.26, -219.26, 36.05, 30.12),
        vector4(-478.26, -214.06, 36.21, 30.11),
        vector4(-481.41, -208.59, 36.37, 30.32),
        vector4(-484.34, -203.49, 36.52, 30.67),
        vector4(-487.17, -198.51, 36.67, 30.37),
        vector4(-490.26, -193.18, 36.83, 29.72),
        vector4(-493.21, -187.98, 36.99, 29.64),
        vector4(-496.19, -182.75, 37.14, 29.96),
        vector4(-499.21, -177.5, 37.3, 30.1),
    },
}