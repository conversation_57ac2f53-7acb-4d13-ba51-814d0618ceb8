local isCarryingCarJack = false -- is player carrying car jack prop?
local carJackProp = nil -- car jack object entity handle

--- Returns whether the player is currently using a car jack
--- @return boolean
local function IsCarryingCarJack()
    return isCarryingCarJack == true
end

--- Returns whether a given vehicle entity is on a carjack or not
--- @param vehicle number The vehicle entity handle
local function IsVehicleOnCarJack(vehicle)
    if not vehicle or not DoesEntityExist(vehicle) or not IsEntityAVehicle(vehicle) then
        return false
    end

    local vehState = Entity(vehicle).state
    if vehState["t1ger_mechanic:onCarJack"] then
        return true
    end

    return false
end
exports("IsVehicleOnCarJack", IsVehicleOnCarJack)

--- Play animation to carry the car jack
local function CarryPropAnimation()
    local anim = {dict = "missfinale_c2ig_11", clip = "pushcar_offcliff_f", blendIn = 3.0, blendOut = 3.0, duration = -1, flags = 49}
    lib.requestAnimDict(anim.dict)
    TaskPlayAnim(player, anim.dict, anim.clip , anim.blendIn, anim.blendOut, anim.duration, anim.flags, 0, false, false, false)
end

--- Carry the car jack prop: play anim and attach prop to player
--- @param prop table Prop data
local function CarryCarJackProp()
    -- carry prop anim:
    CarryPropAnimation()
    Wait(250)

    carJackProp = CreateProp(Config.CarJack.Prop.model, coords)

    -- attach prop to player:
    local boneIndex = GetPedBoneIndex(player, Config.CarJack.Prop.boneId)
    AttachEntityToEntity(carJackProp, player, boneIndex, Config.CarJack.Prop.pos.x, Config.CarJack.Prop.pos.y, Config.CarJack.Prop.pos.z, Config.CarJack.Prop.rot.x, Config.CarJack.Prop.rot.y, Config.CarJack.Prop.rot.z, true, true, false, true, 1, true)
end

--- Returns carjack attach points to be used with ox lib points
--- @param vehicle number The vehicle entity handle
--- @return table
local function GetCarJackAttachPoints(vehicle)
    local attachPoints = {}
    local vehicleWheels = GetVehicleWheels(vehicle)
    if not vehicleWheels or next(vehicleWheels) == nil then return attachPoints end

    local centerCoords = GetEntityCoords(vehicle)
    local heading = GetEntityHeading(vehicle)

    -- Define local-space offsets and facing directions
    local offsetMap = {
        [0] = {x = -0.10, y = -0.7, z = 0.0, h = heading + 90.0}, -- Front Left
        [1] = {x =  0.10, y = -0.7, z = 0.0, h = heading - 90.0}, -- Front Right
        [2] = {x = -0.10, y =  0.7, z = 0.0, h = heading + 90.0}, -- Rear Left
        [3] = {x =  0.10, y =  0.7, z = 0.0, h = heading - 90.0}, -- Rear Right
    }

    -- loop found wheel with offset map
    for wheelIndex, wheelData in pairs(vehicleWheels) do
        local offset = offsetMap[wheelData.wheelId]
        if offset then
            -- Default wheel coords from centerOffset
            local defaultWheelCoords = CalculateBoneWorldCoords(centerCoords, heading, wheelData.centerOffset)
            
            -- Offset slightly inward and along the vehicle and on the ground
            local attachCoords = GetOffsetFromCoordAndHeadingInWorldCoords(defaultWheelCoords.x, defaultWheelCoords.y, defaultWheelCoords.z, heading, offset.x, offset.y, offset.z)
            local groundBool, groundZ = GetGroundZFor_3dCoord(attachCoords.x, attachCoords.y, attachCoords.z, false)
            
            -- store into points
            attachPoints[#attachPoints+1] = {
                coords = vector3(attachCoords.x, attachCoords.y, groundZ + 0.02),
                heading = offset.h,
                distance = 5,
                wheelId = wheelData.wheelId
            }
        end
    end

    return attachPoints
end

--- Triggers the useable car jack item handle
local function UseCarJack()
    if not Config.CarJack.AllowEveryone and not IsPlayerMechanic() then return end

    if IsCarryingCarJack() then
        return _API.ShowNotification(locale("notification.carjack_carrying"), "inform", {})
    end

    -- Get the closest vehicle
    local vehicle, vehicleDist = lib.getClosestVehicle(coords, 4.0, false)
    if not vehicle or not DoesEntityExist(vehicle) or not IsEntityAVehicle(vehicle) then
        return _API.ShowNotification(locale("notification.no_vehicle_nearby"), "inform", {})
    end

    -- get number of vehicle wheels
    local numWheels = GetVehicleNumberOfWheels(vehicle)
    if not numWheels or numWheels ~= 4 then
        return _API.ShowNotification(locale("notification.carjack_4_wheels_only"), "inform", {})
    end

    -- Check if vehicle is attached to carjack
    if IsVehicleOnCarJack(vehicle) then
        return _API.ShowNotification(locale("notification.carjack_vehicle_attached"), "inform", {})
    end

    -- Create points/draw texts to on vehicle to attach the car jack
    local attachPoints = GetCarJackAttachPoints(vehicle)
    if type(attachPoints) ~= "table" or not next(attachPoints) then return end

    -- Update state:
    isCarryingCarJack = true

    -- Create carjack object, play attached anim and return netId:
    CarryCarJackProp()

    --- Removes all ox lib points for carjack placement
    local function RemoveCarJackPoints()
        for index, value in pairs(attachPoints) do
            if value.point and next(value.point) then
                value.point.remove(value.point)
            end
        end
    end

    -- get vehicle netId:
    local vehicleNetId = VehToNet(vehicle)

    -- ox lib points
    local mk = Config.CarJack.Marker
    for index, value in pairs(attachPoints) do
        attachPoints[index].point = lib.points.new({
            coords = value.coords,
            distance = value.distance,
            heading = value.heading,
            wheelId = value.wheelId,

            nearby = function(point)
                local carJackCoords = GetEntityCoords(carJackProp)

                if #(carJackCoords - vector3(point.coords.x, point.coords.y, point.coords.z)) <= 0.5 then
                    DrawMarker(mk.type, point.coords.x, point.coords.y, point.coords.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, mk.scale.x, mk.scale.y, mk.scale.z, mk.color.r, mk.color.g, mk.color.b, mk.color.a, false, true, 0, true, false, false, false)
                    if IsControlJustReleased(0, Config.CarJack.Keybind) then
                        -- removes all ox lib points
                        RemoveCarJackPoints()

                        -- Detach and place carjack prop on the ground properly with forced heading
                        DetachEntity(carJackProp, false, false)
                        FreezeEntityPosition(carJackProp, true)
                        FreezeEntityPosition(vehicle, true)
                        SetVehicleOnGroundProperly(vehicle, true)
                        SetEntityCoords(carJackProp, point.coords.x, point.coords.y, point.coords.z, 0.0, 0.0, 0.0, false)
                        PlaceObjectOnGroundProperly(carJackProp)
                        SetEntityHeading(carJackProp, point.heading)

                        -- statebag on carjack entity with vehicle netId and wheelId
                        local objState = Entity(carJackProp).state
                        objState:set("t1ger_mechanic:carJackInfo", {
                            vehicleNetId = vehicleNetId,
                            wheelId = point.wheelId,
                            busy = false
                        }, true)

                        -- statebag on vehicle:
                        local vehState = Entity(vehicle).state
                        vehState:set("t1ger_mechanic:onCarJack", true, true)

                        -- clear ped tasks
                        ClearPedTasks(player)

                        -- Remove item
                        TriggerServerEvent("t1ger_mechanic:server:removeItem", Config.CarJack.Item, 1)

                        -- update is carrying state
                        isCarryingCarJack = false
                    end
                end
            end
        })
    end
end

--- Event for using car jack item:
RegisterNetEvent("t1ger_mechanic:client:useCarJack", function()
    UseCarJack()
end)

--- Animates vehicle on the car jack
--- @param vehicle number The vehicle entity handle
--- @param vehState table The vehicle statebag
--- @param wheelId number The wheelId the car jack is attached to
--- @param method string `"raise"` or `"lower"`
local function CarJackHandle(vehicle, vehState, wheelId, method)
    if not vehicle or not DoesEntityExist(vehicle) then return end
    if type(method) ~= "string" or type(wheelId) ~= "number" then return end

    local vehicleCoords = GetEntityCoords(vehicle)
    local vehicleRotation = GetEntityRotation(vehicle)
    local vehicleHeading = GetEntityHeading(vehicle)

    local leftSide = (wheelId == 1 or wheelId == 3)
    local delta = (method == "raise") and 1.0 or -1.0
    local pitch = vehicleRotation.y + (leftSide and -delta or delta)

    -- Clamp limits
    local outOfBounds =
        (method == "raise" and ((leftSide and pitch <= -10.0) or (not leftSide and pitch >= 10.0))) or
        (method == "lower" and ((leftSide and pitch >= 1.0) or (not leftSide and pitch <= -1.0)))

    if outOfBounds then
        local msg = (method == "raise") and locale("notification.carjack_fully_raised") or locale("notification.carjack_fully_lowered")
        return _API.ShowNotification(msg, "error", {})
    end

    -- Slight vertical nudge to re-trigger ground collision
    local zNudge = (method == "raise") and 0.01 or -0.01
    vehicleCoords = vehicleCoords + vec3(0.0, 0.0, zNudge)

    vehState:set("t1ger_mechanic:carJackControl", {
        pitch = pitch,
        heading = vehicleHeading,
        coords = vehicleCoords
    }, true)
end

--- Opens car jack handle menu to either "raise" or "lower" the vehicle
--- @param entity The car jack entity handle
local function CarJackMenu(entity)
    local entState = Entity(entity).state
    local carJack = entState["t1ger_mechanic:carJackInfo"]
    if not carJack then return end

    if carJack.busy then
        return _API.ShowNotification(locale("notification.carjack_is_busy"), "error", {})
    end
    carJack.busy = true
    entState:set("t1ger_mechanic:carJackInfo", carJack, true)

    -- request control of carJack.vehicleNetId
    NetworkRequestControlOfNetworkId(carJack.vehicleNetId)
    while not NetworkHasControlOfNetworkId(carJack.vehicleNetId) do
        NetworkRequestControlOfNetworkId(carJack.vehicleNetId)
        Wait(1)
    end

    -- get vehicle
    local vehicle = NetworkGetEntityFromNetworkId(carJack.vehicleNetId)
    while not DoesEntityExist(vehicle) do
        Wait(100)
        vehicle = NetworkGetEntityFromNetworkId(carJack.vehicleNetId)
    end
    local vehState = Entity(vehicle).state

    -- create menu options:
    local menuOptions, methods = {}, {"raise", "lower"}
    for i = 1, #methods do
        menuOptions[i] = {
            title = locale("menu_title.carjack_"..tostring(methods[i])),
            icon = Config.CarJack.Icons.menu[methods[i]],
            onSelect = function()
                CarJackHandle(vehicle, vehState, carJack.wheelId, methods[i])
                Wait(10)
                lib.showContext("t1ger_mechanic:carJackHandle")
            end
        }
    end

    -- Register context
    lib.registerContext({
        id = "t1ger_mechanic:carJackHandle",
        title = "Car Jack",
        onExit = function()
            carJack.busy = false
            entState:set("t1ger_mechanic:carJackInfo", carJack, true)
        end,
        options = menuOptions
    })

    -- Show context menu
    lib.showContext("t1ger_mechanic:carJackHandle")
end

-- Creates target options for car jack model
CreateThread(function()
    while not _Target do Wait(100) end -- wait for target to initialize

    -- insert models into one table
    local models = {Config.CarJack.Prop.model}

    local function CanInteractWithCarJack(entity)
        if IsEntityValid(entity) and GetEntityType(entity) ~= 0 then 
            if GetEntityModel(entity) == GetHashKey(Config.CarJack.Prop.model) then
                if not IsPedInAnyVehicle(player, false) and not IsPedInAnyVehicle(player, true) then
                    if Config.CarJack.AllowEveryone then
                        return true
                    else
                        if IsPlayerMechanic() then 
                            return true
                        end
                    end
                end
            end
        end
        return false
    end

    -- target options:
    local options = {
        [1] = {
            name = "t1ger_mechanic:carjack:remove",
            icon = Config.CarJack.Icons.target.remove,
            label = locale("target.carjack_remove"),
            canInteract = CanInteractWithCarJack,
            distance = 2.0,
            onSelect = function(entity)
                local netId = ObjToNet(entity)
                local carjackCoords = GetEntityCoords(entity)
                TriggerServerEvent("t1ger_mechanic:server:removeCarJack", netId, carjackCoords)
            end
        },
        [2] = {
            name = "t1ger_mechanic:carjack:handle",
            icon = Config.CarJack.Icons.target.handle,
            label = locale("target.carjack_handle"),
            canInteract = CanInteractWithCarJack,
            distance = 2.0,
            onSelect = function(entity)
                CarJackMenu(entity)
            end
        },
    }

    -- add target for models:
    _API.Target.AddModel(models, options)
end)

-- Statebag for animation/handle car jack:
AddStateBagChangeHandler("t1ger_mechanic:carJackControl" --[[key filter]], nil --[[bag filter]], function(bagName, key, value, _unused, replicated)
    Wait(0)
    if not value then return end
    if replicated then return end

    local vehicle = GetEntityFromStateBagName(bagName)
    if not vehicle or not DoesEntityExist(vehicle) then return end

    SetEntityRotation(vehicle, 0.0, value.pitch, value.heading, 2, true)
    SetEntityCoordsNoOffset(vehicle, value.coords.x, value.coords.y, value.coords.z, 0.0, 0.0, 0.0)
end)