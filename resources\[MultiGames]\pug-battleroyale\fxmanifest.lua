lua54 'yes'
fx_version 'cerulean'
game 'gta5'

author 'Pug'
description 'Discord: zpug'
version '3.0.3'

client_script {
    'client/*.lua',
    '@ox_lib/init.lua', -- This can be hashed out if you are not using ox_lib
}

server_script {
    '@oxmysql/lib/MySQL.lua',
	'server/*.lua',
}

shared_script {
    'config/*.lua',
}

escrow_ignore {
    'config/*.lua',
    'client/open.lua',
    'client/map_blips.lua',
    'client/player_damage.lua',
    'server/server.lua',
    'server/sv_open.lua',
    'server/sv_xpsystem.lua',
    'config/config-translate.lua',
    '[ARCADE-MLO]/int_arcade/client.lua',
    '[ARCADE-MLO]/int_arcade/fxmanifest.lua',
    '[ARCADE-MLO]/int_arcade/stream/*',
}

ui_page('html/index.html')

files({
	'html/index.html',
	'html/script.js',
	'html/style.css',
	'html/images/*',
    'html/weapon_icons/*.png',
    'html/sounds/*',

    'html_dui/index.html',
})
dependency '/assetpacks'