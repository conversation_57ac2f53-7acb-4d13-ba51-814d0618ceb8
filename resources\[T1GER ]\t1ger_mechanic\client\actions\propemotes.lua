local holdingObject, carryModel = false, 0

---Returns the prop emote corresponding to item
---@param item string The name of the item
---@return table PropEmote the data for the prop emote
local function GetPropEmoteByItem(item)
    for hash, emote in pairs(Config.Emotes.Props) do
        if item == emote.item then 
            return emote
        end
    end
    return nil
end

---Plays prop emote animation
---@param type string type of animation: `push` or `pickup`
local function PropEmoteAnimation(type)
    local anim = Config.Emotes.Anim[type]
    lib.requestAnimDict(anim.dict)
	TaskPlayAnim(player, anim.dict, anim.clip, anim.blendIn, anim.blendOut, anim.duration, anim.flags, 0, 0, 0, 0)
end

---Attaches the emote object to the player
---@param emote table emote data
---@param object entity the object entity handle
local function AttachPropToPlayer(emote, object)
    local boneIndex = GetPedBoneIndex(player, emote.bone)
    AttachEntityToEntity(object, player, boneIndex, emote.pos[1], emote.pos[2], emote.pos[3], emote.rot[1], emote.rot[2], emote.rot[3], true, true, false, true, 2, 1)
end

---Thread when holding a prop emote
---@param emote table emote data
---@param object entity the object entity handle
local function PropEmoteHandle(emote, object)
    while holdingObject do
        Wait(1)

        -- text ui
        local isOpen, text = lib.isTextUIOpen()
        if not isOpen or text ~= "[E] Place Prop" then
            lib.showTextUI("[E] Place Prop")
        end

        -- keypress
        if IsControlJustPressed(0, Config.Emotes.Keybind) then
            lib.hideTextUI()
            holdingObject = false
            if emote.push == nil then 
                PropEmoteAnimation("pickup")
                Wait(150)
            end
            Wait(250)
            DetachEntity(object)
            ClearPedTasks(player)
        end

    end
end

-- Event to use prop emote triggered by useable item
RegisterNetEvent("t1ger_mechanic:client:propEmote", function(item)
    -- check if not inside vehicle
    if IsPedInAnyVehicle(player, true) then
        return _API.ShowNotification(locale("notification.cannot_be_inside_vehicle"), "inform", {})
    end

    -- check if emote exists:
    local emote = GetPropEmoteByItem(item)
    if not emote then 
        return error("could not find prop emote for given item: "..item.."! Check your item names in Config.Emotes.Props")
    end

    -- check if already holding an object
    if holdingObject then
        return _API.ShowNotification(locale("notification.is_carrying_prop_emote"), "inform", {})
    end

    -- remove item:
    TriggerServerEvent("t1ger_mechanic:server:removeItem", emote.item, 1)

    carryModel = 0
	holdingObject = true

    if type(emote.push) == "boolean" and emote.push == true then
        PropEmoteAnimation("push")
        Wait(250)
    end

    carryModel = CreateProp(emote.model, coords)

    -- attach:
    AttachPropToPlayer(emote, carryModel)
    -- handle:
    Wait(500)
    PropEmoteHandle(emote, carryModel)
end)

---Returns whether the player can interact with an emote object
---@param entity number the entity handle
---@return boolean CanInteractPropEmote `true` can interact. `false` cannot interact
local function CanInteractPropEmote(entity)
    if IsEntityValid(entity) and GetEntityType(entity) ~= 0 then
        local modelHash = GetEntityModel(entity)
        if modelHash and Config.Emotes.Props[tostring(modelHash)] then
            local entityCoords = GetEntityCoords(entity)
            if #(coords - entityCoords) < Config.Emotes.Distance then
                return true
            end
        end
    end
    return false
end

-- Creates target options for prop emotes models
CreateThread(function()
    while not _Target do Wait(100) end -- wait for target to initialize

    -- insert models into one table
    local models = {}
    for hash, emote in pairs(Config.Emotes.Props) do
        table.insert(models, emote.model)
    end

    -- target options:
    local options = {
        [1] = {
            name = "t1ger_mechanic:propEmote:remove",
            icon = Config.Emotes.Icons.remove,
            label = locale("target.prop_emote_remove"),
            canInteract = CanInteractPropEmote,
            distance = 2.0,
            onSelect = function(entity)
                RemovePropEmote(entity)
            end
        },
        [2] = {
            name = "t1ger_mechanic:propEmote:carry",
            icon = Config.Emotes.Icons.attach,
            label = locale("target.prop_emote_attach"),
            canInteract = CanInteractPropEmote,
            distance = 2.0,
            onSelect = function(entity)
                CarryPropEmote(entity)
            end
        },
    }

    -- add target for models:
    _API.Target.AddModel(models, options)
end)

---Removes an emote object and returns item to player
---@param entity number the object entity handle
function RemovePropEmote(entity)
    local netId = NetworkGetNetworkIdFromEntity(entity)

    -- request control of netId
    NetworkRequestControlOfNetworkId(netId)
    while not NetworkHasControlOfNetworkId(netId) do
        NetworkRequestControlOfNetworkId(netId)
        Wait(1)
    end

    local modelHash = GetEntityModel(entity)
    local emote = Config.Emotes.Props[tostring(modelHash)]

    TriggerServerEvent("t1ger_mechanic:server:deletePropEmoteObject", netId, emote)
end

---Function to carry/push emote objects
---@param entity number object entity handle
function CarryPropEmote(entity)
    local modelHash = GetEntityModel(entity)
    local emote = Config.Emotes.Props[tostring(modelHash)]

    -- request control of entity
    NetworkRequestControlOfEntity(entity)
    while not NetworkHasControlOfEntity(entity) do
        NetworkRequestControlOfEntity(entity)
        Wait(1)
    end

    -- check if already holding an object
    if holdingObject then
        return _API.ShowNotification(locale("notification.is_carrying_prop_emote"), "inform", {})
    end

    holdingObject = true
    carryModel = entity
    
    -- animation:
    if emote.push == nil then
        PropEmoteAnimation("pickup")
        Wait(250)
    else
        PropEmoteAnimation("push")
    end
    Wait(250)

    -- attach:
    AttachPropToPlayer(emote, entity)
    Wait(500)
    PropEmoteHandle(emote, entity)
end