if not Config.PushVehicle.Enable then 
    return 
end

local steer_angle = 0.0

---Returns whether player is allowed to push the vehicle
---@return boolean CanPushVehicle `true` if allowed. `false` otherwise
local function CanPushVehicle()
    if Config.PushVehicle.AllowEveryone then 
        return true 
    else
		-- check if isMechanic:
		local isMechanic, shopId = IsPlayerMechanic()
		if isMechanic then
            return true 
        end
    end
    return false
end

---Function to push the closest vehicle in direction
function PushVehicle()
    if not Config.PushVehicle.Enable then return end

	-- is allowed?
    if not CanPushVehicle() then return end

	-- ray cast:
	local hit, entityHit, endCoords, surfaceNormal, materialHash = lib.raycast.fromCoords(coords, GetOffsetFromEntityInWorldCoords(player, 0.0, 5.0, 0.0), 2)
	if not hit or GetEntityType(entityHit) ~= 2 or not IsEntityAVehicle(entityHit) or not DoesEntityExist(entityHit) then 
		return _API.ShowNotification(locale("notification.no_vehicle_in_direction"), "inform", {})
	end

	local pushing, front, newDist = true, nil, 0

	local anim = {dict = 'missfinale_c2ig_11', name = 'pushcar_offcliff_m', blendIn = 3.0, blendOut = 3.0, flag = 35}

	-- thread
	while pushing do
		Wait(1)
		
		-- vehicle dimensions
		local min, max = GetModelDimensions(GetEntityModel(entityHit))

		-- rear distance
		local rearCoords = GetOffsetFromEntityInWorldCoords(entityHit, 0.0, min.y - 0.25, 0.0)
		local rearDist = #(coords - vector3(rearCoords.x, rearCoords.y, rearCoords.z))

		-- front distance
		local frontCoords = GetOffsetFromEntityInWorldCoords(entityHit, 0.0, max.y + 0.25, 0.0)
		local frontDist = #(coords - vector3(frontCoords.x, frontCoords.y, frontCoords.z))

		-- distance from player to vehicle
		local vehicleCoords = GetEntityCoords(entityHit)
		local distance = #(coords - vector3(vehicleCoords.x, vehicleCoords.y, vehicleCoords.z))

		if distance < Config.PushVehicle.DrawDist then
			
			-- determine front or rear
			if rearDist < Config.PushVehicle.DrawDist then
				Draw3DText(rearCoords.x, rearCoords.y, rearCoords.z + 0.4, locale("drawtext.push_vehicle"))
				Draw3DText(rearCoords.x, rearCoords.y, rearCoords.z + 0.30, locale("drawtext.stop_push"))
				front = false
				newDist = rearDist
			elseif frontDist < Config.PushVehicle.DrawDist then
				Draw3DText(frontCoords.x, frontCoords.y, frontCoords.z + 0.4, locale("drawtext.push_vehicle"))
				Draw3DText(frontCoords.x, frontCoords.y, frontCoords.z + 0.30, locale("drawtext.stop_push"))
				front = true
				newDist = frontDist
			end

			-- stop pushing
			if IsControlJustPressed(0, Config.PushVehicle.StopKey) then 
				steer_angle = 0.0 
				pushing = false
			end

			-- if pushing
			if IsControlPressed(0, Config.PushVehicle.PushKey) and newDist <= 1.2 then 
				-- animation / attach
				lib.requestAnimDict(anim.dict)
				local boneIndex = GetPedBoneIndex(6286)
				if front then    
					AttachEntityToEntity(player, entityHit, boneIndex, 0.0, (max.y + 0.25), (min.z + 1.0), 0.0, 0.0, 180.0, false, false, false, true, false, true)
				else
					AttachEntityToEntity(player, entityHit, boneIndex, 0.0, (min.y - 0.25), (min.z + 1.0), 0.0, 0.0, 0.0, false, false, false, true, false, true)
				end
				TaskPlayAnim(player, anim.dict, anim.name, anim.blendIn, anim.blendOut, -1, anim.flag, 1.0, 0, 0, 0)
				Wait(300)
				
				while true do
					Wait(1)
					
					-- textui
					local isOpen, text = lib.isTextUIOpen()
					if not isOpen or text ~= locale("textui.push_vehicle") then
						lib.showTextUI(locale("textui.push_vehicle"))
					end

					-- forward speed
					if front then
						SetVehicleForwardSpeed(entityHit, -0.80)
					else
						SetVehicleForwardSpeed(entityHit, 0.80)
					end

					-- correction
					if HasEntityCollidedWithAnything(entityHit) then
						SetVehicleOnGroundProperly(entityHit)
					end

					-- set steering angle:
					local vehicleSpeed = GetFrameTime() * 75
					if IsDisabledControlPressed(0, Config.PushVehicle.LeftKey) then
						SetVehicleSteeringAngle(entityHit, steer_angle)
						steer_angle = steer_angle + vehicleSpeed
					elseif IsDisabledControlPressed(0, Config.PushVehicle.RightKey) then
						SetVehicleSteeringAngle(entityHit, steer_angle)
						steer_angle = steer_angle - vehicleSpeed
					else
						SetVehicleSteeringAngle(entityHit, steer_angle)
						if steer_angle < -0.7 then
							steer_angle = steer_angle + vehicleSpeed
						elseif steer_angle > 0.7 then
							steer_angle = steer_angle - vehicleSpeed
						else
							steer_angle = 0.0
						end
					end

					-- max steering angles:
					if steer_angle > 25.0 then
						steer_angle = 25.0
					elseif steer_angle < -25.0 then
						steer_angle = -25.0
					end

					-- cancel if stop pushing
					if not IsDisabledControlPressed(0, Config.PushVehicle.PushKey) then
						local isOpen, text = lib.isTextUIOpen()
						if text == locale("textui.push_vehicle") then
							lib.hideTextUI()
						end
						StopAnimTask(player, anim.dict, anim.name, 2.0)
						DetachEntity(player, false, false)
						break
					end

				end	
			end

		end

	end
end

--- Command to push vehicle
if Config.PushVehicle.Command.enable == true then
    RegisterCommand(Config.PushVehicle.Command.name, function()
        PushVehicle()
    end, false)
end