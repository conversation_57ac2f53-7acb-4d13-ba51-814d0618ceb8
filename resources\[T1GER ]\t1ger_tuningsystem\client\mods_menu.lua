SetupModStations = function()
    local function CreateBlip(pos, cfg)
        local blip = AddBlipForCoord(pos.x, pos.y, pos.z)
        SetBlipSprite(blip, cfg.sprite)
        SetBlipDisplay(blip, cfg.display)
        SetBlipScale(blip, cfg.scale)
        SetBlipColour(blip, cfg.color)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName("STRING")
        AddTextComponentString(cfg.name)
        EndTextCommandSetBlipName(blip)
        return blip
    end

    local function CanAccess(jobs)
        for _,jobName in pairs(jobs) do 
            if Core.GetJob().name == jobName then
                return true 
            end
        end
        return false
    end

    local function CanInteract(point)
        if IsPedInAnyVehicle(player, false) then
            if CanAccess(point.jobs) then 
                return true 
            end
        end
        return false
    end
    

    for stationId, station in ipairs(Config.ModStations.Locations) do
        mod_stations[stationId] = {}

        if station.blip == true then
            if CanAccess(station.jobs) then
                mod_stations[stationId].blip = CreateBlip(station.pos, Config.ModStations.Blip)
            end
        end

        mod_stations[stationId].point = lib.points.new({
            coords = station.pos,
            jobs = station.jobs,
            free = station.free,
            mods = station.mods,
            marker = Config.ModStations.Marker,
            distance = Config.ModStations.Interact.drawDist,
            interact = Config.ModStations.Interact,
            textUi = false,

            onEnter = function(point)
                if Config.Debug then 
                    print("Mod Station: "..stationId.." | Player Job: "..Core.GetJob().name)
                end
                lib.hideTextUI()
                point.textUi = false
            end,
    
            onExit = function(point)
                lib.hideTextUI()
                point.textUi = false
                usingMenu = false
                TriggerServerEvent('tuningsystem:server:resetModsMenu')
            end,
    
            nearby = function(point)
                if CanInteract(point) then
                    DrawMarker(point.marker.type, point.coords.x, point.coords.y, point.coords.z, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, point.marker.scale[1], point.marker.scale[2], point.marker.scale[3], point.marker.rgba[1], point.marker.rgba[2], point.marker.rgba[3], point.marker.rgba[4], false, true, 0, true, false, false, false)
        
                    if point.currentDistance < point.interact.dist then
        
                        if not point.textUi then
                            lib.hideTextUI()
                            lib.showTextUI(point.interact.textUi, {position = point.interact.position, icon = point.interact.icon, style = point.interact.style})
                            point.textUi = true
                        end
        
                        if IsControlJustReleased(0, point.interact.keybind) then 
                            lib.hideTextUI()
                            usingMenu = true
                            if mods_menu.inUse == nil then
                                OpenModsMainMenu(mod_stations[stationId].point)
                            else
                                usingMenu = false
                            end
                        end
                    end
                end
            end,
        })
    end
end

IsCategoryAllowedInStation = function(category, list)
    for k,v in pairs(list) do
        if category == v then 
            return true 
        end
    end
    return false
end

GetModsInCategory = function(vehicle, props, category)
    local options = {}

    for modName, mod in pairs(Config.Mods) do
        if mod.category ~= nil and mod.category == category then
            if DoesModExist(vehicle, modName, props) then
                local modLabel = GetVehicleModSlotName(vehicle, modName)
                local ignoreMod = false
                for k,weaponMod in pairs(Config.DisableMods) do
                    if modLabel == weaponMod then
                        ignoreMod = true
                        break
                    end
                end
                if not ignoreMod then
                    options[#options + 1] = {
                        label = modLabel,
                        args = {title = modLabel, category = mod.category, modName = modName, modType = mod.modType, modPrice = mod.price},
                        icon = mod.icon or nil,
                    }
                end
            end
        end
    end

    if next(options) then
        table.sort(options, function(a, b)
            return a.label < b.label
        end)
    end

    return options
end

IsCategoryAllowedInShop = function(category, shopId)
    for k,v in pairs(Config.Shops[shopId].categories) do
        if category == v then 
            return true 
        end
    end
    return false
end

DoesModCategoryExist = function(vehicle, category, props)
    for modName, mod in pairs(Config.Mods) do
        if mod.category == category then
            if DoesModExist(vehicle, modName, props) then
                return true
            end
        end
    end
    return false
end

GetItemNameFromModName = function(modName)
    local itemName = Config.Mods[modName].item
    if itemName == nil then
        if modName == 'customXenon' then 
            itemName = 'mod_light'
        elseif modName == 'modBackWheels' then 
            itemName = 'mod_rim'
        end
    end
    return itemName
end

CalculateModPrice = function(modName, index, vehiclePrice, colorCategory)
    
    local GetPriceFromModName = function(modName)
        if Config.Mods[modName].price ~= nil then 
            return Config.Mods[modName].price
        elseif Config.Mods[modName].variants ~= nil then 
            return Config.Mods[modName].variants
        end
    end

    local GetVehicleTierMultiplier = function(vehiclePrice)
        for _, tier in ipairs(Config.PriceTiers) do
            local vehPrice = vehiclePrice ~= nil and vehiclePrice or Config.DefaultVehiclePrice
            if vehPrice <= tier.upperLimit then
                return tier.multiplier
            end
        end
    end

    local GetEscalatedPrice = function(basePrice, index)
        if Config.PriceEscalationPercent <= 0 then 
            return basePrice 
        end
    
        local escalationMultiplier = ((Config.PriceEscalationPercent/100) + 1)
    
        if index == -1 then
            return basePrice
        elseif index >= 0 then 
            return (basePrice * escalationMultiplier ^ (index + 1))
        end
    
        return escalatedPrice
    end

    local GetItemPriceFromModName = function(modName)
        if Config.Mods[modName] ~= nil then
            local itemName = GetItemNameFromModName(modName)
            local itemInfo = GetItemInfo(itemName)
            return itemInfo.price or 0
        end
        return 0
    end

    local prices = {}
    local basePrice = GetPriceFromModName(modName)

    if basePrice == nil then 
        return 0, ('error, base price for %s is not found'):format(modName)
    end

    if colorCategory ~= nil then
        for i = 1, #Config.ColorCategories do
            if Config.ColorCategories[i].name == colorCategory then
                if Config.ColorCategories[i].price ~= nil and type(Config.ColorCategories[i].price) == 'number' and Config.ColorCategories[i].price > 0 then
                    basePrice = basePrice + Config.ColorCategories[i].price
                end
                break
            end
        end
    end

    local escalatedPrice = 0
    if type(basePrice) == 'number' then
        escalatedPrice = GetEscalatedPrice(basePrice, index)
    elseif type(basePrice) == 'table' then
        local modInfo, key = basePrice, tostring(index)
        if modInfo[tostring(index)] then 
            if modInfo[key].price then
                escalatedPrice = modInfo[key].price
            elseif modInfo[key].values  then
                escalatedPrice = {}
                for i = 1, 2, 1 do
                    escalatedPrice[i] = modInfo[key].values[i].price
                end
            end
        end
    end

    local tieredPrice = 0
    if Config.TieredPricing == true then 
        tieredPrice = (vehiclePrice * GetVehicleTierMultiplier(vehiclePrice))
    end
    
    prices.base = escalatedPrice
    prices.tiered = tieredPrice

    local itemPrice = 0
    if Config.PartAcquisitionCost == true then
        itemPrice = GetItemPriceFromModName(modName) or 0
    end

    prices.item = itemPrice
    
    local calculatedPrice = 0
    if type(escalatedPrice) == 'table' then
        calculatedPrice = {}
        for i = 1, #escalatedPrice do
            calculatedPrice[i] = escalatedPrice[i] + tieredPrice + itemPrice
        end
    elseif type(escalatedPrice) == 'number' then
        calculatedPrice = escalatedPrice + tieredPrice + itemPrice
    end

    -- Incentivize prices in mods menu markers
    if Config.ModsMenu.IncentivizedPricing.enable == true then
        local percent = Config.ModsMenu.IncentivizedPricing.percent
        local incentivizedPrice = 0
        if type(calculatedPrice) == 'table' then
            incentivizedPrice = {}
            for i = 1, #calculatedPrice do
                incentivizedPrice[i] = calculatedPrice[i] * (percent/100)
                calculatedPrice[i] = calculatedPrice[i] + incentivizedPrice[i]
            end
        elseif type(calculatedPrice) == 'number' then
            incentivizedPrice = calculatedPrice * (percent/100)
            calculatedPrice = calculatedPrice + incentivizedPrice
        end
        prices.incentivized = incentivizedPrice
    else
        prices.incentivized = 0
    end

    prices.calculated = calculatedPrice

    return calculatedPrice, prices
end

GetModVariantOptions = function(vehicle, props, modName, modType, vehiclePrice, rgb, paintType)
    local options = {}

    local modVariants = nil

    if modName == 'wheelColor' or modName == 'pearlescentColor' or modName == 'dashboardColor' or modName == 'interiorColor' or modName == 'color1' or modName == 'color2' then
        modVariants = GetVehicleColorVariants(vehicle, modName)
    else
        modVariants = GetVehicleModVariants(vehicle, modName, modType)
    end

    if modVariants ~= nil and next(modVariants) ~= nil then 

        if modName == 'extras' then
            return GetExtrasOptions(vehicle, modName, props, modVariants, vehiclePrice)
        elseif modName == 'xenonColor' or modName == 'customXenon' then 
            return GetXenonColorOptions(vehicle, modName, props, modVariants, vehiclePrice, rgb)
        elseif modName == 'neonEnabled' then
            return GetNeonKitOptions(vehicle, modName, props, modVariants, vehiclePrice)
        elseif modName == 'neonColor' then
            return GetNeonColorOptions(vehicle, modName, props, modVariants, vehiclePrice, rgb)
        elseif modName == 'modFrontWheels' or modName == 'modBackWheels' then 
            return GetWheelOptions(vehicle, modName, props, modVariants, vehiclePrice)
        elseif modName == 'tyreSmokeColor' then 
            return GetTyreSmokeOptions(vehicle, modName, props, modVariants, vehiclePrice, rgb)
        elseif modName == 'dashboardColor' or modName == 'interiorColor' or modName == 'wheelColor' or modName == 'pearlescentColor' or modName == 'color1' or modName == 'color2' then
            return GetColorOptions(vehicle, modName, props, modVariants, vehiclePrice, rgb, paintType)
        end

        for k,v in ipairs(modVariants) do
            local modPrice, priceBreakdown = CalculateModPrice(modName, v.modValue, vehiclePrice)
            
            local menuLabel = '['..k..'] '..v.modLabel..' - '..GetFormattedPrice(modPrice)
            if (props[modName] == v.modValue) or (type(props[modName]) == 'boolean' and Lib.BooleanToNumber(props[modName]) == v.modValue) or json.encode(props[modName]) == json.encode(v.modValue) then
                menuLabel = '['..k..'] '..v.modLabel..' - '..Lang['title_mod_installed']
            end

            local installData, installId = GetInstallationDataFromModName(modName)
            if installData ~= nil then
                if mods_menu.originalProps[modName] ~= nil and (mods_menu.originalProps[modName] == v.modValue) or (type(mods_menu.originalProps[modName]) == 'boolean' and Lib.BooleanToNumber(mods_menu.originalProps[modName]) == v.modValue) or (json.encode(mods_menu.originalProps[modName]) == json.encode(v.modValue)) then
                    menuLabel = '['..k..'] '..v.modLabel
                end
            end
    
            local invoice = {
                modPrice = modPrice,
                breakdown = priceBreakdown,
                modLabel = GetVehicleModSlotName(vehicle, modName),
                variantLabel = '['..k..'] '..v.modLabel,
                modName = modName
            }
    
            options[#options + 1] = {
                label = menuLabel,
                args = {labels = '['..k..'] '..v.modLabel, menuLabel = menuLabel, modName = v.modName, modType = v.modType, modPrice = modPrice, modValue = v.modValue, invoice = invoice},
            }
        end

    end

    return options
end

GetExtrasOptions = function(vehicle, modName, props, modVariants, vehiclePrice)
    local options = {}
    
    for k,v in ipairs(modVariants) do 
        local isExtraTurnedOn = IsVehicleExtraTurnedOn(vehicle, v.modValue)
        local toggleValue = isExtraTurnedOn and 0 or 1

        local title, modPrice, modLabel, modInvoice = {}, {}, {}, {}
        for i, extra in pairs(Config.Mods[v.modName].variants) do
            local num = (tonumber(i) + 1)
            local price, priceBreakdown = CalculateModPrice(v.modName, i, vehiclePrice)
            local menuLabel = '['..num..'] '..extra.label..' - '..GetFormattedPrice(price)

            if extra.index == toggleValue then
                menuLabel = '['..num..'] '..extra.label..' - '..Lang['title_mod_installed']
            end

            title[num] = menuLabel
            modPrice[num] = price
            modLabel[num] = '['..num..'] '..extra.label
            modInvoice[num] = {
                modPrice = price,
                breakdown = priceBreakdown,
                modLabel = v.modLabel..' ['..v.modValue..']',
                variantLabel = '['..num..'] '..extra.label,
                modName = v.modName
            }
        end

        options[#options + 1] = {
            label = v.modLabel..': '..v.modValue,
            values = title,
            defaultIndex = isExtraTurnedOn and 2 or 1,
            args = {labels = modLabel, menuLabel = title, modName = v.modName, modType = v.modType, modPrice = modPrice, modValue = v.modValue, invoice = modInvoice},
        }

    end

    return options
end

GetNeonKitOptions = function(vehicle, modName, props, modVariants, vehiclePrice)
    local options = {}

    -- neonEnabled | checked/toggle options:
    for k,v in ipairs(modVariants) do
        local isNeonToggled = IsVehicleNeonLightEnabled(vehicle, tonumber(v.modValue))
        local toggleValue = isNeonToggled and 1 or 0

        options[#options + 1] = {
            label = '['..(#options + 1)..'] '..v.modLabel,
            checked = toggleValue == 1 and true or false,
            args = {modName = v.modName, modType = v.modType, modValue = v.modValue}
        }
    end

    -- neonEnabled | confirm neon settings
    local cfg = Config.Mods['neonEnabled']
    local modPrice, priceBreakdown = CalculateModPrice('neonEnabled', -1, vehiclePrice)
    local neonkit = {IsVehicleNeonLightEnabled(vehicle, 0), IsVehicleNeonLightEnabled(vehicle, 1), IsVehicleNeonLightEnabled(vehicle, 2), IsVehicleNeonLightEnabled(vehicle, 3)}
    
    local invoice = {
        modPrice = modPrice,
        breakdown = priceBreakdown,
        modLabel = cfg.label,
        variantLabel = json.encode(neonkit),
        modName = 'neonEnabled'
    }

    options[#options + 1] = {
        label = Lang['title_install']..' '..cfg.label..' - '..GetFormattedPrice(modPrice),
        args = {modName = 'neonEnabled', modType = cfg.modType, modValue = neonkit, modPrice = modPrice, invoice = invoice}
    }

    return options
end

GetRGBOption = function(modName, vehiclePrice, rgb, paintType)
    local modPrice, priceBreakdown = CalculateModPrice(modName, -1, vehiclePrice)
    local invoice = {
        modPrice = modPrice,
        breakdown = priceBreakdown,
        modLabel = Config.Mods[modName].label,
        variantLabel = 'RGB',
        modName = modName
    }
    local values = {
        [1] = Lang['options_value_pick_color_tool'],
        [2] = Lang['title_install']..' - '..GetFormattedPrice(modPrice)
    }
    if rgb == nil then
        values[2] = Lang['options_value_pick_color_first']
    else
        invoice.variantLabel = invoice.variantLabel..('(%s,%s,%s)'):format(math.ceil(rgb.x), math.ceil(rgb.y), math.ceil(rgb.z))
        if modName == 'color1' or modName == 'color2' then
            if paintType ~= nil then
                invoice.variantLabel = paintType.label..' - '..invoice.variantLabel
            end
        end
    end

    local args = {modName = modName, modType = Config.Mods[modName].modType, modPrice = modPrice, invoice = invoice, openDialogue = true, modValue = rgb}

    if modName == 'color1' or modName == 'color2' then
        args.paintJob = true
    end

    return {
        label = Config.Mods[modName].label.. ' - RGB',
        values = values,
        args = args,
        description = Lang['options_desc_rgb_color'],
        defaultIndex = 1
    }
end

GetScrollableOption = function(modName, modVariants, props, vehiclePrice)
    local title, modPrice, modLabel, modInvoice, modValue, defaultIndex = {}, {}, {}, {}, {}, 1
    for i = 1, #modVariants do
        local price, priceBreakdown = CalculateModPrice(modVariants[i].modName, -1, vehiclePrice)
        modPrice[i] = price

        local menuLabel = '['..i..'] '..modVariants[i].modLabel..' - '..GetFormattedPrice(price)
        if modVariants[i].modName == 'xenonColor' then
            if (props[modVariants[i].modName] == tonumber(modVariants[i].modValue)) then
                if props['customXenon'] == nil or next(props['customXenon']) == nil then
                    menuLabel = '['..i..'] '..modVariants[i].modLabel..' - '..Lang['title_mod_installed']
                    defaultIndex = i
                end
            end
            modValue[i] = modVariants[i].modValue
        elseif modVariants[i].modName == 'neonColor' then
            if json.encode(props['neonColor']) == json.encode(Config.Mods[modName].variants[tostring(modVariants[i].modValue)].rgb) then
                menuLabel = '['..i..'] '..modVariants[i].modLabel..' - '..Lang['title_mod_installed']
                defaultIndex = i
            end
            modValue[i] = Config.Mods[modName].variants[tostring(modVariants[i].modValue)].rgb
        end

        modInvoice[i] = {
            modPrice = price,
            breakdown = priceBreakdown,
            modLabel = Config.Mods[modName].label,
            variantLabel = '['..i..'] '..modVariants[i].modLabel,
            modName = modName
        }

        title[i] = menuLabel
        modLabel[i] = '['..i..'] '..modVariants[i].modLabel
    end

    return {
        label = Config.Mods[modName].label,
        values = title,
        defaultIndex = defaultIndex,
        args = {labels = modLabel, menuLabel = title, modName = modName, modType = Config.Mods[modName].modType, modPrice = modPrice, modValue = modValue, invoice = modInvoice},
    }
end

GetXenonColorOptions = function(vehicle, modName, props, modVariants, vehiclePrice, rgb)
    local options = {}

    -- xenonColor | pre-defined color options:
    options[#options + 1] = GetScrollableOption(modName, modVariants, props, vehiclePrice)

    -- customXenon | RGB option:
    options[#options + 1] = GetRGBOption('customXenon', vehiclePrice, rgb)

    return options
end

GetNeonColorOptions = function(vehicle, modName, props, modVariants, vehiclePrice, rgb)
    local options = {}

    -- neonColor | pre-defined color options:
    options[#options + 1] = GetScrollableOption(modName, modVariants, props, vehiclePrice)

    -- neonColor | RGB option:
    options[#options + 1] = GetRGBOption(modName, vehiclePrice, rgb)

    return options
end

GetWheelOptions = function(vehicle, modName, props, modVariants, vehiclePrice)
    local options = {}

    local IsWheelTypeAllowed = function(vehicle, wheelType)
        local class = GetVehicleClass(vehicle)

        if class == 13 then -- disable wheels for cycles
            return false 
        end
        if class == 8 and wheelType ~= 6 or wheelType == 6 and class ~= 8 then -- bike wheels only for bikes
            return false
        end
        if class == 22 and wheelType ~= 10 then -- open wheel racing vehicles
            return false
        end

        return true
    end

    local currentWheelType = GetVehicleWheelType(vehicle)
    local currentWheel, currentWheel2, currentIndex, currentIndex2 = GetVehicleMod(vehicle, Config.Mods['modFrontWheels'].modType), GetVehicleMod(vehicle, Config.Mods['modBackWheels'].modType), 1, 1

    for i = 1, #modVariants do
        if IsWheelTypeAllowed(vehicle, modVariants[i].modValue) then 
            SetVehicleWheelType(vehicle, modVariants[i].modValue)

            local title, modPrice, modLabel, modInvoice, modValue, title2 = {}, {}, {}, {}, {}, {}
            local price, priceBreakdown = CalculateModPrice(modName, modVariants[i].modValue, vehiclePrice)
            local modCount = GetNumVehicleMods(vehicle, modVariants[i].modType)

            for wheel = 0, modCount, 1 do
                local num = (wheel + 1)
                local index = tonumber(wheel - 1)
                modPrice[num] = price
                modLabel[num] = GetVehicleModVariantLabel(vehicle, modName, index)
                modValue[num] = index
                title[num] = '['..num..'] '..modLabel[num]..' - '..GetFormattedPrice(price)
                title2[num] = '['..num..'] '..modLabel[num]..' - '..GetFormattedPrice(price)

                if currentWheel == index then
                    currentIndex = num
                    title[num] = '['..num..'] '..modLabel[num]..' - '..Lang['title_mod_installed']
                end
                if currentWheel2 ~= nil and currentWheel2 == index then 
                    currentIndex2 = num
                    title2[num] = '['..num..'] '..modLabel[num]..' - '..Lang['title_mod_installed']
                end

                modInvoice[num] = {
                    modPrice = price,
                    breakdown = priceBreakdown,
                    modLabel = Config.Mods[modName].label..' - '..'['..i..'] '..modVariants[i].modLabel,
                    variantLabel = '['..num..'] '..modLabel[num],
                    modName = modName
                }
            end

            options[#options + 1] = {
                label = '['..i..'] '..modVariants[i].modLabel,
                values = title,
                defaultIndex = currentWheelType == modVariants[i].modValue and currentIndex or 1,
                args = {menuIndex = #options + 1, labels = modLabel, menuLabel = title, modName = modName, modType = Config.Mods[modName].modType, modPrice = modPrice, wheelType = modVariants[i].modValue, modValue = modValue, invoice = modInvoice}
            }

            if GetVehicleClass(vehicle) == 8 then
                local cfg = Config.Mods['modBackWheels']
                options[#options + 1] = {
                    label = '['..i..'] '..cfg.label,
                    values = title2,
                    defaultIndex = currentWheelType == modVariants[i].modValue and currentIndex2 or 1,
                    args = {menuIndex = #options + 1, labels = modLabel, menuLabel = title2, modName = 'modBackWheels', modType = cfg.modType, modPrice = modPrice, wheelType = modVariants[i].modValue, modValue = modValue, invoice = modInvoice, rear = true} 
                }
            end
        end
    end

    SetVehicleWheelType(vehicle, currentWheelType)

    return options
end

GetTyreSmokeOptions = function(vehicle, modName, props, modVariants, vehiclePrice, rgb)
    local options = {}
    
    local isTyreSmokeEnabled = IsToggleModOn(vehicle, 20)
    if not isTyreSmokeEnabled then
        ToggleVehicleMod(vehicle, 20, true)
        isTyreSmokeEnabled = true
    end
    local currentTyreSmokeColor = {GetVehicleTyreSmokeColor(vehicle)}

    -- tyreSmokeColor | current color:
    options[#options + 1] = {
        label = Lang['options_label_cur_tyre_smoke']:format(math.floor(currentTyreSmokeColor[1]), math.floor(currentTyreSmokeColor[2]), math.floor(currentTyreSmokeColor[3])),
        args = {modName = modName, modValue = true, menuIndex = 1, modPrice = 0},
        description = Lang['options_desc_tyre_smoke_preview'],
    }

    -- tyreSmokeColor | predefined colors:
    local title, modPrice, modLabel, modInvoice, modValue, defaultIndex = {}, {}, {}, {}, {}, 1
    for i = 1, #modVariants do
        local price, priceBreakdown = CalculateModPrice(modVariants[i].modName, modVariants[i].modValue, vehiclePrice)
        modPrice[i] = price
        modValue[i] = Config.Mods[modVariants[i].modName].variants[tostring(modVariants[i].modValue)].rgb
        modLabel[i] = '['..i..'] '..modVariants[i].modLabel

        title[i] = '['..i..'] '..modVariants[i].modLabel..' - '..GetFormattedPrice(price)
        if json.encode(modValue[i]) == json.encode(currentTyreSmokeColor) then 
            defaultIndex = i
            title[i] = '['..i..'] '..modVariants[i].modLabel..' - '..Lang['title_mod_installed']
        end

        modInvoice[i] = {
            modPrice = price,
            breakdown = priceBreakdown,
            modLabel = Config.Mods[modName].label,
            variantLabel = '['..i..'] '..modVariants[i].modLabel,
            modName = modName
        }
    end
    options[#options + 1] = {
        label = Config.Mods[modName].label,
        values = title,
        defaultIndex = defaultIndex,
        args = {menuIndex = #options + 1, labels = modLabel, menuLabel = title, modName = modName, modType = modName, modPrice = modPrice, modValue = modValue, invoice = modInvoice}
    }

    -- tyreSmokeColor | RGB option:
    if isTyreSmokeEnabled then
        options[#options + 1] = GetRGBOption('tyreSmokeColor', vehiclePrice, rgb)
    end

    return options
end

GetColorOptions = function(vehicle, modName, props, modVariants, vehiclePrice, rgb, paintType)
    local options = {}
    
    local currentColor = modName == 'dashboardColor' and GetVehicleDashboardColor(vehicle) or modName == 'interiorColor' and GetVehicleInteriorColour(vehicle) or nil
    if modName == 'pearlescentColor' or modName == 'wheelColor' then 
        local curPealescent, curWheelColor = GetVehicleExtraColours(vehicle)
        currentColor = modName == 'pearlescentColor' and curPealescent or modName == 'wheelColor' and curWheelColor
    elseif modName == 'color1' or modName == 'color2' then 
        local primaryColor, secondaryColor = GetVehicleColours(vehicle)
        currentColor = modName == 'color1' and primaryColor or modName == 'color2' and secondaryColor
    end

    for k,v in ipairs(modVariants) do

        if (modName == 'dashboardColor' or modName == 'interiorColor') and v.modValue == 'chameleon' then 
            goto skipLoop
        end

        local title, modPrice, modLabel, modInvoice, modValue, modPaintType, defaultIndex = {}, {}, {}, {}, {}, {}, 1
        for i = 1, #Config.Colors[v.modValue] do
            local price, priceBreakdown = CalculateModPrice(v.modName, -1, vehiclePrice, v.modValue)

            modPrice[i] = price
            modValue[i] = Config.Colors[v.modValue][i].index
            modLabel[i] = '['..i..'] '..Config.Colors[v.modValue][i].label

            if modName == 'color1' or modName == 'color2' then 
                modPaintType[i] = v.modPaintType
            end

            title[i] = '['..i..'] '..Config.Colors[v.modValue][i].label..' - '..GetFormattedPrice(price)
            local match = false
            if Config.Colors[v.modValue][i].index == currentColor then 
                defaultIndex = i
                title[i] = '['..i..'] '..Config.Colors[v.modValue][i].label..' - '..Lang['title_mod_installed']
            end

            modInvoice[i] = {
                modPrice = price,
                breakdown = priceBreakdown,
                modLabel = Config.Mods[modName].label..' - '..'['..k..'] '..v.modLabel,
                variantLabel = '['..i..'] '..Config.Colors[v.modValue][i].label,
                modName = modName
            }
        end

        options[#options + 1] = {
            label = '['..k..'] '..v.modLabel,
            values = title,
            defaultIndex = defaultIndex,
            args = {menuIndex = #options + 1, labels = modLabel, menuLabel = title, modName = v.modName, modType = v.modType, modPrice = modPrice, colorCategory = v.modValue, modValue = modValue, invoice = modInvoice, paintType = next(modPaintType) and modPaintType or nil}
        }

        ::skipLoop::
    end

    -- color1 / color2 | RGB Color Option
    if modName == 'color1' or modName == 'color2' then 
        options[#options + 1] = GetRGBOption(modName, vehiclePrice, rgb, paintType)
    end

    return options
end

GetModsMainMenuOptions = function(vehicle, point)
    local options = {}

    -- Get Repair Button:
    local engineHealth = GetVehicleEngineHealth(vehicle)
    local bodyHealth = GetVehicleBodyHealth(vehicle)
    if engineHealth < 1000.0 or bodyHealth < 1000.0 then
        if Config.ModsMenu.Repair.allow == true then
            options = {
                {
                    label = Lang['title_repair_no'],
                    args = {repair = true, point = point}
                },
                {
                    label = Lang['title_repair_yes']:format(GetFormattedPrice(Config.ModsMenu.Repair.price)),
                    description = Lang['options_desc_repair_menu']:format(Lib.RoundNumber(engineHealth,2), Lib.RoundNumber(bodyHealth,2)),
                    args = {
                        repair = true,
                        price = Config.ModsMenu.Repair.price,
                        toAccount = Config.ModsMenu.Repair.moneyToAccount,
                        point = point
                    }
                }
            }
        end
    else
        local props = {['bodyHealth'] = 1000.0, ['engineHealth'] = 1000.0}
        SetVehicleFixed(vehicle)
        Core.SetVehicleProperties(vehicle, props)
        -- Browse Mods:
        options[#options + 1] = {label = Lang['options_label_browse_mods'], args = {point = point, browse = true}}

        if mods_menu.installations ~= nil and next(mods_menu.installations) ~= nil then 
            -- Confirm Mods:
            if point.free ~= nil then
                if point.free == true then
                    options[#options + 1] = {label = Lang['options_label_apply_mods'], args = {point = point, free = true, payment = true}}
                else
                    options[#options + 1] = {label = Lang['options_label_purchase_mods'], args = {point = point, payment = true, markupToAccount = Config.ModOrder.MarkupToAccount}}
                end
            else
                if Config.ModsMenu.DisablePurchase == false then
                    options[#options + 1] = {label = Lang['options_label_purchase_mods'], args = {point = point, payment = true, markupToAccount = Config.ModOrder.MarkupToAccount}}
                end
                -- Mod Order:
                if point.shopId ~= nil then 
                    options[#options + 1] = {label = Lang['options_label_create_mod_order'], args = {point = point, mod_order_create = true, markupToAccount = Config.ModOrder.MarkupToAccount}}
                end
            end
        elseif point.shopId ~= nil and Config.Shops[point.shopId].orders ~= nil then
            local plate = GetVehicleNumberPlateText(vehicle)
            if Config.Shops[point.shopId].orders[plate] ~= nil then
                options[#options + 1] = {label = Lang['options_label_delete_mod_order'], args = {point = point, mod_order_delete = true, plate = plate}}
            end
        end
    end

    return options
end

OpenModsMainMenu = function(point)
    local vehicle = GetVehiclePedIsIn(player, false)

    if vehicle == nil or vehicle == 0 then
        usingMenu = false
        point.textUi = false
        return Core.Notification({
            title = '',
            message = Lang['must_be_inside_veh'],
            type = 'inform'
        })
    end

    local plate = GetVehicleNumberPlateText(vehicle)
    if point.shopId ~= nil and Config.Shops[point.shopId].orders[plate] ~= nil and Config.Shops[point.shopId].orders[plate].taken == true then
        return Core.Notification({
            title = '',
            message = Lang['mod_order_in_progress'],
            type = 'inform'
        })
    end
    
    local mainOptions = GetModsMainMenuOptions(vehicle, point)
    if mainOptions == nil or next(mainOptions) == nil then
        usingMenu = false
        point.textUi = false
        return Core.Notification({
            title = '',
            message = Lang['veh_must_be_repaired_to_mod'],
            type = 'inform'
        })
    end

    mods_menu.inUse = true
    mods_menu.shopId = point.shopId or nil
    mods_menu.vehicle = vehicle
    mods_menu.props = Core.GetVehicleProperties(mods_menu.vehicle)
    mods_menu.price = Config.DefaultVehiclePrice
    mods_menu.originalProps = mods_menu.props
    mods_menu.installations = {}
    mods_menu.freePayment = point.free or nil

    TriggerServerEvent('tuningsystem:server:usingModsMenu', VehToNet(mods_menu.vehicle), mods_menu.originalProps)

    FreezeEntityPosition(mods_menu.vehicle, true)

    Core.GetVehiclePrice(GetEntityModel(mods_menu.vehicle), function(modelValue)
        mods_menu.price = modelValue > 0 and modelValue or Config.DefaultVehiclePrice
    end)

    -- Main Menu:
    lib.registerMenu({
        id = 'mods_menu_main_menu',
        title = Config.ModsMenu.Menu.title,
        position = Config.ModsMenu.Menu.position,
        onClose = function(keyPressed)
            Core.SetVehicleProperties(mods_menu.vehicle, mods_menu.originalProps)
            mods_menu.inUse = false
            usingMenu = false
            point.textUi = false
            TriggerServerEvent('tuningsystem:server:resetModsMenu')
            FreezeEntityPosition(mods_menu.vehicle, false)
            mods_menu = {}
        end,
        onSelected = function(selected, secondary, args)
            PlaySoundFrontend(-1, Config.MenuSoundFrontend.AudioName, Config.MenuSoundFrontend.AudioRef, true)
            mods_menu['main'] = {id = 'mods_menu_main_menu', index = selected}
        end,
        options = mainOptions
    }, function(selected, scrollIndex, args)
        if args.repair ~= nil then
            if selected == 2 then
                local done = false
                Core.TriggerCallback('t1ger_lib:server:payMoney', function(paid)
                    if paid then
                        if args.point.shopId ~= nil and args.toAccount then 
                            TriggerServerEvent('tuningsystem:server:addAccountMoney', args.point.shopId, args.price)
                        end
                        local props = {['bodyHealth'] = 1000.0, ['engineHealth'] = 1000.0}
                        SetVehicleFixed(mods_menu.vehicle)
                        Core.SetVehicleProperties(mods_menu.vehicle, props)
                        mods_menu.originalProps['bodyHealth'] = 1000.0
                        mods_menu.originalProps['engineHealth'] = 1000.0
                        RepairSound()
                        Core.Notification({
                            title = '',
                            message = Lang['paid_repair_cost']:format(GetFormattedPrice(args.price)),
                            type = 'inform'
                        })
                    else
                        return Core.Notification({
                            title = '',
                            message = Lang['not_enough_money'],
                            type = 'inform'
                        })
                    end
                    done = true
                end, args.price, 'bank')
                while not done do 
                    Wait(5)
                end
                -- refresh menus:
                local newOptions = GetModsMainMenuOptions(mods_menu.vehicle, args.point)
                lib.setMenuOptions('mods_menu_main_menu', newOptions)
                lib.showMenu('mods_menu_main_menu', 1)
                
            else
                mods_menu.inUse = false
                usingMenu = false
                point.textUi = false
                FreezeEntityPosition(mods_menu.vehicle, false)
                TriggerServerEvent('tuningsystem:server:resetModsMenu')
                mods_menu = {}
                return Core.Notification({
                    title = '',
                    message = Lang['veh_must_be_repaired_to_mod'],
                    type = 'inform'
                })
            end
        else
            if args.browse ~= nil then 
                OpenModsMenuCategories(args.point)
            elseif args.payment ~= nil then
                if mods_menu.installations ~= nil and next(mods_menu.installations) ~= nil then 
                    local totalAmount, alertContent = 0, ''
                    for modName,invoice in pairs(mods_menu.installations) do
                        totalAmount = totalAmount + invoice.modPrice
                        if args.point.free ~= nil and args.point.free == true then
                            alertContent = alertContent..'  \n\n **'..invoice.modLabel..'** ('..invoice.variantLabel..')'
                        else
                            alertContent = alertContent..'  \n\n **'..invoice.modLabel..'** ('..invoice.variantLabel..') - **'..GetFormattedPrice(math.floor(invoice.modPrice))..'**'
                        end
                    end

                    local markupAmount = 0
                    if args.point.shopId ~= nil then 
                        local markupAmount = 0
                        if Config.Markup.Enable == true then
                            markupAmount = (totalAmount * (Config.Shops[args.point.shopId].markup/100))
                            totalAmount = totalAmount + markupAmount
                        end
                    end

                    local alertHeader = Lang['alert_header_mods_checkout']:format(GetFormattedPrice(math.floor(totalAmount)))
                    if args.point.free ~= nil and args.point.free == true then
                        alertHeader = Lang['alert_header_mods_apply']
                    end

                    local alert = lib.alertDialog({
                        header = alertHeader,
                        content = alertContent,
                        centered = true,
                        cancel = true
                    })

                    if alert == 'confirm' then
                        local done = false
                        if args.point.free ~= nil and args.point.free == true then
                            totalAmount = 0
                        end
                        Core.TriggerCallback('t1ger_lib:server:payMoney', function(paid)
                            if paid then
                                mods_menu.originalProps = mods_menu.props
                                Wait(100)
                                TriggerServerEvent('t1ger_lib:server:saveVehicleProperties', GetVehicleNumberPlateText(mods_menu.vehicle), Core.GetVehicleProperties(mods_menu.vehicle))
                                if Config.Markup.Enable == true and args.point.shopId ~= nil and args.markupToAccount ~= nil and args.markupToAccount == true then
                                    TriggerServerEvent('tuningsystem:server:addAccountMoney', args.point.shopId, markupAmount)
                                end
                                mods_menu.inUse = false
                                usingMenu = false
                                point.textUi = false
                                TriggerServerEvent('tuningsystem:server:resetModsMenu')
                                FreezeEntityPosition(mods_menu.vehicle, false)
                                mods_menu = {}
                                PlayUpgradeSound()
                                if totalAmount <= 0 then
                                    Core.Notification({
                                        title = '',
                                        message = Lang['vehicle_mods_applied'],
                                        type = 'success'
                                    })
                                else
                                    Core.Notification({
                                        title = '',
                                        message = Lang['vehicle_mods_paid']:format(GetFormattedPrice(math.floor(totalAmount))),
                                        type = 'success'
                                    })
                                end
                            else
                                Core.Notification({
                                    title = '',
                                    message = Lang['vehicle_mods_not_enough_money'],
                                    type = 'error'
                                })
                                -- refresh menus:
                                local newOptions = GetModsMainMenuOptions(mods_menu.vehicle, args.point)
                                lib.setMenuOptions('mods_menu_main_menu', newOptions)
                                lib.showMenu('mods_menu_main_menu', 2)
                            end
                            done = true
                        end, totalAmount, 'bank')
                        while not done do 
                            Wait(5)
                        end
                    else
                        -- refresh menus:
                        local newOptions = GetModsMainMenuOptions(mods_menu.vehicle, args.point)
                        lib.setMenuOptions('mods_menu_main_menu', newOptions)
                        lib.showMenu('mods_menu_main_menu', 2)
                    end
                end
            elseif args.mod_order_create ~= nil then 
                if mods_menu.installations ~= nil and next(mods_menu.installations) ~= nil then
                    
                    local plate = GetVehicleNumberPlateText(mods_menu.vehicle)

                    if Config.Shops[args.point.shopId].orders[plate] == nil then 
                        local totalAmount, partAcquisition, laborCharge, markupAmount, alertContent, taskList = 0, 0, 0, 0, '', {}
                        for k,v in pairs(mods_menu.installations) do
                            local taskId = #taskList + 1
                            local discountedPrice = (v.modPrice - v.breakdown.incentivized)
                            totalAmount = totalAmount + discountedPrice
                            v.discountedPrice = discountedPrice
                            partAcquisition = partAcquisition + v.breakdown.item
                            laborCharge = laborCharge + (discountedPrice - v.breakdown.item)
                            alertContent = alertContent..'  \n **'..v.modLabel..'** ('..v.variantLabel..') - **'..GetFormattedPrice(math.floor(discountedPrice))..'**'
                            v.completed = false
                            local itemInfo = GetItemInfo(GetItemNameFromModName(v.modName))
                            local props = Core.GetVehicleProperties(mods_menu.vehicle)
                            taskList[taskId] = {
                                id = taskId,
                                name = Config.ModOrder.TaskList.name:format(v.modLabel, v.variantLabel),
                                description = Config.ModOrder.TaskList.description:format(itemInfo.label),
                                item = itemInfo,
                                modName = v.modName,
                                modValue = v.modValue,
                                scrollIndex = v.scrollIndex,
                                variantLabel = v.variantLabel,
                                modLabel = v.modLabel,
                                modPrice = v.modPrice,
                                invoice = v.breakdown,
                                price = discountedPrice,
                                completed = false,
                            }
                        end
                        
                        local alertHeader = Lang['alert_header_submit_mod_order']:format(GetFormattedPrice(math.floor(totalAmount)))

                        -- Markup:
                        if Config.Markup.Enable == true then
                            markupAmount = (totalAmount * (Config.Shops[args.point.shopId].markup/100))
                            totalAmount = totalAmount + markupAmount
                            alertHeader = Lang['alert_header_submit_mod_order2']:format(
                                GetFormattedPrice(math.floor(totalAmount)),
                                GetFormattedPrice(math.floor(partAcquisition)),
                                GetFormattedPrice(math.floor(laborCharge)),
                                Config.Shops[args.point.shopId].markup,
                                GetFormattedPrice(math.floor(markupAmount))
                            )
                        end

                        local alert = lib.alertDialog({
                            header = alertHeader,
                            content = alertContent,
                            centered = true,
                            cancel = true
                        })

                        if alert == 'confirm' then
                            local done = false
                            Core.TriggerCallback('t1ger_lib:server:payMoney', function(paid)
                                if paid then
                                    if Config.Markup.Enable == true and args.point.shopId ~= nil and args.markupToAccount ~= nil and args.markupToAccount == true then
                                        TriggerServerEvent('tuningsystem:server:addAccountMoney', args.point.shopId, markupAmount)
                                    end
                                    if Config.ModOrder.PartAcquisitionToAccount == true then 
                                        TriggerServerEvent('tuningsystem:server:addAccountMoney', args.point.shopId, partAcquisition)
                                    end
                                    if Config.ModOrder.LaborChargeToAccount == true then 
                                        TriggerServerEvent('tuningsystem:server:addAccountMoney', args.point.shopId, laborCharge)
                                    end
                                    Core.Notification({
                                        title = '',
                                        message = Lang['mod_order_has_been_placed']:format(GetFormattedPrice(math.floor(totalAmount))),
                                        type = 'success'
                                    })
                                    local props = Core.GetVehicleProperties(mods_menu.vehicle)
                                    TriggerServerEvent('tuningsystem:server:addModOrder', args.point.shopId, plate, taskList, props, totalAmount, partAcquisition, laborCharge, markupAmount, Config.Shops[args.point.shopId].markup)
                                    Core.SetVehicleProperties(mods_menu.vehicle, mods_menu.originalProps)
                                    mods_menu.inUse = false
                                    usingMenu = false
                                    point.textUi = false
                                    TriggerServerEvent('tuningsystem:server:resetModsMenu')
                                    FreezeEntityPosition(mods_menu.vehicle, false)
                                    mods_menu = {}
                                else
                                    Core.Notification({
                                        title = '',
                                        message = Lang['vehicle_mods_not_enough_money'],
                                        type = 'error'
                                    })
                                    -- refresh menus:
                                    local newOptions = GetModsMainMenuOptions(mods_menu.vehicle, args.point)
                                    lib.setMenuOptions('mods_menu_main_menu', newOptions)
                                    lib.showMenu('mods_menu_main_menu', 2)
                                end
                                done = true
                            end, totalAmount, 'bank')
                            while not done do 
                                Wait(5)
                            end
                        else
                            -- refresh menus:
                            local newOptions = GetModsMainMenuOptions(mods_menu.vehicle, args.point)
                            lib.setMenuOptions('mods_menu_main_menu', newOptions)
                            return lib.showMenu('mods_menu_main_menu', 1)  
                        end

                    else
                        Core.Notification({
                            title = '',
                            message = Lang['cannot_submit_mod_order'],
                            type = 'inform'
                        })
                        -- refresh menus:
                        local newOptions = GetModsMainMenuOptions(mods_menu.vehicle, args.point)
                        lib.setMenuOptions('mods_menu_main_menu', newOptions)
                        lib.showMenu('mods_menu_main_menu', 1)
                    end
                end
            elseif args.mod_order_delete ~= nil then
                local plate = GetVehicleNumberPlateText(mods_menu.vehicle)
                local totalAmount = 0
                if Config.Shops[args.point.shopId].orders[plate] ~= nil then
                    totalAmount, alertContent = Config.Shops[args.point.shopId].orders[plate].paidAmount, ''
                    for taskId,taskData in pairs(Config.Shops[args.point.shopId].orders[plate].taskList) do
                        alertContent = alertContent..'  \n\n **'..taskData.modLabel..'** ('..taskData.variantLabel..') - **'..GetFormattedPrice(math.floor(taskData.price))..'**'
                    end
                end

                local alert = lib.alertDialog({
                    header = Lang['alert_header_delete_mod_order']:format(GetFormattedPrice(math.floor(totalAmount))),
                    content = alertContent,
                    centered = true,
                    cancel = true
                })

                if alert == 'confirm' then
                    Core.Notification({
                        title = '',
                        message = Lang['mod_order_has_been_cancelled']:format(GetFormattedPrice(math.floor(totalAmount))),
                        type = 'success'
                    })
                    TriggerServerEvent('tuningsystem:server:cancelModOrder', args.point.shopId, plate)
                    Core.SetVehicleProperties(mods_menu.vehicle, mods_menu.originalProps)
                    mods_menu.inUse = false
                    usingMenu = false
                    point.textUi = false
                    TriggerServerEvent('tuningsystem:server:resetModsMenu')
                    FreezeEntityPosition(mods_menu.vehicle, false)
                    mods_menu = {}
                else
                    -- refresh menus:
                    local newOptions = GetModsMainMenuOptions(mods_menu.vehicle, args.point)
                    lib.setMenuOptions('mods_menu_main_menu', newOptions)
                    lib.showMenu('mods_menu_main_menu', 1)  
                end
            end
        end
    end)

    lib.showMenu('mods_menu_main_menu', 1)
end

OpenModsMenuCategories = function(point)
    local categories = {}
    for k,v in pairs(Config.ModCategories) do
        if point.shopId ~= nil and IsCategoryAllowedInShop(k, point.shopId) or (point.mods ~= nil and IsCategoryAllowedInStation(k, point.mods)) then
            if DoesModCategoryExist(mods_menu.vehicle, k, mods_menu.props) then 
                table.insert(categories, {
                    label = v.title,
                    args = {id = v.id, category = k},
                    icon = v.icon,
                })
            end
        end
    end

    table.sort(categories, function(a, b)
        return a.label < b.label
    end)

    if next(categories) == nil then 
        return Core.Notification({
            title = '',
            message = Lang['no_categories_to_show'],
            type = 'inform'
        })
    end

    -- Categories Menu:
    lib.registerMenu({
        id = 'mods_menu_categories',
        title = 'Mod Categories',
        position = Config.ModsMenu.Menu.position,
        onClose = function(keyPressed)
            -- refresh menus:
            local newOptions = GetModsMainMenuOptions(mods_menu.vehicle, point)
            lib.setMenuOptions('mods_menu_main_menu', newOptions)
            lib.showMenu('mods_menu_main_menu', 1)
        end,
        onSelected = function(selected, secondary, args)
            PlaySoundFrontend(-1, Config.MenuSoundFrontend.AudioName, Config.MenuSoundFrontend.AudioRef, true)
            mods_menu['categories'] = {id = 'mods_menu_categories', index = selected}
        end,
        options = categories
    }, function(selected, scrollIndex, args)
        OpenSelectedModCategory(args)
    end)

    lib.showMenu('mods_menu_categories', 1)
end

OpenSelectedModCategory = function(data)
    local menuOptions = GetModsInCategory(mods_menu.vehicle, mods_menu.props, data.category)
    
    if next(menuOptions) == nil then
        Core.Notification({
            title = '',
            message = Lang['vehicle_no_mod_category']:format(data.category),
            type = 'inform'
        })
        return lib.showMenu(mods_menu['categories'].id, mods_menu['categories'].index)
    end

    lib.registerMenu({
        id = Config.ModCategories[data.category].id,
        title = Config.ModCategories[data.category].title,
        position = Config.ModCategories[data.category].position,
        onClose = function(keyPressed)
            lib.showMenu(mods_menu['categories'].id, mods_menu['categories'].index)
        end,
        onSelected = function(selected, secondary, args)
            PlaySoundFrontend(-1, Config.MenuSoundFrontend.AudioName, Config.MenuSoundFrontend.AudioRef, true)
            mods_menu[data.category] = {id = Config.ModCategories[data.category].id, index = selected}
        end,
        options = menuOptions
    }, function(selected, scrollIndex, args)
        OpenSelectedModVariants(args)
    end)

    lib.showMenu(Config.ModCategories[data.category].id, 1)
end

OpenSelectedModVariants = function(data)
    SetVehicleModKit(mods_menu.vehicle, 0)
    local menuOptions = GetModVariantOptions(mods_menu.vehicle, mods_menu.props, data.modName, data.modType, mods_menu.price, mods_menu.rgb, mods_menu.paintType)

    if next(menuOptions) == nil then
        Core.Notification({
            title = '',
            message = Lang['no_variants_for_mod'],
            type = 'inform'
        })
        return lib.showMenu(mods_menu[data.category].id, mods_menu[data.category].index)
    end

    browsingMods = true

    ModPreviewOpenDoors(menuOptions, mods_menu.vehicle, data.modName)

    if data.modName == 'tyreSmokeColor' then
        if mods_menu.props['modSmokeEnabled'] == false or mods_menu.props['modSmokeEnabled'] == 0 then
            ToggleVehicleMod(mods_menu.vehicle, 20, true)
            mods_menu.props['modSmokeEnabled'] = true
        end
        FreezeEntityPosition(mods_menu.vehicle, false)
        SetVehicleBurnout(mods_menu.vehicle, true)
    elseif data.modName == 'modXenon' or data.modName == 'xenonColor' or data.modName == 'customXenon' or data.modName == 'neonEnabled' or data.modName == 'neonColor' then
        if not GetIsVehicleEngineRunning(vehicle) then 
            SetVehicleEngineOn(mods_menu.vehicle, true, true, false)
        end
    end

    local newMenu = mods_menu[data.category].id..'_'..data.modName
    lib.registerMenu({
        id = newMenu,
        title = data.title,
        position = Config.ModCategories[data.category].position,
        onClose = function(keyPressed)
            -- refresh menus:
            local newOptions = GetModsInCategory(mods_menu.vehicle, mods_menu.props, data.category)
            lib.setMenuOptions(mods_menu[data.category].id, newOptions)
            lib.showMenu(mods_menu[data.category].id, mods_menu[data.category].index)
            -- reset vehicle & mods:
            Core.SetVehicleProperties(mods_menu.vehicle, mods_menu.props)
            FreezeEntityPosition(mods_menu.vehicle, true)
            SetVehicleBurnout(mods_menu.vehicle, false)
            SetVehicleDoorsShut(mods_menu.vehicle, true)
            browsingMods = false
            mods_menu.rgb = nil
            mods_menu.paintType = nil
        end,
        onSelected = function(selected, secondary, args)
            PlaySoundFrontend(-1, Config.MenuSoundFrontend.AudioName, Config.MenuSoundFrontend.AudioRef, true)
            mods_menu[data.modName] = {id = newMenu, index = selected}

            if args.modName == 'xenonColor' or args.modName == 'customXenon' or args.modName == 'neonEnabled' or args.modName == 'neonColor' or args.modName == 'wheelColor' or args.modName == 'tyreSmokeColor' or args.modName == 'color1' or args.modName == 'color2' or args.modName == 'pearlescentColor' or args.modName == 'dashboardColor' or args.modName == 'interiorColor' or args.modName == 'extras' then
                return
            end

            local props = {[args.modName] = args.modValue}
            if args.modName == 'modFrontWheels' or args.modName == 'modBackWheels' then
                props = {['wheels'] = args.wheelType, [args.modName] = args.modValue[secondary]}
            end

            Core.SetVehicleProperties(mods_menu.vehicle, props)

            if args.modName == 'modHorns' then 
                StartVehicleHorn(mods_menu.vehicle, 2000, 'HELDDOWN', false)
            end
        end,
        onSideScroll = function(selected, scrollIndex, args)
            PlaySoundFrontend(-1, Config.MenuSoundFrontend.AudioName, Config.MenuSoundFrontend.AudioRef, true)
            if args.modName == 'xenonColor' or args.modName == 'wheelColor' or args.modName == 'modFrontWheels' or args.modName == 'modBackWheels' or args.modName == 'pearlescentColor' or args.modName == 'dashboardColor' or args.modName == 'interiorColor' or ((args.modName == 'color1' or args.modName == 'color2') and args.openDialogue == nil) then
                local props = {[args.modName] = args.modValue[scrollIndex]}
                if args.modName == 'color1' then 
                    props['paintType1'] = args.paintType[scrollIndex]
                    props['color2'] = mods_menu.props['color2']
                elseif args.modName == 'color2' then
                    props['paintType2'] = args.paintType[scrollIndex]
                    props['color1'] = mods_menu.props['color1']
                    props['paintType1'] = mods_menu.props['paintType1']
                end
                Core.SetVehicleProperties(mods_menu.vehicle, props)
            elseif args.modName == 'extras' then 
                local toggle = scrollIndex == 2 and 0 or scrollIndex == 1 and 1
                local props = {[args.modName] = {[tostring(args.modValue)] = toggle}}
                Core.SetVehicleProperties(mods_menu.vehicle, props)
            elseif (args.modName == 'neonColor' or args.modName == 'tyreSmokeColor') and args.openDialogue == nil then
                local props = {[args.modName] = {math.floor(args.modValue[scrollIndex][1]), math.floor(args.modValue[scrollIndex][2]), math.floor(args.modValue[scrollIndex][3])}}
                Core.SetVehicleProperties(mods_menu.vehicle, props)
            end
        end,
        onCheck = function(selected, checked, args)
            PlaySoundFrontend(-1, Config.MenuSoundFrontend.AudioName, Config.MenuSoundFrontend.AudioRef, true)
            if args.modName == 'neonEnabled' then
                local neons = {}
                for i = 0, 3 do
                    if i == args.modValue then
                        neons[tostring(i)] = checked
                    else
                        neons[tostring(i)] = IsVehicleNeonLightEnabled(mods_menu.vehicle, i)
                    end
                end
                local props = {[args.modName] = neons}
                Core.SetVehicleProperties(mods_menu.vehicle, props)
                mods_menu.neons = neons
            end
        end,
        options = menuOptions
    }, function(selected, scrollIndex, args)
        if Config.Debug then
            print("install mod | ", selected, scrollIndex, args)
        end
        
        local newOptions, installMod = {}, true
        local alreadyInstalled = false

        -- ## CHECK IF ALREADY INSTALLED ## --
        if args.menuLabel ~= nil and type(args.menuLabel) == 'string' and string.match(args.menuLabel, Lang['title_mod_installed']) then 
            alreadyInstalled = true
            Core.Notification({
                title = '',
                message = Lang['mod_variant_already_installed'],
                type = 'inform'
            })
        end

        if args.modName == 'neonEnabled' then
            if mods_menu.neons == nil or next(mods_menu.neons) == nil then 
                alreadyInstalled = true
                Core.Notification({
                    title = '',
                    message = Lang['mod_variant_already_installed'],
                    type = 'inform'
                })
            else
                local match = true
                local curNeon, propNeon = mods_menu.neons, mods_menu.props[args.modName]
                for i = 0, 3 do
                    local neon_1 = type(propNeon[tostring(i)]) == 'boolean' and Lib.BooleanToNumber(propNeon[tostring(i)]) or propNeon[tostring(i)]
                    local neon_2 = type(curNeon[tostring(i)]) == 'boolean' and Lib.BooleanToNumber(curNeon[tostring(i)]) or curNeon[tostring(i)]
                    if neon_1 ~= neon_2 then 
                        match = false 
                        break
                    end
                end
                if match then 
                    alreadyInstalled = true
                    Core.Notification({
                        title = '',
                        message = Lang['mod_variant_already_installed'],
                        type = 'inform'
                    })
                end
            end
        end

        if alreadyInstalled then
            local newOptions = GetModVariantOptions(mods_menu.vehicle, mods_menu.props, data.modName, data.modType, mods_menu.price, mods_menu.rgb, mods_menu.paintType)
            lib.setMenuOptions(mods_menu[data.modName].id, newOptions)
            return lib.showMenu(mods_menu[data.modName].id, mods_menu[data.modName].index)
        end

        -- ## RGB COLOR PICKER ## --

        if args.openDialogue ~= nil and args.openDialogue == true then 
            if scrollIndex == 1 then 
                local inputOptions = {
                    {type = 'color', label = Lang['input_label_rgb_color'], default = 'rgb(255, 255, 255)', format = 'rgb', required = true}
                }

                if args.paintJob ~= nil and args.paintJob == true then
                    inputOptions[#inputOptions + 1] = {type = 'select', label = 'Paint Job', required = true, default = 0, options = Config.PaintTypes}
                end

                local input = lib.inputDialog(Lang['input_title_rgb_color'], inputOptions)

                if input == nil then
                    Core.Notification({
                        title = '',
                        message = Lang['input_required'],
                        type = 'inform'
                    })
                else
                    local rgb = lib.math.tovector(input[1], 0, 255, true)
                    local props = {[args.modName] = {rgb.x, rgb.y, rgb.z}}
    
                    if args.modName == 'color1' then
                        props['paintType1'] = input[2] ~= nil and input[2] or 0
                        props['color2'] = mods_menu.props['color2']
                    elseif args.modName == 'color2' then
                        props['paintType2'] = input[2] ~= nil and input[2] or 0
                        props['color1'] = mods_menu.props['color1']
                        props['paintType1'] = mods_menu.props['paintType1']
                    end

                    local paintType = input[2] ~= nil and input[2] or 0
                    local paintTypeName = nil

                    for k,v in pairs(Config.PaintTypes) do
                        if paintType == v.value then 
                            paintTypeName = v.label
                            break
                        end
                    end
                    
                    mods_menu.rgb = rgb
                    mods_menu.paintType = {value = input[2] ~= nil and input[2] or 0, label = paintTypeName}
                    Core.SetVehicleProperties(mods_menu.vehicle, props)
                end

                installMod = false

            elseif scrollIndex == 2 and (mods_menu.rgb == nil or args.modValue == nil) then
                installMod = false
            end
        end

        if args.modName == 'tyreSmokeColor' then 
            if selected == 1 then
                installMod = false
            end
        end

        if installMod == true then
            -- get current vehicle mods:
            local vehicleProps = Core.GetVehicleProperties(mods_menu.vehicle)
            -- update props:
            Core.SetVehicleProperties(mods_menu.vehicle, vehicleProps)
            mods_menu.props = vehicleProps
            PlayUpgradeSound()
            -- store in mods_menu payments:
            local cache = {modName = args.modName, modType = args.modType, modLabel = args.labels or args.modLabel, modPrice = args.modPrice}
            if scrollIndex ~= nil then 
                cache.modLabel = args.labels ~= nil and args.labels[scrollIndex] or args.modLabel
                cache.modPrice = (args.modPrice ~= nil and type(args.modPrice) == 'table' and next(args.modPrice)) and args.modPrice[scrollIndex] or args.modPrice
            end

            local installIndex = #mods_menu.installations+1
            local installData, installId = GetInstallationDataFromModName(args.modName)
            if installData ~= nil then
                if args.modName ~= 'extras' then
                    mods_menu.installations[installId] = nil
                else
                    if installData.modValue == args.modValue then
                        mods_menu.installations[installId] = nil
                    end
                end
            end

            mods_menu.installations[installIndex] = scrollIndex ~= nil and args.invoice[scrollIndex] or args.invoice
            mods_menu.installations[installIndex].scrollIndex = scrollIndex ~= nil and scrollIndex or nil
            mods_menu.installations[installIndex].modValue = (scrollIndex ~= nil and type(args.modValue) ~= 'number' and args.modValue[scrollIndex] ~= nil and args.modValue[scrollIndex]) or args.modValue
            mods_menu.installations[installIndex].modName = args.modName
            if args.modName == 'neonEnabled' then 
                local neonkit = {IsVehicleNeonLightEnabled(mods_menu.vehicle, 0), IsVehicleNeonLightEnabled(mods_menu.vehicle, 1), IsVehicleNeonLightEnabled(mods_menu.vehicle, 2), IsVehicleNeonLightEnabled(mods_menu.vehicle, 3)}
                mods_menu.installations[installIndex].variantLabel = json.encode(neonkit)
            end
            if json.encode(mods_menu.originalProps[args.modName]) == json.encode(mods_menu.props[args.modName]) then
                --mods_menu.installations[args.modName] = nil
                mods_menu.installations[installIndex] = nil
            end

            -- reset:
            mods_menu.rgb = nil
            mods_menu.paintType = nil
            FreezeEntityPosition(mods_menu.vehicle, true)
            SetVehicleBurnout(mods_menu.vehicle, false)
            SetVehicleDoorsShut(mods_menu.vehicle, true)
            -- return to menu:
            local newOptions = GetModsInCategory(mods_menu.vehicle, mods_menu.props, data.category)
            lib.setMenuOptions(mods_menu[data.category].id, newOptions)
            lib.showMenu(mods_menu[data.category].id, mods_menu[data.category].index)
        else
            local newOptions = GetModVariantOptions(mods_menu.vehicle, mods_menu.props, data.modName, data.modType, mods_menu.price, mods_menu.rgb, mods_menu.paintType)
            lib.setMenuOptions(mods_menu[data.modName].id, newOptions)
            lib.showMenu(mods_menu[data.modName].id, mods_menu[data.modName].index)
        end

    end)

    local defaultIndex = 1
    for k,v in ipairs(menuOptions) do
        if v.args.modName == 'modFrontWheels' or v.args.modName == 'modBackWheels' then
            if mods_menu.props['wheels'] == v.args.wheelType then
                if v.args.rear ~= nil then 
                    defaultIndex = 1
                else
                    defaultIndex = v.args.menuIndex or 1
                end
            end
        elseif v.args.modName == 'color1' or v.args.modName == 'color2' or v.args.modName == 'wheelColor' or v.args.modName == 'pearlescentColor' or v.args.modName == 'interiorColor' or v.args.modName == 'dashboardColor' then
            if Config.Colors[v.args.colorCategory] ~= nil then 
                for i = 1, #Config.Colors[v.args.colorCategory] do
                    if mods_menu.props[v.args.modName] == Config.Colors[v.args.colorCategory][i].index then
                        defaultIndex = v.args.menuIndex
                        break
                    end
                end
            end
        else
            if mods_menu.props[v.args.modName] == v.args.modValue or json.encode(mods_menu.props[v.args.modName]) == json.encode(v.args.modValue) then 
                defaultIndex = k
            end
        end
    end

    lib.showMenu(newMenu, defaultIndex)
end

GetInstallationDataFromModName = function(modName)
    if mods_menu.installations ~= nil and next(mods_menu.installations) then
        for k,v in pairs(mods_menu.installations) do
            if v.modName == modName then 
                return v, k
            end
        end
    end
    return nil
end

RegisterNetEvent('tuningsystem:client:resetVehicleMods')
AddEventHandler('tuningsystem:client:resetVehicleMods', function(netId, props)
    local entity = NetToVeh(netId)
    if DoesEntityExist(entity) then
        SetEntityAsMissionEntity(entity, true, true)
        Core.SetVehicleProperties(entity, props)
    end
end)