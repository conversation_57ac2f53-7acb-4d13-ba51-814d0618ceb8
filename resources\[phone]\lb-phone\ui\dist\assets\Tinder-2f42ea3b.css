:root{--phone-color-primary: rgb(255, 255, 255);--phone-color-opacity: rgba(242, 242, 242, .4);--phone-color-opacity2: rgb(30, 30, 30, .5);--phone-color-highlight: rgb(250, 250, 250);--phone-color-highlight2: rgb(240, 240, 240);--phone-color-highlight3: rgb(220, 220, 220);--phone-highlight-opacity15: rgba(145, 145, 145, .15);--phone-highlight-opacity35: rgba(145, 145, 145, .35);--phone-highlight-opacity45: rgba(145, 145, 145, .45);--phone-highlight-opacity55: rgba(145, 145, 145, .55);--phone-color-input: rgba(241, 241, 241, .656);--phone-text-primary: rgb(0, 0, 0);--phone-text-secondary: rgb(142, 142, 147);--phone-color-hover: rgb(240, 240, 240);--phone-color-border: rgba(200, 200, 200, .4);--phone-color-grey: #8e8e93;--phone-color-blue: #0a84ff;--phone-color-green: #32d74b;--phone-color-green-secondary: #092911;--phone-color-red: #ff3b30;--phone-color-orange: rgb(255, 157, 10);--phone-color-yellow: #cca250;--phone-color-pink: #ff3b30;--instagram-primary: #ffffff;--instagram-blue: rgb(0, 149, 246);--instagram-red: rgb(237, 73, 86);--instagram-primary-text: rgb(38, 38, 38);--instagram-secondary-text: rgb(142, 142, 142);--instagram-stroke: rgb(219, 219, 219);--instagram-border: rgb(219, 219, 219);--instagram-highlight: rgb(239, 239, 239);--tinder-color-pink: #ff4573;--tinder-color-orange: #ff5f65;--tinder-color-mix: #f5547c;--twitter-primary: #f5f8fa;--twitter-secondary: #14171a;--twitter-background-highlight: rgb(239, 243, 244);--twitter-primary-text: #14171a;--twitter-secondary-text: #657786;--twitter-alt-text: #657786;--twitter-border: #bdc5cd75;--twitter-border-secondary: #1d9bf0;--twitter-highlight: #1d9bf0;--twitter-hover: rgba(15, 20, 25, .1);--twitter-action: #14171a;--twitter-blue: #1d9bf0;--tiktok-primary: #ffffff;--tiktok-secondary: #000000;--tiktok-text-primary: #000000;--tiktok-text-secondary: #86878b;--tiktok-color-border: #d0d1d3;--tiktok-color-pink: #fe2c55;--tiktok-color-aqua: #00f2ea;--tiktok-color-yellow: #f8cd14;--tiktok-color-blue: #479fc5;--tiktok-color-unread: rgba(254, 44, 86, .2);--crypto-color-primary: rgb(255, 255, 255);--browser-primary: rgb(245, 245, 245);--browser-secondary: rgba(153, 153, 153, .15);--browser-gradient: linear-gradient(230deg, #f4d6ff, #c5f1ff);--browser-footer: rgba(255, 255, 255, .75);--browser-border: rgba(102, 102, 102, .75);--browser-text-secondary: #696969;--controlcentre-opacity: rgba(255, 255, 255, .15);--controlcentre-opacity2: rgba(255, 255, 255, .2);--controlcentre-active: rgba(255, 255, 255, .5);--notification-primary: rgba(215, 215, 215, .5);--notification-secondary: rgba(215, 215, 215, .1);--lockscreeneditor-background: rgba(255, 255, 255, .75);--lockscreeneditor-secondary: #d9d9d9;--app-bg: #ececec;--app-bg2: #ffffff;--app-secondary: #ffffff;--app-secondary2: #ececec;--app-highlight: #cccccc;--app-highlight2: #999999;--app-highlight3: #ffffff;--app-border: #666666;--app-slider: #cccccc;--app-slider-active: #333333;--app-button: #ffffff;--components-bg: #eeeeee;--components-secondary: #ffffff;--components-highlight: #cccccc}[data-theme=dark]{--phone-color-primary: #000000;--phone-color-opacity: rgb(30, 30, 30, .5);--phone-color-opacity2: rgba(242, 242, 242, .4);--phone-color-highlight: rgb(15, 15, 15);--phone-color-highlight2: rgb(20, 20, 20);--phone-color-highlight3: rgb(25, 25, 25);--phone-highlight-opacity35: rgba(145, 145, 145, .35);--phone-highlight-opacity45: rgba(50, 50, 50, .6);--phone-highlight-opacity55: rgb(60, 60, 60, .8);--phone-color-input: rgba(60, 60, 67, .6);--phone-text-primary: #f2f2f7;--phone-text-secondary: #6f6f6f;--phone-color-grey: #636366;--phone-color-hover: rgb(30, 30, 30);--phone-color-border: rgba(150, 150, 150, .2);--phone-color-blue: #076bcf;--instagram-primary: #000000;--instagram-blue: rgb(0, 149, 246);--instagram-red: rgb(237, 73, 86);--instagram-primary-text: rgb(250, 250, 250);--instagram-secondary-text: rgb(142, 142, 142);--instagram-stroke: rgb(219, 219, 219);--instagram-border: rgb(54, 54, 54);--instagram-highlight: rgb(38, 38, 38);--twitter-primary: #000000;--twitter-secondary: #f5f8fa;--twitter-background-highlight: rgb(20, 20, 20);--twitter-primary-text: #f5f8fa;--twitter-secondary-text: #aab8c2;--twitter-alt-text: #657786;--twitter-border: #38444d;--twitter-border-secondary: #38444d;--twitter-hover: rgba(150, 150, 150, .1);--twitter-highlight: #dcdcdc;--twitter-action: #1d9bf0;--twitter-blue: #1d9bf0;--crypto-color-primary: rgb(24, 26, 32);--tiktok-text-primary: #f2f2f7;--tiktok-text-secondary: #6f6f6f;--tiktok-color-border: #96969633;--browser-primary: rgb(15, 15, 15);--browser-secondary: rgba(153, 153, 153, .15);--browser-gradient: linear-gradient(230deg, #453b48, #2f393d);--browser-footer: rgba(51, 51, 51, .75);--browser-border: rgba(102, 102, 102, .75);--browser-text-secondary: #999999;--controlcentre-opacity: rgba(0, 0, 0, .15);--controlcentre-opacity2: rgba(0, 0, 0, .2);--controlcentre-active: rgba(0, 0, 0, .5);--notification-primary: rgba(0, 0, 0, .1);--notification-secondary: rgba(0, 0, 0, .12);--lockscreeneditor-background: rgba(0, 0, 0, .8);--lockscreeneditor-secondary: #333333;--app-bg: #000000;--app-bg2: #000000;--app-secondary: #141414;--app-secondary2: #141414;--app-highlight: #cccccc;--app-highlight2: #696969;--app-highlight3: #212121;--app-border: #cccccc;--app-slider: #999999;--app-slider-active: #ffffff;--app-button: #333333;--components-bg: #000000;--components-secondary: #141414;--components-highlight: #696969}@keyframes zoomIn{0%{transform:scale(.5)}to{transform:scale(1)}}@keyframes zoomOut{0%{transform:scale(1.5)}to{transform:scale(1)}}@keyframes slideDown{0%{transform:translateY(-20%)}to{transform:translateY(0)}}@keyframes slideUp{0%{transform:translateY(40%)}to{transform:translateY(0)}}@keyframes slideRight{0%{transform:translate(-10%)}to{transform:translate(0)}}@keyframes slideLeft{0%{transform:translate(10%)}to{transform:translate(0)}}@keyframes appJiggle{0%{transform:rotate(-1deg);animation-timing-function:ease-in}50%{transform:rotate(1.5deg);animation-timing-function:ease-out}}@keyframes appJiggle2{0%{transform:rotate(1deg);animation-timing-function:ease-in}50%{transform:rotate(-1.5deg);animation-timing-function:ease-out}}@keyframes widgetJiggle{0%{transform:rotate(-.5deg);animation-timing-function:ease-in}50%{transform:rotate(.5deg);animation-timing-function:ease-out}}@keyframes widgetJiggle2{0%{transform:rotate(.5deg);animation-timing-function:ease-in}50%{transform:rotate(-.5deg);animation-timing-function:ease-out}}.tinder-container{height:100%;width:100%;position:relative;display:flex;flex-direction:column;align-items:center;font-family:Inter,sans-serif;background-color:#fff}.tinder-container .slide.right{animation:slideRight .5s cubic-bezier(.19,1,.22,1)}.tinder-container .slide.left{animation:slideLeft .5s cubic-bezier(.19,1,.22,1)}.tinder-container .loading{height:100%;width:100%;display:flex;align-items:center;justify-content:center;background-color:#f1f1f1}.tinder-container .home-wrapper{height:100%;width:90%;padding:0 5%;display:flex;flex-direction:column;align-items:center}.tinder-container .home-wrapper .message-header{width:94%;margin-top:3rem;padding:1rem 0;display:flex;flex-direction:row;align-items:center;gap:1.5rem;border-bottom:1px solid var(--phone-color-border)}.tinder-container .home-wrapper .message-header svg{color:var(--tinder-color-mix);font-size:27px;cursor:pointer}.tinder-container .home-wrapper .message-header .user{display:flex;flex-direction:row;align-items:center;gap:.5rem;cursor:pointer}.tinder-container .home-wrapper .message-header .user .profile-picture{width:2.5rem;height:2.5rem;border-radius:50%;object-fit:cover}.tinder-container .home-wrapper .message-header .user .name{font-size:20px;font-weight:600}.tinder-container .home-wrapper .popup-profile-container{position:absolute;z-index:1;display:flex;flex-direction:column;height:100%;width:100%;background-color:var(--phone-color-primary)}.tinder-container .home-wrapper .popup-profile-container .gallery{position:relative;filter:drop-shadow(0px 5px 5px rgba(0,0,0,.15))}.tinder-container .home-wrapper .popup-profile-container .gallery img{width:100%;height:30rem;object-position:center;object-fit:cover;filter:brightness(.9)}.tinder-container .home-wrapper .popup-profile-container .gallery .image-count{position:absolute;z-index:1;bottom:.5rem;left:0;right:0;margin:0 auto;height:.5rem;width:95%;display:flex;align-items:center;gap:.25rem}.tinder-container .home-wrapper .popup-profile-container .gallery .image-count .image{width:100%;background-color:#dad9d9;opacity:.6;height:.2rem;border-radius:5px;cursor:pointer}.tinder-container .home-wrapper .popup-profile-container .gallery .image-count .image.active{opacity:1}.tinder-container .home-wrapper .popup-profile-container .gallery .close{position:absolute;top:3.5rem;right:1.5rem;z-index:2;height:2rem;width:2rem;border-radius:50%;display:flex;align-items:center;justify-content:center;background-color:#91919159;cursor:pointer}.tinder-container .home-wrapper .popup-profile-container .gallery .close svg{color:#fafafab9;font-size:20px}.tinder-container .home-wrapper .popup-profile-container .card-content{margin:1rem;display:flex;flex-direction:column;gap:.5rem;width:90%}.tinder-container .home-wrapper .popup-profile-container .card-content .name{font-size:30px;font-weight:600;color:var(--phone-text-primary)}.tinder-container .home-wrapper .popup-profile-container .card-content .name span{font-weight:400;font-size:20px;opacity:.8}.tinder-container .home-wrapper .popup-profile-container .card-content .divider{height:2px;width:100%;background-color:var(--phone-color-border);opacity:.5;margin:.5rem 0}.tinder-container .home-wrapper .popup-profile-container .card-content .description{display:flex;flex-direction:column;gap:.5rem;color:var(--phone-text-secondary);opacity:.9;font-size:20px;overflow-wrap:break-word}.tinder-container .home-wrapper .popup-profile-container .card-content .description .title{font-size:22px;font-weight:600;color:var(--phone-text-primary)}.tinder-container .home-wrapper .message-container{position:relative;display:flex;flex-direction:column-reverse;height:95%;max-height:95%;width:100%;overflow-y:auto;overflow-x:hidden;scroll-behavior:auto}.tinder-container .home-wrapper .message-container::-webkit-scrollbar{display:none}.tinder-container .home-wrapper .message-body{width:100%;display:flex;flex-direction:column;padding:1rem 0;gap:.5rem}.tinder-container .home-wrapper .message-body .message{font-size:16px;font-weight:300;width:auto;max-width:70%;color:#0f1419;display:flex;flex-direction:column;gap:.5rem}.tinder-container .home-wrapper .message-body .message .content a{color:var(--phone-color-blue);cursor:pointer}.tinder-container .home-wrapper .message-body .message .attatchments img,.tinder-container .home-wrapper .message-body .message .attatchments video{width:100%;border-radius:15px}.tinder-container .home-wrapper .message-body .message .profile-picture img{width:2.5rem;height:2.5rem;border-radius:50%;object-fit:cover;object-position:center}.tinder-container .home-wrapper .message-body .message .date{padding:0 .5rem;font-size:13px;font-weight:300;color:#687684}.tinder-container .home-wrapper .message-body .message.self{align-items:flex-end;margin-left:auto}.tinder-container .home-wrapper .message-body .message.self .date{text-align:right;margin-left:auto}.tinder-container .home-wrapper .message-body .message.self .content{padding:.75rem 1rem;border-radius:15px;text-align:right;background-color:#00b3ff;color:#fafafa;overflow-wrap:break-word;font-size:18px}.tinder-container .home-wrapper .message-body .message.other{margin-right:auto}.tinder-container .home-wrapper .message-body .message.other .message-with-pfp{display:flex;flex-direction:row;align-items:flex-end;gap:.6rem}.tinder-container .home-wrapper .message-body .message.other .message-content{display:flex;flex-direction:column;align-items:flex-start}.tinder-container .home-wrapper .message-body .message.other .message-content .content{padding:.75rem 1rem;border-radius:18px;background-color:#e5e5e5;color:#000;overflow-wrap:break-word}.tinder-container .home-wrapper .message-body .message.other .message-content .content:not(img)+.other{background-color:transparent;padding:0}.tinder-container .home-wrapper .message-body .message.other .message-content .date{margin-left:3rem;margin-right:auto}.tinder-container .home-wrapper .attachments{position:absolute;display:flex;flex-direction:row;align-items:center;gap:.5rem;bottom:5.5rem;left:2rem}.tinder-container .home-wrapper .attachments .attachment{position:relative;height:5rem;width:5rem}.tinder-container .home-wrapper .attachments .attachment img,.tinder-container .home-wrapper .attachments .attachment video{width:100%;height:100%;object-fit:cover;border-radius:10px}.tinder-container .home-wrapper .attachments .attachment svg{position:absolute;top:.25rem;right:.25rem;height:1.3rem;width:1.3rem;padding:.1rem;border-radius:50%;display:flex;align-items:center;justify-content:center;background-color:#000c;color:#fff;cursor:pointer;font-size:13px}.tinder-container .home-wrapper .message-bottom{width:95%;padding-bottom:2rem;display:flex;flex-direction:row;align-items:center;gap:.5rem}.tinder-container .home-wrapper .message-bottom .image{padding:.4rem .5rem;height:1.8rem;width:1.8rem;border-radius:50%;background-color:#00b3ff;color:#fff;cursor:pointer;transition:all .2s ease-in-out}.tinder-container .home-wrapper .message-bottom .image:hover{filter:brightness(.8)}.tinder-container .home-wrapper .message-bottom .input{display:flex;flex-direction:row;align-items:center;height:100%;border-radius:20px;padding:0 .5rem;background-color:#e5e5e5;width:100%}.tinder-container .home-wrapper .message-bottom .input input{border:none;background:transparent;width:88%;font-size:16px}.tinder-container .home-wrapper .message-bottom .input input:focus{outline:none}.tinder-container .home-wrapper .message-bottom .input svg{color:#838383;font-size:24px;cursor:pointer}.tinder-container .home-wrapper .message-bottom .input svg.active{color:#00b3ff}.tinder-container .home-wrapper .searchbox{margin-top:1rem}.tinder-container .home-wrapper .message-list-body{margin-top:1rem;width:100%;display:flex;flex-direction:column;gap:1.5rem;overflow-y:scroll;overflow-x:hidden;padding-bottom:2rem}.tinder-container .home-wrapper .message-list-body::-webkit-scrollbar{display:none}.tinder-container .home-wrapper .message-list-body .title{color:var(--tinder-color-mix);text-transform:uppercase;font-size:16px;font-weight:600;letter-spacing:.75px}.tinder-container .home-wrapper .message-list-body .title span{margin-left:.25rem;border-radius:15px;padding:.15rem .5rem;font-weight:400;color:#fff;background-color:var(--tinder-color-mix)}.tinder-container .home-wrapper .message-list-body .profile-picture{width:4.75rem;height:4.75rem;border-radius:50%;object-fit:cover}.tinder-container .home-wrapper .message-list-body .recent-messages,.tinder-container .home-wrapper .message-list-body .recent-messages .messages{display:flex;flex-direction:column;gap:1rem}.tinder-container .home-wrapper .message-list-body .recent-messages .messages .message{display:flex;flex-direction:row;align-items:center;gap:.75rem;cursor:pointer}.tinder-container .home-wrapper .message-list-body .recent-messages .messages .message .user{display:flex;flex-direction:column;gap:.2rem}.tinder-container .home-wrapper .message-list-body .recent-messages .messages .message .user .name{font-size:18px;font-weight:600}.tinder-container .home-wrapper .message-list-body .recent-messages .messages .message .user .message{color:#3b3b3b;font-size:16px}.tinder-container .home-wrapper .message-list-body .new-matches{display:flex;flex-direction:column;gap:1rem;margin-top:.5rem;padding-bottom:1rem;border-bottom:1px solid var(--phone-color-border)}.tinder-container .home-wrapper .message-list-body .new-matches .matches{display:flex;flex-direction:row;align-items:center;gap:.5rem;width:100%;overflow-x:auto;overflow-y:hidden;padding-bottom:1rem}.tinder-container .home-wrapper .message-list-body .new-matches .matches::-webkit-scrollbar{height:5px}.tinder-container .home-wrapper .message-list-body .new-matches .matches::-webkit-scrollbar-track{background:#f1f1f1}.tinder-container .home-wrapper .message-list-body .new-matches .matches::-webkit-scrollbar-thumb{background-color:var(--tinder-color-mix)}.tinder-container .home-wrapper .message-list-body .new-matches .matches::-webkit-scrollbar-thumb:hover{filter:brightness(.8)}.tinder-container .home-wrapper .message-list-body .new-matches .matches .new-match{display:flex;flex-direction:column;align-items:center;gap:.25rem;cursor:pointer;transition:all .2s ease-in-out}.tinder-container .home-wrapper .message-list-body .new-matches .matches .new-match:hover{filter:brightness(.8)}.tinder-container .home-wrapper .message-list-body .new-matches .matches .new-match .name{font-size:18px;font-weight:500}.tinder-container .home-wrapper .match{position:absolute;z-index:99;height:100%;width:100%;background-position:center;background-size:cover;background-repeat:no-repeat;display:flex;flex-direction:column;align-items:center;gap:1rem}.tinder-container .home-wrapper .match .match-image{margin-top:17rem;width:80%}.tinder-container .home-wrapper .match .likes-too{color:#fff;font-size:21px;font-style:italic}.tinder-container .home-wrapper .match .input{display:flex;flex-direction:row;align-items:center;width:75%;height:2.5rem;background-color:#fff;border-radius:10px;padding:.5rem 1rem}.tinder-container .home-wrapper .match .input input{background:transparent;border:none;height:100%;width:82%;font-size:17px;color:#000}.tinder-container .home-wrapper .match .input input:focus{outline:none}.tinder-container .home-wrapper .match .input .send{width:18%;text-align:right;color:var(--phone-color-blue);font-size:16px;font-weight:600;text-transform:uppercase;cursor:pointer}.tinder-container .home-wrapper .match .keep-swiping{margin-top:4rem;font-size:16px;color:#fff;font-weight:300;letter-spacing:1px;text-transform:uppercase;cursor:pointer}.tinder-container .home-wrapper .profile-body{display:flex;flex-direction:column;align-items:center;width:100%;overflow-y:auto;overflow-x:hidden;padding-bottom:5rem}.tinder-container .home-wrapper .profile-body::-webkit-scrollbar{display:none}.tinder-container .home-wrapper .profile-body .user{display:flex;flex-direction:column;align-items:center;gap:.75rem;margin-top:2rem}.tinder-container .home-wrapper .profile-body .user .profile-picture{width:10rem;height:10rem;border-radius:50%;object-fit:cover}.tinder-container .home-wrapper .profile-body .user .name{color:#000;font-size:24px;font-weight:600}.tinder-container .home-wrapper .profile-body .inputs{margin-top:2rem;display:flex;flex-direction:column;align-items:center;gap:1.25rem;width:100%}.tinder-container .home-wrapper .profile-body .inputs .input{display:flex;flex-direction:column;align-items:flex-start;gap:.3rem;width:100%}.tinder-container .home-wrapper .profile-body .inputs .input>*{box-sizing:border-box}.tinder-container .home-wrapper .profile-body .inputs .input.center{align-items:center}.tinder-container .home-wrapper .profile-body .inputs .input .label{width:100%;font-size:18px;color:#000}.tinder-container .home-wrapper .profile-body .inputs .input input{width:100%;border:none;height:3.5rem;padding:.5rem;font-family:Inter,sans-serif;color:#202020;font-size:18px;border-radius:10px}.tinder-container .home-wrapper .profile-body .inputs .input input:focus{outline:none}.tinder-container .home-wrapper .profile-body .inputs .input textarea{width:100%;border:none;height:5rem;padding:.5rem;font-family:Inter,sans-serif;color:#202020;font-size:18px;border-radius:10px;resize:none}.tinder-container .home-wrapper .profile-body .inputs .input textarea:focus{outline:none}.tinder-container .home-wrapper .profile-body .inputs .input select{width:100%;height:3rem;padding:.5rem;border-radius:10px;font-family:Inter,sans-serif;color:#202020;font-size:18px;border:none}.tinder-container .home-wrapper .profile-body .inputs .input select:focus{outline:none}.tinder-container .home-wrapper .profile-body .inputs .input select:focus::-ms-value{background:transparent;color:inherit;outline-style:dotted;outline-width:thin}.tinder-container .home-wrapper .profile-body .inputs .input select option{font-family:Inter,sans-serif;color:#202020;font-size:13px;border-radius:10px}.tinder-container .home-wrapper .profile-body .inputs .input .warning{color:var(--phone-color-red);font-size:15px}.tinder-container .home-wrapper .profile-body .inputs .photo-grid{display:grid;grid-template-columns:repeat(3,1fr);grid-gap:.5rem;margin-top:1rem;width:80%}.tinder-container .home-wrapper .profile-body .inputs .photo-grid .photo{height:9rem;width:100%;position:relative;background-color:#eaedf2;border-radius:7px;background-position:center;background-size:cover;background-repeat:no-repeat;border:1px dashed #a2a2a2}.tinder-container .home-wrapper .profile-body .inputs .photo-grid .photo .photo-button{position:absolute;bottom:-.25rem;right:-.25rem;width:1.7rem;height:1.7rem;border-radius:50%;display:flex;align-items:center;justify-content:center}.tinder-container .home-wrapper .profile-body .inputs .photo-grid .photo .photo-button svg{font-size:22px}.tinder-container .home-wrapper .profile-body .inputs .photo-grid .photo .photo-button.add{background:linear-gradient(45deg,var(--tinder-color-pink),var(--tinder-color-orange))}.tinder-container .home-wrapper .profile-body .inputs .photo-grid .photo .photo-button.add svg{color:#fff}.tinder-container .home-wrapper .profile-body .inputs .photo-grid .photo .photo-button.remove{background:#ffffff}.tinder-container .home-wrapper .profile-body .inputs .photo-grid .photo .photo-button.remove svg{color:var(--tinder-color-pink)}.tinder-container .home-wrapper .profile-body .button{width:90%;padding:1rem 0;border-radius:25px;margin-top:1.5rem;display:flex;align-items:center;justify-content:center;text-transform:uppercase;font-size:16px;font-weight:600;cursor:pointer;transition:all .2s ease-in-out}.tinder-container .home-wrapper .profile-body .button:hover{transform:scale(.97)}.tinder-container .home-wrapper .profile-body .button.red{background-color:var(--phone-color-red);color:#fff;margin-bottom:-.75rem}.tinder-container .home-wrapper .profile-body .button.gradient{background:linear-gradient(45deg,var(--tinder-color-pink),var(--tinder-color-orange));color:#fff}.tinder-container .home-wrapper .home-header{margin-top:4rem;width:96%;display:flex;justify-content:space-between;align-items:center}.tinder-container .home-wrapper .home-header svg{font-size:28px;color:#b3b3b3;cursor:pointer}.tinder-container .home-wrapper .home-header .logo{width:2.5rem;height:2.5rem}.tinder-container .home-wrapper .home-body{display:flex;flex-direction:column;align-items:center;gap:2rem;margin-top:1.5rem}.tinder-container .home-wrapper .home-body .cards{width:24rem;height:35rem}.tinder-container .home-wrapper .home-body .cards .swipe{position:absolute}.tinder-container .home-wrapper .home-body .cards .card{display:flex;flex-direction:column;align-items:flex-start;justify-content:flex-end;gap:1rem;background-position:center;background-size:cover;background-repeat:no-repeat;background-color:#fff;width:24rem;height:35rem;border-radius:12px;filter:drop-shadow(0px 4px 4px rgba(0,0,0,.25))}.tinder-container .home-wrapper .home-body .cards .card.no-more{position:absolute;align-items:center;justify-content:center}.tinder-container .home-wrapper .home-body .cards .card.no-more div{width:80%;font-size:20px;text-align:center;overflow-wrap:break-word}.tinder-container .home-wrapper .home-body .cards .card .image-count{position:absolute;z-index:1;top:.5rem;left:0;right:0;margin:0 auto;height:.5rem;width:90%;display:flex;align-items:center;gap:.25rem}.tinder-container .home-wrapper .home-body .cards .card .image-count .image{width:100%;background-color:#dad9d9;opacity:.6;height:.2rem;border-radius:5px;cursor:pointer}.tinder-container .home-wrapper .home-body .cards .card .image-count .image.active{opacity:1}.tinder-container .home-wrapper .home-body .cards .card .card-content{margin:1rem;display:flex;flex-direction:column;gap:.3rem;width:90%}.tinder-container .home-wrapper .home-body .cards .card .card-content .name{font-size:32px;font-weight:600;color:#fff}.tinder-container .home-wrapper .home-body .cards .card .card-content .name span{font-weight:400;font-size:26px;opacity:.8}.tinder-container .home-wrapper .home-body .cards .card .card-content .description{color:#fff;opacity:.9;font-size:18px;overflow-wrap:break-word}.tinder-container .home-wrapper .home-body .actions{display:flex;justify-content:space-between;align-items:center;gap:1.5rem}.tinder-container .home-wrapper .home-body .actions .button{display:flex;justify-content:center;align-items:center;box-shadow:0 1px 7px #00000029;height:4.5rem;width:4.5rem;border-radius:50%;font-size:40px;cursor:pointer;transition:all .2s ease-in-out}.tinder-container .home-wrapper .home-body .actions .button:hover{transform:scale(1.1)}.tinder-container .home-wrapper .home-body .actions .button img{width:1.8rem;height:1.8rem}.tinder-container .home-wrapper .home-body .actions .button.small{height:4rem;width:4rem;font-size:30px}.tinder-container .home-wrapper .home-body .actions .button.small img{width:1.6rem;height:1.6rem}.tinder-container .home-wrapper .home-body .actions .button.disabled{cursor:not-allowed}.tinder-container .login-container{height:100%;width:80%;padding:0 10%;display:flex;flex-direction:column;align-items:center}.tinder-container .login-container .logo{display:flex;align-items:center;gap:.25rem;margin-top:20rem;color:#fff}.tinder-container .login-container .logo img{width:4.25rem;height:4.25rem;object-position:center}.tinder-container .login-container .logo .logo-text{font-size:60px;font-weight:700}.tinder-container .login-container .create-account{height:100%;width:100%;margin-top:5rem;display:flex;flex-direction:column;position:relative}.tinder-container .login-container .create-account .login-header{display:flex;align-items:center;gap:.25rem;margin-left:-.5rem;width:100%}.tinder-container .login-container .create-account .login-header svg{font-size:34px;color:#a2a2a2;cursor:pointer}.tinder-container .login-container .create-account .login-header .title{font-size:30px;font-weight:600;cursor:pointer}.tinder-container .login-container .create-account .requirement{font-size:15px;display:flex;align-items:center;gap:.3rem;margin-top:1rem}.tinder-container .login-container .create-account .requirement:last-child{margin-top:0}.tinder-container .login-container .create-account .requirement svg{font-size:25px}.tinder-container .login-container .create-account .requirement svg.red{color:var(--phone-color-red)}.tinder-container .login-container .create-account .requirement svg.green{color:var(--phone-color-green)}.tinder-container .login-container .create-account .form-body{display:flex;flex-direction:column;justify-content:center;align-items:center;gap:2rem;margin-top:10rem}.tinder-container .login-container .create-account .form-body input{width:100%;height:2rem;font-size:20px;font-family:Inter,sans-serif;border:none;border-bottom:1px solid rgba(0,0,0,.**********)}.tinder-container .login-container .create-account .form-body input:focus{outline:none;border-bottom:2px solid var(--tinder-color-orange)}.tinder-container .login-container .create-account .form-body input.username{text-transform:lowercase}.tinder-container .login-container .create-account .form-body .dob{display:flex;flex-direction:column;gap:.75rem}.tinder-container .login-container .create-account .form-body .dob .inputs{display:flex;flex-direction:row;align-items:center;gap:.2rem;font-size:25px}.tinder-container .login-container .create-account .form-body .dob .inputs input{border:none;text-align:center;letter-spacing:.3rem;width:6rem}.tinder-container .login-container .create-account .form-body .dob .inputs input::-webkit-inner-spin-button{display:none}.tinder-container .login-container .create-account .photo-grid{display:grid;grid-template-columns:repeat(3,1fr);grid-gap:.5rem;margin-top:7rem}.tinder-container .login-container .create-account .photo-grid .photo{height:9rem;width:100%;position:relative;background-color:#eaedf2;border-radius:7px;background-position:center;background-size:cover;background-repeat:no-repeat;border:1px dashed #a2a2a2}.tinder-container .login-container .create-account .photo-grid .photo .button{position:absolute;bottom:-.25rem;right:-.25rem;width:1.7rem;height:1.7rem;border-radius:50%}.tinder-container .login-container .create-account .photo-grid .photo .button svg{font-size:22px}.tinder-container .login-container .create-account .photo-grid .photo .button.add{background:linear-gradient(45deg,var(--tinder-color-pink),var(--tinder-color-orange))}.tinder-container .login-container .create-account .photo-grid .photo .button.add svg{color:#fff}.tinder-container .login-container .create-account .photo-grid .photo .button.remove{background:#ffffff}.tinder-container .login-container .create-account .photo-grid .photo .button.remove svg{color:var(--tinder-color-pink)}.tinder-container .login-container .create-account textarea{margin-top:5rem;height:15rem;border:none;border-radius:10px;background-color:#eaedf2;padding:.5rem;font-family:Inter;font-size:18px;resize:none}.tinder-container .login-container .create-account textarea:focus{outline:none}.tinder-container .login-container .create-account .showme{height:100%;margin-top:10rem;display:flex;flex-direction:column;align-items:center;gap:1rem}.tinder-container .login-container .buttons{margin-top:10rem;display:flex;flex-direction:column;align-items:center;gap:1rem}.tinder-container .login-container .buttons .disclamer{color:#ffffffe6;font-size:15px;text-align:center;margin-bottom:1rem}.tinder-container .login-container .buttons .disclamer span{color:#fff;font-weight:500;text-decoration:underline}.tinder-container .login-container .button{width:100%;height:3rem;border-radius:25px;display:flex;align-items:center;justify-content:center;text-transform:uppercase;font-size:18px;font-weight:600;cursor:pointer;transition:all .2s ease-in-out}.tinder-container .login-container .button:hover{transform:scale(.97)}.tinder-container .login-container .button.border{color:#a2a2a2;border:2px solid #a2a2a2}.tinder-container .login-container .button.border:hover,.tinder-container .login-container .button.border.active{color:var(--tinder-color-pink);border:2px solid var(--tinder-color-pink)}.tinder-container .login-container .button.create{background-color:#fff;color:#000}.tinder-container .login-container .button.signIn{background-color:transparent;color:#fff;border:1px solid #ffffff}.tinder-container .login-container .button.gradient{background:linear-gradient(45deg,var(--tinder-color-pink),var(--tinder-color-orange));color:#fff;position:absolute;bottom:2rem}
