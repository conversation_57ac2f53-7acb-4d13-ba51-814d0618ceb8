Config = {}

Config.Debug = false -- boolean | true enables debug prints, false disables.
Config.SyncToDatabase = 1  -- time in minutes for data from server file to update to database

Config.AdminMenu = {
	Command = {enable = true, string = 'admintuner'}, -- enable/disable command, set command string.
	Keybind = {enable = true, defaultMapping = '', description = 'Open admin tuner menu'} -- enable/disable keybind, set default mapping (players edit button in-game inside GTA Settings)
}

Config.ShopCreator = {
	UseSocietyAccounts = true, -- set true to use qb-banking (must update to latest) or esx_addonaccounts instead of custom account column from the database table.
	Blip = {input = true, sprite = 446, display = 4, scale = 0.75, color = 5}, -- if input = true, set sprite/color in-game, else define settings here to apply for created shop
	Grades = { -- default job grades that are created, once a shop is being created
		{grade = 0, name = 'recruit', label = 'Recruit', salary = 50},
		{grade = 1, name = 'employee', label = 'Employee', salary = 75},
		{grade = 2, name = 'boss', label = 'Boss', salary = 100, isboss = true}
		-- make sure boss grade is the very last one and has the 'isboss = true' as the only one!
	},
	DefaultDuty = true, -- QB CORE ONLY - set default duty on qb core jobs (doesnt matter for esx)
	OffDutyPay = false, -- QB CORE ONLY - set off duty payment on qb core jobs (doesnt matter for esx)
}

Config.RecruitMember = {
	ShowFullName = true, -- set to false to only show player server id (prevent meta-gaming)
	Distance = 10.0 -- distance to players in area to include.
}

Config.Markers = {
	['duty'] = {
		enable = true, -- set to false to completely disable duty marker.
		interact = {title = Lang['title_duty'], keybind = 38, dist = 1.2, drawDist = 5.0, textUi = Lang['textui_duty'], icon = 'toggle-on', position = 'right-center', style = {borderRadius = 0, backgroundColor = '#48BB78', color = 'white'}},
		blip = {sprite = 280, display = 4, scale = 0.65, color = 0}, -- blip settings
	},
	['boss'] = {
		enable = true, -- set to false to completely disable boss marker.
		interact = {title = Lang['title_boss'], keybind = 38, dist = 1.2, drawDist = 5.0, textUi = Lang['textui_boss'], icon = 'crown', position = 'right-center', style = {borderRadius = 0, backgroundColor = '#48BB78', color = 'white'}},
		blip = {sprite = 164, display = 4, scale = 0.65, color = 0}, -- blip settings
	},
	['garage'] = {
		enable = true, -- set to false to completely disable garage marker.
		interact = {title = Lang['title_garage'], keybind = 38, dist = 1.2, drawDist = 7.0, textUi = Lang['textui_garage'], icon = 'square-parking', position = 'right-center', style = {borderRadius = 0, backgroundColor = '#48BB78', color = 'white'}},
		blip = {sprite = 357, display = 4, scale = 0.65, color = 0}, -- blip settings
	},
	['storage'] = {
		enable = true, -- set to false to completely disable storage marker.
		interact = {title = Lang['title_storage'], keybind = 38, dist = 1.2, drawDist = 5.0, textUi = Lang['textui_storage'], icon = 'box', position = 'right-center', style = {borderRadius = 0, backgroundColor = '#48BB78', color = 'white'}},
		blip = {sprite = 587, display = 4, scale = 0.65, color = 0}, -- blip settings
	},
	['workbench'] = {
		enable = true, -- set to false to completely disable workbench marker.
		interact = {title = Lang['title_workbench'], keybind = 38, dist = 1.2, drawDist = 5.0, textUi = Lang['textui_workbench'], icon = 'screwdriver-wrench', position = 'right-center', style = {borderRadius = 0, backgroundColor = '#48BB78', color = 'white'}},
		blip = {sprite = 566, display = 4, scale = 0.65, color = 0}, -- blip settings
	},
	['laptop'] = { -- marker to order parts etc.
		enable = true, -- set to false to completely disable laptop marker.
		interact = {title = Lang['title_laptop'], keybind = 38, dist = 1.2, drawDist = 5.0, textUi = Lang['textui_laptop'], icon = 'laptop', position = 'right-center', style = {borderRadius = 0, backgroundColor = '#48BB78', color = 'white'}},
		blip = {sprite = 521, display = 4, scale = 0.65, color = 0}, -- blip settings
	},
	['customs'] = { -- marker to customize/upgrade vehicles
		enable = true, -- set to false to completely disable mods menu/bay marker.
		interact = {title = Lang['title_customs'], keybind = 38, dist = 1.2, drawDist = 7.0, textUi = Lang['textui_customs'], icon = 'spray-can', position = 'right-center', style = {borderRadius = 0, backgroundColor = '#48BB78', color = 'white'}},
		blip = {sprite = 72, display = 4, scale = 0.65, color = 0}, -- blip settings
	},
	['tuningbay'] = { -- marker for work bay where tuners do work on vehicles
		enable = true, -- set to false to completely disable work bay marker.
		interact = {title = Lang['title_tuningbay'], keybind = 38, dist = 1.2, drawDist = 7.0, textUi = Lang['textui_tuningbay'], icon = 'wrench', position = 'left-center', style = {borderRadius = 0, backgroundColor = '#48BB78', color = 'white'}},
		blip = {sprite = 402, display = 4, scale = 0.65, color = 0}, -- blip settings
	},
}

Config.MarkerTeleport = true -- set to false to disable option in admin menu to TP to marker.
Config.MarkerEdit = true -- set to false to disable option in admin menu to edit marker.
Config.MarkerDefaultRGBA = 'rgba(199, 30, 30, 1)' -- default RGBA color of the marker creator/editor - only change the numbers!!.

Config.Currency = '$' -- string for currency
Config.MenuSoundFrontend = {AudioName = 'NAV_UP_DOWN', AudioRef = 'HUD_FRONTEND_DEFAULT_SOUNDSET'} -- used when browsing mods

Config.UseBuiltInGarage = true -- set to false and you need to implement your own export/functions to open the garage.
Config.Stash = {slots = 50, weight = 100000, maxSlots = 500, maxWeight = 999999} -- set default stash info in here and max slots/weight for in-game edits.

Config.OrderedItemsToStash = true -- set to true to send ordered mod items from laptop to stash directly.

Config.LaptopItems = {
	{
		category = 'Kits', -- label for the category
		icon = 'https://i.ibb.co/hW4fPcb/tuner-repairkit.png',
		items = {'tuner_tablet', 'tuner_repairkit', 'tuner_enghoist', 'nos_shots_bottle', 'nos_empty_bottle', 'nos_purge_dye'}  -- add items
	},
	{
		category = 'Performance', -- label for the category
		icon = 'https://i.ibb.co/b5gZqr2/mod-brakes.png',
		items = {'mod_engine', 'mod_brakes', 'mod_transmission', 'mod_suspension', 'mod_armor', 'mod_turbo'}  -- add items
	},
	{
		category = 'Cosmetics', -- label for the category
		icon = 'https://i.ibb.co/dQYTKWk/mod-respray.png',
		items = { -- add items:
			'mod_exhaust', 'mod_extras', 'mod_exterior', 'mod_interior', 'mod_fender', 'mod_frame', 'mod_frontbumper', 'mod_grille', 'mod_hood', 'mod_horn', 'mod_light', 'mod_livery', 'mod_neon', 
			'mod_plate', 'mod_rearbumper', 'mod_respray', 'mod_rim', 'mod_roof', 'mod_sideskirt', 'mod_spoiler', 'mod_tyresmoke', 'mod_windowtint', 
		}
	},
	{
		category = 'Custom', -- label for the category
		icon = 'https://i.ibb.co/pRx0whN/mod-drifttires.png',
		items = {'mod_bullettires', 'mod_drifttires', 'mod_stocktires'}  -- add items
	}
}

Config.Markup = {
	Enable = false, -- enable/disable adding markup % on top of the total price (realistic feature)
	Default = 10, -- default markup %, else it uses the defined price by the boss
	Max = 100 -- max value for markup % able to be set by boss in the boss menu (max 100!)
}

Config.ModOrder = {
	MarkupToAccount = false, -- set to false and shop markup won't go into the shop's account.
	PartAcquisitionToAccount = true, -- set to false and item price for each item used, will not be added to shop's account, when customer places the mod order.
	LaborChargeToAccount = true, -- set  to true and the "labor charge", seen in modorders, will be added to shop's account, when customer places the mod order.
	Keymap = {key = 'j', description = 'Toggle Workflow UI Cursor'},
	WorkflowTitle = 'Workflow - %s',
	RefundRequiredGrade = 2, -- required job grade to refund.
	TaskList = {
		name = '<strong>%s (%s)</strong>', -- workflow ui task name
		description = 'Item: %s' -- workflow ui task description
	},
	Icons = {
		customer = 'user',
		paidAmount = 'file-invoice-dollar',
		beginWork = 'circle-play',
		refund = 'hand-holding-dollar',
		stopWork = 'circle-stop',
		taskComplete = 'circle-check',
		taskIncomplete = 'circle-xmark',
	}
}

Config.ModsMenu = {
	Menu = {title = 'Mods Menu', position = 'top-left'}, -- mods main menu title and position
	DisablePurchase = true, -- set to true to disable purchase mods from customs menu, forcing players to do mod orders and rp with tuners.

	Repair = {
		allow = true, -- allow quick repair for $Repair.price (below) in mod menu marker. Set to false to force customers to go to a mechanic first.
		price = 2500, -- 1000$ to repair vehicle, before installing mods.
		moneyToAccount = true, -- if true, paid fees goes to the shop's account, else the money just disappears.
	},

	IncentivizedPricing = {
		enable = true, -- set to true to enable incentivized pricing. Only applies if purchasing and applying mods directly through the mods menu. This incentivized price is ignored if a mod order is created.
		percent = 5 -- percentage to increase the price of mods in the mod menu, to kinda force players to RP with tuners.
	}
}

Config.ModStations = {
	Interact = {keybind = 38, dist = 1.2, drawDist = 7.0, textUi = '[E] Mods Menu', icon = 'spray-can', position = 'right-center', style = {borderRadius = 0, backgroundColor = '#48BB78', color = 'white'}},
	Blip = {name = 'Mod Station', sprite = 72, display = 4, scale = 0.65, color = 0},
	Marker = {type = 20, scale = {0.35, 0.35, 0.35}, rgba = {255, 99, 71, 100}},
	Locations = {
		{ -- mrpd
			pos = vector3(478.68, -1022.33, 28.01), -- pos of the mod station
			jobs = {'police', 'lspd', 'test'}, -- allowed jobs
			mods = {'respray', 'bodyworks', 'chassis', 'enginebay', 'exterior', 'interior', 'lights', 'wheels', 'performance'},
			free = true, -- free of charge?
			blip = true -- enable/disable blip
		}, 
		{ -- pillbox
			pos = vector3(344.53, -554.79, 28.74), -- pos of the mod station
			jobs = {'ambulance', 'ems', 'medic', 'doc'}, -- allowed jobs
			mods = {'respray', 'bodyworks', 'chassis', 'enginebay', 'exterior', 'interior', 'lights', 'wheels', 'performance'},
			free = true, -- free of charge?
			blip = true -- enable/disable blip
		}, 
	}
}

Config.ItemBasedMods = {
	CancelInteraction = {Button = 252, String = '[X] Cancel Install'},
	SkillCheck = {Enable = false, Difficulty = {'easy', 'easy', 'easy'}, Inputs = {'w', 'a', 's', 'd'}},
	Target = {Label = 'Install Mod', Icon = 'fa-solid fa-wrench'},

	PreviewControls = {
		Hood = 108, -- NUMPAD 4 // Opens hood during preview with item based mod installations
		Trunk = 110, -- NUMPAD 5 // Opens trunk during preview with item based mod installations
		Doors = 109, -- NUMPAD 6 // Opens all doors (except hood & trunk) during preview with item based mod installations
		Close = 118 -- NUMPAD 9 // Shuts all doors during preview with item based mod installations
	},
	
	StringToRGB = {
		['white'] = {255, 255, 255},
		['red'] =  {246, 75, 60},
		['pink'] = {253, 226, 226},
		['blue'] = {0, 168, 204},
		['yellow'] = {245, 252, 193},
		['green'] = {99, 154, 103},
		['orange'] = {255, 164, 27},
		['brown'] = {156, 85, 24},
		['purple'] =  {190, 121, 223},
		['grey'] = {50, 50, 50},
		['black'] = {0, 0, 0},
		-- you can add more like bronze etc.
	}
}

Config.Dyno = {

	Torque = { -- This value specifies the drive force of the car, at the wheels. 1.0 will be default setting of the vehicle. Values less than 1.0 will in effect give the vehicle less drive force and values greater than 1.0 will produce more drive force.
		label = 'Torque Modifier', -- label 
		description = 'This value specifies the drive force of the car, at the wheels.', -- description
		icon = 'gears', -- icon 
		slider = { -- input dialog slider settings
			min = 1.0, -- min torque value (not recommended below 1.0, as this is vehicle default)
			max = 3.0, -- max torque value (not recommended above 2.0 as torque is really overpowered)
			step = 0.1 -- steps to increase the slider with (goes from 1.1 to 1.2, 1.3 etc)
		},
	},
	
	Power = {
		label = 'Power Modifier', -- label 
		description = 'Describes how fast an engine will rev.', -- description
		icon = 'gauge-high', -- icon 
		slider = { -- input dialog slider settings
			min = 0.00,
			max = 2.0,
			step = 0.05
		},
	},

	Brakes = { -- Multiplies the game's calculation of deceleration. Bigger number = harder braking
		label = 'Brakes Modifier', -- label 
		description = 'Multiplies the game\'s calculation of deceleration. Bigger number = harder braking.', -- description
		icon = 'shoe-prints', -- icon 
		slider = { -- input dialog slider settings
			min = 0.01, -- almost no brakes
			max = 2.0, -- hard brakes, however 1.0 seems max on most vehicles.
			step = 0.1 -- steps to increase the slider with
		},
	},

	ViewModifiers = {
		icon = 'magnifying-glass',
		description = 'View current dyno handling modifiers',
	},

	FineTuning = { -- input dialog settings
		icon = 'sliders',
		description = 'Fine tune the vehicle\'s torque, power and brakes',
		reset = {label = 'Hard Reset'},
		progressBar = {
			enable = true, 
			duration = 3000,
			label = 'SAVING DYNO PROFILE...'
		}
	},

}

Config.T1GER_MechanicSystem = GetResourceState('t1ger_mechanicsystem'):find('started') and true or false
print("has mechanic system: ", Config.T1GER_MechanicSystem)