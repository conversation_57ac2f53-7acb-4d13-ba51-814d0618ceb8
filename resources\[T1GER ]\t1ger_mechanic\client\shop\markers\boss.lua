-- Opens the main boss menu for the mechanic shop
---@param shopId number unique shop identifier
---@param markerId string unique marker identifier
function BossMain(shopId, markerId)
    lib.registerContext({
        id = "mechanic_boss_menu",
        title = locale("menu_title.boss_main"),
        options = {
            {
                title = locale("menu_title.boss_account"),
                icon = "sack-dollar",
                arrow = true,
                onSelect = function()
                    AccountMenu(shopId, markerId)
                end
            },
            {
                title = locale("menu_title.boss_employees"),
                icon = "people-group",
                arrow = true,
                onSelect = function()
                    EmployeeMenu(shopId, markerId)
                end
            },
        },
    })
    lib.showContext("mechanic_boss_menu")
end

-- Handles deposit or withdrawal actions for the shop account
---@param shopId number unique shop identifier
---@param method string ("deposit" or "withdraw")
---@param markerId string unique marker identifier
---@param shopAccount number the current account balance of the shop
local function AccountAction(shopId, method, markerId, shopAccount)
    local input = lib.inputDialog(locale("input_title.boss_account"), {
        {type = "input", label = locale("input_label.boss_current_account"), icon = "sack-dollar", disabled = true, default = shopAccount},
        {type = "number", label = locale("input_label.boss_"..method.."_account"), icon = (method == "deposit" and "money-bill-trend-up" or method == "withdraw" and "money-bill-transfer"), placeholder = 100}
    })

    if not input then
        return lib.showContext("mechanic_account")
    end

    local amount = tonumber(input[2])

    if type(amount) ~= "number" or amount <= 0 then
        _API.ShowNotification(locale("notification.input_amount_higher_0"), "inform", {})
        return lib.showContext("mechanic_account")
    end

    if method == "withdraw" and amount > shopAccount then
        _API.ShowNotification(locale("notification.account_withdraw_max"), "inform", {})
        return lib.showContext("mechanic_account")
    end

    TriggerServerEvent("t1ger_mechanic:server:"..method.."Account", shopId, amount, markerId)
    AccountMenu(shopId, markerId)
end

-- Opens the shop account menu for deposits and withdrawals
---@param shopId number unique shop identifier
---@param markerId string (unique marker identifier)
function AccountMenu(shopId, markerId)
    -- get shop account
    local shopAccount = lib.callback.await("t1ger_mechanic:server:getShopAccount", false, shopId)

    -- register context
    lib.registerContext({
        id = "mechanic_account",
        title = locale("menu_title.boss_account"),
        menu = "mechanic_boss_menu",
        options = {
            {
                title = string.format(locale("menu_title.boss_account_balance"), math.groupdigits(shopAccount)),
                icon = "sack-dollar",
                readOnly = true
            },
            {
                title = locale("menu_title.boss_account_deposit"),
                icon = "money-bill-trend-up",
                onSelect = function()
                    AccountAction(shopId, "deposit", markerId, shopAccount)
                end
            },
            {
                title = locale("menu_title.boss_account_withdraw"),
                icon = "money-bill-transfer",
                onSelect = function()
                    AccountAction(shopId, "withdraw", markerId, shopAccount)
                end
            }
        }
    })
    lib.showContext("mechanic_account")
end

-- Opens the shop employee menu for employee recruitment & management
---@param shopId number unique shop identifier
---@param markerId string (unique marker identifier)
function EmployeeMenu(shopId, markerId)
    lib.registerContext({
        id = "mechanic_employees",
        title = locale("menu_title.boss_employees"),
        menu = "mechanic_boss_menu",
        options = {
            {
                title = locale("menu_title.boss_employees_recruit"),
                icon = "user-plus",
                onSelect = function()
                    RecruitEmployee(shopId, markerId)
                end
            },
            {
                title = locale("menu_title.boss_employees_view"),
                icon = "users",
                onSelect = function()
                    ViewEmployees(shopId)
                end
            }
        }
    })
    lib.showContext("mechanic_employees")
end

-- Opens the shop employee menu for employee recruitment
---@param shopId number unique shop identifier
---@param markerId string (unique marker identifier)
function RecruitEmployee(shopId, markerId)
    local menuOptions = {}

    -- get nearby players within 10.0 unit radius:
    local players = lib.getNearbyPlayers(coords, 10.0)
    if #players == 0 then
        _API.ShowNotification(locale("notification.no_players_nearby"), "inform", {})
        return lib.showContext("mechanic_employees")
    end

    -- insert players into options:
    for i = 1, #players do
        local player = players[i]
        local playerName = GetPlayerName(player.id)
        local playerSrc = GetPlayerServerId(player.id)
        menuOptions[#menuOptions+1] = {
            title = string.format("[%s] %s", playerSrc, playerName),
            icon = "user",
            onSelect = function()
                TriggerServerEvent("t1ger_mechanic:server:sendRecruitment", shopId, playerSrc)
                Wait(100)
                lib.showContext("mechanic_employees")
            end,
        }
    end

    -- validate menuOptions:
    if #menuOptions == 0 then
        _API.ShowNotification(locale("notification.no_players_nearby"), "inform", {})
        return lib.showContext("mechanic_employees")
    end
    
    -- register context:
    lib.registerContext({
        id = "recruit_mechanic_employee",
        title = locale("menu_title.boss_employees_recruit"),
        menu = "mechanic_employees",
        options = menuOptions,
    })
    -- show context:
    lib.showContext("recruit_mechanic_employee")
end

---function to register view employees context menu
---@param shopId number unique identifier for the shop
local function RegisterViewEmployeesMenu(shopId)
    if not Shops[shopId].employees or next(Shops[shopId].employees) == nil then
        _API.ShowNotification(locale("notification.no_hired_employees"), "inform", {})
        return lib.showContext("mechanic_employees")
    end

    local employeeList = {}
    -- populate employee list table:
    for _, emp in pairs(Shops[shopId].employees) do
        employeeList[#employeeList+1] = {
            title = emp.name,
            icon = "user",
            metadata = {
                {label = locale("menu_metadata.job_grade"), value = Shops[shopId].job.grades[tostring(emp.grade)].label.." ["..emp.grade.."]"},
                {label = locale("menu_metadata.player_identifier"), value = emp.identifier},
            },
            arrow = true,
            onSelect = function()
                print("manage selected employee")
                ManageEmployee(shopId, emp)
            end
        }
    end
    
    -- register context:
    lib.registerContext({
        id = "view_mechanic_employees",
        title = locale("menu_title.boss_employees_view"),
        menu = "mechanic_employees",
        options = employeeList,
    })
end

---function to view view employees
---@param shopId number unique identifier for the shop
function ViewEmployees(shopId)
    RegisterViewEmployeesMenu(shopId)
    -- show context:
    lib.showContext("view_mechanic_employees")
end

--- Manages a selected employee within a shop.
--- This function allows removing an employee from a shop or promoting/demoting job grades.
--- @param shopId number The unique identifier of the shop.
--- @param employee table The employee data.
--- @field name string The name of the employee.
--- @field identifier string The unique identifier of the employee.
--- @field grade number The job grade of the employee.
function ManageEmployee(shopId, employee)
    -- register context:
    lib.registerContext({
        id = "manage_mechanic_employee",
        title = locale("menu_title.boss_employee_manage"),
        menu = "view_mechanic_employees",
        onBack = function()
            RegisterViewEmployeesMenu(shopId)
        end,
        options = {
            {
                title = locale("menu_title.boss_employee_remove"),
                description = string.format(locale("menu_description.boss_employee_remove"), employee.name),
                icon = "user-slash",
                onSelect = function()
                    RemoveEmployee(shopId, employee)
                end
            },
            {
                title = locale("menu_title.boss_employee_update"),
                description = string.format(locale("menu_description.boss_employee_update"), employee.name),
                icon = "user-gear",
                onSelect = function()
                    UpdateEmployee(shopId, employee)
                end
            },
        },
    })
    -- show context:
    lib.showContext("manage_mechanic_employee")
end

--- Remove an emplyoee from the shop
--- @param shopId number The unique identifier of the shop.
--- @param employee table The employee data.
function RemoveEmployee(shopId, employee)
    -- check if employee is owner:
    if employee.identifier == Shops[shopId].owner then
        _API.ShowNotification(locale("notification.cannot_fire_owner"), "inform", {})
        return ManageEmployee(shopId, employee)
    end

    -- remove employee:
    TriggerServerEvent("t1ger_mechanic:server:fireEmployee", shopId, employee)
    Wait(100)
    -- open 'view employees' menu:
    ViewEmployees(shopId)
end

--- Update an emplyoee from the shop
--- @param shopId number The unique identifier of the shop.
--- @param employee table The employee data.
function UpdateEmployee(shopId, employee)
    -- check if employee is owner:
    if employee.identifier == Shops[shopId].owner then
        _API.ShowNotification(locale("notification.cannot_update_owner"), "inform", {})
        return ManageEmployee(shopId, employee)
    end

    local job_grades = {}

    for k,v in pairs(Shops[shopId].job.grades) do
        if employee.grade ~= v.grade then
            job_grades[#job_grades+1] = {
                title = v.label,
                icon = "ellipsis",
                onSelect = function()
                    -- update employee:
                    TriggerServerEvent("t1ger_mechanic:server:promoteEmployee", shopId, employee, v.grade, v.label)
                    Wait(100)
                    employee.grade = v.grade
                    -- return menu:
                    return ManageEmployee(shopId, employee)
                end
            }
        end
    end

    -- register context:
    lib.registerContext({
        id = "update_mechanic_employee",
        title = locale("menu_title.boss_employee_update"),
        menu = "manage_mechanic_employee",
        options = job_grades,
    })
    
    -- show context:
    lib.showContext("update_mechanic_employee")
end

--- Event to receive employee recruitment and respond:
RegisterNetEvent("t1ger_mechanic:client:receiveRecruitment", function(shopId, boss)
    -- Recruitment Alert
    local recruitment = lib.alertDialog({
        header = locale("alert.header.recruit_employee"),
        content = string.format(locale("alert.content.recruit_employee"), Shops[shopId].name),
        centered = true,
        cancel = true
    })

    -- If the user confirms, send reqruitment response
    if recruitment == "confirm" then
        TriggerServerEvent("t1ger_mechanic:server:respondRecruitment", shopId, true, boss)
    else
        TriggerServerEvent("t1ger_mechanic:server:respondRecruitment", shopId, false, boss)
    end
end)