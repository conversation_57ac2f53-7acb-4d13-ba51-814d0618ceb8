Config.Nitrous = {
	RequireTurbo = false, -- if true, vehicle needs turbo installed in order to install a Nitrous Kit.
	MaxSize = 12, -- max lb size of Nitrous Kit, 1 lb equals 10 shots/seconds. Min kit size will always be 1lb.
	UnitPrice = 1500, -- price of 1lb. Example: Purchasing 4lb Nitrous Kit, will cost 4 x 1500.
	DefaultThreshold = 5, -- threshold in seconds. 1 shot of nitrous equals to 1 second. This determines for how long the player can hold the Burst/Purge button down
	Weight = 'lb', -- string used for the weight

	Keybinds = { -- keybind settings for NOS purge and burst
		purge = {
			description = 'Use NOS Purge', -- description shown in-game in keybind settings
			defaultKey = 'RCONTROL', -- default key, players can change in-game from Settings > Keybinds > Fivem
		},
		burst = {
			description = 'Use NOS Burst', -- description shown in-game in keybind settings
			defaultKey = 'LSHIFT' -- default key, players can change in-game from Settings > Keybinds > Fivem
		}
	},

	MenuIcons = {
		unitPrice = 'dollar-sign', -- icon for unit price in nitrous order input dialog
		selectSize = 'bottle-droplet', -- icon for select size in nitrous order input dialog
		currentNosKit = 'fire-flame-simple', -- icon for current nitrous kit in menu
		nitrousKit = 'bottle-droplet', -- icon for nitrous kits in menu
		burstKit = 'gauge-high', -- icon for burst kits in menu
	},

	Install = { -- Nitrous Kit and Burst Kit installation settings
		NitrousKit = {
			prop = 'v_ind_cs_gascanister',
			anim = { dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49, boneId = 28422 },
			offset = { pos = vector3(0.1, -0.1, -0.05), rot = vector3(-45.0, 90.0, 0.0) },
			target = { icon = 'fa-solid fa-screwdriver', label = 'Install Nitrous Kit', distance = 5.0 },
			resetShots = true, -- When installing a nitrous kit, the shots will reset to 0, forcing players to refill the nitrous kit.
		},
		BurstKit = {
            prop = 'imp_prop_impexp_differential_01a',
            anim = { dict = 'anim@heists@humane_labs@finale@keycards', name = 'ped_a_enter_loop', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49, boneId = 60309 },
            offset = { pos = vector3(0.0, 0.0, 0.0), rot = vector3(0.0, -90.0, 0.0) },
            target = { icon = 'fa-solid fa-screwdriver', label = 'Install Burst Kit', distance = 5.0 },
        }
	},

	DefaultBurst = { -- Default Burst Kit, uses .DefaultThreshold seconds
		label = 'Stock Burst Kit', -- label
		icon = 'gauge-simple',
		price = 1000, -- price of this kit (free when installing Nitrous Kit first time).
		description = 'Stock Threshold of 5 seconds'
	},

	BurstKits = { -- Burst Upgrade Kits: allows to upgrade the threshold seconds for NOS Burst (also applies to purge).
		-- Stock Burst Kit will always be displayed first, which equals to: .DefaultThreshold with .DefaultBurst data.
		[1] = {label = 'Street Burst Kit', icon = 'gauge', threshold = 7, price = 6500, description = 'Threshold upgrade to 7 seconds'},
		[2] = {label = 'Sports Burst Kit', icon = 'gauge', threshold = 10, price = 12500, description = 'Threshold upgrade to 10 seconds'},
		[3] = {label = 'Race Burst Kit', icon = 'gauge', threshold = 13, price = 27500, description = 'Threshold upgrade to 13 seconds'},
	},

	Bottle = { -- Nitrous refill via bottle
		skillcheck = {enable = true, difficulty = {'easy', 'easy'}, inputs = {'w', 'a', 's', 'd'}}, -- skillcheck enable/disable & settings
		duration = 3000, -- duration in MS to load nitrous from bottle into kit
		label = 'Filling Nitrous', -- label/text on the progress bar
	},

	DyeInstall = {
		target = { icon = 'fa-solid fa-syringe', label = 'Inject Purge Dye', distance = 5.0 },
		duration = 3000, -- duration in MS to install the purge dye
	},

	Efficiency = {
		noPurge = 30, -- Efficiency in percentage when nitrous is used without any purging
		windowExpiry = 50, -- Efficiency in percentage when the efficiency window expires
		purge = { -- Efficiency settings when purging
			min = 70, -- Minimum efficiency in percentage (1-second purge)
			max = 100, -- Maximum efficiency in percentage (100 is recommended) (after full purge up to the burst kit threshold)
		}
	},

	Purge = { -- Purge mechanics and notifications
		ptfx_size = 0.35, -- size of purge effects. 0.35 seems realistic.
		window = 2, -- Duration in minutes for how long the efficiency window lasts for nitrous burst
		cooldown = 1, -- Duration in minutes after the efficiency window expires (how long the NOS system rests before being able to start a new efficiency window)
		gasLeak = { -- Gas leak settings when purging during cooldown
			duration = 1000, -- time in ms for duration of the ptfx, before its stopped. 1000 ms seems pretty realistic. (remember players can spam this)
			chance = 50, -- Percentage chance of gas leak occurring during purge cooldown
			remove = { -- Range for removing nitrous shots (percentage) from the vehicle's nitrous tank kit
				min = 1, -- Minimum percentage of nitrous shots to be removed in case of a leak
				max = 50 -- Maximum percentage of nitrous shots to be removed in case of a leak
			}
		},
		notify = {
			showHeldDuration  = true, -- shows a message every second purge button is held, to help player keep track of how long the button is held.
			showConsumption = true, -- on purge button release, notification showing NOS shots consumed and available shots out of maximum.
			showEfficiency = true, -- on button release, notification showing NOS purge efficiency percentage
		},
	},

	Burst = {
		cooldown = true, -- cooldown equivalent to how long nos burst was held. So if used for 4 sec, wait 4 sec before using nos again?
		baseTorqueMultiplier = 1.8, -- base torquemultiplier that's multiplied with (maxSpeed / currentSpeed). Do not got below 1.0!
		requiredEngineHealth = 500.0, -- valaue required to activate NOS burst, between 0.0-1000.0 (decimals) where 1000.0 is fully fixed engine. 
		engineDamage = {
			noPurge = {chance = 70, engineHealth = 199.9}, -- when bursting without purging at all | chance to occur and engine health value
			windowExpiry = {chance = 25, engineHealth = 199.9}, -- when bursting after efficiency window expired | chance to occur and engine health value
			threshold = {chance = 90, engineHealth = 199.9} -- when holding the button more than what the burstKit allows | chance to occur and engine health value
		},
		notify = {
            showHeldDuration = true, -- shows a message every second burst button is held, to help player keep track of how long the button is held.
            showRemainingShots = true, -- Show remaining nitrous shots after burst is used
        }
	},

	Refill = { -- points where tuners can refill empty nos bottles (turning them into 10-shots bottles)
		distance = 2.0, -- distance for given coords to be able to use empty nos bottle item
        blip = {name = 'NOS Refill', sprite = 436, display = 4, scale = 0.65, color = 38}, -- blip on the map
		skillcheck = {enable = false, difficulty = {'easy', 'easy'}, inputs = {'w', 'a', 's', 'd'}}, -- skillcheck enable/disable & settings
		duration = 3000, -- duration in MS to transform empty bottle to nos bottle
		label = 'Refilling NOS Bottle', -- label/text on the progress bar
		anim = {dict = 'mini@repair', name = 'fixing_a_player', blendIn = 8.0, blendOut = -8.0, flag = 49},
		points = { -- points must be indexed with number, increamenting from 1 to infinite
			[1] = {
                pos = vector3(1697.86, -1696.32, 112.46), -- pos of the refill point
                blip = true, -- enable/disable blip for the point (only visible for tuners)
                size = 20, -- size in lb, determine how many refills can be done, one refill of 1lb bottle will subtract the size
            },
			[2] = {pos = vector3(251.15, -2932.52, 5.75), blip = true, size = 20},
			[3] = {pos = vector3(1014.79, -2529.23, 28.30), blip = true, size = 20},
			[4] = {pos = vector3(1119.73, -1625.65, 34.87), blip = true, size = 20},
			[5] = {pos = vector3(-104.67, -2686.20, 5.99), blip = true, size = 20},
			[6] = {pos = vector3(-424.44, -2265.67, 7.59), blip = true, size = 20},
		},
	},

}
