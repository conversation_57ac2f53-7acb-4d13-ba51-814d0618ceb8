ESX = exports.es_extended:getSharedObject()
RegisterNetEvent('carwash:DoVehicleWashParticles', function(vehNet, use_props)
    local src = source
    TriggerClientEvent('carwash:DoVehicleWashParticles', -1, vehNet, src, use_props)
end)

RegisterCallback('carwash:CanPurchaseCarWash', function(source, cb)
    local src = source
    local Player = GetPlayer(src)
    local xPlayer = ESX.GetPlayerFromId(source)
    local callback = false
    if (GetPlayerAccountBalance(Player, Config.cash_account_name) >= Config.cost or GetPlayerAccountBalance(Player, Config.bank_account_name) >= Config.cost) then
        if RemovePlayerMoney(Player, Config.cash_account_name, Config.cost, 'Vehiculo Lavado') then
            callback = true
        elseif RemovePlayerMoney(Player, Config.bank_account_name, Config.cost, 'Vehiculo Lavado') then
            callback = true
        end
    end

    local closest, soc = exports['t1ger_mechanic']:GetClosestMecShop(GetEntityCoords(GetPlayerPed(source)))
			print(closest)
			if closest > 0 then
				TriggerEvent('mechanicsystem:server:addAccountMoney',closest, Config.cost)
                --TriggerEvent('bandi_rpchat:loggerfromsv', source, '', 'CarWashPayment', closest, soc, Config.cost) 
			end
    cb(callback)
end)
