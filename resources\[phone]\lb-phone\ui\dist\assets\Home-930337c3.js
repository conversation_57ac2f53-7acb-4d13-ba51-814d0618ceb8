import{u as m,r as u,G as C,q as E,s as h,j as t,a as i,m as v,F as N,L as e,bD as S,bN as A,S as f,J as M,y as H,C as O,bI as k,aF as b,bO as I,bP as _,bQ as T,A as g,aX as U}from"./index-99e0aeb1.js";const p={homes:[{id:"1",label:"Ocean Drive 23",locked:!1,uniqueId:"1",keyholders:[{identifier:"1",name:"<PERSON>"},{identifier:"2",name:"<PERSON>"}]},{id:"2",label:"Sunset Boulevard 12",locked:!0,uniqueId:"2",keyholders:[{identifier:"3",name:"<PERSON>"}]},{id:"3",label:"Sunset Boulevard 13",locked:!1,uniqueId:"3",keyholders:[{identifier:"4",name:"<PERSON>"}]}]};const n=M([]),P=M(null);function L(){const a=m(g),r=m(n),l=m(P),o=m(U.Unlocked),[d,s]=u.useState(1);return u.useEffect(()=>{s(Math.floor(Math.random()*5)+1)},[o]),u.useEffect(()=>{C("Home")&&E("Home",{action:"getHomes"},p.homes).then(c=>{if(!c)return h("warning","Didn't receive any response from getHomes callback");n.set(c)})},[a==null?void 0:a.active]),t("div",{className:"home-container",style:{backgroundImage:`url(./assets/img/backgrounds/default/apps/home/<USER>"blur(10px) brightness(0.75)"},exit:{backdropFilter:"inherit"},transition:{duration:1},className:"blur-overlay"}),i("div",{className:"home-header",children:l?t(N,{children:[i("div",{className:"title",children:e("APPS.HOME.MY_HOME")}),i(S,{className:"close",onClick:()=>P.reset()})]}):i(N,{children:t("div",{className:"title",children:[i(A,{})," ",e("APPS.HOME.MY_HOMES")]})})}),i(v.div,{...f(l?"right":"left",l?"home":"homes",.2),className:"home-wrapper",children:l?i(y,{}):i("div",{className:"items",children:r.map(c=>t(v.div,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},viewport:{once:!0},className:"item small",onClick:()=>P.set(c),children:[i("div",{className:"icon blue",children:i(A,{})}),t("div",{className:"info",children:[i("div",{className:"title",children:c.label}),c.id&&t("div",{className:"value",children:["#",c.id]})]})]},c.id))})})]})}const y=()=>{const a=m(P);let r="";return t(N,{children:[t("div",{className:"title",children:[e("APPS.HOME.ACTIONS"),i(H,{})]}),t("div",{className:"category",children:[t("div",{className:"item",onClick:()=>{O.PopUp.set({title:e("APPS.HOME.WAYPOINT_POPUP.TITLE"),description:e("APPS.HOME.WAYPOINT_POPUP.TEXT").format({home:a.label}),buttons:[{title:e("APPS.HOME.WAYPOINT_POPUP.CANCEL")},{title:e("APPS.HOME.WAYPOINT_POPUP.PROCEED"),cb:()=>{E("Home",{action:"setWaypoint",id:a.id,houseData:a})}}]})},children:[i("div",{className:"icon blue",children:i(k,{})}),t("div",{className:"info",children:[i("div",{className:"title",children:e("APPS.HOME.LOCATION")}),i("div",{className:"value",children:e("APPS.HOME.SET_WAYPOINT")})]})]}),a.locked!==void 0&&t("div",{className:"item","data-active":a.locked,onClick:()=>{O.PopUp.set({title:e("APPS.HOME.LOCK_POPUP.TITLE").format({toggle:a.locked?e("APPS.HOME.UNLOCK"):e("APPS.HOME.LOCK")}),description:e("APPS.HOME.LOCK_POPUP.TEXT").format({toggle:(a.locked?e("APPS.HOME.UNLOCK"):e("APPS.HOME.LOCK")).toLowerCase()}),buttons:[{title:e("APPS.HOME.LOCK_POPUP.CANCEL")},{title:e("APPS.HOME.LOCK_POPUP.PROCEED"),cb:()=>{E("Home",{action:"toggleLocked",id:a.id,uniqueId:a.uniqueId,houseData:a},!a.locked).then(l=>{l!==void 0&&(P.patch({locked:l}),n.set(n.value.map(o=>o.id===a.id?{...o,locked:l}:o)))})}}]})},children:[i("div",{className:"icon blue",children:i(b,{})}),t("div",{className:"info",children:[i("div",{className:"title",children:e("APPS.HOME.FRONT_DOOR")}),i("div",{className:"value",children:a.locked?e("APPS.HOME.LOCKED"):e("APPS.HOME.UNLOCKED")})]})]}),t("div",{className:"item",onClick:()=>{O.PopUp.set({title:e("APPS.HOME.GIVE_KEY_POPUP.TITLE"),description:e("APPS.HOME.GIVE_KEY_POPUP.TEXT"),input:{placeholder:"0",type:"text",minCharacters:1,onChange:l=>r=l},buttons:[{title:e("APPS.HOME.GIVE_KEY_POPUP.CANCEL")},{title:e("APPS.HOME.GIVE_KEY_POPUP.PROCEED"),cb:()=>{var l,o;if(!r)return h("error","src.current is not defined.");E("Home",{action:"addKeyholder",id:a.id,source:r,houseData:a},I()&&[...(o=(l=p.homes)==null?void 0:l.find(d=>d.id===a.id))==null?void 0:o.keyholders,{identifier:r,name:"Chris"}]).then(d=>{if(!d)return h("error","Failed to add keyholder to home");r="",P.patch({keyholders:d}),n.set(n.value.map(s=>s.id===a.id?{...s,keyholders:d}:s))})}}]})},children:[i("div",{className:"icon yellow",children:i(_,{})}),t("div",{className:"info",children:[i("div",{className:"title",children:e("APPS.HOME.GIVE_KEY")}),i("div",{className:"value",children:e("APPS.HOME.MANAGE")})]})]})]}),t("div",{className:"title",children:[e("APPS.HOME.KEY_ACCESS"),i(H,{})]}),i("div",{className:"category scroll",children:a==null?void 0:a.keyholders.map((l,o)=>t("div",{className:"item small full",onClick:()=>{O.PopUp.set({title:e("APPS.HOME.REMOVE_KEY_POPUP.TITLE"),description:e("APPS.HOME.REMOVE_KEY_POPUP.TEXT").format({name:l.name}),buttons:[{title:e("APPS.HOME.REMOVE_KEY_POPUP.CANCEL")},{title:e("APPS.HOME.REMOVE_KEY_POPUP.PROCEED"),cb:()=>{E("Home",{action:"removeKeyholder",id:a.id,identifier:l.identifier,houseData:a},!0).then(d=>{if(!d)return h("error","Failed to remove keyholder from home");P.patch({keyholders:a.keyholders.filter(s=>s!==l)}),n.set(n.value.map(s=>s.id===a.id?{...s,keyholders:a.keyholders.filter(c=>c!==l)}:s))})}}]})},children:[i("div",{className:"icon blue",children:i(T,{})}),t("div",{className:"info",children:[i("div",{className:"title",children:l.name}),i("div",{className:"value",children:e("APPS.HOME.MANAGE")})]})]},o))})]})};export{L as default};
