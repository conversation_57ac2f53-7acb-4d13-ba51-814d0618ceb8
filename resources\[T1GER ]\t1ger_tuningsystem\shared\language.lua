Lang = {
	-- Notifications:
	['no_players_nearby'] = 'No players nearby.',
	['no_vehicle_nearby'] = 'No vehicle nearby.',
	['must_be_inside_veh'] = 'You must be inside a vehicle.',
	['only_admins_can_do_this'] = 'Only admins can perform this action.',
	['no_tuner_shops'] = 'There are no tuner shops registered.',
	['input_amount_higher_0'] = 'Entered amount must be higher than 0.',
	['you_created_tuner_shop'] = 'You created a tuner shop: %s.',
	['you_created_new_job'] = 'You created a new job: %s [%s].',
	['you_assigned_new_boss'] = 'You assigned a new boss (%s) to the shop: %s.',
	['you_removed_boss'] = 'You removed the current boss from the shop entirely.',
	['you_deleted_shop_x'] = 'You deleted the shop: %s.',
	['you_created_marker'] = 'You successfully created a new marker.',
	['you_deleted_marker'] = 'You successfully deleted a marker.',
	['you_edited_marker'] = 'You successfully updated a marker.',
	['you_sat_account_balance'] = 'You successfully set the shop\'s account balance to: $%s',
	['you_sat_shop_categories'] = 'You successfully updated the shop\'s categories / field of work.',
	['you_sat_shop_delivery_pos'] = 'You successfully updated the shop\'s delivery coords.',
	['you_clocked_off_duty'] = 'You clocked off-duty.',
	['you_clocked_on_duty'] = 'You clocked on-duty.',
	['you_are_not_boss'] = 'You are not boss - no access!',
	['input_required'] = 'You must provide an input.',
	['account_withdraw_max'] = 'You cannot withdraw more than what\'s in the account.',
	['not_enough_money'] = 'Not enough money.',
	['money_deposited'] = 'You deposited $%s into the shop account.',
	['money_withdrawn'] = 'You withdrew $%s from the shop account.',
	['cannot_fire_boss'] = 'You cannot fire the boss, idiot.',
	['fired_employee'] = 'You fired %s from the shop.',
	['cannot_demote_boss'] = 'You cannot demote/promote the boss, idiot.',
	['employee_grade_updated'] = 'You updated %s\'s job-grade.',
	['employee_already_hired'] = 'The player is already recruitted in shop: %s',
	['employee_recruit_sent'] = 'You sent recruitment papers to: %s.',
	['recruitment_accepted'] = 'You accepted the shop recruitment.',
	['recruitment_declined'] = 'You declined the shop recruitment.',
	['recruitment_accepted2'] = 'Player has accepted the recruitment and is now hired in the shop.',
	['recruitment_declined2'] = 'Player has declined the recruitment.',
	['no_owned_vehicles'] = 'You have no owned vehicles.',
	['no_owned_vehicles_in_this_garage'] = 'You have no owned vehicles parked in this garage.',
	['this_storage_empty'] = 'This storage is empty.',
	['your_inventory_empty'] = 'Your inventory is empty.',
	['storage_max_deposit_amount_x'] = 'You only have %sx of this item in your inventory.',
	['deposit_item_success'] = 'You have deposited %sx of %s into this storage.',
	['withdraw_item_success'] = 'You have withdrawn %sx of %s from this storage.',
	['you_dont_have_x_item'] = 'You do not have %s. Required amount: %s pcs.',
	['you_need_x_amount_item'] = 'You only have %s pcs. of %s - Required amount: %s pcs.',
	['you_crafted_x_item'] = 'You crafted %sx %s.',
	['order_parts_insufficient_funds'] = 'Insufficient funds in shop account.',
	['engine_swap_insufficient_funds'] = 'Insufficient funds in shop account.',
	['no_laptop_shop_items'] = 'No items to display in the laptop shop order menu.',
	['parts_sent_to_storage'] = 'Your ordered parts have been delivered to the storage.',
	['veh_must_be_repaired_to_mod'] = 'Vehicle must be repaired to begin installing mods.',
	['paid_repair_cost'] = 'You paid %s to repair the vehicle.',
	['vehicle_mods_paid'] = 'You paid: %s for your vehicle modification order!',
	['vehicle_mods_applied'] = 'Selected mods has been applied to your vehicle!',
	['vehicle_mods_not_enough_money'] = 'Not enough money in your bank-account to pay for all these mods. Maybe remove some of the mods?',
	['cannot_submit_mod_order'] = 'You already have an existing mod order, please delete it first to submit a new mod order.',
	['mod_order_has_been_placed'] = 'You have placed your mod order and paid: %s',
	['mod_order_has_been_cancelled'] = 'You have cancelled your mod order and received: %s',
	['mod_order_has_been_refunded'] = 'You refunded the mod order. %s has been added to the bank account of: %s.',
	['mod_order_in_progress'] = 'A mod order for this vehicle is already in progress!',
	['mod_order_completed'] = 'You successfully completed the mod-order. Good job!',
	['no_mod_orders_to_show'] = 'No available mod orders to display.',
	['no_categories_to_show'] = 'No mod categories to display.',
	['vehicle_no_mod_category'] = 'The vehicle has no available mods in this category: %s.',
	['no_variants_for_mod'] = 'No mod variants available for this mod.',
	['mod_variant_already_installed'] = 'Selected mod variant is already installed.',
	['need_tuner_job'] = 'You don\'t have a tuner job, please go on duty or get hired in a shop.',
	['move_closer_mod_interaction'] = 'Move closer to the %s.',
	['no_bills_to_show'] = 'No bills have been invoiced yet to show.',
	['you_declined_bill'] = 'You declined a bill of $%s from: %s.',
	['you_paid_a_bill'] = 'You paid a bill of $%s from: %s.',
	['bill_sent_to_x'] = 'You sent a bill of $%s to %s.',
	['bill_x_no_money'] = '%s cannot cover the cost of the bill.',
	['bill_paid_by_x'] = '%s has paid your bill of $%s.',
	['bill_declined_by_x'] = '%s has declined to pay your bill of $%s.',
	['carjack_not_raised'] = 'Vehicle must be raised on a carjack to do this!',
	['no_jobs_available'] = 'No available jobs to request!',
	['jobs_check_back_later'] = 'Check back later. There are no available positons right now.',
	['have_ongoing_job'] = 'You already have an ongoing job. Please complete/cancel the job before retrieving a new job!',
	['job_cooldown_1'] = 'Another job is available for you in: %s seconds',
	['job_cooldown_2'] = 'Another job is available for you in: %s minutes',
	['already_salvaging_part'] = 'You are already salvaging a part, please finish!',
	['salvage_move_closer'] = 'You need to move closer to the part to salvage it!',
	['salvage_success_msg'] = 'You have succesfully salvaged all necessary parts from the junk car.',
	['salvage_instructions'] = 'Salvage the vehicle by targetting the doors and wheels.',
	['salvage_veh_deleted'] = 'Vehicle has been deleted. Job cancelled. You moved too far away...',
	['mobile_tuning_npc_require'] = 'I need a %s. Can you sort it out, please?',
	['mobile_tuning_reward_msg'] = 'Thank you for the %s. Here\'s some money for you.',
	['mobile_tuning_wrong_part'] = 'Are you stupid? I still need a %s!',
	['mobile_tuning_instructions'] = 'Talk with the NPC to get the work details.',
	['mobile_tuning_veh_deleted'] = 'Vehicle has been deleted. Job cancelled. You moved too far away...',
	['engine_swap_already_have_task'] = 'You already have an ongoing engine swap task.',
	['engine_swap_order_placed'] = 'Shop\'s account has been debited by %s. Your %s will arrive shortly.',
	['engine_swap_prepare_vehicle'] = 'Prepare the current engine for extraction!',
	['engine_swap_use_hoist_extract'] = 'You can now extract the engine using a hoist.',
	['engine_swap_must_collect_hoist'] = 'You must collect the hoist, in order to complete the engine swap.',
	['engine_swap_already_carrying_hoist'] = 'You are already carrying the hoist!',
	['engine_swap_already_hoist_in_use'] = 'The hoist is currently in use, please wait.',
	['engine_swap_order_arrived'] = 'The ordered %s has arrived. Pick it up using your hoist.',
	['engine_swap_only_one_hoist'] = 'Calm down Einstein, you already have a hoist spawned!',
	['engine_swap_must_start_task'] = 'You must have an ongoing engine swap task in order to use this!',
	['nitrous_kit_install_success'] = 'Successfully installed a nitrous kit',
	['burst_kit_install_success'] = 'Successfully installed a burst kit',
	['ongoing_kit_install'] = 'You already have an ongoing task.',
	['not_inside_correct_vehicle'] = 'You are not inside the correct vehicle!',
	['nitrous_insufficient_funds'] = 'Insufficient funds in shop account.',
	['is_refilling_nos_bottle'] = 'You can refill only one bottle at a time.',
	['must_be_near_nos_gas_tank'] = 'You must be near the nitrous gas tank to refill the bottle.',
	['refill_nos_gas_tank_empty'] = 'This nitrous gas tank is empty, please find another one.',
	['is_using_nos_bottle'] = 'You can use only one nitrous bottle at a time.',
	['cannot_drive_load_nos_bottle'] = 'You cannot drive and load nitrous gas. Hold still or ask a passenger to load it.',
	['vehicle_no_nitrous_kit'] = 'This vehicle does not have a nitrous kit installed. Visit a tuner.',
	['vehicle_nitrous_kit_full'] = 'The vehicle\'s nitrous kit tank is full',
	['vehicle_used_nos_bottle'] = 'Filled 1lb of nitrous. Total NOS Shots: %s/%s',
	['nos_purge_threshold'] = 'NOS Purge Threshold: %s',
	['vehicle_nos_kit_empty'] = 'Vehicle NOS tank is empty!',
	['nos_gas_leak'] = 'NOS GAS LEAK! You\'ve lost %s%% of your nitrous shots.',
	['vehicle_purge_not_stationary'] = 'Vehicle must be stationary to purge!',
	['nos_purge_cooldown'] = 'Purge failed. Let the NOS rest or you\'ll risk gas leak.',
	['nos_shots_consumed'] = 'Consumed %s NOS Shots | Tank: %s/%s',
	['nos_purge_efficiency'] = 'NOS Purge Efficiency: %s%%',
	['nos_purge_aborted'] = 'Purge aborted, NOS is resting (cooldown)',
	['nos_burst_threshold'] = 'NOS Burst Threshold: %s',
	['nos_burst_engine_damage'] = 'Due to stressing the NOS, the engine took damage!',
	['nos_burst_activate_fail_engine'] = 'Engine is too much damaged to activate NOS Burst',
	['nos_burst_cooldown'] = 'NOS Burst Cooldown!',
	['nos_chance_engine_damage'] = 'The engine has been damaged due to improper nitrous purge',
	['injected_purge_dye_success'] = 'Successfully injected NOS Purge Dye',
	['is_using_purge_dye'] = 'You can use only one nitrous purge dye at a time.',
	['closest_veh_no_nitrous_kit'] = 'The closest vehicle does not have a nitrous kit installed.',
	['access_eng_bay_to_inject_purge_dye'] = 'Access the engine bay to inject the NOS Purge Dye.',

	-- Advanced Notifications:
	['adv_junk_car_located'] = 'We have located a junk car that might have some valuable parts to be salvaged.',
	['adv_gps_updated'] = '~y~GPS UPDATED~s~',
	['adv_scrapyard'] = 'SCRAPYARD',
	['adv_mobile_tuning_service'] = 'MOBILE TUNING SERVICE',
	['adv_mobile_tuning_located'] = 'A customer needs a new %s. Bring Item: %s.',
	
	-- Context | Title:
	['title_yes'] = 'Yes',
	['title_no'] = 'No',
	['title_tuning_admin'] = 'Tuning System [Admin]',
	['title_tuning_player'] = 'Player Tuning Menu',
	['title_admin_view_shops'] = 'View Tuner Shops',
	['title_admin_create_shop'] = 'Create Tuner Shop',
	['title_admin_manage_shop'] = 'Manage Shop: %s [%s]',
	['title_admin_set_boss'] = 'Set Boss',
	['title_admin_set_account'] = 'Set Account Balance',
	['title_admin_set_mod_categories'] = 'Set Mod Categories',
	['title_admin_marker_manage'] = 'Marker Management',
	['title_admin_set_delivery_coords'] = 'Set Delivery Coords',
	['title_admin_delete_shop'] = 'Delete Shop',
	['title_admin_remove_boss'] = 'Remove Current Boss',
	['title_admin_create_marker'] = 'Create Marker',
	['title_admin_view_markers'] = 'View Markers',
	['title_admin_manage_marker'] = 'Manage: %s',
	['title_admin_teleport_marker'] = 'Teleport',
	['title_admin_edit_marker'] = 'Edit',
	['title_admin_delete_marker'] = 'Delete',
	['title_toggle_duty_menu'] = 'Toggle Duty',
	['title_duty'] = 'Duty',
	['title_on_duty_state'] = 'On Duty: %s',
	['title_clock_in_out'] = 'Clock In/Out',
	['title_boss_menu'] = 'Boss Menu',
	['title_boss'] = 'Boss',
	['title_tuner_account'] = 'Account',
	['title_tuner_employees'] = 'Employees',
	['title_tuner_markup'] = 'Markup',
	['title_account_balance'] = 'Account Balance',
	['title_account_deposit'] = 'Deposit',
	['title_account_withdraw'] = 'Withdraw',
	['title_manage_employees'] = 'Manage Employees',
	['title_recruit_employees'] = 'Recruit Employees',
	['title_fire_employee'] = 'Fire Employee',
	['title_promote_emplotee'] = 'Promote Employee',
	['title_accept_recruitment'] = 'Accept Recruitment From %s',
	['title_garage_menu'] = 'Garage Menu',
	['title_garage'] = 'Garage',
	['title_garage_get_veh'] = 'Get Vehicle',
	['title_garage_store_veh'] = 'Store Vehicle',
	['title_garage_vehicles'] = 'Vehicles',
	['title_storage'] = 'Storage',
	['title_withdraw_storage'] = 'Withdraw Items',
	['title_deposit_storage'] = 'Deposit Items',
	['title_workbench'] = 'Workbench',
	['title_laptop'] = 'Laptop',
	['title_customs'] = 'Customs',
	['title_tuningbay'] = 'Tuning Bay',
	['title_tuningbay2'] = 'Tuning Bay [%s]',
	['title_repair_no'] = '[NO] - Return',
	['title_repair_yes'] = '[YES] - Repair: %s',
	['title_install'] = 'Install',
	['title_stock_variant_label'] = 'Stock',
	['title_mod_installed'] = 'Installed',
	['title_tuner_tablet'] = 'Tuner Tablet',
	['title_tablet_vehicle_info'] = 'Vehicle Info',
	['title_tablet_examine_performance'] = 'Examine Performance Parts',
	['title_tablet_dyno_tuning'] = 'Dyno Tuning',
	['title_tablet_mod_orders'] = 'Mod Orders',
	['title_mod_order_list'] = 'Order #%s [%s]',
	['title_mod_order_customer'] = 'Customer: %s',
	['title_mod_order_paid_amount'] = 'Paid Amount: %s',
	['title_mod_order_begin_work'] = 'Begin Work',
	['title_mod_order_stop_work'] = 'Stop Work',
	['title_mod_order_refund'] = 'Refund',
	['title_tuningbay_mod_order'] = 'Mod Order - %s',
	['title_tuningbay_view_mod_order'] = 'View Mod Order',
	['title_engine_swap'] = 'Engine Swap',
	['title_billing'] = 'Billing',
	['title_npc_jobs'] = 'NPC Jobs',
	['title_npc_job_cancel_job_x'] = 'Cancel: %s',
	['title_create_bill'] = 'Create Bill',
	['title_view_bills'] = 'View Bills',
	['title_bill_overview'] = '%s | $%s',
	['title_bill_respond'] = 'Pay Bill: $%s',
	['title_nos_main_menu'] = 'Nitrous Oxide Systems',
	['title_current_nos_kit'] = 'Current Nitrous Kit',
	['title_nitrous_kits'] = 'Nitrous Kits',
	['title_burst_upg_kits'] = 'Burst Upgrade Kits',
	['title_dyno_view_modifiers'] = 'View Modifiers',
	['title_dyno_fine_tuning'] = 'Fine Tuning',

	-- Context | Description:
	['desc_admin_set_boss'] = 'Set, remove or assign a new boss for the shop',
	['desc_admin_set_account'] = 'Current Balance: $%s',
	['desc_admin_set_mod_categories'] = 'Click to set/update the shop\'s mod categories / field of work.',
	['desc_admin_marker_management'] = 'Manage shop markers; create, teleport or delete.',
	['desc_admin_set_delivery_coords'] = 'Click to set/update the shop\'s delivery coords.',
	['desc_admin_delete_shop'] = 'Delete the shop',
	['desc_admin_remove_boss'] = 'Click to remove current boss of the shop',
	['desc_admin_click_to_set_boss'] = 'Click to set player as boss for the shop.',
	['desc_admin_marker_create'] = 'Click to create a marker with given class.',
	['desc_admin_marker_view'] = 'Click to view all created markers.',
	['desc_admin_marker_manage'] = 'Click to manage the selected marker to update, delete or teleport.',
	['desc_admin_marker_teleport'] = 'Click to teleport to this marker.',
	['desc_admin_marker_edit'] = 'Click to open the marker creator to edit all values, except class.',
	['desc_admin_marker_delete'] = 'Click to delete this marker permanently.',
	['desc_manage_employees'] = 'View and manage employees.',
	['desc_fire_employee'] = 'Click to fire employee from the shop',
	['desc_promote_employee'] = 'Click to promote employee job grade',
	['desc_recruit_employee'] = 'Click to recruit employee.',
	['desc_withdraw_storage'] = 'View or withdraw items from storage.',
	['desc_deposit_storage'] = 'Deposit items from your inventory into storage.',
	['desc_tablet_examine_performance'] = 'View currently installed performance mods.',
	['desc_tablet_mod_orders'] = 'View mod orders submitted by customers.',
	['desc_tuningbay_view_mod_orders'] = 'Begin/stop working on the mod order',
	['desc_tuningbay_engine_swap'] = 'Order an engine for this vehicle to alter the vehicle sound',
	['desc_npc_job_cancel'] = 'You have an ongoing job. Click to cancel it.',
	['desc_current_nos_kit'] = 'Hover to view specs of current installed nitrous kit',
	['desc_nitrous_kits'] = 'Place an order for a Nitrous Kit with given size in lb and install it to the vehicle',
	['desc_burst_kits'] = 'Modify threshold seconds for NOS Burst/Purge with kits',
	['desc_tuningbay_nitrous'] = 'Order different nitrous Kits or upgrade burst Kits',
	

	-- Context | Input Titles:
	['input_title_shop_creation'] = 'Tuner Shop Creation',
	['input_title_set_account_balance'] = 'Set Account Balance',
	['input_title_set_mod_categories'] = 'Set Mod Categories',
	['input_title_create_marker'] = 'Create Marker',
	['input_title_withdraw'] = 'Withdraw: %s [%sx]',
	['input_title_deposit'] = 'Deposit: %s [%sx]',
	['input_title_order_parts'] = '%s - %s / part',
	['input_title_confirm_order_parts'] = 'Confirm: %sx %s for: %s',
	['input_title_laptop_select_storage'] = 'Select Storage',
	['input_title_rgb_color'] = 'RGB Color Picker',
	['input_title_order_nitrous_kit'] = 'Order Nitrous Kit',
	
	-- Context | Input Labels:
	['input_label_shop_name'] = 'Shop Name',
	['input_label_job_name'] = 'Job Name',
	['input_label_job_label'] = 'Job Label',
	['input_label_account'] = 'Account Start Balance',
	['input_label_select_categories'] = 'Select Mod Categories',
	['input_label_blip_coords'] = 'Blip Coords',
	['input_label_blip_enable'] = 'Enable Blip?',
	['input_label_blip_sprite'] = 'Blip Sprite',
	['input_label_blip_color'] = 'Blip Color',
	['input_label_cur_markup'] = 'Current Markup',
	['input_label_set_markup'] = 'Set Markup',
	['input_label_cur_balance'] = 'Current Balance',
	['input_label_enter_amount'] = 'Enter Amount',
	['input_label_marker_class'] = 'Class',
	['input_label_marker_name'] = 'Name',
	['input_label_marker_coords'] = 'Coords',
	['input_label_marker_type'] = 'Type',
	['input_label_marker_color'] = 'Color',
	['input_label_marker_bob'] = 'Bob Up And Down?',
	['input_label_marker_camera'] = 'Face Camera?',
	['input_label_marker_blip'] = 'Enable Blip?',
	['input_label_marker_slots'] = 'Stash Slots',
	['input_label_marker_weight'] = 'Stash Weight',
	['input_label_account_deposit_amount'] = 'Deposit Amount',
	['input_label_account_withdraw_amount'] = 'Withdraw Amount',
	['input_label_storage_deposit_amount'] = 'Deposit Amount',
	['input_label_storage_withdraw_amount'] = 'Withdraw Amount',
	['input_label_order_quantity'] = 'Quantity',
	['input_label_order_confirm'] = 'Confirm',
	['input_label_order_select_storage'] = 'Select which storage to receive ordered parts',
	['input_label_rgb_color'] = 'Colour Input',
	['input_label_bill_player'] = 'Player',
	['input_label_bill_amount'] = 'Bill Amount',
	['input_label_bill_note'] = 'Note',
	['input_label_bill_date'] = 'Date',
	['input_label_bill_time'] = 'Time',
	['input_label_unit_price'] = 'Unit (1lb) Price',
	['input_label_select_size'] = 'Select Size (lb)',

	-- Context | Input Descriptions:
	['input_desc_shop_name'] = 'Enter a shop name',
	['input_desc_job_name'] = 'Enter job name, ex.: lscustom or bennymotorworks2 (MUST BE LOWER CHAR, NO SPECIAL CHARS!!!)',
	['input_desc_job_label'] = 'Enter job label, ex.: LS Custom or Benny Motorworks',
	['input_desc_account'] = 'Enter amount of money this shop should start with',
	['input_desc_select_categories'] = 'Select mod categories that are allowed for this shop.',
	['input_desc_blip_coords'] = 'Blip coords based on player position.',
	['input_desc_blip_enable'] = 'Enable/Disable the blip on the map.',
	['input_desc_blip_sprite'] = 'Set blip sprite - check FiveM documentation for values.',
	['input_desc_blip_color'] = 'Set blip color - check FiveM documentation for values.',
	['input_desc_set_markup'] = 'Number value in percent between minimum %s and maximum of %s.',
	['input_desc_set_account_balance'] = 'Enter amount of money to set as the new account balance.',
	['input_desc_marker_class'] = 'Select the class of marker to create.',
	['input_desc_marker_name'] = 'Set a name for the marker, e.g. T1GERs Workbench.',
	['input_desc_marker_coords'] = 'Marker coords based on the current position of your player.',
	['input_desc_marker_type'] = 'Marker type that will be drawn in-game when nearby the marker.',
	['input_desc_marker_color'] = 'Set RGBA color of the marker to be displayed.',
	['input_desc_marker_bob'] = 'Whether or not the marker should slowly animate up/down.',
	['input_desc_marker_camera'] = 'Whether the marker should be a billboard, as in, should constantly face the camera.',
	['input_desc_marker_blip'] = 'Whether the marker should be visible on the map with a blip for respective players in question.',
	['input_desc_marker_slots'] = 'Set maximum number of slots available in the stash.',
	['input_desc_marker_weight'] = 'Set maximum weight allowed in the stash.',
	['input_desc_order_quantity'] = 'Enter amount of %s parts to order.',
	['input_desc_bill_player'] = 'Select a player to bill',
	['input_desc_bill_note'] = 'Enter notes for the bill',
	['input_desc_unit_price'] = 'Price of 1lb is: %s',

	-- Context | Meta Data:
	['meta_shop_id'] = 'ID',
	['meta_boss'] = 'Boss Name',
	['meta_boss_id'] = 'Boss Id',
	['meta_job_name'] = 'Job Name',
	['meta_not_answered'] = 'N/A',
	['meta_job_label_and_name'] = '%s [%s]',
	['meta_identifier'] = 'Identifier',
	['meta_player_char'] = 'Char',
	['meta_player_id'] = 'Player ID',
	['meta_cur_coords'] = 'Current Coords',
	['meta_marker_class'] = 'Class',
	['meta_marker_name'] = 'Name',
	['meta_marker_coords'] = 'Coords',
	['meta_job_grade'] = 'Grade',
	['meta_veh_make'] = 'Make',
	['meta_veh_model'] = 'Model',
	['meta_veh_plate'] = 'Plate',
	['meta_option_engine'] = 'Engine',
	['meta_option_body'] = 'Body',
	['meta_option_fuel'] = 'Fuel',
	['meta_bill_ref'] = 'Reference',
	['meta_bill_shop'] = 'Shop',
	['meta_bill_sender'] = 'Sender',
	['meta_bill_receiver'] = 'Receiver',
	['meta_bill_amount'] = 'Bill Amount',
	['meta_bill_date'] = 'Date',
	['meta_bill_time'] = 'Time',
	['meta_mod_order_item_price'] = 'Item Price',
	['meta_mod_order_labor_charge'] = 'Labor Charge',
	['meta_mod_order_total'] = 'Total',
	['meta_mod_order_part_acquisition'] = 'Part Acquisition',
	['meta_mod_order_markup_x'] = 'Markup (%s%%)',
	['meta_mod_order_customer_credit'] = 'Customer Acc. Credit',
	['meta_mod_order_shop_debit'] = 'Shop Acc. Debit',
	['meta_nos_size'] = 'Kit Size',
	['meta_nos_burst'] = 'Burst Kit',
	['meta_nos_shots'] = 'NOS Shots',
	['meta_nos_color'] = 'Purge RGB(0-1) Color',

	-- Options | Label:
	['options_label_cur_tyre_smoke'] = 'Current Tyre Smoke [%s, %s, %s]',
	['options_label_rgb_color'] = 'RGB Color',
	['options_label_browse_mods'] = 'Browse Mods',
	['options_label_purchase_mods'] = 'Purchase Mods',
	['options_label_apply_mods'] = 'Apply Mods',
	['options_label_create_mod_order'] = 'Create Mod Order',
	['options_label_delete_mod_order'] = 'Delete Mod Order',

	-- Options | Descriptions:
	['options_desc_tyre_smoke_preview'] = 'Accelerate on the vehicle to do burnout to preview the tyre smoke color',
	['options_desc_rgb_color'] = 'Pick color and then scroll to next button to install',
	['options_desc_repair_menu'] = 'Engine Health: %s | Body Health: %s',

	-- Options | Values:
	['options_value_pick_color_first'] = 'First Pick a Color',
	['options_value_pick_color_tool'] = 'Color Picker Tool',

	-- Alerts | Header:
	['alert_header_delete_shop'] = 'Delete Shop?',
	['alert_header_mods_checkout'] = '**__CHECKOUT - %s__**',
	['alert_header_mods_apply'] = 'Apply Mods',
	['alert_header_submit_mod_order'] = '**__SUBMIT MOD ORDER - %s__**',
	['alert_header_submit_mod_order2'] = '**__SUBMIT MOD ORDER - %s__**  \n\n ---  \n\n **Parts Acquisition:** %s  \n\n **Labor Charge:** %s  \n\n **Markup (%s%%):** %s',
	['alert_header_delete_mod_order'] = 'Delete Mod Order: +%s',
	['alert_header_engine_swap_order'] = 'Order: %s - %s',
	['alert_header_nitrous_kit'] = 'Nitrous Kit %slb - %s',
	['alert_header_burst_kit'] = '%s - %s',
	
	-- Alerts | Content:
	['alert_content_shop_delete_confirm'] = 'Are you sure you want to delete the shop: \n\n %s (%s)?',
	['alert_content_engine_swap_order'] = 'Are you sure you want to place an order for %s and pay %s?',
	['alert_content_delivery_coords'] = 'Confirm Delivery Coords: %s',
	['alert_content_nitrous_kit'] = 'Confirm to order a Nitrous Kit of %slb? \n\n %s will be debited from shop\'s account',
	['alert_content_burst_kit'] = 'Confirm to order a %s of %s threshold? \n\n %s will be debited from shop\'s account',

	-- Draw Text 3D:
	['textui_duty'] = '[E] - Toggle Duty',
	['textui_boss'] = '[E] - Boss Menu',
	['textui_garage'] = '[E] - Garage',
	['textui_storage'] = '[E] - Storage',
	['textui_workbench'] = '[E] - Workbench',
	['textui_laptop'] = '[E] - Laptop',
	['textui_customs'] = '[E] - Customs',
	['textui_tuningbay'] = '[E] - Tuning Bay',
}