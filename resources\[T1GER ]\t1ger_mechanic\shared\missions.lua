local locations = {}

locations["breakdown"] = {
    [1] = {
        pos = vector4(932.04, -62.5, 78.76, 85.98), -- npc vehicle spawn coords
        inUse = false, -- do not touch
        pedCoords = vector4(931.58, -59.92, 78.76, 135.83), -- npc ped spawn coords and model
        dropoff = vector3(-366.25, -124.89, 38.7), -- vehicle dropoff location
        payout = { min = 250,  max = 400 } -- payout cash minimum amount and maximum amount
    },
    [2] = {
        pos = vector4(99.43, 247.17, 108.19, 66.33), -- npc vehicle spawn coords
        inUse = false, -- do not touch
        pedCoords = vector4(97.83, 250.58, 108.38, 177.98), -- npc ped spawn coords and model
        dropoff = vector3(-218.37,-1296.19,31.3), -- vehicle dropoff location
        payout = { min = 250,  max = 400 } -- payout cash minimum amount and maximum amount
    },
    [3] = {
        pos = vector4(167.33, -1460.1, 29.14, 138.45), -- npc vehicle spawn coords
        inUse = false, -- do not touch
        pedCoords = vector4(171.92, -1458.62, 29.24, 126.2), -- npc ped spawn coords and model
        dropoff = vector3(716.32, -1080.13, 22.28), -- vehicle dropoff location
        payout = { min = 250,  max = 400 } -- payout cash minimum amount and maximum amount
    },
    -- add more locations
}

locations["roadsiderepair"] = {
    [1] = {pos = vector4(880.16, -34.66, 78.75, 240.94), inUse = false, payout = {min = 250, max = 400}},
    [2] = {pos = vector4(1492.09, 758.45, 77.45, 288.26), inUse = false, payout = {min = 250, max = 400}},
    [3] = {pos = vector4(387.67, -767.56, 29.29, 358.94), inUse = false, payout = {min = 250, max = 400}},
    [4] = {pos = vector4(-583.75, -239.55, 36.08, 33.14), inUse = false, payout = {min = 250, max = 400}},
    [5] = {pos = vector4(350.12, 345.60, 105.15, 73.70), inUse = false, payout = {min = 250, max = 400}},
    [6] = {pos = vector4(106.11, 317.55, 112.13, 340.15), inUse = false, payout = {min = 250, max = 400}},
    [7] = {pos = vector4(-250.24, 292.83, 91.79, 85.03), inUse = false, payout = {min = 250, max = 400}},
    [8] = {pos = vector4(-1325.35, 275.32, 63.41, 215.43), inUse = false, payout = {min = 250, max = 400}},
    [9] = {pos = vector4(-1622.20, -237.69, 53.93, 158.74), inUse = false, payout = {min = 250, max = 400}},
    [10] = {pos = vector4(-1273.45, -1010.42, 9.34, 195.59), inUse = false, payout = {min = 250, max = 400}},
}

locations["carscrapping"] = {
    [1] = {pos = vector4(-367.42, -1521.98, 26.72, 174.32), inUse = false},
    [2] = {pos = vector4(-12.45, -1799.43, 26.4, 317.32), inUse = false},
    [3] = {pos = vector4(587.51, -1791.45, 21.08, 172.84), inUse = false},
    [4] = {pos = vector4(1043.62, -2130.04, 31.74, 262.95), inUse = false},
    [5] = {pos = vector4(1160.78, -1651.02, 35.92, 203.69), inUse = false},
    [6] = {pos = vector4(1118.26, -975.1, 45.49, 8.07), inUse = false},
    [7] = {pos = vector4(1100.01, -332.12, 66.21, 124.12), inUse = false},
    [8] = {pos = vector4(700.74, 254.74, 92.31, 237.35), inUse = false},
    [9] = {pos = vector4(177.77, 380.25, 108.03, 356.36), inUse = false},
    [10] = {pos = vector4(-1155.14, -228.36, 36.9, 314.13), inUse = false},
    [11] = {pos = vector4(320.33, -2034.69, 20.17, -38.61), inUse = false},
    [12] = {pos = vector4(-520.84, 574.89, 120.56, -78.21), inUse = false},
    [13] = {pos = vector4(-1452.84, -51.61, 52.75, 73.59), inUse = false},
    [14] = {pos = vector4(361.42, -2474.03, 5.90, -91.64), inUse = false},
    [15] = {pos = vector4(815.73, -2144.15, 28.81, -172.18), inUse = false},
    [16] = {pos = vector4(1300.98, -1736.43, 53.38, -159.24), inUse = false},
    [17] = {pos = vector4(1260.82, -1739.77, 49.13, 116.28), inUse = false},
    [18] = {pos = vector4(1228.02, -1606.06, 51.18, 35.05), inUse = false},
    [19] = {pos = vector4(1201.94, -1387.29, 34.72, 19.94), inUse = false},
    [20] = {pos = vector4(-1106.47, 791.78, 164.17, 13.24), inUse = false},
    [21] = {pos = vector4(-683.99, 602.62, 143.10, -31.46), inUse = false},
    [22] = {pos = vector4(318.58, 495.42, 152.24, -72.93), inUse = false},
    [23] = {pos = vector4(-2165.52, -277.60, 12.22, 157.83), inUse = false},
    [24] = {pos = vector4(-1539.14, -1004.59, 12.51, -104.60), inUse = false},
    [25] = {pos = vector4(-996.22, -1262.95, 5.27, -61.32), inUse = false},
    [26] = {pos = vector4(-135.45, -1971.81, 22.30, -61.32), inUse = false},
    [27] = {pos = vector4(-177.49, -2022.95, 27.12, -106.34), inUse = false},
    [28] = {pos = vector4(-3044.68, 112.43, 11.21, 138.63), inUse = false},
    [29] = {pos = vector4(1364.97, -2065.10, 51.49, 117.16), inUse = false},
    [30] = {pos = vector4(-1104.28, -1953.91, 12.65, 132.38), inUse = false}
}

return locations