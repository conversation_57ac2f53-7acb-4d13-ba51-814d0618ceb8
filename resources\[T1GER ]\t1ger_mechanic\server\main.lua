onlineMechanics = {count = 0, players = {}}
mechanicJobs = {}

-- Event for player loaded:
RegisterNetEvent("t1ger_mechanic:server:playerLoaded", function()
    local src = source
    local player = _API.Player.GetFromId(src)

    -- wait
    Wait(1000)

    -- load shops:
    TriggerClientEvent("t1ger_mechanic:client:loadShops", src, Shops)

    -- load carlifts:
    EnsureCarLifts(src)

    if Config.Debug then
        print("playerId: "..src.." loaded", player)
    end

    local job = _API.Player.GetJob(src)
    if job and job.name and mechanicJobs[job.name] then
        PlusMechanicCount(src)
    end
end)

--- Event to keep track of mechanic count on job updates
RegisterNetEvent("t1ger_mechanic:server:onJobUpdate", function(jobName, onDuty)
    local src = source
    if not src or not jobName then return end

    local isMechanicJob = mechanicJobs[jobName]
    local isOnDuty = (onDuty == nil or onDuty == true) -- nil = on duty for ESX

    if onlineMechanics.players[src] then
        if not isMechanicJob or not isOnDuty then
            MinusMechanicCount(src)
        end
    else
        if isMechanicJob and isOnDuty then
            PlusMechanicCount(src)
        end
    end
end)

--- Event listener to deduct mechanic count on player drop
AddEventHandler('playerDropped', function(reason, resourceName, clientDropReason)
    local src = source
    if onlineMechanics.players[src] then
        MinusMechanicCount(src)
    end
end)

--- Callback to return online players
lib.callback.register("t1ger_mechanic:server:getOnlinePlayers", function(source)
    return _API.GetOnlinePlayers()
end)

--- Callback to create a networked object
lib.callback.register("t1ger_mechanic:server:createNetworkedObj", function(source, model, coords)
    return CreateNetworkedObject(model, coords)
end)

-- Event to store vehicle in garage:
RegisterNetEvent("t1ger_mechanic:server:vehicleIn", function(markerId, props)
    _API.SetVehicleStored(1, markerId, props)
end)

-- Event to retrieve vehicle from garage:
RegisterNetEvent("t1ger_mechanic:server:vehicleOut", function(markerId, props)
    _API.SetVehicleStored(0, markerId, props)
end)

-- Event to remove an item from the player
RegisterNetEvent("t1ger_mechanic:server:removeItem", function(itemName, itemAmount)
    local src = source

    if type(itemName) ~= "string" or itemName == "" then
        return error("[t1ger_mechanic:server:removeItem] Invalid itemName type. Must be a non-empty string")
    end
    
    if type(itemAmount) ~= "number" or itemAmount <= 0 then
        return error("[t1ger_mechanic:server:removeItem] Invalid itemAmount type. Must be a number bigger than 0")
    end

    _API.Player.RemoveItem(src, itemName, itemAmount)
end)

-- Event to delete prop/object for emotes
RegisterNetEvent("t1ger_mechanic:server:deletePropEmoteObject", function(netId, emote)
    local src = source

    local entity = NetworkGetEntityFromNetworkId(netId)
    while not DoesEntityExist(entity) do
        Wait(100)
        entity = NetworkGetEntityFromNetworkId(netId)
    end

    if type(emote) ~= "table" or not emote.item then 
        return 
    end

    -- return item to inventory
    _API.Player.AddItem(src, emote.item, 1)

    DeleteEntity(entity)
end)

--- Callback to get online mechanic count
lib.callback.register("t1ger_mechanic:server:getOnlineMechanicCount", function(source)
    return GetOnlineMechanicCount()
end)

-- callback to check if player has materials for crafting x item
lib.callback.register("t1ger_mechanic:server:repairStationPay", function(source, price)
    local src = source

    local playerAccount = _API.Player.GetMoney(src, "bank")
    if playerAccount and playerAccount > price then
        _API.Player.RemoveMoney(src, price, "bank")
        return true
    end
    
    return false
end)

--- event to return materials on component replacements
RegisterNetEvent("t1ger_mechanic:server:returnComponentMaterials", function(materials, partName, partType, formattedText)
    local src = source
    local player = _API.Player.GetFromId(src)
    if not player then return end

    if type(partName) ~= "string" or partName == "" then return end
    if type(partType) ~= "string" or partType == "" then return end

    local partConfig = nil
    local categoryIndex = nil

    if partType == "core_parts" then
        partConfig = Config.CoreParts[partName]
        categoryIndex = 2
    elseif partType == "service_parts" then
        partConfig = Config.ServiceParts[partName]
        categoryIndex = 3
    else
        return
    end

    if not partConfig or not categoryIndex then return end

    local itemName = partConfig.item
    local category = Config.Shop.Workbench.categories[categoryIndex]
    if not category or not category.recipe or not category.recipe[itemName] then return end

    local recipe = category.recipe[itemName].materials
    if type(recipe) ~= "table" then return end

    local materialConfig = Config.Materials

    -- Validate materials
    for material, amount in pairs(materials) do
        -- Check if material is valid
        if not recipe[material] then
            print(string.format("Player %s (%s) tried to send invalid material: %s", GetPlayerName(src), _API.Player.GetIdentifier(src), material))
            return -- immediately cancel
        end

        -- check amount
        if amount <= 0 or amount > recipe[material] then
            print(string.format("Player %s (%s) tried to send invalid amount for %s: %s", GetPlayerName(src), src, material, amount))
            return
        end

        -- Cross-check: Make sure material label appears inside formattedText
        if materialConfig[material] and not string.find(string.lower(formattedText), string.lower(materialConfig[material])) then
            print(string.format("Player %s (%s) tried to send mismatched formattedText for %s", GetPlayerName(src), _API.Player.GetIdentifier(src), material))
            return
        end

        -- Give material
        _API.Player.AddItem(src, material, amount)
    end

    -- Optional: notify
    if type(formattedText) == "string" and Config.Components.Repair.materialReturn.notify then
        _API.SendNotification(src, string.format(locale("notification.component_salvage", formattedText)), "inform", {duration = 5000})
    end
end)

--- Event to handle body repair ptfx
RegisterServerEvent("t1ger_mechanic:server:bodyRepairPtfx", function(dict, name, pos, rot, scale)
    if not dict then
        TriggerClientEvent("t1ger_mechanic:client:bodyRepairPtfx", -1)
    else
        TriggerClientEvent("t1ger_mechanic:client:bodyRepairPtfx", -1, dict, name, pos, rot, scale)
    end
end)

--- Server event to log a service/repair entry in the database.
--- Ensures the vehicle is owned before logging, and uses mechanic job or shop label for context.
RegisterServerEvent("t1ger_mechanic:server:addServiceHistory", function(plate, partLabel, mileage)
    local src = source

    -- Get player identifier
    local identifier = _API.Player.GetIdentifier(src)

    -- Determine the mechanic's shop label (custom shop or job-based)
    local shopLabel = ""
    local isMechanic, shopId = exports["t1ger_mechanic"]:IsPlayerEmployee(src)
    if isMechanic and type(shopId) == "number" and Shops[shopId] then
        shopLabel = Shops[shopId].name
    else
        local playerJob = _API.Player.GetJob(src)
        shopLabel = playerJob and playerJob.label or "Unknown"
    end

    -- Call export to insert service log
    local added = exports["t1ger_mechanic"]:AddServiceHistory(plate, partLabel, mileage, identifier, shopLabel)
    if added and Config.Debug then
        print(("[ServiceHistory] %s at %s serviced '%s' on %s (mileage: %s)"):format(
            identifier,
            shopLabel,
            partLabel,
            plate,
            mileage
        ))
    end
end)

--- Event to remove car jack object
RegisterServerEvent("t1ger_mechanic:server:removeCarJack", function(netId, carJackCoords)
    local src = source

    -- get car jack entity:
    local carJackEntity = NetworkGetEntityFromNetworkId(netId)
    while not DoesEntityExist(carJackEntity) do
        carJackEntity = NetworkGetEntityFromNetworkId(netId)
        Wait(100)
    end

    -- check carjack statebag:
    local entState = Entity(carJackEntity).state
    local carJackInfo = entState["t1ger_mechanic:carJackInfo"]
    if not carJackInfo then return end

    -- check if parsed coords and entity coords are far from each other? 
    local entCoords = GetEntityCoords(carJackEntity)
    if #(entCoords - carJackCoords) > 2.0 then return end

    -- check if player is near the carjack entity
    local pedCoords = GetEntityCoords(GetPlayerPed(src))
    if #(pedCoords - entCoords) > 5.0 then return end

    -- get vehicle attached to carjack
    local vehicle = NetworkGetEntityFromNetworkId(carJackInfo.vehicleNetId)
    while not DoesEntityExist(vehicle) do
        vehicle = NetworkGetEntityFromNetworkId(carJackInfo.vehicleNetId)
        Wait(100)
    end

    -- unfreeze vehicle:
    FreezeEntityPosition(vehicle, false)
    ApplyForceToEntity(vehicle, 0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, false, true, true, false, true)

    -- update vehicle onCarJack statebag
    local vehState = Entity(vehicle).state
    vehState:set("t1ger_mechanic:onCarJack", false, true)

    -- return item to player
    _API.Player.AddItem(src, Config.CarJack.Item, 1)

    -- delete entity
    DeleteEntity(carJackEntity)
end)

--- Registers useable items throughout the script:
function RegisterUseableItems()
    -- prop emotes
    for _, prop in pairs(Config.Emotes.Props) do
        _API.Inventory.RegisterUseableItem(prop.item, function(src, item)
            TriggerClientEvent("t1ger_mechanic:client:propEmote", src, prop.item)
        end)
    end

    -- repair kit
    for repairKit, data in pairs(Config.RepairKits.Types) do
        _API.Inventory.RegisterUseableItem(data.item, function(src, item)
            TriggerClientEvent("t1ger_mechanic:client:useRepairKit", src, repairKit)
        end)
    end

    -- patch kit
    _API.Inventory.RegisterUseableItem(Config.PatchKit.Item, function(src, item)
        TriggerClientEvent("t1ger_mechanic:client:usePatchKit", src)
    end)

    -- diagnostic tool
    _API.Inventory.RegisterUseableItem(Config.Components.Diagnostic.item, function(src, item)
        TriggerClientEvent("t1ger_mechanic:client:useDiagnosticTool", src)
    end)

    -- useable components (core parts)
    for partName, partData in pairs(Config.CoreParts) do
        _API.Inventory.RegisterUseableItem(partData.item, function(src, item)
            TriggerClientEvent("t1ger_mechanic:client:useComponentItem", src, partName, "core_parts")
        end)
    end

    -- useable components (service parts)
    for partName, partData in pairs(Config.ServiceParts) do
        _API.Inventory.RegisterUseableItem(partData.item, function(src, item)
            TriggerClientEvent("t1ger_mechanic:client:useComponentItem", src, partName, "service_parts")
        end)
    end

    -- body parts:
    for partName, partData in pairs(Config.BodyParts) do
        _API.Inventory.RegisterUseableItem(partData.item, function(src, item)
            TriggerClientEvent("t1ger_mechanic:client:useBodyPartItem", src, partName)
        end)
    end

    -- car jack:
    _API.Inventory.RegisterUseableItem(Config.CarJack.Item, function(src, item)
        TriggerClientEvent("t1ger_mechanic:client:useCarJack", src)
    end)

    -- service book item:
    _API.Inventory.RegisterUseableItem(Config.ServiceHistory.Item, function(src, item)
        TriggerClientEvent("t1ger_mechanic:client:useServiceBook", src)
    end)
end




---Bandi
---
local socNames = {}
function GetClosestMecShop(coords)
	local nearestDistance = 9999
	local nearest = 0
  local nearestSoc = 'none'
	local pedCoords = coords
    if next(Shops) ~= nil then
      for id, shop in pairs(Shops) do
			  local shopDistance = #(pedCoords - vector3(shop.blip.coords.x, shop.blip.coords.y, shop.blip.coords.z))
			  if  shopDistance < nearestDistance then
				  nearestDistance = shopDistance
				  nearest = id
          nearestSoc = shop.job.name
			  end
      end
    end
	return nearest, nearestSoc
end
exports('GetClosestMecShop', GetClosestMecShop)


