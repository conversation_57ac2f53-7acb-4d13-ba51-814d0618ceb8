Config.UseBuiltInCrafting = true -- otherwise change in functions of tuning system

Config.Workbench = {
	Enable = true, -- set to false to disable
	Duration = 2500, -- duration in ms for crafting
	ProgressLabel = 'Crafting: %s',
	Anim = {dict = 'mini@repair', name = 'fixing_a_player', blendIn = 8.0, blendOut = -8.0, flag = 49},
	SkillCheck = {enable = true, difficulty = {'easy', 'easy'}, inputs = {'w', 'a', 's', 'd'}},
    
	Categories = {
		{ -- PERFORMANCE
			label = 'Performance',
			description = 'Performance mods are used to upgrade vehicle\'s performance.',
			icon = 'https://i.ibb.co/b5gZqr2/mod-brakes.png',
			recipe = {
				['mod_engine'] = { -- output item name
					['aluminium'] = 6, -- material name and amount required
					['steel'] = 4, -- material name and amount required
					['copper'] = 3, -- material name and amount required
					['carbon_fiber'] = 2, -- material name and amount required
					['electric_scrap'] = 2 -- material name and amount required
				},
				['mod_brakes'] = {
					['steel'] = 3,
					['rubber'] = 2,
					['copper'] = 1,
					['carbon_fiber'] = 1
				},
				['mod_transmission'] = {
					['steel'] = 4,
					['aluminium'] = 2,
					['copper'] = 3,
					['synthetic_oil'] = 1,
					['plastic'] = 2
				},
				['mod_suspension'] = {
					['steel'] = 3,
					['rubber'] = 2,
					['aluminium'] = 2,
					['carbon_fiber'] = 1
				},
				['mod_armor'] = {
					['steel'] = 6,
					['carbon_fiber'] = 3,
					['rubber'] = 2,
					['brass'] = 1,
					['aluminium'] = 4
				},
				['mod_turbo'] = {
					['steel'] = 2,
					['copper'] = 2,
					['aluminium'] = 1,
					['plastic'] = 1,
					['electric_scrap'] = 1
				},
			}
		},
		{ -- Cosmetics
			label = 'Cosmetics',
			description = 'Cosmetic mods are used to customize the vehicle\'s appearance.',
			icon = 'https://i.ibb.co/dQYTKWk/mod-respray.png',
			recipe = {
				['mod_exhaust'] = {
					['steel'] = 3,
					['aluminium'] = 2,
					['rubber'] = 1,
				},				
				['mod_extras'] = {
					['plastic'] = 3,
					['aluminium'] = 1,
					['electric_scrap'] = 1,
				},				
				['mod_exterior'] = {
					['aluminium'] = 4,
					['plastic'] = 3,
					['glass'] = 1,
				},				
				['mod_interior'] = {
					['plastic'] = 4,
					['rubber'] = 2,
					['synthetic_oil'] = 1,
				},				
				['mod_fender'] = {
					['steel'] = 3,
					['plastic'] = 2,
					['aluminium'] = 1,
				},				
				['mod_frame'] = {
					['steel'] = 5,
					['aluminium'] = 3,
					['carbon_fiber'] = 2,
				},				
				['mod_frontbumper'] = {
					['steel'] = 4,
					['plastic'] = 3,
					['rubber'] = 1,
				},				
				['mod_grille'] = {
					['steel'] = 2,
					['aluminium'] = 2,
					['plastic'] = 1,
				},				
				['mod_hood'] = {
					['steel'] = 3,
					['aluminium'] = 2,
					['carbon_fiber'] = 1,
				},				
				['mod_horn'] = {
					['copper'] = 2,
					['plastic'] = 2,
					['electric_scrap'] = 1,
				},				
				['mod_light'] = {
					['glass'] = 2,
					['plastic'] = 2,
					['electric_scrap'] = 2,
				},				
				['mod_livery'] = {
					['plastic'] = 3,
					['synthetic_oil'] = 2,
					['acid'] = 1,
				},				
				['mod_neon'] = {
					['plastic'] = 3,
					['glass'] = 2,
					['electric_scrap'] = 2,
				},				
				['mod_plate'] = {
					['aluminium'] = 2,
					['plastic'] = 2,
					['steel'] = 1,
				},				
				['mod_rearbumper'] = {
					['steel'] = 4,
					['plastic'] = 3,
					['rubber'] = 1,
				},				
				['mod_respray'] = {
					['synthetic_oil'] = 2,
					['acid'] = 2,
					['plastic'] = 1,
				},				
				['mod_rim'] = {
					['aluminium'] = 4,
					['steel'] = 2,
					['rubber'] = 1,
				},				
				['mod_roof'] = {
					['steel'] = 3,
					['aluminium'] = 2,
					['carbon_fiber'] = 1,
				},				
				['mod_sideskirt'] = {
					['plastic'] = 3,
					['steel'] = 2,
					['aluminium'] = 1,
				},				
				['mod_spoiler'] = {
					['carbon_fiber'] = 3,
					['aluminium'] = 2,
					['plastic'] = 1,
				},				
				['mod_tyresmoke'] = {
					['synthetic_oil'] = 2,
					['rubber'] = 2,
					['acid'] = 1,
				},				
				['mod_windowtint'] = {
					['plastic'] = 2,
					['synthetic_oil'] = 1,
					['acid'] = 1,
				},
			}
		},
		{ -- KITS
			label = 'Kits',
			description = 'Kits are used by tuners for different stuff.',
			icon = 'https://i.ibb.co/Kr1C5fD/tuner-tablet.png',
			recipe = {
				['tuner_tablet'] = {
					['plastic'] = 3,
					['electric_scrap'] = 4,
					['glass'] = 2,
					['copper'] = 2,
					['aluminium'] = 1,
				},
				['tuner_repairkit'] = {
					['steel'] = 2,
					['plastic'] = 2,
					['rubber'] = 1,
					['synthetic_oil'] = 1,
					['electric_scrap'] = 1,
				},
				['tuner_enghoist'] = {
					['steel'] = 5,
					['scrap_metal'] = 3,
					['rubber'] = 2,
					['carbon_fiber'] = 1,
					['synthetic_oil'] = 1,
					['brass'] = 1,
				},
				['nos_empty_bottle'] = {
					['steel'] = 3,
					['aluminium'] = 2,
					['carbon_fiber'] = 1,
					['synthetic_oil'] = 1,
					['rubber'] = 1,
					['acid'] = 1,
				},
				['nos_purge_dye'] = {
					['synthetic_oil'] = 6,
					['acid'] = 8,
					['plastic'] = 1,
					['copper'] = 1,
					['glass'] = 2
				},
			},
		},
		{ -- Custom
			label = 'Custom',
			description = 'Custom items can be crafted here.',
			icon = 'https://i.ibb.co/vQXJgJd/mod-bullettires.png',
			recipe = {
				['mod_bullettires'] = {
					['rubber'] = 5,
					['steel'] = 4,
					['carbon_fiber'] = 3,
					['synthetic_oil'] = 2,
				},				
				['mod_drifttires'] = {
					['rubber'] = 6,
					['steel'] = 2,
					['synthetic_oil'] = 2,
					['plastic'] = 1,
				},				
				['mod_stocktires'] = {
					['rubber'] = 4,
					['steel'] = 1,
					['synthetic_oil'] = 1,
				},
			},
		},
	}
}