Config = {}

-- Core Framework Configuration
Config.core = "ESX"  -- Set to "qb-core", "ESX", or "VRP" based on the core framework in use.
-- This setting is only used in the payment event located in `server/editable-main.lua`.


-- Language Settings
Config.language = Spanish  -- Language used in menus and notifications (choose from available languages or add custom ones in the "/locales" folder)
-- Available languages: English, Arabic, Chinese, Spanish, French, German


--------------------
-- BUS CONFIGURATION
--------------------
Config.ENABLE_AI_BUS = true  -- Enables AI-driven buses in the game.
Config.ENABLE_BUS_BLIPS = false  -- Show blips on the map for bus routes.
Config.BLIP_CODE_BUS = 513
Config.BLIP_COLOR_BUS = 2
Config.BLIP_SIZE_BUS = 0.6

-- List of bus routes and their configurations.
Config.BusRoutes = {
    {
        -- The display name for the bus route.
        Name = "Central Los Santos", 
        
        -- Stations (waypoints) along the route where the bus stops.
        Stations = {
            vector3(771.9880, -941.0090, 25.6858),  -- Station 1 coordinates
            vector3(788.3823, -1369.4036, 26.5249), -- Station 2 coordinates
            vector3(826.5194, -1639.2437, 30.2806), -- Station 3 coordinates
            vector3(-106.8626, -1688.0312, 29.2746),-- Station 4 coordinates
            vector3(-1025.9010, -2716.6067, 13.8181),-- Station 5 coordinates
            vector3(-1413.1652, -575.4164, 30.4558), -- Station 6 coordinates
            vector3(-1167.8768, -1470.7745, 4.3238), -- Station 7 coordinates
            vector3(-740.5593, -751.2394, 26.7201),  -- Station 8 coordinates
            vector3(-506.4474, -667.1445, 33.0506),  -- Station 9 coordinates
            vector3(-242.5479, -717.4520, 33.4376),  -- Station 10 coordinates
            vector3(-249.4603, -881.6628, 30.6583),  -- Station 11 coordinates
            vector3(535.0941, -232.4176, 49.9217),   -- Station 12 coordinates
            vector3(903.0831, -137.8177, 76.6008),   -- Station 13 coordinates
            vector3(915.7531, -262.3591, 68.4988),   -- Station 14 coordinates
        },
        
        -- The bus model used for this route, defined by its spawn name.
        BusModel = "bus",  -- Standard in-game bus model.

        -- Maximum speed limit for the bus in in-game units.
        Speed = 100.0, 

        -- AI driving behavior, represented by a driving style ID.
        DrivingStyle = 319,  -- 319 represents cautious driving.

        -- Duration (in seconds) for which the bus will wait at each station.
        WaitTime = 20, 

        -- Price of a bus ticket for the route.
        Price = 10,  -- Set to 10 currency units for this route.
    },

    {
        -- The display name for the bus route.
        Name = "All Towns", 
        
        -- Stations (waypoints) along the route where the bus stops.
        Stations = {
            vector3(-2100.4797, -191.5894, 20.9693),  -- Station 1 coordinates
            vector3(-151.7412, 6215.0864, 31.1955), -- Station 2 coordinates
            vector3(1694.3108, 4956.8174, 43.1916), -- Station 3 coordinates
            vector3(1716.3094, 3586.5608, 35.4347),-- Station 4 coordinates
            vector3(623.2917, 2694.8955, 41.1186),-- Station 5 coordinates
            vector3(2281.1509, 2995.8806, 46.1430), -- Station 6 coordinates
            vector3(1950.2861, 2548.8733, 55.4063), -- Station 7 coordinates
            vector3(113.1231, -785.4324, 31.3993), -- Station 8 coordinates
        },
        
        -- The bus model used for this route, defined by its spawn name.
        BusModel = "coach",  -- Standard in-game bus model.

        -- Maximum speed limit for the bus in in-game units.
        Speed = 20.0, 

        -- AI driving behavior, represented by a driving style ID.
        DrivingStyle = 319,  -- 319 represents cautious driving.

        -- Duration (in seconds) for which the bus will wait at each station.
        WaitTime = 50, 

        -- Price of a bus ticket for the route.
        Price = 100,  -- Set to 100 currency units for this route.
    },
}

----------------------
-- PLANE CONFIGURATION
----------------------
Config.ENABLE_AI_PLANES = true  -- Enables AI-controlled planes in the game.
Config.ENABLE_PLANES_BLIPS = false  -- Show blips on the map for plane routes.
Config.BLIP_CODE_PLANE = 307
Config.BLIP_COLOR_PLANE = 2
Config.BLIP_SIZE_PLANE = 0.6
-- List of plane routes and their configurations.
Config.PlaneRoutes = {
    {
        Name = "LS International to Sandy Shores",  -- Display name for the plane route.
        
        -- Configuration for the plane's starting and finishing runways.
        start = {
            runwayStart = vector3(-1290.3481, -2879.7759, 14.5473),  -- Starting point at LSIA runway.
            runwayEnd = vector3(-1499.2947, -2761.7888, 13.9449),    -- Ending point at LSIA runway.
        },
        finish = {
            runwayStart = vector3(1062.9083, 3011.6797, 41.8074),    -- Starting point at Sandy Shores runway.
            runwayEnd = vector3(1474.9471, 3121.3032, 40.5341),      -- Ending point at Sandy Shores runway.
        },

        -- Plane model used for this route (in-game model name).
        planeModel = "luxor",  -- Luxor private jet model.

        -- Price for a ticket on this plane route.
        Price = 1250,  -- Ticket price set to 1000 currency units.
    },
    {
        -- Name of the plane route displayed to players
        Name = "Sandy Shores to LS International", -- Example Name

        -- Starting runway configuration (for takeoff)
        start = {
            runwayStart = vector3(1700.5720, 3248.2893, 40.9599), -- Start of Sandy Shores Airfield runway
            runwayEnd = vector3(1090.1396, 3084.3186, 39.6830), -- End of Sandy Shores Airfield runway
        },

        -- Ending runway configuration (for landing)
        finish = {
            runwayStart = vector3(-1654.6859, -2765.8936, 13.9447), -- Start of LSIA runway
            runwayEnd = vector3(-1200.5140, -3027.4214, 13.9445), -- End of LSIA runway
        },



        -- The model of the plane used for the route, defined by its spawn name
        planeModel = "shamal", -- Example plane: Luxor, a private jet

        -- Ticket price for using this plane route, defined as a numeric value
        Price = 1000,
    },
    {
        -- Name of the plane route displayed to players
        Name = "LS International to Cayo", -- Example Name

        -- Starting runway configuration (for takeoff)
        start = {
            runwayStart = vector3(-1366.5389, -2437.8640, 13.9458), -- Start of Sandy Shores Airfield runway
            runwayEnd = vector3(-1534.7229, -2734.5051, 13.9445), -- End of Sandy Shores Airfield runway
        },

        -- Ending runway configuration (for landing)
        finish = {
            runwayStart = vector3(3909.6272, -4706.6279, 4.2977), -- Start of LSIA runway
            runwayEnd = vector3(4330.4312, -4551.1353, 4.1843), -- End of LSIA runway
        },



        -- The model of the plane used for the route, defined by its spawn name
        planeModel = "shamal", -- Example plane: Luxor, a private jet

        -- Ticket price for using this plane route, defined as a numeric value
        Price = 1000,
    },
    {
        -- Name of the plane route displayed to players
        Name = "Cayo to LS International", -- Example Name

        -- Starting runway configuration (for takeoff)

        start = {
            runwayStart = vector3(4482.6455, -4495.2407, 4.1939), -- Start of LSIA runway
            runwayEnd = vector3(3909.6272, -4706.6279, 4.2977), -- End of LSIA runway
        },

        -- Ending runway configuration (for landing)
        finish = {
            runwayStart = vector3(-1698.3241, -2848.5962, 13.9444), -- Start of Sandy Shores Airfield runway
            runwayEnd = vector3(-1368.5961, -2266.4517, 13.9450), -- End of Sandy Shores Airfield runway
        },



        -- The model of the plane used for the route, defined by its spawn name
        planeModel = "miljet", -- Example plane: Luxor, a private jet

        -- Ticket price for using this plane route, defined as a numeric value
        Price = 1000,
    },
    -- More plane routes can be added here 
}

-- Target Altitude to fly in
Config.PlanesAlt = 300.0 -- Recommanded low altitude (like 300.0) for faster landing since airports are not too far from eachothers

-- Frequency for plane departures, in minutes.
Config.PlanesFrequency = 1  -- Planes depart every 5 minutes. Min 1 Max 60

---------------------
-- TRAIN CONFIGURATION
---------------------
Config.ENABLE_AI_TRAINS = true  -- Enables AI-driven trains in the game.
Config.ENABLE_TRAINS_BLIPS = false  -- Show blips on the map for train routes.
Config.BLIP_CODE_TRAIN = 36
Config.BLIP_COLOR_TRAIN = 2
Config.BLIP_SIZE_TRAIN = 0.7

-- Price for train tickets.
Config.TrainPrice = 5  -- Price set to 5 currency units.

-- Density of trains spawned around players, from 1 (low) to 10 (high).
Config.TrainsSpawnRate = 10  -- Higher density for train spawns.

---------------------
-- TAXI CONFIGURATION
---------------------
Config.ENABLE_AI_TAXI = false  -- Enables AI-driven taxis in the game.

Config.ENABLE_AI_TAXI_CALL = false  -- Enables requesting AI-driven taxi.

-- Configuration for taxi-related actions and behavior.
Config.WaveAtTaxiButton = false  -- Key press (E) to wave at a taxi to stop. CLIENT EVENT: "taxi:wave" or EXPORT: "waveTaxi" or COMMAND "/wavetaxi"
-- Keybinds: https://docs.fivem.net/docs/game-references/controls/

Config.CallTaxiCommand = "taxi"  -- Command to call a taxi via chat. CLIENT EVENT: "taxi:order" or EXPORT: "orderTaxi"

Config.CallTaxiCooldown = 60 -- Cooldown for calling a taxi in seconds

Config.TaxiModel = "taxi"  -- Default taxi model spawn name.

Config.TaxiSpawnRate = 7  -- Density of taxis spawned around players (1-10 scale).

-- Taxi fare calculation: price per 30 meters traveled.
Config.TaxiPrice = 2  -- 2 currency units per 30 meters traveled.

Config.TaxiStartMeter = 9  -- The initial price for a taxi ride (start at 9 units).

-- Taxi bribery system to speed up travel and avoid police stops.
Config.TaxiCanBeBribed = true  -- Enable bribing option for taxis.
Config.TaxiBribePrice = 200  -- The price for bribing the taxi driver.

-- AI driving styles for taxis.
Config.NormalDrivingStyle = 447  -- Standard taxi driving behavior.
Config.CrazyDrivingStyle = 525116  -- Aggressive driving behavior (when bribed).

-- Taxi Job Settings
Config.useMaxTaxisPlayers = true  -- **AI taxi is only available if not enough human taxi driver are online.**
Config.MaxTaxisPlayers = 4  -- **If 5+ human taxi driver are online, AI taxi is disabled.**

Config.JobTaxi = {'taxi','taxi2'}  -- Taxi jobs names.

---------------------
-- DEBUG CONFIGURATION
---------------------
Config.debug = false  -- Enable/Disable debug messages for easier troubleshooting.
