import{u as D,r as S,j as c,m as q,a as e,F as te,L as i,s as I,C as U,_ as Le,$ as pe,b as W,d as re,f as Y,q as k,P as B,G as Ce,K as _e,a0 as ne,a1 as be,S as Te,O as ie,A as Q,I as $e,g as se,a2 as Ve,i as Ee,a3 as xe,a4 as we,a5 as ce,a6 as fe,a7 as Ie,a8 as Fe,J as de,a9 as ve,aa as oe,t as F,v as ge,ab as Ae,ac as Ye,V as me,ad as Pe,o as Ne,Q as He,R as je,ae as ue,p as We,af as Be,e as J,ag as Xe,z as qe,B as ze,ah as Ze,ai as Qe,x as Ke,aj as Je,ak as et,y as tt}from"./index-99e0aeb1.js";import{A as at,g as st}from"./audioRecorder-de912efa.js";import{g as nt,i as it,M as rt,L as lt,C as ot,D as Me,T as ct,a as dt,b as mt,c as ut,A as St}from"./Maps-0372650c.js";const Oe=t=>{if(t)return/<!SENT-LOCATION-X=(-?\d*\.?\d*)Y=(-?\d*\.?\d*)!>/.test(t)},Ge=t=>{if(t)return t==="<!CALL-NO-ANSWER!>"},ht=t=>{if(t)return/<!AUDIO-MESSAGE-IMAGE="(.*)"-AUDIO="(.*)"-DURATION="(.*)"!>/.test(t)},Et=t=>{if(t.length===0)return!0;if(t)return!/^<!.*!>$/.test(t)},ft=({onEnd:t,close:l})=>{const g=D(W),[C,N]=S.useState(!1),[M,r]=S.useState(0),[a,T]=S.useState(null),f=S.useRef(null);if(!g.EnableVoiceMessages)return null;S.useEffect(()=>{if(!C)return;let o=setInterval(()=>{r(E=>E+1)},1e3);return()=>clearInterval(o)},[C]),S.useEffect(()=>(f.current=new at,()=>{f.current&&f.current.stop()}),[]);const O=o=>{let E=Math.floor(o/60),h=o%60;return`${E}:${h<10?"0":""}${h}`},s=async o=>{const E=await st(new Blob(o),25,7,324,55,3);T(E)};return c(q.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:0},transition:{duration:.2,ease:"easeInOut"},className:"audioMessage-container",children:[e("div",{className:"close",onClick:l}),c("div",{className:"audioMessage-wrapper",children:[c("div",{className:"duration",children:[C&&e("div",{className:"dot"}),O(M)]}),e("div",{className:"content",children:C?e(te,{children:a!=null&&a.base64?e("img",{src:a==null?void 0:a.base64}):i("APPS.MESSAGES.LOADING")}):i("APPS.MESSAGES.START_RECORDING")}),e("div",{className:"button","data-recording":C,onClick:()=>{var E;if(!((E=navigator.mediaDevices)!=null&&E.getUserMedia)||!(f!=null&&f.current))return I("error","No media devices found!");let o=f.current;C?o.stop().then(h=>{if(!h){r(0),N(d=>!d),U.PopUp.set({title:i("APPS.MESSAGES.EMPTY_RECORDING.TITLE"),description:i("APPS.MESSAGES.EMPTY_RECORDING.DESCRIPTION"),buttons:[{title:i("APPS.MESSAGES.EMPTY_RECORDING.OK")}]});return}t(h),N(d=>!d)}):(o.start(100,s),N(h=>!h))},children:C?e(Le,{}):e(pe,{})})]})]})},vt=({paymentAmount:t,setPaymentAmount:l,close:g})=>{const{User:C}=S.useContext(ae),N=D(W),[M]=C,[r,a]=S.useState(0),[T,f]=S.useState(4);let O={0:3,11:2.75,14:2.2};return S.useEffect(()=>{r===0&&f(O[0]);let s=0;for(let o=0;o<Object.keys(O).length;o++)r>=parseInt(Object.keys(O)[o])&&(s=O[Object.keys(O)[o]]);f(s)},[r]),S.useEffect(()=>{l(r)},[r]),c(q.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:0},transition:{duration:.2,ease:"easeInOut"},className:"payment-container",children:[e("div",{className:"close",onClick:g}),c("div",{className:"payment-wrapper",children:[e("div",{className:"button",onClick:()=>t>0&&a(s=>s-1),children:"-"}),e("div",{className:"amount",children:e(re,{type:"number",value:t,style:{fontSize:`${T}rem`},onChange:s=>{if(s.target.value.match(/^[0-9]+$/)&&parseFloat(s.target.value)>0&&parseFloat(s.target.value)<(N.MaxTransferAmount??Number.MAX_SAFE_INTEGER))a(parseFloat(s.target.value));else return s.preventDefault()}})}),e("div",{className:"button",onClick:()=>a(s=>s+1),children:"+"})]}),c("div",{className:"payment-buttons",children:[e("div",{className:"button",onClick:()=>gt(t,M.number),children:i("APPS.MESSAGES.PAY.REQUEST")}),e("div",{className:"button",onClick:()=>ye(t,{id:M.id,name:M.name,number:M.number}),children:i("APPS.MESSAGES.PAY.SEND")})]})]})},ye=(t,l)=>{var g;if(W.value.EnableMessagePay&&!(!t&&t<=0)){if(t>(((g=W.value)==null?void 0:g.MaxTransferAmount)??Number.MAX_SAFE_INTEGER))return I("error","Amount is too high");U.PopUp.set({title:i("APPS.MESSAGES.PAY.SEND_TITLE").format({amount:W.value.CurrencyFormat.replace("%s",t.toString())}),description:i("APPS.MESSAGES.PAY.SEND_DESCRIPTION").format({amount:W.value.CurrencyFormat.replace("%s",t.toString()),name:l.name??Y(l.number)}),buttons:[{title:i("APPS.MESSAGES.PAY.SEND_BUTTON_CANCEL")},{title:i("APPS.MESSAGES.PAY.SEND_BUTTON_SEND"),cb:()=>{k("Wallet",{action:"sendPayment",number:l.number,amount:t},{success:!0}).then(C=>{var N,M;if(C.success){let r={id:l.id,sender:(M=(N=B)==null?void 0:N.PhoneNumber)==null?void 0:M.value,content:`<!SENT-PAYMENT-${t}!>`,attachments:[],timestamp:Date.now()};y.set([...y.value,r])}else{I("error","Failed to send payment "+JSON.stringify(C)),setTimeout(()=>{U.PopUp.set({title:i(`APPS.WALLET.${C.reason}.TITLE`),description:i(`APPS.WALLET.${C.reason}.DESCRIPTION`),buttons:[{title:i("APPS.WALLET.OK")}]})},500);return}})}}]})}},gt=(t,l)=>{var C,N;if(!W.value.EnableMessagePay||!t&&t<=0)return;let g={sender:(N=(C=B)==null?void 0:C.PhoneNumber)==null?void 0:N.value,content:`<!REQUESTED-PAYMENT-${t}!>`,attachments:[],timestamp:Date.now()};k("Messages",{action:"sendMessage",number:l,content:g.content,attachments:[]},{messageId:Math.floor(Math.random()*1e3).toString()}).then(M=>{if(!M)return y.set([...y.value,{...g,delivered:!1}]);y.set([...y.value,{...g,id:M.messageId}])})},De={Messagelist:[{id:"1",number:"4802940940",name:"Loaf Scripts",unread:!0,lastMessage:"Sure Thing!",messages:[{sender:"4802940940",content:"Sure Thing!",timestamp:Date.now()-1e3*60*60*2},{sender:"1234567890",content:"Ready to release more products?",timestamp:Date.now()-1e3*60*60*2-1e3*60*5},{sender:"4802940940",content:"Hey!",timestamp:Date.now()-1e3*60*60*2-1e3*60*10}],timestamp:Date.now()-1e3*60*60*2},{id:"2",name:"Office Chat",unread:!0,lastMessage:"is the meeting at 8pm?",timestamp:Date.now()-1e3*60*60*4,isGroup:!0,members:[{number:"4802940940",name:"Loaf Scripts",isOwner:!1},{number:"1566444151",name:"Peter",isOwner:!1},{number:"2051440412",name:"Peter",isOwner:!1},{number:"2051440412",isOwner:!1}],messages:[{sender:"4802940940",content:"is the meeting at 8pm?",timestamp:Date.now()-1e3*60*60*4},{sender:"1566444151",content:"please confirm the time",timestamp:Date.now()-1e3*60*60*4-1e3*60*5},{sender:"2051440412",content:"Hey everyone, please make sure to be on time for the meeting",timestamp:Date.now()-1e3*60*60*4-1e3*60*10}]},{id:"3",number:"4802940963",unread:!1,lastMessage:"yeah i dont know whats going on, it was super weird. i think i might have to go to the doctor",timestamp:Date.now()-1e3*60*60*2*24,messages:[{sender:"4802940940",content:"yeah i dont know whats going on, it was super weird. i think i might have to go to the doctor",timestamp:Date.now()-1e3*60*60*2*24},{sender:"1234567890",content:"i hope you feel better soon",timestamp:Date.now()-1e3*60*60*2*24-1e3*60*5},{sender:"4802940940",content:"still sick :(",timestamp:Date.now()-1e3*60*60*2*24-1e3*60*10}]},{id:"4",number:"4802940942",unread:!0,lastMessage:"see you!",timestamp:Date.now()-1e3*60*60*2*24*2,messages:[{sender:"4802940942",content:"<!SENT-LOCATION-X=400.2Y=-1550.3!>",timestamp:Date.now()-1e3*60*60*2*24},{sender:"4802940942",content:`see you!

heres the location`,timestamp:Date.now()-1e3*60*60*2*24*2},{sender:"1234567890",content:"ofcourse, i will be there",timestamp:Date.now()-1e3*60*60*2*24*2-1e3*60*3},{sender:"4802940942",content:"hey buddy, still on for tomorrow?",timestamp:Date.now()-1e3*60*60*2*24*2-1e3*60*3}]}]};function At({location:t}){const l=D(St),[g,C]=S.useState("losSantos"),N=Me.mapTypes[g],M=nt(g);return S.useEffect(()=>{C(it({x:t[0],y:t[1]})?"losSantos":"cayoPerico")},[t]),S.useEffect(()=>{setTimeout(()=>{const r=document.querySelector(".leaflet-control-attribution.leaflet-control a");r&&(r.removeAttribute("href"),r.onclick=()=>{window.invokeNative("openUrl","https://leafletjs.com/")})},500)},[]),e("div",{className:"mapscomponent-wrapper",children:c(rt,{className:"map",crs:g==="losSantos"?lt:ot,style:{backgroundColor:Me.backgrounds[l]},center:[t[0],t[1]],zoom:4,scrollWheelZoom:!1,zoomControl:!1,doubleClickZoom:!1,dragging:!1,bounceAtZoomLimits:!0,boxZoom:!1,maxBoundsViscosity:.95,maxBounds:M,attributionControl:!1,preferCanvas:!0,children:[e(ct,{url:dt[g].tileServer.replace("{layer}",l),minZoom:N.minZoom,maxZoom:N.maxZoom,maxNativeZoom:N.maxZoom,noWrap:!0},`${g}-${l}`),e(mt,{position:t,icon:ut()})]})})}const y=de([]);function Pt(){const{User:t,View:l,UnreadMessages:g}=S.useContext(ae),C=D(B.Settings),N=D(W),M=D(B.PhoneNumber),r=D(Q),[a,T]=t,[f,O]=l,[s,o]=g,E=D(y),h=D(U.Emoji),d=D(U.Gif),[R,x]=S.useState(!1),[L,V]=S.useState(null),[_,z]=S.useState(!1),[G,K]=S.useState(!1),[X,A]=S.useState(0),[b,m]=S.useState(null),v=S.useRef(null),w=S.useRef(0),p=S.useRef(!1),[P,H]=S.useState({content:"",attachments:[]});if(!a)return null;S.useEffect(()=>{var n;Ce("Messages")&&k("Messages",{action:"getMessages",page:0,id:a==null?void 0:a.id},((n=De.Messagelist.find(u=>u.id===a.id))==null?void 0:n.messages)??[]).then(u=>{u&&u.length>0&&y.set([...u.reverse()])})},[r==null?void 0:r.active]);const Se=()=>{if(P.content.length<=0&&P.attachments.length<=0&&!b)return I("info","Message is empty, returning");let n={sender:M,timestamp:Date.now(),id:a.id,content:P.content,attachments:P.attachments};if(!Et(P.content))return I("error","Message contains invalid characters, returning",P.content);if(b){if(p.current)return;p.current=!0,ve("Image",b.waves.message).then(u=>{ve("Audio",b.blob).then($=>{n.content=`<!AUDIO-MESSAGE-IMAGE="${u}"-AUDIO="${$}"-DURATION="${b.duration}"!>`,k("Messages",{action:"sendMessage",number:a.number,content:n.content,id:a.id},{messageId:Math.random().toString(36).substring(7)}).then(j=>{j&&oe()?y.set([...y.value,{...n,id:j.messageId}]):y.set([...y.value,{...n,delivered:!1}]),p.current=!1,m(null)})}).catch($=>{console.error("Failed to upload voice message audio",$),p.current=!1})}).catch(u=>{console.error("Failed to upload voice message image",u),p.current=!1});return}if(!oe())return y.set([...y.value,{...n,delivered:!1}]);k("Messages",{action:"sendMessage",number:a.number,content:P.content,attachments:P.attachments,id:a.id},{messageId:Math.random().toString(36).substring(7)}).then(u=>{u?(y.set([...y.value,{...n,id:u.messageId}]),m(null)):y.set([...y.value,{...n,delivered:!1}]),H({content:"",attachments:[]}),v.current&&(v.current.value=""),I("info","Updating recent message cache state"),F.APPS.MESSAGES.messages.set(F.APPS.MESSAGES.messages.value.map($=>$.id===a.id?{...$,timestamp:new Date().getTime(),lastMessage:n.content.length>0&&n.content,unread:!1,deleted:!1}:$))})},he=()=>{U.PopUp.set({title:i("APPS.MESSAGES.SEND_LOCATION_POPUP.TITLE"),description:i("APPS.MESSAGES.SEND_LOCATION_POPUP.TEXT"),buttons:[{title:i("APPS.MESSAGES.SEND_LOCATION_POPUP.CANCEL")},{title:i("APPS.MESSAGES.SEND_LOCATION_POPUP.SEND"),cb:()=>{k("Maps",{action:"getCurrentLocation"},{x:0,y:0}).then(n=>{if(!n)return;let u={id:a.id,sender:M,content:`<!SENT-LOCATION-X=${ge(n.x,2)}Y=${ge(n.y,2)}!>`,attachments:[],timestamp:Date.now()};k("Messages",{action:"sendMessage",number:a.number,content:u.content,attachments:u.attachments,id:u.id},{messageId:Math.random().toString(36).substring(7)}).then($=>{if(!$)return y.set([...y.value,{...u,delivered:!1}]);y.set([...y.value,{...u,id:$.messageId}])})})}}]})},{handleScroll:Ue}=_e({fetchData:n=>k("Messages",{action:"getMessages",id:a.id,page:n}),onDataFetched:n=>{let u=document.querySelector(".message-container");w.current=u.scrollHeight,y.set([...n.reverse(),...y.value])},isReversed:!0,perPage:25});S.useEffect(()=>{let n=document.querySelector(".message-container");const u=n.scrollHeight;n.scrollTop+=u-w.current,n.scroll},[E]),ne("messages:newMessage",n=>{a.id===n.channelId&&y.set([...y.value,{...n,timestamp:Date.now()}])},{waitUntilService:!0}),ne("messages:renameGroup",n=>{a.id===n.channelId&&T(u=>({...u,name:n.name}))},{waitUntilService:!0}),ne("messages:messageDeleted",n=>{if(I("info","messages:messageDeleted triggered",n),!n)return I("error","Did not get any data from messages:messageDeleted, returning");y.set(y.value.filter(u=>u.id!==n.messageId))},{waitUntilService:!0}),ne("messages:removeMember",n=>{a.id===n.channelId&&n.number===M&&(O("userlist"),T(null))},{waitUntilService:!0});const ke=be(n=>{if(!N.DeleteMessages)return I("error","Config.DeleteMessages is set to false");let u=n.target;for(;!u.classList.contains("message");)u=u.parentElement;let $=u.getAttribute("data-id");if(!$)return I("error","Failed to get message id when longpressing");isNaN($)||($=parseInt($));let j=E.find(le=>le.id===$);if(!j)return I("error","Failed to find message with id",$);j.sender===M&&(Oe(j.content)||Re(j.content)||Ge(j.content)||U.ContextMenu.set({buttons:[{title:i("APPS.MESSAGES.DELETE"),color:"red",cb:()=>{k("Messages",{action:"deleteMessage",channel:a.id,id:$}).then(le=>{if(!le)return I("error","Failed to delete message")})}}]}))}),Re=n=>!n||!N.EnableMessagePay?!1:/<!SENT-PAYMENT-(\d*)!>/.test(n)?!0:!!/<!REQUESTED-PAYMENT-(\d*)!>/.test(n);return e(te,{children:c(q.div,{...Te("right","message",.2),className:"animation-container",children:[c(ie,{children:[R&&e(Ct,{setShow:x,setShowUserInfo:V,sendLocation:he,setData:T,data:a}),L&&e(pt,{setShow:V,user:a!=null&&a.isGroup?L:a})]}),c("div",{className:"message-header",children:[c("div",{className:"back",onClick:()=>{var n;O("userlist"),T(null),(n=Q.value.active)!=null&&n.data&&Q.patch({active:{name:"Messages",data:null}}),a.unread&&(k("Messages",{action:"markRead",id:a.id}),o(u=>u-1))},children:[e($e,{}),s>0&&e("span",{className:"back-title",children:s})]}),c("div",{className:"user",onClick:()=>{a!=null&&a.isGroup?x(!0):V(!0)},children:[a!=null&&a.isGroup?e("div",{className:"group-avatar",style:{backgroundImage:a!=null&&a.avatar?`url(${a.avatar})`:null},children:!(a!=null&&a.avatar)&&c(te,{children:[a.members.sort((n,u)=>n.name&&u.name?n.name.localeCompare(u.name):n.name?-1:u.name?1:0).sort((n,u)=>(n.avatar?1:-1)-(u.avatar?1:-1)).slice(0,5).map((n,u)=>e("div",{className:"avatar","data-hasavatar":(n==null?void 0:n.avatar)!==void 0,style:{backgroundImage:n!=null&&n.avatar?`url(${n.avatar})`:null,zIndex:a.members.length-u},children:n.name?!(n!=null&&n.avatar)&&se(n.name):e("img",{src:`./assets/img/avatar-placeholder-${C.display.theme}.svg`,alt:""})},u)),a.members.length===0&&e("img",{src:`./assets/img/avatar-placeholder-${C.display.theme}.svg`,alt:""})]})}):e("div",{className:"avatar","data-hasavatar":(a==null?void 0:a.avatar)!==void 0,style:{backgroundImage:a!=null&&a.avatar?`url(${a.avatar})`:null},children:a.name?!(a!=null&&a.avatar)&&se(a.name):e("img",{src:`./assets/img/avatar-placeholder-${C.display.theme}.svg`,alt:""})}),e("div",{className:"name",children:a!=null&&a.isGroup?a.name??`${a.members.length===0?1:a.members.length} ${i("APPS.MESSAGES.PEOPLE")}`:a.name??Y(a.number)})]}),c("div",{className:"buttons","data-hidden":a==null?void 0:a.isGroup,children:[e(Ve,{onClick:()=>{a!=null&&a.isGroup||Ee({number:a.number})}}),e(xe,{onClick:()=>{a!=null&&a.isGroup||Ee({number:a.number,videoCall:!0})}})]})]}),e("div",{className:"message-container",onScroll:Ue,style:G||_?{height:"48%"}:{},children:e("div",{className:"message-body",children:E.map((n,u)=>e(Nt,{index:u,messages:E,message:n,longPressEvent:ke},u))})}),e("div",{className:"attachments",children:P.attachments.map((n,u)=>c("div",{className:"attachment",children:[we(n)?e("video",{src:n,muted:!0,controls:!1,loop:!0,autoPlay:!0}):e(ce,{src:n,blur:!0}),e(fe,{onClick:()=>{H({...P,attachments:P.attachments.filter(($,j)=>j!==u)})}})]},u))}),c("div",{className:"message-bottom","data-expanded":!!(G||_),children:[e("div",{className:"upper",children:c("div",{className:"input","data-border":!b,children:[b?c("div",{className:"audio-message",children:[e(fe,{onClick:()=>m(null)}),e("div",{className:"audio-waves",children:e("img",{src:b.waves.placeholder})})]}):e(re,{placeholder:i("APPS.MESSAGES.PLACEHOLDER"),ref:v,value:P.content,onChange:n=>{H({content:n.target.value,attachments:P.attachments})},onKeyDown:n=>{n.key=="Enter"&&Se()}}),(P.content.length>0||P.attachments.length>0||b)&&e("div",{className:"send",onClick:Se,children:e(Ie,{})})]})}),!G&&!_&&e("div",{className:"actions-wrapper",children:c("div",{className:"actions",children:[e("div",{className:"action",onClick:()=>{if(h)return U.Emoji.reset();U.Emoji.set({onSelect:n=>H(u=>({content:`${u.content}${n.emoji}`,attachments:u.attachments}))})},children:"😀"}),e("div",{className:"action",onClick:()=>{var n,u,$;P.attachments.length<3&&U.Gallery.set({includeVideos:!0,allowExternal:($=(u=(n=W)==null?void 0:n.value)==null?void 0:u.AllowExternal)==null?void 0:$.Messages,onSelect:j=>H({...P,attachments:[...P.attachments,j.src]})})},children:e("img",{src:"./assets/img/icons/messages/gallery.png"})}),N.EnableGIFs!==!1&&e("div",{className:"action small",onClick:()=>{if(d)return U.Gif.reset();U.Gif.set({onSelect:n=>H(u=>({content:u.content,attachments:[...u.attachments??[],n]}))})},children:"GIF"}),(N==null?void 0:N.EnableMessagePay)&&!(a!=null&&a.isGroup)&&e("div",{className:"action ",onClick:()=>{z(!1),K(n=>!n)},children:"$"}),e("div",{className:"action",onClick:()=>he(),children:e(Fe,{})}),(N==null?void 0:N.EnableVoiceMessages)&&e("div",{className:"action",onClick:()=>{K(!1),z(n=>!n)},children:e(pe,{})})]})}),e(ie,{children:G&&e(vt,{paymentAmount:X,setPaymentAmount:A,close:()=>K(!1)})}),e(ie,{children:_&&e(ft,{onEnd:n=>{if(!n)return I("error","Failed to get audio message data");m({blob:n.blob,waves:n.waveform,duration:n.duration}),z(!1)},close:()=>z(!1)})})]})]})})}const Nt=({messages:t,message:l,index:g,longPressEvent:C})=>{var R,x;const{User:N}=S.useContext(ae),M=D(B.PhoneNumber),[r]=N,a=D(W);let T,f,O,s,o,E,h=l.sender===M?"self":"other",d=((R=t[g+1])==null?void 0:R.sender)===M?"self":"other";if(t[g+1]?O=Math.abs(l.timestamp-t[g+1].timestamp)/36e5:d=void 0,r!=null&&r.isGroup)T=(x=r.members.find(L=>L.number===l.sender))==null?void 0:x.name,f=!t[g-1]||t[g-1].sender!==l.sender;else if(T=r.name,l.content)if(Ge(l.content))if(h==="other")l.content=i("APPS.MESSAGES.MISSED_CALL").format({number:l.sender});else return null;else/<!SENT-PAYMENT-(\d*)!>/.test(l.content)?s={amount:l.content.match(/\d/g).join(""),request:!1}:/<!REQUESTED-PAYMENT-(\d*)!>/.test(l.content)&&(s={amount:l.content.match(/\d/g).join(""),request:!0});if(Oe(l.content)){let L=l.content.match(/X=(-?\d*\.?\d*)Y/)[1],V=l.content.match(/Y=(-?\d*\.?\d*)!>/)[1];o={x:L,y:V}}return ht(l.content)&&(E={wave:l.content.match(/AUDIO-MESSAGE-IMAGE="([^"]+)"/)[1],src:l.content.match(/AUDIO="([^"]+)"/)[1],duration:l.content.match(/DURATION="([^"]+)"/)[1]}),c("div",{className:`message ${h}`,"data-id":l.id,...C,children:[f&&h=="other"&&e("div",{className:"user",children:T??Y(l.sender)}),l.delivered===!1?c("div",{className:"content-wrapper",children:[s&&e("div",{className:"payment",children:a.CurrencyFormat.replace("%s",s.amount)}),!s&&e("div",{className:"content",children:Ae(l.content)}),e(Ye,{})]}):s||o||E?c(te,{children:[o&&c("div",{className:"location",onClick:()=>{U.ContextMenu.set({buttons:[{title:i("APPS.MESSAGES.OPEN_IN_MAPS"),cb:()=>{Q.patch({active:{name:"Maps",data:{location:[o.y,o.x],name:i("APPS.MESSAGES.USERS_LOCATION").format({name:T??Y(l.sender)}),icon:r.avatar}}})}},{title:i("APPS.MAPS.SET_WAYPOINT"),cb:()=>{k("Maps",{action:"setWaypoint",data:{x:o.x,y:o.y}})}}]})},children:[e(At,{location:[parseFloat(o.y),parseFloat(o.x)]}),h==="other"&&c("div",{className:"sender",children:[T??Y(l.sender)," ",i("APPS.MESSAGES.SENT_LOCATION")]})]}),s&&e("div",{className:"payment",children:s.request?c("div",{className:me("request",h),children:[c("div",{className:"title",children:[a.CurrencyFormat.replace("%s",Pe(s.amount).toString())," ",i("APPS.MESSAGES.PAY.REQUESTED")]}),h==="other"&&e("div",{className:"button",onClick:()=>ye(s.amount,{id:r.id,number:r.number,name:r.name}),children:i("APPS.MESSAGES.PAY.PAY")})]}):e("div",{className:"sent",children:a.CurrencyFormat.replace("%s",Pe(s.amount).toString())})}),E&&e(Mt,{data:E,sender:h})]}):l.content&&e("div",{className:"content",children:Ae(l.content)}),l.attachments&&l.attachments.length>0&&e("div",{className:"attatchments",children:l.attachments.map((L,V)=>we(L)?e("video",{src:L,controls:!1,loop:!0,autoPlay:!0,muted:!0,onClick:_=>U.FullscreenImage.set(L)},V):e(ce,{src:L,blur:!0,onClick:()=>U.FullscreenImage.set(L)},V))}),l.delivered===!1?e("div",{className:"status",children:i("APPS.MESSAGES.NOT_DELIVERED")}):t[g+1]&&O>6?e("div",{className:"date",children:Ne(l.timestamp)}):h!==d&&e("div",{className:"date",children:Ne(l.timestamp)})]},g)},Mt=({data:t,sender:l})=>{var T;const[g,C]=S.useState(!1),[N,M]=S.useState(0),r=S.useRef(null);S.useEffect(()=>{r.current&&(r.current.onended=()=>C(!1))},[r]);const a=f=>{f=Math.floor(f);const O=Math.floor(f/60),s=f-O*60;return`${O<10?"0"+O:O}:${s<10?"0"+s:s}`};return c("div",{className:`voice-message ${l}`,children:[e("a",{onClick:()=>{r.current&&(g?(r.current.pause(),r.current.currentTime=0):r.current.play().catch(()=>I("error","Failed to play audio message")),C(f=>!f))},children:g?e(He,{}):e(je,{})}),c("div",{className:"wave",children:[e("div",{className:"overlay",style:{width:`${(t.duration-N)/t.duration*100}%`}}),e("img",{src:t.wave,alt:"wave"})]}),e("div",{className:"duration",children:a(g&&Math.floor(((T=r.current)==null?void 0:T.currentTime)+.5)!==0?r.current.currentTime:t.duration)}),e("audio",{ref:r,onTimeUpdate:f=>M(f.currentTarget.currentTime),children:e("source",{src:t.src,type:"audio/mpeg"})})]})},pt=({user:t,setShow:l})=>{const g=D(B.Settings);return c(q.div,{...ue,className:"info-panel",children:[e("div",{className:"info-panel-header",children:e("div",{className:"done",onClick:()=>l(!1),children:i("APPS.MESSAGES.DONE")})}),c("div",{className:"info-panel-body",children:[c("div",{className:"info-panel-top",children:[e("div",{className:"avatar","data-hasavatar":(t==null?void 0:t.avatar)!==void 0,style:{backgroundImage:t!=null&&t.avatar?`url(${t.avatar})`:null},children:t.name?!(t!=null&&t.avatar)&&se(t.name):e("img",{src:`./assets/img/avatar-placeholder-${g.display.theme}.svg`,alt:""})}),e("div",{className:"name",children:t.name??Y(t.number)})]}),e("div",{className:"items",children:!t.company&&c(te,{children:[t.name&&e("div",{className:"info-section",children:e("div",{className:"item blue",onClick:()=>We(t.number),children:Y(t.number)})}),e("div",{className:"info-section",children:t.name?e("div",{className:"item blue",onClick:()=>{U.Share.set({type:"contact",data:{firstname:t.firstname,lastname:t.lastname,number:t.number,avatar:t.avatar}})},children:i("APPS.PHONE.CONTACTS.SHARE_CONTACT")}):e("div",{className:"item blue",onClick:()=>{Q.patch({active:{name:"Phone",data:{view:"newContact",number:t.number}}})},children:i("APPS.PHONE.CONTACTS.ADD_CONTACT")})})]})})]})]})},Ct=t=>{const{View:l}=S.useContext(ae),g=D(B.Settings),[C,N]=l,[M,r]=S.useState(t.data.name),[a,T]=S.useState(t.data.avatar);let f=t.data,O=!f.members.find(s=>s.isOwner);return e(te,{children:c(q.div,{...ue,className:"info-panel",children:[c("div",{className:"info-panel-header",children:[e("div",{}),e("div",{className:"close",children:e("div",{className:"button",onClick:()=>t.setShow(!1)})}),e("div",{className:"done",onClick:()=>{M&&M!==""&&M!==f.name?k("Messages",{action:"renameGroup",id:f.id,name:M}).then(s=>{t.setShow(!1)}):t.setShow(!1)},children:i("APPS.MESSAGES.DONE")})]}),c("div",{className:"info-panel-body",children:[c("div",{className:"info-panel-top",children:[a?e(ce,{className:"group-image",src:a,blur:!0}):c("div",{className:"group-avatar",children:[f.members.sort((s,o)=>s.avatar?1:-1).slice(0,5).map((s,o)=>e("div",{className:"avatar","data-hasavatar":(s==null?void 0:s.avatar)!==void 0,style:{backgroundImage:s!=null&&s.avatar?`url(${s.avatar})`:null,zIndex:f.members.length-o},children:s.name?!(s!=null&&s.avatar)&&se(s.name):e("img",{src:`./assets/img/avatar-placeholder-${g.display.theme}.svg`,alt:""})},o)),f.members.length===0&&e("img",{src:`./assets/img/avatar-placeholder-${g.display.theme}.svg`,alt:""})]}),e("div",{className:"add-photo",onClick:()=>{var s,o,E;a?U.PopUp.set({title:i("APPS.MESSAGES.REMOVE_AVATAR_POPUP.TITLE"),description:i("APPS.MESSAGES.REMOVE_AVATAR_POPUP.DESCRIPTION"),buttons:[{title:i("APPS.MESSAGES.REMOVE_AVATAR_POPUP.CANCEL")},{title:i("APPS.MESSAGES.REMOVE_AVATAR_POPUP.REMOVE"),color:"red",cb:()=>{k("Messages",{action:"removeGroupAvatar",id:f.id},!0).then(h=>{if(!h)return I("warning","Could not remove group avatar");t.setData(d=>{let R=d;return R.avatar=null,R}),T(null)})}}]}):U.Gallery.set({allowExternal:(E=(o=(s=W)==null?void 0:s.value)==null?void 0:o.AllowExternal)==null?void 0:E.Messages,onSelect:h=>k("Messages",{action:"setGroupAvatar",id:f.id,avatar:h.src},!0).then(d=>{if(!d)return I("warning","Could not set group avatar");t.setData(R=>{let x=R;return x.avatar=h.src,x}),T(h.src)})})},children:a?"Remove Photo":" Add Photo"})]}),c("div",{className:"items",children:[e("div",{className:"info-section",children:c("div",{className:"item blue",children:[e(Be,{className:"add"}),e(re,{defaultValue:f.name??i("APPS.MESSAGES.GROUP_NAME"),onChange:s=>r(s.target.value)})]})}),c("div",{className:"subtitle",children:[f.members.length===0?1:f.members.length," ",i("APPS.MESSAGES.MEMBERS")]}),c("div",{className:"info-section",children:[c("div",{className:"item",onClick:()=>U.ContactSelector.set({filter:f.members.map(s=>s.number),onSelect:s=>{if(f.members.find(o=>o.number===s.number))return I("info","User is already in group");k("Messages",{action:"addMember",id:f.id,number:s.number},!0).then(o=>{if(!o)return I("warning","Could not add member to group");t.setData(E=>{let h=E;return h.members.push({...s,name:J(s.firstname,s.lastname),isOwner:!1}),h})})}}),children:[e(Xe,{className:"add"}),e("div",{className:"name",children:i("APPS.MESSAGES.ADD_MEMBER")})]}),f.members.sort((s,o)=>s.name&&!o.name?-1:!s.name&&o.name?1:s.name<o.name?-1:s.name>o.name?1:0).map((s,o)=>c("div",{className:"item",children:[O&&e(qe,{className:"remove",onClick:()=>{U.PopUp.set({title:i("APPS.MESSAGES.REMOVE_POPUP.TITLE"),description:i("APPS.MESSAGES.REMOVE_POPUP.TEXT").format({name:s.name??Y(s.number)}),buttons:[{title:i("APPS.MESSAGES.CANCEL")},{title:i("APPS.MESSAGES.REMOVE_POPUP.REMOVE"),color:"red",cb:()=>{k("Messages",{action:"removeMember",number:s.number,id:f.id}).then(E=>{E&&(t.setData(h=>{let d=h;return d.members=d.members.filter(R=>R.number!==s.number),d}),t.setShow(!1))})}}]})}}),e("div",{className:"avatar","data-hasavatar":(s==null?void 0:s.avatar)!==void 0,style:{backgroundImage:s!=null&&s.avatar?`url(${s.avatar})`:null},children:s.name?!(s!=null&&s.avatar)&&se(s.name):e("img",{src:`./assets/img/avatar-placeholder-${g.display.theme}.svg`,alt:""})}),c("div",{className:"details",children:[e("div",{className:"name",children:s.name??Y(s.number)}),s.name&&e("div",{className:"phone-number",children:Y(s.number)})]}),e(ze,{className:"info",onClick:()=>t.setShowUserInfo({...s})})]},o))]}),e("div",{className:"info-section",children:e("div",{className:"item blue",onClick:()=>t.sendLocation(),children:i("APPS.MESSAGES.SHARE_LOCATION")})}),e("div",{className:"info-section",onClick:()=>{U.PopUp.set({title:i("APPS.MESSAGES.LEAVE_POPUP.TITLE"),description:i("APPS.MESSAGES.LEAVE_POPUP.TEXT"),buttons:[{title:i("APPS.MESSAGES.CANCEL")},{title:i("APPS.MESSAGES.LEAVE_POPUP.LEAVE"),color:"red",cb:()=>{k("Messages",{action:"leaveGroup",id:f.id}).then(s=>{if(!s)return I("info","Failed to leave group, server didnt callback request");N("userlist"),t.setShow(!1)})}}]})},children:c("div",{className:"item red",children:[e(Ze,{}),i("APPS.MESSAGES.LEAVE_GROUP")]})})]})]})]})})};function bt(){const{User:t,View:l,Newmessage:g,ImportedUser:C}=S.useContext(ae),[N,M]=t,[r,a]=l,[T,f]=C,O=D(B.Settings),s=D(B.PhoneNumber),[o,E]=g,h=D(F.APPS.PHONE.contacts),[d,R]=S.useState([]),x=S.useRef(null),[L,V]=S.useState(""),[_,z]=S.useState([]),[G,K]=S.useState({content:"",attachments:[]});S.useEffect(()=>{T&&(R([T]),f(null))},[T]);const X=()=>{(G.content.length>0||G.attachments.length>0)&&(d.length>1?k("Messages",{action:"createGroup",members:d,content:G.content,attachments:G.attachments}).then(A=>{if(!A)return I("error","Failed to create group");M({id:A.channelId,lastMessage:G.content,timestamp:Date.now(),isGroup:!0,members:d.map(b=>{let m=J(b.firstname,b.lastname);return{...b,name:m}})}),a("messages"),E(!1)}):(k("Messages",{action:"sendMessage",number:d[0].number,content:G.content,attachments:G.attachments}).then(A=>{var v,w,p,P;if(!A)return I("error","Failed to send message");let b;d[0].name?b=d[0].name:(v=d[0])!=null&&v.firstname&&(b=J((w=d[0])==null?void 0:w.firstname,(p=d[0])==null?void 0:p.lastname));let m={number:d[0].number,name:b,avatar:(P=d[0])==null?void 0:P.avatar};M({...m,id:A.channelId,lastMessage:G.content,timestamp:Date.now()}),I("info","Updating recent message cache state"),F.APPS.MESSAGES.messages.set(F.APPS.MESSAGES.messages.value.map(H=>H.id===A.channelId?{...H,timestamp:new Date().getTime(),lastMessage:G.content,unread:!1,deleted:!1}:H))}),a("messages"),E(!1)))};return S.useEffect(()=>{if(L.length>0){if(!h)return I("error","Contacts not loaded");z(h.filter(A=>{let b=J(A.firstname,A.lastname);return b&&b.toLowerCase().includes(L.toLowerCase())&&!A.company}))}else z([])},[L]),c(q.div,{...ue,className:"new-message-container",children:[c("div",{className:"new-message-header",children:[e("span",{}),e("div",{className:"title",children:i("APPS.MESSAGES.NEW_MESSAGE")}),e("div",{className:"button",onClick:()=>{var b,m,v;d.length>0&&(G.content.length>0||G.attachments.length>0)?X():(E(!1),(v=(m=(b=Q)==null?void 0:b.value)==null?void 0:m.active)!=null&&v.data&&Q.patch({active:{...Q.value.active,data:null}}))},children:d.length>0&&(G.content.length>0||G.attachments.length>0)?i("APPS.MESSAGES.SEND"):i("APPS.MESSAGES.CANCEL")})]}),c("div",{className:"new-message-body",children:[c("div",{className:"new-message-search",children:[c("div",{className:"to",children:[i("APPS.MESSAGES.TO"),":"]}),e("div",{className:"contacts",children:d.map((A,b)=>{let m=J(A.firstname,A.lastname),v=m!=="Unknown";return e("div",{className:me("contact",v?"green":"blue"),onClick:()=>{U.PopUp.set({title:i("APPS.MESSAGES.REMOVE_POPUP.TITLE"),description:i("APPS.MESSAGES.REMOVE_POPUP.TEXT").format({NAME:m??Y(A.number)}),buttons:[{title:i("APPS.MESSAGES.CANCEL")},{title:i("APPS.MESSAGES.REMOVE_POPUP.REMOVE"),color:"red",cb:()=>{let w=d.filter(p=>p.number!==A.number);R(w)}}]})},children:v?m:Y(A.number)},b)})}),e(re,{type:"text",ref:x,onChange:A=>{if(V(A.target.value),A.target.value.length==s.length&&/^\d+$/g.test(A.target.value)){if(A.target.value===s||d.find(m=>m.number==A.target.value))return;R([...d,{number:A.target.value}]),x.current.value="",V("")}},onKeyDown:A=>{var b;A.key=="Backspace"&&L.length==0?((b=d[d.length-1])==null?void 0:b.name)===void 0?(x.current.value=d[d.length-1].number,R(d.slice(0,d.length-1))):R(d.slice(0,-1)):A.key=="Tab"&&(A.preventDefault(),_.length==1&&(R([...d,_[0]]),x.current.value="",V("")))}})]}),e("div",{className:"search-results",children:_&&_.filter(A=>!d.find(b=>b.number==A.number)).map((A,b)=>{let m=J(A.firstname,A.lastname);return c("div",{className:"contact",onClick:()=>{d.find(w=>w.number==A.number)||(R([...d,A]),x.current.value="",V(""))},children:[e("img",{src:A.avatar??`./assets/img/avatar-placeholder-${O.display.theme}.svg`}),c("div",{className:"user",children:[e("div",{className:"name",children:m}),e("div",{className:"number",children:Y(A.number)})]})]},b)})})]}),d.length>0&&_.length===0&&e("div",{className:"message-bottom absolute",children:e("div",{className:"upper",children:c("div",{className:"input",children:[e(re,{placeholder:i("APPS.MESSAGES.PLACEHOLDER"),value:G.content,onChange:A=>{K({content:A.target.value??"",attachments:G.attachments})},onKeyDown:A=>{A.key=="Enter"&&X()}}),(G.content.length>0||G.attachments.length>0)&&e("div",{className:"send",onClick:()=>X(),children:e(Ie,{})})]})})})]})}const Z=de([]),ee=de([]);function Tt(){var b;const{User:t,View:l,Newmessage:g,UnreadMessages:C,ImportedUser:N}=S.useContext(ae),M=D(B.PhoneNumber),r=(b=D(Q))==null?void 0:b.active,[a,T]=t,[f,O]=l,[s,o]=N,[E,h]=g,[d,R]=C,x=D(F.APPS.PHONE.contacts),L=D(F.APPS.MESSAGES.messages),V=D(Z),[_,z]=S.useState(""),[G,K]=S.useState(!1),X=D(ee);S.useEffect(()=>{if(Ce("Messages"))if(L)I("info","Using cache for recent messages"),R(V.filter(m=>m.unread).length),Z.set(L.map(m=>{if(!m.name){let v=x.find(w=>w.number===m.number);if(!v)return m;m.name=J(v.firstname,v.lastname),m.avatar=v==null?void 0:v.avatar}return m})??[]);else{if(!oe())return I("info","No service, not fetching recent messages");k("Messages",{action:"getRecentMessages"},De.Messagelist).then(m=>{if(!m)return I("error","No recent messages");let v=m.map(w=>{if(w.isGroup)return w.members=w.members.filter(p=>p.number!==M).map(p=>{let P=x.find(H=>H.number===p.number);return{name:P&&P.firstname?J(P==null?void 0:P.firstname,P==null?void 0:P.lastname):void 0,avatar:P==null?void 0:P.avatar,blocked:P==null?void 0:P.blocked,favourite:P==null?void 0:P.favourite,number:p.number,isOwner:p.isOwner}}),w;{let p=x.find(P=>P.number===w.number);return w.name=p!=null&&p.lastname?`${p.firstname} ${p.lastname}`:p==null?void 0:p.firstname,w.avatar=p==null?void 0:p.avatar,w}});R(v.filter(w=>w.unread).length),Z.set(v),I("info","setting cache"),F.APPS.MESSAGES.messages.set(v)})}},[L,r]);let A=S.useRef(!1);return S.useEffect(()=>{var m;if(!A.current&&r!=null&&r.data&&(A.current=!0,r!=null&&r.data&&((m=r.data)==null?void 0:m.view)=="messages")){let v=V==null?void 0:V.find(w=>w.number===r.data.number);v?(T(v),O("messages")):(h(!0),o({number:r.data.number,name:r.data.name,avatar:r.data.avatar}))}},[r==null?void 0:r.data,V]),ne("messages:newMessage",m=>{let v=JSON.parse(JSON.stringify(Z.value)),w=v.findIndex(p=>p.id===m.channelId);if(w!==-1&&v.unshift(v.splice(w,1)[0]),v.length===0)return I("error","No recent messages");v[0].lastMessage=m.content,v[0].timestamp=new Date,Z.set(v)},{waitUntilService:!0}),c(te,{children:[e(ie,{children:E&&e(bt,{})}),c(q.div,{...Te("left","messagelist",.2),className:"animation-container",children:[c("div",{className:"messages-header",children:[c("div",{className:"buttons",children:[e("div",{className:"edit",onClick:()=>K(!G),children:G?i("APPS.MESSAGES.DONE"):i("APPS.MESSAGES.EDIT")}),e(Qe,{"data-disabled":G,onClick:()=>h(!0)})]}),e("div",{className:"title",children:i("APPS.MESSAGES.TITLE")})]}),e(Ke,{placeholder:i("SEARCH"),onChange:m=>z(m.target.value)}),e("div",{className:"users-list",children:V.filter(m=>{var v,w;return m.deleted?!1:(m.isGroup?m.members.find(p=>{var P;return((P=p.name)==null?void 0:P.toLowerCase().includes(_==null?void 0:_.toLowerCase()))||p.number.includes(_)}):((v=m.name)==null?void 0:v.toLowerCase().includes(_==null?void 0:_.toLowerCase()))||m.number.includes(_))||((w=m.lastMessage)==null?void 0:w.toLowerCase().includes(_==null?void 0:_.toLowerCase()))}).sort((m,v)=>v.timestamp-m.timestamp).map((m,v)=>e(wt,{user:m,editMode:G,onClick:()=>{if(T(m),O("messages"),m.unread){k("Messages",{action:"markRead",id:m.id},!0),R(p=>p-1);let w=F.APPS.MESSAGES.messages.value;F.APPS.MESSAGES.messages.set(w.map(p=>(p.id===m.id&&(p.unread=!1),p)))}}},v))}),e(ie,{children:G&&c(q.div,{initial:{opacity:0,y:50},animate:{opacity:1,y:0},exit:{opacity:0,y:50},transition:{duration:.2,ease:"easeInOut"},className:"messages-footer",children:[e("div",{className:"button","data-disabled":!0,children:i("APPS.MESSAGES.READ")}),e("div",{className:"button",onClick:()=>{if(X.length===0)return I("info","No messages selected, can't delete");U.PopUp.set({title:i("APPS.MESSAGES.DELETE_CONVERSATION.TITLE"),description:i("APPS.MESSAGES.DELETE_CONVERSATION.DESCRIPTION"),buttons:[{title:i("APPS.MESSAGES.CANCEL")},{title:i("APPS.MESSAGES.DELETE"),cb:()=>{k("Messages",{action:"deleteConversations",channels:X},!0).then(m=>{if(!m)return I("error","Failed to delete conversations");K(!1),Z.set(V.filter(v=>!X.includes(v.id))),F.APPS.MESSAGES.messages.set([...F.APPS.MESSAGES.messages.value.map(v=>(X.includes(v.id)&&(v.deleted=!0),v))]),ee.set([])})}}]})},children:i("APPS.MESSAGES.DELETE")})]})})]})]})}const wt=({user:t,editMode:l,onClick:g})=>{var O,s;const C=D(W),N=D(B.Settings),M=D(Z),[r,a]=S.useState(!1);let T;const f=be(o=>{U.ContextMenu.set({buttons:[{title:i("APPS.MESSAGES.MARK_AS_READ"),cb:()=>{k("Messages",{action:"markRead",id:t.id},!0).then(E=>{if(!E)return I("error","Failed to mark message as read");Z.set(M.map(d=>(d.id===t.id&&(d.unread=!1),d)));let h=F.APPS.MESSAGES.messages.value;F.APPS.MESSAGES.messages.set(h.map(d=>(d.id===t.id&&(d.unread=!1),d)))})}},{title:i("APPS.MESSAGES.DELETE_CONVERSATION.TITLE"),color:"red",cb:()=>{U.PopUp.set({title:i("APPS.MESSAGES.DELETE_CONVERSATION.TITLE"),description:i("APPS.MESSAGES.DELETE_CONVERSATION.DESCRIPTION"),buttons:[{title:i("APPS.MESSAGES.CANCEL")},{title:i("APPS.MESSAGES.DELETE"),cb:()=>{k("Messages",{action:"deleteConversations",channels:[t.id]},!0).then(E=>{if(!E)return I("error","Failed to delete conversations");Z.set(M.filter(h=>h.id!==t.id)),F.APPS.MESSAGES.messages.set([...F.APPS.MESSAGES.messages.value.map(h=>(h.id===t.id&&(h.deleted=!0),h))]),ee.set([])})}}]})}}]})});if(t.isGroup)if(t.name)T=t.name;else if(t.members.length===0)T=i("APPS.MESSAGES.GROUP");else{let o=t.members.length-1;T=t.members.sort((E,h)=>E.name&&!h.name?-1:!E.name&&h.name?1:E.name<h.name?-1:E.name>h.name?1:0).slice(0,2).map((E,h)=>{let d=E.name?E.name:Y(E.number);return h===1&&o>2?`${d} +${o} ${i("APPS.MESSAGES.OTHER").toLowerCase()}`:d}).join(", ")}else T=t.name;if(t.lastMessage){if(t.lastMessage==="Attachment"&&(t.lastMessage=i("APPS.MESSAGES.SENT_A_PHOTO")),/<!SENT-PAYMENT-(\d*)!>/.test(t.lastMessage)){let o=t.lastMessage.match(/\d/g).join("");t.lastMessage=`${i("APPS.MESSAGES.SENT")} ${C.CurrencyFormat.replace("%s",o)}`}else if(/<!REQUESTED-PAYMENT-(\d*)!>/.test(t.lastMessage)){let o=t.lastMessage.match(/\d/g).join("");t.lastMessage=`${i("APPS.MESSAGES.REQUESTED")} $${o}`}else if(/<!SENT-LOCATION-X=(-?\d*\.?\d*)Y=(-?\d*\.?\d*)!>/.test(t.lastMessage))t.lastMessage=`${i("APPS.MESSAGES.SENT_LOCATION_SHORT")}`;else if(t.lastMessage.startsWith('<!AUDIO-MESSAGE-IMAGE="'))t.lastMessage=i("APPS.MESSAGES.SENT_AUDIO_MESSAGE");else if(t.lastMessage==="<!CALL-NO-ANSWER!>")t.lastMessage=i("APPS.MESSAGES.TRIED_TO_CALL").format({number:Y(t.number)});else if(t.lastMessage==="")return}return S.useEffect(()=>{r?ee.set([...ee.value,t.id]):ee.set(ee.value.filter(o=>o!==t.id))},[r]),c(q.div,{initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},viewport:{once:!0},className:"user",...f,onClick:()=>{l?a(!r):g()},children:[c("div",{className:"items",children:[l&&e(q.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.9},className:"check","data-checked":r,onClick:o=>{o.stopPropagation(),a(!r)},children:r&&e(Je,{})},"check"),e("div",{className:me("dot",t.unread&&"unread")})]}),t.isGroup?c("div",{className:"avatar group",children:[t.members.map((o,E)=>{if(E<=3)return o.avatar?e("div",{"data-hasavatar":"true",style:{backgroundImage:`url(${o.avatar})`}},E):o.name?e("div",{children:o.name.charAt(0)},E):e("div",{className:"unknown"},E)}),t.members.length===0&&e("img",{className:"avatar",src:`assets/img/avatar-placeholder-${N.display.theme}.svg`})]}):e("div",{className:"avatar",style:{backgroundImage:t!=null&&t.avatar?`url(${t.avatar})`:null},children:t.name?!(t!=null&&t.avatar)&&se(t.name):e("img",{src:`assets/img/avatar-placeholder-${N.display.theme}.svg`,alt:""})}),c("div",{className:"user-body",children:[c("div",{className:"user-header",children:[e("div",{className:"name",children:T??Y(t.number)}),c("div",{className:"right",children:[e("div",{className:"time",children:et(t.timestamp)}),e(tt,{})]})]}),e("div",{className:"content",children:((O=t.lastMessage)==null?void 0:O.length)>40?((s=t.lastMessage)==null?void 0:s.substring(0,40))+"...":t.lastMessage})]})]})};const ae=S.createContext(null);function yt(){const[t,l]=S.useState(null),[g,C]=S.useState("userlist"),[N,M]=S.useState(null),[r,a]=S.useState(0),[T,f]=S.useState(!1);return e("div",{className:"messages-container","data-view":g,children:e(ae.Provider,{value:{User:[t,l],View:[g,C],Newmessage:[T,f],ImportedUser:[N,M],UnreadMessages:[r,a]},children:g==="userlist"?e(Tt,{}):t&&e(Pt,{})})})}export{ae as MessagesContext,yt as default};
