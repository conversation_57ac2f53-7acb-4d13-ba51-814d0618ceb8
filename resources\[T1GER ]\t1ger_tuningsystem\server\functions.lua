Core, Cfg, Framework = exports['t1ger_lib']:GetLib()
Lib = exports['t1ger_lib']:GetUtils()

database_table, tuner_shops = 't1ger_tunershops', {}
Config.Shops = {}
jobCooldown = {}
swapped_engines = {}
nitrous = {}
nos_data = {}
dyno = {}

FetchTunerShops = function()
    local results = MySQL.Sync.fetchAll('SELECT * FROM '..database_table..'')
    local shops = {}
    for k,v in pairs(results) do
        local args = {
            id = v.id,
            name = v.name,
            account = v.account,
            boss = v.boss,
            markup = v.markup or Config.Markup.Default,
            job = json.decode(v.job),
            blip = json.decode(v.blip),
            employees = json.decode(v.employees),
            markers = json.decode(v.markers),
            categories = json.decode(v.categories),
            storage = json.decode(v.storage),
            billing = json.decode(v.billing),
            orders = json.decode(v.orders),
            delivery = json.decode(v.delivery)
        }
        shops[v.id] = CreateShopClass(args)
    end
    return shops
end

InitializeResource = function()
    Config.Shops = FetchTunerShops()
    Wait(1000)
    for id, shop in pairs(Config.Shops) do
        -- create/register storage/stash
        if Config.Markers['storage'].enable and next(shop.markers) then
            if shop.markers['storage'] ~= nil and next(shop.markers['storage']) then
                for k,v in pairs(shop.markers['storage']) do
                    local stash = shop.markers['storage'][k].stash
                    Core.RegisterStash(stash.id, stash.label, stash.slots, stash.weight, nil)
                end
            end
        end
        -- create/ensure job:
        local job = Core.CreateJob(shop.job)
        if Config.Debug then
            if Framework == 'QB' then
                print("The job: "..job.label.." ("..shop.job.name..") has been ensured for: "..shop.name.." ["..shop.id.."]")
            else
                print("The job: "..job.label.." ("..job.name..") has been ensured for: "..shop.name.." ["..shop.id.."]")
            end
        end
    end
    Wait(1000)
	RconPrint('T1GER Tuning System Initialized\n')
end

IsEmployeeInShop = function(identifier)
    if next(Config.Shops) then
        for _,val in pairs(Config.Shops) do
            local employee, callback = Config.Shops[val.id].getEmployee(identifier)
            if employee then 
                return val.id
            end
        end
    end
    return 0
end

IsPlayerAlreadyBoss = function(identifier)
    if next(Config.Shops) then
        for _,val in pairs(Config.Shops) do
            if val.boss == identifier then 
                return true, val.id
            end
        end
    end
    return false
end

GetCooldownTime = function(identifier)
	for k,v in pairs(jobCooldown) do
		if v.identifier == identifier then
			local seconds, cooldown_time = false, (v.time/60000)
			if cooldown_time < 1 then
				cooldown_time = v.time/1000
				seconds = true
			end
			return seconds, math.ceil(cooldown_time)
		end
	end
end

HasCooldown = function(identifier)
	for k,v in pairs(jobCooldown) do
        if v.identifier == identifier then
            return true
        end
    end
	return false
end

SetEngineSound = function(netId, soundName, model)
    local vehicle = NetworkGetEntityFromNetworkId(netId)
    if not DoesEntityExist(vehicle) then return end
    local plate = GetVehicleNumberPlateText(vehicle)

    local result, callback = Core.GetVehicleByPlate(plate)
    if result and next(result) ~= nil then
        MySQL.insert('INSERT INTO t1ger_engineswaps (plate, engine, vehicle) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE engine = ?', {plate, soundName, model, soundName})
    else
        if Config.Debug then 
            print("Engine Swap | Not Saved | Plate: "..plate.." - Does not exists in database!")
        end
    end

    swapped_engines[tostring(plate)] = {plate = plate, soundName = soundName, model = model} 
    
    local ent = Entity(vehicle).state
    ent:set('tuningsystem:forceEngineSound', soundName, true)
end

EnsureVehicleEngineSound = function(entity)
    if DoesEntityExist(entity) and GetEntityType(entity) == 2 and GetEntityPopulationType(entity) == 7 then
        local vehicle = entity
        local plate = GetVehicleNumberPlateText(vehicle)
        if swapped_engines[tostring(plate)] ~= nil then
            local ent = Entity(vehicle).state
            ent:set('tuningsystem:forceEngineSound', swapped_engines[tostring(plate)].soundName, true)
        end
    end
end

EnsureVehicleDynoProperties = function(entity)
    if DoesEntityExist(entity) and GetEntityType(entity) == 2 and GetEntityPopulationType(entity) == 7 then
        local vehicle = entity
        local plate = GetVehicleNumberPlateText(vehicle)
        if dyno[tostring(plate)] ~= nil then
            local ent = Entity(vehicle).state
            ent:set('tuningsystem:vehicleDynoProperties', dyno[tostring(plate)], true)
        end
    end
end

SaveVehicleDyno = function(vehicle)
    if DoesEntityExist(vehicle) and GetEntityType(vehicle) == 2 and GetEntityPopulationType(vehicle) == 7 then
        local plate = GetVehicleNumberPlateText(vehicle)
        local ent = Entity(vehicle).state
        if dyno[tostring(plate)] ~= nil then
            local ent = Entity(vehicle).state
            local data = ent['tuningsystem:vehicleDynoProperties']
            if data == nil then data = dyno[tostring(plate)] end
            -- database update if owned vehicle:
            local result, callback = Core.GetVehicleByPlate(plate)
            if result and next(result) ~= nil then
                MySQL.insert('INSERT INTO t1ger_dyno (plate, torque, power, brakes) VALUES (?, ?, ?, ?) ON DUPLICATE KEY UPDATE torque = ?, power = ?, brakes = ?', {
                    plate, data.torque, data.power, data.brakes, data.torque, data.power, data.brakes
                })
                if Config.Debug then
                    print("Dyno Properties Saved | "..plate.." | "..json.encode(data))
                end
            else
                if Config.Debug then
                    print("Dyno Properties not saved | "..plate.." | Plate does not exists in database!")
                end
            end
        end
    end
end

EnsureVehicleNitrousProperties = function(entity)
    if DoesEntityExist(entity) and GetEntityType(entity) == 2 and GetEntityPopulationType(entity) == 7 then
        local vehicle = entity
        local plate = GetVehicleNumberPlateText(vehicle)
        if nitrous[tostring(plate)] ~= nil then
            local ent = Entity(vehicle).state
            ent:set('tuningsystem:vehicleNitrousProperties', nitrous[tostring(plate)], true)
            if nos_data[tostring(plate)] ~= nil then
                nos_data[tostring(plate)].netId = NetworkGetNetworkIdFromEntity(vehicle)
                ent:set('tuningsystem:nitrousData', nos_data[tostring(plate)], true)
            end
            if Config.Nitrous.Burst.cooldown == true then
                ent:set('tuningsystem:nitrousCooldown', false, true)
            end
        end
    end
end

SaveVehicleNitrous = function(vehicle)
    if DoesEntityExist(vehicle) and GetEntityType(vehicle) == 2 and GetEntityPopulationType(vehicle) == 7 then
        local plate = GetVehicleNumberPlateText(vehicle)
        local ent = Entity(vehicle).state
        if nitrous[tostring(plate)] ~= nil then
            local ent = Entity(vehicle).state
            local data = ent['tuningsystem:vehicleNitrousProperties']
            if data == nil then data = nitrous[tostring(plate)] end
            -- database update if owned vehicle:
            local result, callback = Core.GetVehicleByPlate(plate)
            if result and next(result) ~= nil then
                MySQL.insert('INSERT INTO t1ger_nitrous (plate, size, shots, burst, color) VALUES (?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE size = ?, shots = ?, burst = ?, color = ?', {
                    plate, data.size, data.shots, data.burst, json.encode(data.color), data.size, data.shots, data.burst, json.encode(data.color)
                })
                if Config.Debug then
                    print("Nitrous Properties Saved | "..plate.." | "..json.encode(data))
                end
            else
                if Config.Debug then
                    print("Nitrous Properties not saved | "..plate.." | Plate does not exists in database!")
                end
            end
        end
    end
end

SyncNitrousData = function(vehicle)
    if DoesEntityExist(vehicle) and GetEntityType(vehicle) == 2 and GetEntityPopulationType(vehicle) == 7 then
        local plate = GetVehicleNumberPlateText(vehicle)
        local ent = Entity(vehicle).state
        nos_data[tostring(plate)] = ent['tuningsystem:nitrousData']
        if Config.Debug then
            print("Nitrous Data Cached | "..plate.." | "..json.encode(nos_data[tostring(plate)]))
        end
    end
end

StartNitrousCooldown = function(plate, netId)
    local vehicle = NetworkGetEntityFromNetworkId(netId)
    if DoesEntityExist(vehicle) and nos_data[tostring(plate)] == nil then
        local ent = Entity(vehicle).state
        nos_data[tostring(plate)] = ent['tuningsystem:nitrousData']
    end
    -- save netId:
    nos_data[tostring(plate)].netId = netId
    SetTimeout(Config.Nitrous.Purge.cooldown * 60 * 1000, function()
        if nos_data[tostring(plate)] ~= nil then
            nos_data[tostring(plate)].efficiency.cooldown = false
            nos_data[tostring(plate)].efficiency.threshold = 0
        else
            print("error | could not find nos_data for plate: "..plate)
        end
        local veh = NetworkGetEntityFromNetworkId(nos_data[tostring(plate)].netId)
        if not DoesEntityExist(veh) then
            return
        end
        local ent = Entity(veh).state
        ent:set('tuningsystem:nitrousData', nos_data[tostring(plate)], true)
    end)
end

-- On Entity Creation:
AddEventHandler('entityCreated', function(entity)
    Wait(1000)
    EnsureVehicleEngineSound(entity)
    EnsureVehicleNitrousProperties(entity)
    EnsureVehicleDynoProperties(entity)
end)

SaveVehicleCustomProps = function(vehicle)
    if DoesEntityExist(vehicle) and GetEntityType(vehicle) == 2 and GetEntityPopulationType(vehicle) == 7 then
        local plate = GetVehicleNumberPlateText(vehicle)
        local ent = Entity(vehicle).state
        local result, callback = Core.GetVehicleByPlate(plate)

        if nitrous[tostring(plate)] ~= nil then
            local data = ent['tuningsystem:vehicleNitrousProperties']
            if data == nil then data = nitrous[tostring(plate)] end
            -- database update if owned vehicle:
            if result and next(result) ~= nil then
                MySQL.insert('INSERT INTO t1ger_nitrous (plate, size, shots, burst, color) VALUES (?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE size = ?, shots = ?, burst = ?, color = ?', {
                    plate, data.size, data.shots, data.burst, json.encode(data.color), data.size, data.shots, data.burst, json.encode(data.color)
                })
                if Config.Debug then
                    print("Nitrous Properties Saved | "..plate.." | "..json.encode(data))
                end
            else
                if Config.Debug then
                    print("Nitrous Properties not saved | "..plate.." | Plate does not exists in database!")
                end
            end
        end

        if dyno[tostring(plate)] ~= nil then
            local data = ent['tuningsystem:vehicleDynoProperties']
            if data == nil then data = dyno[tostring(plate)] end
            -- database update if owned vehicle:
            if result and next(result) ~= nil then
                MySQL.insert('INSERT INTO t1ger_dyno (plate, torque, power, brakes) VALUES (?, ?, ?, ?) ON DUPLICATE KEY UPDATE torque = ?, power = ?, brakes = ?', {
                    plate, data.torque, data.power, data.brakes, data.torque, data.power, data.brakes
                })
                if Config.Debug then
                    print("Dyno Properties Saved | "..plate.." | "..json.encode(data))
                end
            else
                if Config.Debug then
                    print("Dyno Properties not saved | "..plate.." | Plate does not exists in database!")
                end
            end
        end
    end
end

-- On Entity Deletion:
AddEventHandler('entityRemoved', function(entity)
    SyncNitrousData(entity)
    SaveVehicleCustomProps(entity)
end)

-- Vehicle Props Statebag:
AddStateBagChangeHandler('t1ger_lib:setVehicleProps' --[[key filter]], nil --[[bag filter]], function(bagName, key, value, _unused, replicated)
	Wait(0)

	local net = tonumber(bagName:gsub('entity:', ''), 10)
	local vehicle = net and NetworkGetEntityFromNetworkId(net)

    Wait(1000)
    
	if DoesEntityExist(vehicle) then
        EnsureVehicleEngineSound(vehicle)
        EnsureVehicleNitrousProperties(vehicle)
	end
end)

-- Vehicle Dyno Updater State Bag:
AddStateBagChangeHandler('tuningsystem:vehicleDynoProperties' --[[key filter]], nil --[[bag filter]], function(bagName, key, value, _unused, replicated)
	Wait(0)
    local net = tonumber(bagName:gsub('entity:', ''), 10)
	local vehicle = net and NetworkGetEntityFromNetworkId(net)

    if Config.Debug then
        print("dyno updated: ", json.encode(value))
    end

	if not value or vehicle == 0 then return end

    if DoesEntityExist(vehicle) then
        local plate = GetVehicleNumberPlateText(vehicle)
        if dyno[tostring(plate)] == nil then
            dyno[tostring(plate)] = value
            SaveVehicleDyno(vehicle)
        else
            dyno[tostring(plate)] = value
        end
    end
end)

-- Vehicle Nitrous Updater State Bag:
AddStateBagChangeHandler('tuningsystem:vehicleNitrousProperties' --[[key filter]], nil --[[bag filter]], function(bagName, key, value, _unused, replicated)
	Wait(0)
    local net = tonumber(bagName:gsub('entity:', ''), 10)
	local vehicle = net and NetworkGetEntityFromNetworkId(net)

    if Config.Debug then
        print("nitrous kit updated: ", json.encode(value))
    end

	if not value or vehicle == 0 then return end

    Wait(1000)

    if DoesEntityExist(vehicle) then
        local plate = GetVehicleNumberPlateText(vehicle)
        if nitrous[tostring(plate)] == nil then
            nitrous[tostring(plate)] = value
            SaveVehicleNitrous(vehicle)
        else
            nitrous[tostring(plate)] = value
        end
    end
end)

Citizen.CreateThread(function()
    Wait(1000)

    local results = MySQL.query.await('SELECT * from t1ger_engineswaps')
    if type(results) == 'table' and next(results) ~= nil then 
        for i = 1, #results do
            swapped_engines[tostring(results[i].plate)] = {plate = results[i].plate, soundName = results[i].engine, model = results[i].vehicle} 
        end
    end

    local results = MySQL.query.await('SELECT * from t1ger_nitrous')
    if type(results) == 'table' and next(results) ~= nil then 
        for i = 1, #results do
            nitrous[tostring(results[i].plate)] = {size = results[i].size, shots = results[i].shots, burst = results[i].burst, color = json.decode(results[i].color)} 
        end
    end

    local results = MySQL.query.await('SELECT * from t1ger_dyno')
    if type(results) == 'table' and next(results) ~= nil then 
        for i = 1, #results do
            dyno[tostring(results[i].plate)] = {torque = results[i].torque, power = results[i].power, brakes = results[i].brakes} 
        end
    end

    Wait(2000)

    for _,vehicle in ipairs(GetAllVehicles()) do
        local plate = GetVehicleNumberPlateText(vehicle)
        if swapped_engines[tostring(plate)] ~= nil then
            local ent = Entity(vehicle).state
            ent:set('tuningsystem:forceEngineSound', swapped_engines[tostring(plate)].soundName, true)
        end
        if nitrous[tostring(plate)] ~= nil then
            local ent = Entity(vehicle).state
            ent:set('tuningsystem:vehicleNitrousProperties', nitrous[tostring(plate)], true)
        end 
        if dyno[tostring(plate)] ~= nil then
            local ent = Entity(vehicle).state
            ent:set('tuningsystem:vehicleDynoProperties', dyno[tostring(plate)], true)
        end 
    end

    RconPrint('T1GER Tuning System - CACHED ALL ENGINE SWAPS\n')
    RconPrint('T1GER Tuning System - CACHED ALL NITROUS KITS\n')
    RconPrint('T1GER Tuning System - CACHED ALL DYNO MODIFIERS\n')
end)

Citizen.CreateThread(function()
	while true do
        Citizen.Wait(1000)
        if jobCooldown ~= nil and next(jobCooldown) then 
            for k,v in pairs(jobCooldown) do
                if v.time <= 0 then
                    table.remove(jobCooldown, k)
                else
                    v.time = v.time - 1000
                end
            end
        end
	end
end)

SyncTunerShops = function()
    if Config.Debug then
        RconPrint('[SAVED] T1GER TUNER SHOPS\n')
    end
    if tuner_shops and next(tuner_shops) then
        for k,v in pairs(tuner_shops) do
            MySQL.Sync.execute(
                'UPDATE '..database_table..' SET account = ?, boss = ?, markup = ?, job = ?, employees = ?, markers = ?, categories = ?, storage = ?, billing = ?, orders = ?, delivery = ? WHERE id = ?',
                {v.account, v.boss, v.markup, v.job, json.encode(v.employees), json.encode(v.markers), json.encode(categories), json.encode(v.storage), json.encode(v.billing), json.encode(v.orders), json.encode(v.delivery), v.id}
            )
        end
    end
end
