if not Config.FlipVehicle.Enable then 
    return 
end

--- Function to flip closest vehicle
function FlipVehicle()
    if not Config.FlipVehicle.Enable then return end

    local isMechanic, shopId = IsPlayerMechanic()
    if not isMechanic then return end
    
	-- ray cast:
	local hit, entityHit, endCoords, surfaceNormal, materialHash = lib.raycast.fromCoords(coords, GetOffsetFromEntityInWorldCoords(player, 0.0, 5.0, 0.0), 2)
	if not hit or GetEntityType(entityHit) ~= 2 or not IsEntityAVehicle(entityHit) or not DoesEntityExist(entityHit) then 
		return _API.ShowNotification(locale("notification.no_vehicle_in_direction"), "inform", {})
	end

	local flipped = false 
    local min, max = GetModelDimensions(GetEntityModel(entityHit))
    local flipCoords = GetOffsetFromEntityInWorldCoords(entityHit, min.x-0.2,0.0,0.0)

    while not flipped do
        Wait(1)

        local distance = #(coords - vector3(flipCoords.x, flipCoords.y, flipCoords.z))
        if distance < Config.FlipVehicle.DrawDist then
            -- draw text
            Draw3DText(flipCoords.x, flipCoords.y, flipCoords.z, locale("drawtext.flip_vehicle"))

            -- key press
            if IsControlJustReleased(0, Config.FlipVehicle.Keybind) then
                if distance <= 1.0 then
                    TaskTurnPedToFaceEntity(player, entityHit, 1.0)
                    Wait(500)
                    SetCurrentPedWeapon(player, GetHashKey("WEAPON_UNARMED"), true)
                    Wait(300)

                    if ProgressBar({
                        duration = Config.FlipVehicle.Duration,
                        label = locale("progressbar.flip_vehicle"),
                        useWhileDead = false,
                        canCancel = true,
                        anim = {
                            scenario = Config.FlipVehicle.Scenario
                        },
                        disable = {
                            move = true,
                            combat = true
                        }
                    }) then
                        ClearPedTasks(player)
                        flipped = true
                        break
                    end

                else
                    _API.ShowNotification(locale("notification.move_closer_to_interact"), "inform", {})
                end
            end
        end
    end
    
    -- complete?
    if flipped then
        SetVehicleOnGroundProperly(entityHit)
    end
end

--- Command to flip closest vehicle
if Config.FlipVehicle.Command.enable == true then
    RegisterCommand(Config.FlipVehicle.Command.name, function()
        FlipVehicle()
    end, false)
end