CarLift = {}
CarLift.__index = CarLift

---Creates a new CarLift instance
---@param id number CarLift ID
---@param data table CarLift data from the database
---@return CarLift
function CarLift:New(id, coords, rotation)
    local self = setmetatable({}, CarLift)

    self.id = id
    self.coords = coords
    self.rotation = rotation
    self.spawned = false

    return self
end

---Returns carlift data
---@return table data carlift data (id, coords, rotation, spawned, baseNetId, armNetId)
function CarLift:GetData()
    return {
        id = self.id,
        coords = self.coords,
        rotation = self.rotation,
        spawned = self.spawned,
        baseNetId = self.baseNetId,
        armNetId = self.armNetId
    }
end

---Returns whether the carlift is spawned or not
---@return boolean isSpawned `true` if carlift is spawned. `false` otherwise
function CarLift:IsSpawned()
    return self.spawned
end

---Spawns carlift objects
function CarLift:Spawn()
    if self:IsSpawned() then return end 
    
    -- create base:
    local baseObject = CreateObjectNoOffset(Config.CarLift.Model.base, self.coords.x, self.coords.y, self.coords.z, true, true, false)
    FreezeEntityPosition(baseObject, true)
    SetEntityRotation(baseObject, self.rotation.x, self.rotation.y, self.rotation.z)

    -- get networkd id:
    self.baseNetId = NetworkGetNetworkIdFromEntity(baseObject)

    -- create arms:
    local armObject = CreateObjectNoOffset(Config.CarLift.Model.arm, self.coords.x, self.coords.y, self.coords.z, true, true, false)
    FreezeEntityPosition(armObject, true)
    SetEntityRotation(armObject, self.rotation.x, self.rotation.y, self.rotation.z)

    -- get networkd id:
    self.armNetId = NetworkGetNetworkIdFromEntity(armObject)

    -- update state:
    self.spawned = true

    -- statebag:
    local ent = Entity(baseObject).state
    ent:set("t1ger_mechanic:carLiftCreated", self:GetData(), true)
end

---Deletes a spawned car lift
---@param permanent boolean `true` delete permanently. `false` only delete props/objects
function CarLift:Delete(permanent)
    -- delete prop/object
    local arm = NetworkGetEntityFromNetworkId(self.armNetId)
    DeleteEntity(arm)
    local base = NetworkGetEntityFromNetworkId(self.baseNetId)
    DeleteEntity(base)

    -- if not permanent then return
    if type(permanent) ~= "boolean" or not permanent then 
        return 
    end

    -- Remove from database
    MySQL.query("DELETE FROM t1ger_carlifts WHERE id = ?", {self.id})

    -- Sync with clients
    TriggerClientEvent("t1ger_mechanic:client:carLiftDeleted", -1, self.baseNetId)
    
    -- Now remove from CarLifts table
    CarLifts[self.id] = nil
end