Config.PriceEscalationPercent = 5 -- Integer | value in percent between 0-100 where setting to 0 will disable price escalation, otherwise mod variant prices increase progressively. Making each higher option cost more.

Config.PartAcquisitionCost = true -- boolean | if true, item price from Config.Items['mods'] will be added to the calculated mod price price, else skipped. Ideally this would cover the cost to acquire parts if enabled.

Config.DefaultVehiclePrice = 3000 -- integer | Default vehicle price to be used if the script cannot retrieve the vehicle value from a database table or shared lua file.

Config.TieredPricing = true -- boolean | if true then tier-based multipliers for mod pricing, to disable set to false. This option uses Config.PriceTiers.

Config.PriceTiers = { -- A list of tiers where each tier specifies an upper limit and a corresponding multiplier. Config.TieredPricing must be enabled.
    { upperLimit = 10000, multiplier = 0.05 }, -- Tier 1: For vehicle prices up to $10,000, apply a multiplier of 5%.
    { upperLimit = 50000, multiplier = 0.03 }, -- Tier 2: For vehicle prices between $10,000 and $50,000, apply a multiplier of 3%.
    { upperLimit = 100000, multiplier = 0.02 }, -- Tier 3: For vehicle prices between $50,000 and $100,000, apply a multiplier of 2%.
    { upperLimit = 500000, multiplier = 0.01 }, -- Tier 4: For vehicle prices between $100,000 and $500,000, apply a multiplier of 1%.
    { upperLimit = math.huge, multiplier = 0.005 }, -- Tier 5: For vehicle prices above $500,000, apply a multiplier of 0.5%.
    -- u can easily add more tiers, edit the current ones but make sure that math.huge is used to represent infinity for the highest tier.
}

Config.ModCategories = { -- All mods from Config.Mods are sorted in these categories. You are only allowed to change title, positition and icon (also supports fontawesome icons). Changing anything else will break the script functionality.
    ['respray'] = {id = 'mod_respray', title = 'Respray', position = 'top-left', icon = 'https://i.ibb.co/T0q9bgj/mod-respray.png'},
    ['bodyworks'] = {id = 'mod_bodyworks', title = 'Body Works', position = 'top-left', icon = 'https://i.ibb.co/YZkqQRj/mod-bodyworks.png'},
    ['chassis'] = {id = 'mod_chassis', title = 'Chassis', position = 'top-left', icon = 'https://i.ibb.co/1XGXthM/mod-chassis.png'},
    ['enginebay'] = {id = 'mod_enginebay', title = 'Engine Bay', position = 'top-left', icon = 'https://i.ibb.co/ngv65rV/mod-enginebay.png'},
    ['exterior'] = {id = 'mod_exterior', title = 'Exterior', position = 'top-left', icon = 'https://i.ibb.co/wz2s9mG/mod-exterior.png'},
    ['interior'] = {id = 'mod_interior', title = 'Interior', position = 'top-left', icon = 'https://i.ibb.co/ZKZS3mP/mod-interior.png'},
    ['lights'] = {id = 'mod_lights', title = 'Lights', position = 'top-left', icon = 'https://i.ibb.co/P56HBBg/mod-lights.png'},
    ['wheels'] = {id = 'mod_wheels', title = 'Wheels', position = 'top-left', icon = 'https://i.ibb.co/8BbyF7y/mod-wheels.png'},
    ['performance'] = {id = 'mod_performance', title = 'Performance', position = 'top-left', icon = 'https://i.ibb.co/MgPCg06/mod-engine.png'}
}

--Not all servers prefer the players being able to install special mods, such as weapons, proximity mines and so on. The resource have disabled some of the most common mods that are seen in-game. You can alter this array by finding the config option inside the mods.lua file.
Config.DisableMods = { -- Add specific mods in here you want to disable. If you find more in-game, you can add them in here. By default, weapon mods are disabled
    'Primary Weapons', 'Proximity Mine', 'Armor Plating', 'Ram Weapons', 'Imani Tech', 'Blades', 'Spikes', 'Secondary Weapons'
}

Config.Mods = {
    modSpoilers = {
        label = 'Spoiler', modType = 0, price = 700, category = 'bodyworks', point = 'rear', item = 'mod_spoiler', icon = 'https://i.ibb.co/d5p3PF5/mod-spoiler.png',
        prop = {model = 'imp_prop_impexp_spoiler_01a', pos = {0.0, -0.2, -0.2}, rot = {0.0, 0.0, -180.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'mini@repair', name = 'fixing_a_player', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modFrontBumper = {
        label = 'Front Bumper', modType = 1, price = 2100, category = 'bodyworks', point = 'front', item = 'mod_frontbumper', icon = 'https://i.ibb.co/Fhsy0Ld/mod-frontbumper.png',
        prop = {model = 'imp_prop_impexp_front_bumper_01a', pos = {0.0, -0.25, -0.15}, rot = {0.0, 0.0, -180.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modRearBumper = {
        label = 'Rear Bumper', modType = 2, price = 1800, category = 'bodyworks', point = 'rear', item = 'mod_rearbumper', icon = 'https://i.ibb.co/kXBRkjv/mod-rearbumper.png',
        prop = {model = 'imp_prop_impexp_rear_bumper_03a', pos = {0.0, -0.25, -0.15}, rot = {-20.0, 0.0, -180.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modSideSkirt = {
        label = 'Skirt', modType = 3, price = 800, category = 'bodyworks', point = 'vehicle', item = 'mod_sideskirt', icon = 'https://i.ibb.co/crc48qZ/mod-sideskirt.png',
        prop = {model = 'imp_prop_impexp_rear_bumper_01a', pos = {0.0, -0.25, -0.15}, rot = {-20.0, 0.0, -180.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modExhaust = {
        label = 'Exhaust', modType = 4, price = 1000, category = 'bodyworks', point = 'rear', item = 'mod_exhaust', icon = 'https://i.ibb.co/mzVgNSC/mod-exhaust.png',
        prop = {model = 'imp_prop_impexp_exhaust_01', pos = {-0.1, -0.20, -0.20}, rot = {20.0, 10.0, 90.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modFrame = {
        label = 'Roll Cage', modType = 5, price = 2300, category = 'chassis', point = 'vehicle', item = 'mod_frame', icon = 'https://i.ibb.co/R4wnXnV/mod-frame.png',
        prop = {model = 'imp_prop_impexp_rear_bars_01b', pos = {0.0, 0.60, -0.26}, rot = {-30.0, 0.0, -180.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'mini@repair', name = 'fixing_a_player', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modGrille = {
        label = 'Grill', modType = 6, price = 900, category = 'exterior', point = 'front', item = 'mod_grille', icon = 'https://i.ibb.co/Jzg8mhf/mod-grille.png',
        prop = {model = 'imp_prop_impexp_radiator_01', pos = {0.0, -0.28, -0.23}, rot = {0.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modHood = {
        label = 'Hood', modType = 7, price = 1900, category = 'bodyworks', point = 'front', item = 'mod_hood', icon = 'https://i.ibb.co/hZcZZqB/mod-hood.png',
        prop = {model = 'imp_prop_impexp_bonnet_04a', pos = {0.0, 0.20, 0.12}, rot = {-20.0, 0.0, 180.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'mini@repair', name = 'fixing_a_player', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modFender = {
        label = 'Left Fender', modType = 8, price = 1200, category = 'bodyworks', point = 'vehicle', item = 'mod_fender', icon = 'https://i.ibb.co/wCT743n/mod-fender.png',
        prop = {model = 'imp_prop_impexp_car_panel_01a', pos = {-0.15, -0.18, -0.16}, rot = {5.0, 0.0, -90.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modRightFender = {
        label = 'Right Fender', modType = 9, price = 1200, category = 'bodyworks', point = 'vehicle', item = 'mod_fender', icon = 'https://i.ibb.co/wCT743n/mod-fender.png',
        prop = {model = 'imp_prop_impexp_car_panel_01a', pos = {0.0, -0.0, -0.4}, rot = {0.0, 0.0, 90.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modRoof = {
        label = 'Roof', modType = 10, price = 1100, category = 'bodyworks', point = 'vehicle', item = 'mod_roof', icon = 'https://i.ibb.co/7SqxCkM/mod-roof.png',
        prop = {model = 'prop_cs_cardbox_01', pos = {0.0, -0.2, -0.1}, rot = {40.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'mini@repair', name = 'fixing_a_player', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modEngine = {
        label = 'Engine', modType = 11, category = 'performance', point = 'engine', item = 'mod_engine', icon = 'https://i.ibb.co/289jHGP/mod-engine.png',
        variants = {
            ['-1'] = {label = 'Stock Engine', index = -1, price = 3000},
            ['0'] = {label = 'EMS Upg. Level 1', index = 0, price = 5000},
            ['1'] = {label = 'EMS Upg. Level 2', index = 1, price = 7000},
            ['2'] = {label = 'EMS Upg. Level 3', index = 2, price = 9000},
            ['3'] = {label = 'EMS Upg. Level 4', index = 3, price = 12000},
            ['4'] = {label = 'Custom Engine Tune', index = 4, price = 15000}
        },
        prop = {model = 'imp_prop_impexp_engine_part_01a', pos = {0.0, -0.1, -0.20}, rot = {40.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'mini@repair', name = 'fixing_a_player', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modBrakes = {
        label = 'Brakes', modType = 12, category = 'performance', point = 'wheel', item = 'mod_brakes', icon = 'https://i.ibb.co/b5gZqr2/mod-brakes.png',
        variants = {
            ['-1'] = {label = 'Stock Brakes', index = -1, price = 1500},
            ['0'] = {label = 'Street Brakes', index = 0, price = 2500},
            ['1'] = {label = 'Sports Brakes', index = 1, price = 3500},
            ['2'] = {label = 'Race Brakes', index = 2, price = 4500},
            ['3'] = {label = 'Custom Brakes', index = 3, price = 5500}
        },
        prop = {model = 'imp_prop_impexp_brake_caliper_01a', pos = {0.0, -0.1, -0.2}, rot = {0.0, 90.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modTransmission = {
        label = 'Transmission', modType = 13, category = 'performance', point = 'engine', item = 'mod_transmission', icon = 'https://i.ibb.co/3YcbPkN/mod-transmission.png',
        variants = {
            ['-1'] = {label = 'Stock Transmission', index = -1, price = 2000},
            ['0'] = {label = 'Street Transmission', index = 0, price = 3500},
            ['1'] = {label = 'Sports Transmission', index = 1, price = 5000},
            ['2'] = {label = 'Race Transmission', index = 2, price = 6500},
            ['3'] = {label = 'Custom Transmission', index = 3, price = 7000}
        },
        prop = {model = 'imp_prop_impexp_gearbox_01', pos = {0.0, -0.1, -0.2}, rot = {0.0, 0.0, 90.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'mini@repair', name = 'fixing_a_player', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modHorns = {
        label = 'Horn', modType = 14, category = 'exterior', point = 'engine', item = 'mod_horn', icon = 'https://i.ibb.co/jVCNtTK/mod-horn.png',
        variants = {
            ['-1'] = {label = 'Stock',				    index = -1,					price = 250},
            ['0'] = {label = 'Truck Horn',			    index = 0,					price = 400},
            ['1'] = {label = 'Cop Horn',			    index = 1,					price = 400},
            ['2'] = {label = 'Clown Horn',			    index = 2,					price = 400},
            ['3'] = {label = 'Musical Horn 1',		    index = 3,					price = 550},
            ['4'] = {label = 'Musical Horn 2',		    index = 4,					price = 550},
            ['5'] = {label = 'Musical Horn 3',		    index = 5,					price = 550},
            ['6'] = {label = 'Musical Horn 4',		    index = 6,					price = 550},
            ['7'] = {label = 'Musical Horn 5',		    index = 7,					price = 550},
            ['8'] = {label = 'Sad Trombone',		    index = 8,					price = 600},
            ['9'] = {label = 'Classical Horn 1',	    index = 9,					price = 700},
            ['10'] = {label = 'Classical Horn 2',	    index = 10,					price = 700},
            ['11'] = {label = 'Classical Horn 3',	    index = 11,					price = 700},
            ['12'] = {label = 'Classical Horn 4',	    index = 12,					price = 700},
            ['13'] = {label = 'Classical Horn 5',	    index = 13,					price = 700},
            ['14'] = {label = 'Classical Horn 6',	    index = 14,					price = 700},
            ['15'] = {label = 'Classical Horn 7',	    index = 15,					price = 700},
            ['16'] = {label = 'Scale - Do',			    index = 16,					price = 800},
            ['17'] = {label = 'Scale - Re',			    index = 17,					price = 800},
            ['18'] = {label = 'Scale - Mi',			    index = 18,					price = 800},
            ['19'] = {label = 'Scale - Fa',			    index = 19,					price = 800},
            ['20'] = {label = 'Scale - Sol',			index = 20,					price = 800},
            ['21'] = {label = 'Scale - La',			    index = 21,					price = 800},
            ['22'] = {label = 'Scale - Ti',			    index = 22,					price = 800},
            ['23'] = {label = 'Scale - Do',			    index = 23,					price = 800},
            ['24'] = {label = 'Jazz Horn 1',			index = 24,					price = 850},
            ['25'] = {label = 'Jazz Horn 2',			index = 25,					price = 850},
            ['26'] = {label = 'Jazz Horn 3',			index = 26,					price = 850},
            ['27'] = {label = 'Jazz Horn Loop', 		index = 27,					price = 850},
            ['28'] = {label = 'Star Spangled Banner 1', index = 28,				    price = 900},
            ['29'] = {label = 'Star Spangled Banner 2', index = 29,				    price = 900},
            ['30'] = {label = 'Star Spangled Banner 3', index = 30,				    price = 900},
            ['31'] = {label = 'Star Spangled Banner 4', index = 31,				    price = 900},
            ['32'] = {label = 'Classical Horn 8 Loop',  index = 32,				    price = 1000},
            ['33'] = {label = 'Classical Horn 9 Loop',  index = 33,				    price = 1000},
            ['34'] = {label = 'Classical Horn 10 Loop', index = 34,				    price = 1000},
            ['35'] = {label = 'Classical Horn 8', 	    index = 35,					price = 1100},
            ['36'] = {label = 'Classical Horn 9', 	    index = 36,					price = 1100},
            ['37'] = {label = 'Classical Horn 10', 	    index = 37,					price = 1100},
            ['38'] = {label = 'Funeral Loop', 		    index = 38,					price = 1200},
            ['39'] = {label = 'Funeral', 			    index = 39,					price = 1200},
            ['40'] = {label = 'Spooky Loop', 		    index = 40,					price = 1200},
            ['41'] = {label = 'Spooky', 				index = 41,					price = 1200},
            ['42'] = {label = 'San Andreas Loop', 	    index = 42,					price = 1200},
            ['43'] = {label = 'San Andreas', 		    index = 43,					price = 1200},
            ['44'] = {label = 'Liberty City Loop', 	    index = 44,					price = 1200},
            ['45'] = {label = 'Liberty City', 		    index = 45,					price = 1200},
            ['46'] = {label = 'Festive 1 Loop', 		index = 46,					price = 1250},
            ['47'] = {label = 'Festive 1', 			    index = 47,					price = 1250},
            ['48'] = {label = 'Festive 2 Loop', 		index = 48,					price = 1250},
            ['49'] = {label = 'Festive 2', 			    index = 49,					price = 1250},
            ['50'] = {label = 'Festive 3 Loop', 		index = 50,					price = 1250},
            ['51'] = {label = 'Festive 3', 			    index = 51,					price = 1250},
            ['52'] = {label = 'Air Horn Low Loop', 	    index = 52,					price = 1300},
            ['53'] = {label = 'Air Horn Low', 		    index = 53,					price = 1300},
            ['54'] = {label = 'Air Horn Medium Loop',   index = 54,				    price = 1300},
            ['55'] = {label = 'Air Horn Medium', 	    index = 55,					price = 1300},
            ['56'] = {label = 'Air Horn High Loop', 	index = 56,					price = 1300},
            ['57'] = {label = 'Air Horn High', 		    index = 57,					price = 1300}
        },
        prop = {model = 'prop_cs_cardbox_01', pos = {0.0, -0.2, -0.1}, rot = {135.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'mini@repair', name = 'fixing_a_player', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modSuspension = {
        label = 'Suspension', modType = 15, category = 'performance', point = 'wheel', item = 'mod_suspension', icon = 'https://i.ibb.co/0K3vc0c/mod-suspension.png',
        variants = {
            ['-1'] = {label = 'Stock Suspension', index = -1, price = 1000},
            ['0'] = {label = 'Lowered Suspension', index = 0, price = 2000},
            ['1'] = {label = 'Street Suspension', index = 1, price = 3000},
            ['2'] = {label = 'Sports Suspension', index = 2, price = 4000},
            ['3'] = {label = 'Competition Suspension', index = 3, price = 5000},
            ['4'] = {label = 'Custom Suspension', index = 4, price = 7500}
        },
        prop = {model = 'prop_cs_cardbox_01', pos = {0.0, -0.2, -0.1}, rot = {135.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modArmor = {
        label = 'Armor', modType = 16, category = 'performance', point = 'vehicle', item = 'mod_armor', icon = 'https://i.ibb.co/4SKJv2H/mod-armor.png',
        variants = {
            ['-1'] = {label = 'Stock Armor', index = -1, price = 2500},
            ['0'] = {label = '20% Armor', index = 0, price = 5000},
            ['1'] = {label = '40% Armor', index = 1, price = 10000},
            ['2'] = {label = '60% Armor', index = 2, price = 15000},
            ['3'] = {label = '80% Armor', index = 3, price = 20000},
            ['4'] = {label = '100% Armor', index = 4, price = 25000}
        },
        prop = {model = 'prop_cs_cardbox_01', pos = {0.0, -0.2, -0.1}, rot = {135.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'mini@repair', name = 'fixing_a_player', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modTurbo = {
        label = 'Turbo', modType = 18, category = 'performance', point = 'engine', item = 'mod_turbo', icon = 'https://i.ibb.co/5WL0SwJ/mod-turbo.png',
        variants = {
            ['0'] = {label = 'Stock Turbo', index = 0, price = 1500},
            ['1'] = {label = 'Upgraded Turbo', index = 1, price = 7500}
        },
        prop = {model = 'prop_cs_cardbox_01', pos = {0.0, -0.2, -0.1}, rot = {135.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'mini@repair', name = 'fixing_a_player', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modXenon = {
        label = 'Xenon Kit', modType = 22, category = 'lights', point = 'engine', item = 'mod_light', icon = 'https://i.ibb.co/DtHTTT1/mod-light.png',
        prop = {model = 'prop_cs_package_01', pos = {0.0, -0.05, -0.1}, rot = {100.0, 0.0, 0.0}},
        variants = {
            ['0'] = {label = 'Stock',		index = 0,		price = 100},
            ['1'] = {label = 'Xenon',		index = 1,		price = 2000},
        },
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'mini@repair', name = 'fixing_a_player', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modFrontWheels = {
        label = 'Wheels', modType = 23, category = 'wheels', point = 'wheel', item = 'mod_rim', icon = 'https://i.ibb.co/j61LvC6/mod-rim.png',
        variants = {
            ['0'] = {label = 'Sport',				index = 0,					price = 1200},
            ['1'] = {label = 'Muscle',				index = 1,					price = 1000},
            ['2'] = {label = 'Lowrider',			index = 2,					price = 1100},
            ['3'] = {label = 'SUV',					index = 3,					price = 1400},
            ['4'] = {label = 'Offroad',				index = 4,					price = 900},
            ['5'] = {label = 'Tuner',				index = 5,					price = 1600},
            ['6'] = {label = 'Bike Front',			index = 6,					price = 700},
            ['7'] = {label = 'High End',			index = 7,					price = 1800},
            ['8'] = {label = 'Benny\'s Original',	index = 8,					price = 1700},
            ['9'] = {label = 'Benny\'s Bespoke',	index = 9,					price = 2000},
            ['10'] = {label = 'Open Wheel',			index = 10,					price = 2200},
            ['11'] = {label = 'Street',				index = 11,					price = 2500},
            ['12'] = {label = 'Track',				index = 12,					price = 3200}
        },
        prop = {model = 'imp_prop_impexp_tyre_01c', pos = {0.0, -0.1, 0.2}, rot = {0.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modBackWheels = { -- this is technically not in use; only for the label of modBackWheels. 
        label = 'Wheels Rear', modType = 24, category = '', point = 'wheel', icon = 'https://i.ibb.co/j61LvC6/mod-rim.png',
        prop = {model = 'imp_prop_impexp_tyre_01c', pos = {0.0, -0.1, 0.2}, rot = {0.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modPlateHolder = {
        label = 'Plate Holder', modType = 25, category = 'exterior', price = 4.95, point = 'vehicle', item = 'mod_plate', icon = 'https://i.ibb.co/BZbB2D5/mod-plate.png',
        prop = {model = 'prop_cs_cardbox_01', pos = {0.0, -0.2, -0.1}, rot = {135.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'mp_intro_seq@', name = 'mp_mech_fix', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modVanityPlate = { -- sometimes is: CANARDS, SPLITTER, 
        label = 'Vanity Plates', modType = 26, category = 'exterior', price = 700, point = 'vehicle', item = 'mod_plate', icon = 'https://i.ibb.co/BZbB2D5/mod-plate.png',
        prop = {model = 'prop_cs_cardbox_01', pos = {0.0, -0.2, -0.1}, rot = {135.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'mp_intro_seq@', name = 'mp_mech_fix', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modTrimA = {
        label = 'Trim A', modType = 27, price = 300, category = 'interior', point = 'vehicle', item = 'mod_interior', icon = 'https://i.ibb.co/C2M0xrV/mod-interior.png',
        prop = {model = 'prop_cs_cardbox_01', pos = {0.0, -0.2, -0.1}, rot = {135.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modOrnaments = {
        label = 'Ornaments', modType = 28, price = 250, category = 'interior', point = 'vehicle', item = 'mod_interior', icon = 'https://i.ibb.co/C2M0xrV/mod-interior.png',
        prop = {model = 'prop_cs_cardbox_01', pos = {0.0, -0.2, -0.1}, rot = {135.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modDashboard = {
        label = 'Dashboard', modType = 29, price = 400, category = 'interior', point = 'vehicle', item = 'mod_interior', icon = 'https://i.ibb.co/C2M0xrV/mod-interior.png',
        prop = {model = 'prop_cs_package_01', pos = {0.0, -0.05, -0.1}, rot = {100.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modDial = {
        label = 'Gauges', modType = 30, price = 350, category = 'interior', point = 'vehicle', item = 'mod_interior', icon = 'https://i.ibb.co/C2M0xrV/mod-interior.png',
        prop = {model = 'prop_cs_cardbox_01', pos = {0.0, -0.2, -0.1}, rot = {135.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modDoorSpeaker = {
        label = 'Door Speaker', modType = 31, price = 650, category = 'interior', point = 'vehicle', item = 'mod_interior', icon = 'https://i.ibb.co/C2M0xrV/mod-interior.png',
        prop = {model = 'prop_cs_cardbox_01', pos = {0.0, -0.2, -0.1}, rot = {135.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modSeats = {
        label = 'Seats', modType = 32, price = 1400, category = 'interior', point = 'vehicle', item = 'mod_interior', icon = 'https://i.ibb.co/C2M0xrV/mod-interior.png',
        prop = {model = 'prop_ejector_seat_01', pos = {0.0, -0.3, 0.07}, rot = {30.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modSteeringWheel = {
        label = 'Steering Wheel', modType = 33, price = 950, category = 'interior', point = 'vehicle', item = 'mod_interior', icon = 'https://i.ibb.co/C2M0xrV/mod-interior.png',
        prop = {model = 'prop_cs_cardbox_01', pos = {0.0, -0.2, -0.1}, rot = {135.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modShifterLeavers = {
        label = 'Shifter Leaver', modType = 34, price = 450, category = 'interior', point = 'vehicle', item = 'mod_interior', icon = 'https://i.ibb.co/C2M0xrV/mod-interior.png',
        prop = {model = 'prop_cs_cardbox_01', pos = {0.0, -0.2, -0.1}, rot = {135.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modAPlate = {
        label = 'Plaque', modType = 35, price = 800, category = 'interior', point = 'vehicle', item = 'mod_interior', icon = 'https://i.ibb.co/C2M0xrV/mod-interior.png',
        prop = {model = 'prop_cs_cardbox_01', pos = {0.0, -0.2, -0.1}, rot = {135.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modSpeakers = {
        label = 'Speaker', modType = 36, price = 1200, category = 'interior', point = 'vehicle', item = 'mod_interior', icon = 'https://i.ibb.co/C2M0xrV/mod-interior.png',
        prop = {model = 'sm_prop_smug_speaker', pos = {0.0, -0.1, -0.2}, rot = {-15.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modTrunk = {
        label = 'Trunk', modType = 37, price = 2300, category = 'interior', point = 'rear', item = 'mod_interior', icon = 'https://i.ibb.co/C2M0xrV/mod-interior.png',
        prop = {model = 'imp_prop_impexp_trunk_01a', pos = {0.0, 0.15, 0.1}, rot = {30.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'mini@repair', name = 'fixing_a_player', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modHydrolic = {
        label = 'Hydraulics', modType = 38, price = 5400, category = 'exterior', point = 'vehicle', item = 'mod_exterior', icon = 'https://i.ibb.co/vXStD4H/mod-exterior.png',
        prop = {model = 'prop_cs_cardbox_01', pos = {0.0, -0.2, -0.1}, rot = {135.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modEngineBlock = {
        label = 'Engine Block', modType = 39, price = 1600, category = 'enginebay', point = 'engine', item = 'mod_exterior', icon = 'https://i.ibb.co/vXStD4H/mod-exterior.png',
        prop = {model = 'prop_cs_cardbox_01', pos = {0.0, -0.2, -0.1}, rot = {135.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'mini@repair', name = 'fixing_a_player', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modAirFilter = {
        label = 'Air Filters', modType = 40, price = 1300, category = 'enginebay', point = 'engine', item = 'mod_exterior', icon = 'https://i.ibb.co/vXStD4H/mod-exterior.png',
        prop = {model = 'prop_cs_cardbox_01', pos = {0.0, -0.2, -0.1}, rot = {135.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'mini@repair', name = 'fixing_a_player', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modStruts = {
        label = 'Strut Brace', modType = 41, price = 1400, category = 'enginebay', point = 'engine', item = 'mod_exterior', icon = 'https://i.ibb.co/vXStD4H/mod-exterior.png',
        prop = {model = 'prop_cs_cardbox_01', pos = {0.0, -0.2, -0.1}, rot = {135.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'mini@repair', name = 'fixing_a_player', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modArchCover = {
        label = 'Arch Cover', modType = 42, price = 900, category = 'chassis', point = 'vehicle', item = 'mod_exterior', icon = 'https://i.ibb.co/vXStD4H/mod-exterior.png',
        prop = {model = 'prop_cs_cardbox_01', pos = {0.0, -0.2, -0.1}, rot = {135.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modAerials = {
        label = 'Aerials', modType = 43, price = 600, category = 'chassis', point = 'vehicle', item = 'mod_exterior', icon = 'https://i.ibb.co/vXStD4H/mod-exterior.png',
        prop = {model = 'prop_cs_cardbox_01', pos = {0.0, -0.2, -0.1}, rot = {135.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modTrimB = {
        label = 'Trim B', modType = 44, price = 1100, category = 'chassis', point = 'vehicle', item = 'mod_exterior', icon = 'https://i.ibb.co/vXStD4H/mod-exterior.png',
        prop = {model = 'prop_cs_cardbox_01', pos = {0.0, -0.2, -0.1}, rot = {135.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modTank = {
        label = 'Fuel Tank', modType = 45, price = 1900, category = 'chassis', point = 'vehicle', item = 'mod_exterior', icon = 'https://i.ibb.co/vXStD4H/mod-exterior.png',
        prop = {model = 'prop_cs_cardbox_01', pos = {0.0, -0.2, -0.1}, rot = {135.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modWindows = {
        label = 'Left Door', modType = 46, price = 1700, category = 'exterior', point = 'vehicle', item = 'mod_exterior', icon = 'https://i.ibb.co/vXStD4H/mod-exterior.png',
        prop = {model = 'imp_prop_impexp_car_door_04a', pos = {-0.7, -0.2, 0.0}, rot = {0.0, -10.0, 90.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modDoorR = {
        label = 'Right Door', modType = 47, price = 1700, category = 'exterior', point = 'vehicle', item = 'mod_exterior', icon = 'https://i.ibb.co/vXStD4H/mod-exterior.png',
        prop = {model = 'imp_prop_impexp_car_door_04a', pos = {-0.7, -0.2, 0.0}, rot = {0.0, -10.0, 90.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modLivery = {
        label = 'Livery', modType = 48, price = 1500, category = 'respray', point = 'vehicle', item = 'mod_livery', icon = 'https://i.ibb.co/2NxkxPT/mod-livery.png',
        prop = {model = 'v_ind_cfwrap', pos = {0.0, -0.10, -0.15}, rot = {0.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@business@weed@weed_inspecting_lo_med_hi@', name = 'weed_spraybottle_crouch_spraying_03_inspector', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modRoofLivery = {
        label = 'Roof Livery', modType = 'modRoofLivery', price = 250, category = 'respray', point = 'vehicle', item = 'mod_livery', icon = 'https://i.ibb.co/2NxkxPT/mod-livery.png',
        prop = {model = 'v_ind_cfwrap', pos = {0.0, -0.10, -0.15}, rot = {0.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@business@weed@weed_inspecting_lo_med_hi@', name = 'weed_spraybottle_crouch_spraying_03_inspector', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    modLightbar = {
        label = 'Lightbar', modType = 49, price = 900, category = 'exterior', point = 'vehicle', item = 'mod_exterior', icon = 'https://i.ibb.co/vXStD4H/mod-exterior.png',
        prop = {model = 'prop_cs_cardbox_01', pos = {0.0, -0.2, -0.1}, rot = {135.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'mini@repair', name = 'fixing_a_player', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    neonEnabled = {
        label = 'Neon Kits', modType = 'neonEnabled', price = 1000, category = 'lights', point = 'engine', item = 'mod_neon', icon = 'https://i.ibb.co/yXj9WHf/mod-neon.png',
        variants = {
            ['0'] = {label = 'Left Light Kit',      index = 0},
            ['1'] = {label = 'Right Light Kit',	    index = 1},
            ['2'] = {label = 'Front Light Kit',	    index = 2},
            ['3'] = {label = 'Rear Light Kit',	    index = 3},
        },
        prop = {model = 'prop_cs_package_01', pos = {0.0, -0.05, -0.1}, rot = {100.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'mini@repair', name = 'fixing_a_player', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    xenonColor = {
        label = 'Xenon Colors', modType = 'xenonColor', price = 250, category = 'lights', point = 'engine', item = 'mod_light', icon = 'https://i.ibb.co/DtHTTT1/mod-light.png',
        variants = {
            ['-1'] = {label = 'Default',        index = -1},
            ['0'] = {label = 'White',           index = 0},
            ['1'] = {label = 'Blue',            index = 1},
            ['2'] = {label = 'Electric Blue',   index = 2},
            ['3'] = {label = 'Mint Green', 		index = 3},
            ['4'] = {label = 'Lime Green', 		index = 4},
            ['5'] = {label = 'Yellow', 			index = 5},
            ['6'] = {label = 'Golden Shower', 	index = 6},
            ['7'] = {label = 'Orange', 			index = 7},
            ['8'] = {label = 'Red', 			index = 8},
            ['9'] = {label = 'Pony Pink', 		index = 9},
            ['10'] = {label = 'Hot Pink', 		index = 10},
            ['11'] = {label = 'Purple', 		index = 11},
            ['12'] = {label = 'Blacklight', 	index = 12},
        },
        prop = {model = 'prop_cs_package_01', pos = {0.0, -0.05, -0.1}, rot = {100.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'mini@repair', name = 'fixing_a_player', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    customXenon = {
        label = 'Xenon Custom', modType = 'customXenon', price = 600, category = '', point = 'engine', icon = 'https://i.ibb.co/DtHTTT1/mod-light.png',
        prop = {model = 'prop_cs_package_01', pos = {0.0, -0.05, -0.1}, rot = {100.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'mini@repair', name = 'fixing_a_player', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    neonColor = {
        label = 'Neon Colors', modType = 'neonColor', price = 200, category = 'lights', point = 'engine', item = 'mod_neon', icon = 'https://i.ibb.co/yXj9WHf/mod-neon.png',
        variants = {
            ['-1'] = {label = 'Stock', 			    rgb = {0, 0, 0}},
            ['0'] = {label = 'White', 				rgb = {222, 222, 255}},
            ['1'] = {label = 'Blue', 				rgb = {2, 21, 255}},
            ['2'] = {label = 'Electric Blue', 		rgb = {3, 83, 255}},
            ['3'] = {label = 'Mint Green', 			rgb = {0, 255, 140}},
            ['4'] = {label = 'Lime Green', 			rgb = {94, 222, 1}},
            ['5'] = {label = 'Yellow', 				rgb = {255, 255, 0}},
            ['6'] = {label = 'Golden Shower',		rgb = {255, 150, 0}},
            ['7'] = {label = 'Orange', 				rgb = {255, 62, 0}},
            ['8'] = {label = 'Red', 				rgb = {255, 1, 1}},
            ['9'] = {label = 'Pony Pink', 			rgb = {255, 50, 100}},
            ['10'] = {label = 'Hot Pink', 			rgb = {255, 5, 190}},
            ['11'] = {label = 'Purple', 			rgb = {35, 1, 255}},
            ['12'] = {label = 'Blacklight', 		rgb = {15, 3, 255}}
        },
        prop = {model = 'prop_cs_package_01', pos = {0.0, -0.05, -0.1}, rot = {100.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'mini@repair', name = 'fixing_a_player', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    tyreSmokeColor = {
        label = 'Tyre Smoke Color', modType = 20, price = 150, category = 'wheels', point = 'wheel', item = 'mod_tyresmoke', icon = 'https://i.ibb.co/Cw8FpBr/mod-tyresmoke.png',
        variants = {
            ['0'] = {label = 'White',  rgb = {254, 254, 254}},
            ['1'] = {label = 'Black',  rgb = {1, 1, 1}},
            ['2'] = {label = 'Blue',   rgb = {0, 150, 255}},
            ['3'] = {label = 'Yellow', rgb = {255, 255, 50}},
            ['4'] = {label = 'Orange', rgb = {255, 153, 51}},
            ['5'] = {label = 'Red',    rgb = {255, 10, 10}},
            ['6'] = {label = 'Green',  rgb = {10, 255, 10}},
            ['7'] = {label = 'Purple', rgb = {153, 10, 153}},
            ['8'] = {label = 'Pink',   rgb = {255, 102, 178}},
            ['9'] = {label = 'Gray',   rgb = {128, 128, 128}}
        },
        prop = {model = 'imp_prop_impexp_tyre_01a', pos = {0.0, -0.10, 0.15}, rot = {0.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    extras = {
        label = 'Extras', modType = 'extras', category = 'exterior', point = 'vehicle', item = 'mod_extras', icon = 'https://i.ibb.co/S0VVVgZ/mod-extras.png',
        variants = {
            ['0'] = {label = 'Stock',		index = 1,		price = 200},
            ['1'] = {label = 'Extra',		index = 0,		price = 1000},
        },
        prop = {model = 'prop_cs_package_01', pos = {0.0, -0.05, -0.1}, rot = {100.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'mini@repair', name = 'fixing_a_player', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    plateIndex = {
        label = 'License Plates', modType = 'plateIndex', category = 'exterior', point = 'rear', item = 'mod_plate', icon = 'https://i.ibb.co/BZbB2D5/mod-plate.png',
        variants = {
            ['0'] = {label = 'Blue on White 1', 	    index = 0,					price = 300},
            ['1'] = {label = 'Yellow on Black', 	    index = 1,					price = 400},
            ['2'] = {label = 'Yellow on Blue', 		    index = 2,					price = 500},
            ['3'] = {label = 'Blue on White 2', 	    index = 3,					price = 600},
            ['4'] = {label = 'Blue on White 3', 	    index = 4,					price = 700},
            ['5'] = {label = 'Yankton', 			    index = 5,					price = 800},
            -- These requires gamebuild 3095:
            ['6'] = {label = 'eCola', 	                index = 6,					price = 900},
            ['7'] = {label = 'Las Venturas', 		    index = 7,					price = 1000},
            ['8'] = {label = 'Liberty City', 	        index = 8,					price = 1100},
            ['9'] = {label = 'Los Santos Car Meet',     index = 9,					price = 1200},
            ['10'] = {label = 'Los Santos Panic',       index = 10,					price = 1300},
            ['11'] = {label = 'Los Santos Pounders',    index = 11,					price = 1400},
            ['12'] = {label = 'Sprunk', 	            index = 12,					price = 1500},
        },
        prop = {model = 'p_num_plate_01', pos = {0.01, -0.1, -0.18}, rot = {-60.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    windowTint = {
        label = 'Window Tints', modType = 'windowTint', category = 'exterior', point = 'vehicle', item = 'mod_windowtint', icon = 'https://i.ibb.co/G0QPR6F/mod-windowtint.png',
        variants = {
            ['0'] = {label = 'None',				index = 0,					price = 300},
            ['1'] = {label = 'Pure black',			index = 1,					price = 2500},
            ['2'] = {label = 'Dark Smoke',			index = 2,					price = 1800},
            ['3'] = {label = 'Light Smoke',			index = 3,					price = 1200},
            ['4'] = {label = 'Stock',				index = 4,					price = 300},
            ['5'] = {label = 'Green',				index = 5,					price = 2200}
        },
        prop = {model = 'h4_prop_yacht_glass_04', pos = {0.0, -0.1, 0.3}, rot = {0.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'mini@repair', name = 'fixing_a_player', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    color1 = {
        label = 'Primary Color', modType = 'color1', price = 300, category = 'respray', point = 'vehicle', item = 'mod_respray', icon = 'https://i.ibb.co/dQYTKWk/mod-respray.png',
        prop = {model = 'ng_proc_spraycan01b', pos = {0.072, 0.041, -0.06}, rot = {33.0, 38.0, 0.0}},
        anim = {
            idle = {dict = nil, name = nil, blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@business@weed@weed_inspecting_lo_med_hi@', name = 'weed_spraybottle_crouch_spraying_03_inspector', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    color2 = {
        label = 'Secondary Color', modType = 'color2', price = 200, category = 'respray', point = 'vehicle', item = 'mod_respray', icon = 'https://i.ibb.co/dQYTKWk/mod-respray.png',
        prop = {model = 'ng_proc_spraycan01b', pos = {0.072, 0.041, -0.06}, rot = {33.0, 38.0, 0.0}},
        anim = {
            idle = {dict = nil, name = nil, blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@business@weed@weed_inspecting_lo_med_hi@', name = 'weed_spraybottle_crouch_spraying_03_inspector', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    pearlescentColor = {
        label = 'Pearlescent Color', modType = 'pearlescentColor', price = 100, category = 'respray', point = 'vehicle', item = 'mod_respray', icon = 'https://i.ibb.co/dQYTKWk/mod-respray.png',
        prop = {model = 'ng_proc_spraycan01b', pos = {0.072, 0.041, -0.06}, rot = {33.0, 38.0, 0.0}},
        anim = {
            idle = {dict = nil, name = nil, blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@business@weed@weed_inspecting_lo_med_hi@', name = 'weed_spraybottle_crouch_spraying_03_inspector', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    wheelColor = {
        label = 'Wheel Color', modType = 'wheelColor', price = 200, category = 'wheels', point = 'wheel', item = 'mod_respray', icon = 'https://i.ibb.co/dQYTKWk/mod-respray.png',
        prop = {model = 'ng_proc_spraycan01b', pos = {0.072, 0.041, -0.06}, rot = {33.0, 38.0, 0.0}},
        anim = {
            idle = {dict = nil, name = nil, blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@business@weed@weed_inspecting_lo_med_hi@', name = 'weed_spraybottle_crouch_spraying_03_inspector', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    dashboardColor = {
        label = 'Dashboard Color', modType = 'dashboardColor', price = 100, category = 'respray', point = 'vehicle', item = 'mod_respray', icon = 'https://i.ibb.co/dQYTKWk/mod-respray.png',
        prop = {model = 'ng_proc_spraycan01b', pos = {0.072, 0.041, -0.06}, rot = {33.0, 38.0, 0.0}},
        anim = {
            idle = {dict = nil, name = nil, blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@business@weed@weed_inspecting_lo_med_hi@', name = 'weed_spraybottle_crouch_spraying_03_inspector', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    interiorColor = {
        label = 'Interior Color', modType = 'interiorColor', price = 100, category = 'respray', point = 'vehicle', item = 'mod_respray', icon = 'https://i.ibb.co/dQYTKWk/mod-respray.png',
        prop = {model = 'ng_proc_spraycan01b', pos = {0.072, 0.041, -0.06}, rot = {33.0, 38.0, 0.0}},
        anim = {
            idle = {dict = nil, name = nil, blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@business@weed@weed_inspecting_lo_med_hi@', name = 'weed_spraybottle_crouch_spraying_03_inspector', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    driftTyres = {
        label = 'Drift Tyres', modType = 'driftTyres', price = 1000, category = '', point = 'wheel', item = 'mod_drifttires', icon = 'https://i.ibb.co/pRx0whN/mod-drifttires.png',
        prop = {model = 'imp_prop_impexp_tyre_01a', pos = {0.0, -0.10, 0.15}, rot = {0.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    },
    bulletProofTyres = {
        label = 'Bulletproof Tyres', modType = 'bulletProofTyres', price = 3500, category = '', point = 'wheel', item = 'mod_bullettires', icon = 'https://i.ibb.co/vQXJgJd/mod-bullettires.png',
        prop = {model = 'imp_prop_impexp_tyre_01a', pos = {0.0, -0.10, 0.15}, rot = {0.0, 0.0, 0.0}},
        anim = {
            idle = {dict = 'anim@heists@box_carry@', name = 'idle', blendIn = 4.0, blendOut = 1.0, duration = -1, flags = 49},
            install = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        }
    }
}

Config.ColorCategories = { -- color categories used with Config.Colors 
	{name = 'metallic', label = 'Metallic', paintType = 1, price = 250 },
	{name = 'matte', label = 'Matte', paintType = 3, price = 350},
	{name = 'metals', label = 'Metals', paintType = 4, price = 500},
	{name = 'util', label = 'Util', paintType = 1, price = 700},
	{name = 'worn', label = 'Worn', paintType = 1, price = 750},
	{name = 'chameleon', label = 'Chameleon', paintType = 6, price = 1000}
}

Config.Colors = {
	['metallic'] = {
		{label = 'Black', index = 0},
		{label = 'Graphite Black', index = 1},
		{label = 'Black Steel', index = 2},
		{label = 'Dark Silver', index = 3},
		{label = 'Silver', index = 4},
		{label = 'Blue Silver', index = 5},
		{label = 'Steel Grey', index = 6},
		{label = 'Shadow Silver', index = 7},
		{label = 'Stone Silver', index = 8},
		{label = 'Midnight Silver', index = 9},
		{label = 'Gun Metal', index = 10},
		{label = 'Anthracite Grey', index = 11},
		{label = 'Red', index = 27},
		{label = 'Torino Red', index = 28},
		{label = 'Formula Red', index = 29},
		{label = 'Blaze Red', index = 30},
		{label = 'Graceful Red', index = 31},
		{label = 'Garnet Red', index = 32},
		{label = 'Desert Red', index = 33},
		{label = 'Cabernet Red', index = 34},
		{label = 'Candy Red', index = 35},
		{label = 'Sunrise Orange', index = 36},
		{label = 'Classic Gold', index = 37},
		{label = 'Orange', index = 38},
		{label = 'Dark Green', index = 49},
		{label = 'Racing Green', index = 50},
		{label = 'Sea Green', index = 51},
		{label = 'Olive Green', index = 52},
		{label = 'Green', index = 53},
		{label = 'Gasoline Blue Green', index = 54},
		{label = 'Midnight Blue', index = 61},
		{label = 'Dark Blue', index = 62},
		{label = 'Saxony Blue', index = 63},
		{label = 'Blue', index = 64},
		{label = 'Mariner Blue', index = 65},
		{label = 'Harbor Blue', index = 66},
		{label = 'Diamond Blue', index = 67},
		{label = 'Surf Blue', index = 68},
		{label = 'Nautical Blue', index = 69},
		{label = 'Bright Blue', index = 70},
		{label = 'Purple Blue', index = 71},
		{label = 'Spinnaker Blue', index = 72},
		{label = 'Ultra Blue', index = 73},
		{label = 'Light Blue', index = 74},
		{label = 'Taxi Yellow', index = 88},
		{label = 'Race Yellow', index = 89},
		{label = 'Bronze', index = 90},
		{label = 'Yellow Bird', index = 91},
		{label = 'Lime', index = 92},
		{label = 'Champagne', index = 93},
		{label = 'Pueblo Beige', index = 94},
		{label = 'Dark Ivory', index = 95},
		{label = 'Choco Brown', index = 96},
		{label = 'Golden Brown', index = 97},
		{label = 'Light Brown', index = 98},
		{label = 'Straw Beige', index = 99},
		{label = 'Moss Brown', index = 100},
		{label = 'Biston Brown', index = 101},
		{label = 'Beechwood', index = 102},
		{label = 'Dark Beechwood', index = 103},
		{label = 'Choco Orange', index = 104},
		{label = 'Beach Sand', index = 105},
		{label = 'Sun Bleeched Sand', index = 106},
		{label = 'Cream', index = 107},
		{label = 'White', index = 111},
		{label = 'Frost White', index = 112},
		{label = 'Securicor Green', index = 125},
		{label = 'Pure White', index = 134},
		{label = 'Hot Pink', index = 135},
		{label = 'Salmon pink', index = 136},
		{label = 'Vermillion Pink', index = 137},
		{label = 'Bright Orange', index = 138},
		{label = 'Bright Green', index = 139},
		{label = 'Bright Blue', index = 140},
		{label = 'Black Blue', index = 141},
		{label = 'Black Purple', index = 142},
		{label = 'Black Red', index = 143},
		{label = 'Purple', index = 145},
		{label = 'V Dark Blue', index = 146},
		{label = 'Carbon Black', index = 147},
		{label = 'Lava Red', index = 150}
	},
	['matte'] = {
		{label = 'Black', index = 12},
		{label = 'Gray', index = 13},
		{label = 'Light Grey', index = 14},
		{label = 'Red', index = 39},
		{label = 'Dark Red', index = 40},
		{label = 'Orange', index = 41},
		{label = 'Yellow', index = 42},
		{label = 'Lime Green', index = 55},
		{label = 'Dark Blue', index = 82},
		{label = 'Blue', index = 83},
		{label = 'Midnight Blue', index = 84},
		{label = 'Green', index = 128},
		{label = 'Brown', index = 129},
		{label = 'White', index = 131},
		{label = 'Purple', index = 148},
		{label = 'Dark Purple', index = 149},
		{label = 'Forest Green', index = 151},
		{label = 'Olive Drab', index = 152},
		{label = 'Desert Brown', index = 153},
		{label = 'Desert Tan', index = 154},
		{label = 'Foliage Green', index = 155}
	},
	['metals'] = {
		{label = 'Brushed Steel', index = 117},
		{label = 'Brushed Black Steel', index = 118},
		{label = 'Brushed Aluminum', index = 119},
		{label = 'Chrome', index = 120},
		{label = 'Alloy', index = 156},
		{label = 'Epsilon Blue', index = 157},
		{label = 'Pure Gold', index = 158},
		{label = 'Brushed Gold', index = 159}
	},
	['util'] = {
		{label = 'Black', index = 15},
		{label = 'Black Poly', index = 16},
		{label = 'Dark silver', index = 17},
		{label = 'Silver', index = 18},
		{label = 'Gun Metal', index = 19},
		{label = 'Shadow Silver', index = 20},
		{label = 'Red', index = 43},
		{label = 'Bright Red', index = 44},
		{label = 'Garnet Red', index = 45},
		{label = 'Dark Green', index = 56},
		{label = 'Green', index = 57},
		{label = 'Dark Blue', index = 75},
		{label = 'Midnight Blue', index = 76},
		{label = 'Blue', index = 77},
		{label = 'Sea Foam Blue', index = 78},
		{label = 'Lightning blue', index = 79},
		{label = 'Maui Blue Poly', index = 80},
		{label = 'Bright Blue', index = 81},
		{label = 'Brown', index = 108},
		{label = 'Medium Brown', index = 109},
		{label = 'Light Brown', index = 110},
		{label = 'Off White', index = 122}
	},
	['worn'] = {
		{label = 'Black', index = 21},
		{label = 'Graphite', index = 22},
		{label = 'Silver Grey', index = 23},
		{label = 'Silver', index = 24},
		{label = 'Blue Silver', index = 25},
		{label = 'Shadow Silver', index = 26},
		{label = 'Red', index = 46},
		{label = 'Golden Red', index = 47},
		{label = 'Dark Red', index = 48},
		{label = 'Dark Green', index = 58},
		{label = 'Green', index = 59},
		{label = 'Sea Wash', index = 60},
		{label = 'Dark Blue', index = 85},
		{label = 'Blue', index = 86},
		{label = 'Light Blue', index = 87},
		{label = 'Honey Beige', index = 113},
		{label = 'Brown', index = 114},
		{label = 'Dark Brown', index = 115},
		{label = 'Straw Beige', index = 116},
		{label = 'Off White', index = 121},
		{label = 'Orange', index = 123},
		{label = 'Light Orange', index = 124},
		{label = 'Taxi Yellow', index = 126},
		{label = 'Orange 2', index = 130},
		{label = 'White', index = 132},
		{label = 'Olive Army Green', index = 133}
	},
	['chameleon'] = {
        {label = 'Anodized Red Pearl', index = 161},
        {label = 'Anodized Wine Pearl', index = 162},
        {label = 'Anodized Purple Pearl', index = 163},
        {label = 'Anodized Blue Pearl', index = 164},
        {label = 'Anodized Green Pearl', index = 165},
        {label = 'Anodized Lime Pearl', index = 166},
        {label = 'Anodized Copper Pearl', index = 167},
        {label = 'Anodized Bronze Pearl', index = 168},
        {label = 'Anodized Champagne Pearl', index = 169},
        {label = 'Anodized Gold Pearl', index = 170},
        {label = 'Green/Blue Flip', index = 171},
        {label = 'Green/Red Flip', index = 172},
        {label = 'Green/Brown Flip', index = 173},
        {label = 'Green/Turquoise Flip', index = 174},
        {label = 'Green/Purple Flip', index = 175},
        {label = 'Teal/Purple Flip', index = 176},
        {label = 'Turquoise/Red Flip', index = 177},
        {label = 'Turquoise/Purple Flip', index = 178},
        {label = 'Cyan/Purple Flip', index = 179},
        {label = 'Blue/Pink Flip', index = 180},
        {label = 'Blue/Green Flip', index = 181},
        {label = 'Purple/Red Flip', index = 182},
        {label = 'Purple/Green Flip', index = 183},
        {label = 'Magenta/Green Flip', index = 184},
        {label = 'Magenta/Yellow Flip', index = 185},
        {label = 'Burgundy/Green Flip', index = 186},
        {label = 'Magenta/Cyan Flip', index = 187},
        {label = 'Copper/Purple Flip', index = 188},
        {label = 'Magenta/Orange Flip', index = 189},
        {label = 'Red/Orange Flip', index = 190},
        {label = 'Orange/Purple Flip', index = 191},
        {label = 'Orange/Blue Flip', index = 192},
        {label = 'White/Purple Flip', index = 193},
        {label = 'Red/Rainbow Flip', index = 194},
        {label = 'Blue/Rainbow Flip', index = 195},
        {label = 'Dark Green Pearl', index = 196},
        {label = 'Dark Teal Pearl', index = 197},
        {label = 'Dark Blue Pearl', index = 198},
        {label = 'Dark Purple Pearl', index = 199},
        {label = 'Oil Slick Pearl', index = 200},
        {label = 'Light Green Pearl', index = 201},
        {label = 'Light Blue Pearl', index = 202},
        {label = 'Light Purple Pearl', index = 203},
        {label = 'Light Pink Pearl', index = 204},
        {label = 'Off White Pearl', index = 205},
        {label = 'Cute Pink Pearl', index = 206},
        {label = 'Baby Yellow Pearl', index = 207},
        {label = 'Baby Green Pearl', index = 208},
        {label = 'Baby Blue Pearl', index = 209},
        {label = 'Cream Pearl', index = 210},
        {label = 'White Prismatic Pearl', index = 211},
        {label = 'Graphite Prismatic Pearl', index = 212},
        {label = 'Blue Prismatic Pearl', index = 213},
        {label = 'Purple Prismatic Pearl', index = 214},
        {label = 'Hot Pink Prismatic Pearl', index = 215},
        {label = 'Red Prismatic Pearl', index = 216},
        {label = 'Green Prismatic Pearl', index = 217},
        {label = 'Black Prismatic Pearl', index = 218},
        {label = 'Oil Spill Prismatic Pearl', index = 219},
        {label = 'Rainbow Prismatic Pearl', index = 220},
        {label = 'Black Holographic Pearl', index = 221},
        {label = 'White Holographic Pearl', index = 222},
        {label = 'Fubuki-jo Specials: Monochrome', index = 223},
        {label = 'Fubuki-jo Specials: Night & Day', index = 224},
        {label = 'Fubuki-jo Specials: The Verlierer', index = 225},
        {label = 'Fubuki-jo Specials: Sprunk Extreme', index = 226},
        {label = 'Fubuki-jo Specials: Vice City', index = 227},
        {label = 'Fubuki-jo Specials: Synthwave Night', index = 228},
        {label = 'Fubuki-jo Specials: Four Seasons', index = 229},
        {label = 'Fubuki-jo Specials: M9 Throwback', index = 230},
        {label = 'Fubuki-jo Specials: Bubblegum', index = 231},
        {label = 'Fubuki-jo Specials: Full Rainbow', index = 232},
        {label = 'Fubuki-jo Specials: Sunset', index = 233},
        {label = 'Fubuki-jo Specials: The Seven', index = 234},
        {label = 'Fubuki-jo Specials: Kamen Rider', index = 235},
        {label = 'Fubuki-jo Specials: Chromatic', index = 236},
        {label = 'Fubuki-jo Specials: It\'s Christmas!', index = 237},
        {label = 'Fubuki-jo Specials: Temperature', index = 238},
        {label = 'Fubuki-jo Specials: HSW Badge', index = 239},
        {label = 'Fubuki-jo Specials: Anod. Lightning', index = 240},
        {label = 'Fubuki-jo Specials: Emeralds', index = 241},
        {label = 'Fubuki-jo Specials: Fubuki Castle', index = 242} 
	}
}

Config.PaintTypes = {
    {value = 0, label = 'Normal'},
    {value = 1, label = 'Metallic'},
    {value = 2, label = 'Pearl'},
    {value = 3, label = 'Matte'},
    {value = 4, label = 'Metal'},
    {value = 5, label = 'Chrome'}
}