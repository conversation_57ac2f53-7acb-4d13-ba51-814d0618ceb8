Translations = {
    error = {
        join_in_first = "You need to join the battle boyale first",
        no_lives_entered = "Player lives not entered.",
        max_lives_set = "Maximum lives that can be set is ",
        no_wager = "Wager Amount not entered.",
        wager_cap = "Wager must be between ",
        too_far = "You were too far away from the arena",
        active_game = "A game is already active",
        closing_game = "A game is closing down and can take up to two minutes to be available again",
        in_game = "You need to be in a battle royale to use this",
        started_spectate = "A game needs to be ongoing to spectate",
        need_players = "There are no players in game",
        countdown = "The Royale will start in 10 seconds",
        flag_too_far = "Too far",
    },
    success = {
        savedfrinfall = "Don't worry you've been saved king! :)",
        weapon_chosen = "Youve chosen ",
        super_jump = "Super jump active!",
    },
    menu = {
        arenalobby = "Battle Royale Lobby",
        start = "Start",
        players = "Players",
        join_royale = "Join Royale",
        leave_royale = "Leave Royale",
        lives = 'Amount of lives',
        wager = 'Wager Amount',
        weapon = 'Choose Weapon',
        map = 'Choose Map',
        spectate = 'Spectate Players',
        random = 'Random',
        pickup = '[E] ',
        zonetime = 'Zone Cooldown Time',
        vrmenu = 'Choose Vr Game',
        PaintBall = 'paint Ball',
        Royale = 'Battle Royale',
        length = 'A normal game length',
        Royaletxt = 'Last player standing wins',
        PaintBalltxt = 'Where tf is the paintball gun?',
        change_time = ' | Changes the cooldown time inbetween zones',
        fast = 'No rush, just a little faster',
        medium_fast = 'Better for a medium amount of players',
        quicker = 'Quicker paced game than usual',
        fast_pace = 'Fast paced game',
        small_amount = 'Best for small amounts of people',
    },
    other = {
        jump = '[E] TO JUMP',
        armoring = "Armoring Up",
        capturing = "Capturing Flag",
        canceled = "Cancelled..",
        reload = "RELOAD",
    },
}
