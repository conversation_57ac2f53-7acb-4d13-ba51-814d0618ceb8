local dyno = {}

local GetVehicleDynoProperties = function(vehicleEntity)
    if not vehicleEntity or not DoesEntityExist(vehicleEntity) then
        return
    end
    local ent = Entity(vehicleEntity).state
    local props = ent['tuningsystem:vehicleDynoProperties']
    return props
end
exports('GetVehicleDynoProperties', GetVehicleDynoProperties)

local SetVehicleDynoProperties = function(vehicle, props)
    if vehicle == nil or not DoesEntityExist(vehicle) then
        return
    end
    local ent = Entity(vehicle).state
    ent:set('tuningsystem:vehicleDynoProperties', props, true)
end
exports('SetVehicleDynoProperties', SetVehicleDynoProperties)

local GetVehicleCurrentModifiers = function(vehicle)
    local dynoProperties = GetVehicleDynoProperties(vehicle)
    if dynoProperties ~= nil then return dynoProperties end

    return {
        torque = lib.math.round(GetVehicleCheatPowerIncrease(vehicle), 2),
        power = GetVehicleTopSpeedModifier(vehicle) == -1 and 1.0 or lib.math.round(GetVehicleTopSpeedModifier(vehicle), 2),
        brakes = lib.math.round(GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fBrakeForce'), 2)
    }
end

local GetDefaultVehicleModifiers = function(vehicle)
    -- load model:
    local model = GetEntityModel(vehicle)
    Lib.LoadModel(model)

    -- create new test vehicle:
    local newVehicle = CreateVehicle(model, coords.x, coords.y, (coords.z - 5.0), GetEntityHeading(vehicle), false, true)
    FreezeEntityPosition(newVehicle, true)
    SetEntityInvincible(newVehicle, true)
    SetEntityCollision(newVehicle, false, false)
    SetEntityVisible(newVehicle, false)

    -- get data:
    local stats = GetVehicleCurrentModifiers(newVehicle)

    -- delete test vehicle:
    DeleteVehicle(newVehicle)
    if DoesEntityExist(newVehicle) then 
        DeleteEntity(newVehicle)
    end

    -- return data:
    return stats
end

-- DYNO TUNING MAIN MENU:
RegisterNetEvent('tuningsystem:client:dynoTuningMainMenu', function(data)
    local menuOptions = {}
    local vehicle = data.vehicle

    local ViewCurrentModifiersMenu = function(vehicle)
        -- get dyno stats:
        local stats = GetVehicleCurrentModifiers(vehicle)
        -- register context menu:
        lib.registerContext({
            id = 'dyno_view_current_modifiers',
            title = Lang['title_dyno_view_modifiers'],
            menu = 'dyno_tuning_main_menu',
            options = {
                {
                    title = Config.Dyno.Torque.label..': '..stats.torque,
                    icon = Config.Dyno.Torque.icon,
                    description = Config.Dyno.Torque.description,
                    readOnly = true
                },
                {
                    title = Config.Dyno.Power.label..': '..stats.power,
                    icon = Config.Dyno.Power.icon,
                    description = Config.Dyno.Power.description,
                    readOnly = true
                },
                {
                    title = Config.Dyno.Brakes.label..': '..stats.brakes,
                    icon = Config.Dyno.Brakes.icon,
                    description = Config.Dyno.Brakes.description,
                    readOnly = true
                },
            }
        })
    end
    
    ViewCurrentModifiersMenu(vehicle)
    menuOptions[#menuOptions + 1] = {
        title = Lang['title_dyno_view_modifiers'],
        icon = Config.Dyno.ViewModifiers.icon,
        description = Config.Dyno.ViewModifiers.description,
        menu = 'dyno_view_current_modifiers',
        arrow = true,
    }
    
    menuOptions[#menuOptions + 1] = {
        title = Lang['title_dyno_fine_tuning'],
        icon = Config.Dyno.FineTuning.icon,
        description = Config.Dyno.FineTuning.description,
        onSelect = function()
            FineTuningSlider(vehicle)
        end
    }

    lib.registerContext({
        id = 'dyno_tuning_main_menu',
        title = Lang['title_tablet_dyno_tuning'],
        menu = 'tuner_tablet_main',
        options = menuOptions
    })
    lib.showContext('dyno_tuning_main_menu')
end)

FineTuningSlider = function(vehicle)
    local stats = GetVehicleCurrentModifiers(vehicle)
    local cfg = Config.Dyno

    local input = lib.inputDialog(Lang['title_dyno_fine_tuning'], {
        {type = 'slider', label = cfg.Torque.label..':', icon = cfg.Torque.icon, default = stats.torque, min = cfg.Torque.slider.min, max = cfg.Torque.slider.max, step = cfg.Torque.slider.step},
        {type = 'slider', label = cfg.Power.label..':', icon = cfg.Power.icon, default = stats.power, min = cfg.Power.slider.min, max = cfg.Power.slider.max, step = cfg.Power.slider.step},
        {type = 'slider', label = cfg.Brakes.label..':', icon = cfg.Brakes.icon, default = stats.brakes, min = cfg.Brakes.slider.min, max = cfg.Brakes.slider.max, step = cfg.Brakes.slider.step},
        {type = 'checkbox', label = cfg.FineTuning.reset.label, checked = false},
    })

    if not input then
        return lib.showContext('dyno_tuning_main_menu')
    end

    local resetHandling = input[4]

    local success = false

    if cfg.FineTuning.progressBar.enable then 
        success = ProgressBar({
            duration = cfg.FineTuning.progressBar.duration,
            label = cfg.FineTuning.progressBar.label,
            useWhileDead = false,
            canCancel = true,
            disable = {
                car = true,
            }
        })
    else
        success = true
    end

    if success then
        if resetHandling == true then 
            -- get default modifiers:
            local stats = GetDefaultVehicleModifiers(vehicle)
            -- apply default modifiers:
            SetVehicleCheatPowerIncrease(vehicle, stats.torque)
            ModifyVehicleTopSpeed(vehicle, stats.power)
            SetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fBrakeForce', stats.brakes)
            
            SetVehicleDynoProperties(vehicle, stats)
        else
            local props = {
                torque = lib.math.round((input[1] + 0.0), 2),
                power = lib.math.round((input[2] + 0.0), 2),
                brakes = lib.math.round((input[3] + 0.0), 2)
            }

            SetVehicleCheatPowerIncrease(vehicle, props.torque)
            ModifyVehicleTopSpeed(vehicle, props.power)
            SetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fBrakeForce', props.brakes)

            SetVehicleDynoProperties(vehicle, props)
        end
    else
        print("false")
    end

    -- back to main menu?
    Wait(10)
    TriggerEvent('tuningsystem:client:dynoTuningMainMenu', {vehicle = vehicle})
end

local function IsDrivingDynoVehicle()
    if not cache.vehicle then return end
	local vehicle = cache.vehicle

    Wait(1500)

    local dynoProperties = GetVehicleDynoProperties(vehicle)

    local tick = 1000

    -- thread:
	while cache.seat == -1 do
		Wait(1)
        local sleep = true
		if not DoesEntityExist(vehicle) then return end
        if dynoProperties ~= nil or (type(dynoProperties) == 'table' and next(dynoProperties) ~= nil) then
            sleep = false
            SetVehicleCheatPowerIncrease(vehicle, dynoProperties.torque)
            ModifyVehicleTopSpeed(vehicle, dynoProperties.power)
        end
        tick = tick - 1 
        if tick <= 0 then
            tick = 1000
            dynoProperties = GetVehicleDynoProperties(vehicle)
        end
        if sleep then 
            Wait(2000)
        end
	end
end

-- check if driver when script (re)starts:
if cache.seat == -1 then
    Wait(2000)
    CreateThread(IsDrivingDynoVehicle)
end

-- is seats changed:
lib.onCache('seat', function(seat)
    -- if driver:
	if seat == -1 then
		SetTimeout(0, IsDrivingDynoVehicle)
	end
end)
