var royaleActive = false;
let weaponWheelVisible = false;
let weapons = [];
let currentSelection = 0;

// **Adjust this to your actual folder name if needed**
const ICON_FOLDER = "weapon_icons";
var audioPlayer = null;
var loopedPlayer = null;

// Initialize event listeners
$(document).ready(function () {
    // Listen for NUI messages from Lua
    window.addEventListener("message", function (event) {
        const data = event.data;

        switch (data.action) {
            case "openWeaponWheel":
                openWeaponWheel(data.weapons); // Pass the weapons data
                break;
            case "closeWeaponWheel":
                closeWeaponWheel();
                break;
            case "Update":
                UpdateUI(data.type, data);
                break;
            case "Draw":
                DrawText(data.type, data);
                break;
            case "empty":
                DrawReload(data.type, data);
                break;
            case "play":
                audioPlayer = new Howl({src: ["./sounds/" + data.audio]});
                audioPlayer.volume(data.volume);
                audioPlayer.play();
                break;
            case "playlooped":
                if (loopedPlayer != null) {
                    loopedPlayer.pause();
                }
                loopedPlayer = new Howl({
                    src: ["./sounds/" + data.audio],
                    loop: data.loop === true
                });
                loopedPlayer.volume(data.volume);
                loopedPlayer.play();
                break;
            case "stopaudiolooped":
                if (loopedPlayer != null) {
                    loopedPlayer.pause();
                }
                break;
            case "stopaudio":
                if (audioPlayer != null) {
                    audioPlayer.pause();
                }
                break;
        }
    });


    $(document).on('keyup', function (event) {
        if (event.code === "KeyG" && weaponWheelVisible) {
            closeWeaponWheel();
            selectWeapon();
        }
    });

    // Highlight weapon by mouse movement
    $(document).on("mousemove", function (event) {
        if (weaponWheelVisible && weapons.length > 0) {
            highlightWeaponByMouse(event.clientX, event.clientY);
        }
    });

});

// Open the weapon wheel
function openWeaponWheel(weaponData) {
    weapons = weaponData || [];
    weaponWheelVisible = true;
    populateWeaponWheel();
    $("#custom-weapon-wheel").fadeIn(200);
}

// Close the weapon wheel
function closeWeaponWheel() {
    weaponWheelVisible = false;
    $("#custom-weapon-wheel").fadeOut(200);
    $.post(`https://${GetParentResourceName()}/closeWeaponWheel`);
}

// Populate the weapon wheel with weapons
function populateWeaponWheel() {
    const weaponList = $("#weapon-list");
    weaponList.empty();

    const radius = 250; // Distance from the center
    const centerX = 300; // Center X of the wheel
    const centerY = 300; // Center Y of the wheel
    const angleIncrement = (Math.PI * 2) / (weapons.length + 1); // Adjusted for avoiding the bottom middle

    weapons.forEach((weapon, index) => {
        // Adjust the angle to start at the top (12 o'clock)
        const angle = (angleIncrement * index) - (Math.PI / 2);

        // Calculate positions for each weapon slot
        const x = centerX + radius * Math.cos(angle) - 40; // Adjust for item size
        const y = centerY + radius * Math.sin(angle) - 40;

        // Create the list item with an icon + label
        const listItem = $("<li>")
            .append(
                $("<img>")
                    .attr("src", `${ICON_FOLDER}/${weapon.name}.png`)  // Reference the matching PNG
                    .addClass("weapon-icon")
            )
            // .append($("<span>").text(weapon.label))
            .data("hash", weapon.hash)
            .css({
                top: `${y}px`,
                left: `${x}px`,
            })
            .addClass(index === currentSelection ? "selected" : "");

        weaponList.append(listItem);
    });

    updateWeaponDisplay();
}

// Update the weapon name and icon in the center
function updateWeaponDisplay() {
    const selectedWeapon = weapons[currentSelection];
    if (!selectedWeapon) {
        $("#weapon-display").text("Select a Weapon");
        return;
    }

    // Show both the weapon icon and label in the center
    $("#weapon-display").html(`
        <img 
          class="weapon-display-icon" 
          src="${ICON_FOLDER}/${selectedWeapon.name}.png" 
          alt="${selectedWeapon.label}" 
        />
        <div>${selectedWeapon.label}</div>
    `);
}

// Highlight the weapon based on mouse position
// function highlightWeaponByMouse(mouseX, mouseY) {
//     const centerX = window.innerWidth / 2;
//     const centerY = window.innerHeight / 2;

//     // Calculate the angle from the center
//     const angle = Math.atan2(mouseY - centerY, mouseX - centerX) + Math.PI; // Normalize to 0 - 2π

//     // Map the angle to the corresponding weapon index
//     const angleIncrement = (2 * Math.PI) / weapons.length;
//     const index = Math.max(0, Math.min(weapons.length - 1, Math.round(angle / angleIncrement) % weapons.length));


//     // Only update if the index has changed
//     if (index !== currentSelection) {
//         currentSelection = index;
//         highlightWeapon(index);
//         updateWeaponDisplay();
//     }
// }

function highlightWeaponByMouse(mouseX, mouseY) {
    const items = document.querySelectorAll("#weapon-list li");

    for (let i = 0; i < items.length; i++) {
        const rect = items[i].getBoundingClientRect();

        if (
            mouseX >= rect.left &&
            mouseX <= rect.right &&
            mouseY >= rect.top &&
            mouseY <= rect.bottom
        ) {
            if (i !== currentSelection) {
                currentSelection = i;
                highlightWeapon(i);
                updateWeaponDisplay();
            }
            return;
        }
    }
}

// Highlight a weapon
function highlightWeapon(index) {
    $("#weapon-list li").removeClass("selected");
    $("#weapon-list li").eq(index).addClass("selected");
    $.post(`https://${GetParentResourceName()}/WeaponSelectSound`, JSON.stringify({}));
}

// Select the currently highlighted weapon
function selectWeapon() {
    const selectedWeapon = weapons[currentSelection];
    if (selectedWeapon) {
        $.post(`https://${GetParentResourceName()}/SelectWeapon`, JSON.stringify({
            weapon: selectedWeapon.hash,
            isCustom: selectedWeapon.isCustom
        }));
    }
}


// 1.0 code
function DrawReload(type, data) {
    if (type == "reload") {
        if (data.active) {
            $(".blackbg2").show();
            $("#royale-text2").html(data.data.txt);
            $("#royale-char2").html(data.data.char);
        } else {
            royaleActive = false;
            $(".blackbg2").fadeOut(100);
        }
    }
}

function DrawText(type, data) {
    if (type == "text") {
        if (data.active) {
            $(".blackbg").show();
            $("#royale-text").html(data.data.txt);
            $("#royale-char").html(data.data.char);
        } else {
            royaleActive = false;
            $(".blackbg").fadeOut(100);
        }
    }
}

function UpdateUI(type, data) {
    if (type == "royale") {
        if (data.active) {
            if (data.data.shrink) {
                royaleActive = true;
                $(".background").show();
                $(".royale").show();
                $("#royale-position").html(data.data.ppllft + '/' + data.data.players + ' ALIVE');
                $("#royale-points").html(data.data.myills);
                $("#royale-time").html('\u2614 ' + data.data.time);
                $("#royale-skull").html("\u2620");
            } else {
                $(".background").show();
                $(".royale").show();
                $("#royale-position").html(data.data.ppllft + '/' + data.data.players + ' ALIVE');
                $("#royale-points").html(data.data.myills);
                $("#royale-time").html('\u2600 ' + data.data.time);
                $("#royale-skull").html("\u2620");
            }
        } else {
            if (data.data.shrink == "gulag") {
                $(".background").fadeOut(300);
                $(".royale").show();
                $("#royale-position").html(' ');
                $("#royale-points").html(' ');
                $("#royale-time").html(data.data.time);
                $("#royale-skull").html(" ");
            } else {
                royaleActive = false;
                $(".royale").fadeOut(300);
                $(".background").fadeOut(300);
            }
        }
    }
}
