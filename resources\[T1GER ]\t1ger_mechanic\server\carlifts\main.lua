CarLifts = {} -- Global table to store all carlifts instances

CreateThread(function()
    while Framework == nil or _FW[Framework] == nil do 
        Wait(500)
    end
    Wait(1000)

    -- initialize carlifts:
    FetchCarLifts()
end)

--- Fetches carlifts from database, instantiate a new CarLift object and store in CarLifts table
function FetchCarLifts()
    -- Query the database for carlifts
    local success, carlifts = pcall(MySQL.query.await, "SELECT * FROM t1ger_carlifts")
	if success and carlifts and next(carlifts) then
        for i = 1, #carlifts do
            -- instantiate a new CarLift object and store in CarLifts table
            CarLifts[carlifts[i].id] = CarLift:New(carlifts[i].id, json.decode(carlifts[i].coords), json.decode(carlifts[i].rotation))
        end
    end
end

--- Spawns instantiated CarLift objects and sync carlifts data to the source
--- @param src number player source/id
function EnsureCarLifts(src)
    local carlifts = {}
    -- spawn carlifts:
    for id, carlift in pairs(CarLifts) do
        CarLifts[id]:Spawn()
        carlifts[id] = CarLifts[id]:GetData()
    end
    -- sync data with client:
    TriggerClientEvent("t1ger_mechanic:client:loadCarLifts", src, carlifts)
end

-- Event to create a new car lift with coords and rotation
RegisterNetEvent("t1ger_mechanic:server:createCarLift", function(coords, rotation)
    MySQL.insert("INSERT INTO t1ger_carlifts (coords, rotation) VALUES (?, ?)", {json.encode(coords), json.encode(rotation)}, function(insertId)
        -- store inserted id
        local id = insertId
        
        -- instantiate a new CarLift object and store in CarLifts table
        CarLifts[id] = CarLift:New(id, coords, rotation)

        -- spawn car lift:
        CarLifts[id]:Spawn()
	end)
end)

-- Event to delete a carlift permanently
RegisterNetEvent("t1ger_mechanic:server:deleteCarLift", function(id, permanent)
    if not CarLifts or not CarLifts[id] then return end 
    CarLifts[id]:Delete(true)
end)

AddEventHandler("onResourceStop", function(resourceName)
    if (GetCurrentResourceName() ~= resourceName) then return end
    for id, carlift in pairs(CarLifts) do
        CarLifts[id]:Delete(false)
    end
end)