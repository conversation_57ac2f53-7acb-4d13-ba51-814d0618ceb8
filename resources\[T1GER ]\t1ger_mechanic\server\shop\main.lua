CreateThread(function()
    while Framework == nil or _FW[Framework] == nil do 
        Wait(500)
    end
    Wait(1000)

    -- initialize shops:
    ShopHandle:Init()
end)

--- Creates a new mechanic shop and saves it in the database.
RegisterNetEvent("t1ger_mechanic:server:createShop", function(input)
    local src = source

    --- Create Shop:
    local newShop = ShopHandle:Create({
        name = input.name,
        job = {name = input.job.name, label = input.job.label},
        blip = input.blip,
        account = input.account,
        employees = {},
        markers = {},
        billing = {},
        sale_price = input.sale_price,
        for_sale = input.for_sale
    })
    
    if not newShop then 
        return error("Failed to create shop")
    end

    TriggerClientEvent("t1ger_mechanic:client:shopCreated", -1, newShop)

    _API.SendNotification(src, string.format(locale("notification.shop_created"), input.name, input.job.name), "success", {})
end)

--- Deletes a shop:
RegisterNetEvent("t1ger_mechanic:server:deleteShop", function(shopId)
    local src = source

    local shopName = Shops[shopId].name

    local shopDeleted = ShopHandle:Delete(shopId)

    if not shopDeleted then 
        return error("Failed to delete shop")
    end

    TriggerClientEvent("t1ger_mechanic:client:shopDeleted", -1, shopId)

    _API.SendNotification(src, string.format(locale("notification.shop_deleted"), shopName, shopId), "success", {})
end)

--- Cancels a shop sale:
RegisterNetEvent("t1ger_mechanic:server:cancelShopSale", function(shopId)
    local src = source
    ShopHandle:CancelSale(shopId)
end)

--- Lists a shop for_sale:
RegisterNetEvent("t1ger_mechanic:server:listShopForSale", function(shopId, price)
    local src = source
    ShopHandle:ListForSale(shopId, price)
end)

--- Buy a shop for_sale:
RegisterNetEvent("t1ger_mechanic:server:buyShop", function(shopId)
    local src = source
    local identifier = _API.Player.GetIdentifier(src)

    -- check if player is owning another shop:
    local isOwner, ownedShopId = ShopHandle:IsPlayerShopOwner(src)
    if isOwner then
        Shops[ownedShopId]:RemoveEmployee(identifier)
        Shops[ownedShopId]:RemoveOwner()
    end

    ShopHandle:BuyListedShop(shopId, src)
end)

--- Sets owner for shop
RegisterNetEvent("t1ger_mechanic:server:setOwner", function(shopId, player, remove)
    local src = source

    -- remove action:
    if remove then
        ShopHandle:RemoveEmployee(shopId, true, player)
        Shops[shopId]:RemoveOwner()
        return _API.SendNotification(src, locale("notification.owner_removed"), "inform", {})
    end

    -- assign new owner:
    local highestJobGrade = Shops[shopId]:GetBossGrade() -- gets highest grade for the job

    -- remove current/old owner from employees:
    if Shops[shopId].owner then
        Shops[shopId]:RemoveEmployee(Shops[shopId].owner)
    end

    -- check if player is owning another shop:
    local isOwner, ownedShopId = ShopHandle:IsPlayerShopOwner(player.source)
    if isOwner then
        Shops[ownedShopId]:RemoveEmployee(player.identifier)
        Shops[ownedShopId]:RemoveOwner()
    end

    -- try hire new owner:
    local success = ShopHandle:AddEmployee(shopId, highestJobGrade, true, player.identifier)
    if not success then -- player is already hired in the shop
        success = ShopHandle:UpdateEmployee(shopId, highestJobGrade, true, player.identifier) -- update employee job grade
    end

    if not success then 
        return error("[t1ger_mechanic:server:setOwner] Unexpected error when updating employee")
    end

    -- set as owner:
    Shops[shopId]:SetOwner(player.identifier)

    -- get player full name:
    local fullname = _API.Player.GetCharacterName(tonumber(player.source))

    return _API.SendNotification(src, string.format(locale("notification.owner_assigned"), fullname, Shops[shopId].name), "inform", {})
end)

--- Sets account balance for shop
RegisterNetEvent("t1ger_mechanic:server:setAccountMoney", function(shopId, amount)
    local src = source

    if not Shops[shopId] then 
        return error("[t1ger_mechanic:server:setAccountMoney] shop doesnt exist for given shop id: ", shopId)
    end

    Shops[shopId]:SetMoney(amount)

    _API.SendNotification(src, string.format(locale("notification.updated_shop_account"), amount), "success", {})
end)

--- Create a marker:
RegisterNetEvent("t1ger_mechanic:server:createMarker", function(shopId, input)
    local src = source

    if not Shops[shopId] then 
        return error("[t1ger_mechanic:server:createMarker] Shop doesnt exist for given shop id: ", shopId)
    end

    if type(input) ~= 'table' then 
        return error("[t1ger_mechanic:server:createMarker] Invalid input type! Expected a table")
    end

    -- new marker payload:
    local newMarker = Shops[shopId]:CreateMarker(input.class, {
        class = input.class,
        name = input.name,
        coords = input.coords,
        blip = input.blip,
        type = input.type,
        color = input.color,
        bobUpAndDown = input.bobUpAndDown,
        faceCamera = input.faceCamera,
        stash = input.stash or nil
    })

    if not newMarker then 
        return error(string.format("Failed to create marker: '%s'", input.name))
    end

    _API.SendNotification(src, string.format(locale("notification.marker_created"), input.class, input.name), "success", {})
end)

--- Edit a marker:
RegisterNetEvent("t1ger_mechanic:server:editMarker", function(shopId, class, markerId, input)
    local src = source

    if not Shops[shopId] then 
        return error("[t1ger_mechanic:server:createMarker] Shop doesnt exist for given shop id: ", shopId)
    end

    if type(input) ~= 'table' then 
        return error("[t1ger_mechanic:server:createMarker] Invalid input type! Expected a table")
    end

    local updatedMarker = Shops[shopId]:UpdateMarker(class, markerId, {
        class = input.class,
        name = input.name,
        coords = input.coords,
        blip = input.blip,
        type = input.type,
        color = input.color,
        bobUpAndDown = input.bobUpAndDown,
        faceCamera = input.faceCamera,
        stash = input.stash or nil
    })

    if not updatedMarker then 
        return error(string.format("Failed to update marker: '%s' with id: '%s'", input.name, markerId))
    end

    TriggerClientEvent("t1ger_mechanic:client:markerUpdated", -1, shopId, class)

    _API.SendNotification(src, string.format(locale("notification.marker_updated"), input.class, markerId), "success", {})
end)

--- Delete a marker:
RegisterNetEvent("t1ger_mechanic:server:deleteMarker", function(shopId, class, markerId)
    local src = source

    if not Shops[shopId] then 
        return error("[t1ger_mechanic:server:deleteMarker] Shop doesnt exist for given shop id: ", shopId)
    end

    if type(class) ~= 'string' or class == "" then 
        return error("[t1ger_mechanic:server:deleteMarker] Invalid class type! Expected a non-empty string")
    end

    if type(markerId) ~= 'string' or markerId == "" then 
        return error("[t1ger_mechanic:server:deleteMarker] Invalid markerId type! Expected a non-empty string")
    end

    local markerDeleted = Shops[shopId]:DeleteMarker(class, markerId)

    if not markerDeleted then 
        return error(string.format("Failed to delete marker: '%s' with id: '%s'", class, markerId))
    end

    TriggerClientEvent("t1ger_mechanic:client:markerDeleted", -1, shopId, class, markerId)

    _API.SendNotification(src, string.format(locale("notification.marker_deleted"), class, markerId), "success", {})
end)

-- Function to check if player src can use boss events
---@param src number player source/server id
---@param shopId number unique shop identifier
---@return boolean
local function CanUseBossEvents(src, shopId)
    if ShopHandle:IsPlayerMechanicBoss(src, shopId) then 
        return true
    elseif ShopHandle:IsPlayerShopOwner(src, shopId) then 
        return true
    else
        return false
    end
end

-- Function to check if provided markerId exists
---@param shopId number unique shop identifier 
---@param class string marker class type
---@param markerId string unique identifier for the marker
---@return boolean
local function ValidateMarkerId(shopId, class, markerId)
    if Shops[shopId].markers and Shops[shopId]:MarkerExists(class, markerId) then 
        return true 
    else
        return false 
    end
end

-- Function to check if player src is near marker
---@param shopId number unique shop identifier 
---@param class string marker class type
---@param markerId string unique identifier for the marker
---@return boolean
local function IsNearMarker(src, markerCoords, dist)
    local pedCoords = GetEntityCoords(GetPlayerPed(src))
    local distance = #(pedCoords - vector3(markerCoords.x, markerCoords.y, markerCoords.z))
    if distance <= dist then
        return true 
    else
        return false
    end
end

--- Event to deposit account money:
RegisterNetEvent("t1ger_mechanic:server:depositAccount", function(shopId, amount, markerId)
    local src = source

    -- boss/owner check:
    if not CanUseBossEvents(src, shopId) then 
        return error(string.format("playerId: '%s' with identifier: '%s' attempted to deposit account funds without being boss/owner", src, _API.Player.GetIdentifier(src)))
    end

    -- validate marker id:
    if not ValidateMarkerId(shopId, "boss", markerId) then
        return error(string.format("playerId: '%s' with identifier: '%s' attempted to deposit account funds without using boss marker", src, _API.Player.GetIdentifier(src)))
    end

    -- coords check:
    local markerCoords = Shops[shopId].markers["boss"][markerId].coords
    if not IsNearMarker(src, markerCoords, 10.0) then
        return error(string.format("playerId: '%s' with identifier: '%s' attempted to deposit account funds without being near the boss menu", src, _API.Player.GetIdentifier(src)))
    end

    local money = _API.Player.GetMoney(src)
    if money >= amount then
        _API.Player.RemoveMoney(src, amount)
        Shops[shopId]:AddMoney(amount)
        _API.SendNotification(src, string.format(locale("notification.account_deposited"), math.groupdigits(amount)), "success", {})
    else
        _API.SendNotification(src, locale("notification.not_enough_money"), "inform", {})
    end
end)

--- Event to withdraw account money:
RegisterNetEvent("t1ger_mechanic:server:withdrawAccount", function(shopId, amount, markerId)
    local src = source

    -- boss/owner check:
    if not CanUseBossEvents(src, shopId) then 
        return error(string.format("playerId: '%s' with identifier: '%s' attempted to withdraw account funds without being boss/owner", src, _API.Player.GetIdentifier(src)))
    end

    -- validate marker id:
    if not ValidateMarkerId(shopId, "boss", markerId) then
        return error(string.format("playerId: '%s' with identifier: '%s' attempted to withdraw account funds without using boss marker", src, _API.Player.GetIdentifier(src)))
    end

    -- coords check:
    local markerCoords = Shops[shopId].markers["boss"][markerId].coords
    if not IsNearMarker(src, markerCoords, 10.0) then
        return error(string.format("playerId: '%s' with identifier: '%s' attempted to withdraw account funds without being near the boss menu", src, _API.Player.GetIdentifier(src)))
    end

    Shops[shopId]:RemoveMoney(amount)
    _API.Player.AddMoney(src, amount)
    _API.SendNotification(src, string.format(locale("notification.account_withdrew"), math.groupdigits(amount)), "success", {})
end)

--- Event to send recruitment offer to target player:
RegisterNetEvent("t1ger_mechanic:server:sendRecruitment", function(shopId, targetSrc)
    local src = source

    -- check if target player is already hired in any shop:
    local isEmployee, hiredShopId = ShopHandle:IsPlayerEmployee(targetSrc)
    if isEmployee then 
        return _API.SendNotification(src, string.format(locale("notification.player_already_hired_shop"), Shops[hiredShopId].name), "inform", {})
    end

    -- send recruitment offer:
    _API.SendNotification(src, string.format(locale("notification.employee_recruitment_sent"), targetSrc, GetPlayerName(targetSrc)), "inform", {})
    TriggerClientEvent("t1ger_mechanic:client:receiveRecruitment", targetSrc, shopId, src)
end)

-- Event for recruitment response from target player:
RegisterNetEvent("t1ger_mechanic:server:respondRecruitment", function(shopId, response, boss)
    local src = source

    -- if recruitment declined:
    if not response then 
        return _API.SendNotification(boss, string.format(locale("notification.player_declined_recruitment"), GetPlayerName(src)), "inform", {})
    end

    -- recruit player:
    local identifier = _API.Player.GetIdentifier(src)
    local success = ShopHandle:AddEmployee(shopId, 0, true, _API.Player.GetIdentifier(src))

    -- if AddEmployee fails:
    if not success then
        return _API.SendNotification(boss, string.format(locale("notification.player_already_hired_shop"), Shops[shopId].name), "inform", {})
    end

    -- notify boss:
    _API.SendNotification(src, string.format(locale("notification.you_have_been_hired"), Shops[shopId].name), "success", {})
    _API.SendNotification(boss, string.format(locale("notification.player_accepted_recruitment"), GetPlayerName(src)), "success", {})
end)

-- Event to fire/remove an employee:
RegisterNetEvent("t1ger_mechanic:server:fireEmployee", function(shopId, employee)
    local src = source

    if type(employee) ~= "table" then 
        return error("[t1ger_mechanic:server:fireEmployee] invalid employee type! Expected a table with identifier and name.")
    end

    local success = ShopHandle:RemoveEmployee(shopId, true, employee.identifier)

    if success then 
        _API.SendNotification(src, string.format(locale("notification.you_fired_employee"), employee.name), "inform", {})
    end
end)

-- Event to promote/demote an employee:
RegisterNetEvent("t1ger_mechanic:server:promoteEmployee", function(shopId, employee, grade, gradeLabel)
    local src = source

    if type(employee) ~= "table" then 
        return error("[t1ger_mechanic:server:promoteEmployee] invalid employee type! Expected a table with identifier and name.")
    end

    if type(grade) ~= "number" then
        return error("[t1ger_mechanic:server:promoteEmployee] invalid grade type! Expected a number")
    end

    local success = ShopHandle:UpdateEmployee(shopId, grade, true, employee.identifier)

    if success then 
        _API.SendNotification(src, string.format(locale("notification.you_updated_employee"), employee.name, gradeLabel), "inform", {})
    end
end)

-- Event to toggle duty/job in mechanic shops
RegisterNetEvent("t1ger_mechanic:server:toggleDuty", function(onJob, shopId)
    local src = source
    local playerJob = _API.Player.GetJob(src)

    if onJob then -- set as unemployed / off-duty
        _API.Player.SetJob(src, "unemployed", 0)
        _API.SendNotification(src, locale("notification.you_clocked_off_duty"), "inform", {})
    else -- set on job with grade / on-duty
        local identifier = _API.Player.GetIdentifier(src)
        local isEmployee, employee, index = Shops[shopId]:GetEmployee(identifier) -- find employee in the shop
        if isEmployee then
            _API.Player.SetJob(src, Shops[shopId].job.name, employee.grade)
        end
        _API.SendNotification(src, locale("notification.you_clocked_on_duty"), "inform", {})
    end
end)

-- callback to check if player has materials for crafting x item
lib.callback.register("t1ger_mechanic:server:hasMaterials", function(source, materials, quantity)
    local src = source
    local missingItems = {}

    -- loop through materials and check if has:
    for _, material in ipairs(materials) do
        local totalRequired = material.amount * quantity -- total count
        local itemCount = _API.Inventory.GetItemCount(src, material.name) -- get current inventory item count

        if itemCount < totalRequired then -- if not, add to missing table:
            missingItems[#missingItems + 1] = {
                name = material.name,
                label = material.label,
                invAmount = itemCount,
                reqAmount = totalRequired,
                diffAmount = (totalRequired - itemCount)
            }
        end
    end

    -- check if any missing materials:
    local hasMaterials = next(missingItems) == nil

    -- return boolean and missingItems?
    return hasMaterials, missingItems
end)

-- Event to craft item from workbench
RegisterNetEvent("t1ger_mechanic:server:craftItem", function(materials, output, quantity, shopId, markerId)
    local src = source

    -- check if shopId and markerId data types:
    if type(shopId) ~= "number" or type(markerId) ~= "string" then return end 

    -- check if marker exists
    if not Shops[shopId] or not Shops[shopId]:MarkerExists("workbench", markerId) then return end
    
    -- get player ped & coords:
    local playerPed = GetPlayerPed(src)
    if not playerPed then return end
    local plyCoords = GetEntityCoords(playerPed)

    -- get marker coords:
    local markerCoords = Shops[shopId].markers["workbench"][markerId].coords
    markerCoords = vector3(markerCoords.x, markerCoords.y, markerCoords.z)

    -- check distance:
    local distance = #(plyCoords - markerCoords)
    if distance > 10.0 then return end

    -- remove materials:
    for i = 1, #materials do
        local totalAmount = materials[i].amount * quantity
        _API.Inventory.RemoveItem(src, materials[i].name, totalAmount)
        Wait(10)
    end

    Wait(100)

    -- add crafted item:
    _API.Inventory.AddItem(src, output.name, quantity)

    -- notification:
    _API.SendNotification(src, string.format(locale("notification.you_crafted_item"), quantity, output.label), "success", {})
end)

-- Event to place an order from the supplier marker
RegisterNetEvent("t1ger_mechanic:server:supplierOrder", function(shopId, markerId, itemName, itemLabel, quantity, totalPrice, storageId)
    local src = source

    -- check if shopId and markerId data types:
    if type(shopId) ~= "number" or type(markerId) ~= "string" then return end 

    -- check if marker exists
    if not Shops[shopId] or not Shops[shopId]:MarkerExists("supplier", markerId) then return end
    
    -- get player ped & coords:
    local playerPed = GetPlayerPed(src)
    if not playerPed then return end
    local plyCoords = GetEntityCoords(playerPed)

    -- get marker coords:
    local markerCoords = Shops[shopId].markers["supplier"][markerId].coords
    markerCoords = vector3(markerCoords.x, markerCoords.y, markerCoords.z)

    -- check distance:
    local distance = #(plyCoords - markerCoords)
    if distance > 10.0 then return end

    -- check money:
    if Config.Shop.Supplier.playerPay then -- player account
        local playerAccount = _API.Player.GetMoney(src, "bank")
        if playerAccount < totalPrice then
            return _API.SendNotification(src, locale("notification.not_enough_bank_money"), "inform", {})
        end
        -- remove money from player bank account:
        _API.Player.RemoveMoney(src, totalPrice, "bank")
    else -- shop account
        local shopAccount = Shops[shopId]:GetAccount()
        if shopAccount < totalPrice then
            return _API.SendNotification(src, locale("notification.shop_account_insufficient_balance"), "inform", {})
        end
        -- remove money from shop account
        Shops[shopId]:RemoveMoney(totalPrice)
    end
    
    -- send items to storage?
    if Config.Shop.Supplier.receiveInStash and type(storageId) == "string" and storageId ~= "" then
        local success, response = _API.Stash.AddItem(storageId, itemName, quantity)
        if not success then
            _API.Inventory.AddItem(src, itemName, quantity)
        else
            _API.SendNotification(src, locale("notification.order_delivered_to_storage"), "success", {})
        end
    else
        _API.Inventory.AddItem(src, itemName, quantity)
    end 

    -- notification:
    _API.SendNotification(src, string.format(locale("notification.supplier_order_success"), string.format("%s%s", Config.Currency, math.groupdigits(totalPrice)), quantity, itemLabel), "success", {})
end)

-- callback to get shop's account
lib.callback.register("t1ger_mechanic:server:getShopAccount", function(source, shopId)
    local src = source

    if not Shops[shopId] then 
        return error("[t1ger_mechanic:server:getShopAccount] shop doesnt exist for given shop id: ", shopId)
    end

    return Shops[shopId]:GetAccount() or 0
end)