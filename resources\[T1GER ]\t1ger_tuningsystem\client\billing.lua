CreateBillingInput = function(inputTitle, billAmount)
    local nearbyPlayers = GetNearbyPlayersOption()
    if not nearbyPlayers then
        return false
    end
    
    local options = {
        {type = 'select', label = Lang['input_label_bill_player'], description = <PERSON>['input_desc_bill_player'], icon = 'person', options = nearbyPlayers, required = true},
        {type = 'number', label = Lang['input_label_bill_amount'], icon = 'dollar-sign', required = true, min = 1},
        {type = 'textarea', label = Lang['input_label_bill_note'], description = Lang['input_desc_bill_note'], icon = 'note', autosize = true, required = true},
        {type = 'date', label = Lang['input_label_bill_date'], icon = 'calendar', default = true, disabled = true},
        {type = 'time', label = Lang['input_label_bill_time'], icon = 'clock', format = 24, default = true, disabled = true},
    }

    if billAmount ~= nil and type(billAmount) == 'number' then
        table.remove(options, 3)
        options[2] = {type = 'input', label = Lang['input_label_bill_amount'], icon = 'dollar-sign', default = Lib.CommaValue(billAmount), disabled = true}
    end

    local input = lib.inputDialog(inputTitle, options)

    if not input or input == nil then
        return false
    end
    
    return input
end

RegisterNetEvent('tuningsystem:client:billing', function()
    local isTunerJob, shopId = IsTunerJob(Core.GetJob().name)
    local menuOptions = {}
    
    -- view bill:
    local shopBills = {}
    if Config.Shops[shopId] ~= nil and Config.Shops[shopId].billing ~= nil and next(Config.Shops[shopId].billing) ~= nil then
        for k,v in pairs(Config.Shops[shopId].billing) do
            table.insert(shopBills, {
                title = Lang['title_bill_overview']:format(v.id, Lib.CommaValue(v.amount)),
                icon = 'file-invoice-dollar',
                description = v.note,
                metadata = {
                    {label = Lang['meta_bill_ref'], value = v.id},
                    {label = Lang['meta_bill_shop'], value = v.shop},
                    {label = Lang['meta_bill_sender'], value = v.sender.name},
                    {label = Lang['meta_bill_receiver'], value = v.receiver.name},
                    {label = Lang['meta_bill_amount'], value = Config.Currency..v.amount},
                    {label = Lang['meta_bill_date'], value = v.date},
                    {label = Lang['meta_bill_time'], value = v.time},
                },
            })
        end
        menuOptions[#menuOptions + 1] = { title = Lang['title_view_bills'], icon = 'receipt', arrow = true, menu = 'view_bills'}
    end

    -- create bill:
    menuOptions[#menuOptions + 1] = {
        title = Lang['title_create_bill'],
        icon = 'file-invoice-dollar',
        onSelect = function()
            local input = CreateBillingInput(Lang['title_create_bill'])

            if not input then 
                return lib.showContext('billing_menu')
            else
                TriggerServerEvent('tuningsystem:server:createBill', shopId, input)
            end

            TriggerServerEvent('tuningsystem:server:createBill', shopId, input)
            if not Config.Debug then
                Wait(100)
                TriggerEvent('tuningsystem:client:billing')
            end
        end,

    }

    -- Register Billing Main Menu:
    lib.registerContext({
        id = 'billing_menu',
        title = Lang['title_billing'],
        menu = 'tuner_tablet_main',
        options = menuOptions,
    })

    -- Register View Bills Menu:
    lib.registerContext({
        id = 'view_bills',
        title = Lang['title_view_bills'],
        menu = 'billing_menu',
        options = shopBills,
    })

    lib.showContext('billing_menu')
end)

RegisterNetEvent('tuningsystem:client:sendBill')
AddEventHandler('tuningsystem:client:sendBill', function(shopId, input, playerSrc)
    lib.registerContext({
        id = 'pay_bill',
        title = Lang['title_bill_respond']:format(input[2]),
        canClose = false,
        options = {
            {
                title = Lang['title_yes'],
                icon = 'check',
                onSelect = function()
                    TriggerServerEvent('tuningsystem:server:payBill', shopId, input, true, playerSrc)
                    Core.Notification({
                        title = '',
                        message = Lang['you_paid_a_bill']:format(input[2], Config.Shops[shopId].name),
                        type = 'inform'
                    })
                end
            },
            {
                title = Lang['title_no'],
                icon = 'ban',
                onSelect = function()
                    TriggerServerEvent('tuningsystem:server:payBill', shopId, input, false, playerSrc)
                    Core.Notification({
                        title = '',
                        message = Lang['you_declined_bill']:format(input[2], Config.Shops[shopId].name),
                        type = 'inform'
                    })
                end
            },
        },
    })
    lib.showContext('pay_bill')
end)
