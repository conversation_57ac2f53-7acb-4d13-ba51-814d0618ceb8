:root{--phone-color-primary: rgb(255, 255, 255);--phone-color-opacity: rgba(242, 242, 242, .4);--phone-color-opacity2: rgb(30, 30, 30, .5);--phone-color-highlight: rgb(250, 250, 250);--phone-color-highlight2: rgb(240, 240, 240);--phone-color-highlight3: rgb(220, 220, 220);--phone-highlight-opacity15: rgba(145, 145, 145, .15);--phone-highlight-opacity35: rgba(145, 145, 145, .35);--phone-highlight-opacity45: rgba(145, 145, 145, .45);--phone-highlight-opacity55: rgba(145, 145, 145, .55);--phone-color-input: rgba(241, 241, 241, .656);--phone-text-primary: rgb(0, 0, 0);--phone-text-secondary: rgb(142, 142, 147);--phone-color-hover: rgb(240, 240, 240);--phone-color-border: rgba(200, 200, 200, .4);--phone-color-grey: #8e8e93;--phone-color-blue: #0a84ff;--phone-color-green: #32d74b;--phone-color-green-secondary: #092911;--phone-color-red: #ff3b30;--phone-color-orange: rgb(255, 157, 10);--phone-color-yellow: #cca250;--phone-color-pink: #ff3b30;--instagram-primary: #ffffff;--instagram-blue: rgb(0, 149, 246);--instagram-red: rgb(237, 73, 86);--instagram-primary-text: rgb(38, 38, 38);--instagram-secondary-text: rgb(142, 142, 142);--instagram-stroke: rgb(219, 219, 219);--instagram-border: rgb(219, 219, 219);--instagram-highlight: rgb(239, 239, 239);--tinder-color-pink: #ff4573;--tinder-color-orange: #ff5f65;--tinder-color-mix: #f5547c;--twitter-primary: #f5f8fa;--twitter-secondary: #14171a;--twitter-background-highlight: rgb(239, 243, 244);--twitter-primary-text: #14171a;--twitter-secondary-text: #657786;--twitter-alt-text: #657786;--twitter-border: #bdc5cd75;--twitter-border-secondary: #1d9bf0;--twitter-highlight: #1d9bf0;--twitter-hover: rgba(15, 20, 25, .1);--twitter-action: #14171a;--twitter-blue: #1d9bf0;--tiktok-primary: #ffffff;--tiktok-secondary: #000000;--tiktok-text-primary: #000000;--tiktok-text-secondary: #86878b;--tiktok-color-border: #d0d1d3;--tiktok-color-pink: #fe2c55;--tiktok-color-aqua: #00f2ea;--tiktok-color-yellow: #f8cd14;--tiktok-color-blue: #479fc5;--tiktok-color-unread: rgba(254, 44, 86, .2);--crypto-color-primary: rgb(255, 255, 255);--browser-primary: rgb(245, 245, 245);--browser-secondary: rgba(153, 153, 153, .15);--browser-gradient: linear-gradient(230deg, #f4d6ff, #c5f1ff);--browser-footer: rgba(255, 255, 255, .75);--browser-border: rgba(102, 102, 102, .75);--browser-text-secondary: #696969;--controlcentre-opacity: rgba(255, 255, 255, .15);--controlcentre-opacity2: rgba(255, 255, 255, .2);--controlcentre-active: rgba(255, 255, 255, .5);--notification-primary: rgba(215, 215, 215, .5);--notification-secondary: rgba(215, 215, 215, .1);--lockscreeneditor-background: rgba(255, 255, 255, .75);--lockscreeneditor-secondary: #d9d9d9;--app-bg: #ececec;--app-bg2: #ffffff;--app-secondary: #ffffff;--app-secondary2: #ececec;--app-highlight: #cccccc;--app-highlight2: #999999;--app-highlight3: #ffffff;--app-border: #666666;--app-slider: #cccccc;--app-slider-active: #333333;--app-button: #ffffff;--components-bg: #eeeeee;--components-secondary: #ffffff;--components-highlight: #cccccc}[data-theme=dark]{--phone-color-primary: #000000;--phone-color-opacity: rgb(30, 30, 30, .5);--phone-color-opacity2: rgba(242, 242, 242, .4);--phone-color-highlight: rgb(15, 15, 15);--phone-color-highlight2: rgb(20, 20, 20);--phone-color-highlight3: rgb(25, 25, 25);--phone-highlight-opacity35: rgba(145, 145, 145, .35);--phone-highlight-opacity45: rgba(50, 50, 50, .6);--phone-highlight-opacity55: rgb(60, 60, 60, .8);--phone-color-input: rgba(60, 60, 67, .6);--phone-text-primary: #f2f2f7;--phone-text-secondary: #6f6f6f;--phone-color-grey: #636366;--phone-color-hover: rgb(30, 30, 30);--phone-color-border: rgba(150, 150, 150, .2);--phone-color-blue: #076bcf;--instagram-primary: #000000;--instagram-blue: rgb(0, 149, 246);--instagram-red: rgb(237, 73, 86);--instagram-primary-text: rgb(250, 250, 250);--instagram-secondary-text: rgb(142, 142, 142);--instagram-stroke: rgb(219, 219, 219);--instagram-border: rgb(54, 54, 54);--instagram-highlight: rgb(38, 38, 38);--twitter-primary: #000000;--twitter-secondary: #f5f8fa;--twitter-background-highlight: rgb(20, 20, 20);--twitter-primary-text: #f5f8fa;--twitter-secondary-text: #aab8c2;--twitter-alt-text: #657786;--twitter-border: #38444d;--twitter-border-secondary: #38444d;--twitter-hover: rgba(150, 150, 150, .1);--twitter-highlight: #dcdcdc;--twitter-action: #1d9bf0;--twitter-blue: #1d9bf0;--crypto-color-primary: rgb(24, 26, 32);--tiktok-text-primary: #f2f2f7;--tiktok-text-secondary: #6f6f6f;--tiktok-color-border: #96969633;--browser-primary: rgb(15, 15, 15);--browser-secondary: rgba(153, 153, 153, .15);--browser-gradient: linear-gradient(230deg, #453b48, #2f393d);--browser-footer: rgba(51, 51, 51, .75);--browser-border: rgba(102, 102, 102, .75);--browser-text-secondary: #999999;--controlcentre-opacity: rgba(0, 0, 0, .15);--controlcentre-opacity2: rgba(0, 0, 0, .2);--controlcentre-active: rgba(0, 0, 0, .5);--notification-primary: rgba(0, 0, 0, .1);--notification-secondary: rgba(0, 0, 0, .12);--lockscreeneditor-background: rgba(0, 0, 0, .8);--lockscreeneditor-secondary: #333333;--app-bg: #000000;--app-bg2: #000000;--app-secondary: #141414;--app-secondary2: #141414;--app-highlight: #cccccc;--app-highlight2: #696969;--app-highlight3: #212121;--app-border: #cccccc;--app-slider: #999999;--app-slider-active: #ffffff;--app-button: #333333;--components-bg: #000000;--components-secondary: #141414;--components-highlight: #696969}.home-container{display:flex;flex-direction:column;align-items:center;gap:1rem;width:100%;height:100%;background-position:center;background-size:cover;position:relative}.home-container .blur-overlay{position:absolute;width:100%;height:100%}.home-container .home-header{width:85%;margin-top:4rem;color:#fff;font-size:28px;font-weight:500;text-align:left;z-index:1;display:flex;align-items:center;justify-content:space-between}.home-container .home-header .close{cursor:pointer;font-size:30px;color:var(--phone-color-hover)}.home-container .home-header .title{display:flex;align-items:center;gap:.5rem}.home-container .home-wrapper{width:85%;display:flex;flex-direction:column;z-index:1;overflow-y:auto;overflow-x:hidden}.home-container .home-wrapper::-webkit-scrollbar{display:none}.home-container .home-wrapper .title{font-size:18px;color:#fff;font-weight:500;margin-bottom:.75rem;margin-top:1.5rem;display:flex;align-items:center;gap:.2rem}.home-container .home-wrapper .title svg{font-size:22px;opacity:.5}.home-container .home-wrapper .category{display:grid;grid-template-columns:repeat(2,1fr);gap:1rem}.home-container .home-wrapper .category.scroll{overflow-y:auto;overflow-x:none;max-height:100%}.home-container .home-wrapper .category.scroll::-webkit-scrollbar{display:none}.home-container .home-wrapper .items{display:flex;flex-direction:column;gap:1rem}.home-container .home-wrapper .item{width:100%;height:11rem;background-color:var(--phone-highlight-opacity45);-webkit-backdrop-filter:blur(30px);backdrop-filter:blur(30px);border-radius:20px;display:flex;align-items:flex-end;cursor:pointer;transition:all .3s ease-in-out}.home-container .home-wrapper .item.small{display:flex;align-items:center;height:5rem}.home-container .home-wrapper .item.small .icon{margin-left:.75rem;position:inherit}.home-container .home-wrapper .item.small:hover{filter:brightness(1.2)}.home-container .home-wrapper .item.full{grid-row:span 2}.home-container .home-wrapper .item[data-active=true]{background-color:#fff;filter:brightness(.9)}.home-container .home-wrapper .item[data-active=true] .icon{background-color:#ddd;opacity:.8}.home-container .home-wrapper .item[data-active=true] .icon.blue{background-color:#66e4e5;color:#fff}.home-container .home-wrapper .item[data-active=true] .icon.yellow{background-color:var(--phone-color-yellow);color:#fff}.home-container .home-wrapper .item[data-active=true] .info .title{color:#000;opacity:1}.home-container .home-wrapper .item[data-active=true] .info .value{color:#000;opacity:.6}.home-container .home-wrapper .item .icon{position:absolute;left:.75rem;top:.75rem;width:3rem;height:3rem;border-radius:50%;background-color:var(--phone-highlight-opacity45);display:flex;justify-content:center;align-items:center;opacity:1;color:#fff}.home-container .home-wrapper .item .icon.blue{color:#66e4e5}.home-container .home-wrapper .item .icon.yellow{color:var(--phone-color-yellow)}.home-container .home-wrapper .item .icon svg{font-size:28px}.home-container .home-wrapper .item .info{display:flex;flex-direction:column;padding:.75rem}.home-container .home-wrapper .item .info .title{color:#fafafa;opacity:.9;font-size:18px;font-weight:500;margin:0}.home-container .home-wrapper .item .info .value{color:#fff;opacity:.4;font-size:16px;font-weight:400}.home-container .home-wrapper .item:hover:not(.active,.small){background-color:#fff}.home-container .home-wrapper .item:hover:not(.active,.small) .icon{background-color:#ddd;opacity:.8}.home-container .home-wrapper .item:hover:not(.active,.small) .info .title{color:#000;opacity:1}.home-container .home-wrapper .item:hover:not(.active,.small) .info .value{color:#000;opacity:.6}
