npc_job, cancel_job = {}, false

GetAvailableJobLocation = function(jobMenu, locations)
    math.randomseed(GetGameTimer())
	local num = math.random(1, #locations)

	local count = 0
	while locations[num].inUse and count < 100 do
        count = count + 1
        num = math.random(1, #locations)
    end

	if count == 100 then
        return Core.Notification({
            title = '',
            message = Lang['jobs_check_back_later'],
            type = 'inform'
        })
	else
        npc_job = {menu = jobMenu, jobId = num, started = true}
		Wait(200)
		locations[num].inUse = true
        return locations[num], num
	end
end

GetRandomJobVehicle = function(vehicles)
    math.randomseed(GetGameTimer())
    return vehicles[math.random(#vehicles)]
end

GetRandomJobPed = function(peds)
    math.randomseed(GetGameTimer())
    return peds[math.random(#peds)]
end

CreateJobVehicle = function(pos, model)
    local entity, count = nil, 0
    ClearAreaOfVehicles(pos.x, pos.y, pos.z, 5.0, false, false, false, false, false)
    Lib.LoadModel(model)

    Core.SpawnVehicle(model, {x = pos[1], y = pos[2], z = pos[3]}, pos[4], function(vehicle)
        SetEntityCoordsNoOffset(vehicle, pos[1], pos[2], pos[3])
        SetEntityHeading(vehicle, pos[4])
        SetVehicleOnGroundProperly(vehicle)
        entity = vehicle
    end, true)

    while not DoesEntityExist(entity) and count < 100 do
        count = count + 1
        Wait(1000)
    end

    return entity
end

CreateTestJobVehicle = function(pos, model)
    local entity, count = nil, 0
    Lib.LoadModel(model)
    Core.SpawnVehicle(model, {x = pos[1], y = pos[2], z = pos[3]}, pos[4], function(vehicle)
        SetEntityCoordsNoOffset(vehicle, pos[1], pos[2], pos[3])
        SetEntityHeading(vehicle, pos[4])
        FreezeEntityPosition(vehicle, true)
        SetEntityVisible(vehicle, false)
        SetEntityInvincible(vehicle, true)
        SetEntityCollision(vehicle, false, false)
        entity = vehicle
    end, true)

    while not DoesEntityExist(entity) and count < 100 do
        count = count + 1
        Wait(1000)
    end

    return entity
end

CreateJobPedInsideVehicle = function(vehicle, pedModel)
    local entity, count = nil, 0
    
    SetPedRelationshipGroupHash(player, GetHashKey("PLAYER"))
	AddRelationshipGroup('NPC')

    Lib.LoadModel(pedModel)
    entity = CreatePedInsideVehicle(vehicle, 4, GetHashKey(pedModel), -1, true, true)

    while not DoesEntityExist(entity) and count < 100 do
        count = count + 1
        Wait(1000)
    end

    -- Networked: 
    NetworkRegisterEntityAsNetworked(entity)
    SetNetworkIdCanMigrate(NetworkGetNetworkIdFromEntity(entity), true)
    SetNetworkIdExistsOnAllMachines(NetworkGetNetworkIdFromEntity(entity), true)

    -- Settings:
    SetPedKeepTask(entity, true)
    SetPedDropsWeaponsWhenDead(entity, false)
    SetEntityInvincible(entity, false)
    SetEntityVisible(entity, true)

    -- Relationship:
	SetPedRelationshipGroupHash(entity, GetHashKey("NPC"))	
	SetRelationshipBetweenGroups(0, GetHashKey("PLAYER"), GetHashKey("NPC"))
	SetRelationshipBetweenGroups(0, GetHashKey("NPC"), GetHashKey("PLAYER"))

    return entity
end

CreateJobBlip = function(pos, cfg, entity)
    local blip = AddBlipForCoord(pos[1],pos[2],pos[3])
    if entity ~= nil then
        RemoveBlip(blip)
        blip = AddBlipForEntity(entity)
    end
    SetBlipSprite(blip, cfg.sprite)
    SetBlipColour(blip, cfg.color)
    AddTextEntry('MYBLIP', cfg.label)
    BeginTextCommandSetBlipName('MYBLIP')
    AddTextComponentSubstringPlayerName(name)
    EndTextCommandSetBlipName(blip)
    SetBlipScale(blip, cfg.scale)
    SetBlipAsShortRange(blip, true)
    SetBlipRoute(blip, cfg.route.enable)
    SetBlipRouteColour(blip, cfg.route.color)
    return blip
end

CanInteractWithDoor = function(entity, pos, door)
    if Lib.IsEntityValid(entity) and GetEntityType(entity) ~= 0 then
        if not GetIsDoorValid(entity, door) or GetVehicleDoorLockStatus(entity) > 1 or IsVehicleDoorDamaged(entity, door) then return end

        if door == 4 or door == 5 then
            local offset = door == 4 and vec3(0.5, 1.0, 0.5) or door == 5 and vec3(0.5, 0.0, 0.5)
            local min, max = GetModelDimensions(GetEntityModel(entity))
            offset = (max - min) * offset + min
            local doorPos = GetOffsetFromEntityInWorldCoords(entity, offset.x, offset.y, offset.z)

            if #(pos - doorPos) <= 1.0 then
                return true
            end
        else
            local boneName = Lib.VehicleDoors[tostring(door)].bone
    
            if not boneName then return false end
    
            local boneId = GetEntityBoneIndexByName(entity, boneName)
    
            if boneId ~= -1 then
                return #(pos - GetEntityBonePosition_2(entity, boneId)) <= 1.0 or Lib.VehicleDoors[tostring(door)].bone2 ~= nil and #(pos - GetEntityBonePosition_2(entity, GetEntityBoneIndexByName(entity, Lib.VehicleDoors[tostring(door)].bone2))) <= 1.0
            end
        end

    else
        return false 
    end
end

CanInteractWithWheel = function(entity, pos, wheelId, boneName)
    if Lib.IsEntityValid(entity) and GetEntityType(entity) ~= 0 then
        if GetVehicleWheelHealth(entity, wheelId) <= 0.0 then return end

        if boneName == nil or not boneName then return false end

        local boneId = GetEntityBoneIndexByName(entity, boneName)
    
        if boneId ~= -1 then
            return #(pos - GetEntityBonePosition_2(entity, boneId)) <= 1.2
        end
    else
        return false 
    end
end

CanInteractWithPedInsideVehicle = function(vehicle, ped)
    if Lib.IsEntityValid(vehicle) and GetEntityType(vehicle) ~= 0 and (ped ~= nil and Lib.IsEntityValid(ped) and GetEntityType(ped) ~= 0) then
        local pedCoords = GetEntityCoords(ped)
        local distance = #(coords - pedCoords)
        if distance <= 2.0 then
            return true
        end
    else
        return false 
    end
end

isSalvaging = false
SalvageSelectedPart = function(vehicle, target, part, index, bones)
    if isSalvaging then 
        return Core.Notification({
            title = '',
            message = Lang['already_salvaging_part'],
            type = 'inform'
        }) 
    end

    local boneId = GetEntityBoneIndexByName(vehicle, bones[1])
    local bonePos, targetPos = GetEntityBonePosition_2(vehicle, boneId), nil
    if part == 'door' and (index == 4 or index == 5) then
        local offset = index == 4 and vec3(0.5, 1.0, 0.5) or index == 5 and vec3(0.5, 0.0, 0.5)
        local min, max = GetModelDimensions(GetEntityModel(vehicle))
        offset = (max - min) * offset + min
        targetPos = GetOffsetFromEntityInWorldCoords(vehicle, offset.x, offset.y, offset.z)
    end

    local distance = targetPos ~= nil and #(coords - targetPos) or #(coords - bonePos)
    local allowedDist = targetPos ~= nil and 0.8 or 1.2

    if distance > allowedDist then
        return Core.Notification({
            title = '',
            message = Lang['salvage_move_closer'],
            type = 'inform'
        })
    end

    isSalvaging = true

    TaskTurnPedToFaceCoord(player, bonePos.x, bonePos.y, bonePos.z, 1000)
    Wait(1000)
    local anim, modName = {dict = 'anim@amb@clubhouse@tutorial@bkr_tut_ig3@', name = 'machinic_loop_mechandplayer', blendIn = 2.0, blendOut = 5.0, duration = -1, flag = 1}, 'modDoorR'
    
    if part == 'door' then
        if index == 4 or index == 5 then
            modName = index == 4 and 'modHood' or index == 5 and 'modTrunk'
            anim = {dict = 'mini@repair', name = 'fixing_a_player', blendIn = 2.0, blendOut = 2.0, duration = -1, flag = 1}
        end
    elseif part == 'wheel' then
        modName = 'modFrontWheels'
    end

    Lib.LoadAnim(anim.dict)
    TaskPlayAnim(player, anim.dict, anim.name, anim.blendIn, anim.blendOut, anim.duration, anim.flags, 0, 0, 0, 0)

    Wait(100)

    local success = nil
    if Config.SalvageJob.SkillCheck.enable then
        success = SkillCheck(Config.SalvageJob.SkillCheck.difficulty, Config.SalvageJob.SkillCheck.inputs)
    else
        success = true
    end

    if success then
        Wait(250) 
        if part == 'door' then
            SetVehicleDoorBroken(vehicle, index, true)
        elseif part == 'wheel' then
            BreakOffVehicleWheel(vehicle, index, false, true, true, false)
            ApplyForceToEntityCenterOfMass(vehicle, 0, 0.01, 0.01, 0.01, true, true, true)
        end

        ClearPedTasks(player)

        local newAnim = Config.Mods[modName].anim.idle
        if newAnim.dict ~= nil then
            Lib.LoadAnim(newAnim.dict)
            TaskPlayAnim(player, newAnim.dict, newAnim.name, newAnim.blendIn, newAnim.blendOut, newAnim.duration, newAnim.flags, 0, 0, 0, 0)
        end
            
        local modObject, netId, pos, rot = CreateModObject(modName)
        AttachEntityToEntity(modObject, player, GetPedBoneIndex(player, 28422), pos.x, pos.y, pos.z, rot.x, rot.y, rot.z, true, true, false, true, 2, true)

        local partIdentifier = part..tostring(index)
        npc_job.parts[tostring(partIdentifier)] = false
        Lib.RemoveLocalEntity(vehicle, {target.id}, target.label)

        Wait(500)

        local isComplete = true
        for k,v in pairs(npc_job.parts) do
            if v == true then 
                isComplete = false 
                break
            end
        end

        DeleteObject(modObject)
        TriggerServerEvent('tuningsystem:server:salvageReward', part)
        ClearPedTasks(player)

        if isComplete then 
            Core.Notification({
                title = '',
                message = Lang['salvage_success_msg'],
                type = 'inform'
            })
            Wait(2000)
            NetworkFadeOutEntity(vehicle, false, true)
            npc_job.vehicleSalvaged = true
        end
    else
        Wait(200)
        ClearPedTasks(player)
    end

    isSalvaging = false
end

GetVehicleSalvagePoints = function(vehEntity)
    local options = {}
    npc_job.parts = {}

    for doorId,v in pairs(Lib.VehicleDoors) do
        if GetIsDoorValid(vehEntity, v.index) then
            local bones = {v.bone}
            if v.bone2 ~= nil then
                table.insert(bones, v.bone2)
            end
            local cfg = Config.SalvageJob.Target
            local target = {
                id = 'tuningsystem:salvageCar:'..v.bone, icon = cfg.icon, label = cfg.label:format(v.label),
            }
            local optionNum = #options + 1 
            options[optionNum] = {
                name = target.id,
                icon = target.icon,
                label = target.label,
                distance = 2,
                canInteract = function(entity, distance)
                    return CanInteractWithDoor(entity, coords, v.index)
                end,
                onSelect = function(data)
                    SalvageSelectedPart(data.entity, target, 'door', v.index, bones)
                end
            }
            if v.index <= 3 then 
                options[optionNum].bones = bones
            end
            local partIdentifier = 'door'..tostring(v.index)
            npc_job.parts[partIdentifier] = true
        end
    end

    local numWheels = tostring(GetVehicleNumberOfWheels(vehEntity))
    for tyreIdx,v in pairs(Lib.VehicleWheels[numWheels]) do
        local bones = {v.bone}
        local cfg = Config.SalvageJob.Target
        local target = { id = 'tuningsystem:salvageCar:'..v.bone, icon = cfg.icon, label = cfg.label:format(v.label), }
        options[#options + 1] = {
            name = target.id,
            icon = target.icon,
            label = target.label,
            bones = bones,
            distance = 2,
            canInteract = function(entity, distance)
                return CanInteractWithWheel(entity, coords, v.wheelId, v.bone)
            end,
            onSelect = function(data)
                SalvageSelectedPart(data.entity, target, 'wheel', v.wheelId, bones)
            end
        }
        local partIdentifier = 'wheel'..tostring(v.wheelId)
        npc_job.parts[partIdentifier] = true
    end

    return options
end

StartSalvageVehicleJob = function()
    
    if npc_job ~= nil and next(npc_job) then
        return Core.Notification({
            title = '',
            message = Lang['have_ongoing_job'],
            type = 'inform'
        }) 
    end
    
    local complete = false
    local cfg = Config.SalvageJob
    
    local location, locationNum = GetAvailableJobLocation(cfg.Menu, cfg.Locations)
    if locationNum == nil then return end

    if cfg.Cooldown.enable then
        TriggerServerEvent('tuningsystem:server:addJobCooldown', cfg.Cooldown.time)
    end

    local junkCar = GetRandomJobVehicle(cfg.JunkCars)

    npc_job.blip = CreateJobBlip(location.pos, cfg.Blip)

    local function CreateSalvageCarTarget(vehicleEntity)
        Wait(1000)
        local target = {
            options = GetVehicleSalvagePoints(vehicleEntity),
            distance = 2.0,
            canInteract = IsNearVehicle,
        }
        Lib.AddLocalEntity(vehicleEntity, target)
    end

    while not complete do
        Wait(1)

        if location.inUse then 

            -- Start Message:
            if npc_job.startMessage == nil then
                Lib.AdvancedNotification(cfg.AdvNotify.textureDict, cfg.AdvNotify.textureName, 6, Lang['adv_scrapyard'], false, Lang['adv_gps_updated'], Lang['adv_junk_car_located'])
                npc_job.startMessage = true 
            end

            -- Variable to store distance from location:
			local job_distance = #(coords - vector3(location.pos[1], location.pos[2], location.pos[3]))
            
            -- Spawn Junk Car:
            if job_distance < 100.0 and npc_job.vehicle == nil then
				npc_job.vehicle = CreateJobVehicle(location.pos, junkCar)
                -- update blip to be on vehicle entity:
				SetEntityAsMissionEntity(npc_job.vehicle, true, true)
                if DoesBlipExist(npc_job.blip) then 
                    RemoveBlip(npc_job.blip)
                end
                npc_job.blip = CreateJobBlip(location.pos, cfg.Blip, npc_job.vehicle)
			end

            -- Create Target:
            if job_distance < 10.0 and DoesEntityExist(npc_job.vehicle) then
                
                if npc_job.vehicleTarget == nil then
                    Core.Notification({
                        title = '',
                        message = Lang['salvage_instructions'],
                        type = 'inform'
                    })
                    CreateSalvageCarTarget(npc_job.vehicle)
                    npc_job.vehicleTarget = true
                end

            end

            if npc_job.vehicleTarget then

                if npc_job.vehicleSalvaged then
                    Wait(2000)
                    cancel_job = true 
                end

                if job_distance > 50.0 then
                    Core.Notification({
                        title = '',
                        message = Lang['salvage_veh_deleted'],
                        type = 'inform'
                    })
                    cancel_job = true
                end
            end

        end

        if npc_job == nil or next(npc_job) == nil  then
            cancel_job = true
        end

        if cancel_job == true then
            if DoesBlipExist(npc_job.blip) then 
                RemoveBlip(npc_job.blip)
            end
            if DoesEntityExist(npc_job.vehicle) then 
                DeleteEntity(npc_job.vehicle)
            end
            Config.SalvageJob.Locations[locationNum].inUse = false
            complete = true
            npc_job = {}
            cancel_job = false
        end

    end

end

StartMobileTuningJob = function()
    if npc_job ~= nil and next(npc_job) then
        return Core.Notification({
            title = '',
            message = Lang['have_ongoing_job'],
            type = 'inform'
        }) 
    end
    
    local complete = false
    local cfg = Config.MobileTuningJob
    
    local location, locationNum = GetAvailableJobLocation(cfg.Menu, cfg.Locations)
    if locationNum == nil then return end

    if cfg.Cooldown.enable then
        TriggerServerEvent('tuningsystem:server:addJobCooldown', cfg.Cooldown.time)
    end

    local mobileCar = GetRandomJobVehicle(cfg.Cars)
    local mobilePed = GetRandomJobPed(cfg.Peds)

    npc_job.blip = CreateJobBlip(location.pos, cfg.Blip)

    local CreateJobPedTarget = function(vehEntity, pedEntity)
        Wait(1000)
        local target = {
            options = {
                {
                    name = 'tuningsystem:mobileTuning:ped:target',
                    icon = cfg.Target.icon,
                    label = cfg.Target.label,
                    distance = 2,
                    canInteract = function(entity, distance, pos, name)
                        return CanInteractWithPedInsideVehicle(entity, pedEntity)
                    end,
                    onSelect = function(data)
                        local vehicle = data.entity 
                        if DoesEntityExist(npc_job.vehicle) and Lib.IsEntityValid(npc_job.vehicle) and GetEntityType(npc_job.vehicle) ~= 0 then
                            if npc_job.required == nil then 
                                SetVehicleModKit(npc_job.vehicle, 0)
                                npc_job.vehicleProps = Core.GetVehicleProperties(npc_job.vehicle)
                                RollDownWindow(npc_job.vehicle, 0)
                                local modVariants = GetVehicleModVariants(npc_job.vehicle, npc_job.modInfo.modName, npc_job.modInfo.modType)
                                local availableMods, currentValue = {}, nil
                                for i = 1, #modVariants, 1 do
                                    if modVariants[i].modValue ~= npc_job.vehicleProps[npc_job.modInfo.modName] then
                                        availableMods[#availableMods+1] = modVariants[i]
                                    else
                                        currentValue = modVariants[i].modValue
                                    end
                                end
                                math.randomseed(GetGameTimer())
                                npc_job.required = availableMods[math.random(#availableMods)]

                                Core.Notification({
                                    title = '',
                                    message = Lang['mobile_tuning_npc_require']:format(npc_job.required.modLabel),
                                    type = 'inform'
                                })
                            else
                                npc_job.vehicleProps = Core.GetVehicleProperties(npc_job.vehicle)
                                RollDownWindow(npc_job.vehicle, 0)

                                if npc_job.vehicleProps[npc_job.modInfo.modName] == npc_job.required.modValue then
                                    Lib.LoadAnim('mp_common')
                                    TaskTurnPedToFaceEntity(player, npc_job.ped, 1000)
                                    Wait(1000)
                                    TaskPlayAnim(player, 'mp_common', 'givetake2_a', 4.0, 4.0, 2000, 49, 0.0, 0, 0, 0)
                                    Wait(2000)
                                    ClearPedTasks(player)
                                    RollUpWindow(npc_job.vehicle, 0)
                                    SetVehicleCanBeUsedByFleeingPeds(npc_job.vehicle, true)
                                    math.randomseed(GetGameTimer())
                                    local amount = math.random(cfg.Reward.min, cfg.Reward.max)
                                    TriggerServerEvent('tuningsystem:server:mobileTuningReward', amount)
                                    Core.Notification({
                                        title = '',
                                        message = Lang['mobile_tuning_reward_msg']:format(npc_job.required.modLabel),
                                        type = 'inform'
                                    })
                                    TaskVehicleDriveWander(npc_job.ped, npc_job.vehicle, 80.0, 786603)
                                    Wait(3000)
                                    TaskSmartFleePed(npc_job.ped, player, 40.0, 20000)
                                    npc_job.workDone = true
                                else
                                    Core.Notification({
                                        title = '',
                                        message = Lang['mobile_tuning_wrong_part']:format(npc_job.required.modLabel),
                                        type = 'inform'
                                    })
                                end

                            end
                        end
                    end
                }
            },
            distance = 2.0,
            canInteract = IsNearVehicle,
        }
        Lib.AddLocalEntity(vehEntity, target)
    end

    local GetRandomModItem = function(model)
        -- Create a test vehicle:
        local testVehicle = CreateTestJobVehicle({coords.x, coords.y, coords.z-3.0}, model)
        FreezeEntityPosition(testVehicle, true)
        SetEntityInvincible(testVehicle, true)
        SetEntityVisible(testVehicle, false)
        SetEntityCollision(testVehicle, false, false)
        local vehProps = Core.GetVehicleProperties(testVehicle)

        -- We dont want these mod items in the NPC jobs:
        local blacklisted = {}
        local tb = {'mod_turbo', 'mod_exterior', 'mod_frame', 'mod_interior', 'mod_exterior', 'mod_extras', 'mod_respray', 'mod_rim', 'mod_light', 'mod_livery', 'mod_neon', 'mod_plate', 'mod_tyresmoke', 'mod_bullettires', 'mod_stocktires', 'mod_drifttires'}
        for k,v in pairs(tb) do 
            blacklisted[v] = true
        end

        -- Get Random Amount Mod Item:
        math.randomseed(GetGameTimer())
        local item = Config.Items['mods'][math.random(# Config.Items['mods'])]
        local options = GetAllowedFromItem(item.name, testVehicle, vehProps)
        local count = 0
        while (blacklisted[item.name] == true or next(options) == nil) and count < 100 do
            math.randomseed(GetGameTimer())
            item =  Config.Items['mods'][math.random(# Config.Items['mods'])]
            options = GetAllowedFromItem(item.name, testVehicle, vehProps)
            count = count + 1
            Wait(10)
        end

        -- Delete Test Vehicle:
        DeleteVehicle(testVehicle)
        if testVehicle ~= nil and DoesEntityExist(testVehicle) then 
            DeleteEntity(testVehicle)
        end

        -- properly store the data:
        local data = {}
        if #options > 1 then
            math.randomseed(GetGameTimer())
            data = options[math.random(#options)]
        else
            data = options[1]
        end

        return item, {modName = data.args.modName, modType = data.args.modType, itemName = data.args.itemName, modLabel = data.label}
    end

    while not complete do
        Wait(1)

        if location.inUse then 

            -- Start Message:
            if npc_job.startMessage == nil then
                npc_job.modItem, npc_job.modInfo = GetRandomModItem(mobileCar)
                Lib.AdvancedNotification(cfg.AdvNotify.textureDict, cfg.AdvNotify.textureName, 6, Lang['adv_mobile_tuning_service'], false, Lang['adv_gps_updated'], Lang['adv_mobile_tuning_located']:format(tostring((npc_job.modInfo.modLabel)), npc_job.modItem.label))
                npc_job.startMessage = true 
            end

            -- Variable to store distance from location:
			local job_distance = #(coords - vector3(location.pos[1], location.pos[2], location.pos[3]))
            
            -- Spawn Mobile Tuning Car:
            if job_distance < 100.0 and npc_job.vehicle == nil then
				npc_job.vehicle = CreateJobVehicle(location.pos, mobileCar)
				SetEntityAsMissionEntity(npc_job.vehicle, true, true)

                -- Settings for job vehicle:
                SetVehicleDoorsLockedForAllPlayers(npc_job.vehicle, true)
                SetVehicleUndriveable(npc_job.vehicle, true)

                -- update blip to be on vehicle entity:
                if DoesBlipExist(npc_job.blip) then 
                    RemoveBlip(npc_job.blip)
                end
                npc_job.blip = CreateJobBlip(location.pos, cfg.Blip, npc_job.vehicle)
			end

            -- Create Job Ped:
            if npc_job.vehicle ~= nil and DoesEntityExist(npc_job.vehicle) and npc_job.ped == nil then
				npc_job.ped = CreateJobPedInsideVehicle(npc_job.vehicle, mobilePed)
                RollDownWindow(npc_job.vehicle, 0)
                CreateJobPedTarget(npc_job.vehicle, npc_job.ped)
            end

            -- instructions:
            if job_distance < 10.0 and DoesEntityExist(npc_job.vehicle) then
                
                if npc_job.vehicleTarget == nil then
                    Core.Notification({
                        title = '',
                        message = Lang['mobile_tuning_instructions'],
                        type = 'inform'
                    })
                    npc_job.vehicleTarget = true
                end

            end

            if npc_job.vehicleTarget then

                if npc_job.workDone then
                    Wait(4000)
                    cancel_job = true 
                end

                if job_distance > 50.0 then
                    Core.Notification({
                        title = '',
                        message = Lang['mobile_tuning_veh_deleted'],
                        type = 'inform'
                    })
                    cancel_job = true
                end
            end

        end

        if npc_job == nil or next(npc_job) == nil  then
            cancel_job = true
        end

        if cancel_job == true then
            if DoesBlipExist(npc_job.blip) then 
                RemoveBlip(npc_job.blip)
            end
            if DoesEntityBelongToThisScript(npc_job.ped) then 
                DeleteEntity(npc_job.ped)
            end
            if DoesEntityExist(npc_job.vehicle) then 
                DeleteEntity(npc_job.vehicle)
            end
            Config.MobileTuningJob.Locations[locationNum].inUse = false
            complete = true
            npc_job = {}
            cancel_job = false
        end

    end
end
