import{r as i,a1 as ge,j as a,a as e,ca as M,d as $,L as s,q as N,s as F,t as V,C as D,u as Q,G as j,K as J,a0 as ee,V as ne,ab as Z,a4 as K,a5 as z,F as L,o as fe,cb as Se,b as q,cc as Ie,ak as Pe,cd as Ae,aF as ie,bT as Re,ce as Ce,J as We,O as pe,m as Ee,aj as Oe,a6 as _e,bN as Le,cf as be,bV as ke,cg as ye,aO as ve,ch as De,ci as Ue,cj as Ne,ck as ae,an as Fe,ao as xe,cl as Be,cm as Me,ad as Ge,aR as He,cn as qe,co as Ve,a7 as $e,aX as we,aa as je,b0 as Ye,ax as ze,P as Je,A as Ke}from"./index-99e0aeb1.js";import{T as re}from"./Textarea-88ad0a75.js";import{S as Xe}from"./Switch-4e23059b.js";const x={account:{username:"lb",name:"<PERSON><PERSON>",avatar:"https://docs.lbscripts.com/images/icons/icon.png",bio:`Official LB account
https://lbphone.com`,verified:!0,date_joined:162e10},accounts:{lb:{username:"lb",name:"LB",avatar:"https://docs.lbscripts.com/images/icons/icon.png",bio:`Official LB account
https://lbphone.com`,verified:!0,date_joined:162e10,followers:13,following:2},breze:{username:"breze",name:"Breze",bio:"Developer @lb",verified:!1,date_joined:162e10,followers:0,following:0},loaf:{username:"loaf",name:"Loaf Scripts",bio:"Developer @lb",verified:!1,date_joined:162e10,followers:0,following:0}},tweets:[{user:{username:"lb",name:"LB",avatar:"https://docs.lbscripts.com/images/icons/icon.png",verified:!0},tweet:{id:"2",content:"LB Phone 2.0 is now released! Check it out at https://lbscripts.com",attachments:[],date_created:Date.now()-1e3*60*60*2,retweets:0,replies:1,likes:4,liked:!1,retweeted:!1,_replies:[{user:{username:"breze",name:"Breze"},tweet:{id:"4",content:"finally, been waiting for this for ages!",attachments:[],date_created:Date.now()-1e3*60*60*1,retweets:0,replies:0,likes:0,liked:!1,retweeted:!1}}]}},{user:{username:"breze",name:"Breze"},tweet:{id:"3",content:"wow, the birdy app really is cool",attachments:[],date_created:Date.now()-1e3*60*60*3,retweets:0,replies:0,likes:1,liked:!0,retweeted:!1}},{user:{username:"lb",name:"LB",avatar:"https://docs.lbscripts.com/images/icons/icon.png",verified:!0},tweet:{id:"1",content:"Hello, world!",attachments:[],date_created:Date.now()-1e3*60*60*5,retweets:0,replies:0,likes:0,liked:!1,retweeted:!1}}],notifications:[{type:"reply",username:"breze",name:"Breze",tweet_id:"2",content:"finally, been waiting for this for ages!",retweet_count:0,reply_count:0,like_count:0,timestamp:Date.now()-1e3*60*60*2},{type:"like",username:"loaf",name:"Loaf Scripts",tweet_id:"2",timestamp:Date.now()-1e3*60*60*2},{type:"follow",username:"loaf",name:"Loaf Scripts",isFollowing:!0,timestamp:Date.now()-1e3*60*60*24*3},{type:"follow",username:"breze",name:"Breze",isFollowing:!0,timestamp:Date.now()-1e3*60*60*24*3}],messages:[{username:"breze",name:"Breze",content:"great job on the new update, really proud of you!",timestamp:Date.now()-1e3*60*60*4,messages:[{sender:"breze",name:"Breze",content:"great job on the new update, really proud of you!",attachments:[],timestamp:Date.now()-1e3*60*60*4}]},{username:"loaf",name:"Loaf Scripts",content:"thanks for responding, haha!",timestamp:Date.now()-1e3*60*60*24,messages:[{sender:"loaf",name:"Loaf Scripts",content:"thanks for responding, give me 5 mins..",attachments:[],timestamp:Date.now()-1e3*60*60*24},{sender:"lb",name:"LB",content:"no problem, happy to help!",attachments:[],timestamp:Date.now()-1e3*60*60*24+1e3},{sender:"loaf",name:"Loaf Scripts",content:"Hey LB, I have a question about the new update, can you help me?",attachments:[],timestamp:Date.now()-1e3*60*60*24+2e3}]}]};function te(n){const{Func:o,User:v}=i.useContext(G),[r,m]=v;o.Profile;const[t,u]=o.LoggedIn,[f,E]=o.View,[R,c]=o.Timeline,b=()=>{N("AccountSwitcher",{action:"getAccounts",app:"Twitter"},null).then(g=>{var S;if(!g)return F("info","No accounts found");let T=r.isAdmin,l=(S=g==null?void 0:g.filter(A=>A!==r.username))==null?void 0:S.map(A=>({title:A,cb:()=>{N("AccountSwitcher",{action:"switch",app:"Twitter",account:A}).then(()=>{N("Twitter",{action:"isLoggedIn"}).then(_=>{_?(V.APPS.TWITTER.account.set(_),m({..._,isAdmin:T}),E("timeline")):V.APPS.TWITTER.account.set(!1)})})}}));D.ContextMenu.set({buttons:[{title:r.username},...l,{title:s("APPS.TWITTER.ADD_ACCOUNT"),cb:()=>{u(!1)}}]})})},h=ge(()=>b(),{delay:250});return a("div",{className:"twitter-header",children:[a("div",{className:"twitter-header-top",children:[e("div",{...h,onClick:()=>o.fetchAndSetProfile(r.username),children:e(M,{className:"profile-picture",avatar:r.profile_picture})}),n.search?e("div",{className:"search-container",children:e($,{type:"text",placeholder:n.placeholder??s("APPS.TWITTER.SEARCH_TWITTER"),onChange:g=>n.search(g.target.value)})}):n.notifications?e("div",{className:"title",children:s("APPS.TWITTER.NOTIFICATIONS")}):e("div",{className:"logo",children:e("img",{src:"assets/img/icons/twitter/logo.png"})}),e("div",{})]}),f=="timeline"&&e("div",{className:"twitter-header-bottom",children:a("div",{className:"options",children:[e("div",{className:`option ${R==="default"?"active":""}`,onClick:()=>c("default"),children:s("APPS.TWITTER.ALL")}),e("div",{className:`option ${R==="following"?"active":""}`,onClick:()=>c("following"),children:s("APPS.TWITTER.FOLLOWING")})]})})]})}function Qe(n){const{Func:o,User:v}=i.useContext(G),r=Q(q),[m]=v,t=n.data,[u,f]=i.useState([]),E=i.useRef(null),R=i.useRef(0),[c,b]=i.useState({content:"",attachments:[]});i.useEffect(()=>{var T,l;j("Twitter")&&(f([]),N("Twitter",{action:"getMessages",data:{username:t.username,page:0}},(l=(T=x==null?void 0:x.messages)==null?void 0:T.find(S=>S.username===t.username))==null?void 0:l.messages).then(S=>{S&&f(S.reverse())}))},[]);const{handleScroll:h}=J({fetchData:T=>N("Twitter",{action:"getMessages",data:{username:t.username,page:T}}),onDataFetched:T=>{let l=document.querySelector(".dm-message-container");R.current=l.scrollHeight,f([...T.reverse(),...u])},isReversed:!0,perPage:25});i.useEffect(()=>{let T=document.querySelector(".dm-message-container");const l=T.scrollHeight;T.scrollTop+=l-R.current,T.scroll},[u]);const g=()=>{if(c.content.length<=0&&c.attachments.length<=0)return;const T={sender:m.username,recipient:t.username,content:c.content,attachments:c.attachments,timestamp:new Date().getTime()};N("Twitter",{action:"sendMessage",data:T},!0).then(l=>{if(!l)return F("error","Failed to send message");E.current.value="",b({content:"",attachments:[]}),f([...u,T])})};return ee("twitter:newMessage",T=>{t.username===T.sender&&f([...u,T])}),a("div",{className:"slide left",children:[a("div",{className:"dm-header",children:[e("i",{className:"fal fa-arrow-left",onClick:()=>n.goBack()}),a("div",{className:"user",onClick:()=>o.fetchAndSetProfile(t.username),children:[e("div",{className:"profile-picture",children:e(M,{avatar:t.profile_picture})}),a("div",{className:"name",children:[t.name,t.verified&&e("i",{className:"fas fa-badge-check verified"})]})]}),e("span",{})]}),e("div",{className:"dm-message-container",onScroll:h,children:e("div",{className:"message-body",children:u.map((T,l)=>{var O;let S,A=T.sender===m.username?"self":"other",_=((O=u[l+1])==null?void 0:O.sender)===m.username?"self":"other";if(u[l+1]?S=Math.abs(T.timestamp-u[l+1].timestamp)/36e5:_=void 0,T.content.includes(".gif")){let d=T.content,W=d.split(" ").find(P=>P.includes(".gif"));T.content=d.replace(W,""),T.attachments.push(W)}return a("div",{className:ne("message",A),children:[A=="other"?a("div",{className:"tweet-with-pfp",children:[e("div",{className:`profile-picture ${A!==_?"show":"hide"}`,children:e(M,{avatar:t.profile_picture})}),a("div",{className:"tweet-content",children:[T.content&&e("div",{className:"content",children:Z(T.content,!0)}),T.attachments.length>0&&e("div",{className:"attatchments",children:T.attachments.map((d,W)=>K(d)?e("video",{src:d,controls:!1,loop:!0,autoPlay:!0,muted:!0,onClick:()=>{D.FullscreenImage.set(d)}},W):e(z,{src:d,blur:!0,onClick:()=>{D.FullscreenImage.set(d)}},W))})]})]}):a(L,{children:[T.content&&e("div",{className:"content",children:Z(T.content)}),T.attachments.length>0&&e("div",{className:"attatchments",children:T.attachments.map((d,W)=>K(d)?e("video",{src:d,controls:!1,loop:!0,autoPlay:!0,muted:!0,onClick:()=>d},W):e(z,{src:d,onClick:()=>{D.FullscreenImage.set(d)}},W))})]}),u[l+1]&&S>6?e("div",{className:"date",children:fe(T.timestamp)}):A!==_&&e("div",{className:"date",children:fe(T.timestamp)})]},l)})})}),e("div",{className:"attachments",children:c.attachments.map((T,l)=>a("div",{className:"attachment",children:[K(T)?e("video",{src:T,muted:!0,controls:!1,loop:!0,autoPlay:!0}):e(z,{src:T}),e("i",{className:"fal fa-times",onClick:()=>{b({...c,attachments:c.attachments.filter((S,A)=>A!==l)})}})]}))}),a("div",{className:"message-bottom",children:[e(Se,{onClick:()=>{var T,l,S;c.attachments.length<2&&D.Gallery.set({includeVideos:!0,allowExternal:(S=(l=(T=q)==null?void 0:T.value)==null?void 0:l.AllowExternal)==null?void 0:S.Birdy,onSelect:A=>b({...c,attachments:[...c.attachments,A.src]})})}}),r.EnableGIFs!==!1&&e("div",{className:"gif",onClick:()=>{D.Gif.set({onSelect:T=>b({...c,content:c.content.length>0?c.content+" "+T:T})})},children:"GIF"}),e(re,{placeholder:s("APPS.TWITTER.MESSAGE_PLACEHOLDER"),rows:3,value:c.content,onChange:T=>b({...c,content:T.target.value}),onKeyDown:T=>{T.key==="Enter"&&g()},ref:E}),e(Ie,{className:"send",onClick:g})]})]})}function Ze(){const{Func:n,User:o}=i.useContext(G),[v]=o,[r,m]=i.useState(""),[t,u]=i.useState(""),[f,E]=i.useState([]),[R,c]=i.useState([]),[b,h]=i.useState(!1),[g,T]=n.DmUser;return i.useEffect(()=>{j("Twitter")&&N("Twitter",{action:"getRecentMessages",page:0},x.messages).then(l=>{if(!l)return F("error","Failed to fetch recent messages");c(l.filter(S=>S.username!==v.username))})},[]),i.useEffect(()=>{const l=setTimeout(()=>m(t),500);return()=>clearTimeout(l)},[t]),i.useEffect(()=>{r.length>0?b?N("Twitter",{action:"searchAccounts",query:r},Object.keys(x.accounts).filter(l=>l==null?void 0:l.toLowerCase().includes(r==null?void 0:r.toLowerCase()))).then(l=>{l&&E(l.filter(S=>S.username!==v.username))}):E(R.filter(l=>(l.username.includes(r)||l.name.includes(r))&&l.username!==v.username)):E([])},[r]),i.useEffect(()=>{m(""),E([])},[b]),e(L,{children:g&&g.username?e(Qe,{data:g,goBack:()=>T(null)}):a(L,{children:[e(te,{search:l=>u(l),placeholder:s("APPS.TWITTER.SEARCH_DIRECT_MESSAGES")}),e("div",{className:"dms-container",children:r.length>0?e("div",{className:"dm-users",children:f.length>0?f.map(l=>a("div",{className:"item",onClick:()=>T(l),children:[a("div",{className:"data",children:[e("div",{className:"profile-picture",children:e(M,{avatar:l.profile_picture})}),e("div",{className:"content",children:a("div",{className:"name",children:[e("span",{children:l.name}),a("span",{className:"username",children:["@",l.username]})]})})]}),e("div",{className:"timestamp",children:Pe(l.timestamp)})]})):e("div",{className:"search-for",children:s("APPS.TWITTER.SEARCH_FOR").format({search:r})})}):e("div",{className:"dm-users",children:R.map(l=>(l.content&&(l.content.includes("imgur.com")||l.content.includes("tenor.com")||l.content.includes(".png"))&&(l.content=s("APPS.TWITTER.SENT_ATTACHMENT")),a("div",{className:"item",onClick:()=>T(l),children:[a("div",{className:"data",children:[e("div",{className:"profile-picture",onClick:S=>{S.stopPropagation(),n.fetchAndSetProfile(l.username)},children:e(M,{avatar:l.profile_picture})}),a("div",{className:"content",children:[a("div",{className:"name",children:[e("span",{children:l.name}),a("span",{className:"username",children:["@",l.username]})]}),l.content&&e("div",{className:"text",children:l.content.length>30?l.content.substring(0,30)+"...":l.content})]})]}),e("div",{className:"timestamp",children:Pe(l.timestamp)})]})))})}),e("div",{className:"new-tweet",onClick:()=>h(!0),children:e(Ae,{})}),b&&a("div",{className:"new-message-container",children:[a("div",{className:"new-message-header",children:[e("div",{className:"cancel-text",onClick:()=>h(!1),children:s("APPS.TWITTER.CANCEL")}),e("div",{className:"title",children:s("APPS.TWITTER.NEW_MESSAGE")}),e("div",{})]}),a("div",{className:"new-message-body",children:[a("div",{className:"new-message-input",children:[s("APPS.TWITTER.TO"),":",e($,{type:"text",maxLength:10,onChange:l=>m(l.target.value)})]}),e("div",{className:"results",children:f.map(l=>a("div",{className:"item",onClick:()=>T(l),children:[e("div",{className:"profile-picture",children:e(M,{avatar:l.profile_picture})}),a("div",{className:"name",children:[a("span",{children:[l.name,l.verified&&e("i",{className:"fas fa-badge-check verified"})]}),a("span",{className:"username",children:["@",l.username]})]})]}))})]})]})]})})}function et(){const{User:n,Func:o}=i.useContext(G),[v,r]=n,[m,t]=o.LoggedIn,[u,f]=i.useState(""),[E,R]=i.useState(1),[c,b]=i.useState(""),[h,g]=i.useState(""),[T,l]=i.useState(""),[S,A]=i.useState(null);i.useEffect(()=>{A(null)},[u,E]);const _=()=>{N("Twitter",{action:"login",data:{username:h,password:T}},{success:!!x.accounts[h],data:x.accounts[h]}).then(d=>{if(!(d!=null&&d.success))return A(d==null?void 0:d.error);N("isAdmin",null,!1).then(W=>{V.APPS.TWITTER.account.set(d.data),r({...d.data,isAdmin:W}),t(!0)})})},O=()=>{let d=h==null?void 0:h.toLowerCase().replace(/\s/g,""),W=d.match(q.value.UsernameFilter);if(!W||(W==null?void 0:W[0])!==d){F("warning","Username did not match regex"),A("USERNAME_NOT_ALLOWED");return}N("Twitter",{action:"createAccount",data:{username:d.replace(/\s/g,""),password:T,name:c}},{success:!x.accounts[d]}).then(P=>{if(!(P!=null&&P.success))return A(P==null?void 0:P.error);let w={name:c,username:d,followers:0,following:0,date_joined:Math.floor(Date.now())};N("isAdmin",null,!1).then(U=>{V.APPS.TWITTER.account.set(w),r({...w,isAdmin:U}),t(!0)})})};return i.useEffect(()=>{g(""),l("")},[u]),a("div",{className:"login-container",children:[u===""&&a(L,{children:[a("div",{className:"login-header",children:[e("div",{}),e("img",{src:"assets/img/icons/twitter/logo.png"}),e("div",{})]}),e("div",{className:"login-body",children:s("APPS.TWITTER.SIGN_UP_TEXT")}),a("div",{className:"create-account",children:[e("div",{className:"button",onClick:()=>f("register"),children:s("APPS.TWITTER.CREATE_ACCOUNT")}),e("div",{className:"text",children:s("APPS.TWITTER.TERMS_OF_SERVICE")})]}),a("div",{className:"login-text",children:[s("APPS.TWITTER.HAS_ACCOUNT"),e("span",{onClick:()=>f("login"),children:s("APPS.TWITTER.LOGIN")})]})]}),u==="register"&&a(L,{children:[a("div",{className:"login-header",children:[e("div",{className:"cancel",onClick:()=>{f(""),R(1)},children:s("APPS.TWITTER.CANCEL")}),e("div",{className:"profile-picture",children:e("img",{src:"assets/img/icons/twitter/logo.png"})}),e("div",{})]}),a("div",{className:"login",children:[e("div",{className:"text",children:s("APPS.TWITTER.CREATE_YOUR_ACCOUNT")}),e($,{placeholder:s("APPS.TWITTER.NAME_PLACEHOLDER"),maxLength:30,onChange:d=>b(d.target.value)}),e($,{className:"username",placeholder:s("APPS.TWITTER.USERNAME_PLACEHOLDER"),maxLength:15,onChange:d=>{var W;return g((W=d.target.value)==null?void 0:W.toLowerCase().replace(/\s/g,""))}}),e($,{type:"password",placeholder:s("APPS.TWITTER.PASSWORD_PLACEHOLDER"),maxLength:50,onChange:d=>l(d.target.value)}),S&&e("div",{className:"error-text",children:s(`APPS.TWITTER.${S}`)})]}),e("div",{className:"buttons",children:e("div",{className:`button ${T.length>0&&(h==null?void 0:h.length)>2?"active":""}`,onClick:()=>{T.length>0&&(h==null?void 0:h.length)>2&&O()},children:s("APPS.TWITTER.CREATE_ACCOUNT")})})]}),u==="login"&&a(L,{children:[a("div",{className:"login-header",children:[e("div",{className:"cancel",onClick:()=>{f(""),R(1)},children:s("APPS.TWITTER.CANCEL")}),e("div",{className:"profile-picture",children:e("img",{src:"assets/img/icons/twitter/logo.png"})}),e("div",{})]}),E===1&&a(L,{children:[a("div",{className:"login",children:[e("div",{className:"text",children:s("APPS.TWITTER.GET_STARTED")}),e($,{placeholder:s("APPS.TWITTER.USERNAME_PLACEHOLDER"),maxLength:15,onChange:d=>g(d.target.value)})]}),a("div",{className:"footer",children:[e("div",{className:"forgot",children:s("APPS.TWITTER.FORGOT_PASSWORD")}),e("div",{className:`button ${h.length>2?"active":""}`,onClick:()=>{h.length>2&&R(E+1)},children:s("APPS.TWITTER.NEXT")})]})]}),E===2&&a(L,{children:[a("div",{className:"login",children:[e("div",{className:"text",children:s("APPS.TWITTER.ENTER_PASSWORD")}),e($,{placeholder:s("APPS.TWITTER.USERNAME_PLACEHOLDER"),defaultValue:h,maxLength:15,type:"text",onChange:d=>g(d.target.value)}),e($,{placeholder:s("APPS.TWITTER.PASSWORD_PLACEHOLDER"),type:"password",onChange:d=>l(d.target.value)}),S&&e("div",{className:"error-text",children:s(`APPS.TWITTER.${S}`)})]}),e("div",{className:"buttons",children:e("div",{className:ne("button",T&&(h==null?void 0:h.length)>2&&"active"),onClick:()=>{T&&(h==null?void 0:h.length)>2&&_()},children:s("APPS.TWITTER.LOGIN")})})]})]})]})}function X({data:n,last:o,pinned:v}){const{User:r,Func:m}=i.useContext(G),t=Q(q),[u]=r,[f,E]=m.Tweet,[R,c]=m.View,[b,h]=m.Update;i.useState(!1);const g=i.useRef([]),[T,l]=i.useState(null),[S,A]=i.useState([]),[_,O]=i.useState({retweeted:!1,liked:!1}),[d,W]=i.useState({likes:0,retweets:0,replies:0});return i.useEffect(()=>{var P;if((P=n==null?void 0:n.tweet)!=null&&P.attachments)if(Array.isArray(n.tweet.attachments))A(n.tweet.attachments);else{const w=JSON.parse(n.tweet.attachments);w&&Array.isArray(w)&&A(JSON.parse(n.tweet.attachments))}O({retweeted:n.tweet.retweeted,liked:n.tweet.liked}),W({likes:n.tweet.likes,retweets:n.tweet.retweets,replies:n.tweet.replies})},[n]),i.useEffect(()=>{if(!j("Twitter"))return;if(!S||!g.current||g.current.length===0)return F("info","No attachments or video refs");const P=g.current.map(w=>{if(!w)return null;const U=new IntersectionObserver(p=>{p.forEach(C=>{C.isIntersecting?C.target.play().catch(k=>{F("error","Failed to play video",k)}):C.target.pause()})},{threshold:.5});return U.observe(w),U}).filter(Boolean);return()=>{P.forEach(w=>w.disconnect())}},[S]),ee("twitter:updateTweetData",P=>{P.tweetId===n.tweet.id&&W(w=>({...w,[P.data]:P.increment?w[P.data]+1:w[P.data]-1}))}),i.useEffect(()=>{if(n.tweet.date_created){let w=new Date(n.tweet.date_created),U=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],p=w.getDate(),C=U[w.getMonth()],k=w.getFullYear();var P=Math.floor(new Date().getTime()-w.getTime())/1e3;P<0&&(P=1),k!==new Date().getFullYear()&&l(`${C} ${p}, ${k}`);let y=P/86400;return y>1?l(`${C} ${p}`):(y=P/3600,y>1?l(`${Math.floor(y)}h`):(y=P/60,y>1?l(`${Math.floor(y)}m`):l(`${Math.floor(y*60)}s`)))}},[n]),e(L,{children:n&&e(L,{children:a("div",{className:"tweet",id:o?"last":"",onClick:()=>{E(n),c("tweet")},children:[v&&a("div",{className:"pinned",children:[e("i",{className:"fas fa-thumbtack"})," ",s("APPS.TWITTER.PINNED_POST")]}),n.tweet.retweetedByUsername&&a("div",{className:"retweeted",onClick:P=>{P.stopPropagation(),m.fetchAndSetProfile(n.tweet.retweetedByUsername)},children:[e("i",{className:"far fa-retweet"}),n.tweet.retweetedByName," ",s("APPS.TWITTER.RETWEETED")]}),a("div",{className:"tweet-body",children:[e("div",{className:"profile-picture",children:e(M,{avatar:n.user.profile_picture,onClick:P=>{P.stopPropagation(),m.fetchAndSetProfile(n.user.username)}})}),a("div",{className:"tweet-content",children:[a("div",{className:"tweet-header",onClick:P=>{P.stopPropagation(),m.fetchAndSetProfile(n.user.username)},children:[a("div",{className:"user",children:[e("span",{className:"name",children:n.user.name}),n.user.private&&e(ie,{}),n.user.verified&&e("i",{className:"fas fa-badge-check verified"}),a("span",{className:"username",children:["@",n.user.username]}),e("span",{className:"time",children:T})]}),(u.username===n.user.username||u.isAdmin)&&e("div",{className:"dots",children:e(Re,{onClick:P=>{var U;P.stopPropagation();let w=[u.username===n.user.username&&{title:v?s("APPS.TWITTER.UNPIN"):s("APPS.TWITTER.PIN"),cb:()=>{N("Twitter",{action:"pinTweet",tweet_id:n.tweet.id,toggle:!v},!0).then(p=>{if(!p)return F("error","Failed to pin tweet");if(v)return v=!1})}},{title:s("APPS.TWITTER.DELETE_POPUP.TITLE"),color:"red",cb:()=>{D.PopUp.set({title:s("APPS.TWITTER.DELETE_POPUP.TITLE"),description:s("APPS.TWITTER.DELETE_POPUP.DESCRIPTION"),buttons:[{title:s("APPS.TWITTER.DELETE_POPUP.CANCEL")},{title:s("APPS.TWITTER.DELETE_POPUP.PROCEED"),color:"red",cb:()=>{N("Twitter",{action:"deleteTweet",tweet_id:n.tweet.id},!0).then(()=>{h(!0)})}}]})}}].filter(Boolean);(U=t==null?void 0:t.PromoteBirdy)!=null&&U.Enabled&&!n.tweet.replyToId&&n.user.username===u.username&&w.push({title:s("APPS.TWITTER.PROMOTE"),cb:()=>{var p,C,k;D.PopUp.set({title:s("APPS.TWITTER.PROMOTE_POPUP.TITLE"),description:s("APPS.TWITTER.PROMOTE_POPUP.DESCRIPTION").format({price:t.CurrencyFormat.replace("%s",(C=(p=q)==null?void 0:p.value.PromoteBirdy)==null?void 0:C.Cost.toString()),views:(k=q)==null?void 0:k.value.PromoteBirdy.Views}),buttons:[{title:s("APPS.TWITTER.PROMOTE_POPUP.CANCEL")},{title:s("APPS.TWITTER.PROMOTE_POPUP.PROCEED"),cb:()=>{N("Twitter",{action:"promoteTweet",tweet_id:n.tweet.id},!0).then(y=>{n.tweet.promoted=!0})}}]})}}),D.ContextMenu.set({buttons:w})}})})]}),n.tweet.replyToId&&e("div",{className:"replying-to",onClick:P=>{P.stopPropagation(),N("Twitter",{action:"getTweet",tweetId:n.tweet.replyToId},x.tweets.find(w=>w.tweet.id===n.tweet.replyToId)).then(w=>{w&&w.tweet&&(E(w),c("tweet"))})},children:s("APPS.TWITTER.REPLYING_TO").format({name:`@${n.tweet.replyToAuthor}`})}),e("div",{className:"text",children:Z(n.tweet.content,!0,(P,w)=>{w.stopPropagation(),m.fetchAndSetProfile(P)})}),(S==null?void 0:S.length)>0&&e("div",{className:"attatchments",children:S==null?void 0:S.map((P,w)=>{var U;return e("div",{className:"attatchment",onClick:p=>{p.stopPropagation(),D.FullscreenImage.set(P)},children:K(P)?e("video",{src:P,muted:!0,controls:!1,loop:!0,autoPlay:!0,ref:p=>{g.current&&(g.current[w]=p)}}):e(z,{src:P,blur:((U=n.user)==null?void 0:U.username)!==(u==null?void 0:u.username)})},w)})}),a("div",{className:"actions",children:[a("div",{className:"action comment",children:[e("i",{className:"far fa-comment"}),d.replies]}),a("div",{className:`action retweet ${_.retweeted?"active":""}`,children:[e("i",{className:"fal fa-retweet",onClick:P=>{P.stopPropagation(),D.PopUp.set({title:_.retweeted?s("APPS.TWITTER.RETWEET_POPUP.UNDO_RETWEET"):s("APPS.TWITTER.RETWEET_POPUP.RETWEET"),description:s("APPS.TWITTER.RETWEET_POPUP.DESCRIPTION").format({action:_.retweeted?s("APPS.TWITTER.RETWEET_POPUP.UNDO_RETWEET"):s("APPS.TWITTER.RETWEET_POPUP.RETWEET")}),buttons:[{title:s("APPS.TWITTER.RETWEET_POPUP.CANCEL")},{title:s("APPS.TWITTER.RETWEET_POPUP.PROCEED"),cb:()=>{N("Twitter",{action:"toggleRetweet",tweet_id:n.tweet.id,retweeted:!_.retweeted},!_.retweeted).then(w=>{O({..._,retweeted:w})})}}]})}}),d.retweets]}),a("div",{className:`action like ${_.liked?"active":""}`,children:[e("i",{className:"fal fa-heart",onClick:P=>{P.stopPropagation(),N("Twitter",{action:"toggleLike",tweet_id:n.tweet.id,liked:!_.liked},!_.liked).then(w=>{O({..._,liked:w})})}}),d.likes]})]}),n.tweet.promoted&&a("div",{className:"promoted",children:[e(Ce,{})," ",s("APPS.TWITTER.PROMOTED")]})]})]})]})})})}const se=We(!1);function tt(){const n=Q(se),[o,v]=i.useState(null);i.useEffect(()=>{j("Twitter")&&N("Twitter",{action:"getNotifications",page:0},{requests:[],notifications:x.notifications}).then(m=>{if(!m)return F("warning","Failed to fetch notifications");v(m)})},[]);const{handleScroll:r}=J({fetchData:m=>N("Twitter",{action:"getNotifications",page:m}),onDataFetched:m=>v(t=>({notifications:[...t.notifications,...m.notifications],requests:m.requests})),perPage:25});return a(L,{children:[e(te,{notifications:!0}),e(pe,{children:n&&e(st,{})}),a("div",{className:"notifications-container",onScroll:r,children:[o!=null&&o.requests&&(o==null?void 0:o.requests)>0?a("div",{className:"notification-item follow",onClick:()=>se.set(!0),children:[e("i",{className:"fas fa-user"}),e("div",{className:"content",children:a("div",{className:"title",children:[e("span",{children:o.requests})," ",s("APPS.TWITTER.FOLLOWER_REQUESTS")]})})]}):null,o==null?void 0:o.notifications.map((m,t)=>e(at,{notification:m}))]})]})}const at=({notification:n})=>{var v,r;const o=n.type;return a("div",{className:`notification-item ${o}`,children:[o==="like"&&a(L,{children:[e("img",{className:"icon",src:"./assets/img/icons/twitter/heart.svg"}),a("div",{className:"content",children:[e("div",{className:"profile-pictures",children:e(M,{avatar:n.profile_picture})}),a("div",{className:"title",children:[e("span",{children:n.name})," ",s("APPS.TWITTER.LIKED_TWEET")]}),n.content&&e("div",{className:"tweet-content",children:((v=n.content)==null?void 0:v.length)>100?n.content.substring(0,100)+"...":n.content})]})]}),o==="retweet"&&a(L,{children:[e("i",{className:"fas fa-repeat"}),a("div",{className:"content",children:[e("div",{className:"profile-pictures",children:e(M,{avatar:n.profile_picture})}),a("div",{className:"title",children:[e("span",{children:n.name})," ",s("APPS.TWITTER.RETWEETED_TWEET")]}),n.content&&e("div",{className:"tweet-content",children:((r=n.content)==null?void 0:r.length)>100?n.content.substring(0,100)+"...":n.content})]})]}),o==="follow"&&a(L,{children:[e("i",{className:"fas fa-user"}),a("div",{className:"content",children:[e("div",{className:"profile-pictures",children:e(M,{avatar:n.profile_picture})}),a("div",{className:"title",children:[e("span",{children:n.name})," ",s("APPS.TWITTER.FOLLOWED")]})]})]}),o==="reply"&&e(X,{data:{user:{profile_picture:n.profile_picture,name:n.name,username:n.username,verified:n.verified},tweet:{id:n.tweet_id,content:n.content,attachments:n.attachments,replies:n.reply_count,retweets:n.retweet_count,likes:n.like_count,liked:n.liked??!1,retweeted:n.retweeted??!1,date_created:n.timestamp,replyToId:n.replyToId,replyToAuthor:n.replyToAuthor}}})]})},st=()=>{const{Func:n}=i.useContext(G),[o,v]=i.useState([]);i.useEffect(()=>{j("Twitter")&&N("Twitter",{action:"getFollowRequests",page:0},null).then(t=>{if(!t)return F("warning","Failed to fetch follow requests");F("info","Fetched follow requests",t),v(t)})},[]);const{handleScroll:r}=J({fetchData:t=>N("Twitter",{action:"getFollowRequests",page:t}),onDataFetched:t=>v(t),perPage:25}),m=(t,u)=>{if(!t||u===void 0)return F("warning","No username / accept provided as an argument, cancelling request");N("Twitter",{action:"handleFollowRequest",username:t,accept:u}).then(f=>{if(!f)return F("warning","Failed to handle follow request");v(E=>E.filter(R=>R.username!==t))})};return e(L,{children:a(Ee.div,{initial:{opacity:.5,x:150},animate:{opacity:1,x:0},exit:{opacity:.5,x:150},transition:{duration:.2,ease:"easeInOut"},className:"userpanel-container requests",children:[a("div",{className:"userpanel-header",children:[e("div",{onClick:()=>se.set(!1),children:e("i",{className:"far fa-arrow-left"})}),e("div",{className:"userpanel-title",children:s("APPS.TWITTER.FOLLOWER_REQUESTS")}),e("div",{})]}),e("div",{className:"items",onScroll:r,children:o.map((t,u)=>a("div",{className:"item",children:[a("div",{className:"item-header",children:[a("div",{className:"user",onClick:()=>n.fetchAndSetProfile(t.username),children:[e("div",{className:"profile-picture",children:e(M,{avatar:t.profile_picture})}),a("div",{className:"name",children:[e("span",{children:t.name}),a("span",{className:"username",children:["@",t.username]})]}),t.isFollowingYou&&e("div",{className:"follows",children:s("APPS.TWITTER.FOLLOWS_YOU")})]}),a("div",{className:"actions",children:[e(Oe,{className:"primary",onClick:f=>{f.stopPropagation(),m(t.username,!0)}}),e(_e,{className:"red",onClick:f=>{f.stopPropagation(),m(t.username,!1)}})]})]}),e("div",{className:"item-footer",children:t.bio})]},u))})]})})};function nt(){const{Func:n}=i.useContext(G),[o,v]=n.View;return e("div",{className:"footer",children:[{value:"timeline",activeIcon:e(Le,{}),icon:e(be,{})},{value:"search",activeIcon:e(ke,{}),icon:e(ye,{})},{value:"notifications",activeIcon:e(ve,{}),icon:e(De,{})},{value:"dmlist",activeIcon:e(Ue,{}),icon:e(Ne,{})}].map((m,t)=>{let u=o==m.value;return e("div",{"data-active":u,onClick:()=>v(m.value),children:u?m.activeIcon:m.icon},t)})})}function it(){var R;const{User:n,Func:o,UserPanel:v}=i.useContext(G),[r,m]=v,[t,u]=i.useState([]);let f={[s("APPS.TWITTER.FOLLOWING")]:"getFollowing",[s("APPS.TWITTER.FOLLOWERS")]:"getFollowers",[s("APPS.TWITTER.LIKED_BY")]:"getLikes",[s("APPS.TWITTER.RETWEETED_BY")]:"getRetweeters"};i.useEffect(()=>{j("Twitter")&&N("Twitter",{action:f[r.data.title],data:{tweet_id:r.data.tweet_id,username:r.data.username,page:0}}).then(c=>{u(c)})},[]);const{handleScroll:E}=J({fetchData:c=>N("Twitter",{action:f[r.data.title],data:{tweet_id:r.data.tweet_id,username:r.data.username,page:c}}),onDataFetched:c=>u([...t,...c]),perPage:25});return e(L,{children:a("div",{className:"userpanel-container",children:[a("div",{className:"userpanel-header",children:[e("div",{onClick:()=>{var c;(c=r.data)==null||c.cb(),m({display:!1,data:null})},children:e("i",{className:"far fa-arrow-left"})}),e("div",{className:"userpanel-title",children:(R=r==null?void 0:r.data)==null?void 0:R.title}),e("div",{})]}),e("div",{className:"items",onScroll:E,children:t.map((c,b)=>e(rt,{account:c}))})]})})}const rt=n=>{const{User:o,Func:v}=i.useContext(G),[r]=o,m=n.account,[t,u]=i.useState(m.isFollowing);return e("div",{className:"item",children:a(L,{children:[a("div",{className:"item-header",children:[a("div",{className:"user",onClick:()=>v.fetchAndSetProfile(m.username),children:[e("div",{className:"profile-picture",children:e(M,{avatar:m.profile_picture})}),a("div",{className:"name",children:[e("span",{children:m.name}),a("span",{className:"username",children:["@",m.username]})]}),m.isFollowingYou&&e("div",{className:"follows",children:s("APPS.TWITTER.FOLLOWS_YOU")})]}),e("div",{className:"action",onClick:()=>{m.username!==r.username&&(t?D.PopUp.set({title:s("APPS.TWITTER.UNFOLLOW_POPUP.TITLE"),description:s("APPS.TWITTER.UNFOLLOW_POPUP.DESCRIPTION").format({name:m.username}),buttons:[{title:s("APPS.TWITTER.UNFOLLOW_POPUP.CANCEL")},{title:s("APPS.TWITTER.UNFOLLOW_POPUP.PROCEED"),color:"red",cb:()=>{N("Twitter",{action:"toggleFollow",data:{username:m.username,following:!t}},!0).then(()=>{u(f=>!f)})}}]}):N("Twitter",{action:"toggleFollow",data:{username:m.username,following:!t}},!0).then(f=>{u(E=>!E)}))},children:m.username===r.username?e("div",{className:"following",children:s("APPS.TWITTER.YOU")}):t?e("div",{className:"following",children:s("APPS.TWITTER.FOLLOWING")}):e("div",{className:"follow",children:s("APPS.TWITTER.FOLLOW")})})]}),e("div",{className:"item-footer",children:m.bio})]})})};function lt(){var oe,de,ue,me,Te,he;const{User:n,Func:o,UserPanel:v}=i.useContext(G),[r,m]=n,[t,u]=o.Profile,[f,E]=v,[R,c]=o.Update,[b,h]=o.LoggedIn,[g,T]=o.DmUser,[l,S]=o.View,[A,_]=i.useState("tweets"),[O,d]=i.useState([]),[W,P]=i.useState(null),[w,U]=i.useState(null),[p,C]=i.useState(!1);i.useEffect(()=>{j("Twitter")&&(d([]),c(!1),N("Twitter",{action:"getTweets",page:0,filter:{type:A==="tweets"?"user":A,username:t.username}},x.tweets.filter(I=>{var B,H;switch(A){case"tweets":return I.user.username===t.username;case"replies":return(H=(B=I.tweet)==null?void 0:B._replies)==null?void 0:H.find(Y=>Y.tweet.username===t.username);case"media":return I.tweet.attachments.length>0&&I.user.username===t.username;case"liked":return[];default:return[]}})).then(I=>{d(I)}))},[A,R]);const{handleScroll:k}=J({fetchData:I=>N("Twitter",{action:"getTweets",filter:{type:A==="tweets"?"user":A,username:t.username},page:I}),onDataFetched:I=>d([...O,...I]),perPage:25});i.useEffect(()=>{var I;if(t.date_joined){const B=new Date(t.date_joined);let H=B.toLocaleDateString((I=q.value)==null?void 0:I.DateLocale,{month:"long"}),Y=B.getFullYear();P(`${H} ${Y}`),U(t.profile_picture),r.username===t.username&&m({...r,...t})}},[t]);const y=i.useRef(!0);i.useEffect(()=>{if(y.current){y.current=!1;return}N("Twitter",{action:"toggleFollow",data:{username:t.username,following:t.isFollowing}})},[t.isFollowing]),ee("twitter:updateProfileData",I=>{I.username===t.username&&u({...t,[I.data]:I.increment?t[I.data]+1:t[I.data]-1})});const ce={tweets:s("APPS.TWITTER.TWEETS"),replies:s("APPS.TWITTER.REPLIES"),media:s("APPS.TWITTER.MEDIA"),liked:s("APPS.TWITTER.LIKES")};return a(L,{children:[p&&a("div",{className:"edit-profile-container",children:[a("div",{className:"edit-profile-header",children:[e("div",{className:"cancel",onClick:()=>C(!1),children:s("APPS.TWITTER.CANCEL")}),e("div",{className:"title",children:s("APPS.TWITTER.EDIT_PROFILE")}),e("div",{className:"save",onClick:()=>{N("Twitter",{action:"updateProfile",data:{name:t.name,bio:t.bio,profile_picture:t.profile_picture,header:t.header,private:t.private}},!0).then(I=>{if(!I)return F("warning","Could not save profile, updateProfile did not callback");C(!1),V.APPS.TWITTER.account.set(t)})},children:s("APPS.TWITTER.SAVE")})]}),e("div",{className:"banner-wrapper",children:t.header?a(L,{children:[e(z,{className:"profile-banner",src:t.header,blur:t.username!==r.username,onClick:()=>{var I,B,H;D.Gallery.set({allowExternal:(H=(B=(I=q)==null?void 0:I.value)==null?void 0:B.AllowExternal)==null?void 0:H.Birdy,onSelect:Y=>u({...t,header:Y.src})})}}),e(ae,{})]}):e("div",{className:"profile-banner",onClick:()=>{var I,B,H;D.Gallery.set({allowExternal:(H=(B=(I=q)==null?void 0:I.value)==null?void 0:B.AllowExternal)==null?void 0:H.Birdy,onSelect:Y=>u({...t,header:Y.src})})},children:e(ae,{})})}),a("div",{className:"profile-info",children:[a("div",{className:"logo",onClick:()=>{var I,B,H;D.Gallery.set({allowExternal:(H=(B=(I=q)==null?void 0:I.value)==null?void 0:B.AllowExternal)==null?void 0:H.Birdy,onSelect:Y=>u({...t,profile_picture:Y.src})})},children:[e(M,{avatar:t.profile_picture}),e(ae,{})]}),a("div",{className:"items",children:[a("div",{className:"item",children:[s("APPS.TWITTER.NAME"),e($,{onChange:I=>u({...t,name:I.target.value}),defaultValue:t.name})]}),a("div",{className:"item",children:[s("APPS.TWITTER.BIO"),e(re,{onChange:I=>u({...t,bio:I.target.value}),rows:2,maxLength:100,defaultValue:t.bio})]}),a("div",{className:"item",children:[s("APPS.TWITTER.JOIN_DATE"),e($,{disabled:!0,value:W})]}),a("div",{className:"item",children:[s("APPS.TWITTER.PROTECTED_POSTS"),e(Xe,{checked:t.private,onChange:()=>u({...t,private:!t.private})})]})]}),a("div",{className:"buttons",children:[e("div",{className:"button",onClick:()=>{D.PopUp.set({title:s("APPS.TWITTER.SIGN_OUT_POPUP.TITLE"),description:s("APPS.TWITTER.SIGN_OUT_POPUP.DESCRIPTION"),buttons:[{title:s("APPS.TWITTER.SIGN_OUT_POPUP.CANCEL")},{title:s("APPS.TWITTER.SIGN_OUT_POPUP.PROCEED"),color:"red",cb:()=>{N("Twitter",{action:"signOut"},!0).then(()=>{h(!1),V.APPS.TWITTER.account.set(!1)})}}]})},children:e("div",{children:s("APPS.TWITTER.SIGN_OUT")})}),((ue=(de=(oe=q)==null?void 0:oe.value)==null?void 0:de.ChangePassword)==null?void 0:ue.Birdy)&&e("div",{className:"button red",onClick:()=>Fe("Twitter",()=>{}),children:e("div",{children:s("APPS.TWITTER.CHANGE_PASSWORD")})}),((he=(Te=(me=q)==null?void 0:me.value)==null?void 0:Te.DeleteAccount)==null?void 0:he.Birdy)&&e("div",{className:"button red",onClick:()=>{xe("Twitter",()=>{h(null),m(null),V.APPS.TWITTER.account.set(null)})},children:e("div",{children:s("APPS.TWITTER.DELETE_ACCOUNT")})})]})]})]}),a("div",{className:"profile",children:[a("div",{className:"profile-header",children:[t.header?e(z,{src:t.header}):e("div",{}),e("div",{className:"go-back",onClick:()=>S("timeline"),children:e(Be,{})})]}),a("div",{className:"profile-details",children:[a("div",{className:"top",children:[e("div",{className:"profile-picture",children:e(M,{avatar:w})}),e("div",{className:"action-buttons",children:a(L,{children:[t.username!==r.username&&a(L,{children:[e("div",{className:"button",onClick:()=>{T({username:t.username,name:t.name,profile_picture:w,verified:t.verified}),S("dmlist")},children:e(Ne,{})}),t.isFollowing&&e("div",{className:"button",onClick:()=>{var I;D.PopUp.set({title:s("APPS.TWITTER.NOTIFICATIONS_POPUP.TITLE"),description:s("APPS.TWITTER.NOTIFICATIONS_POPUP.DESCRIPTION").format({action:(I=t.notificationsEnabled?s("APPS.TWITTER.NOTIFICATIONS_POPUP.DISABLE"):s("APPS.TWITTER.NOTIFICATIONS_POPUP.ENABLE"))==null?void 0:I.toLowerCase(),name:t.username}),buttons:[{title:s("APPS.TWITTER.NOTIFICATIONS_POPUP.CANCEL")},{title:s("APPS.TWITTER.NOTIFICATIONS_POPUP.PROCEED"),color:t.notificationsEnabled?"red":null,cb:()=>{N("Twitter",{action:"toggleNotifications",data:{username:t.username,toggle:!t.notificationsEnabled}},!t.notificationsEnabled).then(B=>u({...t,notificationsEnabled:B}))}}]})},children:t.notificationsEnabled?e(ve,{}):e(Me,{})})]}),t.username===r.username?e("div",{className:"edit",onClick:()=>C(!0),children:s("APPS.TWITTER.EDIT_PROFILE")}):t.isFollowing?e("div",{className:"following",onClick:()=>{D.PopUp.set({title:s("APPS.TWITTER.UNFOLLOW_POPUP.TITLE"),description:s("APPS.TWITTER.UNFOLLOW_POPUP.DESCRIPTION").format({name:t.username}),buttons:[{title:s("APPS.TWITTER.UNFOLLOW_POPUP.CANCEL")},{title:s("APPS.TWITTER.UNFOLLOW_POPUP.PROCEED"),color:"red",cb:()=>{u({...t,isFollowing:!1,notificationsEnabled:!1})}}]})},children:s("APPS.TWITTER.FOLLOWING")}):e(L,{children:e("div",{className:"follow",onClick:()=>{t.private?N("Twitter",{action:"toggleFollow",data:{username:t.username,following:!0}},!0).then(I=>{I&&u({...t,requested:!t.requested})}):u({...t,isFollowing:!0})},children:t.requested?s("APPS.TWITTER.PENDING"):s("APPS.TWITTER.FOLLOW")})})]})})]}),a("div",{className:"profile-bottom",children:[a("div",{className:"name-area",children:[a("span",{className:"name",children:[t.name," ",t.verified&&e("i",{className:"fas fa-badge-check verified"}),t.private&&e(ie,{})]}),a("span",{className:"username",children:["@",t.username,t.isFollowingYou&&e("div",{className:"follows",children:s("APPS.TWITTER.FOLLOWS_YOU")})]})]}),e("div",{className:"bio",children:Z(t.bio,!0)}),a("div",{className:"joindate",children:[e("i",{className:"far fa-calendar-alt"}),s("APPS.TWITTER.JOINED").format({date:W})]}),a("div",{className:"stats",children:[a("div",{"data-disabled":!t.isFollowing&&t.private&&t.username!==r.username,onClick:()=>{!t.isFollowing&&t.private&&t.username!==r.username||E({display:!0,data:{title:s("APPS.TWITTER.FOLLOWING"),username:t.username,cb:()=>o.fetchAndSetProfile(t.username)}})},children:[e("span",{children:t.following})," ",s("APPS.TWITTER.FOLLOWING")]}),a("div",{"data-disabled":!t.isFollowing&&t.private&&t.username!==r.username,onClick:()=>{!t.isFollowing&&t.private&&t.username!==r.username||E({display:!0,data:{title:s("APPS.TWITTER.FOLLOWERS"),username:t.username,cb:()=>o.fetchAndSetProfile(t.username)}})},children:[e("span",{children:t.followers})," ",s("APPS.TWITTER.FOLLOWERS")]})]}),!(!t.isFollowing&&t.private&&t.username!==r.username)&&e("div",{className:"categories",children:Object.keys(ce).map(I=>e("div",{className:`category ${A===I?"active":""}`,onClick:()=>_(I),children:ce[I]},I))})]})]}),!t.isFollowing&&t.private&&t.username!==r.username?a("div",{className:"protected-tweets-container",children:[e("div",{className:"title",children:s("APPS.TWITTER.PROTECTED_TWEETS_TITLE")}),e("div",{className:"description",children:s("APPS.TWITTER.PROTECTED_TWEETS_DESCRIPTION").format({username:t.username})})]}):e("div",{className:"profile-tweets-container",children:a("div",{className:"tweets",onScroll:k,children:[t.pinnedTweet&&A==="tweets"&&e(X,{data:t.pinnedTweet,pinned:!0}),O.map((I,B)=>e(X,{data:I}))]})})]})]})}function ct(){const{Func:n}=i.useContext(G),[o,v]=i.useState(""),[r,m]=i.useState(""),[t,u]=i.useState([]),[f,E]=i.useState([]),[R,c]=i.useState("");i.useEffect(()=>{j("Twitter")&&N("Twitter",{action:"getRecentHashtags"}).then(h=>{h&&E(h)})},[]),i.useEffect(()=>{const h=setTimeout(()=>v(r),500);return()=>clearTimeout(h)},[r]),i.useEffect(()=>{o.length>0&&N("Twitter",{action:"searchAccounts",query:o},Object.keys(x.accounts).filter(h=>{var g;return(h==null?void 0:h.toLowerCase().includes(o==null?void 0:o.toLowerCase()))||((g=x.accounts[h].name)==null?void 0:g.toLowerCase().includes(o==null?void 0:o.toLowerCase()))}).map(h=>x.accounts[h])).then(h=>{h&&u(h)})},[o]);const{handleScroll:b}=J({fetchData:h=>N("Twitter",{action:"searchTweets",query:R,page:h}),onDataFetched:h=>u([...t,...h]),perPage:25});return a(L,{children:[e(te,{search:h=>m(h)}),e("div",{className:"searchpage-container",children:o.length>0||R.length>0?e(L,{children:e("div",{className:"search-results",onScroll:b,children:t.length>0?t.map(h=>{if(h.tweet)return e(X,{data:h});{let g=h;return a("div",{className:"item",onClick:()=>n.fetchAndSetProfile(g.username),children:[e("div",{className:"profile-picture",children:e(M,{avatar:g.profile_picture})}),a("div",{className:"name",children:[a("span",{children:[g.name,g.verified&&e("i",{className:"fas fa-badge-check verified"}),g.private&&e(ie,{})]}),a("span",{className:"username",children:["@",g.username]})]})]})}}):e("div",{className:"search-for",children:s("APPS.TWITTER.SEARCH_FOR").format({search:o})})})}):a(L,{children:[a("div",{className:"twitter-banner",children:[e("img",{src:"./assets/img/icons/twitter/banner.png"}),e("div",{className:"text",children:s("APPS.TWITTER.START_SEARCHING")})]}),f.length>0&&a("div",{className:"recent-hashtags",children:[e("div",{className:"title",children:s("APPS.TWITTER.TRENDING_HASHTAGS")}),e("div",{className:"hashtags",children:f.map(h=>a("div",{className:"item",children:[a("div",{className:"hashtag",onClick:()=>{N("Twitter",{action:"searchTweets",query:h.hashtag,page:0}).then(g=>{g&&(c(h.hashtag),u(g))})},children:["#",h.hashtag]}),a("div",{className:"uses",children:[Ge(h.uses,!0)," ",h.uses>1?"Tweets":"Tweet"]})]}))})]})]})})]})}function le(n){var t,u;const{User:o}=i.useContext(G),[v]=o,[r,m]=i.useState({content:"",attachments:(t=n.data)!=null&&t.attachments?(u=n.data)==null?void 0:u.attachments:[]});return a("div",{className:"new-tweet-container",children:[a("div",{className:"new-tweet-header",children:[e("div",{className:"cancel-text",onClick:()=>n.goBack(),children:s("APPS.TWITTER.CANCEL")}),e("div",{className:"tweet-button",onClick:()=>{if(r.content.length>0||r.attachments.length>0){let f=[...r.content.split(" ").filter(E=>E.startsWith("#")).filter((E,R,c)=>c.indexOf(E)===R).map(E=>E.replace("#",""))];N("Twitter",{action:"sendTweet",data:{username:v.username,content:r.content,attachments:r.attachments.length>0?r.attachments:null,replyTo:n.isReply,hashtags:f}},!0).then(E=>{if(!E)return F("error","Failed to send tweet!");n.onSend()})}},children:s("APPS.TWITTER.TWEET")})]}),a("div",{className:"message-container",children:[a("div",{className:"message-top",children:[v&&e("div",{className:"profile-picture",children:e(M,{avatar:v.profile_picture})}),e(re,{maxLength:280,id:"textArea",value:r.content,onChange:f=>{m({...r,content:f.target.value});let E=f.target.scrollHeight;f.target.style.height=E+"px"},placeholder:n.isReply?s("APPS.TWITTER.TWEET_REPLY"):s("APPS.TWITTER.TWEET_GENERAL")})]}),e("div",{className:"tweet-attatchments",children:r.attachments.map((f,E)=>a("div",{className:"tweet-attatchment",children:[K(f)?e("video",{src:f,muted:!0,controls:!1,loop:!0,autoPlay:!0}):e("img",{src:f}),e("i",{className:"fal fa-times",onClick:()=>{m({...r,attachments:r.attachments.filter((R,c)=>c!==E)})}})]},E))}),a("div",{className:"buttons",children:[e("div",{className:"button",onClick:()=>{var f,E,R;r.attachments.length<2&&D.Gallery.set({includeVideos:!0,allowExternal:(R=(E=(f=q)==null?void 0:f.value)==null?void 0:E.AllowExternal)==null?void 0:R.Birdy,onSelect:c=>m({...r,attachments:[...r.attachments,c.src]})})},children:e(He,{})}),e("div",{className:"button",onClick:()=>{D.Emoji.set({onSelect:f=>m(E=>({content:`${E.content}${f.emoji}`,attachments:E.attachments}))})},children:e(qe,{})})]})]})]})}function ot(){const{User:n,Func:o}=i.useContext(G),[v]=o.Timeline,[r]=n,[m,t]=o.Update,[u,f]=i.useState([]),[E,R]=i.useState(!1),[c,b]=i.useState(0),[h,g]=i.useState(!1),[T,l]=i.useState(!1),[S,A]=i.useState(!1);return ee("twitter:newTweet",O=>{var W,P,w;if(!O)return F("warning","No tweet data was provided for the twitter:newTweet event, returning.");if((W=O.tweet)!=null&&W.replyToId)return F("info","Tweet is reply, not adding to timeline");if(!((P=O.tweet)!=null&&P.content)||v==="following")return;f([O,...u]);let d=(w=document.querySelector(".tweets-container"))==null?void 0:w.scrollTop;A(d&&d>0)}),i.useEffect(()=>{if(!j("Twitter"))return;g(!1),l(!1),t(!1),b(0),f([]);let O=v==="following"?{type:"following",username:r.username}:null;N("Twitter",{action:"getTweets",page:0,filters:O},x.tweets).then(d=>{if(!d)return F("warning","No tweets returned from server");f(d)})},[v,m,r==null?void 0:r.username]),a(L,{children:[e(te,{}),e(pe,{children:S?e(dt,{hide:()=>A(!1)}):null}),E&&e("div",{className:"reply-area",children:e(le,{onSend:()=>R(!1),goBack:()=>R(!1)})}),e("div",{className:"tweets-container",onScroll:()=>{var P;if(h||T)return;let O=(P=document.querySelector(".tweets-container"))==null?void 0:P.scrollTop;O&&S&&O<1&&A(!1);let d=document.querySelector("#last");if(!d)return;!Ve(d)&&(l(!0),N("Twitter",{action:"getTweets",page:c+1,filter:{type:v==="following"?"following":void 0,username:v==="following"?r.username:void 0}},null).then(w=>{w&&w.length>0?(f([...u,...w]),l(!1)):g(!0)}),b(w=>w+1))},children:u==null?void 0:u.map((O,d)=>{let W=d===u.length-1;return e(X,{data:O,last:W},d)})})]})}const dt=({hide:n})=>a(Ee.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.15,ease:"easeInOut"},className:"new-tweets",onClick:()=>{let o=document.querySelector(".tweets-container");o&&(o.scrollTo({top:0,behavior:"smooth"}),n==null||n())},children:[e($e,{}),s("APPS.TWITTER.NEW_POSTS")]});function ut(){const{Func:n,User:o,UserPanel:v}=i.useContext(G),[r]=o,[m,t]=n.Tweet,[u,f]=n.View,[E,R]=v,[c,b]=i.useState(m),[h,g]=i.useState([]),[T,l]=i.useState(null),[S,A]=i.useState([]),[_,O]=i.useState(!1);i.useState(0),i.useState(!1),i.useState(!1);const[d,W]=i.useState({retweeted:!1,liked:!1}),[P,w]=i.useState({likes:0,retweets:0});i.useEffect(()=>{var p;if(b(m),A([]),(p=c==null?void 0:c.tweet)!=null&&p.attachments)if(Array.isArray(c.tweet.attachments))A(c.tweet.attachments);else{const C=JSON.parse(c.tweet.attachments);C&&Array.isArray(C)&&A(JSON.parse(c.tweet.attachments))}W({retweeted:c.tweet.retweeted,liked:c.tweet.liked}),w({likes:c.tweet.likes,retweets:c.tweet.retweets})},[c,m]),i.useEffect(()=>{if(c.tweet.date_created){const p=new Date(c.tweet.date_created);let C=p.getDate(),k=p.getMonth()+1,y=p.getFullYear();l(`${p.getHours()}:${p.getMinutes()}· ${C}/${k}/${y}`)}},[c]),ee("twitter:updateTweetData ",p=>{p.tweetId===c.tweet.id&&w({...P,[p.data]:p.increment?P[p.data]+1:P[p.data]-1})}),i.useEffect(()=>{var p;j("Twitter")&&(g([]),N("Twitter",{action:"getTweets",page:0,filter:{type:"replyTo",tweet_id:m.tweet.id}},(p=x.tweets.find(C=>C.tweet.id===m.tweet.id))==null?void 0:p.tweet._replies).then(C=>{g(C)}))},[m,_]);const{handleScroll:U}=J({fetchData:p=>N("Twitter",{action:"getTweets",filter:{type:"replyTo",tweet_id:c.tweet.id},page:p}),onDataFetched:p=>g([...h,...p]),perPage:25});return a(L,{children:[a("div",{className:ne("viewtweet-container",_&&"replying"),children:[a("div",{className:"viewtweet-header",children:[e("div",{onClick:()=>f("timeline"),children:e("i",{className:"far fa-arrow-left"})}),e("div",{className:"viewtweet-title",children:s("APPS.TWITTER.TWEET")}),e("div",{})]}),a("div",{className:"viewtweet-body",children:[a("div",{className:"viewtweet-content-header",children:[e("div",{className:"profile-picture",onClick:()=>n.fetchAndSetProfile(c.user.username),children:e(M,{avatar:c.user.profile_picture})}),a("div",{className:"profile-name",children:[a("div",{className:"name",children:[c.user.name," ",c.user.verified&&e("i",{className:"fas fa-badge-check verified"})]}),a("span",{children:["@",c.user.username]})]})]}),c.tweet.replyToId&&a("div",{className:"replying-to",onClick:p=>{p.stopPropagation(),N("Twitter",{action:"getTweet",tweetId:c.tweet.replyToId},x.tweets.find(C=>C.tweet.id===c.tweet.replyToId)).then(C=>{t(C)})},children:[s("APPS.TWITTER.REPLYING_TO").format({name:""})," ",a("span",{children:["@",c.tweet.replyToAuthor]})]}),a("div",{className:"content",children:[Z(c.tweet.content,!0,n.fetchAndSetProfile),(S==null?void 0:S.length)>0&&e("div",{className:"attachment",children:S.map((p,C)=>{var k;return K(p)?e(mt,{src:p,id:C,onClick:()=>{D.FullscreenImage.set(p)}},C):e(z,{src:p,blur:((k=c.user)==null?void 0:k.username)!==(r==null?void 0:r.username),onClick:()=>{D.FullscreenImage.set(p)}},C)})})]}),e("div",{className:"date",children:T})]}),a("div",{className:"stats",children:[a("div",{className:"stats-item",onClick:()=>{R({display:!0,data:{title:s("APPS.TWITTER.RETWEETED_BY"),tweet_id:c.tweet.id,cb:()=>{t(c),f("tweet")}}})},children:[e("span",{children:P.retweets})," Retweets"]}),a("div",{className:"stats-item",onClick:()=>{R({display:!0,data:{title:s("APPS.TWITTER.LIKED_BY"),tweet_id:c.tweet.id,cb:()=>{t(c),f("tweet")}}})},children:[e("span",{children:P.likes})," ",s("APPS.TWITTER.LIKES")]})]}),a("div",{className:"buttons",children:[e("div",{className:"button comment",children:e("i",{className:"fal fa-comment",onClick:()=>O(!0)})}),e("div",{className:`button retweet ${d.retweeted?"active":""}`,children:e("i",{className:"fal fa-retweet",onClick:()=>{D.PopUp.set({title:d.retweeted?s("APPS.TWITTER.RETWEET_POPUP.UNDO_RETWEET"):s("APPS.TWITTER.RETWEET_POPUP.RETWEET"),description:s("APPS.TWITTER.RETWEET_POPUP.DESCRIPTION").format({action:d.retweeted?s("APPS.TWITTER.RETWEET_POPUP.UNDO_RETWEET"):s("APPS.TWITTER.RETWEET_POPUP.RETWEET")}),buttons:[{title:s("APPS.TWITTER.RETWEET_POPUP.CANCEL")},{title:d.retweeted?s("APPS.TWITTER.RETWEET_POPUP.UNDO_RETWEET"):s("APPS.TWITTER.RETWEET_POPUP.RETWEET"),cb:()=>{N("Twitter",{action:"toggleRetweet",tweet_id:c.tweet.id,retweeted:!d.retweeted},!d.retweeted).then(p=>{W({...d,retweeted:p})})}}]})}})}),e("div",{className:`button like ${d.liked?"active":""}`,children:e("i",{className:"fal fa-heart",onClick:p=>{p.stopPropagation(),N("Twitter",{action:"toggleLike",tweet_id:c.tweet.id,liked:!d.liked},!d.liked).then(C=>{W({...d,liked:C})})}})})]}),e("div",{className:"tweet-replies",children:e("div",{className:"tweets",onScroll:U,children:h.map((p,C)=>e(X,{data:p},C))})})]}),_&&e("div",{className:"reply-area",children:e(le,{onSend:()=>O(!1),goBack:()=>O(!1),isReply:c.tweet.id})})]})}const mt=({src:n,id:o,onClick:v})=>e("video",{src:n,id:o,autoPlay:!0,loop:!0,muted:!0,onClick:()=>v()}),Pt=["#gay","#pride","#99kr"],G=i.createContext(null);function wt(){var p,C;const n=Q(Je.Settings),o=(p=Q(Ke))==null?void 0:p.active,[v,r]=i.useState(null),[m,t]=i.useState(!1),[u,f]=i.useState("timeline"),[E,R]=i.useState("default"),[c,b]=i.useState(null),[h,g]=i.useState(null),[T,l]=i.useState(null),[S]=i.useState(!1),[A,_]=i.useState(!1),[O,d]=i.useState({display:!1,data:null}),[W,P]=i.useState(!0);i.useEffect(()=>{var k,y;o&&((k=o==null?void 0:o.data)==null?void 0:k.view)==="profile"&&w((y=o==null?void 0:o.data)==null?void 0:y.username)},[o]),i.useEffect(()=>{N("isAdmin",null,!1).then(k=>{if(V.APPS.TWITTER.account.value){r({...V.APPS.TWITTER.account.value,isAdmin:k}),t(!0),P(!1);return}else{if(V.APPS.TWITTER.account.value===!1)return P(!1);N("Twitter",{action:"isLoggedIn"},x.account).then(y=>{y?(V.APPS.TWITTER.account.set(y),r({...y,isAdmin:k}),t(!0)):V.APPS.TWITTER.account.set(!1),P(!1)})}})},[]),i.useEffect(()=>{O.display&&f("userpanel")},[O]),i.useEffect(()=>(we.ReceiveAppNotifications.set(!T),()=>{we.ReceiveAppNotifications.set(!0)}),[u]);const w=k=>{if(!k)return F("warning","No username provided.");N("Twitter",{action:"getProfile",data:{username:k}},x.accounts[k]).then(y=>{if(!y)return F("warning","No profile found for",k);g(y),f("profile")})},U={timeline:e(ot,{}),profile:e(lt,{}),tweet:e(ut,{}),search:e(ct,{}),notifications:e(tt,{}),userpanel:e(it,{}),dmlist:e(Ze,{})};return e(G.Provider,{value:{User:[v,r],Func:{View:[u,f],Timeline:[E,R],Profile:[h,g],Tweet:[c,b],Update:[A,_],LoggedIn:[m,t],DmUser:[T,l],fetchAndSetProfile:w},UserPanel:[O,d]},children:W||!je()?e("div",{className:"loading",children:e(Ye,{size:40,lineWeight:5,speed:2,color:((C=n==null?void 0:n.display)==null?void 0:C.theme)==="dark"?"#fff":"#000"})}):e("div",{className:"twitter-container",children:m===!0?a(L,{children:[U==null?void 0:U[u],u==="new-tweet"&&e("div",{className:"reply-area",children:e(le,{data:o==null?void 0:o.data,goBack:()=>f("timeline"),onSend:()=>f("timeline")})}),u==="timeline"&&!S&&e("div",{className:"new-tweet",onClick:()=>f("new-tweet"),children:e(ze,{})}),e(nt,{})]}):e(et,{})})})}export{G as TwitterContext,wt as default,Pt as rainbowHashtags};
