[{"id": 1000, "name": "fleeca legion bank vault", "data": {"heading": 250, "model": **********, "coords": {"x": 148.************, "y": -1044.*************, "z": 29.**************}, "doors": false, "state": 1, "maxDistance": 2}}, {"id": 1001, "name": "fleeca legion gate", "data": {"heading": 160, "model": -**********, "coords": {"x": 150.**************, "y": -1047.*************, "z": 29.**************}, "doors": false, "state": 1, "maxDistance": 2, "groups": {"police": 1}}}, {"id": 1004, "name": "bank fleeca above pillbox vault", "data": {"state": 1, "coords": {"x": 312.*************, "y": -282.*************, "z": 54.**************}, "model": **********, "groups": {"police": 1}, "maxDistance": 2, "heading": 250, "doors": false}}, {"id": 1005, "name": "bank fleeca gate above pillbox", "data": {"state": 1, "coords": {"x": 314.*************, "y": -285.*************, "z": 54.**************}, "model": -**********, "maxDistance": 2, "heading": 160, "doors": false}}, {"id": 1006, "name": "bank fleeca vault ls customs", "data": {"state": 1, "coords": {"x": -352.**************, "y": -53.**************, "z": 49.**************}, "model": **********, "groups": {"police": 1}, "maxDistance": 2, "heading": 251, "doors": false}}, {"id": 1007, "name": "bank fleeca gate ls customs", "data": {"state": 1, "coords": {"x": -350.**************, "y": -56.**************, "z": 49.**************}, "model": -**********, "groups": {"police": 1}, "maxDistance": 2, "heading": 161, "doors": false}}, {"id": 1008, "name": "bank fleeca vault life invader", "data": {"state": 1, "coords": {"x": -1211.************, "y": -334.*************, "z": 37.**************}, "model": **********, "maxDistance": 2, "heading": 297, "doors": false}}, {"id": 1009, "name": "bank fleeca gate life invader", "data": {"state": 1, "coords": {"x": -1207.*************, "y": -335.*************, "z": 38.**************}, "model": -**********, "groups": {"police": 1}, "maxDistance": 2, "heading": 207, "doors": false}}, {"id": 1010, "name": "bank fleeca vault ocean", "data": {"state": 1, "coords": {"x": -2958.***********, "y": 482.**************, "z": 15.**************}, "model": -********, "groups": {"police ": 1}, "maxDistance": 2, "heading": 358, "doors": false}}, {"id": 1011, "name": "bank fleeca gate ocean", "data": {"state": 1, "coords": {"x": -2956.**********, "y": 485.**************, "z": 15.**************}, "model": -**********, "groups": {"police": 1}, "maxDistance": 2, "heading": 268, "doors": false}}, {"id": 1013, "name": "bank paleto vault", "data": {"state": 1, "coords": {"x": -104.**************, "y": 6473.***********, "z": 31.**************}, "model": -**********, "groups": {"police": 1}, "maxDistance": 2, "heading": 45, "doors": false}}, {"id": 1014, "name": "bank paleto gate", "data": {"state": 1, "coords": {"x": -106.**************, "y": 6476.***********, "z": 31.**************}, "model": **********, "groups": {"police": 1}, "maxDistance": 2, "heading": 315, "doors": false}}, {"id": 1015, "name": "bank fleeca harmony vault", "data": {"groups": {"police": 1}, "maxDistance": 2, "coords": {"x": 1175.*************, "y": 2710.*********, "z": 38.**************}, "doors": false, "heading": 90, "model": **********, "state": 1}}, {"id": 1016, "name": "bank fleeca harmony gate", "data": {"groups": {"police": 1}, "maxDistance": 2, "coords": {"x": 1172.*************, "y": 2713.************, "z": 38.**************}, "doors": false, "heading": 0, "model": -**********, "state": 1}}, {"id": 1017, "name": "Vangelico Jewelry Store Double Front Doors", "data": {"groups": {"police": 1}, "maxDistance": 2, "coords": {"x": -631.***********, "y": -237.**************, "z": 38.*************}, "doors": [{"model": **********, "heading": 306, "coords": {"x": -631.*************, "y": -236.**************, "z": 38.*************}}, {"model": 9467943, "heading": 306, "coords": {"x": -630.************, "y": -238.*************, "z": 38.*************}}], "state": 1}}, {"id": 1018, "name": "Vangelico Jewelry Store Inside Single Door", "data": {"groups": {"police": 1}, "maxDistance": 2, "coords": {"x": -629.*************, "y": -230.**************, "z": 38.**************}, "doors": false, "heading": 36, "model": **********, "state": 1}}, {"id": 1019, "name": "humane labs door 1", "data": {"maxDistance": 2, "state": 1, "model": 19193616, "doors": false, "heading": 170, "coords": {"x": 3526.0205078125, "y": 3702.24267578125, "z": 21.34196090698242}}}, {"id": 1020, "name": "Humane Double Door 2", "data": {"maxDistance": 2, "auto": true, "state": 1, "doors": [{"model": -**********, "heading": 170, "coords": {"x": 3533.093017578125, "y": 3670.61279296875, "z": 27.12124252319336}}, {"model": *********, "heading": 170, "coords": {"x": 3530.5322265625, "y": 3671.064453125, "z": 27.12123870849609}}], "groups": {"police": 1}, "coords": {"x": 3531.8125, "y": 3670.838623046875, "z": 27.12124061584472}}}, {"id": 1021, "name": "Humane Double Door 1", "data": {"maxDistance": 2, "auto": true, "state": 1, "doors": [{"model": 1878909644, "heading": 350, "coords": {"x": 3538.99951171875, "y": 3673.658935546875, "z": 19.991735********}}, {"model": 1709395619, "heading": 350, "coords": {"x": 3541.56005859375, "y": 3673.20751953125, "z": 19.991735********}}], "groups": {"police": 1}, "coords": {"x": 3540.27978515625, "y": 3673.43310546875, "z": 19.991735********}}}, {"id": 1022, "name": "Humane labs double slide door 3", "data": {"maxDistance": 2, "auto": true, "state": 1, "doors": [{"model": *********, "heading": 350, "coords": {"x": 3555.***********, "y": 3664.***********, "z": 27.**************}}, {"model": -**********, "heading": 350, "coords": {"x": 3552.************, "y": 3665.************, "z": 27.**************}}], "groups": {"police": 1}, "coords": {"x": 3554.***********, "y": 3665.***********, "z": 27.**************}}}, {"id": 1023, "name": "Big Bank Main Door", "data": {"maxDistance": 2, "state": 1, "model": -*********, "doors": false, "groups": {"police": 1}, "heading": 340, "coords": {"x": 256.*************, "y": 220.**************, "z": 106.**************}}}, {"id": 1024, "name": "Big Bank Door 2", "data": {"maxDistance": 2, "state": 1, "model": *********, "doors": false, "groups": {"police": 1}, "heading": 250, "coords": {"x": 262.*************, "y": 222.************, "z": 106.**************}}}, {"id": 1025, "name": "Big Bank Lower Vault", "data": {"maxDistance": 2, "state": 1, "model": *********, "doors": false, "groups": {"police": 1}, "heading": 160, "coords": {"x": 255.**************, "y": 223.**************, "z": 102.**************}}}, {"id": 1026, "name": "Big Bank Lower Gate", "data": {"maxDistance": 2, "state": 1, "model": -**********, "doors": false, "heading": 160, "coords": {"x": 251.**************, "y": 221.*************, "z": 101.**************}}}, {"id": 1027, "name": "Big Bank Lower Gate 2", "data": {"maxDistance": 2, "state": 1, "model": -**********, "doors": false, "heading": 250, "coords": {"x": 261.*************, "y": 214.**************, "z": 101.**************}}}, {"id": 1028, "name": "Big Bank Door Side", "data": {"maxDistance": 2, "state": 1, "model": **********, "doors": false, "heading": 340, "coords": {"x": 237.**************, "y": 227.*************, "z": 106.**************}}}, {"id": 1029, "name": "Big Bank Door Top", "data": {"maxDistance": 2, "state": 1, "model": **********, "doors": false, "heading": 250, "coords": {"x": 256.**************, "y": 206.**************, "z": 110.**************}}}, {"id": 1030, "name": "Big Bank Door Top 2", "data": {"maxDistance": 2, "state": 1, "model": **********, "doors": false, "heading": 340, "coords": {"x": 266.**************, "y": 217.*************, "z": 110.**************}}}]