local isWorkflowOpen, isFocusCursor = false, false

--- Returns whether the workflow is active and open
--- @return boolean
function IsWorkflowActive()
    return isWorkflowOpen == true
end
exports("IsWorkflowActive", IsWorkflowActive)

--- Updates workflow UI focus:
local function UpdateFocus()
    if isWorkflowOpen then
        SetNuiFocus(true, true)
        SetNuiFocusKeepInput(true)
    else
        SetNuiFocus(false, false)
        SetNuiFocusKeepInput(true)
    end
end

--- Updates workflow cursor focus
local function ToggleWorkflowCursor()
    if isWorkflowOpen then 
        if isFocusCursor then
            SetNuiFocus(false, false)
            SetNuiFocusKeepInput(true)
            isFocusCursor = false
        else
            SetNuiFocus(true, true)
            SetNuiFocusKeepInput(true)
            isFocusCursor = true
        end
    else
        UpdateFocus()
        isFocusCursor = false
    end
end

--- Toggles the workflow UI. Opens the workflow UI if `true` and hides if `false`
--- @param enable boolean
function ToggleWorkflowUI(enable)
    isWorkflowOpen = enable
    if not enable then
        UpdateFocus()
    end
    SendNUIMessage({
        action = "t1ger_mechanic:workflow:toggle",
        data = {
            state = enable
        }
    })
end

--- Sets the title of the workflow
--- @param title string
function SetWorkflowTitle(title)
    if type(title) ~= "string" or title == "" then
        title = Config.WorkflowUI.DefaultTitle
    end
    SendNUIMessage({
        action = "t1ger_mechanic:workflow:setTitle",
        data = title
    })
end

--- Sets data to the workflow
--- @param data table
function SetWorkflowData(data)
    if type(data) ~= "table" then
        return error("[SetWorkflowData] Invalid type for data. Must be a table containing entries")
    end
    SendNUIMessage({
        action = "t1ger_mechanic:workflow:setData",
        data = data
    })
end

--- Updates a given task in the workflow
--- @param id number The task ID
--- @param completed boolean Whether the task should be marked as completed or not
function UpdateWorkflowTask(id, completed)
    if type(id) ~= "number" or type(completed) ~= "boolean" then return end
    SendNUIMessage({
        action = "t1ger_mechanic:workflow:updateTask",
        data = {
            id = id,
            completed = completed
        }
    })
end

--- Removes a given task from the workflow
--- @param id number The task ID
function RemoveWorkflowTask(id)
    if type(id) ~= "number" then return end
    SendNUIMessage({
        action = "t1ger_mechanic:workflow:removeTask",
        data = {
            id = id
        }
    })
end

--- Clears all tasks from the workflow
function ClearWorkflowTasks()
    SendNUIMessage({
        action = "t1ger_mechanic:workflow:clearTasks"
    })
end

--- Starts the workflow UI with given title and data
--- @param title string
--- @param data table
function StartWorkflow(title, data)
    if IsWorkflowActive() then
        ClearWorkflowTasks()
        ToggleWorkflowUI(false)
    end
    -- Set title:
    SetWorkflowTitle(title)

    -- Set data:
    SetWorkflowData(data)

    -- Toggle workflow UI:
    ToggleWorkflowUI(true)
end

--- Stops the workflow UI and resets title and data
function StopWorkflow()
    -- Set title:
    SetWorkflowTitle()

    -- Clear All Tasks
    ClearWorkflowTasks()

    -- Toggle workflow UI:
    ToggleWorkflowUI(false)
end

--- Callback function when closing the workflow UI
RegisterNUICallback("t1ger_mechanic:workflow:closeNUI", function()
    isWorkflowOpen = false
    UpdateFocus()
end)

-- Callback function to set workflow total progress:
RegisterNUICallback("t1ger_mechanic:workflow:progressUpdated", function(newValue, cb)
    if newValue == 100 then
        Wait(1000)
        StopWorkflow()
    end
    cb("ok")
end)

--- Thread to disable control actions
CreateThread(function()
    while true do
        if isWorkflowOpen and isFocusCursor then
            -- Active check with minimal delay for smooth control blocking
            Wait(1)
            DisableAllControlActions(0)
        else
            -- Sleep longer when cursor is not active to save resources
            Wait(500)
        end
    end
end)

--- Comamnd to close workflow UI:
RegisterCommand(Config.WorkflowUI.Commands.close, function()
    ToggleWorkflowUI(false)
end, false)

--- Comamnd to open workflow UI:
RegisterCommand(Config.WorkflowUI.Commands.open, function()
    ToggleWorkflowUI(true)
end, false)

--- command to toggle workflow cursor:
RegisterCommand(Config.WorkflowUI.Commands.cursor, function()
    ToggleWorkflowCursor()
end, false)

--- command to clear workflow:
RegisterCommand(Config.WorkflowUI.Commands.clear, function()
    -- Set title:
    SetWorkflowTitle()

    -- Clear All Tasks
    ClearWorkflowTasks()
end, false)

--- KeyMapping for enable/disable cursor for UI:
RegisterKeyMapping(Config.WorkflowUI.Commands.cursor, Config.WorkflowUI.CursorKeybind.description, "keyboard", Config.WorkflowUI.CursorKeybind.defaultKey)