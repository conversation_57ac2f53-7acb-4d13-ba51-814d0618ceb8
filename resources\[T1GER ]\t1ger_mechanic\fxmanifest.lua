fx_version 'cerulean'
game 'gta5'
lua54 'yes'
use_experimental_fxv2_oal 'yes'

name 't1ger_mechanic'
author 'T1GE<PERSON> Scripts'
discord 'https://discord.gg/t1ger'
description 'T1GER Mechanic'
version '3.6.2'

dependencies {
    '/server:7290',     	-- ⚠️PLEASE READ⚠️; Requires at least SERVER build 7290.
    '/gameBuild:3258',  	-- ⚠️PLEASE READ⚠️; Requires at least GAME build 3095.
	't1ger_carlift',		-- ⚠️PLEASE READ⚠️; Requires t1ger_carlift.
	't1ger_mechanicprops'	-- ⚠️PLEASE READ⚠️; Requires t1ger_mechanicprops. 
}

shared_scripts {
    '@ox_lib/init.lua',
	'bridge/config.lua',
	'bridge/init.lua',
	'shared/*.lua',
}

client_scripts {
    'bridge/framework/client.lua', -- Framework
    'bridge/target/client.lua', -- Target
    'bridge/inventory/client.lua', -- Inventory
    'bridge/garage/client.lua', -- Garage
    'bridge/notification/client.lua', -- Notification

	'client/main.lua', -- Main
    'client/vehicledata/*.lua', -- Vehicle Data (mileage, components, degradation and so on)

	'client/shop/**/*.lua', -- Shop & Markers
	'client/carlifts/class.lua', -- CarLift Class
	'client/carlifts/main.lua', -- CarLift
	'client/actions/*.lua', -- Main
    'client/missions/*.lua', -- Missions
	'client/carjack.lua', -- Car Jack
	'client/repairs/main.lua', -- Repairs - main
	'client/repairs/functions.lua', -- Repairs - functions
	'client/repairs/body.lua', -- Repairs - functions
	'client/workflow.lua', -- Workflow UI
}

server_scripts {
	'@mysql-async/lib/MySQL.lua', -- or '@oxmysql/lib/MySQL.lua' if using oxmysql
	
    'bridge/framework/server.lua', -- Framework
    'bridge/inventory/server.lua', -- Inventory
    'bridge/jobaccount/server.lua', -- Job Account / Society Account
    'bridge/garage/server.lua', -- Garage
    'bridge/notification/server.lua', -- Notification

	'server/functions.lua', -- Functions
	'server/main.lua', -- Start main server logic
	'server/shop/class.lua', -- Shop Class
	'server/shop/handler.lua', -- Shop Handler
	'server/shop/main.lua', -- Shops
	'server/vehicledata.lua', -- Vehicle Data
    'server/carlifts/class.lua', -- Car Lift Class
    'server/carlifts/main.lua', -- Car Lifts
    'server/billing.lua', -- Billing
	'server/missions.lua', -- Missions
}

files {
    'locales/*.json',
	'web/index.html',
	'web/assets/*.*'
}

ui_page 'web/index.html'

ox_libs {
    'locale',
    'math'
}

escrow_ignore { 
	-- Shared files
    'shared/*.lua',

	-- Bridge files
	'bridge/**/*.lua',

	-- Client files (Source-Available)
	'client/**/*.lua',

	-- Server files (Source-Available)
	'server/**/*.lua',
}
dependency '/assetpacks'