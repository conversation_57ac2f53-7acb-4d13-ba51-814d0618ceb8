-- Opens the main workbench menu for the mechanic shop
---@param shopId number unique shop identifier
---@param markerId string unique marker identifier
function WorkbenchMain(shopId, markerId)
    if not Config.Shop.Workbench.enable then
        -- add export/event to open your own crafting stuff 
    else
        local categories = {}

        for category, cat in ipairs(Config.Shop.Workbench.categories) do
            table.insert(categories, {
                title = cat.label,
                description = cat.description,
                icon = cat.icon,
                arrow = true,
                onSelect = function()
                    local recipes = GetCraftingRecipes(cat, shopId, markerId)
                    table.sort(recipes, function(a, b)
                        return a.title < b.title
                    end)
                    -- register context for recipe:
                    lib.registerContext({
                        id = "mechanic_workbench_menu_recipes",
                        title = cat.label,
                        menu = "mechanic_workbench_menu",
                        options = recipes,
                    })
                    lib.showContext("mechanic_workbench_menu_recipes")
                end
            })
        end
        
        -- register context:
        lib.registerContext({
            id = "mechanic_workbench_menu",
            title = locale("menu_title.workbench_main"),
            options = categories
        })
    
        -- show context:
        lib.showContext("mechanic_workbench_menu")
    end
end

--- Returns crafting recipes for a given category
--- @param cat table
--- @param shopId number
--- @param markerId string
--- @return table
GetCraftingRecipes = function(cat, shopId, markerId)
    local recipes = {}

    for outputItem, recipe in pairs(cat.recipe) do
        local materials = {}, {}
        if type(recipe.materials) == "table" and next(recipe.materials) then
            for inputItem, amount in pairs(recipe.materials) do
                materials[#materials + 1] = {name = inputItem, label = Config.Materials[inputItem] or inputItem, amount = amount, value = amount.."x"}
            end
        end
        local outputLabel = _API.Inventory.GetItemLabel(outputItem)
        if outputLabel then
            recipes[#recipes + 1] = {
                title = outputLabel,
                icon = recipe.icon,
                metadata = materials,
                args = {input = materials, output = {name = outputItem, label = outputLabel, icon = recipe.icon}},
                onSelect = function(args)

                    -- input dialog:
                    local quantity = lib.inputDialog(string.format(locale("input_title.workbench_craft"), outputLabel), {
                        {
                            type = "number",
                            label = locale("input_label.workbench_amount"),
                            description = string.format(locale("input_description.workbench_amount"), outputLabel),
                            placeholder = 1,
                            default = 1,
                            required = true,
                            min = 1,
                            max = Config.Shop.Workbench.maxCraftAmount
                        }
                    })

                    -- validate input
                    if not quantity or type(quantity[1]) ~= "number" or quantity[1] <= 0 then 
                        return lib.showContext("mechanic_workbench_menu_recipes")
                    end

                    quantity = quantity[1]

                    -- callback to check if has materials:
                    local hasMaterials, missingItems = lib.callback.await("t1ger_mechanic:server:hasMaterials", false, args.input, quantity)

                    -- if not has materials, notify items and return to menu
                    if not hasMaterials then
                        local lines = {}

                        for _, material in pairs(missingItems) do
                            local line = string.format("- %s: %d / %d pcs", material.label, material.invAmount, material.reqAmount)
                            table.insert(lines, line)
                        end

                        local message = locale("notification.workbench_missing_materials") .. " \n" .. table.concat(lines, "\n")
                    
                        -- Calculate dynamic duration (e.g., 50ms per character)
                        local perChar = 60
                        local minDuration = 3000
                        local maxDuration = 10000
                        local dynamicDuration = math.min(maxDuration, math.max(minDuration, #message * perChar))
                    
                        _API.ShowNotification(message, "inform", {duration = dynamicDuration})

                        Wait(100)
                        return lib.showContext("mechanic_workbench_menu_recipes")
                    end

                    -- Skillcheck logic
                    local success = not Config.Shop.Workbench.skillcheck.enable or SkillCheck(Config.Shop.Workbench.skillcheck.difficulty, Config.Shop.Workbench.skillcheck.inputs)
                    -- If skillcheck failed, return to menu
                    if not success then 
                        return lib.showContext("mechanic_workbench_menu_recipes")
                    end

                    -- progress bar:
                    if lib.progressBar({
                        duration = Config.Shop.Workbench.duration,
                        label = string.format(locale("progressbar.crafting"), quantity, args.output.label),
                        useWhileDead = false,
                        canCancel = true,
                        anim = {
                            dict = Config.Shop.Workbench.anim.dict,
                            clip = Config.Shop.Workbench.anim.clip,
                            flag = Config.Shop.Workbench.anim.flag,
                            blendIn = Config.Shop.Workbench.anim.blendIn,
                            blendOut = Config.Shop.Workbench.anim.blendOut
                        },
                        disable = {
                            move = true,
                            combat = true
                        }
                    }) then
                        -- craft item event
                        TriggerServerEvent("t1ger_mechanic:server:craftItem", args.input, args.output, quantity, shopId, markerId)
                    end

                    Wait(100)
                    lib.showContext("mechanic_workbench_menu")
                end,
            }
        end
    end

    return recipes
end