function CreateShopClass(args)
	local self = {}

    self.id = args.id
    self.name = args.name
    self.account = args.account
    self.boss = args.boss
    self.markup = args.markup
    self.job = args.job
    self.blip = args.blip
    self.employees = args.employees
    self.markers = args.markers
    self.categories = args.categories
    self.storage = args.storage
    self.billing = args.billing
    self.orders = args.orders
    self.delivery = args.delivery

    -- paramters n(string): name
    function self.setName(n) -- set shop name. 
        self.name = n
        MySQL.Async.execute('UPDATE '..database_table..' SET name = ? WHERE id = ?', {self.name, self.id})
        TriggerClientEvent('tuningsystem:client:updateShopConfig', -1, self.id, Config.Shops[self.id])
    end
    
    -- paramters identifier(string): identifier of the boss to assign
    function self.setBoss(identifier) -- update the self.boss identifier
        self.boss = identifier
        MySQL.Async.execute('UPDATE '..database_table..' SET boss = ? WHERE id = ?', {identifier, self.id})
        TriggerClientEvent('tuningsystem:client:updateShopConfig', -1, self.id, Config.Shops[self.id])
    end

    -- paramters categories(table): categories to allow for the shop
    function self.setCategories(categories) -- update the categories for the shop
        self.categories = categories
        MySQL.Async.execute('UPDATE '..database_table..' SET categories = ? WHERE id = ?', {json.encode(categories), self.id})
        TriggerClientEvent('tuningsystem:client:updateShopConfig', -1, self.id, Config.Shops[self.id])
    end

    -- paramters pos(array): coords for delivery
    function self.setDelivery(pos) -- update the delivery coords for the shop
        self.delivery = pos
        MySQL.Async.execute('UPDATE '..database_table..' SET delivery = ? WHERE id = ?', {json.encode(self.delivery), self.id})
        TriggerClientEvent('tuningsystem:client:updateShopConfig', -1, self.id, Config.Shops[self.id])
    end

    -- function to get boss grade
    function self.getBossGrade()
        for k,v in pairs(self.job.grades) do 
            if v.isboss ~= nil and v.isboss ~= 0 then
                return v.grade
            end
        end
        return nil
    end

    -- ## SHOP MARKUP ## --
    
    function self.getMarkup()
        return self.markup
    end

    function self.setMarkup(amount)
        self.markup = tonumber(amount)
        self.saveMarkup()
    end

    function self.saveMarkup() -- save and sync shop markup
        MySQL.Async.execute('UPDATE '..database_table..' SET markup = ? WHERE id = ?', {self.markup, self.id})
        TriggerClientEvent('tuningsystem:client:updateShopConfig', -1, self.id, Config.Shops[self.id])
	end

    -- ## ACCOUNTS ## --

    -- paramters m(number): amount of money
    function self.addMoney(m) -- add money to shop's account
        if Config.ShopCreator.UseSocietyAccounts then
            Core.AddSharedAccountMoney(self.job.name, m)
        end
        self.account = self.account + m
        self.saveAccount()
    end

    -- paramters m(number): amount of money
	function self.removeMoney(m) -- remove money from shop's account
        if Config.ShopCreator.UseSocietyAccounts then
            Core.RemoveSharedAccountMoney(self.job.name, m)
        end
        self.account = self.account - m
        self.saveAccount()
	end

    -- paramters m(number): amount of money
	function self.setMoney(m) -- set shop's account balance to specific value
        if Config.ShopCreator.UseSocietyAccounts then
            Core.SetSharedAccountMoney(self.job.name, m)
        end
        self.account = m
		self.saveAccount()
	end

    function self.getAccount()
        if Config.ShopCreator.UseSocietyAccounts then
            self.account = Core.GetSharedAccountBalance(self.job.name)
        end
        return self.account
    end

    function self.saveAccount() -- save and sync shop account
        if Config.ShopCreator.UseSocietyAccounts then
            self.account = Core.GetSharedAccountBalance(self.job.name)
        end
        MySQL.Async.execute('UPDATE '..database_table..' SET account = ? WHERE id = ?', {self.account, self.id})
        TriggerClientEvent('tuningsystem:client:updateShopConfig', -1, self.id, Config.Shops[self.id])
	end

    -- ## EMPLOYEES ## --

    -- paramters identifier(string): identifier of the player
    function self.getEmployee(identifier) -- get employee in the shop
        if next(self.employees) then
            for k,v in pairs(self.employees) do
                if identifier == v.identifier then
                    return true, v
                end
            end
        end
        return false
    end

    -- paramters src(number), grade(number): playerId and jobGrade
    function self.addEmployee(src, grade, setJob) -- add employee to the shop
        local identifier = Core.Player.GetIdentifier(src)
        local employee, callback = self.getEmployee(identifier)
        if not employee then
            table.insert(self.employees, {
                identifier = identifier,
                name = Core.Player.GetName(src),
                char = Core.Player.GetFullName(src),
                grade = self.job.grades[tostring(grade)] ~= nil and self.job.grades[tostring(grade)].grade or 0
            })
            if setJob == nil then
                Core.Player.SetPlayerJob(src, self.job.name, grade or 0)
            end
            self.saveEmployees()
            return true
        else
            return false
        end
    end

    -- paramters identifier(string): identifier of the player
    function self.removeEmployee(identifier, setJob) -- remove employee from the shop
        if next(self.employees) then
            for k,v in pairs(self.employees) do
                if identifier == v.identifier then
                    table.remove(self.employees, k)
                    if self.boss == identifier then
                        self.setBoss(nil)
                    end
                    if setJob == nil then
                        local src = Core.Player.GetSrcFromIdentifier(identifier)
                        if src ~= nil or src ~= 0 then
                            Core.Player.SetPlayerJob(src, 'unemployed', 0)
                        else
                            MySQL.Async.execute('UPDATE users SET job = ?, job_grade = ? WHERE identifier = ?', {'unemployed', 0, identifier})
                        end
                    end
                    self.saveEmployees()
                    return true
                end
            end
        end
        return false
    end

    -- paramters identifier(string), grade(number): identifier of the player and grade to update
    function self.updateEmployee(identifier, grade, setJob) -- promote/demote employee job grade
        if next(self.employees) then
            for k,v in pairs(self.employees) do
                if identifier == v.identifier then
                    v.grade = grade
                    if self.boss == identifier then
                        -- player is boss:
                        local bossGrade = self.getBossGrade()
                        -- remove as boss, if grades mismatch
                        if grade ~= bossGrade then
                            self.setBoss(nil)
                        end
                    end
                    if setJob == nil then
                        local src = Core.Player.GetSrcFromIdentifier(identifier)
                        if src ~= nil or src ~= 0 then
                            Core.Player.SetPlayerJob(src, self.job.name, grade)
                        else
                            MySQL.Async.execute('UPDATE users SET job = ?, job_grade = ? WHERE identifier = ?', {self.job.name, grade, identifier})
                        end
                    end
                    self.saveEmployees()
                    return true
                end
            end
        end
        return false
    end

    function self.saveEmployees() -- save and sync shop employeses
        MySQL.Async.execute('UPDATE '..database_table..' SET employees = ? WHERE id = ?', {json.encode(self.employees), self.id})
        TriggerClientEvent('tuningsystem:client:updateShopConfig', -1, self.id, Config.Shops[self.id])
    end

    -- ## STORAGE ## --

    -- paramters identifier(string): identifier of the player
    function self.getStorage(markerId) -- get employee in the shop
        if next(self.storage) then
            --for k,v in pairs(self.storage) do
                --print(k,v)
            --end
        end
    end

    -- paramters markerId(number), item(table), amount(number)
    function self.depositStorage(markerId, itemName, amount) -- add items to specific storage
        if self.storage[markerId] and next(self.storage[markerId]) then
            local itemMatch = false
            for k,v in pairs(self.storage[markerId]) do
                if itemName == v.name then
                    itemMatch = true
                    v.count = (v.count + amount)
                    break
                end
            end
            if not itemMatch then
                local item = Core.GetItemInfo(itemName)
                table.insert(self.storage[markerId], {name = item.name, label = item.label, count = amount})
            end
        else
            table.insert(self.storage[markerId], {name = item.name, label = item.label, count = amount})
        end
        self.saveStorage()
    end

    -- paramters markerId(number), item(table), amount(number)
    function self.withdrawStorage(markerId, itemName, amount) -- remove items from pecific storage
        if self.storage[markerId] and next(self.storage[markerId]) then
            for k,v in pairs(self.storage[markerId]) do
                if itemName == v.name then
                    v.count = (v.count - amount)
                    if v.count <= 0 then
                        table.remove(self.storage[markerId], k)
                    end
                    break
                end
            end
        end
        self.saveStorage()
    end

    function self.saveStorage() -- save and sync shop employeses
        MySQL.Async.execute('UPDATE '..database_table..' SET storage = ? WHERE id = ?', {json.encode(self.storage), self.id})
        TriggerClientEvent('tuningsystem:client:updateShopConfig', -1, self.id, Config.Shops[self.id])
    end

    -- ## MARKERS ## --
    local function CreateStorage(markerId, class, data)
        self.markers[class][markerId].stash = {
            id = markerId,
            label = markerId,
            slots = data.stash.slots or Config.Stash.slots,
            weight = data.stash.weight or Config.Stash.weight,
            owner = nil -- make sure its accessable by everyone!!
        }
        self.storage[markerId] = {}
        self.saveStorage()
        local stash = self.markers[class][markerId].stash
        Core.CreateStash(stash.id, stash.label, stash.slots, stash.weight, stash.owner)
    end

    -- paramter class(string): marker class; storage, workbench etc
    -- paramter data(table): marker properties
    function self.createMarker(class, data)
        if self.markers == nil then self.markers = {} end
        if self.markers[class] == nil then self.markers[class] = {} end

        data.id = tostring(GenerateMarkerId(self.id, class, self.markers))
        self.markers[class][data.id] = data

        if class == 'storage' or class == 'stash' then
            CreateStorage(data.id, class, data)
        end

        self.saveMarkers()

        return true, data.id
    end

    -- paramter class(string): marker class; storage, workbench etc
    -- paramter markerId(number): markers[class] index markerId to find marker in question
    function self.deleteMarker(class, markerId)
        if self.markers == nil then return end
        if self.markers[class] == nil then return end
        if next(self.markers[class]) == nil then return end
        if self.markers[class][tostring(markerId)] == nil then return end

        TriggerClientEvent('tuningsystem:client:deleteMarker', -1, self.id, class, markerId)
        Wait(100)

        self.markers[class][tostring(markerId)] = nil

        self.saveMarkers()

        return true
    end

    -- paramter class(string): marker class; storage, workbench etc
    -- paramter markerId(number): markers[class] index markerId to find marker in question
    -- paramter data(table): marker properties
    function self.editMarker(class, data, markerId)
        if next(self.markers[class]) == nil then return end
        if self.markers[class][tostring(markerId)] == nil then return end

        TriggerClientEvent('tuningsystem:client:deleteMarker', -1, self.id, class, markerId)
        Wait(100)
        
        data.id = tostring(markerId)
        self.markers[class][data.id] = data

        if class == 'storage' or class == 'stash' then
            CreateStorage(data.id, class, data)
        end

        self.saveMarkers()

        return true
    end

    function self.saveMarkers() -- save and sync shop employeses
        MySQL.Async.execute('UPDATE '..database_table..' SET markers = ? WHERE id = ?', {json.encode(self.markers), self.id})
        TriggerClientEvent('tuningsystem:client:updateShopConfig', -1, self.id, Config.Shops[self.id])
    end

    -- ## ORDERS ## --
    function self.getOrders()
        return self.orders
    end

    function self.hasOrder(plate)
        if self.orders[plate] ~= nil then
            return true
        else
            return false
        end
    end

    function self.addOrder(src, plate, taskList, props, totalAmount, partAcquisition, laborCharge, markupAmount, markupValue)
        if self.orders == nil then self.orders = {} end
        self.orders[plate] = {
            player = {
                identifier = Core.Player.GetIdentifier(src),
                name = Core.Player.GetFullName(src)
            },
            plate = plate,
            taskList = taskList,
            props = props,
            paidAmount = totalAmount,
            partAcquisition = partAcquisition or 0,
            laborCharge = laborCharge or 0,
            markupAmount = markupAmount or 0,
            markupValue = markupValue or self.getMarkup(),
            taken = false,
            tuner = nil,
        }
        self.saveOrders()
    end

    function self.setTaskCompleted(plate, taskId, state)
        if self.orders[plate].taskList[taskId] ~= nil then
            self.orders[plate].taskList[taskId].completed = state
        end
        self.saveOrders()
    end

    function self.orderTakenStatus(plate, state, src)
        if self.orders ~= nil and next(self.orders) and self.orders[plate] ~= nil then
            self.orders[plate].taken = state
            if state == true then 
                self.orders[plate].tuner = Core.Player.GetIdentifier(src)
            else
                self.orders[plate].tuner = nil
            end
        end
        self.saveOrders()
    end

    function self.orderReceipt(plate)
        local receipt = {
            customer = self.orders[plate].player,
            tuner = {identifier = self.orders[plate].tuner, name = Core.Player.GetFullName(Core.Player.GetSrcFromIdentifier(self.orders[plate].tuner))},
            paidAmount = self.orders[plate].totalAmount,
            partAcquisition = self.orders[plate].partAcquisition,
            laborCharge = self.orders[plate].laborCharge,
            markupAmount = self.orders[plate].markupAmount,
            markupValue = self.orders[plate].markupValue,
            plate = self.orders[plate].plate,
            taskList = self.orders[plate].taskList,
        }
    end

    function self.removeOrder(plate)
        if self.orders ~= nil and next(self.orders) and self.orders[plate] ~= nil then
            --self.orderReceipt(plate)
            self.orders[plate] = nil
        end
        self.saveOrders()
    end

    function self.cancelOrder(plate)
        if self.orders ~= nil and next(self.orders) and self.orders[plate] ~= nil then
            local src = Core.Player.GetSrcFromIdentifier(self.orders[plate].player.identifier)
            if src ~= nil or src ~= 0 then
                Core.Player.AddMoney(src, self.orders[plate].paidAmount, 'bank')
            else
                Core.Player.AddMoneyOffline(self.orders[plate].player.identifier, 'bank', self.orders[plate].paidAmount)
            end
            if self.orders[plate].markupAmount ~= nil and self.orders[plate].markupAmount > 0 then
                Config.Shops[self.id].removeMoney(self.orders[plate].markupAmount)
            end
            if Config.ModOrder.PartAcquisitionToAccount == true and self.orders[plate].partAcquisition ~= nil and self.orders[plate].partAcquisition > 0 then
                Config.Shops[self.id].removeMoney(self.orders[plate].partAcquisition)
            end
            if Config.ModOrder.LaborChargeToAccount == true and self.orders[plate].laborCharge ~= nil and self.orders[plate].laborCharge > 0 then
                Config.Shops[self.id].removeMoney(self.orders[plate].laborCharge)
            end

            self.removeOrder(plate)
        end
    end

    function self.saveOrders() -- save and sync shop employeses
        MySQL.Async.execute('UPDATE '..database_table..' SET orders = ? WHERE id = ?', {json.encode(self.orders), self.id})
        TriggerClientEvent('tuningsystem:client:updateShopConfig', -1, self.id, Config.Shops[self.id])
    end

    -- ## BILLING ## --
    function self.getBills()
        return self.billing
    end

    local function GenerateBillingNumber()
        local number, number_exists = nil, true
        while number_exists do
            Wait(1)
            math.randomseed(GetGameTimer())
            number = '#'..string.upper(Lib.GetRandomLetter(2))..'-'..Lib.GetRandomNumber(5)
            if self.billing ~= nil and next(self.billing) then
                if self.billing[number] == nil then 
                    number_exists = false
                end
            else
                number_exists = false
            end
        end
    
        return number
    end
    
    -- paramter sender(string): identifier for sender of the bill
    -- paramter receiver(string): identifier for receiver of the bill
    -- paramter amount(int): total bill amount
    -- paramter notes(string): notes for the bill (e.g. all mod items)
    function self.createBill(sender, receiver, amount, note)
        if self.billing == nil then self.billing = {} end

        local bill_id = GenerateBillingNumber()
        self.billing[bill_id] = {
            id = bill_id,
            shop = self.name,
            sender = {id = sender, name = Core.Player.GetFullName(Core.Player.GetSrcFromIdentifier(sender))},
            receiver = {id = receiver, name = Core.Player.GetFullName(Core.Player.GetSrcFromIdentifier(receiver))},
            amount = amount,
            note = note,
            date = os.date('%d-%m-%Y'),
            time = os.date('%H:%M')
        }

        self.saveBilling()
    end

    -- paramter id(string): bill id
    function self.deleteBill(id)
        self.billing[id] = nil
        self.saveBilling()
    end

    function self.saveBilling() -- save and sync shop employeses
        MySQL.Async.execute('UPDATE '..database_table..' SET billing = ? WHERE id = ?', {json.encode(self.billing), self.id})
        TriggerClientEvent('tuningsystem:client:updateShopConfig', -1, self.id, Config.Shops[self.id])
    end

    return self
end

GenerateMarkerId = function(shopId, class, markers)
    local markerId, idExists = nil, true
    while idExists do 
        Wait(1)
        math.randomseed(GetGameTimer())
        markerId = 'tuner'..shopId..'_'..class..'_'..tostring(Lib.GetRandomNumber(4))
        if markers[class][tostring(markerId)] == nil then 
            idExists = false 
        end
    end
    return markerId
end

RegisterServerEvent('tuningsystem:server:createMarker')
AddEventHandler('tuningsystem:server:createMarker', function(shopId, class, marker)
    local src = Core.Player.GetSource(source)
    local created, markerId = Config.Shops[shopId].createMarker(class, marker)
    if created then
        TriggerClientEvent('tuningsystem:client:createMarker', -1, shopId, class, markerId)
        Core.Notification(src, {
            title = '',
            message = Lang['you_created_marker'],
            type = 'success'
        })
    else
        error('marker creation failed. Something wrong in database for: ', shopId, class, json.encode(marker))
    end
end)

RegisterServerEvent('tuningsystem:server:deleteMarker')
AddEventHandler('tuningsystem:server:deleteMarker', function(shopId, class, markerId)
    local src = Core.Player.GetSource(source)
    local deleted = Config.Shops[shopId].deleteMarker(class, markerId)
    if deleted then
        Core.Notification(src, {
            title = '',
            message = Lang['you_deleted_marker'],
            type = 'success'
        })
    else
        error('marker deletion failed. Something wrong in database for: ', shopId, class, markerId)
    end
end)

RegisterServerEvent('tuningsystem:server:editMarker')
AddEventHandler('tuningsystem:server:editMarker', function(shopId, class, marker, markerId)
    local src = Core.Player.GetSource(source)
    local edited = Config.Shops[shopId].editMarker(class, marker, markerId)
    if edited then
        Wait(1000)
        TriggerClientEvent('tuningsystem:client:createMarker', -1, shopId, class, tostring(markerId))
        Core.Notification(src, {
            title = '',
            message = Lang['you_edited_marker'],
            type = 'success'
        })
    else
        error('marker edit failed. Something wrong in database for: ', shopId, class, markerId, json.encode(marker))
    end
end)

SetMarkup = function(shopId, amount)
    if Config.Shops[shopId] == nil then return end
    Config.Shops[shopId].setMarkup(amount)
end

RegisterServerEvent('tuningsystem:server:setMarkup')
AddEventHandler('tuningsystem:server:setMarkup', function(shopId, amount)
    SetMarkup(shopId, amount)
end)

GetAccountMoney = function(shopId)
    return Config.Shops[shopId].getAccount()
end

lib.callback.register('tuningsystem:shop:getAccount', function(source, shopId)
    if Config.Shops[shopId] ~= nil then
        return Config.Shops[shopId].getAccount()
    else
        return 0
    end
end)

Core.RegisterCallback('tuningsystem:server:getAccountMoney', function(src, cb, shopId)
    local account = GetAccountMoney(shopId)
    cb(account)
end)

SetAccountMoney = function(shopId, amount)
    if Config.Shops[shopId] == nil then return end
    Config.Shops[shopId].setMoney(amount)
end

RegisterServerEvent('tuningsystem:server:setAccountMoney')
AddEventHandler('tuningsystem:server:setAccountMoney', function(shopId, amount)
    SetAccountMoney(shopId, amount)
end)

AddAccountMoney = function(shopId, amount)
    if Config.Shops[shopId] == nil then return end
    Config.Shops[shopId].addMoney(amount)
end

RegisterServerEvent('tuningsystem:server:addAccountMoney')
AddEventHandler('tuningsystem:server:addAccountMoney', function(shopId, amount)
    AddAccountMoney(shopId, amount)
end)

RemoveAccountMoney = function(shopId, amount)
    if Config.Shops[shopId] == nil then return end
    Config.Shops[shopId].removeMoney(amount)
end

RegisterServerEvent('tuningsystem:server:removeAccountMoney')
AddEventHandler('tuningsystem:server:removeAccountMoney', function(shopId, amount)
    RemoveAccountMoney(shopId, amount)
end)

RegisterServerEvent('tuningsystem:server:addEmployee')
AddEventHandler('tuningsystem:server:addEmployee', function(shopId, grade, setJob)
    local src = Core.Player.GetSource(source)
    if Config.Shops[shopId] == nil then return end
    Config.Shops[shopId].addEmployee(src, grade, setJob)
end)

RegisterServerEvent('tuningsystem:server:removeEmployee')
AddEventHandler('tuningsystem:server:removeEmployee', function(shopId, setJob)
    local src = Core.Player.GetSource(source)
    local identifier = Core.Player.GetIdentifier(src)
    if Config.Shops[shopId] == nil then return end
    Config.Shops[shopId].removeEmployee(identifier, setJob)
end)

RegisterServerEvent('tuningsystem:server:updateEmployee')
AddEventHandler('tuningsystem:server:updateEmployee', function(shopId, grade, setJob)
    local src = Core.Player.GetSource(source)
    local identifier = Core.Player.GetIdentifier(src)
    if Config.Shops[shopId] == nil then return end
    Config.Shops[shopId].updateEmployee(identifier, grade, setJob)
end)

RegisterServerEvent('tuningsystem:server:addModOrder')
AddEventHandler('tuningsystem:server:addModOrder', function(shopId, plate, taskList, props, totalAmount, partAcquisition, laborCharge, markupAmount, markupValue)
    local src = Core.Player.GetSource(source)
    if Config.Shops[shopId] == nil then return end
    Config.Shops[shopId].addOrder(src, plate, taskList, props, totalAmount, partAcquisition, laborCharge, markupAmount, markupValue)
end)

RegisterServerEvent('tuningsystem:server:modOrderTakenStatus')
AddEventHandler('tuningsystem:server:modOrderTakenStatus', function(shopId, plate, state)
    local src = Core.Player.GetSource(source)
    if Config.Shops[shopId] == nil then return end
    Config.Shops[shopId].orderTakenStatus(plate, state, src)
end)

RegisterServerEvent('tuningsystem:server:setTaskCompleted')
AddEventHandler('tuningsystem:server:setTaskCompleted', function(shopId, plate, taskId, state)
    local src = Core.Player.GetSource(source)
    if Config.Shops[shopId] == nil then return end
    Config.Shops[shopId].setTaskCompleted(plate, taskId, state)
end)

RegisterServerEvent('tuningsystem:server:cancelModOrder')
AddEventHandler('tuningsystem:server:cancelModOrder', function(shopId, plate)
    if Config.Shops[shopId] == nil then return end
    Config.Shops[shopId].cancelOrder(plate)
end)

RegisterServerEvent('tuningsystem:server:removeModOrder')
AddEventHandler('tuningsystem:server:removeModOrder', function(shopId, plate)
    if Config.Shops[shopId] == nil then return end
    Config.Shops[shopId].removeOrder(plate)
end)