OpenBossMenu = function(point)
    if not isTunerBoss then
        usingMenu = false
        point.textUi = false
        Core.Notification({
            title = '',
            message = Lang['you_are_not_boss'],
            type = 'error'
        })
        return 
    end
    lib.registerContext({
        id = 'tuner_boss_menu',
        title = Lang['title_boss_menu'],
        onExit = function()
            usingMenu = false
            point.textUi = false
        end,
        options = {
            {
                title = Lang['title_tuner_account'],
                icon = 'sack-dollar',
                arrow = true,
                args = point,
                event = 'tuningsystem:client:account'
            },
            {
                title = Lang['title_tuner_employees'],
                icon = 'people-group',
                arrow = true,
                args = point,
                event = 'tuningsystem:client:employees'
            },
            {
                title = Lang['title_tuner_markup'],
                icon = 'file-invoice-dollar',
                args = point,
                onSelect = function()
                    local input = lib.inputDialog(Lang['title_tuner_markup'], {
                        {type = 'input', label = Lang['input_label_cur_markup'], icon = 'file-invoice-dollar', disabled = true, default = Config.Shops[point.shopId].markup},
                        {type = 'number', label = Lang['input_label_set_markup'], icon = 'money-bill-trend-up', min = 0, max = Config.Markup.Max, required = true, description = Lang['input_desc_set_markup']:format(0, Config.Markup.Max)}
                    })
    
                    if not input then
                        return OpenBossMenu(point)
                    end
    
                    if input[2] == nil or input[2] == '' or input[2] == ' ' then
                        Core.Notification({
                            title = '',
                            message = Lang['input_required'],
                            type = 'error'
                        })
                        return OpenBossMenu(point)
                    end
    
                    if input[2] <= 0 then 
                        Core.Notification({
                            title = '',
                            message = Lang['input_amount_higher_0'],
                            type = 'error'
                        })
                        return OpenBossMenu(point)
                    end

                    TriggerServerEvent('tuningsystem:server:setMarkup', point.shopId, input[2])

                    Wait(100)
                    OpenBossMenu(point)
                end
            },
        },
    })
    lib.showContext('tuner_boss_menu')
end

RegisterNetEvent('tuningsystem:client:account', function(point)
    lib.registerContext({
        id = 'tuner_account',
        title = Lang['title_tuner_account'],
        onExit = function()
            usingMenu = false
            point.textUi = false
        end,
        menu = 'tuner_boss_menu',
        options = {
            {
                title = Lang['title_account_balance'],
                icon = 'sack-dollar',
                description = Config.Currency..Lib.CommaValue(Config.Shops[point.shopId].account),
            },
            {
                title = Lang['title_account_deposit'],
                icon = 'money-bill-trend-up',
                onSelect = function()
                    local input = lib.inputDialog(Lang['title_tuner_account'], {
                        {type = 'input', label = Lang['input_label_cur_balance'], icon = 'sack-dollar', disabled = true, default = Config.Shops[point.shopId].account},
                        {type = 'number', label = Lang['input_label_account_deposit_amount'], icon = 'money-bill-trend-up', placeholder = 100}
                    })
    
                    if not input then
                        return TriggerEvent('tuningsystem:client:account', point)
                    end
    
                    if input[2] == nil or input[2] == '' or input[2] == ' ' then
                        Core.Notification({
                            title = '',
                            message = Lang['input_required'],
                            type = 'error'
                        })
                        return TriggerEvent('tuningsystem:client:account', point)
                    end
    
                    if input[2] <= 0 then 
                        Core.Notification({
                            title = '',
                            message = Lang['input_amount_higher_0'],
                            type = 'error'
                        })
                        return TriggerEvent('tuningsystem:client:account', point)
                    end

                    TriggerServerEvent('tuningsystem:server:depositAccount', point.shopId, input[2])
                    Wait(100)
                    TriggerEvent('tuningsystem:client:account', point)
                end,
            },
            {
                title = Lang['title_account_withdraw'],
                icon = "money-bill-transfer",
                onSelect = function()
                    local input = lib.inputDialog(Lang['title_tuner_account'], {
                        {type = "input", label = Lang['input_label_cur_balance'], icon = 'sack-dollar', disabled = true, default = Config.Shops[point.shopId].account},
                        {type = "number", label = Lang['input_label_account_withdraw_amount'], icon = 'money-bill-transfer', placeholder = 50}
                    })
    
                    if not input then
                        return TriggerEvent('tuningsystem:client:account', point)
                    end
    
                    if input[2] == nil or input[2] == '' or input[2] == ' ' then
                        Core.Notification({
                            title = '',
                            message = Lang['input_required'],
                            type = 'error'
                        })
                        return TriggerEvent('tuningsystem:client:account', point)
                    end
    
                    if input[2] <= 0 then 
                        Core.Notification({
                            title = '',
                            message = Lang['input_amount_higher_0'],
                            type = 'error'
                        })
                        return TriggerEvent('tuningsystem:client:account', point)
                    end
                        
                    if input[2] > Config.Shops[point.shopId].account then
                        Core.Notification({
                            title = '',
                            message = Lang['account_withdraw_max'],
                            type = 'error'
                        })
                        return TriggerEvent('tuningsystem:client:account', point)
                    end
    
                    TriggerServerEvent('tuningsystem:server:withdrawAccount', point.shopId, input[2])
                    Wait(100)
                    TriggerEvent('tuningsystem:client:account', point)
                end,
            },
        },
    })
    lib.showContext('tuner_account')
end)

RegisterNetEvent('tuningsystem:client:employees', function(point)
    lib.registerContext({
        id = 'tuner_employees',
        title = Lang['title_tuner_employees'],
        onExit = function()
            usingMenu = false
            point.textUi = false
        end,
        menu = 'tuner_boss_menu',
        options = {
            {
                title = Lang['title_manage_employees'],
                icon = 'people-group',
                description = Lang['desc_manage_employees'],
                arrow = true, 
                args = point,
                event = 'tuningsystem:client:manageEmployees',
            },
            {
                title = Lang['title_recruit_employees'],
                icon = 'paper-plane',
                arrow = true, 
                args = point,
                event = 'tuningsystem:client:recruitEmployees',
            },
        },
    })
    lib.showContext('tuner_employees')
end)

RegisterNetEvent('tuningsystem:client:manageEmployees', function(point)
    local menuOptions = {}
    if Config.Shops[point.shopId].employees and next(Config.Shops[point.shopId].employees) then
        for k,v in pairs(Config.Shops[point.shopId].employees) do
            table.insert(menuOptions, {
                title = v.name,
                icon = 'user',
                args = {player = v, shopId = point.shopId, employees = Config.Shops[point.shopId].employees, point = point},
                metadata = {
                    {label = Lang['meta_job_grade'], value = Config.Shops[point.shopId].job.grades[tostring(v.grade)].label..' ['..v.grade..']'},
                    {label = Lang['meta_identifier'], value = v.identifier},
                },
                arrow = true,
                event = 'tuningsystem:client:manageThisEmployee',
            })
        end
        lib.registerContext({
            id = 'manage_employees',
            title = Lang['title_manage_employees'],
            onExit = function()
                usingMenu = false
                point.textUi = false
            end,
            menu = 'tuner_employees',
            options = menuOptions,
        })
        lib.showContext('manage_employees')
    end
end)

RegisterNetEvent('tuningsystem:client:manageThisEmployee', function(args)
    local menuOptions = {}

    table.insert(menuOptions, {
        title = Lang['title_fire_employee'],
        icon = 'ban',
        description = Lang['desc_fire_employee'],
        onSelect = function()
            if args.player.identifier == Config.Shops[args.shopId].boss then
                Core.Notification({
                    title = '',
                    message = Lang['cannot_fire_boss'],
                    type = 'error'
                })
                return TriggerEvent('tuningsystem:client:manageThisEmployee', args)
            else
                TriggerServerEvent('tuningsystem:server:fireEmployee', args.shopId, args.player)
                Wait(100)
                TriggerEvent('tuningsystem:client:manageEmployees', args.point)
            end
        end,
    })

    table.insert(menuOptions, {title = Lang['title_promote_emplotee'], icon = 'wrench', description = Lang['desc_promote_employee'], args = args, event = 'tuningsystem:client:promoteEmployee'})

    lib.registerContext({
        id = 'manage_this_employee',
        title = args.player.name,
        onExit = function()
            usingMenu = false
            args.textUi = false
        end,
        menu = 'manage_employees',
        options = menuOptions,
    })
    lib.showContext('manage_this_employee')
end)

RegisterNetEvent('tuningsystem:client:promoteEmployee', function(args)
    local menuOptions = {}
    for k,v in pairs(Config.Shops[args.shopId].job.grades) do
        if v.isboss == nil or v.isboss == 0 or (v.isboss ~= nil and v.isboss == false) then
            if args.player.grade ~= v.grade then 
                table.insert(menuOptions, {
                    title = v.label,
                    metadata = {
                        {label = Lang['meta_job_grade'], value = v.grade},
                        {label = Lang['meta_job_name'], value = v.name},
                    },
                    onSelect = function()
                        if args.player.identifier == Config.Shops[args.shopId].boss then
                            Core.Notification({
                                title = '',
                                message = Lang['cannot_demote_boss'],
                                type = 'error'
                            })
                            return TriggerEvent('tuningsystem:client:manageThisEmployee', args)
                        else
                            TriggerServerEvent('tuningsystem:server:promoteEmployee', args.shopId, args.player, v.grade)
                            Wait(100)
                            TriggerEvent('tuningsystem:client:manageEmployees', args.point)
                        end
                    end,
                })
            end
        end
    end
    lib.registerContext({
        id = 'promote_employee',
        title = args.player.name,
        onExit = function()
            usingMenu = false
            args.textUi = false
        end,
        menu = 'manage_employees',
        options = menuOptions,
    })
    lib.showContext('promote_employee')
end)

RegisterNetEvent('tuningsystem:client:recruitEmployees', function(point)
    local menuOptions = {}
    local players = Lib.GetPlayersInArea(GetEntityCoords(PlayerPedId()), Config.RecruitMember.Distance)
    for i = 1, #players do
        local fullName = Core.GetFullName(GetPlayerServerId(players[i]))
        local menuTitle = fullName..' ['..GetPlayerServerId(players[i])..']'
        if Config.RecruitMember.ShowFullName == false then
            menuTitle = '['..GetPlayerServerId(players[i])..']'
        end
        table.insert(menuOptions, {
            title = menuTitle,
            icon = 'user',
            description = Lang['desc_recruit_employee'],
            onSelect = function()
                TriggerServerEvent('tuningsystem:server:tryRecruit', point, {name = fullName, serverId = GetPlayerServerId(players[i])})
                TriggerEvent('tuningsystem:client:employees', point)
            end,
        })
    end
    if #menuOptions <= 0 then
        Core.Notification({
            title = '',
            message = Lang['no_players_nearby'],
            type = 'error'
        })
        return TriggerEvent('tuningsystem:client:employees', point)
    end
    lib.registerContext({
        id = 'recruit_employee',
        title = Lang['title_recruit_employees'],
        menu = 'tuner_employees',
        onExit = function()
            usingMenu = false
            point.textUi = false
        end,
        onBack = function()
            TriggerEvent('tuningsystem:client:employees', point)
        end,
        options = menuOptions,
    })
    lib.showContext('recruit_employee')
end)

RegisterNetEvent('tuningsystem:client:sendRecruitment')
AddEventHandler('tuningsystem:client:sendRecruitment', function(point, args, playerSrc)
    lib.registerContext({
        id = 'shop_recruitment',
        title = Lang['title_accept_recruitment']:format(Config.Shops[point.shopId].name),
        canClose = false,
        options = {
            {
                title = Lang['title_yes'],
                icon = 'check',
                onSelect = function()
                    TriggerServerEvent('tuningsystem:server:recruitmentRespond', point, args, true, playerSrc)
                    Core.Notification({
                        title = '',
                        message = Lang['recruitment_accepted'],
                        type = 'success'
                    })
                end
            },
            {
                title = Lang['title_no'],
                icon = 'ban',
                onSelect = function()
                    TriggerServerEvent('tuningsystem:server:recruitmentRespond', point, args, false, playerSrc)
                    Core.Notification({
                        title = '',
                        message = Lang['recruitment_declined'],
                        type = 'inform'
                    })
                end
            },
        },
    })
    lib.showContext('shop_recruitment')
end)
