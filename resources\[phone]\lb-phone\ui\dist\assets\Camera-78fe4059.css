:root{--phone-color-primary: rgb(255, 255, 255);--phone-color-opacity: rgba(242, 242, 242, .4);--phone-color-opacity2: rgb(30, 30, 30, .5);--phone-color-highlight: rgb(250, 250, 250);--phone-color-highlight2: rgb(240, 240, 240);--phone-color-highlight3: rgb(220, 220, 220);--phone-highlight-opacity15: rgba(145, 145, 145, .15);--phone-highlight-opacity35: rgba(145, 145, 145, .35);--phone-highlight-opacity45: rgba(145, 145, 145, .45);--phone-highlight-opacity55: rgba(145, 145, 145, .55);--phone-color-input: rgba(241, 241, 241, .656);--phone-text-primary: rgb(0, 0, 0);--phone-text-secondary: rgb(142, 142, 147);--phone-color-hover: rgb(240, 240, 240);--phone-color-border: rgba(200, 200, 200, .4);--phone-color-grey: #8e8e93;--phone-color-blue: #0a84ff;--phone-color-green: #32d74b;--phone-color-green-secondary: #092911;--phone-color-red: #ff3b30;--phone-color-orange: rgb(255, 157, 10);--phone-color-yellow: #cca250;--phone-color-pink: #ff3b30;--instagram-primary: #ffffff;--instagram-blue: rgb(0, 149, 246);--instagram-red: rgb(237, 73, 86);--instagram-primary-text: rgb(38, 38, 38);--instagram-secondary-text: rgb(142, 142, 142);--instagram-stroke: rgb(219, 219, 219);--instagram-border: rgb(219, 219, 219);--instagram-highlight: rgb(239, 239, 239);--tinder-color-pink: #ff4573;--tinder-color-orange: #ff5f65;--tinder-color-mix: #f5547c;--twitter-primary: #f5f8fa;--twitter-secondary: #14171a;--twitter-background-highlight: rgb(239, 243, 244);--twitter-primary-text: #14171a;--twitter-secondary-text: #657786;--twitter-alt-text: #657786;--twitter-border: #bdc5cd75;--twitter-border-secondary: #1d9bf0;--twitter-highlight: #1d9bf0;--twitter-hover: rgba(15, 20, 25, .1);--twitter-action: #14171a;--twitter-blue: #1d9bf0;--tiktok-primary: #ffffff;--tiktok-secondary: #000000;--tiktok-text-primary: #000000;--tiktok-text-secondary: #86878b;--tiktok-color-border: #d0d1d3;--tiktok-color-pink: #fe2c55;--tiktok-color-aqua: #00f2ea;--tiktok-color-yellow: #f8cd14;--tiktok-color-blue: #479fc5;--tiktok-color-unread: rgba(254, 44, 86, .2);--crypto-color-primary: rgb(255, 255, 255);--browser-primary: rgb(245, 245, 245);--browser-secondary: rgba(153, 153, 153, .15);--browser-gradient: linear-gradient(230deg, #f4d6ff, #c5f1ff);--browser-footer: rgba(255, 255, 255, .75);--browser-border: rgba(102, 102, 102, .75);--browser-text-secondary: #696969;--controlcentre-opacity: rgba(255, 255, 255, .15);--controlcentre-opacity2: rgba(255, 255, 255, .2);--controlcentre-active: rgba(255, 255, 255, .5);--notification-primary: rgba(215, 215, 215, .5);--notification-secondary: rgba(215, 215, 215, .1);--lockscreeneditor-background: rgba(255, 255, 255, .75);--lockscreeneditor-secondary: #d9d9d9;--app-bg: #ececec;--app-bg2: #ffffff;--app-secondary: #ffffff;--app-secondary2: #ececec;--app-highlight: #cccccc;--app-highlight2: #999999;--app-highlight3: #ffffff;--app-border: #666666;--app-slider: #cccccc;--app-slider-active: #333333;--app-button: #ffffff;--components-bg: #eeeeee;--components-secondary: #ffffff;--components-highlight: #cccccc}[data-theme=dark]{--phone-color-primary: #000000;--phone-color-opacity: rgb(30, 30, 30, .5);--phone-color-opacity2: rgba(242, 242, 242, .4);--phone-color-highlight: rgb(15, 15, 15);--phone-color-highlight2: rgb(20, 20, 20);--phone-color-highlight3: rgb(25, 25, 25);--phone-highlight-opacity35: rgba(145, 145, 145, .35);--phone-highlight-opacity45: rgba(50, 50, 50, .6);--phone-highlight-opacity55: rgb(60, 60, 60, .8);--phone-color-input: rgba(60, 60, 67, .6);--phone-text-primary: #f2f2f7;--phone-text-secondary: #6f6f6f;--phone-color-grey: #636366;--phone-color-hover: rgb(30, 30, 30);--phone-color-border: rgba(150, 150, 150, .2);--phone-color-blue: #076bcf;--instagram-primary: #000000;--instagram-blue: rgb(0, 149, 246);--instagram-red: rgb(237, 73, 86);--instagram-primary-text: rgb(250, 250, 250);--instagram-secondary-text: rgb(142, 142, 142);--instagram-stroke: rgb(219, 219, 219);--instagram-border: rgb(54, 54, 54);--instagram-highlight: rgb(38, 38, 38);--twitter-primary: #000000;--twitter-secondary: #f5f8fa;--twitter-background-highlight: rgb(20, 20, 20);--twitter-primary-text: #f5f8fa;--twitter-secondary-text: #aab8c2;--twitter-alt-text: #657786;--twitter-border: #38444d;--twitter-border-secondary: #38444d;--twitter-hover: rgba(150, 150, 150, .1);--twitter-highlight: #dcdcdc;--twitter-action: #1d9bf0;--twitter-blue: #1d9bf0;--crypto-color-primary: rgb(24, 26, 32);--tiktok-text-primary: #f2f2f7;--tiktok-text-secondary: #6f6f6f;--tiktok-color-border: #96969633;--browser-primary: rgb(15, 15, 15);--browser-secondary: rgba(153, 153, 153, .15);--browser-gradient: linear-gradient(230deg, #453b48, #2f393d);--browser-footer: rgba(51, 51, 51, .75);--browser-border: rgba(102, 102, 102, .75);--browser-text-secondary: #999999;--controlcentre-opacity: rgba(0, 0, 0, .15);--controlcentre-opacity2: rgba(0, 0, 0, .2);--controlcentre-active: rgba(0, 0, 0, .5);--notification-primary: rgba(0, 0, 0, .1);--notification-secondary: rgba(0, 0, 0, .12);--lockscreeneditor-background: rgba(0, 0, 0, .8);--lockscreeneditor-secondary: #333333;--app-bg: #000000;--app-bg2: #000000;--app-secondary: #141414;--app-secondary2: #141414;--app-highlight: #cccccc;--app-highlight2: #696969;--app-highlight3: #212121;--app-border: #cccccc;--app-slider: #999999;--app-slider-active: #ffffff;--app-button: #333333;--components-bg: #000000;--components-secondary: #141414;--components-highlight: #696969}.camera-container{height:100%;width:100%;display:flex;flex-direction:column;align-items:center;background-color:#000}.camera-container .camera-header{margin-top:10%;display:flex;align-items:center;justify-content:space-between;width:80%;height:7%}.camera-container .camera-header>div{flex:1}.camera-container .camera-header svg{color:#fff;font-size:22px;border:2px solid #ffffff;border-radius:50%;padding:.1rem;cursor:pointer;transition:all .2s ease-in-out}.camera-container .camera-header svg:hover{filter:brightness(.5)}.camera-container .camera-header .timer{color:#fff;font-size:22px;text-align:center}.camera-container .camera-header .grid-toggle{display:flex;justify-content:flex-end}.camera-container .camera-header .grid-toggle svg{border:none;border-radius:0}.camera-container .camera-body{width:90%;height:65%;display:flex;align-items:center;justify-content:center;overflow:hidden;position:relative}.camera-container .camera-body .camera-loading{position:absolute;height:100%;width:100%;display:flex;flex-direction:column;align-items:center;justify-content:center;gap:1rem;background-color:inherit;-webkit-backdrop-filter:brightness(.8) blur(5px);backdrop-filter:brightness(.8) blur(5px)}.camera-container .camera-body .camera-loading .uploading{font-size:18px;font-weight:500;color:#fff;text-align:center}.camera-container .camera-body canvas{max-height:100%}.camera-container .camera-body .grid{position:absolute;top:0;left:0;width:100%;height:100%;display:grid;grid-template-columns:repeat(3,1fr);grid-template-rows:repeat(3,1fr)}.camera-container .camera-body .grid .grid-item:nth-child(1),.camera-container .camera-body .grid .grid-item:nth-child(2),.camera-container .camera-body .grid .grid-item:nth-child(4),.camera-container .camera-body .grid .grid-item:nth-child(5),.camera-container .camera-body .grid .grid-item:nth-child(7),.camera-container .camera-body .grid .grid-item:nth-child(8){border-right:1px solid rgba(255,255,255,.5)}.camera-container .camera-body .grid .grid-item:nth-child(1),.camera-container .camera-body .grid .grid-item:nth-child(2),.camera-container .camera-body .grid .grid-item:nth-child(3),.camera-container .camera-body .grid .grid-item:nth-child(4),.camera-container .camera-body .grid .grid-item:nth-child(5),.camera-container .camera-body .grid .grid-item:nth-child(6){border-bottom:1px solid rgba(255,255,255,.5)}.camera-container .camera-body .quick-zoom{position:absolute;bottom:1.5rem;display:flex;align-items:center;justify-content:center;gap:.25rem;background-color:#0000001a;padding:.2rem .75rem;border-radius:1rem;min-height:2rem}.camera-container .camera-body .quick-zoom .item{border-radius:50%;background-color:#0000004d;display:flex;align-items:center;justify-content:center;width:1.35rem;aspect-ratio:1/1;font-size:11px;font-weight:500;color:#c8c8c8bf;cursor:pointer}.camera-container .camera-body .quick-zoom .item[data-active=true]{width:1.75rem;font-size:12px;color:var(--phone-color-blue)}.camera-container .camera-body.rotate canvas{transform:rotate(90deg)}.camera-container .camera-body.selfie canvas{transform:translate(15%)}.camera-container .camera-bottom{width:90%;display:flex;flex-direction:column;align-items:center;margin-top:1rem;overflow:hidden}.camera-container .camera-bottom .camera-types{display:flex;align-items:center;gap:1.25rem;transition:all .2s ease-in-out}.camera-container .camera-bottom .camera-types div{text-transform:uppercase;cursor:pointer;color:#fafafa;font-weight:500;font-size:17px}.camera-container .camera-bottom .camera-types div.active{color:var(--phone-color-blue);font-weight:600}.camera-container .camera-bottom .camera-buttons{margin-top:1rem;display:flex;align-items:center;justify-content:space-between;gap:4rem}.camera-container .camera-bottom .camera-buttons .image-gallery{width:3rem;height:3rem;border-radius:50%;border:2px solid #fafafa;cursor:pointer;background-position:center;background-size:cover;background-color:#0f0f0f}.camera-container .camera-bottom .camera-buttons .camera-button .camera-button-container{display:flex;align-items:center;justify-content:center;width:4.5rem;height:4.5rem;border-radius:50%;border:3px solid white}.camera-container .camera-bottom .camera-buttons .camera-button .camera-button-container .camera-button-inner{width:85%;height:85%;border-radius:50%;background-color:#fff;cursor:pointer;transition:all .2s ease-in-out}.camera-container .camera-bottom .camera-buttons .camera-button .camera-button-container .camera-button-inner.Video{background-color:var(--phone-color-red)}.camera-container .camera-bottom .camera-buttons .camera-button .camera-button-container .camera-button-inner.Recording{border-radius:10%;height:50%;width:50%}.camera-container .camera-bottom .camera-buttons .camera-button .camera-button-container .camera-button-inner:hover:not(.Recording){height:80%;width:80%;opacity:.9}.camera-container .camera-bottom .camera-buttons .flip-camera{display:flex;align-items:center;justify-content:center;height:3rem;width:3rem;color:#fafafa;font-size:16px;padding:.5rem;box-sizing:border-box;border:2px solid #fafafa;border-radius:50%;cursor:pointer}.camera-container .camera-bottom .camera-buttons .flip-camera svg{font-size:32px}
