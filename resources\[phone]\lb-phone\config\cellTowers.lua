CellTowers = {
    vector3(-338.79, -579.62, 48.09),
    vector3(-293.02, -632.12, 47.43),
    vector3(-269.23, -962.78, 143.51),
    vector3(98.89, -870.87, 136.92),
    vector3(-214.62, -744.65, 219.44),
    vector3(-166.72, -590.67, 199.08),
    vector3(124.30, -654.87, 261.86),
    vector3(149.28, -769.01, 261.86),
    vector3(580.18, 89.59, 117.33),
    vector3(423.65, 15.57, 151.92),
    vector3(424.92, 18.59, 151.93),
    vector3(552.00, -28.20, 93.86),
    vector3(305.86, -284.85, 68.30),
    vector3(299.49, -313.95, 68.30),
    vector3(1240.90, -1090.10, 44.36),
    vector3(-418.45, -2804.49, 14.81),
    vector3(802.34, -2996.21, 27.37),
    vector3(253.30, -3145.93, 39.41),
    vector3(207.65, -3145.93, 39.41),
    vector3(207.65, -3307.40, 39.52),
    vector3(247.34, -3307.40, 39.52),
    vector3(484.29, -2178.58, 40.25),
    vector3(548.35, -2219.76, 67.95),
    vector3(-701.22, 58.91, 68.69),
    vector3(-696.77, 208.70, 139.77),
    vector3(-769.82, 255.01, 134.74),
    vector3(-150.32, -150.25, 96.15),
    vector3(-202.97, -327.19, 65.05),
    vector3(-1913.77, -3031.85, 22.59),
    vector3(-1918.88, -3028.62, 22.61),
    vector3(-1039.82, -2385.44, 27.40),
    vector3(-1042.58, -2390.23, 27.40),
    vector3(-1583.46, -3216.81, 28.63),
    vector3(-1590.37, -3212.55, 28.66),
    vector3(-1308.23, -2626.37, 36.09),
    vector3(-1312.00, -2624.59, 36.12),
    vector3(-984.67, -2778.28, 48.29),
    vector3(-991.58, -2774.02, 48.31),
    vector3(-556.70, -119.85, 50.99),
    vector3(-619.08, -106.58, 51.01),
    vector3(-1167.27, -575.03, 40.20),
    vector3(-1152.41, -443.97, 42.89),
    vector3(-1156.08, -498.81, 49.32),
    vector3(-1290.01, -445.24, 106.47),
    vector3(-928.51, -383.13, 135.27),
    vector3(-902.81, -443.05, 170.82),
    vector3(-770.08, -786.34, 83.83),
    vector3(-824.31, -719.18, 120.25),
    vector3(-598.83, -917.81, 35.84),
    vector3(-678.52, -717.01, 54.10),
    vector3(-669.46, -804.25, 31.88),
    vector3(-1463.99, -526.12, 83.58),
    vector3(-1525.90, -596.80, 66.52),
    vector3(-1375.13, -465.26, 83.51),
    vector3(-1711.98, 478.33, 127.19),
    vector3(-2311.60, 335.44, 187.60),
    vector3(-2214.42, 342.21, 198.10),
    vector3(-2234.36, 187.02, 193.60),
    vector3(202.69, 1204.00, 230.26),
    vector3(217.06, 1140.44, 230.26),
    vector3(758.47, 1273.72, 405.94),
    vector3(668.78, 590.32, 136.99),
    vector3(722.25, 562.27, 134.29),
    vector3(838.17, 510.11, 138.66),
    vector3(773.17, 575.36, 138.42),
    vector3(735.45, 232.00, 145.14),
    vector3(450.93, 5566.45, 795.44),
    vector3(-449.06, 6019.92, 35.57),
    vector3(-142.56, 6286.78, 39.26),
    vector3(-368.05, 6105.01, 38.43),
    vector3(2792.25, 5996.05, 355.19),
    vector3(2796.77, 5992.87, 354.99),
    vector3(3460.88, 3653.53, 51.17),
    vector3(3459.18, 3659.83, 51.19),
    vector3(3615.94, 3642.95, 51.19),
    vector3(3614.59, 3636.56, 51.17),
    vector3(-2180.79, 3252.70, 54.33),
    vector3(-2124.38, 3219.85, 54.33),
    vector3(-2050.94, 3178.41, 54.33),
    vector3(1858.30, 3694.04, 37.91),
    vector3(1695.49, 3614.86, 37.80),
    vector3(715.30, 2582.81, 81.21),
    vector3(1692.83, 2532.07, 60.34),
    vector3(1692.83, 2647.94, 60.34),
    vector3(1824.35, 2574.39, 60.56),
    vector3(1407.91, 2117.49, 104.10),

    vector3(-886.51, -1920.89, 24.42),
    vector3(-320.3847, -1975.3879, 66.7448),
    vector3(2579.5579, 410.2249, 108.4568),
    vector3(2690.3967, 1642.7449, 24.5805),
    vector3(2255.2349, -527.5021, 95.5090),
    vector3(1182.3922, 6411.7212, 48.5860),
    vector3(-1356.5483, 4849.8281, 138.0838),
    vector3(-3008.5544, 1704.0256, 44.6332),
    vector3(-3094.9658, 863.9202, 36.5924),
    vector3(-1260.5933, -1533.5509, 4.3108),
    vector3(-836.7013, 850.1918, 202.8860),
    vector3(2026.7595, 4729.8237, 41.6302),
    vector3(2016.9045, 6270.7261, 46.3074),
    vector3(2601.1360, 5032.5552, 44.8804),
    vector3(2828.8787, 4156.5957, 48.2632),
    vector3(2467.6133, 3038.7227, 46.0666),
    vector3(1571.0028, 1233.8522, 96.9722),
    vector3(2598.7710, 1015.1808, 66.0674),
    vector3(963.7183, -1400.9333, 32.0790),
    vector3(1361.7966, -2325.5366, 62.3288),
    vector3(-2686.3491, 2308.4270, 19.2207),
    vector3(238.9034, 3375.5171, 39.9582),

    
    --Roxwood
    vector3(-1071.70, 7247.79, 123.78),
    vector3(-1710.41, 6823.52, 100.08),
    vector3(-3290.78, 7893.79, 62.78),
}
