ShopHandle = {}

Shops = {} -- Global table to store all shop instances

--- Converts job grades into a framework-specific format.
--- @param jobName string: The name of the job.
--- @param jobLabel string: The display label for the job.
--- @param jobGrades table: A list of job grades and their attributes.
--- @return table|nil: A table containing converted job grades, or nil if an error occurs.
function ShopHandle:ConvertJobGrades(jobName, jobLabel, jobGrades)
    if type(jobName) ~= "string" or jobName == "" then
        return error("^1[ShopHandle:ConvertJobGrades] Invalid job name!^7")
    end

    if type(jobLabel) ~= "string" or jobLabel == "" then
        return error("^1[ShopHandle:ConvertJobGrades] Invalid job label!^7")
    end

    if type(jobGrades) ~= "table" or not next(jobGrades) then
        return error("^1[ShopHandle:ConvertJobGrades] Job grades missing or invalid!^7")
    end

    local convertedGrades = {}
    local lastGrade = 0

    for grade, data in pairs(jobGrades) do
        -- Convert string indices to numeric where applicable
        local success, numericGrade = pcall(tonumber, grade)
        numericGrade = success and numericGrade or grade -- Ensure fallback
        
        if Framework == "esx" then
            convertedGrades[tostring(numericGrade)] = {
                job_name = jobName,
                grade = numericGrade,
                name = data.name or data.label or "unknownname",
                label = data.label or data.name or "Unknown Label",
                salary = data.salary or data.payment or 0,
                skin_male = data.skin_male or '{}',
                skin_female = data.skin_female or '{}'
            }
        elseif Framework == "qbcore" or Framework == "qbox" then
            local gradeData = {
                name = data.label or data.name or "Unlabeled",
                payment = data.salary or data.payment or 0
            }
            if Framework == "qbcore" then
                convertedGrades[tostring(numericGrade)] = gradeData
            elseif Framework == "qbox" then
                convertedGrades[numericGrade] = gradeData
            end
        end

        lastGrade = math.max(lastGrade, numericGrade)
    end

    -- Assign boss roles only if grades exist
    if next(convertedGrades) then
        if Framework == "qbcore" then
            convertedGrades[tostring(lastGrade)].isboss = true
        elseif Framework == "qbox" then
            convertedGrades[lastGrade].isboss = true
            convertedGrades[lastGrade].bankAuth = true
        end
    end

    return convertedGrades
end

--- Converts a job into a framework-compatible format.
--- @param jobName string: The name of the job.
--- @param jobLabel string: The display label for the job.
--- @param jobGrades table: A list of job grades and their attributes.
--- @param defaultDuty boolean|nil: Whether employees start on duty by default (QB/QBox only).
--- @param offDutyPay boolean|nil: Whether employees get paid while off duty (QB/QBox only).
--- @return table|nil: A table containing the converted job data, or nil if an error occurs.
function ShopHandle:ConvertJob(jobName, jobLabel, jobGrades, defaultDuty, offDutyPay)
    if type(jobName) ~= "string" or jobName == "" then
        return error("^1[ShopHandle:ConvertJob] Invalid job name!^7")
    end

    if type(jobLabel) ~= "string" or jobLabel == "" then
        return error("^1[ShopHandle:ConvertJob] Invalid job label!^7")
    end

    if type(jobGrades) ~= "table" or not next(jobGrades) then
        return error("^1[ShopHandle:ConvertJob] Job grades missing or invalid!^7")
    end

    local convertedGrades = self:ConvertJobGrades(jobName, jobLabel, jobGrades)
    if not convertedGrades then
        return error("^1[ShopHandle:ConvertJob] Failed to convert job grades!^7")
    end

    -- Initialize the converted job structure
    local convertedJob = {
        name = jobName,
        label = jobLabel,
        grades = convertedGrades
    }

    if Framework == "qbcore" or Framework == "qbox" then
        convertedJob.defaultDuty = defaultDuty or Config.Shop.Job.defaultDuty or true
        convertedJob.offDutyPay = offDutyPay or Config.Shop.Job.offDutyPay or false
    end

    return convertedJob
end

--- Ensures a job exists in the framework.
--- If the job already exists, it retrieves it.
--- If the job does not exist, it determines whether to create a new job (for new shops) or use existing data (for shops being loaded).
--- @param jobName string The unique name of the job.
--- @param jobLabel string The display label for the job.
--- @param jobGrades? table (Optional) The job grades. This is only present for existing shops.
--- @param defaultDuty? boolean (Optional) Whether the job starts on duty by default.
--- @param offDutyPay? boolean (Optional) Whether the job has off-duty pay.
--- @return table job Returns the job in a framework-compatible format.
function ShopHandle:EnsureJob(jobName, jobLabel, jobGrades, defaultDuty, offDutyPay)
    if type(jobName) ~= "string" or jobName == "" then
        return error("^1[ShopHandle:EnsureJob] Invalid jobName. Expected a non-empty string!^7")
    end

    if type(jobLabel) ~= "string" or jobLabel == "" then
        return error("^1[ShopHandle:EnsureJob] Invalid jobLabel. Expected a non-empty string!^7")
    end

    -- If job already exists in the framework, return it
    if _API.DoesJobExist(jobName) then
        local job = _API.GetJob(jobName)
        job.name = jobName
        return job
    end

    -- Determine if it's a new shop creation or an existing shop being loaded
    local newJob
    if jobGrades and type(jobGrades) == "table" then
        -- Existing shop being loaded → Use job data from the database
        newJob = ShopHandle:ConvertJob(jobName, jobLabel, jobGrades, defaultDuty or Config.Shop.Job.defaultDuty, offDutyPay or Config.Shop.Job.offDutyPay)
    else
        -- New shop creation → Convert job into framework-compatible format
        newJob = ShopHandle:ConvertJob(jobName, jobLabel, Config.Shop.Job.defaultGrades, defaultDuty or Config.Shop.Job.defaultDuty, offDutyPay or Config.Shop.Job.offDutyPay)
    end

    -- Ensure newJob has valid attributes
    if not newJob or not newJob.name or not newJob.label or not newJob.grades then
        return error("ShopHandle:EnsureJob - Failed to generate valid job data.")
    end

    -- Register job in the framework
    local success = _API.CreateJob(newJob.name, newJob.label, newJob.grades, defaultDuty or Config.Shop.Job.defaultDuty, offDutyPay or Config.Shop.Job.offDutyPay)
    if not success then 
        return error(("ShopHandle:EnsureJob - Failed to create job: '%s'"):format(newJob.name))
    end

    -- Return the newly created job in a framework-compatible format
    local job = _API.GetJob(newJob.name)
    job.name = newJob.name
    return job
end

--- Ensures that a job account exists for the specified shop job and returns its balance.
--- If the JobAccount system is enabled (`Config.Shop.JobAccount = true`), the function will:
--- - Retrieve the existing job account balance if the account exists.
--- - Create a new job account with the provided `startBalance` if no account exists.
--- - Return the current balance of the job account.
--- If the JobAccount system is disabled (`Config.Shop.JobAccount = false`), the function simply returns `startBalance`,
--- and the shop database table’s `account` column is used instead.
--- @param jobName string The name of the job associated with the shop.
--- @param startBalance number The initial balance to set if creating a new job account.
--- @return number The balance of the job account or the provided `startBalance` if JobAccount is disabled.
function ShopHandle:EnsureJobAccount(jobName, startBalance)

    -- if not using job account system, just return the startBalance
    if not Config.Shop.JobAccount then
        return startBalance
    end

    if type(jobName) ~= "string" or jobName == "" then
        return error("ShopHandle:EnsureJobAccount - Invalid jobName. Expected a non-empty string.")
    end

    local jobAccount = _API.JobAccount.Get(jobName)
    if not jobAccount then 
        local success = _API.JobAccount.Create(jobName, startBalance)
        if not success then
            return error(("ShopHandle:EnsureJobAccount - Failed to create job account for: '%s'"):format(jobName))
        end
    end
    
    return _API.JobAccount.GetBalance(jobName)
end

--- Converts job data into a standardized format for internal use.
--- @param jobName string: The unique name of the job.
--- @param jobLabel string: The display label for the job.
--- @param jobGrades table: A list of job grades from the database.
--- @param defaultDuty boolean|nil: Whether employees start on duty by default (QB/QBox only).
--- @param offDutyPay boolean|nil: Whether employees get paid while off duty (QB/QBox only).
--- @return table: A standardized job table with a consistent structure.
function ShopHandle:StandardizeJob(jobName, jobLabel, jobGrades, defaultDuty, offDutyPay)
    
    if type(jobName) ~= "string" or jobName == "" then
        return error("^StandardizeJob: Invalid job name!^7")
    end

    if type(jobLabel) ~= "string" or jobLabel == "" then
        return error("^StandardizeJob: Invalid job label!^7")
    end

    if type(jobGrades) ~= "table" or not next(jobGrades) then
        return error("^StandardizeJob: Job grades missing or invalid!^7")
    end

    local job = {
        name = jobName,
        label = jobLabel,
        grades = {}
    }

    for grade, data in pairs(jobGrades) do
        -- Convert string indices to numeric where applicable
        local success, numericGrade = pcall(tonumber, grade)
        numericGrade = success and numericGrade or grade -- Ensure fallback

        job.grades[tostring(numericGrade)] = {
            grade = numericGrade,
            label = data.label or data.name or "Unknown" -- Ensure a valid label
        }
    end

    return job
end

--- Initializes all mechanic shops on server start or restart.
--- - Fetches all shops from the database.
--- - Ensures the associated job exists and updates it if necessary.
--- - Ensures the job account exists or creates a new one.
--- - Creates shop instances and stores them in the `Shops` table.
function ShopHandle:Init()
    -- Fetch all shops from the database
    local shops = MySQL.query.await("SELECT * FROM t1ger_mechanic")

    if not shops or #shops == 0 then
        return
    end

    local function deepEqual(t1, t2)
        if type(t1) ~= "table" or type(t2) ~= "table" then return t1 == t2 end
    
        for k, v in pairs(t1) do
            if not deepEqual(v, t2[k]) then return false end
        end
        for k in pairs(t2) do
            if t1[k] == nil then return false end
        end
        return true
    end

    for _, shop in pairs(shops) do
        -- Decode job data from the database
        local decodedJob = json.decode(shop.job)
        if not decodedJob or type(decodedJob) ~= "table" or not decodedJob.name then
            error(("[ShopHandle:Init] Invalid job data for shop ID: %d. Please check your database."):format(shop.id))
        end

        -- Ensure the job exists or create/update it
        local job = ShopHandle:EnsureJob(decodedJob.name, decodedJob.label, decodedJob.grades, decodedJob.defaultDuty, decodedJob.offDutyPay)
        if not job then
            error(("[ShopHandle:Init] Failed to ensure job for shop ID: %d. Please verify job settings."):format(shop.id))
        end

        -- Update shop's job data in the database if it has changed
        if job.name ~= decodedJob.name or not deepEqual(job.grades, decodedJob.grades) then
            MySQL.update.await("UPDATE t1ger_mechanic SET job = ? WHERE id = ?", { json.encode(job), shop.id })
            if Config.Debug then
                print(("^3[ShopHandle:Init] Updated job data for shop ID: %d^7"):format(shop.id))
            end
        end

        if GetResourceState("esx_society") == "started" then
            TriggerEvent("esx_society:registerSociety", job.name, job.label, "society_"..job.name, "society_"..job.name, "society_"..job.name, {type = "private"})
        end

        -- Ensure the job account exists or create a new one
        local account = ShopHandle:EnsureJobAccount(decodedJob.name, shop.account)
        if not account then
            error(("^1[ShopHandle:Init] Failed to ensure job account for shop ID: %d. Please check account configurations.^7"):format(shop.id))
        end

        -- Standardize job format and create shop instance
        shop.job = ShopHandle:StandardizeJob(job.name, job.label, job.grades, job.defaultDuty, job.offDutyPay)

        -- JSON decode data:
        shop.blip, shop.employees, shop.markers, shop.billing = json.decode(shop.blip), json.decode(shop.employees), json.decode(shop.markers), json.decode(shop.billing)

        -- Register Stash:
        if Config.Shop.Markers['storage'].enable and next(shop.markers) then
            if shop.markers['storage'] ~= nil and next(shop.markers['storage']) then
                for markerId, markerData in pairs(shop.markers['storage']) do
                    local stash = markerData.stash
                    _API.Stash.Register(stash.id, stash.label, stash.slots, stash.weight, nil)
                end
            end
        end

        mechanicJobs[job.name] = true

        Shops[shop.id] = Shop:New(shop.id, shop)
    end
end

--- Creates a new mechanic shop, saves it in the database, and initializes a shop instance.
--- This function ensures that the required job, blip, and shop configurations are correctly set up.
--- @param data table A table containing shop creation details:
---     data.name string - The name of the shop.
---     data.job table - A table containing job-related data:
---         data.job.name string - The name of the associated job (Sanitized).
---         data.job.label string - The display label for the job.
---     data.account number - The starting balance for the shop account (defaults to 0 if not provided).
---     data.sale_price number - The price for which the shop is available for sale.
---     data.for_sale boolean - Whether the shop is currently for sale.
---     data.blip table - A table containing blip settings:
---         data.blip.enable boolean - Whether the blip is enabled.
---         data.blip.coords vector3 - The coordinates where the shop blip will be displayed.
---         data.blip.sprite number - The sprite ID for the shop blip (defaults to config value if not provided).
---         data.blip.color number - The color ID for the shop blip (defaults to config value if not provided).
--- @return table shopInstance The created shop instance.
function ShopHandle:Create(data)
    -- Validate input data
    if type(data) ~= "table" then
        error("ShopHandle:Create - Invalid data type. Expected table.")
    end
    if type(data.name) ~= "string" then
        error("ShopHandle:Create - Invalid shop name. Expected non-empty string.")
    end
    if type(data.job) ~= "table" then
        error("ShopHandle:Create - Invalid job data. Expected table.")
    end
    if type(data.job.name) ~= "string" or data.job.name == "" then
        error("ShopHandle:Create - Invalid job name. Expected a non-empty string.")
    end
    if type(data.job.label) ~= "string" or data.job.label == "" then
        error("ShopHandle:Create - Invalid job label. Expected a non-empty string.")
    end
    if Config.Shop.Sale and type(data.sale_price) ~= "number" then
        error("ShopHandle:Create - Invalid sale price. Expected number.")
    end
    if Config.Shop.Sale and type(data.for_sale) ~= "boolean" then
        error("ShopHandle:Create - Invalid for_sale flag. Expected boolean.")
    end
    if type(data.blip) ~= "table" then
        error("ShopHandle:Create - Invalid blip data. Expected table.")
    end

    -- Ensure required defaults for missing optional fields
    data.account = data.account or 0
    data.employees = data.employees or {}
    data.markers = data.markers or {}
    data.billing = data.billing or {}

    -- Ensure Job:
    local job = ShopHandle:EnsureJob(data.job.name, data.job.label)
    if not job then 
        return error(("ShopHandle:Create - Failed to ensure job '%S'"):format(data.job.name))
    end

    -- Ensure Job Account:
    local accountBalance = ShopHandle:EnsureJobAccount(data.job.name, data.account)
    if not accountBalance then 
        return error(("ShopHandle:Create - Failed to ensure job acount for '%S'"):format(data.job.name))
    end

    -- Insert shop into the database
    local shopId = MySQL.insert.await("INSERT INTO t1ger_mechanic (name, job, blip, account, employees, markers, billing, sale_price, for_sale) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)", {
        data.name,
        json.encode(job),
        json.encode(data.blip),
        accountBalance,
        json.encode(data.employees),
        json.encode(data.markers),
        json.encode(data.billing),
        data.sale_price,
        data.for_sale and 1 or 0
    })

    -- Ensure shop creation was successful
    if not shopId then
        error("ShopHandle:Create - Database error: Failed to insert shop into the database.")
    end

    data.id = shopId  -- Manually adding the ID to data
    data.job = self:StandardizeJob(job.name, job.label, job.grades)

    mechanicJobs[job.name] = true

    -- Instantiate a new Shop object and store in Shops table
    Shops[data.id] = Shop:New(data.id, data)

    -- Debugging Output
    if Config.Debug then
        print("Shop Created with ID: "..data.id)
        print(json.encode(Shops[data.id], {indent = true}))
    end

    return Shops[data.id]
end

--- Deletes a shop from the database and removes it from memory.
--- @param shopId (number) The ID of the shop to be deleted.
--- @return (boolean) Returns `true` if the shop was successfully deleted.
function ShopHandle:Delete(shopId)
    -- Ensure the given shop ID exists in the Shops table
    if not Shops[shopId] then
        error(("ShopHandle:Delete - The given shopId: %s is not initialized with a shop!"):format(shopId)) 
    end

    -- Delete the shop from the database
    local deleted = MySQL.update.await("DELETE FROM t1ger_mechanic WHERE id = ?", { shopId })
    if not deleted then 
        error(("ShopHandle:Delete - Database error, could not delete shop for ID: %s"):format(shopId)) 
    end

    local jobName = Shops[shopId].job.name
    mechanicJobs[jobName] = nil

    -- Remove shop from memory
    Shops[shopId] = nil

    return true
end

--- Cancels a shop from being listed as for_sale.
--- @param shopId (number) The ID of the shop
--- @return (boolean) Returns `true` if the sale was cancelled.
function ShopHandle:CancelSale(shopId)
    -- Ensure the given shop ID exists in the Shops table
    if not Shops[shopId] then
        error(("ShopHandle:CancelSale - The given shopId: %s is not initialized with a shop!"):format(shopId)) 
    end
    
    return Shops[shopId]:CancelSale()
end

--- Lists a shop for sale.
--- @param shopId (number) The ID of the shop
--- @return (boolean) Returns `true` if the sale was listed successfully. `false` otherwise.
function ShopHandle:ListForSale(shopId, price)
    -- Ensure the given shop ID exists in the Shops table
    if not Shops[shopId] then
        error(("ShopHandle:ListForSale - The given shopId: %s is not initialized with a shop!"):format(shopId)) 
    end

    if type(price) ~= "number" or price < 0 then
        error("ShopHandle:ListForSale - The given price must be of number type and higher or equal than 0")
    end
    
    return Shops[shopId]:ListForSale(price)
end

--- Cancels a shop from being listed as for_sale.
--- @param shopId (number) The ID of the shop
--- @param buyerSrc (number) Buying player's server id/source.
--- @return (boolean) Returns `true` if the purchase was success.
function ShopHandle:BuyListedShop(shopId, buyerSrc)
    -- Ensure the given shop ID exists in the Shops table
    if not Shops[shopId] then
        error(("ShopHandle:CancelSale - The given shopId: %s is not initialized with a shop!"):format(shopId)) 
    end
    
    return Shops[shopId]:BuyShop(buyerSrc)
end

--- Checks if the player is an employee of a specific shop or any shop.
---@param src number Player source ID
---@param shopId number|nil Optional shop ID to check
---@return boolean, number|nil (true/false) Returns boolean and shop ID if the player is an employee, otherwise false
function ShopHandle:IsPlayerEmployee(src, shopId)
    if not src or not Shops then return false end

    local playerIdentifier = _API.Player.GetIdentifier(src)
    if not playerIdentifier then return false end

    if shopId then
        -- Check only the specified shop
        local shopData = Shops[shopId]
        if shopData and type(shopData.employees) == "table" then
            for _, emp in pairs(shopData.employees) do
                if emp.identifier == playerIdentifier then
                    return true, shopId
                end
            end
        end
    else
        -- Check all shops
        for id, shopData in pairs(Shops) do
            if type(shopData.employees) == "table" then
                for _, emp in pairs(shopData.employees) do
                    if emp.identifier == playerIdentifier then
                        return true, id
                    end
                end
            end
        end
    end

    return false
end
exports("IsPlayerEmployee", function(src, shopId)
    return ShopHandle:IsPlayerEmployee(src, shopId)
end)

--- Checks if the player's job is a boss-grade mechanic in a specific shop or any shop.
---@param src number Player source ID
---@param shopId number|nil Optional shop ID to check
---@return boolean, number|nil Returns true and shop ID if the player is a mechanic boss, otherwise false
function ShopHandle:IsPlayerMechanicBoss(src, shopId)
    if not src or not Shops then return false end

    local playerJob = _API.Player.GetJob(src)
    if not playerJob or not playerJob.name then return false end

    if shopId then
        -- Check only the specified shop
        local shopData = Shops[shopId]
        if shopData and shopData.job and shopData.job.name == playerJob.name then
            local bossGrade = Shops[shopId]:GetBossGrade()
            if bossGrade and playerJob.grade >= bossGrade then
                return true, shopId
            end
        end
    else
        -- Check all shops
        for id, shopData in pairs(Shops) do
            if shopData.job and shopData.job.name == playerJob.name then
                local bossGrade = Shops[id]:GetBossGrade()
                if bossGrade and playerJob.grade >= bossGrade then
                    return true, id
                end
            end
        end
    end

    return false
end
exports("IsPlayerMechanicBoss", function(src, shopId)
    return ShopHandle:IsPlayerMechanicBoss(src, shopId)
end)

--- Checks if the player is the owner of a specific shop or any shop.
---@param src number Player source ID
---@param shopId number|nil Optional shop ID to check
---@return boolean, number|nil Returns true and shop ID if the player is the owner, otherwise false
function ShopHandle:IsPlayerShopOwner(src, shopId)
    if not src or not Shops then return false end

    local playerIdentifier = _API.Player.GetIdentifier(src)
    if not playerIdentifier then return false end

    if shopId then
        -- Check only the specified shop
        local shopData = Shops[shopId]
        if shopData and shopData.owner and shopData.owner == playerIdentifier then
            return true, shopId
        end
    else
        -- Check all shops
        for id, shopData in pairs(Shops) do
            if shopData.owner and shopData.owner == playerIdentifier then
                return true, id
            end
        end
    end

    return false
end
exports("IsPlayerShopOwner", function(src, shopId)
    return ShopHandle:IsPlayerShopOwner(src, shopId)
end)

--- Adds an employee to a shop.
---@param shopId number Shop ID
---@param jobGrade number Job grade
---@param setJob boolean|nil Whether to set the player's job
---@param srcOrIdentifier number|string Player source or identifier
---@return boolean success
function ShopHandle:AddEmployee(shopId, jobGrade, setJob, srcOrIdentifier)
    -- Validate Shop
    if not Shops[shopId] then
        return error(string.format("[ShopHandle:AddEmployee] Shop does not exist with the given shopId: '%s'", shopId))
    end

    -- Validate Job Grade
    if type(jobGrade) ~= "number" then
        return error("[ShopHandle:AddEmployee] Invalid jobGrade. Expected a number")
    end

    -- If src is provided, convert to identifier; otherwise, assume it's already an identifier
    local identifier = (type(srcOrIdentifier) == "number" and srcOrIdentifier > 0) and _API.Player.GetIdentifier(srcOrIdentifier) or srcOrIdentifier
    if not identifier or type(identifier) ~= "string" then
        return error("[ShopHandle:AddEmployee] Invalid srcOrIdentifier parameter. Expected src(number) or identifier(string)")
    end

    return Shops[shopId]:AddEmployee(identifier, jobGrade, setJob)
end

exports("AddEmployee", function(shopId, jobGrade, setJob, srcOrIdentifier)
    if not srcOrIdentifier then
        return error("[exports:AddEmployee] Invalid srcOrIdentifier parameter. Expected src(number) or identifier(string)")
    end
    return ShopHandle:AddEmployee(shopId, jobGrade, setJob, srcOrIdentifier)
end)

RegisterNetEvent("t1ger_mechanic:server:addEmployee", function(shopId, jobGrade, setJob, srcOrIdentifier)
    local src = source
    srcOrIdentifier = srcOrIdentifier or src  -- Use `source` if no param was given

    if not srcOrIdentifier then
        return error("[t1ger_mechanic:server:addEmployee] Invalid srcOrIdentifier parameter. Expected src(number) or identifier(string)")
    end

    ShopHandle:AddEmployee(shopId, jobGrade, setJob, srcOrIdentifier)
end)

--- Removes an employee from a shop.
---@param shopId number Shop ID
---@param setJob boolean|nil Whether to reset the player's job
---@param srcOrIdentifier number|string Player source or identifier
---@return boolean success
function ShopHandle:RemoveEmployee(shopId, setJob, srcOrIdentifier)
    -- Validate Shop
    if not Shops[shopId] then
        return error(string.format("[ShopHandle:RemoveEmployee] Shop does not exist with the given shopId: '%s'", shopId))
    end

    -- If src is provided, convert to identifier; otherwise, assume it's already an identifier
    local identifier = (type(srcOrIdentifier) == "number" and srcOrIdentifier > 0) and _API.Player.GetIdentifier(srcOrIdentifier) or srcOrIdentifier
    if not identifier or type(identifier) ~= "string" then
        return error("[ShopHandle:RemoveEmployee] Invalid srcOrIdentifier parameter. Expected src(number) or identifier(string)")
    end

    return Shops[shopId]:RemoveEmployee(identifier, setJob)
end

exports("RemoveEmployee", function(shopId, setJob, srcOrIdentifier)
    if not srcOrIdentifier then
        return error("[exports:RemoveEmployee] Invalid srcOrIdentifier parameter. Expected src(number) or identifier(string)")
    end
    return ShopHandle:RemoveEmployee(shopId, setJob, srcOrIdentifier)
end)

RegisterNetEvent("t1ger_mechanic:server:removeEmployee", function(shopId, setJob, srcOrIdentifier)
    local src = source
    srcOrIdentifier = srcOrIdentifier or src  -- Use `source` if no param was given

    if not srcOrIdentifier then
        return error("[t1ger_mechanic:server:removeEmployee] Invalid srcOrIdentifier parameter. Expected src(number) or identifier(string)")
    end

    ShopHandle:RemoveEmployee(shopId, setJob, srcOrIdentifier)
end)

--- Promotes or demotes an employee in a shop.
---@param shopId number Shop ID
---@param jobGrade number New job grade
---@param setJob boolean|nil Whether to set the player's job
---@param srcOrIdentifier number|string Player source or identifier
---@return boolean success
function ShopHandle:UpdateEmployee(shopId, jobGrade, setJob, srcOrIdentifier)
    -- Validate Shop
    if not Shops[shopId] then
        return error(string.format("[ShopHandle:UpdateEmployee] Shop does not exist with the given shopId: '%s'", shopId))
    end

    -- Validate Job Grade
    if type(jobGrade) ~= "number" then
        return error("[ShopHandle:UpdateEmployee] Invalid jobGrade. Expected a number")
    end

    -- If src is provided, convert to identifier; otherwise, assume it's already an identifier
    local identifier = (type(srcOrIdentifier) == "number" and srcOrIdentifier > 0) and _API.Player.GetIdentifier(srcOrIdentifier) or srcOrIdentifier

    if not identifier or type(identifier) ~= "string" then
        return error("[ShopHandle:UpdateEmployee] Invalid srcOrIdentifier parameter. Expected src(number) or identifier(string)")
    end

    return Shops[shopId]:UpdateEmployee(identifier, jobGrade, setJob)
end

exports("UpdateEmployee", function(shopId, jobGrade, setJob, srcOrIdentifier)
    if not srcOrIdentifier then
        return error("[exports:UpdateEmployee] Invalid srcOrIdentifier parameter. Expected src(number) or identifier(string)")
    end
    return ShopHandle:UpdateEmployee(shopId, jobGrade, setJob, srcOrIdentifier)
end)

RegisterNetEvent("t1ger_mechanic:server:updateEmployee", function(shopId, jobGrade, setJob, srcOrIdentifier)
    local src = source
    srcOrIdentifier = srcOrIdentifier or src  -- Use `source` if no param was given

    if not srcOrIdentifier then
        return error("[t1ger_mechanic:server:updateEmployee] Invalid srcOrIdentifier parameter. Expected src(number) or identifier(string)")
    end

    ShopHandle:UpdateEmployee(shopId, jobGrade, setJob, srcOrIdentifier)
end)