-- Maps (If you add a new map then you need to make sure that all of these config tables are updated with that new maps spawn locations)
Config.Maps = {
    ["cayo-perico"] = {
        Image = "https://img.gta5-mods.com/q75/images/asphalted-roads-on-cayo-perico/6705c8-1.jpg",
        Lable = "Cayo Perico",
        Description = "Orginal Island",
    },
    ["cargo-docs"] = {
        Image = "https://r2.fivemanage.com/LPokMhDpBlOhpx7I1MH9J/2391271c-ebd9-45ba-a2eb-a9ea237c0ab9.png",
        Lable = "Cargo Docs",
        Description = "Small and fast pace action!",
    },
}
-- This looks complicated 
-- But it's really just a formula: 
-- ShrinkSpeed = (CurrentRadius - NextRadius) / ShrinkTime
-- Each circle phase defines:
-- - How long it takes to shrink (ShrinkTime in seconds)
-- - The starting size of the circle before shrinking (Radius)
-- - And how fast it should shrink per second (ShrinkSpeed)
-- The zone will shrink from the current Radius to the next one using this speed over the given time.
Config.MapsCircleShrinkData = {
    ["cayo-perico"] = {
        Circle1 = {
            ShrinkTime = 115,
            Radius = 1500.0,
            ShrinkSpeed = (1500.0 - 750.0) / 115, -- = 6.52
        },
        Circle2 = {
            ShrinkTime = 74,
            Radius = 750.0,
            ShrinkSpeed = (750.0 - 380.0) / 74, -- = 5.0
        }, 
        Circle3 = {
            ShrinkTime = 61,
            Radius = 380.0,
            ShrinkSpeed = (380.0 - 135.0) / 61, -- = 4.016
        }, 
        Circle4 = {
            ShrinkTime = 100,
            Radius = 135.0,
            ShrinkSpeed = (135.0 - 35.0) / 100, -- = 1.0
        },        
    },
    ["cargo-docs"] = {
        Circle1 = {
            ShrinkTime = 80,
            Radius = 400.0,
            ShrinkSpeed = (400.0 - 250.0) / 80, -- = 1.875
        },
        Circle2 = {
            ShrinkTime = 55,
            Radius = 250.0,
            ShrinkSpeed = (250.0 - 155.0) / 55, -- = 1.7857
        }, 
        Circle3 = {
            ShrinkTime = 45,
            Radius = 155.0,
            ShrinkSpeed = (155.0 - 50.0) / 45, -- = 2.0833
        }, 
        Circle4 = {
            ShrinkTime = 50,
            Radius = 50.0,
            ShrinkSpeed = (50.0 - 45.0) / 10, -- = 0.5
        },
    } 
}
Config.BlipLocations = {
    ["cayo-perico"] = {
        {
            name = "Luxury Villa",
            coords = vector2(5016.88, -5734.19),
            sprite = 176,      -- sprite
            color = 5,        -- color
            scale = 0.8       -- scale
        },
        {
            name = "Palm Port",
            coords = vector2(4974.49, -5184.33),
            sprite = 836,      -- sprite
            color = 18,        -- color
            scale = 0.9       -- scale
        },
        {
            name = "Boathouse Bay",
            coords = vector2(5098.55, -4624.47),
            sprite = 427,      -- sprite
            color = 8,        -- color
            scale = 0.8       -- scale
        },
        {
            name = "Redroof Runway",
            coords = vector2(4490.43, -4484.56),
            sprite = 569,      -- sprite
            color = 6,        -- color
            scale = 0.8       -- scale
        },
        {
            name = "Happy Farms",
            coords = vector2(5352.18, -5221.39),
            sprite = 469,      -- sprite
            color = 2,        -- color
            scale = 1.0       -- scale
        },
    },
    ["cargo-docs"] = {
        {
            name = "Anchor’s Edge",
            coords = vector2(958.7, -2887.79),
            sprite = 751,      -- sprite
            color = 2,        -- color
            scale = 1.0       -- scale
        },
        {
            name = "Crate Core",
            coords = vector2(1025.75, -3034.12),
            sprite = 615,      -- sprite
            color = 3,        -- color
            scale = 1.0       -- scale
        },
        {
            name = "Railrunner Row",
            coords = vector2(1041.88, -3270.3),
            sprite = 795,      -- sprite
            color = 5,        -- color
            scale = 1.0       -- scale
        },
        {
            name = "Overflow Freighter",
            coords = vector2(1240.2, -2975.25),
            sprite = 755,      -- sprite
            color = 8,        -- color
            scale = 1.0       -- scale
        },
    }
}
-- Random location the zone circles can start.
Config.ZoneOptions = {
    ["cayo-perico"] = {
        vector3(4886.69, -5060.89, 18.07),
        vector3(5033.29, -5445.01, 46.18),
        vector3(4617.11, -4787.89, 11.69),
    },
    ["cargo-docs"] = {
        vector3(975.8, -3080.08, 5.9),
        vector3(941.62, -3064.36, 5.9),
    },
}

-- WHERE THE PLANE SPAWNS WHEN THE MATCH STARTS AND ENDS
Config.PlaneSpawns = {
    ["cayo-perico"] = {
        [1] = {
            start = vector4(4118.22, -3920.88, 900.0, 210.4),
            finish = vector4(5441.01, -6139.76, 900.0, 212.12)
        },
        [2] = {
            start = vector4(5726.45, -6173.04, 900.0, 34.41),
            finish = vector4(4073.93, -4394.59, 900.0, 53.18)
        },
        [3] = {
            start = vector4(4153.4, -5936.88, 900.0, 308.75),
            finish = vector4(5708.53, -4416.32, 900.0, 308.98)
        },
        [4] = {
            start = vector4(3512.25, -5002.15, 900.0, 261.56),
            finish = vector4(6157.89, -5278.77, 900.0, 265.9)
        },
    },
    ["cargo-docs"] = {
        [1] = {
            start = vector4(-625.95, -2628.22, 500.0, 248.66),
            finish = vector4(2352.86, -3791.75, 500.0, 248.66)
        },
        [2] = {
            start = vector4(2352.86, -3791.75, 500.0, 63.47),
            finish = vector4(-349.06, -2442.67, 500.0, 63.47)
        },
        [3] = {
            start = vector4(915.31, -1341.57, 500.0, 183.51),
            finish = vector4(1021.56, -4113.23, 500.0, 183.51)
        },
        [4] = {
            start = vector4(1005.17, -4477.87, 500.0, 357.45),
            finish = vector4(893.27, -2020.67, 500.0, 357.45)
        },
    },
}

-- Heli Spawns ONLY HALF OF THE LOCATIONS YOU PUT HERE WILL ACTUALLY SPAWN A HELICOPTER PER MATCH
Config.HeliSpawns = {
    ["cayo-perico"] = {
        vector4(4889.87, -5738.09, 26.35, 160.25),
        vector4(4881.78, -5282.85, 8.4, 269.49),
        vector4(5573.62, -5218.95, 15.54, 125.77),
        vector4(5041.5, -4675.97, 5.06, 136.29),
        vector4(3940.42, -4696.73, 4.18, 289.71),
        vector4(4792.07, -4294.51, 5.37, 251.7),
    },
    ["cargo-docs"] = {
        vector4(1277.18, -3087.55, 5.91, 133.15),
        vector4(785.8, -3295.07, 5.95, 265.52),
        vector4(811.57, -2933.71, 5.91, 271.83),
        vector4(1277.82, -3338.65, 5.9, 356.79),
    },
}
-- Car Spawns ONLY HALF OF THE LOCATIONS YOU PUT HERE WILL ACTUALLY SPAWN A HELICOPTER PER MATCH
Config.CarSpawn = {
    ["cayo-perico"] = {
        vector4(4799.73, -5907.79, 22.06, 334.79),
        vector4(4899.36, -5834.63, 28.3, 352.13),
        vector4(4999.98, -5893.98, 17.79, 323.78),
        vector4(4985.42, -5713.54, 19.42, 49.35),
        vector4(4771.17, -5648.99, 21.24, 3.69),
        vector4(4968.95, -5589.27, 23.95, 248.1),
        vector4(4883.75, -5532.27, 31.81, 315.77),
        vector4(5475.88, -5830.94, 19.07, 11.9),
        vector4(4792.23, -5486.29, 19.91, 331.03),
        vector4(5478.61, -5861.1, 19.81, 281.82),
        vector4(5567.19, -5502.1, 22.72, 98.18),
        vector4(5591.93, -5215.31, 14.35, 228.17),
        vector4(5461.77, -5226.32, 27.18, 129.39),
        vector4(5334.2, -5266.44, 32.74, 44.97),
        vector4(5184.78, -5132.7, 2.87, 77.68),
        vector4(5008.03, -5195.31, 2.52, 316.42),
        vector4(4918.56, -5236.12, 2.06, 37.21),
        vector4(4815.56, -5020.0, 31.61, 244.07),
        vector4(4921.61, -4914.78, 3.41, 101.34),
        vector4(5151.19, -4938.59, 13.89, 139.72),
        vector4(5093.93, -4697.25, 2.8, 74.0),
        vector4(5154.57, -4617.71, 2.81, 163.95),
        vector4(5135.57, -4604.65, 3.0, 351.86),
        vector4(4796.5, -4316.37, 5.25, 340.76),
        vector4(4476.34, -4454.08, 4.11, 207.54),
        vector4(4352.97, -4583.28, 4.47, 13.8),
        vector4(4519.92, -4650.21, 11.17, 2.62),
        vector4(3851.22, -4718.46, 3.16, 284.2),
        vector4(4871.08, -4612.9, 15.73, 108.85),
        vector4(4833.26, -5035.05, 31.29, 302.98),
        vector4(4501.63, -4547.32, 3.57, 22.89),
        vector4(5077.65, -5724.47, 15.31, 324.16),
        vector4(4965.82, -5799.88, 20.38, 61.62),
        vector4(4807.29, -5795.04, 36.67, 298.13),
        vector4(4884.47, -5653.97, 23.06, 75.09),
        vector4(5125.99, -5576.48, 38.93, 300.49),
        vector4(5290.89, -5435.32, 63.89, 138.48),
        vector4(5316.86, -5600.26, 64.84, 336.66),
        vector4(5456.54, -5737.35, 30.41, 13.23),
        vector4(5520.18, -5520.59, 28.38, 81.24),
        vector4(5263.63, -5494.91, 50.74, 110.62),
        vector4(5098.44, -5512.38, 53.57, 273.14),
        vector4(5079.65, -5269.62, 5.77, 30.74),
        vector4(4975.24, -5172.08, 2.43, 249.85),
        vector4(4974.67, -5210.12, 2.29, 41.23),
        vector4(4971.68, -5319.74, 8.14, 28.29),
        vector4(4974.92, -5210.12, 2.5, 41.45),
        vector4(4961.74, -5115.97, 2.5, 234.67),
        vector4(4846.17, -5172.48, 2.42, 290.14),
        vector4(4929.73, -4995.47, 26.6, 282.82),
        vector4(5082.07, -4877.79, 16.7, 151.12),
        vector4(5209.3, -5115.5, 5.23, 2.8),
        vector4(5418.89, -5117.25, 13.24, 77.8),
        vector4(4956.74, -4751.88, 8.21, 354.63),
        vector4(5089.19, -4599.71, 3.65, 167.65),
        vector4(5186.53, -4670.87, 1.76, 162.65),
        vector4(5116.63, -4699.18, 2.93, 159.35),
        vector4(4665.97, -4561.07, 22.32, 127.28),
        vector4(4419.09, -4474.22, 4.38, 192.29),
        vector4(4525.15, -4456.02, 4.47, 154.48),
        vector4(4436.31, -4587.8, 1.75, 71.13),
        vector4(4981.88, -5871.15, 19.92, 45.71),
    },
    ["cargo-docs"] = {
        vector4(1246.23, -3215.3, 5.8, 96.0),
        vector4(1134.41, -3284.17, 5.9, 0.3),
        vector4(900.93, -3127.31, 5.9, 0.37),
        vector4(1017.37, -3128.58, 5.9, 2.42),
        vector4(1096.32, -3083.43, 5.88, 91.01),
        vector4(1012.64, -2924.08, 5.9, 180.69),
        vector4(1182.68, -2926.35, 5.9, 88.16),
        vector4(849.14, -3138.02, 5.9, 271.1),
        vector4(884.88, -2990.17, 5.9, 358.73),
    },
}

-- Loot locations 1
Config.GunsSingleTable = {
    ["cargo-docs"] = {
        -- CARGOSHIP 1
        vector3(827.46, -2883.99, 13.7),
        vector3(832.79, -2892.19, 13.7),
        vector3(832.12, -2870.48, 13.7),
        vector3(832.02, -2880.8, 13.7),
        vector3(819.76, -2883.64, 13.7),
        vector3(820.65, -2877.46, 13.7),
        vector3(836.67, -2871.46, 11.27),
        vector3(837.23, -2880.9, 11.27),
        vector3(837.07, -2891.5, 11.27),
        vector3(842.75, -2876.27, 13.82),
        vector3(848.41, -2868.66, 13.82),
        vector3(847.97, -2878.32, 16.41),
        vector3(840.62, -2873.82, 16.41),
        vector3(844.68, -2881.04, 19.0),
        vector3(848.94, -2887.96, 16.41),
        vector3(845.85, -2883.24, 16.41),
        vector3(842.53, -2885.93, 13.82),
        vector3(842.74, -2892.53, 13.82),
        vector3(861.88, -2895.49, 16.43),
        vector3(854.61, -2895.83, 16.43),
        vector3(856.94, -2892.72, 19.02),
        vector3(861.48, -2890.66, 21.62),
        vector3(854.18, -2887.89, 19.02),
        vector3(860.98, -2886.36, 19.02),
        vector3(857.89, -2882.16, 19.02),
        vector3(854.56, -2878.78, 19.02),
        vector3(862.12, -2875.45, 19.02),
        vector3(868.09, -2892.51, 19.02),
        vector3(872.94, -2892.3, 19.02),
        vector3(872.48, -2884.43, 19.02),
        vector3(872.58, -2873.05, 19.02),
        vector3(867.96, -2880.44, 19.02),
        vector3(883.06, -2875.51, 19.02),
        vector3(882.31, -2869.26, 19.02),
        vector3(881.17, -2893.97, 19.02),
        vector3(887.18, -2889.54, 19.02),
        vector3(856.56, -2866.5, 11.27),
        vector3(851.48, -2878.84, 11.27),
        vector3(855.72, -2895.81, 11.27),
        vector3(858.09, -2866.31, 16.43),
        vector3(858.83, -2871.41, 21.62),
        vector3(888.15, -2885.66, 21.62),
        vector3(883.58, -2883.72, 24.21),
        vector3(888.29, -2880.72, 21.62),
        vector3(896.16, -2873.88, 19.02),
        vector3(896.85, -2886.81, 19.02),
        vector3(910.75, -2888.99, 19.02),
        vector3(915.7, -2871.36, 19.02),
        vector3(923.69, -2879.78, 19.02),
        vector3(927.59, -2886.85, 19.02),
        vector3(930.08, -2870.42, 19.02),
        vector3(941.4, -2881.39, 19.02),
        vector3(916.42, -2883.29, 19.02),
        vector3(939.46, -2893.13, 19.02),
        vector3(944.6, -2890.71, 21.62),
        vector3(946.68, -2876.41, 21.62),
        vector3(938.24, -2876.39, 21.62),
        vector3(942.72, -2871.15, 19.02),
        vector3(953.84, -2869.18, 19.02),
        vector3(959.69, -2875.46, 19.02),
        vector3(952.21, -2882.26, 19.02),
        vector3(959.93, -2890.82, 19.02),
        vector3(970.89, -2871.3, 19.02),
        vector3(969.75, -2892.87, 19.02),
        vector3(968.34, -2882.35, 19.02),
        vector3(982.67, -2868.61, 19.02),
        vector3(987.15, -2876.2, 19.02),
        vector3(987.55, -2887.79, 19.02),
        vector3(979.57, -2894.83, 19.03),
        vector3(984.17, -2882.2, 21.62),
        vector3(993.98, -2890.73, 19.02),
        vector3(1001.26, -2891.99, 19.02),
        vector3(1002.42, -2871.66, 19.02),
        vector3(994.53, -2871.48, 19.02),
        vector3(998.44, -2883.95, 19.02),
        vector3(867.53, -2895.89, 11.27),
        vector3(865.65, -2889.34, 11.27),
        vector3(865.56, -2876.87, 11.27),
        vector3(874.45, -2866.39, 11.27),
        vector3(879.29, -2875.19, 11.27),
        vector3(879.39, -2886.37, 11.27),
        vector3(887.97, -2895.46, 11.27),
        vector3(893.46, -2886.94, 11.27),
        vector3(893.53, -2874.22, 11.27),
        vector3(895.43, -2866.56, 11.27),
        vector3(911.57, -2866.43, 11.27),
        vector3(907.42, -2874.06, 11.27),
        vector3(907.52, -2888.66, 11.27),
        vector3(913.09, -2895.7, 11.27),
        vector3(927.59, -2895.64, 11.27),
        vector3(921.47, -2887.85, 11.27),
        vector3(921.45, -2876.99, 11.27),
        vector3(926.98, -2866.56, 11.27),
        vector3(941.46, -2866.53, 11.27),
        vector3(956.23, -2866.63, 11.27),
        vector3(970.65, -2866.54, 11.27),
        vector3(982.45, -2866.65, 11.27),
        vector3(998.41, -2866.58, 11.27),
        vector3(1009.73, -2868.2, 11.27),
        vector3(1021.54, -2868.63, 11.27),
        vector3(1029.85, -2866.47, 11.27),
        vector3(1040.53, -2866.55, 11.27),
        vector3(1055.22, -2866.46, 11.27),
        vector3(1070.51, -2866.49, 11.27),
        vector3(1078.42, -2869.0, 11.27),
        vector3(1078.5, -2882.51, 11.27),
        vector3(1078.31, -2895.66, 11.27),
        vector3(1069.76, -2895.46, 11.27),
        vector3(1058.36, -2895.44, 11.27),
        vector3(1045.19, -2895.41, 11.27),
        vector3(1030.74, -2895.4, 11.27),
        vector3(1021.12, -2893.07, 11.27),
        vector3(1011.23, -2893.0, 11.27),
        vector3(999.32, -2895.72, 11.27),
        vector3(984.93, -2895.58, 11.27),
        vector3(970.8, -2895.55, 11.27),
        vector3(956.48, -2895.75, 11.27),
        vector3(942.82, -2895.76, 11.27),
        vector3(935.58, -2887.53, 11.27),
        vector3(935.45, -2874.82, 11.27),
        vector3(949.41, -2874.85, 11.27),
        vector3(949.34, -2887.84, 11.27),
        vector3(963.39, -2886.08, 11.27),
        vector3(963.41, -2870.85, 11.27),
        vector3(977.48, -2873.72, 11.27),
        vector3(977.42, -2890.22, 11.27),
        vector3(991.41, -2887.44, 11.27),
        vector3(991.55, -2874.11, 11.27),
        vector3(1005.51, -2875.14, 11.27),
        vector3(1005.57, -2887.76, 11.27),
        vector3(1023.48, -2875.87, 11.27),
        vector3(1023.6, -2886.27, 11.27),
        vector3(1036.88, -2887.07, 11.27),
        vector3(1036.8, -2875.55, 11.27),
        vector3(1050.49, -2874.97, 11.27),
        vector3(1050.48, -2887.93, 11.27),
        vector3(1064.63, -2888.92, 11.27),
        vector3(1064.39, -2873.62, 11.27),
        vector3(934.83, -2861.56, 43.28),
        vector3(937.75, -2861.85, 43.28),
        vector3(936.35, -2868.71, 43.28),
        vector3(937.91, -2875.36, 43.28),
        vector3(934.82, -2878.41, 43.28),
        vector3(937.95, -2884.92, 43.29),
        vector3(934.44, -2890.31, 43.28),
        vector3(937.69, -2897.04, 43.29),
        vector3(935.0, -2910.99, 43.28),
        vector3(937.07, -2918.93, 43.29),
        vector3(934.46, -2926.33, 43.28),
        vector3(938.47, -2936.19, 43.29),
        vector3(934.48, -2945.72, 43.29),
        vector3(937.46, -2955.42, 43.29),
        vector3(931.27, -2942.91, 47.23),
        vector3(941.12, -2942.92, 47.23),
        vector3(944.18, -2933.72, 49.13),
        vector3(939.07, -2938.21, 52.37),
        vector3(934.1, -2937.0, 52.42),
        vector3(936.34, -2965.35, 44.8),
        vector3(939.79, -2904.69, 47.63),
        vector3(978.31, -2861.02, 43.28),
        vector3(981.29, -2864.06, 43.28),
        vector3(977.92, -2868.56, 43.28),
        vector3(980.98, -2875.58, 43.28),
        vector3(977.93, -2882.61, 43.28),
        vector3(981.4, -2887.77, 43.29),
        vector3(977.94, -2897.35, 43.28),
        vector3(981.1, -2899.76, 43.29),
        vector3(978.86, -2906.76, 43.28),
        vector3(980.4, -2914.18, 43.29),
        vector3(978.07, -2921.13, 43.28),
        vector3(981.55, -2927.77, 43.29),
        vector3(979.78, -2934.32, 43.29),
        vector3(981.03, -2943.76, 43.29),
        vector3(978.57, -2951.02, 43.29),
        vector3(981.62, -2956.29, 43.29),
        vector3(979.71, -2965.19, 44.8),
        vector3(973.48, -2942.76, 47.23),
        vector3(985.49, -2942.89, 47.23),
        vector3(987.37, -2933.71, 49.13),
        vector3(982.89, -2938.16, 52.37),
        vector3(976.43, -2937.85, 52.39),
        vector3(982.28, -2904.73, 47.63),
        vector3(1027.08, -2867.04, 19.03),
        vector3(1034.34, -2871.42, 19.02),
        vector3(1028.5, -2879.4, 19.02),
        vector3(1033.84, -2888.43, 19.02),
        vector3(1028.14, -2892.11, 19.02),
        vector3(1038.28, -2879.7, 19.02),
        vector3(1039.68, -2870.55, 19.02),
        vector3(1047.55, -2871.07, 19.02),
        vector3(1044.02, -2880.05, 19.02),
        vector3(1045.09, -2889.6, 19.02),
        vector3(1054.63, -2892.83, 19.02),
        vector3(1060.08, -2888.22, 19.02),
        vector3(1052.57, -2879.27, 19.02),
        vector3(1059.18, -2874.64, 19.02),
        vector3(1056.06, -2869.51, 19.02),
        vector3(1070.45, -2895.6, 16.43),
        vector3(1075.03, -2892.31, 19.02),
        vector3(1067.12, -2890.65, 19.02),
        vector3(1070.6, -2888.17, 21.62),
        vector3(1070.66, -2885.94, 24.22),
        vector3(1074.65, -2879.01, 21.62),
        vector3(1067.17, -2882.48, 21.62),
        vector3(1070.41, -2876.23, 24.21),
        vector3(1073.51, -2873.61, 19.02),
        vector3(1068.9, -2869.96, 19.02),
        vector3(1071.61, -2866.07, 16.43),
        vector3(1005.56, -2878.23, 15.22),
        vector3(999.88, -2866.52, 16.43),
        vector3(1021.24, -2866.38, 15.22),
        vector3(1011.02, -2869.29, 15.22),
        vector3(1021.96, -2867.98, 19.21),
        vector3(1014.48, -2868.71, 19.22),
        vector3(1019.07, -2867.68, 23.2),
        vector3(1017.8, -2867.69, 27.19),
        vector3(1017.91, -2867.89, 31.18),
        vector3(1017.74, -2871.51, 35.17),
        vector3(1019.03, -2875.99, 43.15),
        vector3(1008.61, -2878.22, 43.15),
        vector3(1009.74, -2884.92, 43.15),
        vector3(1018.08, -2886.78, 43.15),
        vector3(1017.07, -2890.38, 35.17),
        vector3(1014.37, -2893.05, 31.19),
        vector3(1021.98, -2893.98, 31.18),
        vector3(1014.43, -2893.37, 27.2),
        vector3(1021.87, -2894.02, 27.19),
        vector3(1014.27, -2893.97, 23.2),
        vector3(1020.81, -2894.18, 19.21),
        vector3(1013.58, -2892.88, 15.22),
        vector3(1021.17, -2895.71, 15.22),
        vector3(1008.76, -2894.14, 15.22),
        vector3(1022.41, -2890.1, 39.16),
        vector3(1022.5, -2871.67, 39.16),
        vector3(1011.44, -2867.24, 39.16),
        vector3(1008.02, -2872.37, 39.16),
        vector3(1004.48, -2881.17, 39.16),
        vector3(1011.23, -2894.71, 39.16),
        -- END OF SHIP 1   
        
        


        -- SHIP 2
        vector3(1231.85, -2884.56, 9.32),
        vector3(1239.48, -2888.32, 9.32),
        vector3(1240.46, -2881.82, 9.32),
        vector3(1248.1, -2883.52, 9.32),
        vector3(1245.76, -2889.33, 9.32),
        vector3(1250.97, -2898.35, 9.32),
        vector3(1252.42, -2904.67, 9.32),
        vector3(1252.27, -2911.21, 13.33),
        vector3(1253.38, -2907.27, 13.33),
        vector3(1253.82, -2903.42, 13.33),
        vector3(1251.02, -2913.72, 17.33),
        vector3(1250.5, -2921.03, 17.33),
        vector3(1251.08, -2903.23, 17.33),
        vector3(1247.11, -2900.25, 17.33),
        vector3(1245.23, -2893.25, 17.33),
        vector3(1246.41, -2902.42, 21.33),
        vector3(1248.67, -2909.89, 21.33),
        vector3(1247.13, -2910.86, 25.33),
        vector3(1245.08, -2906.36, 25.34),
        vector3(1243.65, -2902.23, 25.34),
        vector3(1238.28, -2901.81, 25.34),
        vector3(1238.35, -2906.94, 25.34),
        vector3(1235.31, -2912.2, 25.33),
        vector3(1230.41, -2918.34, 26.46),
        vector3(1232.83, -2915.6, 26.46),
        vector3(1244.45, -2917.32, 29.69),
        vector3(1235.08, -2917.33, 29.69),
        vector3(1240.15, -2914.94, 29.69),
        vector3(1241.33, -2910.88, 29.69),
        vector3(1244.38, -2909.79, 29.69),
        vector3(1250.29, -2918.0, 26.46),
        vector3(1248.2, -2914.54, 26.46),
        vector3(1232.12, -2911.21, 21.33),
        vector3(1232.65, -2904.33, 21.33),
        vector3(1237.25, -2894.29, 17.33),
        vector3(1233.09, -2893.0, 17.33),
        vector3(1235.33, -2899.02, 17.33),
        vector3(1230.17, -2901.76, 17.33),
        vector3(1230.14, -2913.7, 17.33),
        vector3(1229.9, -2920.1, 17.33),
        vector3(1235.47, -2921.3, 17.33),
        vector3(1242.68, -2921.09, 17.33),
        vector3(1229.52, -2894.33, 9.32),
        vector3(1228.88, -2906.69, 9.32),
        vector3(1230.37, -2912.96, 9.32),
        vector3(1228.14, -2918.54, 9.32),
        vector3(1234.29, -2925.93, 9.32),
        vector3(1236.15, -2920.71, 9.32),
        vector3(1241.2, -2923.14, 9.32),
        vector3(1252.65, -2921.27, 9.32),
        vector3(1245.84, -2925.15, 9.32),
        vector3(1250.92, -2910.9, 9.32),
        vector3(1252.89, -2929.94, 9.32),
        vector3(1252.38, -2940.77, 9.32),
        vector3(1246.13, -2943.28, 9.32),
        vector3(1241.13, -2941.59, 9.32),
        vector3(1235.88, -2944.48, 9.32),
        vector3(1228.91, -2942.43, 9.32),
        vector3(1229.32, -2949.18, 9.32),
        vector3(1234.98, -2951.09, 9.32),
        vector3(1244.92, -2953.26, 9.32),
        vector3(1249.81, -2949.87, 9.32),
        vector3(1245.07, -2947.17, 12.19),
        vector3(1235.96, -2946.88, 12.19),
        vector3(1233.01, -2929.8, 12.16),
        vector3(1241.55, -2930.02, 12.16),
        vector3(1252.14, -2932.81, 12.19),
        vector3(1242.26, -2937.04, 12.16),
        vector3(1232.93, -2936.95, 12.16),
        vector3(1248.94, -2957.34, 12.16),
        vector3(1249.51, -2965.44, 12.16),
        vector3(1232.19, -2965.1, 12.16),
        vector3(1230.89, -2957.25, 12.16),
        vector3(1240.49, -2961.66, 12.16),
        vector3(1228.33, -2960.04, 9.32),
        vector3(1230.39, -2969.83, 9.32),
        vector3(1236.68, -2969.53, 9.32),
        vector3(1252.13, -2969.6, 9.32),
        vector3(1252.68, -2977.14, 9.32),
        vector3(1251.27, -2986.06, 9.32),
        vector3(1242.31, -2986.01, 9.32),
        vector3(1231.34, -2985.79, 9.32),
        vector3(1228.27, -2977.87, 9.32),
        vector3(1228.5, -2991.49, 9.32),
        vector3(1229.49, -3003.13, 9.32),
        vector3(1236.7, -3003.14, 9.32),
        vector3(1244.49, -3002.44, 9.32),
        vector3(1253.0, -3000.19, 9.32),
        vector3(1251.71, -3008.5, 9.32),
        vector3(1242.13, -3010.27, 9.32),
        vector3(1236.26, -3009.3, 9.32),
        vector3(1229.44, -3012.59, 9.32),
        vector3(1230.76, -3019.65, 9.32),
        vector3(1231.12, -3027.29, 9.35),
        vector3(1234.39, -3034.0, 9.35),
        vector3(1240.58, -3033.87, 9.32),
        vector3(1249.0, -3031.31, 9.37),
        vector3(1248.02, -3023.18, 9.32),
        vector3(1252.06, -3015.71, 9.32),
        vector3(1252.16, -2971.55, 11.97),
        vector3(1229.09, -2971.34, 11.97),
        vector3(1236.37, -2971.35, 11.97),
        vector3(1245.0, -2971.37, 11.97),
        vector3(1251.84, -2982.19, 15.02),
        vector3(1251.27, -2973.77, 15.02),
        vector3(1245.96, -2977.86, 14.98),
        vector3(1241.41, -2982.76, 14.98),
        vector3(1241.36, -2974.05, 14.98),
        vector3(1230.64, -2973.59, 14.98),
        vector3(1230.12, -2982.0, 14.98),
        vector3(1235.24, -2977.2, 14.98),
        vector3(1230.05, -2990.5, 12.19),
        vector3(1230.28, -2998.04, 12.16),
        vector3(1238.94, -2998.65, 12.16),
        vector3(1241.18, -2990.47, 12.16),
        vector3(1248.91, -2990.8, 12.16),
        vector3(1251.47, -2999.07, 12.19),
        vector3(1246.0, -3006.95, 12.19),
        vector3(1236.42, -3006.3, 12.19),
        vector3(1236.63, -3014.39, 13.74),
        vector3(1245.11, -3014.7, 13.74),
        vector3(1242.01, -3018.39, 13.74),
        vector3(1244.92, -3021.79, 13.74),
        vector3(1236.36, -3021.52, 13.74),
        vector3(1240.28, -3024.97, 16.61),
        vector3(1235.98, -3029.18, 13.78),
        vector3(1244.43, -3028.24, 13.77),
        vector3(1237.32, -3052.31, 14.3),
        vector3(1241.18, -3053.02, 14.3),
        vector3(1244.37, -3052.04, 14.3),
        vector3(1250.42, -3038.07, 14.3),
        vector3(1231.27, -3037.47, 14.3),
        vector3(1234.81, -3042.96, 14.3),
        vector3(1241.05, -3046.01, 14.3),
        vector3(1246.01, -3041.61, 14.3),
        vector3(1241.15, -3038.05, 14.3),
        -- END OF SHIP 2
        -- MIDDLE CRATE CONTAINERS
        vector3(828.47, -3070.72, 5.9),
        vector3(815.21, -3071.18, 5.9),
        vector3(816.24, -3086.56, 5.9),
        vector3(810.1, -3102.28, 5.9),
        vector3(824.92, -3105.6, 5.9),
        vector3(865.17, -3108.02, 5.9),
        vector3(874.3, -3096.62, 5.9),
        vector3(874.83, -3074.06, 5.9),
        vector3(863.52, -3071.35, 5.9),
        vector3(851.12, -3067.55, 5.9),
        vector3(858.61, -3078.34, 5.9),
        vector3(868.53, -3088.02, 5.9),
        vector3(858.58, -3100.92, 5.9),
        vector3(857.21, -3087.85, 5.9),
        vector3(852.69, -3088.07, 5.9),
        vector3(856.37, -3098.73, 5.9),
        vector3(853.83, -3095.32, 5.9),
        vector3(849.34, -3099.17, 5.9),
        vector3(844.69, -3094.68, 5.9),
        vector3(844.64, -3090.39, 5.9),
        vector3(851.81, -3105.85, 5.9),
        vector3(891.85, -3105.65, 5.9),
        vector3(892.91, -3089.94, 5.9),
        vector3(898.32, -3086.91, 5.9),
        vector3(890.58, -3073.76, 5.9),
        vector3(904.19, -3068.12, 5.9),
        vector3(913.76, -3071.65, 5.9),
        vector3(919.48, -3068.35, 5.9),
        vector3(928.87, -3072.48, 5.9),
        vector3(935.6, -3068.23, 5.9),
        vector3(945.66, -3071.99, 5.9),
        vector3(953.15, -3068.45, 5.9),
        vector3(961.46, -3072.16, 5.9),
        vector3(968.11, -3069.0, 5.9),
        vector3(962.15, -3095.87, 5.9),
        vector3(956.76, -3099.11, 5.9),
        vector3(952.86, -3092.8, 5.9),
        vector3(942.93, -3105.01, 5.9),
        vector3(908.87, -3104.99, 5.9),
        vector3(822.54, -3096.08, 5.9),
        vector3(830.74, -3093.72, 5.9),
        vector3(830.57, -3081.13, 5.9),
        vector3(824.88, -3087.8, 5.9),
        vector3(836.46, -3088.02, 5.9),
        vector3(844.58, -3085.34, 5.9),
        vector3(844.69, -3076.77, 5.9),
        vector3(956.39, -3087.76, 5.9),
        vector3(948.93, -3084.78, 5.9),
        vector3(941.47, -3088.36, 5.9),
        vector3(937.89, -3096.12, 5.9),
        vector3(937.98, -3076.91, 5.9),
        vector3(930.15, -3088.09, 5.9),
        vector3(924.03, -3097.16, 5.9),
        vector3(923.9, -3080.78, 5.9),
        vector3(912.99, -3088.58, 5.9),
        vector3(919.81, -3087.48, 5.9),
        vector3(909.86, -3095.28, 5.9),
        vector3(909.89, -3079.15, 5.9),
        vector3(907.46, -3084.85, 5.9),
        vector3(822.52, -3081.41, 14.33),
        vector3(822.87, -3098.51, 11.51),
        vector3(836.02, -3097.6, 14.33),
        vector3(850.75, -3091.73, 17.15),
        vector3(866.33, -3094.91, 17.15),
        vector3(866.1, -3081.53, 14.33),
        vector3(851.4, -3079.1, 14.33),
        vector3(902.6, -3097.61, 14.33),
        vector3(916.19, -3097.2, 17.15),
        vector3(929.56, -3097.47, 17.15),
        vector3(917.07, -3084.21, 17.15),
        vector3(919.84, -3078.6, 17.15),
        vector3(945.64, -3093.05, 14.33),
        vector3(958.29, -3078.8, 14.33),
        vector3(902.02, -3050.27, 5.9),
        vector3(911.61, -3053.49, 5.9),
        vector3(926.89, -3050.27, 5.9),
        vector3(940.83, -3053.36, 5.9),
        vector3(952.75, -3051.02, 5.9),
        vector3(967.58, -3051.33, 5.9),
        vector3(971.94, -3038.06, 5.9),
        vector3(970.54, -3021.05, 5.9),
        vector3(964.26, -3034.06, 5.9),
        vector3(955.13, -3034.28, 5.9),
        vector3(943.02, -3034.21, 5.9),
        vector3(951.64, -3042.28, 5.9),
        vector3(952.04, -3027.4, 5.9),
        vector3(927.76, -3109.71, 5.9),
        vector3(969.64, -3106.03, 5.9),
        vector3(971.97, -3088.52, 5.9),
        vector3(968.08, -3077.46, 5.9),
        vector3(960.35, -3017.88, 5.9),
        vector3(949.4, -3013.25, 5.9),
        vector3(931.95, -3018.38, 5.9),
        vector3(914.47, -3013.62, 5.9),
        vector3(898.77, -3017.65, 5.9),
        vector3(889.33, -3026.08, 5.9),
        vector3(887.46, -3046.35, 5.9),
        vector3(902.52, -3034.35, 5.9),
        vector3(917.42, -3034.12, 5.9),
        vector3(909.71, -3026.72, 5.9),
        vector3(909.84, -3041.16, 5.9),
        vector3(923.95, -3028.42, 5.9),
        vector3(923.8, -3042.02, 5.9),
        vector3(931.71, -3042.47, 5.9),
        vector3(937.61, -3038.67, 5.9),
        vector3(937.92, -3029.96, 5.9),
        vector3(945.5, -3023.34, 17.1),
        vector3(930.94, -3022.43, 17.1),
        vector3(916.53, -3028.31, 17.15),
        vector3(917.24, -3046.09, 17.15),
        vector3(901.82, -3025.19, 14.33),
        vector3(902.35, -3038.09, 11.51),
        vector3(929.46, -3046.33, 17.1),
        vector3(927.58, -3038.19, 17.15),
        vector3(943.87, -3044.45, 17.15),
        vector3(957.29, -3045.94, 17.15),
        vector3(892.06, -2995.15, 5.9),
        vector3(898.41, -3001.57, 5.9),
        vector3(910.52, -2998.02, 5.9),
        vector3(925.0, -3001.65, 5.9),
        vector3(943.04, -2997.47, 5.9),
        vector3(957.92, -3001.01, 5.9),
        vector3(972.51, -2994.74, 5.9),
        vector3(967.68, -2986.4, 5.9),
        vector3(971.58, -2974.99, 5.9),
        vector3(967.05, -2967.5, 5.9),
        vector3(958.04, -2959.71, 5.9),
        vector3(948.04, -2962.47, 5.9),
        vector3(935.59, -2965.09, 5.9),
        vector3(920.66, -2965.87, 5.9),
        vector3(903.27, -2962.08, 5.9),
        vector3(892.48, -2967.4, 5.9),
        vector3(891.04, -2977.31, 5.9),
        vector3(898.71, -2981.91, 5.9),
        vector3(915.77, -2981.86, 5.9),
        vector3(909.95, -2974.1, 5.9),
        vector3(909.73, -2988.11, 5.9),
        vector3(923.96, -2974.11, 5.9),
        vector3(924.05, -2989.0, 5.9),
        vector3(933.92, -2989.33, 5.9),
        vector3(929.46, -2981.53, 5.9),
        vector3(937.99, -2973.22, 5.9),
        vector3(948.65, -2978.89, 5.9),
        vector3(941.56, -2982.29, 5.9),
        vector3(951.97, -2971.99, 5.9),
        vector3(951.99, -2989.58, 5.9),
        vector3(962.98, -2981.64, 5.9),
        vector3(958.44, -2985.9, 11.51),
        vector3(944.74, -2972.11, 14.33),
        vector3(931.46, -2972.46, 14.33),
        vector3(918.36, -2971.38, 17.15),
        vector3(916.79, -2984.51, 14.33),
        vector3(915.35, -2993.68, 17.15),
        vector3(930.03, -2993.62, 17.15),
        vector3(992.99, -2963.36, 5.9),
        vector3(1005.92, -2964.45, 5.9),
        vector3(1026.44, -2962.17, 5.9),
        vector3(1045.23, -2965.28, 5.9),
        vector3(1062.54, -2961.36, 5.9),
        vector3(1069.26, -2973.41, 5.9),
        vector3(1067.24, -2985.88, 5.9),
        vector3(1070.21, -3002.44, 5.9),
        vector3(1052.87, -3004.55, 5.9),
        vector3(1036.88, -2999.38, 5.9),
        vector3(1021.3, -3002.26, 5.9),
        vector3(1005.61, -2998.75, 5.9),
        vector3(989.61, -3002.24, 5.9),
        vector3(985.5, -2988.78, 5.9),
        vector3(984.8, -2971.51, 5.9),
        vector3(994.99, -2981.86, 5.9),
        vector3(1013.1, -2981.8, 5.9),
        vector3(1006.52, -2975.56, 5.9),
        vector3(1006.26, -2989.06, 5.9),
        vector3(1026.24, -2981.71, 5.9),
        vector3(1020.5, -2972.96, 5.9),
        vector3(1020.49, -2989.98, 5.9),
        vector3(1034.42, -2972.97, 5.9),
        vector3(1034.33, -2989.74, 5.9),
        vector3(1044.86, -2978.82, 5.9),
        vector3(1038.69, -2981.94, 5.9),
        vector3(1054.46, -2981.6, 5.9),
        vector3(1048.4, -2972.17, 5.9),
        vector3(1048.22, -2989.67, 5.9),
        vector3(1057.01, -2970.09, 17.15),
        vector3(1055.64, -2988.28, 17.15),
        vector3(1041.2, -2991.13, 17.15),
        vector3(1027.96, -2990.87, 17.15),
        vector3(1027.51, -2970.02, 14.33),
        vector3(1041.4, -2969.44, 14.33),
        vector3(1040.45, -2974.34, 11.51),
        vector3(1027.8, -2976.74, 11.51),
        vector3(1014.59, -2975.69, 17.15),
        vector3(1000.29, -2969.9, 14.33),
        vector3(1013.36, -2992.58, 14.33),
        vector3(1062.16, -3016.96, 5.9),
        vector3(1043.61, -3013.25, 5.9),
        vector3(1027.12, -3016.2, 5.9),
        vector3(1008.49, -3014.36, 5.9),
        vector3(995.38, -3017.58, 5.9),
        vector3(983.01, -3024.6, 5.9),
        vector3(987.34, -3037.44, 5.9),
        vector3(986.32, -3051.52, 5.9),
        vector3(1000.38, -3050.42, 5.9),
        vector3(1013.12, -3053.7, 5.9),
        vector3(1027.03, -3050.05, 5.9),
        vector3(1041.4, -3054.26, 5.9),
        vector3(1054.85, -3049.02, 5.9),
        vector3(1068.56, -3048.96, 5.9),
        vector3(1068.09, -3032.86, 5.9),
        vector3(1069.81, -3021.37, 5.9),
        vector3(1061.18, -3035.53, 5.9),
        vector3(1058.32, -3041.19, 5.9),
        vector3(1051.19, -3037.08, 5.9),
        vector3(1050.08, -3045.5, 5.9),
        vector3(1044.78, -3034.4, 5.9),
        vector3(1044.52, -3034.11, 5.9),
        vector3(1048.06, -3029.66, 5.9),
        vector3(1048.41, -3025.33, 5.9),
        vector3(1037.97, -3031.43, 5.9),
        vector3(1028.91, -3034.2, 5.9),
        vector3(1034.27, -3042.65, 5.9),
        vector3(1034.26, -3024.35, 5.9),
        vector3(1020.15, -3043.83, 5.9),
        vector3(1020.4, -3024.92, 5.9),
        vector3(1026.57, -3037.01, 8.69),
        vector3(1014.4, -3031.3, 8.69),
        vector3(1000.34, -3031.52, 8.69),
        vector3(996.24, -3037.19, 5.9),
        vector3(1004.04, -3034.36, 5.9),
        vector3(1006.56, -3042.85, 5.9),
        vector3(1006.39, -3026.76, 5.9),
        vector3(1011.92, -3034.06, 5.9),
        vector3(1000.27, -3043.71, 14.33),
        vector3(999.07, -3027.47, 11.51),
        vector3(997.91, -3022.2, 17.15),
        vector3(1012.78, -3022.32, 14.33),
        vector3(1013.05, -3046.21, 17.15),
        vector3(1027.31, -3046.13, 14.33),
        vector3(1026.74, -3039.83, 14.33),
        vector3(1026.08, -3022.62, 17.15),
        vector3(1040.09, -3024.97, 14.33),
        vector3(1055.97, -3027.7, 17.15),
        vector3(1041.18, -3044.69, 14.33),
        vector3(1055.03, -3015.35, 19.99),
        vector3(1048.25, -3017.07, 15.97),
        vector3(1053.49, -3017.11, 15.96),
        vector3(1056.57, -3020.03, 23.09),
        vector3(1052.35, -3021.41, 24.19),
        vector3(1063.02, -3069.49, 5.9),
        vector3(1048.13, -3071.43, 5.9),
        vector3(1033.62, -3068.39, 5.9),
        vector3(1022.13, -3071.18, 5.9),
        vector3(1009.79, -3066.46, 5.9),
        vector3(994.84, -3069.87, 5.9),
        vector3(982.28, -3079.01, 5.9),
        vector3(987.33, -3090.87, 5.9),
        vector3(987.51, -3107.39, 5.9),
        vector3(1004.34, -3105.98, 5.9),
        vector3(1018.93, -3108.3, 5.9),
        vector3(1032.79, -3105.35, 5.9),
        vector3(1046.33, -3107.88, 5.9),
        vector3(1060.16, -3105.18, 5.9),
        vector3(1072.13, -3105.41, 5.9),
        vector3(1068.61, -3092.47, 5.9),
        vector3(1070.36, -3079.6, 5.9),
        vector3(1060.01, -3087.56, 5.9),
        vector3(1041.59, -3088.42, 5.9),
        vector3(1048.45, -3082.22, 5.9),
        vector3(1048.3, -3095.88, 5.9),
        vector3(1027.98, -3087.92, 5.9),
        vector3(1034.58, -3095.52, 5.9),
        vector3(1034.64, -3081.22, 5.9),
        vector3(1014.69, -3088.25, 5.9),
        vector3(1020.77, -3095.1, 5.9),
        vector3(1020.77, -3080.42, 5.9),
        vector3(1006.33, -3096.91, 5.9),
        vector3(995.6, -3087.56, 5.9),
        vector3(1003.38, -3090.69, 5.9),
        vector3(1006.33, -3081.53, 5.9),
        vector3(1002.66, -3074.61, 5.9),
        vector3(999.11, -3078.79, 14.33),
        vector3(998.96, -3099.73, 14.33),
        vector3(1012.97, -3090.88, 14.33),
        vector3(1027.28, -3094.85, 17.15),
        vector3(1030.23, -3101.2, 17.15),
        vector3(1041.97, -3096.85, 17.15),
        vector3(1043.06, -3101.17, 17.11),
        vector3(1024.2, -3080.36, 17.15),
        vector3(1032.29, -3079.23, 17.15),
        vector3(1036.75, -3079.35, 17.15),
        vector3(1044.07, -3083.98, 17.15),
        vector3(1014.04, -3084.26, 17.15),
        vector3(1012.52, -3080.3, 14.33),
        vector3(1042.35, -3074.75, 14.33),
        vector3(1055.69, -3077.49, 14.33),
        vector3(1056.58, -3074.43, 11.51),
        vector3(1055.38, -3090.76, 14.33),
        vector3(1058.54, -3093.53, 17.15),
        vector3(1051.62, -3097.63, 17.15),
        vector3(1041.69, -3091.54, 11.51),
        vector3(1002.09, -3070.92, 15.97),
        vector3(1007.16, -3070.92, 15.96),
        vector3(1008.98, -3068.95, 19.99),
        vector3(1010.02, -3072.7, 23.48),
        vector3(1010.68, -3082.15, 23.09),
        vector3(1006.33, -3075.01, 24.19),
        vector3(1092.36, -3052.28, 5.9),
        vector3(1109.18, -3055.5, 5.9),
        vector3(1123.84, -3051.67, 5.9),
        vector3(1139.43, -3054.91, 5.9),
        vector3(1155.33, -3051.31, 5.9),
        vector3(1171.37, -3054.98, 5.9),
        vector3(1182.18, -3047.66, 5.9),
        vector3(1187.69, -3034.61, 5.9),
        vector3(1180.63, -3019.57, 5.9),
        vector3(1167.43, -3010.52, 5.9),
        vector3(1153.27, -3014.53, 5.9),
        vector3(1138.98, -3013.0, 5.9),
        vector3(1126.11, -3017.91, 5.9),
        vector3(1111.07, -3013.94, 5.9),
        vector3(1094.51, -3018.21, 5.9),
        vector3(1083.82, -3025.16, 5.9),
        vector3(1087.46, -3036.42, 5.9),
        vector3(1084.06, -3049.89, 5.9),
        vector3(1097.39, -3034.28, 5.9),
        vector3(1111.02, -3034.48, 5.9),
        vector3(1116.88, -3031.21, 5.9),
        vector3(1107.47, -3024.56, 5.9),
        vector3(1107.35, -3042.54, 5.9),
        vector3(1121.42, -3024.36, 5.9),
        vector3(1121.36, -3043.22, 5.9),
        vector3(1125.21, -3034.74, 5.9),
        vector3(1133.24, -3033.55, 5.9),
        vector3(1135.39, -3041.73, 5.9),
        vector3(1135.4, -3026.31, 5.9),
        vector3(1138.5, -3034.1, 5.9),
        vector3(1154.29, -3034.16, 5.9),
        vector3(1149.37, -3026.52, 5.9),
        vector3(1149.14, -3042.31, 5.9),
        vector3(1163.5, -3025.63, 5.9),
        vector3(1163.51, -3043.78, 5.9),
        vector3(1169.25, -3037.18, 8.69),
        vector3(1169.37, -3042.58, 14.33),
        vector3(1158.21, -3038.32, 11.51),
        vector3(1156.92, -3046.32, 17.15),
        vector3(1143.18, -3040.94, 14.33),
        vector3(1155.81, -3027.61, 14.33),
        vector3(1155.05, -3020.75, 17.15),
        vector3(1143.38, -3026.17, 14.33),
        vector3(1129.39, -3021.09, 14.33),
        vector3(1132.38, -3023.29, 17.15),
        vector3(1126.4, -3024.27, 17.15),
        vector3(1129.8, -3030.73, 17.15),
        vector3(1114.15, -3025.28, 14.33),
        vector3(1115.4, -3020.85, 17.15),
        vector3(1118.65, -3037.71, 17.15),
        vector3(1111.59, -3037.7, 17.15),
        vector3(1114.87, -3043.83, 17.15),
        vector3(1123.57, -3043.13, 17.15),
        vector3(1131.77, -3043.27, 17.15),
        vector3(1100.32, -3043.62, 14.33),
        vector3(1100.99, -3024.88, 17.15),
        vector3(1101.02, -3030.13, 11.51),
        vector3(1183.03, -2965.87, 5.9),
        vector3(1170.3, -2958.62, 5.9),
        vector3(1156.19, -2962.7, 5.9),
        vector3(1134.14, -2961.37, 5.9),
        vector3(1120.28, -2965.06, 5.9),
        vector3(1108.56, -2962.51, 5.9),
        vector3(1097.77, -2965.71, 5.9),
        vector3(1085.92, -2964.14, 5.9),
        vector3(1091.15, -2976.13, 5.9),
        vector3(1085.14, -2991.61, 5.9),
        vector3(1094.17, -3002.27, 5.9),
        vector3(1107.83, -2997.93, 5.9),
        vector3(1124.64, -3001.23, 5.9),
        vector3(1138.43, -2998.38, 5.9),
        vector3(1152.88, -3001.36, 5.9),
        vector3(1168.66, -2997.46, 5.9),
        vector3(1183.25, -3000.91, 5.9),
        vector3(1191.27, -2986.81, 5.9),
        vector3(1179.96, -2981.09, 5.9),
        vector3(1172.75, -2968.75, 5.9),
        vector3(1166.13, -2977.24, 5.9),
        vector3(1159.0, -2981.51, 5.9),
        vector3(1144.27, -2981.5, 5.9),
        vector3(1149.41, -2988.94, 5.9),
        vector3(1149.34, -2974.46, 5.9),
        vector3(1130.58, -2981.37, 5.9),
        vector3(1135.21, -2989.39, 5.9),
        vector3(1135.69, -2973.99, 5.9),
        vector3(1115.41, -2981.46, 5.9),
        vector3(1121.53, -2990.16, 5.9),
        vector3(1121.38, -2972.88, 5.9),
        vector3(1107.38, -2988.66, 5.9),
        vector3(1107.75, -2975.1, 5.9),
        vector3(1101.4, -2981.45, 5.9),
        vector3(1098.85, -2972.73, 17.15),
        vector3(1104.23, -2984.91, 14.33),
        vector3(1096.08, -2988.02, 14.33),
        vector3(1113.02, -2976.21, 14.33),
        vector3(1114.15, -2971.94, 17.15),
        vector3(1127.6, -2969.51, 17.15),
        vector3(1128.71, -2974.84, 11.51),
        vector3(1127.48, -2984.91, 11.51),
        vector3(1141.87, -2974.66, 17.15),
        vector3(1141.82, -2970.67, 14.33),
        vector3(1157.6, -2973.45, 14.33),
        vector3(1146.58, -2985.35, 17.15),
        vector3(1138.72, -2987.85, 17.15),
        vector3(1128.16, -2993.28, 14.33),
        vector3(1142.49, -2992.61, 14.33),
        vector3(1155.71, -2993.69, 11.51),
        vector3(1156.47, -2987.01, 14.33),
        vector3(1169.22, -2993.74, 14.33),
        vector3(1116.13, -2993.86, 17.15),
        vector3(1114.91, -2988.31, 11.51),
        vector3(1098.99, -2994.8, 8.69),
        -- END OF MIDDLE CRATE CONTAINERS







        -- TRAINS
        vector3(873.63, -3255.05, 5.9),
        vector3(869.79, -3272.77, 5.9),
        vector3(878.4, -3272.78, 5.9),
        vector3(883.84, -3276.42, 5.9),
        vector3(871.84, -3265.26, 10.03),
        vector3(879.06, -3263.57, 10.02),
        vector3(886.17, -3270.64, 10.04),
        vector3(896.25, -3269.35, 10.03),
        vector3(886.09, -3261.34, 5.9),
        vector3(895.92, -3261.9, 5.9),
        vector3(899.72, -3275.29, 10.02),
        vector3(911.15, -3274.53, 10.03),
        vector3(926.58, -3279.54, 10.02),
        vector3(907.59, -3284.68, 5.9),
        vector3(919.47, -3282.59, 5.9),
        vector3(927.86, -3287.07, 5.9),
        vector3(884.65, -3286.13, 5.9),
        vector3(895.2, -3287.04, 5.9),
        vector3(853.22, -3267.18, 5.9),
        vector3(858.38, -3258.79, 5.9),
        vector3(867.19, -3259.18, 5.9),
        vector3(855.37, -3285.77, 5.9),
        vector3(865.63, -3282.1, 5.9),
        vector3(843.14, -3276.12, 5.9),
        vector3(848.02, -3292.79, 5.9),
        vector3(847.41, -3305.96, 5.9),
        vector3(846.01, -3314.02, 5.9),
        vector3(860.42, -3310.7, 5.9),
        vector3(859.48, -3297.18, 5.9),
        vector3(867.13, -3294.79, 5.9),
        vector3(872.46, -3308.83, 5.9),
        vector3(874.09, -3298.6, 5.89),
        vector3(882.77, -3304.08, 5.9),
        vector3(886.57, -3309.76, 5.88),
        vector3(892.4, -3303.7, 5.9),
        vector3(903.35, -3309.38, 5.9),
        vector3(918.33, -3310.72, 5.9),
        vector3(932.15, -3308.24, 5.9),
        vector3(942.7, -3312.27, 5.9),
        vector3(890.47, -3298.55, 5.89),
        vector3(905.7, -3296.31, 5.9),
        vector3(921.66, -3298.98, 5.9),
        vector3(937.34, -3296.49, 5.9),
        vector3(953.02, -3298.92, 5.9),
        vector3(969.85, -3297.35, 5.9),
        vector3(985.62, -3299.0, 5.89),
        vector3(999.26, -3297.01, 5.9),
        vector3(1014.18, -3298.84, 5.9),
        vector3(1029.18, -3296.28, 5.9),
        vector3(1045.23, -3298.63, 5.9),
        vector3(1060.86, -3295.23, 5.9),
        vector3(1066.37, -3300.94, 5.88),
        vector3(1067.68, -3309.95, 5.9),
        vector3(1077.88, -3309.77, 5.9),
        vector3(1081.73, -3304.91, 5.9),
        vector3(1077.62, -3294.21, 5.9),
        vector3(1091.38, -3295.01, 5.9),
        vector3(1100.01, -3298.56, 5.9),
        vector3(1113.6, -3295.53, 5.9),
        vector3(1119.93, -3299.5, 5.9),
        vector3(1128.03, -3306.1, 5.9),
        vector3(1121.61, -3287.81, 5.9),
        vector3(1114.11, -3310.69, 5.91),
        vector3(1096.47, -3309.44, 5.9),
        vector3(1054.01, -3305.63, 9.05),
        vector3(1032.52, -3301.55, 9.05),
        vector3(1016.52, -3305.99, 8.99),
        vector3(999.87, -3301.61, 8.99),
        vector3(978.89, -3305.9, 9.05),
        vector3(960.92, -3301.67, 9.05),
        vector3(939.45, -3305.95, 8.99),
        vector3(922.4, -3301.59, 8.99),
        vector3(901.27, -3305.74, 9.05),
        vector3(883.77, -3301.42, 9.05),
        vector3(932.19, -3275.73, 5.9),
        vector3(923.63, -3269.17, 5.9),
        vector3(921.56, -3261.64, 5.9),
        vector3(900.75, -3253.09, 5.89),
        vector3(891.07, -3241.93, 5.9),
        vector3(906.25, -3245.95, 5.88),
        vector3(916.7, -3236.09, 5.9),
        vector3(928.42, -3238.74, 5.9),
        vector3(938.55, -3246.04, 5.9),
        vector3(954.01, -3240.39, 5.9),
        vector3(969.37, -3244.42, 5.9),
        vector3(982.26, -3240.74, 5.9),
        vector3(997.48, -3246.05, 5.9),
        vector3(1012.11, -3242.94, 5.89),
        vector3(1027.52, -3245.61, 5.89),
        vector3(1041.51, -3241.64, 5.89),
        vector3(1056.97, -3244.24, 5.89),
        vector3(1071.56, -3241.55, 5.89),
        vector3(1087.24, -3245.95, 5.9),
        vector3(1102.4, -3242.72, 5.9),
        vector3(1118.22, -3246.05, 5.89),
        vector3(1121.3, -3237.79, 5.89),
        vector3(1110.55, -3234.92, 5.89),
        vector3(1133.83, -3230.52, 5.9),
        vector3(1137.65, -3242.38, 5.9),
        vector3(1146.41, -3236.0, 5.9),
        vector3(1155.59, -3250.07, 5.9),
        vector3(1108.48, -3301.51, 9.05),
        vector3(1117.85, -3257.26, 5.9),
        vector3(1099.82, -3259.56, 5.9),
        vector3(1084.32, -3256.52, 5.9),
        vector3(1068.8, -3259.08, 5.9),
        vector3(1051.7, -3256.5, 5.87),
        vector3(1035.02, -3258.91, 5.9),
        vector3(1019.48, -3256.24, 5.89),
        vector3(1003.94, -3258.83, 5.9),
        vector3(986.52, -3256.3, 5.9),
        vector3(970.89, -3258.59, 5.9),
        vector3(954.33, -3256.01, 5.9),
        vector3(954.14, -3262.94, 5.9),
        vector3(936.6, -3258.08, 5.9),
        vector3(912.61, -3260.85, 5.9),
        vector3(943.04, -3268.79, 5.93),
        vector3(967.12, -3264.51, 5.9),
        vector3(981.65, -3262.99, 5.9),
        vector3(996.26, -3264.99, 5.9),
        vector3(1010.61, -3263.16, 5.9),
        vector3(1026.35, -3263.78, 5.9),
        vector3(1048.35, -3263.04, 5.9),
        vector3(1069.2, -3266.01, 5.89),
        vector3(1083.54, -3268.31, 5.9),
        vector3(1094.79, -3264.4, 5.9),
        vector3(1112.65, -3264.77, 5.9),
        vector3(1114.3, -3275.1, 5.9),
        vector3(1107.14, -3281.45, 5.9),
        vector3(1091.22, -3275.76, 5.9),
        vector3(1080.54, -3280.39, 5.9),
        vector3(1069.6, -3275.54, 5.9),
        vector3(1060.58, -3280.85, 5.9),
        vector3(1051.98, -3275.82, 5.9),
        vector3(1043.83, -3281.53, 5.9),
        vector3(1033.33, -3275.47, 5.9),
        vector3(1023.88, -3281.05, 5.9),
        vector3(1016.62, -3274.82, 5.9),
        vector3(1007.35, -3281.22, 5.9),
        vector3(996.38, -3274.91, 5.9),
        vector3(986.11, -3281.31, 5.9),
        vector3(976.35, -3274.79, 5.9),
        vector3(967.03, -3281.6, 5.9),
        vector3(961.25, -3274.16, 5.9),
        vector3(957.72, -3283.25, 5.9),
        vector3(961.17, -3292.98, 5.9),
        vector3(976.71, -3294.12, 5.9),
        vector3(991.43, -3292.34, 5.9),
        vector3(1010.51, -3292.33, 5.9),
        vector3(1034.18, -3292.13, 5.9),
        vector3(1051.07, -3291.76, 5.9),
        vector3(1065.84, -3291.94, 5.89),
        vector3(1062.21, -3286.78, 5.9),
        vector3(1085.23, -3286.78, 5.9),
        vector3(1108.94, -3286.99, 5.9),
        vector3(899.78, -3248.82, 9.09),
        vector3(918.55, -3253.4, 9.09),
        vector3(938.64, -3248.62, 9.03),
        vector3(956.61, -3253.45, 9.09),
        vector3(976.47, -3248.89, 9.09),
        vector3(996.7, -3248.8, 9.09),
        vector3(1014.37, -3253.18, 9.03),
        vector3(1034.36, -3248.92, 9.09),
        vector3(1053.28, -3253.33, 9.09),
        vector3(1073.05, -3248.73, 9.09),
        vector3(1091.09, -3253.12, 9.03),
        vector3(1110.92, -3248.9, 9.09),
        vector3(1113.5, -3267.45, 10.04),
        vector3(1110.73, -3272.04, 9.09),
        vector3(1096.57, -3267.38, 10.03),
        vector3(1089.74, -3272.39, 9.09),
        vector3(1072.41, -3271.99, 9.09),
        vector3(1054.39, -3267.82, 9.09),
        vector3(1053.32, -3272.01, 9.03),
        vector3(1034.15, -3272.0, 9.09),
        vector3(1035.62, -3267.19, 9.09),
        vector3(1014.35, -3272.19, 9.09),
        vector3(1016.33, -3267.63, 9.03),
        vector3(998.17, -3267.64, 9.09),
        vector3(996.55, -3271.89, 9.09),
        vector3(977.32, -3267.46, 9.09),
        vector3(976.7, -3271.81, 9.03),
        vector3(970.76, -3284.32, 10.03),
        vector3(985.12, -3284.43, 10.04),
        vector3(1002.56, -3284.49, 10.05),
        vector3(1019.5, -3284.32, 10.03),
        vector3(1035.39, -3284.48, 10.05),
        vector3(1062.06, -3284.6, 10.04),
        vector3(1079.95, -3284.46, 10.12),
        vector3(1095.04, -3284.4, 10.11),
        vector3(1111.2, -3284.39, 10.11),
        vector3(1093.79, -3305.79, 8.99),
        vector3(1144.73, -3312.77, 6.03),
        vector3(873.78, -3237.91, 5.9),
        vector3(858.28, -3236.1, 5.9),
        vector3(847.77, -3247.0, 5.9),
        vector3(842.65, -3265.7, 6.07),
        vector3(839.57, -3238.07, 5.9),
        vector3(865.5, -3245.61, 5.9),
        vector3(885.14, -3252.0, 5.89),
        vector3(1089.89, -3242.31, 8.71),
        vector3(1083.76, -3244.23, 15.99),
        vector3(1089.69, -3245.71, 23.5),
        vector3(1089.95, -3258.01, 23.42),
        vector3(1085.96, -3248.38, 24.22),
        vector3(1082.1, -3245.37, 23.12),
        vector3(1082.27, -3257.11, 23.42),
        vector3(986.36, -3257.67, 23.47),
        vector3(993.59, -3257.64, 23.5),
        vector3(989.82, -3248.4, 24.22),
        vector3(993.53, -3245.49, 23.5),
        vector3(986.53, -3245.73, 23.41),
        vector3(989.55, -3244.24, 15.99),
        vector3(936.68, -3246.92, 24.22),
        vector3(940.27, -3244.68, 23.5),
        vector3(932.95, -3255.74, 23.5),
        vector3(940.21, -3256.16, 23.48),
        vector3(932.75, -3244.08, 23.12),
        -- END OF TRAINS



        -- DOUBLE BUILDINGS AND PARKING LOT
        vector3(1172.33, -3279.83, 5.81),
        vector3(1179.2, -3283.38, 6.03),
        vector3(1180.1, -3277.09, 6.03),
        vector3(1181.1, -3292.8, 5.54),
        vector3(1174.06, -3303.65, 5.9),
        vector3(1179.8, -3305.03, 6.03),
        vector3(1173.97, -3317.61, 5.9),
        vector3(1180.34, -3319.91, 6.03),
        vector3(1174.22, -3332.35, 5.86),
        vector3(1186.22, -3335.24, 5.85),
        vector3(1190.24, -3328.99, 5.57),
        vector3(1197.87, -3330.36, 6.03),
        vector3(1203.9, -3337.95, 5.8),
        vector3(1205.59, -3329.46, 5.61),
        vector3(1218.38, -3339.56, 5.8),
        vector3(1218.82, -3328.83, 5.56),
        vector3(1232.98, -3341.72, 5.8),
        vector3(1225.15, -3330.45, 6.03),
        vector3(1233.43, -3328.07, 5.53),
        vector3(1243.29, -3331.32, 6.03),
        vector3(1254.5, -3331.35, 5.8),
        vector3(1243.92, -3322.56, 6.03),
        vector3(1252.69, -3313.95, 5.8),
        vector3(1243.22, -3304.51, 6.03),
        vector3(1251.92, -3293.9, 5.8),
        vector3(1242.18, -3289.45, 5.53),
        vector3(1252.56, -3280.15, 5.8),
        vector3(1244.96, -3280.51, 6.03),
        vector3(1252.38, -3270.64, 5.8),
        vector3(1245.39, -3270.13, 5.71),
        vector3(1246.41, -3262.82, 5.79),
        vector3(1251.7, -3253.07, 5.81),
        vector3(1242.92, -3256.51, 6.03),
        vector3(1244.2, -3247.27, 6.03),
        vector3(1251.14, -3239.62, 5.83),
        vector3(1244.93, -3239.2, 6.03),
        vector3(1244.47, -3229.72, 6.03),
        vector3(1252.53, -3223.57, 5.8),
        vector3(1234.97, -3223.81, 5.8),
        vector3(1233.36, -3230.59, 5.68),
        vector3(1226.82, -3232.54, 6.03),
        vector3(1220.95, -3225.03, 5.83),
        vector3(1218.32, -3231.77, 5.6),
        vector3(1212.54, -3232.54, 6.03),
        vector3(1206.45, -3226.03, 5.86),
        vector3(1205.45, -3233.66, 6.01),
        vector3(1197.51, -3224.74, 5.83),
        vector3(1200.6, -3238.77, 6.03),
        vector3(1205.07, -3242.44, 5.9),
        vector3(1197.54, -3242.19, 5.91),
        vector3(1191.07, -3243.2, 6.03),
        vector3(1183.71, -3231.69, 6.03),
        vector3(1184.92, -3224.49, 5.82),
        vector3(1173.24, -3230.65, 5.83),
        vector3(1178.38, -3246.23, 5.97),
        vector3(1184.67, -3246.8, 6.03),
        vector3(1181.31, -3255.8, 6.03),
        vector3(1195.44, -3255.62, 7.1),
        vector3(1207.23, -3249.58, 7.1),
        vector3(1196.26, -3249.96, 7.1),
        vector3(1180.68, -3271.25, 5.57),
        vector3(1177.65, -3263.04, 5.78),
        vector3(1156.3, -3250.49, 5.9),
        vector3(1154.98, -3238.01, 6.01),
        vector3(1146.9, -3239.57, 5.9),
        vector3(1148.71, -3249.44, 5.9),
        vector3(1139.95, -3238.7, 5.9),
        vector3(1134.4, -3230.32, 5.9),
        vector3(1131.68, -3236.04, 5.9),
        vector3(1122.38, -3233.61, 5.9),
        vector3(1113.07, -3230.83, 5.9),
        vector3(1208.12, -3252.8, 11.29),
        vector3(1198.81, -3255.97, 11.29),
        vector3(1207.74, -3241.43, 10.25),
        vector3(1193.56, -3260.3, 15.97),
        vector3(1184.89, -3259.45, 13.5),
        vector3(1193.15, -3274.95, 15.86),
        vector3(1185.68, -3279.8, 13.72),
        vector3(1192.44, -3285.37, 15.66),
        vector3(1185.83, -3289.85, 13.76),
        vector3(1192.63, -3297.56, 15.71),
        vector3(1192.78, -3311.32, 15.75),
        vector3(1185.76, -3320.67, 13.74),
        vector3(1196.38, -3318.99, 18.69),
        vector3(1199.16, -3305.89, 18.82),
        vector3(1199.79, -3287.05, 18.64),
        vector3(1199.77, -3266.28, 18.64),
        vector3(1208.95, -3260.57, 14.14),
        vector3(1210.09, -3277.99, 13.81),
        vector3(1203.83, -3291.9, 15.58),
        vector3(1209.45, -3297.68, 14.0),
        vector3(1208.91, -3316.12, 14.15),
        vector3(1215.96, -3319.88, 14.4),
        vector3(1221.22, -3297.1, 15.89),
        vector3(1221.17, -3272.31, 15.88),
        vector3(1220.3, -3244.42, 15.63),
        vector3(1224.46, -3242.68, 18.72),
        vector3(1227.26, -3259.33, 18.79),
        vector3(1227.74, -3282.75, 18.65),
        vector3(1227.46, -3298.44, 18.73),
        vector3(1224.48, -3307.73, 18.73),
        vector3(1229.85, -3319.43, 16.13),
        vector3(1238.03, -3321.44, 13.79),
        vector3(1233.04, -3312.77, 15.21),
        vector3(1237.16, -3304.19, 14.04),
        vector3(1229.75, -3293.68, 16.16),
        vector3(1238.51, -3275.98, 13.66),
        vector3(1230.03, -3267.36, 16.08),
        vector3(1238.73, -3259.24, 13.59),
        vector3(1229.88, -3254.61, 16.13),
        vector3(1237.34, -3243.17, 13.99),
        vector3(1231.84, -3244.89, 15.56),
        vector3(1269.85, -3260.39, 5.9),
        vector3(1269.2, -3243.55, 5.9),
        vector3(1279.07, -3255.78, 5.9),
        vector3(1279.82, -3247.6, 5.9),
        vector3(1244.38, -3206.07, 6.03),
        vector3(1246.19, -3200.02, 6.03),
        vector3(1251.67, -3188.24, 5.8),
        vector3(1244.47, -3185.81, 6.03),
        vector3(1251.89, -3170.63, 5.8),
        vector3(1243.1, -3160.82, 5.53),
        vector3(1246.36, -3152.15, 5.64),
        vector3(1252.16, -3146.65, 5.8),
        vector3(1243.05, -3139.11, 5.53),
        vector3(1252.4, -3128.09, 5.8),
        vector3(1246.67, -3123.79, 6.03),
        vector3(1252.94, -3110.73, 5.8),
        vector3(1273.95, -3124.83, 5.9),
        vector3(1274.43, -3115.16, 5.9),
        vector3(1266.45, -3107.52, 5.9),
        vector3(1276.67, -3102.51, 5.91),
        vector3(1243.62, -3105.32, 6.03),
        vector3(1244.25, -3098.09, 5.84),
        vector3(1229.23, -3096.43, 5.81),
        vector3(1226.24, -3083.94, 5.9),
        vector3(1214.36, -3098.79, 5.85),
        vector3(1210.26, -3105.04, 6.07),
        vector3(1204.27, -3106.63, 5.59),
        vector3(1197.65, -3106.77, 6.03),
        vector3(1193.64, -3099.04, 5.85),
        vector3(1187.94, -3107.68, 5.53),
        vector3(1181.15, -3098.62, 5.85),
        vector3(1175.3, -3108.64, 6.03),
        vector3(1171.74, -3118.97, 5.8),
        vector3(1179.88, -3115.6, 6.03),
        vector3(1170.07, -3129.38, 5.8),
        vector3(1178.68, -3128.62, 6.03),
        vector3(1180.96, -3139.85, 5.53),
        vector3(1174.36, -3143.37, 5.87),
        vector3(1170.37, -3151.08, 5.8),
        vector3(1167.84, -3159.05, 5.8),
        vector3(1177.97, -3160.22, 5.61),
        vector3(1179.34, -3168.64, 5.53),
        vector3(1172.39, -3174.11, 5.8),
        vector3(1183.31, -3170.21, 7.12),
        vector3(1182.72, -3181.36, 6.03),
        vector3(1175.34, -3188.58, 6.03),
        vector3(1190.62, -3181.24, 6.03),
        vector3(1184.54, -3179.1, 7.1),
        vector3(1156.24, -3180.43, 5.9),
        vector3(1153.94, -3189.28, 5.9),
        vector3(1154.03, -3200.58, 5.9),
        vector3(1178.18, -3194.9, 6.03),
        vector3(1179.77, -3204.23, 6.03),
        vector3(1185.93, -3198.3, 6.03),
        vector3(1190.11, -3206.6, 6.03),
        vector3(1201.96, -3196.41, 6.03),
        vector3(1200.39, -3186.24, 5.95),
        vector3(1212.05, -3205.86, 5.98),
        vector3(1219.77, -3204.13, 5.6),
        vector3(1227.2, -3206.21, 6.03),
        vector3(1234.25, -3203.07, 5.53),
        vector3(1232.5, -3282.4, 15.38),
        vector3(1238.04, -3294.72, 13.79),
        vector3(1219.91, -3255.04, 15.53),
        vector3(1184.93, -3175.82, 14.83),
        vector3(1189.3, -3164.79, 15.69),
        vector3(1208.13, -3176.2, 15.0),
        vector3(1207.87, -3157.88, 15.05),
        vector3(1186.85, -3145.84, 15.21),
        vector3(1208.19, -3135.21, 14.98),
        vector3(1185.95, -3122.56, 15.03),
        vector3(1196.68, -3114.88, 17.16),
        vector3(1197.27, -3138.08, 17.16),
        vector3(1198.45, -3161.68, 16.93),
        vector3(1214.61, -3115.64, 14.76),
        vector3(1226.75, -3115.28, 17.17),
        vector3(1238.01, -3112.76, 15.03),
        vector3(1239.76, -3132.89, 14.68),
        vector3(1226.88, -3132.81, 17.17),
        vector3(1226.95, -3153.26, 17.17),
        vector3(1226.93, -3194.04, 17.17),
        vector3(1238.17, -3184.97, 15.0),
        vector3(1217.55, -3185.53, 15.34),
        vector3(1215.72, -3167.66, 14.98),
        vector3(1236.47, -3166.66, 15.33),
        vector3(1216.73, -3145.35, 15.18),
        vector3(1236.27, -3143.77, 15.37),
        vector3(1236.69, -3122.37, 15.29),
        vector3(1215.58, -3123.53, 14.95),
        vector3(1203.66, -3123.53, 15.89),
        vector3(1058.34, -3208.34, 5.9),
        vector3(1062.29, -3186.8, 5.9),
        vector3(1048.17, -3185.06, 5.9),
        vector3(1043.3, -3208.53, 5.89),
        vector3(1029.08, -3186.51, 5.9),
        vector3(1026.24, -3206.37, 5.9),
        vector3(1013.67, -3207.57, 5.9),
        vector3(1003.17, -3204.34, 5.9),
        vector3(998.27, -3212.89, 5.9),
        vector3(1017.35, -3190.72, 5.9),
        vector3(1014.75, -3181.07, 5.9),
        vector3(994.94, -3180.86, 5.9),
        vector3(995.22, -3191.95, 5.9),
        vector3(969.52, -3203.01, 5.9),
        vector3(968.27, -3214.15, 5.9),
        vector3(960.22, -3209.47, 5.9),
        -- RANDOM LOCATIONS
        vector3(1037.97, -3031.43, 5.9),
        vector3(1028.91, -3034.2, 5.9),
        vector3(1034.27, -3042.65, 5.9),
        vector3(1034.26, -3024.35, 5.9),
        vector3(1020.15, -3043.83, 5.9),
        vector3(1020.4, -3024.92, 5.9),
        vector3(1026.57, -3037.01, 8.69),
        vector3(1014.4, -3031.3, 8.69),
        vector3(1000.34, -3031.52, 8.69),
        vector3(996.24, -3037.19, 5.9),
        vector3(1004.04, -3034.36, 5.9),
        vector3(1006.56, -3042.85, 5.9),
        vector3(1006.39, -3026.76, 5.9),
        vector3(1011.92, -3034.06, 5.9),
        vector3(1000.27, -3043.71, 14.33),
        vector3(999.07, -3027.47, 11.51),
        vector3(997.91, -3022.2, 17.15),
        vector3(1012.78, -3022.32, 14.33),
        vector3(1013.05, -3046.21, 17.15),
        vector3(1027.31, -3046.13, 14.33),
        vector3(1026.74, -3039.83, 14.33),
        vector3(1026.08, -3022.62, 17.15),
        vector3(1040.09, -3024.97, 14.33),
        vector3(1055.97, -3027.7, 17.15),
        vector3(1041.18, -3044.69, 14.33),
        vector3(1055.03, -3015.35, 19.99),
        vector3(1048.25, -3017.07, 15.97),
        vector3(1053.49, -3017.11, 15.96),
        vector3(1056.57, -3020.03, 23.09),
        vector3(1052.35, -3021.41, 24.19),
        vector3(1063.02, -3069.49, 5.9),
        vector3(1048.13, -3071.43, 5.9),
        vector3(1033.62, -3068.39, 5.9),
        vector3(1022.13, -3071.18, 5.9),
        vector3(1009.79, -3066.46, 5.9),
        vector3(994.84, -3069.87, 5.9),
        vector3(982.28, -3079.01, 5.9),
        vector3(987.33, -3090.87, 5.9),
        vector3(987.51, -3107.39, 5.9),
        vector3(1004.34, -3105.98, 5.9),
        vector3(1018.93, -3108.3, 5.9),
        vector3(1032.79, -3105.35, 5.9),
        vector3(1046.33, -3107.88, 5.9),
        vector3(1060.16, -3105.18, 5.9),
        vector3(1072.13, -3105.41, 5.9),
        vector3(1068.61, -3092.47, 5.9),
        vector3(1070.36, -3079.6, 5.9),
        vector3(1060.01, -3087.56, 5.9),
        vector3(1041.59, -3088.42, 5.9),
        vector3(1048.45, -3082.22, 5.9),
        vector3(1048.3, -3095.88, 5.9),
        vector3(1027.98, -3087.92, 5.9),
        vector3(1034.58, -3095.52, 5.9),
        vector3(1034.64, -3081.22, 5.9),
        vector3(1014.69, -3088.25, 5.9),
        vector3(1020.77, -3095.1, 5.9),
        vector3(1020.77, -3080.42, 5.9),
        vector3(1006.33, -3096.91, 5.9),
        vector3(995.6, -3087.56, 5.9),
        vector3(1003.38, -3090.69, 5.9),
        vector3(1006.33, -3081.53, 5.9),
        vector3(1002.66, -3074.61, 5.9),
        vector3(999.11, -3078.79, 14.33),
        vector3(998.96, -3099.73, 14.33),
        vector3(1012.97, -3090.88, 14.33),
        vector3(1027.28, -3094.85, 17.15),
        vector3(1030.23, -3101.2, 17.15),
        vector3(1041.97, -3096.85, 17.15),
        vector3(1043.06, -3101.17, 17.11),
        vector3(1024.2, -3080.36, 17.15),
        vector3(1032.29, -3079.23, 17.15),
        vector3(1036.75, -3079.35, 17.15),
        vector3(1044.07, -3083.98, 17.15),
        vector3(1014.04, -3084.26, 17.15),
        vector3(1012.52, -3080.3, 14.33),
        vector3(1042.35, -3074.75, 14.33),
        vector3(1055.69, -3077.49, 14.33),
        vector3(1056.58, -3074.43, 11.51),
        vector3(1055.38, -3090.76, 14.33),
        vector3(1058.54, -3093.53, 17.15),
        vector3(1051.62, -3097.63, 17.15),
        vector3(1041.69, -3091.54, 11.51),
        vector3(1002.09, -3070.92, 15.97),
        vector3(1007.16, -3070.92, 15.96),
        vector3(1008.98, -3068.95, 19.99),
        vector3(1010.02, -3072.7, 23.48),
        vector3(1010.68, -3082.15, 23.09),
        vector3(1006.33, -3075.01, 24.19),
        vector3(1092.36, -3052.28, 5.9),
        vector3(1109.18, -3055.5, 5.9),
        vector3(1123.84, -3051.67, 5.9),
        vector3(1139.43, -3054.91, 5.9),
        vector3(1155.33, -3051.31, 5.9),
        vector3(1171.37, -3054.98, 5.9),
        vector3(1182.18, -3047.66, 5.9),
        vector3(1187.69, -3034.61, 5.9),
        vector3(1180.63, -3019.57, 5.9),
        vector3(1167.43, -3010.52, 5.9),
        vector3(1153.27, -3014.53, 5.9),
        vector3(1138.98, -3013.0, 5.9),
        vector3(1126.11, -3017.91, 5.9),
        vector3(1111.07, -3013.94, 5.9),
        vector3(1094.51, -3018.21, 5.9),
        vector3(1083.82, -3025.16, 5.9),
        vector3(1087.46, -3036.42, 5.9),
        vector3(1084.06, -3049.89, 5.9),
        vector3(1097.39, -3034.28, 5.9),
        vector3(1111.02, -3034.48, 5.9),
        vector3(1116.88, -3031.21, 5.9),
        vector3(1107.47, -3024.56, 5.9),
        vector3(1107.35, -3042.54, 5.9),
        vector3(1121.42, -3024.36, 5.9),
        vector3(1121.36, -3043.22, 5.9),
        vector3(1125.21, -3034.74, 5.9),
        vector3(1133.24, -3033.55, 5.9),
        vector3(1135.39, -3041.73, 5.9),
        vector3(1135.4, -3026.31, 5.9),
        vector3(1138.5, -3034.1, 5.9),
        vector3(1154.29, -3034.16, 5.9),
        vector3(1149.37, -3026.52, 5.9),
        vector3(1149.14, -3042.31, 5.9),
        vector3(1163.5, -3025.63, 5.9),
        vector3(1163.51, -3043.78, 5.9),
        vector3(1169.25, -3037.18, 8.69),
        vector3(1169.37, -3042.58, 14.33),
        vector3(1158.21, -3038.32, 11.51),
        vector3(1156.92, -3046.32, 17.15),
        vector3(1143.18, -3040.94, 14.33),
        vector3(1155.81, -3027.61, 14.33),
        vector3(1155.05, -3020.75, 17.15),
        vector3(1143.38, -3026.17, 14.33),
        vector3(1129.39, -3021.09, 14.33),
        vector3(1132.38, -3023.29, 17.15),
        vector3(1126.4, -3024.27, 17.15),
        vector3(1129.8, -3030.73, 17.15),
        vector3(1114.15, -3025.28, 14.33),
        vector3(1115.4, -3020.85, 17.15),
        vector3(1118.65, -3037.71, 17.15),
        vector3(1111.59, -3037.7, 17.15),
        vector3(1114.87, -3043.83, 17.15),
        vector3(1123.57, -3043.13, 17.15),
        vector3(1131.77, -3043.27, 17.15),
        vector3(1100.32, -3043.62, 14.33),
        vector3(1100.99, -3024.88, 17.15),
        vector3(1101.02, -3030.13, 11.51),
        vector3(1183.03, -2965.87, 5.9),
        vector3(1170.3, -2958.62, 5.9),
        vector3(1156.19, -2962.7, 5.9),
        vector3(1134.14, -2961.37, 5.9),
        vector3(1120.28, -2965.06, 5.9),
        vector3(1108.56, -2962.51, 5.9),
        vector3(1097.77, -2965.71, 5.9),
        vector3(1085.92, -2964.14, 5.9),
        vector3(1091.15, -2976.13, 5.9),
        vector3(1085.14, -2991.61, 5.9),
        vector3(1094.17, -3002.27, 5.9),
        vector3(1107.83, -2997.93, 5.9),
        vector3(1124.64, -3001.23, 5.9),
        vector3(1138.43, -2998.38, 5.9),
        vector3(1152.88, -3001.36, 5.9),
        vector3(1168.66, -2997.46, 5.9),
        vector3(1183.25, -3000.91, 5.9),
        vector3(1191.27, -2986.81, 5.9),
        vector3(1179.96, -2981.09, 5.9),
        vector3(1172.75, -2968.75, 5.9),
        vector3(1166.13, -2977.24, 5.9),
        vector3(1159.0, -2981.51, 5.9),
        vector3(1144.27, -2981.5, 5.9),
        vector3(1149.41, -2988.94, 5.9),
        vector3(1149.34, -2974.46, 5.9),
        vector3(1130.58, -2981.37, 5.9),
        vector3(1135.21, -2989.39, 5.9),
        vector3(1135.69, -2973.99, 5.9),
        vector3(1115.41, -2981.46, 5.9),
        vector3(1121.53, -2990.16, 5.9),
        vector3(1121.38, -2972.88, 5.9),
        vector3(1107.38, -2988.66, 5.9),
        vector3(1107.75, -2975.1, 5.9),
        vector3(1101.4, -2981.45, 5.9),
        vector3(1098.85, -2972.73, 17.15),
        vector3(1104.23, -2984.91, 14.33),
        vector3(1096.08, -2988.02, 14.33),
        vector3(1113.02, -2976.21, 14.33),
        vector3(1114.15, -2971.94, 17.15),
        vector3(1127.6, -2969.51, 17.15),
        vector3(1128.71, -2974.84, 11.51),
        vector3(1127.48, -2984.91, 11.51),
        vector3(1141.87, -2974.66, 17.15),
        vector3(1141.82, -2970.67, 14.33),
        vector3(1157.6, -2973.45, 14.33),
        vector3(1146.58, -2985.35, 17.15),
        vector3(1138.72, -2987.85, 17.15),
        vector3(1128.16, -2993.28, 14.33),
        vector3(1142.49, -2992.61, 14.33),
        vector3(1155.71, -2993.69, 11.51),
        vector3(1156.47, -2987.01, 14.33),
        vector3(1169.22, -2993.74, 14.33),
        vector3(1116.13, -2993.86, 17.15),
        vector3(1114.91, -2988.31, 11.51),
        vector3(1098.99, -2994.8, 8.69),
        vector3(810.48, -2983.8, 21.37),
        vector3(810.51, -2973.45, 21.37),
        vector3(803.39, -2999.51, 28.41),
        vector3(806.65, -2995.01, 28.39),
        vector3(799.3, -2994.98, 28.4),
        vector3(812.54, -2991.21, 24.68),
        vector3(810.82, -2994.8, 24.68),
        vector3(816.32, -2995.14, 24.68),
        vector3(820.0, -2991.29, 24.68),
        vector3(821.63, -2997.12, 24.68),
        vector3(768.3, -3154.92, 5.9),
        vector3(772.72, -3149.25, 5.93),
        vector3(764.92, -3144.3, 5.9),
        vector3(762.4, -3138.34, 5.9),
        vector3(755.85, -3144.0, 5.9),
        vector3(754.9, -3151.29, 5.9),
        vector3(746.31, -3146.64, 5.9),
        vector3(746.14, -3156.47, 5.9),
        vector3(760.84, -3159.11, 5.9),
        vector3(755.45, -3166.1, 5.9),
        vector3(747.55, -3169.84, 5.9),
        vector3(746.11, -3179.63, 5.9),
        vector3(740.39, -3169.07, 5.9),
        vector3(739.44, -3187.68, 5.9),
        vector3(746.87, -3195.21, 5.9),
        vector3(736.56, -3198.15, 5.9),
        vector3(743.67, -3205.49, 5.9),
        vector3(737.37, -3209.62, 5.9),
        vector3(728.32, -3195.73, 5.9),
        vector3(743.65, -3217.48, 5.93),
        vector3(748.58, -3206.99, 8.69),
        vector3(725.7, -3181.02, 5.9),
        vector3(753.45, -3229.0, 6.05),
        vector3(754.86, -3223.33, 6.0),
        vector3(761.28, -3217.43, 6.03),
        vector3(764.99, -3226.17, 5.92),
        vector3(770.2, -3219.65, 5.9),
        vector3(768.87, -3212.67, 5.9),
        vector3(765.7, -3204.06, 5.98),
        vector3(777.34, -3202.06, 5.9),
        vector3(766.16, -3195.69, 5.94),
        vector3(767.03, -3187.98, 5.9),
        vector3(778.37, -3181.5, 5.9),
        vector3(774.39, -3170.57, 5.9),
        vector3(763.06, -3177.08, 5.9),
        vector3(784.16, -3173.95, 5.93),
        vector3(784.45, -3193.34, 5.9),
        vector3(787.32, -3206.02, 5.9),
        vector3(799.12, -3206.36, 5.9),
        vector3(799.59, -3196.86, 5.9),
        vector3(799.31, -3185.91, 6.03),
        vector3(810.27, -3186.86, 5.9),
        vector3(811.1, -3198.27, 5.9),
        vector3(817.16, -3189.91, 5.9),
        vector3(819.04, -3200.55, 5.9),
        vector3(826.7, -3204.62, 5.9),
        vector3(827.44, -3212.83, 5.9),
        vector3(836.45, -3212.06, 5.9),
        vector3(834.95, -3204.87, 5.9),
        vector3(846.48, -3211.51, 5.9),
        vector3(846.99, -3205.21, 5.99),
        vector3(844.21, -3221.85, 5.9),
        vector3(856.26, -3206.59, 5.9),
        vector3(861.57, -3217.43, 5.9),
        vector3(866.73, -3206.56, 5.9),
        vector3(871.77, -3215.81, 5.9),
        vector3(869.08, -3225.85, 5.9),
        vector3(871.62, -3199.66, 5.9),
        vector3(873.32, -3191.51, 5.92),
        vector3(866.27, -3183.15, 5.98),
        vector3(857.11, -3183.06, 5.98),
        vector3(858.77, -3178.57, 6.06),
        vector3(848.04, -3180.11, 6.04),
        vector3(846.85, -3183.73, 5.94),
        vector3(839.87, -3179.74, 6.06),
        vector3(832.16, -3183.26, 5.9),
        vector3(818.17, -3182.84, 5.95),
        vector3(809.69, -3178.77, 5.99),
        vector3(798.42, -3174.65, 5.99),
        vector3(853.99, -3187.12, 14.5),
        vector3(854.3, -3192.69, 14.5),
        vector3(844.52, -3198.87, 14.5),
        vector3(836.63, -3197.82, 14.5),
        vector3(826.88, -3200.36, 14.5),
        vector3(826.89, -3190.37, 14.5),
        vector3(836.36, -3188.91, 14.5),
        vector3(835.21, -3193.1, 14.5),
        vector3(848.33, -3196.13, 11.9),
        vector3(848.14, -3201.53, 11.9),
        vector3(854.84, -3196.06, 11.9),
        vector3(859.49, -3201.59, 11.9),
        vector3(865.04, -3200.19, 11.9),
        vector3(858.56, -3188.28, 11.9),
        vector3(866.49, -3187.56, 11.9),
        vector3(862.31, -3193.89, 11.9),
    },
    ["cayo-perico"] = {
        vector3(4979.43, -5721.11, 19.91),
        vector3(4998.83, -5735.57, 19.87),
        vector3(4988.21, -5761.49, 20.88),
        vector3(4960.95, -5790.54, 26.27),
        vector3(4989.52, -5787.29, 20.88),
        vector3(5028.89, -5781.65, 16.35),
        vector3(5077.97, -5744.12, 15.7),
        vector3(5059.55, -5715.43, 14.59),
        vector3(5033.06, -5689.73, 19.88),
        vector3(5028.03, -5720.74, 17.67),
        vector3(4991.12, -5733.36, 14.84),
        vector3(5016.24, -5746.15, 15.48),
        vector3(4986.81, -5770.35, 15.89),
        vector3(4909.07, -5834.38, 28.22),
        vector3(4913.39, -5841.04, 28.1),
        vector3(4989.81, -5876.2, 20.53),
        vector3(4984.03, -5879.85, 20.54),
        vector3(4987.91, -5885.25, 20.54),
        vector3(4992.91, -5881.25, 20.54),
        vector3(4955.53, -5899.06, 15.21),
        vector3(4965.3, -5604.11, 23.73),
        vector3(4887.26, -5460.53, 30.74),
        vector3(4887.75, -5457.26, 47.52),
        vector3(4821.6, -5438.71, 16.51),
        vector3(4827.39, -5431.26, 16.5),
        vector3(4902.42, -5345.84, 10.15),
        vector3(4904.55, -5343.02, 20.44),
        vector3(4901.5, -5334.63, 35.61),
        vector3(4950.31, -5321.63, 8.06),
        vector3(4966.92, -5320.15, 8.12),
        vector3(4953.23, -5269.78, 7.13),
        vector3(4917.44, -5275.48, 8.29),
        vector3(4931.96, -5295.9, 5.58),
        vector3(4921.18, -5240.32, 2.52),
        vector3(4896.95, -5171.97, 2.46),
        vector3(4866.7, -5161.46, 2.44),
        vector3(4837.13, -5176.67, 2.18),
        vector3(4931.46, -5146.56, 2.47),
        vector3(4954.2, -5184.35, 4.9),
        vector3(4947.58, -5209.05, 2.52),
        vector3(4982.65, -5213.17, 2.5),
        vector3(5012.46, -5200.84, 2.52),
        vector3(5011.65, -5155.28, 3.93),
        vector3(5004.3, -5123.85, 3.0),
        vector3(4960.35, -5107.5, 2.98),
        vector3(4962.26, -5107.31, 2.98),
        vector3(4959.17, -5084.52, 3.23),
        vector3(4870.03, -5121.08, 2.1),
        vector3(5043.39, -5115.25, 22.94),
        vector3(5003.92, -5736.87, 16.04),
        vector3(4979.43, -5721.11, 19.91),
        vector3(4990.33, -5751.32, 19.9),
        vector3(4966.75, -5758.26, 21.97),
        vector3(4982.93, -5765.7, 20.94),
        vector3(5006.47, -5805.99, 18.33),
        vector3(5067.34, -5776.38, 16.32),
        vector3(5079.92, -5733.12, 21.04),
        vector3(5043.81, -5705.33, 14.59),
        vector3(5017.9, -5698.52, 19.86),
        vector3(5003.92, -5736.87, 16.04),
        vector3(4987.53, -5751.24, 13.84),
        vector3(5004.63, -5753.77, 15.48),
        vector3(4999.58, -5778.34, 16.28),
        vector3(4897.62, -5837.35, 28.28),
        vector3(4914.53, -5832.19, 29.6),
        vector3(4985.87, -5876.65, 20.54),
        vector3(4984.75, -5883.76, 20.54),
        vector3(4991.62, -5884.88, 20.54),
        vector3(4992.71, -5878.13, 20.54),
        vector3(4951.88, -5897.16, 13.9),
        vector3(4962.73, -5599.08, 25.18),
        vector3(4905.03, -5457.17, 30.76),
        vector3(4884.14, -5453.73, 31.63),
        vector3(4877.66, -5289.26, 8.52),
        vector3(4838.27, -5421.15, 20.14),
        vector3(4907.68, -5335.3, 10.03),
        vector3(4961.28, -5325.23, 12.35),
        vector3(4928.0, -5323.84, 7.22),
        vector3(4952.8, -5299.65, 8.54),
        vector3(4968.77, -5274.87, 5.92),
        vector3(4929.12, -5251.87, 3.38),
        vector3(4895.97, -5282.33, 8.48),
        vector3(4954.54, -5270.21, 4.61),
        vector3(4888.73, -5204.44, 2.67),
        vector3(4882.97, -5192.09, 3.49),
        vector3(4850.08, -5162.37, 3.6),
        vector3(4840.88, -5180.97, 3.8),
        vector3(4930.07, -5174.39, 2.48),
        vector3(4964.29, -5158.49, 3.74),
        vector3(4982.04, -5199.89, 4.74),
        vector3(5000.8, -5214.55, 2.5),
        vector3(5003.37, -5191.03, 2.52),
        vector3(4999.89, -5150.35, 4.45),
        vector3(4995.23, -5113.62, 5.39),
        vector3(4961.34, -5108.37, 2.98),
        vector3(4963.26, -5108.99, 2.98),
        vector3(4932.45, -5107.22, 2.58),
        vector3(4879.81, -5111.38, 2.36),
        vector3(5043.39, -5115.25, 22.94),
        vector3(5004.69, -5735.1, 16.84),
        vector3(4962.25, -5594.66, 23.77),
        vector3(4905.03, -5457.17, 30.76),
        vector3(4887.57, -5457.46, 47.52),
        vector3(4887.6, -5274.93, 8.31),
        vector3(4838.67, -5422.15, 19.14),
        vector3(4903.52, -5332.2, 9.97),
        vector3(4904.49, -5338.38, 35.61),
        vector3(4923.51, -5316.78, 7.24),
        vector3(4938.11, -5260.0, 2.7),
        vector3(5143.94, -5103.22, 2.25),
        vector3(4956.13, -5216.96, 2.51),
        vector3(4964.11, -5197.62, 3.12),
        vector3(4989.89, -5194.9, 3.15),
        vector3(5000.32, -5163.24, 3.7),
        vector3(5001.38, -5165.39, 3.7),
        vector3(5000.97, -5167.62, 3.39),
        vector3(4995.8, -5120.83, 3.3),
        vector3(4988.9, -5122.67, 4.75),
        vector3(4959.66, -5130.31, 2.45),
        vector3(4882.92, -5111.9, 3.57),
        vector3(4953.05, -5085.75, 4.12),
        vector3(4963.57, -5297.66, 6.24),
        vector3(4964.34, -5280.3, 6.2),
        vector3(5140.52, -5243.96, 26.3),
        vector3(5140.65, -5246.35, 9.47),
        vector3(5141.19, -5190.25, 2.46),
        vector3(5136.96, -5189.35, 4.28),
        vector3(5136.67, -5193.71, 2.4),
        vector3(5134.65, -5200.01, 2.6),
        vector3(5110.03, -5186.98, 2.91),
        vector3(5106.85, -5165.96, 2.06),
        vector3(5106.66, -5155.04, 2.22),
        vector3(5118.19, -5163.08, 2.85),
        vector3(5118.09, -5168.39, 3.56),
        vector3(5124.28, -5141.98, 2.21),
        vector3(5118.22, -5128.84, 3.26),
        vector3(5107.15, -5127.47, 2.0),
        vector3(5109.46, -5114.74, 2.54),
        vector3(5118.17, -5122.26, 3.75),
        vector3(5137.98, -5093.54, 2.22),
        vector3(5133.85, -5097.54, 2.18),
        vector3(5141.29, -5098.48, 2.21),
        vector3(5141.38, -5111.64, 4.06),
        vector3(5157.19, -5113.6, 3.29),
        vector3(5153.6, -5125.81, 2.27),
        vector3(5157.11, -5123.81, 2.33),
        vector3(5153.26, -5137.61, 2.3),
        vector3(5151.77, -5149.24, 3.34),
        vector3(5182.3, -5146.9, 3.51),
        vector3(5188.01, -5153.21, 3.62),
        vector3(5188.71, -5147.64, 3.62),
        vector3(5183.92, -5143.57, 3.4),
        vector3(5187.68, -5143.38, 3.59),
        vector3(5196.79, -5133.75, 4.29),
        vector3(5195.32, -5136.03, 3.35),
        vector3(5188.63, -5130.23, 4.49),
        vector3(5136.37, -5135.35, 4.94),
        vector3(5141.02, -5123.15, 8.43),
        vector3(5141.07, -5117.91, 8.43),
        vector3(5137.34, -5123.79, 2.94),
        vector3(5133.1, -5081.31, 2.34),
        vector3(5141.85, -5084.23, 2.35),
        vector3(5140.28, -5079.31, 2.35),
        vector3(5204.05, -5119.69, 6.15),
        vector3(5210.98, -5126.14, 6.21),
        vector3(5209.42, -5132.06, 6.19),
        vector3(5214.94, -5130.62, 6.24),
        vector3(5214.58, -5126.17, 6.23),
        vector3(5208.91, -5140.83, 8.65),
        vector3(5213.4, -5144.13, 9.01),
        vector3(3899.15, -4697.91, 7.14),
        vector3(3897.71, -4721.26, 4.44),
        vector3(3877.37, -4765.63, 2.18),
        vector3(3903.32, -4799.02, 2.61),
        vector3(3816.42, -4643.82, 1.92),
        vector3(3858.22, -4640.13, 2.11),
        vector3(4074.52, -4685.08, 4.16),
        vector3(4053.45, -4694.25, 4.15),
        vector3(3987.21, -4675.47, 4.18),
        vector3(4027.23, -4632.74, 3.38),
        vector3(4072.65, -4667.58, 4.28),
        vector3(4126.23, -4477.29, 4.45),
        vector3(4102.59, -4527.36, 3.7),
        vector3(4228.2, -4589.88, 4.18),
        vector3(4281.48, -4540.63, 4.23),
        vector3(4288.49, -4528.65, 4.45),
        vector3(4339.63, -4550.92, 4.18),
        vector3(4370.79, -4570.53, 4.21),
        vector3(4369.1, -4586.3, 4.21),
        vector3(4361.72, -4578.95, 4.21),
        vector3(4459.58, -4506.59, 4.18),
        vector3(4436.92, -4446.44, 4.33),
        vector3(4447.29, -4451.59, 4.33),
        vector3(4430.03, -4467.26, 6.01),
        vector3(4453.55, -4465.37, 4.33),
        vector3(4434.01, -4477.44, 4.33),
        vector3(4438.34, -4461.12, 4.33),
        vector3(4441.9, -4459.76, 4.33),
        vector3(4430.03, -4467.41, 6.01),
        vector3(4410.18, -4470.19, 4.39),
        vector3(4433.29, -4443.84, 6.51),
        vector3(4272.92, -4363.01, 22.46),
        vector3(4259.41, -4367.51, 21.62),
        vector3(4172.51, -4344.53, 2.2),
        vector3(4257.84, -4273.78, 2.4),
        vector3(4215.2, -4530.34, 4.43),
        vector3(4505.76, -4396.58, 3.72),
        vector3(4433.43, -4584.49, 1.87),
        vector3(4519.6, -4513.21, 4.53),
        vector3(4539.57, -4524.17, 5.36),
        vector3(4501.09, -4563.96, 7.53),
        vector3(4505.52, -4553.15, 4.17),
        vector3(4503.54, -4555.38, 4.17),
        vector3(4502.55, -4553.0, 4.17),
        vector3(4472.1, -4595.27, 5.56),
        vector3(4491.21, -4585.23, 5.55),
        vector3(4498.47, -4616.21, 8.85),
        vector3(4504.93, -4654.21, 11.58),
        vector3(4494.78, -4737.69, 11.94),
        vector3(4490.6, -4735.18, 10.4),
        vector3(4355.27, -4630.79, 2.34),
        vector3(4500.28, -4461.53, 4.21),
        vector3(4427.38, -4451.01, 7.24),
        vector3(4445.61, -4443.1, 7.24),
        vector3(4446.68, -4445.0, 7.24),
        vector3(4442.19, -4447.41, 7.24),
        vector3(4622.55, -4515.62, 11.27),
        vector3(4682.07, -4403.67, 3.62),
        vector3(4649.3, -4447.57, 7.35),
        vector3(4708.8, -4338.07, 2.37),
        vector3(4656.69, -4557.3, 23.1),
        vector3(4673.06, -4544.51, 23.34),
        vector3(4751.28, -4685.98, 3.64),
        vector3(4732.87, -4694.29, 1.43),
        vector3(4802.02, -4722.86, 5.69),
        vector3(4782.9, -4754.33, 4.86),
        vector3(4772.19, -4776.64, 8.86),
        vector3(4616.44, -4687.35, 2.04),
        vector3(4807.18, -4654.38, 16.8),
        vector3(4817.73, -4653.79, 16.86),
        vector3(4854.52, -4625.05, 15.64),
        vector3(4876.72, -4627.22, 14.62),
        vector3(4891.83, -4623.98, 14.57),
        vector3(4860.26, -4638.73, 14.25),
        vector3(4926.73, -4816.5, 23.95),
        vector3(4890.73, -4791.75, 2.47),
        vector3(4894.17, -4799.1, 2.65),
        vector3(4772.59, -4732.86, 2.24),
        vector3(4759.54, -4555.92, 25.7),
        vector3(4774.18, -4569.22, 24.65),
        vector3(4815.2, -4314.81, 9.78),
        vector3(4818.77, -4308.42, 5.48),
        vector3(4799.04, -4322.66, 5.93),
        vector3(4801.21, -4384.65, 20.89),
        vector3(4847.6, -4437.5, 9.45),
        vector3(4877.96, -4488.6, 26.93),
        vector3(4957.68, -4470.67, 10.58),
        vector3(4963.42, -4474.04, 14.03),
        vector3(5008.14, -4407.48, 3.77),
        vector3(5017.63, -4514.09, 9.4),
        vector3(4866.22, -4479.12, 11.41),
        vector3(4961.25, -4819.71, 6.24),
        vector3(4600.42, -4872.25, 18.17),
        vector3(4596.6, -4877.99, 18.34),
        vector3(4586.86, -4876.35, 18.0),
        vector3(4598.03, -4866.01, 17.51),
        vector3(4594.98, -4872.95, 17.94),
        vector3(4566.66, -4897.24, 15.72),
        vector3(4521.05, -4986.23, 5.54),
        vector3(4503.04, -4958.31, 5.22),
        vector3(4540.48, -4882.47, 11.27),
        vector3(4666.57, -4864.25, 14.14),
        vector3(4663.08, -4802.48, 11.26),
        vector3(4863.3, -4955.32, 2.53),
        vector3(4867.5, -4939.86, 2.45),
        vector3(4872.59, -4917.67, 2.92),
        vector3(4866.45, -4907.2, 2.56),
        vector3(4905.36, -4936.65, 3.37),
        vector3(4905.5, -4942.96, 3.38),
        vector3(4916.48, -4908.81, 3.37),
        vector3(4955.63, -4901.9, 7.84),
        vector3(4990.68, -4934.31, 7.77),
        vector3(5008.81, -4925.18, 9.63),
        vector3(4893.42, -4902.86, 3.49),
        vector3(5102.97, -4510.55, 2.39),
        vector3(5123.02, -4543.47, 2.13),
        vector3(5171.12, -4586.54, 4.91),
        vector3(5176.78, -4590.39, 4.93),
        vector3(5169.57, -4591.14, 3.63),
        vector3(5107.58, -4583.07, 29.72),
        vector3(5109.9, -4579.65, 29.72),
        vector3(5104.59, -4581.15, 29.72),
        vector3(5103.38, -4583.42, 23.25),
        vector3(5105.03, -4582.16, 4.21),
        vector3(5133.05, -4615.14, 2.41),
        vector3(5132.49, -4612.97, 2.47),
        vector3(5135.25, -4613.25, 2.46),
        vector3(5121.84, -4612.4, 4.23),
        vector3(5100.64, -4624.4, 2.63),
        vector3(5116.38, -4638.43, 1.41),
        vector3(5140.51, -4645.74, 1.41),
        vector3(5147.75, -4617.85, 2.87),
        vector3(5170.48, -4617.09, 2.86),
        vector3(5170.42, -4609.95, 2.86),
        vector3(5178.32, -4649.57, 2.53),
        vector3(5180.34, -4670.24, 7.23),
        vector3(5175.46, -4659.05, 6.44),
        vector3(5171.93, -4675.35, 5.99),
        vector3(5166.77, -4711.99, 2.16),
        vector3(5237.0, -4632.21, 1.86),
        vector3(5153.22, -4656.5, 1.44),
        vector3(5136.05, -4702.43, 5.1),
        vector3(5145.25, -4730.59, 1.44),
        vector3(5108.47, -4700.8, 3.56),
        vector3(5117.78, -4674.75, 4.23),
        vector3(5079.79, -4662.01, 2.44),
        vector3(5092.14, -4680.87, 2.41),
        vector3(5091.23, -4685.09, 2.41),
        vector3(5092.64, -4683.04, 2.41),
        vector3(5104.07, -4682.18, 8.71),
        vector3(5035.1, -4628.58, 11.32),
        vector3(5032.84, -4630.98, 21.68),
        vector3(5079.51, -4634.71, 2.19),
        vector3(5051.61, -4590.35, 3.14),
        vector3(5062.93, -4590.69, 2.86),
        vector3(5067.29, -4591.44, 2.86),
        vector3(5065.09, -4592.44, 2.85),
        vector3(5075.42, -4598.91, 3.5),
        vector3(5036.91, -4644.79, 8.42),
        vector3(5036.86, -4644.86, 8.44),
        vector3(5032.56, -4855.17, 21.45),
        vector3(5088.72, -4880.83, 17.13),
        vector3(5088.47, -4885.2, 19.96),
        vector3(5093.78, -4889.09, 20.06),
        vector3(5088.98, -4912.98, 18.18),
        vector3(5053.84, -4878.23, 17.39),
        vector3(4995.56, -4816.43, 14.11),
        vector3(5213.72, -4893.76, 2.11),
        vector3(5198.53, -4889.4, 1.46),
        vector3(5136.76, -4946.48, 14.74),
        vector3(4987.18, -5732.57, 20.05),
        vector3(5005.35, -5734.97, 19.88),
        vector3(4985.69, -5770.4, 20.88),
        vector3(4963.63, -5785.85, 26.27),
        vector3(4978.05, -5797.71, 20.88),
        vector3(5034.84, -5777.95, 16.28),
        vector3(5085.37, -5750.78, 15.7),
        vector3(5073.93, -5719.0, 14.54),
        vector3(5018.68, -5707.9, 19.87),
        vector3(5021.46, -5728.6, 17.68),
        vector3(4991.38, -5738.37, 14.84),
        vector3(5010.41, -5750.28, 15.48),
        vector3(4994.18, -5752.82, 16.04),
        vector3(4909.05, -5841.64, 28.09),
        vector3(4918.93, -5859.42, 26.2),
        vector3(4987.09, -5890.13, 19.64),
        vector3(4982.74, -5854.42, 18.79),
        vector3(4956.83, -5868.39, 17.63),
        vector3(5010.7, -5888.1, 17.2),
        vector3(4940.35, -5881.23, 17.92),
        vector3(4976.96, -5613.36, 23.7),
        vector3(4884.43, -5450.29, 27.38),
        vector3(4889.87, -5459.61, 42.72),
        vector3(4827.8, -5427.95, 16.49),
        vector3(4855.91, -5420.28, 18.46),
        vector3(4900.82, -5312.7, 9.78),
        vector3(4904.95, -5332.18, 29.14),
        vector3(4904.38, -5339.32, 35.61),
        vector3(4938.72, -5352.95, 10.73),
        vector3(4963.41, -5292.72, 6.24),
        vector3(4940.59, -5275.07, 4.23),
        vector3(4946.44, -5264.61, 3.17),
        vector3(4916.81, -5308.28, 7.53),
        vector3(4897.35, -5202.87, 2.83),
        vector3(4884.88, -5192.82, 3.52),
        vector3(4837.25, -5170.88, 3.58),
        vector3(4868.41, -5164.97, 2.44),
        vector3(4960.14, -5128.8, 2.46),
        vector3(4975.18, -5205.57, 2.5),
        vector3(4935.69, -5227.36, 2.54),
        vector3(4976.03, -5232.04, 5.03),
        vector3(5027.85, -5192.64, 2.68),
        vector3(5050.11, -5149.21, 6.83),
        vector3(4995.94, -5120.79, 3.3),
        vector3(4977.24, -5107.59, 2.91),
        vector3(4973.87, -5109.5, 3.11),
        vector3(4941.07, -5084.93, 2.05),
        vector3(4907.19, -5099.38, 2.1),
        vector3(5041.14, -5117.32, 18.14),
        vector3(4994.79, -5740.31, 14.84),
        vector3(4991.08, -5751.4, 19.9),
        vector3(4967.54, -5746.69, 19.88),
        vector3(4983.7, -5769.54, 20.88),
        vector3(4964.42, -5782.51, 20.91),
        vector3(5024.27, -5800.96, 17.68),
        vector3(5049.67, -5761.04, 16.28),
        vector3(5076.18, -5736.77, 18.25),
        vector3(5045.19, -5700.88, 17.29),
        vector3(5013.42, -5703.48, 19.87),
        vector3(4988.37, -5764.46, 15.89),
        vector3(4999.0, -5732.74, 13.84),
        vector3(5012.12, -5747.4, 15.48),
        vector3(5011.3, -5763.13, 16.28),
        vector3(4869.45, -5866.48, 19.7),
        vector3(4930.55, -5816.26, 23.44),
        vector3(4957.34, -5845.27, 21.02),
        vector3(4985.21, -5914.81, 12.36),
        vector3(4990.32, -5927.67, 12.07),
        vector3(5014.09, -5889.41, 17.04),
        vector3(4963.77, -5919.09, 11.65),
        vector3(4977.77, -5609.89, 24.84),
        vector3(4886.7, -5466.94, 30.48),
        vector3(4899.38, -5456.08, 30.9),
        vector3(4880.8, -5290.6, 8.54),
        vector3(4863.85, -5462.46, 23.26),
        vector3(4896.91, -5335.78, 10.02),
        vector3(4956.46, -5315.86, 11.73),
        vector3(4924.95, -5316.85, 7.23),
        vector3(4951.44, -5294.78, 8.54),
        vector3(4961.49, -5292.76, 6.23),
        vector3(4947.53, -5269.19, 3.87),
        vector3(4912.53, -5290.99, 8.31),
        vector3(4953.09, -5274.17, 7.76),
        vector3(4893.5, -5209.76, 2.67),
        vector3(4874.76, -5184.34, 4.74),
        vector3(4857.96, -5160.4, 2.44),
        vector3(4861.17, -5173.46, 2.44),
        vector3(4935.68, -5184.55, 2.45),
        vector3(4937.63, -5149.18, 2.46),
        vector3(4991.55, -5200.74, 10.86),
        vector3(5001.01, -5214.85, 6.08),
        vector3(5003.95, -5193.52, 4.75),
        vector3(5008.77, -5122.01, 2.48),
        vector3(4991.77, -5114.11, 5.37),
        vector3(4953.85, -5108.51, 3.77),
        vector3(4953.01, -5107.89, 3.77),
        vector3(4940.3, -5109.72, 5.8),
        vector3(4878.83, -5130.32, 2.53),
        vector3(5041.15, -5117.23, 18.14),
        vector3(5010.87, -5728.47, 15.84),
        vector3(4940.59, -5581.35, 23.97),
        vector3(4908.95, -5467.48, 30.24),
        vector3(4889.85, -5459.05, 47.52),
        vector3(4891.87, -5291.16, 8.64),
        vector3(4851.93, -5372.56, 15.96),
        vector3(4894.37, -5347.84, 10.25),
        vector3(4900.54, -5334.32, 35.61),
        vector3(4927.57, -5324.02, 7.22),
        vector3(4930.94, -5248.82, 5.37),
        vector3(5141.56, -5111.83, 4.06),
        vector3(4935.24, -5197.11, 2.47),
        vector3(4982.66, -5214.19, 2.5),
        vector3(4980.58, -5201.21, 3.8),
        vector3(4972.53, -5171.06, 2.31),
        vector3(4974.73, -5168.55, 2.37),
        vector3(4972.56, -5174.9, 2.43),
        vector3(4994.57, -5113.8, 5.38),
        vector3(4990.16, -5129.59, 2.5),
        vector3(4965.28, -5158.44, 4.14),
        vector3(4888.0, -5124.98, 2.53),
        vector3(4940.23, -5084.76, 1.95),
        vector3(4945.37, -5286.19, 4.75),
        vector3(4970.26, -5281.61, 11.11),
        vector3(5138.41, -5241.77, 23.69),
        vector3(5138.64, -5236.35, 8.79),
        vector3(5136.91, -5190.85, 4.61),
        vector3(5145.5, -5202.34, 3.09),
        vector3(5158.3, -5192.44, 2.87),
        vector3(5108.4, -5206.38, 2.06),
        vector3(5107.88, -5141.83, 1.95),
        vector3(5116.94, -5171.71, 2.28),
        vector3(5107.0, -5120.39, 2.01),
        vector3(5115.07, -5183.76, 2.36),
        vector3(5117.13, -5156.82, 5.74),
        vector3(5136.16, -5134.58, 4.94),
        vector3(5119.98, -5124.99, 9.46),
        vector3(5109.9, -5110.07, 2.3),
        vector3(5118.2, -5096.85, 2.21),
        vector3(5134.68, -5116.49, 2.12),
        vector3(5137.72, -5095.9, 5.54),
        vector3(5139.14, -5095.67, 5.35),
        vector3(5137.65, -5097.3, 5.53),
        vector3(5124.14, -5112.28, 3.24),
        vector3(5140.59, -5117.08, 8.43),
        vector3(5159.57, -5148.38, 2.37),
        vector3(5142.11, -5140.95, 2.19),
        vector3(5177.25, -5146.71, 3.09),
        vector3(5130.6, -5141.49, 2.63),
        vector3(5186.48, -5147.33, 4.45),
        vector3(5185.43, -5152.52, 4.48),
        vector3(5175.13, -5151.63, 2.75),
        vector3(5197.0, -5140.6, 3.53),
        vector3(5184.77, -5151.63, 7.67),
        vector3(5188.63, -5130.3, 4.49),
        vector3(5185.65, -5134.78, 3.34),
        vector3(5181.79, -5133.56, 3.32),
        vector3(5140.56, -5122.92, 8.43),
        vector3(5157.04, -5115.7, 7.15),
        vector3(5138.57, -5117.65, 6.82),
        vector3(5125.32, -5103.71, 2.16),
        vector3(5118.18, -5035.28, 2.23),
        vector3(5145.52, -5062.42, 3.75),
        vector3(5147.67, -5044.53, 4.58),
        vector3(5199.1, -5136.0, 3.98),
        vector3(5209.81, -5131.54, 9.68),
        vector3(5216.96, -5102.3, 5.38),
        vector3(5216.09, -5124.19, 9.46),
        vector3(5209.31, -5124.46, 9.42),
        vector3(5215.68, -5157.85, 10.74),
        vector3(5209.23, -5166.72, 11.63),
        vector3(3908.62, -4683.58, 4.02),
        vector3(3953.71, -4671.39, 4.01),
        vector3(3963.05, -4688.42, 4.18),
        vector3(3846.91, -4753.73, 2.73),
        vector3(3844.42, -4741.74, 2.67),
        vector3(3904.81, -4798.45, 2.49),
        vector3(3968.05, -4753.89, 2.18),
        vector3(4064.7, -4629.92, 4.07),
        vector3(4066.22, -4663.51, 4.19),
        vector3(4043.79, -4672.68, 4.18),
        vector3(4025.73, -4695.21, 5.18),
        vector3(4065.91, -4691.58, 4.18),
        vector3(4212.32, -4633.76, 2.54),
        vector3(4206.94, -4613.91, 4.23),
        vector3(4194.8, -4578.39, 4.01),
        vector3(4131.08, -4487.35, 2.94),
        vector3(4097.23, -4534.68, 2.17),
        vector3(3960.85, -4609.04, 3.56),
        vector3(3897.84, -4629.49, 2.02),
        vector3(4182.94, -4454.1, 3.05),
        vector3(4192.94, -4299.22, 2.29),
        vector3(4273.64, -4364.56, 23.52),
        vector3(4249.86, -4356.92, 20.49),
        vector3(4286.53, -4526.63, 4.62),
        vector3(4259.04, -4554.67, 4.06),
        vector3(4287.95, -4536.04, 5.62),
        vector3(4297.44, -4603.86, 2.56),
        vector3(4363.38, -4587.5, 7.04),
        vector3(4355.91, -4569.73, 4.21),
        vector3(4380.03, -4574.52, 4.21),
        vector3(4373.66, -4589.89, 4.21),
        vector3(4361.85, -4620.23, 3.0),
        vector3(4437.53, -4471.88, 12.26),
        vector3(4429.7, -4451.3, 12.17),
        vector3(4413.78, -4465.93, 7.15),
        vector3(4410.33, -4495.76, 4.84),
        vector3(4446.92, -4467.56, 4.33),
        vector3(4449.73, -4474.39, 4.33),
        vector3(4453.63, -4466.32, 4.33),
        vector3(4428.46, -4462.92, 4.78),
        vector3(4432.08, -4452.95, 7.23),
        vector3(4437.83, -4447.74, 4.33),
        vector3(4456.03, -4507.03, 4.18),
        vector3(4522.41, -4460.1, 4.19),
        vector3(4496.5, -4520.13, 4.41),
        vector3(4503.27, -4520.79, 4.41),
        vector3(4524.53, -4537.13, 7.55),
        vector3(4506.83, -4541.31, 5.35),
        vector3(4479.1, -4555.17, 5.75),
        vector3(4471.95, -4581.35, 5.62),
        vector3(4487.37, -4593.91, 5.58),
        vector3(4488.56, -4633.26, 8.89),
        vector3(4432.14, -4448.45, 7.24),
        vector3(4440.59, -4448.3, 8.37),
        vector3(4445.86, -4444.38, 7.24),
        vector3(4437.78, -4445.36, 7.24),
        vector3(4534.34, -4706.27, 2.49),
        vector3(4661.32, -4558.12, 23.02),
        vector3(4657.56, -4547.54, 21.26),
        vector3(4636.3, -4421.13, 2.53),
        vector3(4600.61, -4429.77, 4.02),
        vector3(4802.64, -4379.02, 21.37),
        vector3(4802.9, -4320.76, 8.77),
        vector3(4809.21, -4310.8, 7.78),
        vector3(4786.49, -4272.31, 3.06),
        vector3(4860.68, -4351.72, 8.9),
        vector3(5004.51, -4403.89, 3.75),
        vector3(5015.87, -4541.02, 8.78),
        vector3(4955.71, -4474.37, 10.35),
        vector3(4915.15, -4494.08, 9.51),
        vector3(4886.68, -4480.91, 10.06),
        vector3(4880.43, -4490.31, 16.57),
        vector3(4753.32, -4559.96, 24.8),
        vector3(4767.31, -4548.82, 25.42),
        vector3(4813.95, -4650.48, 17.19),
        vector3(4814.58, -4662.95, 15.7),
        vector3(4855.22, -4682.06, 9.88),
        vector3(4878.99, -4656.75, 12.86),
        vector3(4883.77, -4636.09, 13.72),
        vector3(4859.46, -4636.06, 15.71),
        vector3(4871.92, -4624.33, 14.94),
        vector3(4874.39, -4521.74, 16.08),
        vector3(4764.59, -4780.22, 3.8),
        vector3(4787.82, -4746.92, 2.06),
        vector3(4860.59, -4772.66, 2.19),
        vector3(4883.78, -4824.87, 1.48),
        vector3(4991.12, -4540.76, 9.03),
        vector3(4847.1, -4366.6, 6.85),
        vector3(4801.21, -4377.89, 21.43),
        vector3(4764.24, -4726.69, 1.58),
        vector3(4726.49, -4679.65, 2.36),
        vector3(4757.26, -4688.72, 3.93),
        vector3(4823.72, -4753.45, 13.19),
        vector3(4598.41, -4461.46, 2.95),
        vector3(4616.56, -4457.73, 3.55),
        vector3(4591.57, -4882.57, 17.94),
        vector3(4600.06, -4878.8, 18.07),
        vector3(4603.47, -4870.47, 17.45),
        vector3(4590.98, -4863.1, 17.05),
        vector3(4590.52, -4876.07, 18.04),
        vector3(4593.84, -4872.91, 19.01),
        vector3(4597.54, -4870.7, 17.91),
        vector3(4647.35, -4892.99, 11.89),
        vector3(4621.31, -4765.08, 11.78),
        vector3(4547.71, -4986.3, 3.35),
        vector3(4551.57, -4839.33, 10.18),
        vector3(4921.17, -4896.66, 3.54),
        vector3(4901.29, -4922.38, 3.36),
        vector3(4875.3, -4950.06, 3.62),
        vector3(4859.58, -4943.38, 1.59),
        vector3(4837.2, -4948.62, 2.12),
        vector3(4866.37, -4907.07, 2.56),
        vector3(4874.14, -4927.59, 3.14),
        vector3(4886.87, -4916.86, 3.37),
        vector3(4866.8, -4895.57, 4.93),
        vector3(4910.28, -4927.56, 3.37),
        vector3(4896.68, -4954.54, 4.77),
        vector3(5070.82, -4500.38, 2.13),
        vector3(4957.85, -4468.86, 10.56),
        vector3(4939.11, -4413.95, 3.11),
        vector3(4909.38, -4386.71, 3.62),
        vector3(4882.37, -4458.7, 7.87),
        vector3(4884.25, -4455.35, 7.7),
        vector3(4978.85, -4529.5, 9.83),
        vector3(4994.62, -4545.75, 9.06),
        vector3(5009.9, -4519.3, 9.74),
        vector3(5050.56, -4524.48, 6.6),
        vector3(5051.72, -4593.29, 10.52),
        vector3(5095.22, -4607.67, 3.25),
        vector3(5099.2, -4624.1, 2.63),
        vector3(5132.45, -4642.91, 1.41),
        vector3(5163.09, -4612.44, 2.92),
        vector3(5113.57, -4583.28, 4.49),
        vector3(5110.87, -4574.78, 14.55),
        vector3(5110.3, -4583.93, 23.25),
        vector3(5107.91, -4576.98, 30.51),
        vector3(5107.16, -4580.22, 29.85),
        vector3(5103.65, -4579.34, 29.72),
        vector3(5110.63, -4580.08, 29.72),
        vector3(5083.54, -4600.56, 5.86),
        vector3(5249.09, -4616.36, 1.98),
        vector3(5180.11, -4649.95, 5.98),
        vector3(5176.13, -4657.1, 7.62),
        vector3(5172.03, -4676.27, 5.92),
        vector3(5170.32, -4706.89, 2.19),
        vector3(5164.34, -4708.95, 2.16),
        vector3(5137.02, -4701.28, 3.49),
        vector3(5108.09, -4765.64, 3.19),
        vector3(5180.19, -4805.19, 2.04),
        vector3(5075.43, -4599.12, 3.5),
        vector3(5065.92, -4599.1, 4.1),
        vector3(5064.1, -4590.26, 2.86),
        vector3(5067.44, -4591.47, 2.86),
        vector3(5056.93, -4591.02, 2.9),
        vector3(5043.02, -4639.21, 6.64),
        vector3(5037.13, -4647.63, 8.19),
        vector3(5081.46, -4667.25, 3.03),
        vector3(5112.58, -4674.85, 2.23),
        vector3(5099.06, -4676.69, 2.42),
        vector3(5108.99, -4683.34, 8.7),
        vector3(5094.45, -4680.59, 8.01),
        vector3(5035.52, -4635.45, 4.9),
        vector3(5035.66, -4624.31, 3.6),
        vector3(5000.6, -4664.09, 6.87),
        vector3(5032.44, -4630.92, 21.68),
        vector3(5172.41, -4590.94, 3.73),
        vector3(5170.33, -4595.83, 4.64),
        vector3(5176.9, -4589.7, 4.93),
        vector3(5189.51, -4619.94, 7.08),
        vector3(5167.32, -4616.51, 2.86),
        vector3(5161.37, -4669.04, 1.44),
        vector3(5165.48, -4660.88, 3.29),
        vector3(5170.77, -4676.18, 2.43),
        vector3(4877.66, -5730.18, 26.32),
        vector3(4890.65, -5471.18, 30.15),
        vector3(4892.2, -5459.67, 30.74),
        vector3(4894.66, -5721.43, 26.35),
        vector3(4902.02, -5827.23, 28.3),
        vector3(4902.65, -5465.18, 30.43),
        vector3(4903.64, -5729.19, 26.35),
        vector3(4903.98, -5832.1, 29.56),
        vector3(4911.31, -5827.64, 28.08),
        vector3(4920.95, -5834.76, 27.31),
        vector3(4947.34, -5886.98, 13.9),
        vector3(4950.56, -5891.41, 13.9),
        vector3(4954.4, -5885.99, 13.9),
        vector3(4954.6, -5892.68, 13.9),
        vector3(4956.56, -5789.09, 20.82),
        vector3(4957.46, -5771.76, 20.88),
        vector3(4959.7, -5796.3, 20.83),
        vector3(4961.81, -5786.7, 21.04),
        vector3(4964.47, -5789.38, 26.27),
        vector3(4964.58, -5782.3, 20.91),
        vector3(4967.3, -5764.58, 20.88),
        vector3(4967.53, -5800.78, 20.85),
        vector3(4968.63, -5755.61, 20.88),
        vector3(4970.59, -5784.67, 20.88),
        vector3(4970.77, -5737.05, 19.88),
        vector3(4972.32, -5806.89, 20.83),
        vector3(4973.26, -5770.77, 20.88),
        vector3(4973.26, -5751.36, 19.88),
        vector3(4974.48, -5729.77, 19.88),
        vector3(4978.25, -5745.71, 19.95),
        vector3(4979.0, -5809.04, 20.79),
        vector3(4979.91, -5773.14, 20.9),
        vector3(4982.31, -5798.89, 20.88),
        vector3(4982.62, -5714.36, 25.24),
        vector3(4982.65, -5787.42, 20.88),
        vector3(4983.13, -5807.55, 20.87),
        vector3(4983.23, -5760.17, 20.88),
        vector3(4983.81, -5712.1, 25.24),
        vector3(4985.61, -5716.86, 25.24),
        vector3(4985.95, -5719.88, 19.88),
        vector3(4986.21, -5792.58, 20.88),
        vector3(4987.68, -5715.6, 25.24),
        vector3(4987.88, -5776.46, 17.08),
        vector3(4988.07, -5785.89, 17.08),
        vector3(4989.0, -5755.77, 19.88),
        vector3(4989.57, -5871.22, 19.85),
        vector3(4994.02, -5794.14, 20.88),
        vector3(4995.3, -5806.24, 20.88),
        vector3(4995.8, -5775.87, 16.28),
        vector3(4995.8, -5767.29, 16.28),
        vector3(4996.44, -5730.07, 19.88),
        vector3(4997.7, -5720.75, 19.91),
        vector3(4997.81, -5708.96, 19.88),
        vector3(5000.48, -5785.52, 17.58),
        vector3(5000.67, -5770.02, 16.29),
        vector3(5003.19, -5795.7, 17.48),
        vector3(5003.69, -5775.79, 16.28),
        vector3(5004.2, -5800.1, 18.08),
        vector3(5004.64, -5790.28, 17.57),
        vector3(5008.93, -5777.63, 17.68),
        vector3(5010.91, -5770.99, 16.28),
        vector3(5011.36, -5791.68, 17.68),
        vector3(5012.87, -5784.03, 17.68),
        vector3(5016.04, -5769.82, 16.3),
        vector3(5016.17, -5809.94, 17.48),
        vector3(5016.21, -5762.79, 16.33),
        vector3(5017.05, -5755.5, 16.28),
        vector3(5019.48, -5793.22, 17.68),
        vector3(5021.01, -5781.9, 16.28),
        vector3(5024.06, -5746.48, 16.28),
        vector3(5024.83, -5756.21, 16.28),
        vector3(5025.65, -5765.93, 16.28),
        vector3(5026.91, -5788.37, 16.28),
        vector3(5027.19, -5804.19, 17.48),
        vector3(5027.39, -5761.28, 15.75),
        vector3(5030.92, -5773.9, 16.28),
        vector3(5031.18, -5749.19, 16.28),
        vector3(5032.61, -5762.34, 15.75),
        vector3(5037.47, -5796.76, 17.48),
        vector3(5039.39, -5761.34, 15.68),
        vector3(5040.52, -5771.97, 15.77),
        vector3(5041.44, -5778.9, 15.68),
        vector3(5046.04, -5785.37, 15.68),
        vector3(5046.88, -5750.53, 15.68),
        vector3(5049.13, -5791.26, 15.68),
        vector3(5050.98, -5771.2, 16.28),
        vector3(5053.71, -5752.34, 15.73),
        vector3(5054.54, -5784.86, 16.28),
        vector3(5055.68, -5761.5, 16.28),
        vector3(5059.48, -5780.58, 16.28),
        vector3(5060.29, -5758.22, 15.72),
        vector3(5065.92, -5772.1, 16.28),
        vector3(5068.17, -5763.58, 15.72),
        vector3(5069.94, -5740.53, 15.88),
        vector3(5072.71, -5765.43, 15.68),
        vector3(5076.37, -5750.78, 15.68),
        vector3(5080.25, -5766.18, 15.68),
        vector3(5083.65, -5742.6, 15.7),
        vector3(5083.92, -5738.31, 15.68),
        vector3(5086.02, -5759.52, 15.68),
        vector3(5090.9, -5743.56, 15.68),
        vector3(5091.17, -5752.19, 15.68),
        vector3(5097.19, -5527.5, 54.3),
        vector3(5098.78, -5517.27, 54.28),
        vector3(5105.02, -5524.21, 54.23),
        vector3(5105.82, -5531.41, 54.09),
        vector3(5106.69, -5518.59, 55.56),
        vector3(5107.1, -5527.32, 55.44),
        vector3(5110.52, -5517.92, 54.12),
        vector3(5110.57, -5524.72, 54.25),
        vector3(5120.63, -5525.26, 54.19),
        vector3(5123.95, -5530.31, 54.19),
        vector3(5124.17, -5529.68, 62.75),
        vector3(5124.83, -5526.19, 70.98),
        vector3(5126.28, -5521.35, 54.19),
        vector3(5128.2, -5527.06, 70.98),
        vector3(5129.81, -5527.07, 54.19),
        vector3(5242.79, -5414.53, 65.3),
        vector3(5252.34, -5426.93, 64.34),
        vector3(5257.91, -5414.09, 65.64),
        vector3(5260.34, -5430.95, 65.6),
        vector3(5262.77, -5428.64, 90.73),
        vector3(5263.3, -5425.27, 65.6),
        vector3(5263.55, -5428.2, 141.05),
        vector3(5263.74, -5428.16, 109.15),
        vector3(5264.2, -5444.41, 63.81),
        vector3(5265.65, -5434.98, 65.6),
        vector3(5266.18, -5419.91, 65.6),
        vector3(5266.68, -5431.55, 90.73),
        vector3(5266.72, -5430.68, 109.15),
        vector3(5267.03, -5429.49, 141.05),
        vector3(5267.12, -5426.41, 141.04),
        vector3(5267.31, -5428.1, 65.6),
        vector3(5271.25, -5426.0, 65.6),
        vector3(5274.73, -5465.65, 59.8),
        vector3(5278.9, -5426.21, 64.71),
        vector3(5289.24, -5440.76, 63.88),
        vector3(5307.29, -5476.5, 56.22),
        vector3(5327.24, -5511.28, 56.77),
        vector3(5331.71, -5514.82, 56.75),
        vector3(5333.41, -5523.66, 55.98),
        vector3(5340.18, -5511.37, 55.83),
        vector3(5342.36, -5511.41, 59.01),
        vector3(5345.74, -5512.29, 65.51),
        vector3(5345.97, -5517.81, 54.88),
        vector3(5347.02, -5432.96, 49.27),
        vector3(5348.37, -5512.5, 59.01),
        vector3(5351.35, -5529.41, 53.26),
        vector3(5354.67, -5415.68, 49.45),
        vector3(5355.48, -5518.28, 53.24),
        vector3(5355.7, -5425.99, 49.25),
        vector3(5356.01, -5535.87, 52.83),
        vector3(5356.64, -5437.28, 55.82),
        vector3(5356.84, -5528.97, 55.49),
        vector3(5359.45, -5551.94, 52.85),
        vector3(5359.66, -5523.89, 52.72),
        vector3(5360.13, -5436.66, 66.18),
        vector3(5360.44, -5440.33, 49.4),
        vector3(5361.2, -5432.49, 49.4),
        vector3(5362.08, -5435.07, 66.18),
        vector3(5362.39, -5533.63, 52.27),
        vector3(5367.75, -5375.9, 43.36),
        vector3(5370.6, -5383.33, 43.36),
        vector3(5371.63, -5459.36, 50.34),
        vector3(5373.78, -5703.59, 43.71),
        vector3(5374.55, -5394.94, 43.61),
        vector3(5374.88, -5533.58, 52.16),
        vector3(5375.23, -5379.64, 43.71),
        vector3(5377.02, -5386.99, 43.48),
        vector3(5377.64, -5463.57, 50.18),
        vector3(5378.49, -5528.01, 52.1),
        vector3(5379.73, -5391.6, 43.54),
        vector3(5380.95, -5375.13, 42.63),
        vector3(5382.1, -5383.47, 43.55),
        vector3(5382.59, -5498.6, 48.6),
        vector3(5383.15, -5625.4, 52.43),
        vector3(5383.76, -5567.33, 52.05),
        vector3(5385.02, -5505.72, 47.8),
        vector3(5386.96, -5493.31, 47.41),
        vector3(5388.28, -5514.33, 47.76),
        vector3(5389.02, -5386.33, 42.47),
        vector3(5391.9, -5505.76, 47.48),
        vector3(5392.53, -5690.18, 46.88),
        vector3(5393.46, -5491.86, 47.12),
        vector3(5395.26, -5481.82, 46.39),
        vector3(5395.63, -5498.01, 47.2),
        vector3(5396.02, -5679.02, 48.16),
        vector3(5396.88, -5664.19, 50.02),
        vector3(5399.78, -5485.15, 46.52),
        vector3(5401.36, -5492.21, 46.75),
        vector3(5401.83, -5715.61, 37.36),
        vector3(5402.44, -5495.66, 47.88),
        vector3(5403.92, -5659.43, 49.35),
        vector3(5405.97, -5678.03, 48.44),
        vector3(5406.7, -5669.11, 49.14),
        vector3(5424.77, -5452.06, 41.21),
        vector3(5426.35, -5445.05, 41.15),
        vector3(5428.54, -5452.4, 41.12),
        vector3(5429.39, -5447.57, 42.35),
        vector3(5430.93, -5714.06, 36.38),
        vector3(5431.68, -5452.56, 40.55),
        vector3(5433.29, -5439.34, 40.28),
        vector3(5434.11, -5447.13, 40.61),
        vector3(5434.19, -5458.91, 40.16),
        vector3(5456.18, -5755.18, 27.68),
        vector3(5457.05, -5730.39, 31.23),
        vector3(5457.65, -5844.19, 19.58),
        vector3(5459.47, -5857.21, 19.78),
        vector3(5464.28, -5810.5, 20.19),
        vector3(5466.95, -5830.85, 18.94),
        vector3(5473.12, -5867.11, 19.47),
        vector3(5473.22, -5853.18, 20.75),
        vector3(5475.69, -5782.69, 21.82),
        vector3(5477.79, -5845.66, 21.89),
        vector3(5477.9, -5837.58, 19.5),
        vector3(5480.33, -5856.61, 20.11),
        vector3(5487.64, -5828.15, 18.85),
        vector3(5487.95, -5855.76, 19.95),
        vector3(5488.07, -5845.39, 19.58),
        vector3(5490.43, -5863.61, 19.57),
        vector3(5498.87, -5843.08, 18.96),
        vector3(4722.98, -5730.9, 13.9),
        vector3(4725.41, -5727.86, 13.9),
        vector3(4728.83, -5732.2, 13.9),
        vector3(4753.6, -5501.06, 9.87),
        vector3(4875.92, -5740.95, 26.58),
        vector3(4884.51, -5722.44, 26.37),
        vector3(4886.38, -5751.54, 26.38),
        vector3(4902.69, -5746.62, 26.27),
        vector3(4973.78, -5708.13, 19.89),
        vector3(4974.32, -5717.13, 19.89),
        vector3(4979.66, -5701.73, 19.89),
        vector3(4989.71, -5698.77, 19.8),
        vector3(4991.9, -5704.67, 19.89),
        vector3(5001.9, -5714.71, 19.92),
        vector3(5002.08, -5685.47, 19.9),
        vector3(5004.86, -5719.57, 19.5),
        vector3(5004.87, -5725.69, 19.5),
        vector3(5008.72, -5693.83, 19.87),
        vector3(5009.71, -5735.84, 17.69),
        vector3(5016.08, -5691.02, 19.87),
        vector3(5016.31, -5718.21, 20.08),
        vector3(5019.08, -5682.32, 19.88),
        vector3(5021.04, -5670.2, 19.96),
        vector3(5021.6, -5736.86, 17.68),
        vector3(5021.79, -5717.57, 17.71),
        vector3(5022.7, -5695.22, 19.87),
        vector3(5025.22, -5709.73, 19.88),
        vector3(5027.79, -5700.1, 19.87),
        vector3(5031.68, -5726.37, 17.7),
        vector3(5033.43, -5734.09, 17.69),
        vector3(5034.6, -5713.71, 17.68),
        vector3(5034.72, -5679.83, 19.77),
        vector3(5035.7, -5702.36, 19.88),
        vector3(5038.91, -5721.75, 17.08),
        vector3(5039.02, -5694.29, 19.88),
        vector3(5039.71, -5746.99, 17.68),
        vector3(5041.18, -5708.55, 17.71),
        vector3(5044.09, -5716.03, 17.68),
        vector3(5044.81, -5727.83, 17.09),
        vector3(5045.36, -5735.97, 17.09),
        vector3(5047.34, -5710.95, 14.57),
        vector3(5055.32, -5707.13, 14.57),
        vector3(5056.52, -5786.59, 11.48),
        vector3(5060.96, -5721.29, 14.51),
        vector3(5062.86, -5785.75, 11.48),
        vector3(5064.85, -5779.75, 16.28),
        vector3(5068.27, -5779.1, 11.48),
        vector3(5070.1, -5723.87, 14.48),
        vector3(5070.84, -5728.31, 15.88),
        vector3(5071.7, -5718.06, 14.5),
        vector3(5072.11, -5773.01, 11.48),
        vector3(5076.76, -5726.54, 15.77),
        vector3(5079.12, -5721.22, 15.77),
        vector3(5083.87, -5734.17, 15.81),
        vector3(5084.39, -5735.64, 21.04),
        vector3(5088.66, -5724.1, 15.77),
        vector3(5089.08, -5735.88, 15.77),
        vector3(5093.2, -5717.27, 15.77),
        vector3(5095.33, -5732.65, 15.77),
        vector3(5098.06, -5723.22, 15.77),
        vector3(5189.43, -5008.61, 13.85),
        vector3(5193.94, -5012.7, 14.08),
        vector3(5251.29, -5262.25, 24.99),
        vector3(5254.33, -5257.41, 25.32),
        vector3(5257.52, -5254.09, 25.37),
        vector3(5258.25, -5261.73, 25.44),
        vector3(5259.69, -5269.94, 26.29),
        vector3(5261.38, -5250.48, 25.41),
        vector3(5261.6, -5256.86, 25.41),
        vector3(5264.2, -5073.53, 14.51),
        vector3(5264.26, -5253.11, 25.48),
        vector3(5267.25, -5256.86, 25.51),
        vector3(5267.99, -5080.23, 14.29),
        vector3(5272.21, -5082.19, 14.53),
        vector3(5283.16, -5232.1, 31.5),
        vector3(5292.5, -5240.91, 31.79),
        vector3(5302.04, -5213.25, 31.96),
        vector3(5302.59, -5250.83, 32.3),
        vector3(5309.34, -5221.28, 32.5),
        vector3(5311.99, -5209.48, 32.0),
        vector3(5312.11, -5203.0, 31.76),
        vector3(5316.59, -5237.56, 32.78),
        vector3(5318.74, -5202.61, 31.73),
        vector3(5320.72, -5259.97, 32.76),
        vector3(5321.47, -5224.79, 32.3),
        vector3(5325.42, -5211.77, 31.77),
        vector3(5326.41, -5265.34, 33.15),
        vector3(5327.87, -5260.69, 32.74),
        vector3(5329.25, -5269.18, 33.19),
        vector3(5330.0, -5235.27, 32.46),
        vector3(5330.44, -5263.45, 32.76),
        vector3(5330.77, -5220.38, 32.1),
        vector3(5330.87, -5271.43, 33.19),
        vector3(5332.38, -5243.45, 32.55),
        vector3(5338.69, -5268.24, 32.86),
        vector3(5341.82, -5217.12, 31.57),
        vector3(5342.72, -5233.62, 31.97),
        vector3(5345.09, -5240.66, 32.25),
        vector3(5351.79, -5224.65, 31.47),
        vector3(5353.82, -5247.71, 32.69),
        vector3(5354.3, -5211.48, 30.99),
        vector3(5363.43, -5255.06, 33.1),
        vector3(5367.02, -5210.8, 30.94),
        vector3(5377.0, -5265.59, 34.17),
        vector3(5377.5, -5246.21, 33.45),
        vector3(5378.06, -5258.38, 34.0),
        vector3(5379.86, -5217.12, 32.08),
        vector3(5381.86, -5203.96, 31.75),
        vector3(5383.64, -5182.22, 31.08),
        vector3(5387.93, -5262.52, 34.62),
        vector3(5389.58, -5210.6, 32.71),
        vector3(5389.74, -5218.9, 33.34),
        vector3(5391.93, -5200.49, 32.01),
        vector3(5392.77, -5223.86, 33.83),
        vector3(5394.37, -5207.19, 32.66),
        vector3(5394.51, -5174.55, 31.27),
        vector3(5396.13, -5166.26, 31.35),
        vector3(5398.34, -5215.09, 33.72),
        vector3(5398.73, -5197.61, 32.42),
        vector3(5398.9, -5174.34, 31.36),
        vector3(5399.68, -5202.63, 32.97),
        vector3(5399.82, -5186.52, 31.77),
        vector3(5400.62, -5225.34, 34.36),
        vector3(5402.03, -5169.14, 31.38),
        vector3(5402.6, -5194.42, 32.36),
        vector3(5403.44, -5174.74, 31.46),
        vector3(5403.53, -5179.39, 31.56),
        vector3(5403.58, -5209.91, 33.73),
        vector3(5404.12, -5232.06, 34.89),
        vector3(5404.77, -5127.99, 13.33),
        vector3(5404.78, -5171.24, 31.44),
        vector3(5405.83, -5224.09, 34.59),
        vector3(5406.43, -5203.82, 33.23),
        vector3(5406.73, -5216.5, 34.29),
        vector3(5407.88, -5209.23, 33.95),
        vector3(5408.97, -5231.42, 35.01),
        vector3(5413.0, -5194.38, 33.33),
        vector3(5413.73, -5226.86, 34.95),
        vector3(5414.09, -5242.15, 35.36),
        vector3(5414.45, -5203.29, 33.63),
        vector3(5415.03, -5209.85, 34.1),
        vector3(5415.44, -5219.0, 34.64),
        vector3(5417.69, -5216.34, 34.7),
        vector3(5419.36, -5235.67, 35.37),
        vector3(5419.38, -5226.37, 35.32),
        vector3(5419.39, -5241.54, 35.46),
        vector3(5422.47, -5240.07, 35.47),
        vector3(5424.77, -5217.09, 35.09),
        vector3(5427.59, -5212.16, 34.91),
        vector3(5428.72, -5223.01, 35.36),
        vector3(5441.71, -5114.87, 13.05),
        vector3(5443.95, -5109.88, 12.84),
        vector3(5444.95, -5118.11, 13.05),
        vector3(5450.49, -5117.33, 12.67),
        vector3(5450.58, -5110.79, 12.28),
        vector3(5451.53, -5114.26, 12.63),
        vector3(5455.26, -5236.34, 27.37),
        vector3(5460.68, -5231.37, 27.26),
        vector3(5462.75, -5234.69, 33.6),
        vector3(5464.08, -5232.99, 28.01),
        vector3(5465.56, -5239.17, 30.12),
        vector3(5465.67, -5235.94, 43.97),
        vector3(5468.23, -5237.54, 43.97),
        vector3(5468.29, -5237.56, 39.17),
        vector3(5469.11, -5229.31, 27.29),
        vector3(5470.04, -5238.84, 27.18),
        vector3(5493.86, -5582.87, 14.08),
        vector3(5493.92, -5590.13, 14.08),
        vector3(5495.99, -5597.04, 14.08),
        vector3(5498.1, -5592.13, 14.08),
        vector3(5499.98, -5598.26, 14.08),
        vector3(5500.9, -5586.54, 14.08),
        vector3(5511.01, -5619.59, 18.36),
        vector3(5511.15, -5632.61, 18.36),
        vector3(5514.29, -5622.73, 18.36),
        vector3(5517.94, -5630.88, 18.36),
        vector3(5524.67, -5635.47, 18.36),
        vector3(5528.54, -5644.54, 18.36),
        vector3(5533.52, -5646.38, 18.36),
        vector3(5533.88, -5782.01, 11.09),
        vector3(5536.13, -5775.84, 11.09),
        vector3(5539.61, -5778.96, 11.09),
        vector3(5539.71, -5786.22, 11.09),
        vector3(5542.09, -5773.31, 11.09),
        vector3(5544.4, -5780.32, 11.09),
        vector3(5545.73, -5785.29, 11.09),
        vector3(5562.1, -5185.05, 11.55),
        vector3(5563.35, -5180.98, 11.19),
        vector3(5566.21, -5185.57, 10.94),
        vector3(5566.34, -5182.27, 12.34),
        vector3(5568.99, -5184.9, 12.2),
        vector3(5578.18, -5222.25, 14.72),
        vector3(5580.28, -5225.6, 14.42),
        vector3(5583.27, -5224.01, 15.54),
        vector3(5583.5, -5215.13, 14.43),
        vector3(5586.73, -5222.42, 14.35),
        vector3(5588.13, -5226.63, 15.54),
        vector3(5588.34, -5213.41, 14.35),
        vector3(5589.15, -5240.5, 15.09),
        vector3(5589.71, -5231.41, 14.87),
        vector3(5591.03, -5219.02, 15.48),
        vector3(5591.89, -5462.26, 10.72),
        vector3(5594.06, -5225.78, 14.28),
        vector3(5594.26, -5669.4, 11.99),
        vector3(5594.33, -5665.4, 12.42),
        vector3(5594.59, -5654.47, 12.77),
        vector3(5595.85, -5218.34, 14.28),
        vector3(5596.24, -5459.86, 10.72),
        vector3(5597.43, -5663.38, 12.19),
        vector3(5597.82, -5455.89, 10.72),
        vector3(5599.97, -5647.09, 10.4),
        vector3(5600.01, -5461.99, 10.72),
        vector3(5600.26, -5670.86, 10.84),
        vector3(5601.62, -5220.31, 14.27),
        vector3(5601.73, -5661.95, 11.48),
        vector3(5606.23, -5664.74, 10.92),
        vector3(5606.61, -5656.25, 10.63),
        vector3(5608.31, -5651.65, 9.86),
        vector3(5608.53, -5659.62, 10.37),
        vector3(5609.93, -5648.65, 9.67),
        vector3(5612.94, -5658.32, 10.06),
        vector3(4826.93, -5438.06, 16.49),
        vector3(4850.06, -5180.14, 2.88),
        vector3(4855.58, -5175.62, 2.44),
        vector3(4860.07, -4952.1, 2.01),
        vector3(4860.98, -4910.74, 1.34),
        vector3(4861.19, -5164.78, 2.44),
        vector3(4862.17, -4931.5, 1.43),
        vector3(4867.85, -4920.02, 2.6),
        vector3(4871.15, -5127.5, 2.5),
        vector3(4872.41, -5172.35, 2.44),
        vector3(4872.71, -5179.75, 2.44),
        vector3(4874.7, -4904.08, 3.05),
        vector3(4879.12, -4911.34, 3.36),
        vector3(4879.55, -5120.44, 2.33),
        vector3(4880.55, -4937.3, 3.36),
        vector3(4881.66, -5186.63, 2.44),
        vector3(4884.04, -4922.64, 3.37),
        vector3(4884.23, -4945.64, 3.37),
        vector3(4884.78, -5179.9, 2.44),
        vector3(4886.29, -4951.92, 3.45),
        vector3(4887.56, -5119.56, 2.53),
        vector3(4888.39, -5167.7, 2.47),
        vector3(4889.14, -4903.73, 3.38),
        vector3(4890.43, -4930.5, 3.37),
        vector3(4890.48, -5111.34, 2.53),
        vector3(4890.98, -5275.87, 8.32),
        vector3(4891.23, -4941.35, 3.36),
        vector3(4892.72, -5176.47, 2.45),
        vector3(4893.54, -4921.85, 3.37),
        vector3(4894.29, -5214.68, 2.51),
        vector3(4894.76, -5195.89, 2.44),
        vector3(4894.88, -4910.62, 3.36),
        vector3(4896.37, -5184.36, 2.44),
        vector3(4896.49, -4950.27, 3.37),
        vector3(4896.65, -4932.76, 3.37),
        vector3(4898.6, -5208.12, 2.51),
        vector3(4901.07, -4929.14, 3.37),
        vector3(4901.19, -4902.1, 3.38),
        vector3(4902.46, -5180.69, 2.45),
        vector3(4903.12, -4915.8, 3.36),
        vector3(4905.09, -5175.88, 2.46),
        vector3(4905.7, -5217.4, 2.51),
        vector3(4906.79, -5287.13, 8.32),
        vector3(4906.93, -5222.05, 2.52),
        vector3(4908.89, -4902.34, 3.37),
        vector3(4911.73, -4912.8, 3.36),
        vector3(4912.29, -5195.98, 2.47),
        vector3(4912.81, -5319.28, 8.5),
        vector3(4912.86, -5230.58, 2.52),
        vector3(4913.18, -5185.46, 2.45),
        vector3(4915.44, -4917.68, 3.37),
        vector3(4915.93, -5237.96, 2.52),
        vector3(4917.21, -5203.81, 2.46),
        vector3(4918.08, -5224.97, 2.52),
        vector3(4918.28, -5272.72, 5.67),
        vector3(4920.19, -5233.29, 2.52),
        vector3(4920.96, -5210.17, 2.47),
        vector3(4922.85, -4907.06, 3.48),
        vector3(4923.75, -5310.08, 7.26),
        vector3(4925.32, -5190.88, 2.47),
        vector3(4925.82, -5333.07, 8.41),
        vector3(4926.31, -5271.0, 5.55),
        vector3(4926.84, -4896.47, 3.58),
        vector3(4929.06, -5262.53, 4.22),
        vector3(4929.57, -5221.02, 2.55),
        vector3(4931.2, -5283.75, 5.75),
        vector3(4931.22, -5204.56, 2.45),
        vector3(4931.45, -5244.2, 2.62),
        vector3(4932.21, -4910.62, 3.53),
        vector3(4932.59, -5321.45, 7.27),
        vector3(4932.76, -5191.09, 2.46),
        vector3(4933.69, -4893.38, 3.84),
        vector3(4934.52, -5307.86, 6.11),
        vector3(4934.74, -4901.03, 3.65),
        vector3(4936.49, -5219.26, 2.53),
        vector3(4936.5, -5292.87, 5.17),
        vector3(4941.1, -5172.46, 2.46),
        vector3(4941.85, -5103.1, 3.21),
        vector3(4941.96, -5092.68, 2.75),
        vector3(4942.26, -5192.61, 2.48),
        vector3(4942.43, -5213.74, 2.52),
        vector3(4943.75, -5151.94, 2.46),
        vector3(4944.27, -5204.19, 2.53),
        vector3(4944.91, -5180.52, 2.51),
        vector3(4946.67, -5111.68, 3.33),
        vector3(4946.98, -5199.3, 2.53),
        vector3(4947.74, -5216.07, 2.44),
        vector3(4947.81, -5172.34, 2.51),
        vector3(4948.99, -5315.82, 7.85),
        vector3(4949.87, -5083.36, 3.23),
        vector3(4951.9, -5325.02, 9.5),
        vector3(4953.11, -5144.35, 2.45),
        vector3(4953.74, -5155.12, 2.44),
        vector3(4954.16, -5187.74, 2.47),
        vector3(4954.76, -5207.11, 2.54),
        vector3(4955.41, -5288.67, 5.58),
        vector3(4955.74, -5317.43, 8.28),
        vector3(4957.75, -5111.8, 2.97),
        vector3(4962.26, -5119.13, 2.97),
        vector3(4963.27, -5186.71, 2.48),
        vector3(4963.99, -5287.2, 6.24),
        vector3(4965.02, -5131.21, 2.44),
        vector3(4966.81, -5209.5, 2.51),
        vector3(4967.75, -5145.78, 2.53),
        vector3(4968.73, -5200.2, 2.48),
        vector3(4972.57, -5122.58, 2.78),
        vector3(4972.75, -5193.66, 2.52),
        vector3(4973.16, -5132.31, 2.89),
        vector3(4976.26, -5143.22, 2.6),
        vector3(4981.04, -5185.51, 2.48),
        vector3(4982.04, -5163.48, 2.52),
        vector3(4982.06, -5144.41, 2.48),
        vector3(4982.84, -5174.69, 2.49),
        vector3(4984.1, -5114.1, 2.56),
        vector3(4985.7, -5147.33, 2.53),
        vector3(4987.71, -5109.3, 2.45),
        vector3(4992.84, -5154.81, 2.6),
        vector3(4994.07, -5163.78, 2.7),
        vector3(4994.79, -5135.69, 2.48),
        vector3(4999.75, -5186.52, 2.52),
        vector3(5000.37, -5173.75, 2.65),
        vector3(5004.47, -5115.62, 2.56),
        vector3(5009.07, -5128.12, 2.57),
        vector3(5010.97, -5146.96, 2.5),
        vector3(5011.99, -5175.41, 2.54),
        vector3(5014.15, -5193.71, 2.52),
        vector3(5015.36, -5172.68, 2.51),
        vector3(5016.47, -5160.76, 2.61),
        vector3(5017.83, -5198.9, 2.52),
        vector3(5021.09, -5169.84, 2.62),
        vector3(5030.45, -4627.3, 4.9),
        vector3(5043.47, -4900.34, 15.07),
        vector3(5043.91, -4892.02, 15.59),
        vector3(5049.05, -4894.94, 15.85),
        vector3(5055.42, -4882.4, 16.16),
        vector3(5058.59, -4880.43, 17.48),
        vector3(5081.05, -4910.82, 16.65),
        vector3(5081.74, -4888.54, 16.96),
        vector3(5087.48, -4910.01, 16.99),
        vector3(5089.74, -4893.24, 17.11),
        vector3(5095.53, -4896.69, 17.23),
        vector3(5107.85, -5178.23, 2.13),
        vector3(5113.54, -5103.68, 2.11),
        vector3(5116.43, -5141.59, 2.18),
        vector3(5117.01, -5151.38, 2.22),
        vector3(5117.52, -5203.36, 2.39),
        vector3(5117.69, -5116.54, 2.14),
        vector3(5121.77, -4949.46, 15.44),
        vector3(5123.24, -5092.79, 2.2),
        vector3(5124.59, -4939.43, 15.19),
        vector3(5125.71, -5082.28, 2.28),
        vector3(5127.81, -5088.53, 2.24),
        vector3(5128.48, -5203.15, 2.86),
        vector3(5129.37, -5212.72, 3.11),
        vector3(5129.37, -5149.09, 2.22),
        vector3(5129.91, -5084.37, 2.88),
        vector3(5131.34, -4942.07, 15.03),
        vector3(5133.11, -5128.91, 2.11),
        vector3(5133.85, -4951.19, 15.68),
        vector3(5133.9, -5102.95, 2.16),
        vector3(5134.2, -5077.35, 2.4),
        vector3(5134.25, -5088.14, 2.26),
        vector3(5136.96, -5198.0, 2.57),
        vector3(5139.05, -4931.73, 15.06),
        vector3(5139.15, -4956.91, 14.48),
        vector3(5140.0, -5127.58, 8.43),
        vector3(5140.45, -5085.12, 2.3),
        vector3(5141.24, -4949.42, 14.44),
        vector3(5142.25, -5241.73, 26.3),
        vector3(5142.74, -4963.63, 14.22),
        vector3(5144.3, -5131.77, 8.71),
        vector3(5144.43, -5054.09, 4.24),
        vector3(5144.5, -5241.63, 9.51),
        vector3(5144.77, -5191.63, 2.57),
        vector3(5144.9, -5249.82, 9.55),
        vector3(5145.15, -4960.63, 14.13),
        vector3(5146.47, -5055.27, 20.4),
        vector3(5146.73, -5055.54, 15.6),
        vector3(5146.86, -5029.04, 5.35),
        vector3(5147.44, -4935.39, 15.47),
        vector3(5147.74, -4954.38, 14.2),
        vector3(5148.48, -5053.35, 20.4),
        vector3(5148.54, -5040.33, 4.57),
        vector3(5150.0, -5120.07, 7.65),
        vector3(5150.0, -4963.9, 13.94),
        vector3(5150.14, -5059.53, 3.83),
        vector3(5150.5, -4957.39, 14.0),
        vector3(5151.01, -4933.14, 14.57),
        vector3(5151.09, -4939.35, 14.34),
        vector3(5152.58, -5133.88, 7.2),
        vector3(5152.8, -5125.54, 7.3),
        vector3(5153.91, -4944.0, 14.19),
        vector3(5154.07, -4932.65, 30.88),
        vector3(5154.13, -4935.63, 30.88),
        vector3(5154.39, -4935.75, 26.08),
        vector3(5157.44, -5202.14, 4.2),
        vector3(5158.44, -4938.39, 14.04),
        vector3(5159.11, -5134.93, 2.38),
        vector3(5160.9, -5169.84, 1.98),
        vector3(5161.0, -4948.89, 13.9),
        vector3(5161.14, -4943.6, 13.87),
        vector3(5161.52, -5156.89, 2.44),
        vector3(5165.0, -4935.76, 13.78),
        vector3(5167.93, -4985.8, 13.64),
        vector3(5168.78, -4943.37, 13.7),
        vector3(5170.36, -5187.24, 2.25),
        vector3(5171.87, -5135.14, 2.77),
        vector3(5172.89, -4989.46, 13.85),
        vector3(5176.44, -5125.92, 2.92),
        vector3(5182.85, -4993.96, 14.26),
        vector3(5188.11, -5001.22, 14.08),
        vector3(5189.6, -5133.89, 3.34),
        vector3(5192.77, -5005.71, 14.12),
        vector3(5197.37, -5011.55, 14.3),
        vector3(5197.74, -5006.73, 14.16),
        vector3(5202.92, -5215.32, 15.65),
        vector3(5206.96, -5196.66, 14.59),
        vector3(5209.26, -5206.17, 15.69),
        vector3(5211.38, -5223.31, 17.29),
        vector3(5213.5, -5225.97, 17.46),
        vector3(5214.39, -5215.99, 16.73),
        vector3(5217.83, -5197.52, 15.29),
        vector3(4344.84, -4575.92, 4.13),
        vector3(4351.9, -4560.31, 4.21),
        vector3(4353.06, -4590.07, 4.56),
        vector3(4365.05, -4598.04, 4.23),
        vector3(4368.61, -4560.4, 4.21),
        vector3(4376.96, -4559.58, 4.2),
        vector3(4377.44, -4596.77, 3.0),
        vector3(4384.44, -4585.74, 3.12),
        vector3(4386.02, -4569.27, 4.31),
        vector3(4411.8, -4471.94, 4.34),
        vector3(4416.11, -4482.81, 4.32),
        vector3(4418.79, -4454.58, 5.17),
        vector3(4418.9, -4493.9, 4.22),
        vector3(4419.7, -4465.86, 4.33),
        vector3(4424.44, -4479.54, 4.32),
        vector3(4427.79, -4486.67, 4.22),
        vector3(4437.85, -4488.33, 4.23),
        vector3(4453.76, -4480.5, 4.24),
        vector3(4463.33, -4475.58, 4.23),
        vector3(4470.4, -4588.11, 5.6),
        vector3(4478.98, -4585.03, 5.56),
        vector3(4488.09, -4515.73, 4.41),
        vector3(4489.81, -4511.39, 4.19),
        vector3(4498.44, -4512.42, 4.01),
        vector3(4518.2, -4507.2, 4.26),
        vector3(4526.71, -4511.4, 5.14),
        vector3(4593.52, -4462.19, 3.01),
        vector3(4596.02, -4466.73, 3.07),
        vector3(4602.14, -4464.35, 3.17),
        vector3(4650.68, -4455.71, 6.7),
        vector3(4652.62, -4450.27, 6.98),
        vector3(4792.84, -4320.05, 5.83),
        vector3(4793.52, -4724.92, 3.97),
        vector3(4795.38, -4736.45, 4.86),
        vector3(4795.51, -4325.38, 6.38),
        vector3(4795.94, -4719.95, 4.72),
        vector3(4796.18, -4309.1, 4.84),
        vector3(4796.28, -4730.21, 3.89),
        vector3(4800.33, -4303.45, 4.94),
        vector3(4801.45, -4715.51, 6.09),
        vector3(4801.49, -4655.86, 16.17),
        vector3(4804.61, -4297.56, 5.19),
        vector3(4805.15, -4310.07, 5.15),
        vector3(4805.55, -4661.56, 15.97),
        vector3(4807.05, -4726.59, 6.6),
        vector3(4808.97, -4718.0, 7.18),
        vector3(4809.33, -4648.42, 17.18),
        vector3(4809.49, -4665.09, 15.4),
        vector3(4810.73, -4304.92, 5.3),
        vector3(4819.17, -4658.39, 16.15),
        vector3(4819.4, -4642.7, 17.51),
        vector3(4820.23, -4455.69, 8.42),
        vector3(4821.33, -4297.78, 5.58),
        vector3(4824.0, -4313.25, 5.55),
        vector3(4824.56, -4318.8, 5.39),
        vector3(4826.73, -4456.29, 8.4),
        vector3(4828.68, -4469.39, 8.51),
        vector3(4831.58, -4452.43, 8.67),
        vector3(4831.74, -4636.45, 16.62),
        vector3(4831.99, -4307.43, 5.56),
        vector3(4835.91, -4650.5, 15.59),
        vector3(4836.84, -4446.94, 8.94),
        vector3(4841.38, -4445.97, 8.87),
        vector3(4842.87, -4469.02, 8.22),
        vector3(4845.56, -4317.83, 5.41),
        vector3(4850.5, -4448.23, 8.81),
        vector3(4851.83, -4675.18, 11.35),
        vector3(4853.01, -4453.49, 8.16),
        vector3(4853.39, -4668.32, 12.29),
        vector3(4854.54, -4633.83, 14.63),
        vector3(4855.04, -4622.25, 15.33),
        vector3(4855.82, -4658.7, 13.44),
        vector3(4857.03, -4642.16, 14.34),
        vector3(4857.8, -4676.54, 10.85),
        vector3(4859.75, -4618.5, 15.7),
        vector3(4861.66, -4646.56, 14.05),
        vector3(4863.43, -4656.9, 13.34),
        vector3(4863.77, -4490.11, 9.98),
        vector3(4865.44, -4680.67, 10.01),
        vector3(4868.37, -4482.04, 10.04),
        vector3(4869.18, -4673.57, 11.26),
        vector3(4869.43, -4616.33, 15.65),
        vector3(4873.47, -4491.99, 10.15),
        vector3(4874.86, -4484.97, 10.15),
        vector3(4874.92, -4654.12, 13.12),
        vector3(4875.6, -4486.95, 26.94),
        vector3(4878.93, -4612.6, 15.57),
        vector3(4881.09, -4479.75, 10.05),
        vector3(4881.49, -4491.3, 10.15),
        vector3(4882.49, -4642.17, 13.21),
        vector3(4885.77, -4618.17, 15.1),
        vector3(4889.65, -4634.97, 13.68),
        vector3(4893.42, -4616.26, 15.41),
        vector3(4896.13, -4606.46, 16.8),
        vector3(4903.71, -4616.71, 15.0),
        vector3(4946.2, -4476.71, 9.13),
        vector3(4960.05, -4477.06, 10.55),
        vector3(4961.21, -4465.65, 10.56),
        vector3(4967.97, -4479.01, 9.85),
        vector3(4968.66, -4469.87, 10.39),
        vector3(4974.04, -4522.86, 9.06),
        vector3(4987.81, -4537.34, 9.02),
        vector3(4991.2, -4539.64, 9.02),
        vector3(4999.01, -4550.72, 8.92),
        vector3(5000.13, -4515.35, 7.92),
        vector3(5005.68, -4511.13, 7.21),
        vector3(5006.14, -4522.4, 8.14),
        vector3(5012.38, -4523.29, 7.77),
        vector3(5024.8, -4515.95, 6.65),
        vector3(5026.97, -4635.16, 4.95),
        vector3(5029.39, -4628.43, 4.9),
        vector3(5031.0, -4632.62, 21.69),
        vector3(5034.51, -4593.03, 4.82),
        vector3(5037.7, -4639.6, 3.77),
        vector3(5037.91, -4646.71, 3.43),
        vector3(5041.59, -4629.5, 3.56),
        vector3(5042.11, -4587.29, 3.37),
        vector3(5042.45, -4638.09, 2.82),
        vector3(5042.81, -4645.24, 2.77),
        vector3(5045.16, -4618.09, 3.14),
        vector3(5045.3, -4580.73, 3.67),
        vector3(5046.3, -4592.14, 3.01),
        vector3(5046.55, -4613.7, 3.12),
        vector3(5047.67, -4597.05, 2.93),
        vector3(5050.59, -4593.66, 2.9),
        vector3(5052.77, -4618.84, 2.97),
        vector3(5053.85, -4598.46, 2.87),
        vector3(5055.97, -4603.37, 2.87),
        vector3(5056.6, -4579.25, 3.33),
        vector3(5058.01, -4611.51, 2.87),
        vector3(5058.17, -4596.84, 2.87),
        vector3(5060.94, -4664.5, 3.11),
        vector3(5063.17, -4603.55, 2.85),
        vector3(5065.5, -4674.0, 3.09),
        vector3(5065.68, -4578.51, 3.43),
        vector3(5068.21, -4634.3, 2.42),
        vector3(5068.8, -4583.58, 3.16),
        vector3(5069.21, -4651.22, 2.48),
        vector3(5069.51, -4641.94, 2.38),
        vector3(5070.33, -4597.0, 2.87),
        vector3(5072.26, -4614.2, 2.73),
        vector3(5073.28, -4682.46, 3.11),
        vector3(5073.93, -4665.02, 2.72),
        vector3(5074.41, -4619.84, 2.56),
        vector3(5076.35, -4606.33, 2.91),
        vector3(5076.4, -4629.18, 2.41),
        vector3(5076.7, -4602.71, 2.89),
        vector3(5079.17, -4647.06, 2.39),
        vector3(5079.26, -4676.26, 2.64),
        vector3(5079.53, -4585.48, 3.34),
        vector3(5080.83, -4660.35, 2.28),
        vector3(5084.78, -4621.99, 2.54),
        vector3(5085.75, -4689.71, 2.73),
        vector3(5086.35, -4604.24, 3.15),
        vector3(5086.85, -4684.09, 2.37),
        vector3(5087.35, -4667.11, 2.55),
        vector3(5088.41, -4590.97, 3.32),
        vector3(5088.69, -4653.58, 1.92),
        vector3(5089.95, -4609.47, 2.55),
        vector3(5090.16, -4694.66, 3.17),
        vector3(5090.22, -4674.71, 2.56),
        vector3(5091.43, -4595.65, 3.54),
        vector3(5095.48, -4700.82, 3.32),
        vector3(5097.46, -4611.46, 2.4),
        vector3(5099.04, -4694.63, 3.09),
        vector3(5099.94, -4666.96, 2.44),
        vector3(5102.97, -4702.83, 3.08),
        vector3(5104.39, -4574.54, 4.24),
        vector3(5104.5, -4625.61, 2.63),
        vector3(5107.82, -4671.99, 2.35),
        vector3(5108.82, -4679.16, 2.41),
        vector3(5108.93, -4594.2, 4.05),
        vector3(5110.53, -4581.08, 4.35),
        vector3(5112.45, -4702.6, 3.14),
        vector3(5112.66, -4669.13, 2.4),
        vector3(5113.43, -4695.05, 3.08),
        vector3(5114.04, -4587.48, 4.63),
        vector3(5115.13, -4624.38, 2.63),
        vector3(5115.32, -4615.92, 2.82),
        vector3(5116.38, -4687.71, 2.77),
        vector3(5117.03, -4682.24, 2.26),
        vector3(5117.32, -4601.88, 3.61),
        vector3(5119.42, -4610.72, 3.1),
        vector3(5120.02, -4704.47, 3.03),
        vector3(5122.82, -4695.86, 2.8),
        vector3(5124.08, -4617.35, 2.63),
        vector3(5124.29, -4630.77, 1.44),
        vector3(5125.12, -4637.74, 1.41),
        vector3(5126.21, -4611.82, 2.73),
        vector3(5127.34, -4626.33, 2.6),
        vector3(5129.86, -4701.99, 2.49),
        vector3(5130.37, -4606.78, 2.67),
        vector3(5130.74, -4683.28, 1.62),
        vector3(5131.24, -4707.52, 2.44),
        vector3(5133.48, -4695.68, 2.21),
        vector3(5133.6, -4638.47, 1.41),
        vector3(5136.82, -4606.95, 2.68),
        vector3(5137.63, -4708.27, 2.26),
        vector3(5138.75, -4630.43, 2.62),
        vector3(5140.19, -4599.31, 3.81),
        vector3(5140.35, -4703.23, 2.18),
        vector3(5140.38, -4635.28, 1.44),
        vector3(5148.31, -4598.8, 3.61),
        vector3(5150.48, -4623.18, 2.59),
        vector3(5152.51, -4605.79, 3.07),
        vector3(5154.11, -4638.93, 2.65),
        vector3(5155.52, -4667.39, 1.44),
        vector3(5160.81, -4688.21, 2.27),
        vector3(5163.15, -4637.24, 2.61),
        vector3(5163.87, -4619.71, 2.85),
        vector3(5165.89, -4649.42, 2.67),
        vector3(5166.76, -4699.48, 2.17),
        vector3(5167.73, -4681.79, 2.38),
        vector3(5170.38, -4660.42, 2.52),
        vector3(5174.42, -4690.19, 2.31),
        vector3(5174.44, -4644.52, 2.52),
        vector3(5176.43, -4676.54, 2.44),
        vector3(5180.85, -4662.53, 2.52),
        vector3(5182.18, -4670.13, 2.49),
        vector3(5184.04, -4648.87, 2.52),
        vector3(5185.45, -4656.57, 2.53),
        vector3(5186.47, -4681.12, 1.71),
        vector3(5192.01, -4657.04, 2.21),
    },
}