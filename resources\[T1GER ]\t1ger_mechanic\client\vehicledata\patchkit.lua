--- Use a patch kit to temporarily repair core and service parts
local function UsePatchKit()
    local patchKit = Config.PatchKit
    if not patchKit then return end

    -- Get the closest vehicle
    local vehicle, vehicleDist = lib.getClosestVehicle(coords, 5.0, false)
    if not vehicle or not DoesEntityExist(vehicle) or not IsEntityAVehicle(vehicle) then
        return _API.ShowNotification(locale("notification.no_vehicle_nearby"), "inform", {})
    end

    -- Is near the hood/engine?
    local hood = GetVehicleClosestHood(vehicle, 0.8, false)
    if not hood then
        return _API.ShowNotification(locale("notification.must_be_near_engine_hood"), "inform", {})
    end

    -- Face vehicle
    TaskTurnPedToFaceEntity(player, vehicle, 1000)

    -- Open hood if exists
    VehicleHoodAnimation(vehicle, false)

    -- Play animation
    local anim = {dict = "mini@repair", clip = "fixing_a_player", blendIn = 2.0, blendOut = 2.0, flag = 1}
    lib.requestAnimDict(anim.dict)
    TaskPlayAnim(player, anim.dict, anim.clip, anim.blendIn, anim.blendOut, -1, anim.flag, 0, 0, 0, 0)

    -- Skillcheck
    local skillcheck = false
    if patchKit.Skillcheck.enable then
        skillcheck = SkillCheck(patchKit.Skillcheck.difficulty, patchKit.Skillcheck.inputs)
    else
        skillcheck = true 
    end
    if not skillcheck then
        ClearPedTasks(player)
        VehicleHoodAnimation(vehicle, true)
        return
    end

    -- Progress bar
    local success = ProgressBar({
        duration = patchKit.Duration, -- You can move this to Config if you want
        label = locale("progressbar.patch_kit"),
        useWhileDead = false,
        canCancel = true,
        disable = { move = true, combat = true }
    })
    if not success then
        ClearPedTasks(player)
        VehicleHoodAnimation(vehicle, true)
        return
    end

    -- Remove item
    TriggerServerEvent("t1ger_mechanic:server:removeItem", patchKit.Item, 1)

    -- Get vehicle state data
    local data = GetVehicleData(vehicle)

    if data then
        -- Patch core parts
        if data.core_parts then
            for partName, health in pairs(data.core_parts) do
                if health <= 0.0 then
                    local newHealth = math.clamp(patchKit.CorePartHealth, 1.0, 100.0)
                    data.core_parts[partName] = math.round(newHealth, 2)
                end
            end
        end

        -- Patch service parts
        if data.service_parts then
            for partName, mileage in pairs(data.service_parts) do
                local sPart = Config.ServiceParts[partName]
                if sPart then
                    local serviceInterval = GetVehicleServiceInterval(vehicle, partName)
                    if serviceInterval and mileage > serviceInterval then
                        local remaining = math.max(0.0, patchKit.ServicePartRemainingMileage or 10.0)
                        local newMileage = math.max(0.0, serviceInterval - remaining)
                        data.service_parts[partName] = math.round(newMileage, 2)
                    end
                end
            end
        end

        -- Update statebag
        SetVehicleData(vehicle, data, true)
    end

    -- Repair sound
    RepairSound()

    -- Clear tasks
    ClearPedTasks(player)
    VehicleHoodAnimation(vehicle, true)

    -- Notification
    _API.ShowNotification(locale("notification.patchkit_success"), "success", {})
end

--- Event for using repair kit item:
RegisterNetEvent("t1ger_mechanic:client:usePatchKit", function()
    UsePatchKit()
end)