if not Config.UnlockVehicle.Enable then 
    return 
end

-- Function to unlock closest vehicle:
function UnlockVehicle()
    if not Config.UnlockVehicle.Enable then return end

    local isMechanic, shopId = IsPlayerMechanic()
    if not isMechanic then return end
    
	-- ray cast:
	local hit, entityHit, endCoords, surfaceNormal, materialHash = lib.raycast.fromCoords(coords, GetOffsetFromEntityInWorldCoords(player, 0.0, 5.0, 0.0), 2)
	if not hit or GetEntityType(entityHit) ~= 2 or not IsEntityAVehicle(entityHit) or not DoesEntityExist(entityHit) then 
		return _API.ShowNotification(locale("notification.no_vehicle_in_direction"), "inform", {})
	end

	local unlocked = false
    local min, max = GetModelDimensions(GetEntityModel(entityHit))
    local unlockCoords = GetOffsetFromEntityInWorldCoords(entityHit, min.x-0.2,0.0,0.0)


    while not unlocked do
        Wait(1)

        local distance = #(coords - vector3(unlockCoords.x, unlockCoords.y, unlockCoords.z))
        if distance < Config.UnlockVehicle.DrawDist then
            -- draw text
            Draw3DText(unlockCoords.x, unlockCoords.y, unlockCoords.z, locale("drawtext.unlock_vehicle"))

            -- key press
            if IsControlJustReleased(0, Config.UnlockVehicle.Keybind) then
                if distance <= 1.0 then
                    TaskTurnPedToFaceEntity(player, entityHit, 1.0)
                    Wait(500)
                    SetCurrentPedWeapon(player, GetHashKey("WEAPON_UNARMED"), true)
                    Wait(300)
                    if ProgressBar({
                        duration = Config.UnlockVehicle.Duration,
                        label = locale("progressbar.unlock_vehicle"),
                        useWhileDead = false,
                        canCancel = true,
                        anim = Config.UnlockVehicle.Anim,
                        disable = {
                            move = true,
                            combat = true
                        }
                    }) then
                        ClearPedTasks(player)
                        unlocked = true
                        break
                    end

                else
                    _API.ShowNotification(locale("notification.move_closer_to_interact"), "inform", {})
                end
            end
        end
    end

    -- if unlocked:
    if unlocked then
        PlayVehicleDoorOpenSound(entityHit, 0)
        SetVehicleDoorsLockedForAllPlayers(entityHit, false)
        SetVehicleDoorsLocked(entityHit, 1)
    end
end

if Config.UnlockVehicle.Command.enable == true then
    RegisterCommand(Config.UnlockVehicle.Command.name, function()
        UnlockVehicle()
    end, false)
end