Core = nil

if Config.Framework == 'qbcore' or Config.Framework == 'qb-core' then
    Core = exports['qb-core']:GetCoreObject();
elseif Config.Framework == 'standalone' or Config.Framework == '' then
    -- Custom function
end

if Config.Framework == 'qbcore' or Config.Framework == 'qb-core' then
    Core.Commands.Add('givelodo', 'Ludo Game', { { name = 'id', help = 'Player Id' } }, true, function(source, args)
        TriggerClientEvent('ludo:client:menu', tonumber(args[1]))
    end, 'god')


    Core.Commands.Add('closeGame', 'Ludo Game', { }, true, function(source, args)
        TriggerClientEvent('ludo:client:FF', source)
    end, 'god')
end

function GiftCommand()
    local rr = math.random( 1, #Config.KeyRandom )
    local text
    if Config.KeyRandom[rr].type == 'red' then 
        text =  '~r~ '..Config.KeyRandom[rr].name..'' --'<span style="color:red;">'..Config.KeyRandom[rr].name..'</span>'
    else
        text = Config.KeyRandom[rr].name
    end
    text = string.sub(text,1, 1000)
    TriggerClientEvent('3dme:ludo', -1, text, source)
end