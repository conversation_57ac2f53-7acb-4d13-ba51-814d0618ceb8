
local teamMapping = {}     
local squads = {}          
local squadCounter = 0     

local maxSquadSize = 1     

local allplayers = {}
local spectateplayers = {}
local gameStarted = false
local gameHASStarted = false
local cashprize = Config.MinWager
local lives = 1
local dimention = math.random(400,999)
local everyone = 0
local CoolDownTime = Config.CircleCooldownSeconds
local GulagPlayers = {}
local GulagMap = "Set_Dystopian_02"

local syncedArmor = {}

local ChosenMap = "cayo-perico"

local WaitingForAllPlayersToAccept = 0
local WaitingForPlayerToAccept


RegisterNetEvent("Pug:server:SetLobbyMode", function(mode)
    if gameStarted then TriggerClientEvent('Pug:client:BattleRoyaleNotify', source, "You can not do this during a live match...", 'error') return end

    if mode < 1 or mode > 4 then
        if Config.Debug then
            print("Invalid lobby mode. Must be 1,2,3, or 4.")
        end
        return
    end
    maxSquadSize = mode
    if Config.Debug then
        print("Lobby/Team mode set to:", mode)
    end

	if mode == 1 then

        teamMapping = {}
        squads = {}
        squadCounter = 0
        print("All squads disbanded due to SOLO mode.")

        for _, playerId in ipairs(allplayers) do
            TriggerClientEvent('Pug:client:BattleRoyaleNotify', playerId, "Squads have been disbanded. Solo mode is now active.")
            TriggerClientEvent("Pug:client:SetTeammates", playerId)
        end
    end
end)

local function GetOrCreateSquad()
    for squadId, data in pairs(squads) do
        if #data.members < maxSquadSize then
            return squadId
        end
    end

    squadCounter = squadCounter + 1
    squads[squadCounter] = {
        members = {},
        aliveCount = 0 
    }
    return squadCounter
end

local function RemovePlayerFromSquad(playerSrc)
    local sqId = teamMapping[playerSrc]
    if not sqId then return end

    if squads[sqId] then
        for i, memSrc in ipairs(squads[sqId].members) do
            if memSrc == playerSrc then
                table.remove(squads[sqId].members, i)
                squads[sqId].aliveCount = squads[sqId].aliveCount - 1
                break
            end
        end

        if #squads[sqId].members > 0 then
            local updatedList = {}
            for _, id in ipairs(squads[sqId].members) do
                local firstname, lastname = GetCharacterInfo(id)
                table.insert(updatedList, {
                    id = id,
                    name = (firstname and lastname) and (firstname .. " " .. lastname) or ("Player " .. id)
                })
            end

            for _, id in ipairs(squads[sqId].members) do
                TriggerClientEvent("Pug:client:SetTeammates", id, updatedList)
            end
        else
            squads[sqId] = nil
        end
    end

    teamMapping[playerSrc] = nil
end

local function GetSquadMembers(playerSrc)
    local sqId = teamMapping[playerSrc]
    if not sqId or not squads[sqId] then return {} end
    return squads[sqId].members
end

local function GetSquadMembersWithNames(playerSrc)
    local sqId = teamMapping[playerSrc]
    if not sqId or not squads[sqId] then return {} end

    local result = {}

    for _, memberId in ipairs(squads[sqId].members) do
        local firstname, lastname = GetCharacterInfo(memberId)
        table.insert(result, {
            id = memberId,
            name = (firstname and lastname) and (firstname .. " " .. lastname) or ("Player " .. memberId)
        })
    end

    return result
end


local function IsPlayerInASquad(playerSrc)
    return teamMapping[playerSrc] ~= nil
end

local function AddPlayerToASquad(playerSrc)
    if IsPlayerInASquad(playerSrc) then
        return teamMapping[playerSrc]
    end

    local squadId = GetOrCreateSquad()
    table.insert(squads[squadId].members, playerSrc)
    squads[squadId].aliveCount = squads[squadId].aliveCount + 1

    teamMapping[playerSrc] = squadId

    local squadMembers = GetSquadMembersWithNames(playerSrc)
    for _, member in ipairs(GetSquadMembers(playerSrc)) do
        TriggerClientEvent("Pug:client:SetTeammates", member, squadMembers)
    end
    

    return newSquadId
end

function GetPlayerCash(player)
	if Framework == "QBCore" then
		return player.PlayerData.money["cash"]
	else
		return player.getMoney()
	end
	return 0
end

RegisterNetEvent("Pug:server:JoinFriendSquad", function(friendID)
    local src = source
    local friendIsInRoyale = false
	if gameStarted then TriggerClientEvent('Pug:client:BattleRoyaleNotify', src, "You can not do this during a live match...", 'error') return end

	if src == friendID then
		TriggerClientEvent('Pug:client:BattleRoyaleNotify', src, 'This is your own ID...', 'error')
		return
	end

    for _, pid in pairs(allplayers) do
        if pid == friendID then
            friendIsInRoyale = true
            break
        end
    end

    if not friendIsInRoyale then
        TriggerClientEvent('Pug:client:BattleRoyaleNotify', src, 'Your friend is not in the Royale.', 'error')
        return
    end

    RemovePlayerFromSquad(src)

    local friendSquadId = teamMapping[friendID]
    if friendSquadId and squads[friendSquadId] then
        if #squads[friendSquadId].members < maxSquadSize then
            table.insert(squads[friendSquadId].members, src)
            teamMapping[src] = friendSquadId
            if not isInTable(allplayers, src) then
                table.insert(allplayers, src)
                table.insert(spectateplayers, src)
            end
            TriggerClientEvent('Pug:client:BattleRoyaleNotify', src, 'Joined friend’s squad!', 'success')
            
            local squadMembers = GetSquadMembersWithNames(src)
            for _, id in ipairs(squads[friendSquadId].members) do
                TriggerClientEvent("Pug:client:SetTeammates", id, squadMembers)
            end
        else
            TriggerClientEvent('Pug:client:BattleRoyaleNotify', src, 'Your friend’s squad is already full!', 'error')
        end
    else
        local newSquadId = AddPlayerToASquad(src)  
        TriggerClientEvent('Pug:client:BattleRoyaleNotify', src, 'Could not find friend’s squad. Joined a new squad instead.', 'error')
    end
end)

local LeaderboardRefreshTime

local function FullyResetEverything()
    -- Full reset
    GulagPlayers = {}
    syncedArmor = {}
    spectateplayers = {}
    allplayers = {}
    gameStarted = false
    lives = 1
    everyone = 0
    teamMapping = {}     
    squads = {}   
end

-- FRAMEWORK FUNCTIONS
function GetCharacterInfo(playerId)
    if Framework == "QBCore" then
        local player = FWork.Functions.GetPlayer(playerId)
        if player and player.PlayerData and player.PlayerData.charinfo then
            return player.PlayerData.charinfo.firstname,
			player.PlayerData.charinfo.lastname,
			player.PlayerData.citizenid
        end
    else
        local xPlayer = FWork.GetPlayerFromId(playerId)
        if xPlayer then
            local first = xPlayer.get("firstName") or xPlayer.get("firstname")
            local last = xPlayer.get("lastName") or xPlayer.get("lastname")
            local cid = xPlayer.identifier
            return first, last, cid
        end
    end
    return nil, nil, nil
end
local function PlayerHasPermission(src, perm)
    if Framework == "QBCore" then
        return FWork.Functions.HasPermission(src, perm)
    else
        return IsPlayerAceAllowed(src, "command") 
    end
end

local function GetUnifiedPlayer(src)
    if Framework == "QBCore" then
        return FWork.Functions.GetPlayer(src)
    else
        return FWork.GetPlayerFromId(src)
    end
end

local function FixAddMoneyForOx(Player, Amount) 
	if Player then
		CreateThread(function()
			Wait(5000)
			if Framework == "QBCore" then
				Player.Functions.AddMoney('cash', Amount)
			else
				Player.addAccountMoney('money', Amount)
			end
		end)
	end
end

function RemoveMoneyFromPlayer(player, amount)
	if Framework == "QBCore" then
		player.Functions.RemoveMoney('cash', amount)
	else
		player.removeAccountMoney('money', amount)
	end
end


local function PlayerWon(PlayerSrc)
	print("Player WON!")

	local first, last, cid = GetCharacterInfo(PlayerSrc)
	if not cid or not first then return end

	local name = first .. " " .. last

	MySQL.update([[
		INSERT INTO pug_battleroyale_stats (type, citizenid, name, wins)
		VALUES (?, ?, ?, 1)
		ON DUPLICATE KEY UPDATE wins = wins + 1, name = ?
	]], { maxSquadSize, cid, name, name })

	if not LeaderboardRefreshTime then
		LeaderboardRefreshTime = true
		TriggerClientEvent("Pug:client:RefreshLeaderboard", -1)
		CreateThread(function()
			Wait(7000)
			LeaderboardRefreshTime = false
		end)
	end
end


local function PlayerLost(PlayerSrc)
	print("Player Lost!")

	local first, last, cid = GetCharacterInfo(PlayerSrc)
	if not cid or not first then return end

	local name = first .. " " .. last

	MySQL.update([[
		INSERT INTO pug_battleroyale_stats (type, citizenid, name, losses)
		VALUES (?, ?, ?, 1)
		ON DUPLICATE KEY UPDATE losses = losses + 1, name = ?
	]], { maxSquadSize, cid, name, name })
end


RegisterNetEvent("Pug:server:SyncMyArmorBR", function(armor)
    local src = source
	if not syncedArmor[src] then syncedArmor[src] = 0 end
    syncedArmor[src] = syncedArmor[src] + armor
end)

local damageCooldowns = {}

AddEventHandler("weaponDamageEvent", function(sender, data)
	if not gameHASStarted then return end
	local attackerId = sender
	local targetPed = NetworkGetEntityFromNetworkId(data.hitGlobalId)
	if not IsPedAPlayer(targetPed) then return end

	local targetServerId = NetworkGetEntityOwner(targetPed)
	local targetCoords = GetEntityCoords(targetPed)
	local weaponDamage = data.weaponDamage
	local isFinalHit = data.willKill
	local weaponType = data.weaponType

	local now = os.time()
	damageCooldowns[attackerId] = damageCooldowns[attackerId] or {}
	local lastHitTime = damageCooldowns[attackerId][targetServerId] or 0

	if isFinalHit and (now - lastHitTime < 5) then
		return
	end

	if isFinalHit then
		damageCooldowns[attackerId][targetServerId] = now
	end

	local previousArmor = syncedArmor[targetServerId] or 0
	local armorDamage = math.min(previousArmor, weaponDamage)
	local healthDamage = weaponDamage - armorDamage
	local newArmor = math.max(0, previousArmor - weaponDamage)
	syncedArmor[targetServerId] = newArmor
	local armorCracked = previousArmor > 0 and newArmor == 0

	TriggerClientEvent('Pug:client:ShowHitEffectSplit', attackerId, targetCoords, armorDamage, healthDamage, isFinalHit, weaponType, armorCracked)
end)


-- Functions
local function round(x)
    return x>=0 and math.floor(x+0.5) or math.ceil(x-0.5)
end

function SvInfo()
    local info = {
		players = allplayers,
	}
	return info
end

-- Callbacks
Config.FrameworkFunctions.CreateCallback('Pug:serverCB:CheckongoingRoyale', function(source, cb)
	local src = source
	if gameHASStarted and everyone >= 1 then
		TriggerClientEvent('Pug:client:BattleRoyaleNotify', src, Translations.error.active_game, 'error')
	elseif gameHASStarted then
		TriggerClientEvent('Pug:client:BattleRoyaleNotify', src, Translations.error.closing_game, 'error')
	end
	cb(gameHASStarted)
end)

Config.FrameworkFunctions.CreateCallback('Pug:serverCB:GetAllPlayers', function(source, cb)
	cb(allplayers)
end)

Config.FrameworkFunctions.CreateCallback('Pug:ServerCB:ViewBattleRoyale', function(source, cb)
    local src = source
	local plyers = (#allplayers)
	local lobby = {}
	local playersdisplay = {}
    local _, _, citizenid = GetCharacterInfo(src)
    if not citizenid then return cb({}) end

	if (#allplayers) >= 1 then
		for _,playerId in pairs(allplayers) do
			local firstname, lastname = GetCharacterInfo(playerId)
			if firstname and lastname then
				playersdisplay[#playersdisplay+1] = firstname .. " " .. lastname
			end
		end
	end

	lobby = {
		life = lives,
		playsrsall = playersdisplay,
		Players = plyers,
		amount = cashprize,
		cooldown = CoolDownTime,
		spec = spectateplayers,
		type = maxSquadSize,
        Map = ChosenMap,
        Started = gameStarted,
        Xp = GetPlayerXP(citizenid, cb),
	}
	cb(lobby)
end)

Config.FrameworkFunctions.CreateCallback('Pug:ServerCB:ViewCooldownMenu', function(source, cb)
	local time = {
		cooldown = tonumber(CoolDownTime),
		started = gameHASStarted,
	}
	cb(time)
end)

Config.FrameworkFunctions.CreateCallback('Pug:SVCB:SpecatateplayersRoyale', function(source, cb)
    local src = source
	local info = {}
	if gameStarted then
		if (#spectateplayers) >= 1 then
			for k, v in pairs(spectateplayers) do
				local targetped = GetPlayerPed(v)
				local firstname, lastname = GetCharacterInfo(v)
				if firstname and lastname then
					table.insert(info, {
						coords = GetEntityCoords(targetped),
						name = firstname.. " " .. lastname,
						id = v,
						map = GulagMap,
					})
				end
			end
		else
			cb(false)
			TriggerClientEvent('Pug:client:BattleRoyaleNotify', src, Translations.error.need_players,'error')
            FullyResetEverything()
			return
		end
	else
		cb(false)
		TriggerClientEvent('Pug:client:BattleRoyaleNotify', src, Translations.error.started_spectate,'error')
		return
	end
	cb(info)
end)

RegisterServerEvent('Pug:server:ChangeCoolDownTime',function(time)
    if gameStarted then TriggerClientEvent('Pug:client:BattleRoyaleNotify', source, "You can not do this during a live match...", 'error') return end
	CoolDownTime = tonumber(time)
end)

Config.FrameworkFunctions.CreateCallback("Pug:ServerCB:GetValidTeammateToSpectate", function(source, cb, teammateIds)
    local src = source
    local result = nil

    for _, id in ipairs(teammateIds) do
        if isInTable(allplayers, id) then
            local ped = GetPlayerPed(id)
            local coords = GetEntityCoords(ped)
            local first, last = GetCharacterInfo(id)

            if first and last and coords then
                result = {
                    id = id,
                    coords = coords,
                    name = first .. " " .. last,
                    map = GulagMap,
                }
                break
            end
        end
    end

    cb(result)
end)
local function CheckAndHandleFullGulag(squadId)
    if not squadId or not squads[squadId] then return end

    -- Abort if at least one member is NOT in the Gulag
    for _, member in ipairs(squads[squadId].members) do
        if not isInTable(GulagPlayers, member) then
            return
        end
    end

    -- At this point the entire squad is in the Gulag ➜ they lose
    EliminateSquad(squadId)

    -- Remove those players from GulagPlayers + clean their UI
    for i = #GulagPlayers, 1, -1 do
        local id = GulagPlayers[i]
        if not teamMapping[id] then  -- squad was wiped in EliminateSquad
            table.remove(GulagPlayers, i)
            TriggerClientEvent("Pug:client:RemoveFromGulags", id)
        end
    end
end
RegisterServerEvent("Pug:server:AddPlayerToGulag", function()
    local src = source
    Wait(5000)
    if not isInTable(GulagPlayers, src) then
        table.insert(GulagPlayers, src)
    end

    local squadId = teamMapping[src]
    CheckAndHandleFullGulag(squadId)
end)
RegisterServerEvent("Pug:server:StarGulagLoop", function()
	if everyone < Config.GulagPlayersMin then return end
    CreateThread(function()
        while gameHASStarted do
            Wait(Config.GulagCheckTime * 1000)
            if not gameHASStarted then break end
            if #GulagPlayers < 2 then goto continue end

            local shuffled = {}
            for _, gulagSrc in ipairs(GulagPlayers) do
                table.insert(shuffled, gulagSrc)
            end

            for i = #shuffled, 2, -1 do
                local j = math.random(1, i)
                shuffled[i], shuffled[j] = shuffled[j], shuffled[i]
            end

            local i = 1
            while i <= #shuffled - 1 do
                local p1 = shuffled[i]
                local p2 = shuffled[i + 1]

                local gulagDimension = math.random(1000, 9999)
                local weapon = Config.RandomGulagWeapon[math.random(1, #Config.RandomGulagWeapon)]

                local flag = nil
                if Config.FlagModel then
                    local coords = Config.MapLocation[GulagMap]["FlagSpawn"]
                    flag = CreateObjectNoOffset(Config.FlagModel, coords.x, coords.y, coords.z - 1, 1, true, 0)
                    SetEntityRoutingBucket(flag, gulagDimension)
                end

                SetPlayerRoutingBucket(p1, gulagDimension)
                SetPlayerRoutingBucket(p2, gulagDimension)

                TriggerClientEvent("Pug:client:PutPlayerInGulag1v1", p1, 1, os.time(), flag, weapon, p2)
                TriggerClientEvent("Pug:client:PutPlayerInGulag1v1", p2, 2, os.time(), flag, weapon, p1)

                for _, v in ipairs({p1, p2}) do
                    for idx, id in ipairs(GulagPlayers) do
                        if id == v then
                            table.remove(GulagPlayers, idx)
                            break
                        end
                    end
                end

                i = i + 2
                Wait(500)
            end

			if everyone < Config.GulagPlayersMin and #GulagPlayers > 0 then
				for _, v in ipairs(GulagPlayers) do
					TriggerClientEvent("Pug:client:GulagRemoveAllDutooCount", v)
				end
				GulagPlayers = {}
			end

            ::continue::
        end
    end)
end)

RegisterServerEvent("Pug:server:LostTheGulag", function(OtherPlayer)
	local src = source
	TriggerClientEvent("Pug:client:RemoveFromGulags", src)
	if OtherPlayer then
		SetPlayerRoutingBucket(tonumber(OtherPlayer), tonumber(dimention))
		TriggerClientEvent("Pug:client:RemoveFromGulags", tonumber(OtherPlayer))
	end
end)

RegisterServerEvent("Pug:server:RemoveFrumGulag", function()
	for k, v in pairs(GulagPlayers) do
		if v == source then
			table.remove(GulagPlayers, k)
		end
	end
end)

RegisterServerEvent("Pug:server:setBucket", function(tog)
	local src = source
	if tog then
		SetPlayerRoutingBucket(src, tonumber(dimention))
	else
		SetPlayerRoutingBucket(src, 0)
	end
end)

RegisterServerEvent("Pug:server:KillFeed", function(kill, causes)
	local src = source

	local pFirst, pLast, pCID = GetCharacterInfo(src)
	local kFirst, kLast, kCID = GetCharacterInfo(kill)

	for _, v in pairs(allplayers) do
		if kFirst and pFirst then
			if causes and causes ~= 0 then
				print(kFirst .. " " .. kLast .. ' killed ' .. pFirst .. " " .. pLast .. " with a " .. causes)
				TriggerClientEvent('Pug:client:BattleRoyaleNotify', v, kFirst .. " " .. kLast .. ' killed ' .. pFirst .. " " .. pLast .. " with a " .. causes, 'error')
			else
				TriggerClientEvent('Pug:client:BattleRoyaleNotify', v, kFirst .. " " .. kLast .. ' killed ' .. pFirst .. " " .. pLast, 'error')
			end
		elseif pFirst then
			if causes and causes ~= 0 then
				TriggerClientEvent('Pug:client:BattleRoyaleNotify', v, pFirst .. " " .. pLast .. " died from " .. causes, 'error')
			else
				TriggerClientEvent('Pug:client:BattleRoyaleNotify', v, pFirst .. " " .. pLast .. ' died', 'error')
			end
		end
	end

	if kCID then
		TriggerClientEvent('Pug:client:UpdateKills', kill)
        AwardKillXP(kill)

		local killerName = kFirst .. " " .. kLast
		MySQL.update([[
			INSERT INTO pug_battleroyale_stats (type, citizenid, name, kills)
			VALUES (?, ?, ?, 1)
			ON DUPLICATE KEY UPDATE kills = kills + 1, name = ?
		]], { maxSquadSize, kCID, killerName, killerName })
	end

	if pCID then
		local victimName = pFirst .. " " .. pLast
		MySQL.update([[
			INSERT INTO pug_battleroyale_stats (type, citizenid, name, deaths)
			VALUES (?, ?, ?, 1)
			ON DUPLICATE KEY UPDATE deaths = deaths + 1, name = ?
		]], { maxSquadSize, pCID, victimName, victimName })
	end
end)

RegisterServerEvent("Pug:server:RemoveLootForEveryone", function(coords)
	for _, v in pairs(allplayers) do
		TriggerClientEvent("Pug:client:RemoveLootForEveryone", v, coords)
	end
end)

RegisterNetEvent("Pug:server:UpdateLootModel", function(coords, newModelName)
    for _, v in pairs(allplayers) do
        TriggerClientEvent("Pug:client:UpdateLootModel", v, coords, newModelName)
    end
end)
RegisterNetEvent("Pug:server:BroadcastDroppedLoot", function(droppedLoot)
    local finalLoot = {}

    for index, loot in ipairs(droppedLoot) do
        local coords = loot.coords
        local roundedCoords = vector3(
            tonumber(string.format("%.2f", coords.x)),
            tonumber(string.format("%.2f", coords.y)),
            tonumber(string.format("%.2f", coords.z))
        )

        local key = string.format("%.2f_%.2f_%.2f_%s_%d", roundedCoords.x, roundedCoords.y, roundedCoords.z, loot.item, index)

        table.insert(finalLoot, {
            key = key,
            coords = roundedCoords,
            item = loot.item,
            model = loot.model,
            type = loot.type
        })
    end

    for _, v in pairs(allplayers) do
        TriggerClientEvent("Pug:client:ReceiveDroppedLoot", v, finalLoot)
    end
end)


local function GenerateLootData()
    local Data = {}
    local LootTable = Config.GunsSingleTable[ChosenMap]

    for _, v in pairs(LootTable) do
        local random = math.random(1, #Config.GunOptions)
        local ammo = Config.GunOptions[random].ammo
        local amount = Config.GunOptions[random].amount
        local item = Config.GunOptions[random][math.random(1,6)]

        table.insert(Data, {
            coords = vector3(v.x, v.y, v.z),
            item = item,
            ammo = ammo,
            amount = amount
        })
    end
    
    return Data
end


RegisterNetEvent("Pug:server:SetRoyaleMap", function(Map)
    if gameStarted then TriggerClientEvent('Pug:client:BattleRoyaleNotify', source, "You can not do this during a live match...", 'error') return end
    ChosenMap = Map
end)


RegisterNetEvent("Pug:server:RoyaleSpawnCar", function()
    local spawnPool = Config.CarSpawn[ChosenMap]
    local spawnCount = math.floor(#spawnPool / 2)

    if spawnCount < 1 then
        print("Not enough car spawn positions available.")
        return
    end

    -- Get N random unique vectors
    local function getRandomSubset(tbl, amount)
        local copy = { table.unpack(tbl) }
        for i = #copy, 2, -1 do
            local j = math.random(1, i)
            copy[i], copy[j] = copy[j], copy[i]
        end
        local result = {}
        for i = 1, amount do
            result[#result + 1] = copy[i]
        end
        return result
    end

    local chosenSpawns = getRandomSubset(spawnPool, spawnCount)

    for _, spawn in ipairs(chosenSpawns) do
        Wait(250)
        local randomModel = Config.CarOptions[math.random(1, #Config.CarOptions)]
        local x, y, z, w = spawn.x, spawn.y, spawn.z, spawn.w

        local veh = CreateVehicle(randomModel, x, y, z, w, true, false)

        local startTime = os.clock()
        local timeout = 5.0

        while not DoesEntityExist(veh) do
            if os.clock() - startTime > timeout then
                print("Timeout: Vehicle didn't spawn within 5 seconds.")
                break
            end
            Wait(10)
        end

        if veh and DoesEntityExist(veh) then
            SetEntityRoutingBucket(veh, dimention)
        end
    end
end)


RegisterNetEvent("Pug:server:RoyaleSpawnHeliCopter", function()
    local spawnPool = Config.HeliSpawns[ChosenMap]
    local spawnCount = math.floor(#spawnPool / 2)
    local PlaneLocationTableData = {}

    if spawnCount < 1 then
        print("Not enough heli spawn points.")
        return
    end

    -- Shuffle and get half
    local function getRandomSubset(tbl, amount)
        local copy = { table.unpack(tbl) }
        for i = #copy, 2, -1 do
            local j = math.random(1, i)
            copy[i], copy[j] = copy[j], copy[i]
        end
        local result = {}
        for i = 1, amount do
            result[#result + 1] = copy[i]
        end
        return result
    end

    local chosenSpawns = getRandomSubset(spawnPool, spawnCount)

    for _, spawn in ipairs(chosenSpawns) do
        Wait(250)
        local model = Config.HeliOptions[math.random(1, #Config.HeliOptions)]
        local x, y, z, w = spawn.x, spawn.y, spawn.z, spawn.w

        local veh = CreateVehicle(model, x, y, z, w, true, false)
        local startTime = os.clock()
        local timeout = 5.0

        while not DoesEntityExist(veh) do
            if os.clock() - startTime > timeout then
                print("Timeout: Helicopter didn't spawn within 5 seconds.")
                break
            end
            Wait(10)
        end

        if veh and DoesEntityExist(veh) then
            SetEntityRoutingBucket(veh, dimention)

            table.insert(PlaneLocationTableData, {
                name = "HELICOPTER",
                coords = vector2(x, y),
                sprite = 43,
                color = 38,
                scale = 0.7
            })
        end
    end

    -- Sync blips to all players
    for _, playerId in pairs(allplayers) do
        TriggerClientEvent("Pug:client:CreateBlip", playerId, PlaneLocationTableData)
    end
end)



local function AwardAndEndMatch(winningSquadId)
    local winningSquad = squads[winningSquadId]
    if not winningSquad or not winningSquad.members then
        print("AwardAndEndMatch: No valid squad found with ID", winningSquadId)
        return
    end

    local squadSize = #winningSquad.members
    if squadSize <= 0 then
        print("AwardAndEndMatch: Squad has no members?")
        return
    end

    local sharePerPlayer = math.floor(cashprize / squadSize)

    -- Award the squad
    for _, playerSrc in ipairs(winningSquad.members) do
        local Player = GetUnifiedPlayer(playerSrc)
        if Player then
            FixAddMoneyForOx(Player, sharePerPlayer)

            PlayerWon(playerSrc)
            AwardPlacementXP(playerSrc, 1)
            TriggerClientEvent('Pug:client:BattleRoyaleNotify', playerSrc,
                ("Your squad WON the Royale! You earned $%d."):format(sharePerPlayer),
                'success', 10000
            )
        end
    end

    local anyWinningPlayer = winningSquad.members[1] 
    if anyWinningPlayer then
        local fName, lName = GetCharacterInfo(anyWinningPlayer)
        local winnerLabel = (fName and lName) and (fName .. " " .. lName .. "'s Squad") or "The last squad"
        for _, p in ipairs(allplayers) do
            TriggerClientEvent('Pug:client:BattleRoyaleNotify', p, (winnerLabel .. " has won the Royale!"))
        end
    end

    for _, p in ipairs(allplayers) do
        TriggerClientEvent('Pug:client:removeFromRoyale', p)
        SetPlayerRoutingBucket(p, 0)
    end

    GulagPlayers = {}
    syncedArmor = {}
    spectateplayers = {}
    allplayers = {}

    squads = {}
    teamMapping = {}

    gameStarted = false

    lives = 1
    everyone = 0
    cashprize = Config.MinWager
end


local function OnPlayerEliminated(playerSrc)
    local squadId = teamMapping[playerSrc]

    if not squadId or not squads[squadId] then return end

    squads[squadId].aliveCount = squads[squadId].aliveCount - 1
    if squads[squadId].aliveCount <= 0 then
        squads[squadId] = nil
    end

    local activeSquads, lastSquad = 0, nil
    for sId, sData in pairs(squads) do
        if sData.aliveCount and sData.aliveCount > 0 then
            activeSquads = activeSquads + 1
            lastSquad = sId
            if activeSquads > 1 then break end
        end
    end
    print(activeSquads,"activeSquads")
    if activeSquads == 1 then
        AwardAndEndMatch(lastSquad)
    elseif activeSquads == 0 then
        FullyResetEverything()
    end
    RemovePlayerFromSquad(playerSrc)
end



-- Drop logic
AddEventHandler('playerDropped', function(reason)
    local src = source
    if allplayers and #allplayers < 1 then return end

    local Player = nil
    if Framework == "QBCore" then
        Player = FWork.Players[src] 
    end

    local wasInMatch = false
    for i = #allplayers, 1, -1 do
        if allplayers[i] == src then
            table.remove(allplayers, i)
            everyone = everyone - 1
            wasInMatch = true
            break
        end
    end

    if not wasInMatch then
        return
    end

    local name = "A player"
    if Player and Player.PlayerData and Player.PlayerData.charinfo then
        local first = Player.PlayerData.charinfo.firstname
        local last  = Player.PlayerData.charinfo.lastname
        if first and last then
            name = first .. " " .. last
        end
    end

    for _, v in ipairs(allplayers) do
        TriggerClientEvent('Pug:client:BattleRoyaleNotify', v, (name .. ' lagged out!'), 'error')
        if gameStarted then
            TriggerClientEvent("Pug:client:PlayerKilledNotificationRoyale", v)
            TriggerClientEvent('Pug:UpdatePlayersLeft', v, everyone)
            AwardSurviveXP(v)
        end
    end

    for i = #spectateplayers, 1, -1 do
        if spectateplayers[i] == src then
            table.remove(spectateplayers, i)
        end
    end

    for i = #GulagPlayers, 1, -1 do
        if GulagPlayers[i] == src then
            table.remove(GulagPlayers, i)
            for _, v in ipairs(GulagPlayers) do
                TriggerClientEvent("Pug:client:RemoveFromGulags", v)
            end
        end
    end
    if gameStarted then
        print(maxSquadSize,"maxSquadSize")
        if maxSquadSize == 1 then
            if everyone == 3 then
                for _, playerId in ipairs(allplayers) do
                    local pObj = GetUnifiedPlayer(playerId)
                    if pObj then
                        local thirdPrize = round(cashprize * 0.35)
                        FixAddMoneyForOx(pObj, thirdPrize)
                        -- PlayerWon(playerId)
                        AwardPlacementXP(playerId, 3)
                        TriggerClientEvent('Pug:client:BattleRoyaleNotify', playerId,
                            'You placed 3rd and earned $' .. thirdPrize, 'success', 10000)
                    end
                end

            elseif everyone == 2 then
                for _, playerId in ipairs(allplayers) do
                    local pObj = GetUnifiedPlayer(playerId)
                    if pObj then
                        local secondPrize = round(cashprize * 0.50)
                        FixAddMoneyForOx(pObj, secondPrize)
                        -- PlayerWon(playerId)
                        AwardPlacementXP(playerId, 2)
                        TriggerClientEvent('Pug:client:BattleRoyaleNotify', playerId,
                            'You placed 2nd and earned $' .. secondPrize, 'success', 10000)
                    end
                end

            elseif everyone == 1 then
                local winnerId = allplayers[1]
                local winnerObj = GetUnifiedPlayer(winnerId)
                if winnerObj then
                    local firstPrize = round(cashprize * 0.85)
                    FixAddMoneyForOx(winnerObj, firstPrize)
                    PlayerWon(winnerId)
                    AwardPlacementXP(winnerId, 1)
                    TriggerClientEvent('Pug:client:BattleRoyaleNotify', winnerId,
                        'You won 1st place and earned $' .. firstPrize, 'success', 10000)
                    TriggerClientEvent('Pug:client:removeFromRoyale', winnerId)
                    SetPlayerRoutingBucket(winnerId, 0)
                end

                -- Full reset
                GulagPlayers = {}
                syncedArmor = {}
                spectateplayers = {}
                allplayers = {}
                gameStarted = false
                lives = 1
                everyone = 0
                return
            elseif everyone <= 0 then
                -- Everyone left the game
                GulagPlayers = {}
                syncedArmor = {}
                spectateplayers = {}
                allplayers = {}
                gameStarted = false
                lives = 1
                everyone = 0
                return
            end
        else
            OnPlayerEliminated(src)
        end
    end
end)



RegisterServerEvent('Pug:server:RemoveAllFromPlane',function()
	for _, v in pairs(allplayers) do
		TriggerClientEvent('Pug:client:RemoveAllFromPlane', v)
	end
end)

RegisterServerEvent('Pug:server:EnemyUAVEffectForAll', function()
    local src = source
    local mySquad = teamMapping[src] 

    for _, v in pairs(allplayers) do
        if v == src then
            TriggerClientEvent('Pug:client:PugSoundPlay', v, "uaventeringao", 0.3)
        else
            if teamMapping[v] ~= mySquad then
                TriggerClientEvent('Pug:client:PugSoundPlay', v, "enemyuav", 0.3)
            end
        end
    end

    local count = 0
    while count <= 10 do
        Wait(5000)
        count = count + 1

        for _, v in pairs(allplayers) do
            if v ~= src and teamMapping[v] ~= mySquad then
                local coords = GetEntityCoords(GetPlayerPed(v))
                TriggerClientEvent("Pug:client:AcivateUav", src, coords, v)
            end
        end
    end
end)


function isInTable(tbl, val)
    for _, v in ipairs(tbl) do
        if v == val then return true end
    end
    return false
end

RegisterServerEvent('Pug:server:JoinRoyale', function()
	local src = source
	local first, last = GetCharacterInfo(src)

	if (#allplayers) < Config.MaxPlayers then
		if not isInTable(allplayers, src) then
			AddPlayerToASquad(src)  -- place them in a squad

			table.insert(allplayers, src)
			table.insert(spectateplayers, src)

			TriggerClientEvent('Pug:client:joinedRoyale', src)
            TriggerClientEvent("Pug:client:StopDrawingPrompt", src)

            WaitingForAllPlayersToAccept = 0
            WaitingForPlayerToAccept = false

			local displayName = (first and last) and (first .. " " .. last) or "A player"
			for _, v in ipairs(allplayers) do
                TriggerClientEvent("Pug:client:StopDrawingPrompt", v)
				TriggerClientEvent('Pug:client:BattleRoyaleNotify', v, displayName .. ' joined the Royale!', 'success')
			end
		else
			TriggerClientEvent('Pug:client:BattleRoyaleNotify', src, 'You have been detected for cheating and reported....', 'error')
		end
	else
		TriggerClientEvent('Pug:client:BattleRoyaleNotify', src, 'The Royale is full', 'error')
	end
end)


RegisterServerEvent('Pug:SV:SetlivesOfPlayersRoyale',function(lifeNum)
	lives = lifeNum
	if lives == 0 then lives = 1 end
	for _, v in pairs(allplayers) do
		TriggerClientEvent('Pug:client:BattleRoyaleNotify', v, lives..' lives per player has been set!')
	end
end)

RegisterServerEvent('Pug:SV:SetRoyaleWagerAmount',function(WageNum)
	cashprize = WageNum
	local prize = cashprize * (#allplayers)
	for _, v in pairs(allplayers) do
		TriggerClientEvent('Pug:client:BattleRoyaleNotify', v, '$'..prize..' has been set for the prize pool!')
	end
end)

RegisterServerEvent('Pug:server:RoyaleLeave',function()
	local src = source
	local first, last = GetCharacterInfo(src)

	for k, v in pairs(allplayers) do
		if v == src then
			table.remove(allplayers, k)
			local displayName = (first and last) and (first .. " " .. last) or "A player"

	
			for _, plid in pairs(allplayers) do
				TriggerClientEvent('Pug:client:BattleRoyaleNotify', plid, displayName .. ' left the royale!', 'error')
                TriggerClientEvent("Pug:client:StopDrawingPrompt", plid)
				TriggerClientEvent("Pug:client:PlayerKilledNotificationRoyale", plid)
			end
			break
		end
	end
	
	RemovePlayerFromSquad(src)

	for k, v in pairs(spectateplayers) do
		if v == src then
			table.remove(spectateplayers,k)
			break
		end
	end

    WaitingForAllPlayersToAccept = 0
    WaitingForPlayerToAccept = false
    TriggerClientEvent("Pug:client:StopDrawingPrompt", src)

	TriggerClientEvent('Pug:client:removeFromRoyale',src)
	if (#allplayers) <= 0 then
		GulagPlayers = {}
		syncedArmor = {}
		spectateplayers = {}
		allplayers = {}
		gameStarted = false
		lives = 1
		everyone = 0
	end
end)

RegisterServerEvent('Pug:server:ReadyResponse', function(accepted)
    if accepted then
        WaitingForAllPlayersToAccept += 1
        for _, playerId in pairs(allplayers) do
            TriggerClientEvent('Pug:client:UpdateReadyCount', playerId, WaitingForAllPlayersToAccept)
        end
    end
end)

RegisterServerEvent('Pug:server:startroyale', function()
	local src = source
    if gameStarted or WaitingForPlayerToAccept then return end
	local CanStartGame
	local Whitelisted
	local _, _, PlayerCID = GetCharacterInfo(src)

    WaitingForAllPlayersToAccept = 0 -- reset in case it was set from earlier

    local requiredToStart = math.ceil(#allplayers * Config.ReadyUpRequirment)
    local waitTime = 30
    local waited = 0
    WaitingForPlayerToAccept = true

    for _, playerId in pairs(allplayers) do
        TriggerClientEvent('Pug:client:PromptReady', playerId, requiredToStart)
    end

    while WaitingForAllPlayersToAccept < requiredToStart and waited < waitTime do
        Wait(1000)
        waited += 1
    end

    WaitingForPlayerToAccept = false

    if WaitingForAllPlayersToAccept < requiredToStart then
        WaitingForAllPlayersToAccept = 0
        return
    else
        WaitingForAllPlayersToAccept = 0
    end

	for _, v in pairs(Config.WhitelistedCIDsToStartGame) do
		if PlayerCID and string.upper(PlayerCID) == string.upper(v) then
			Whitelisted = true
			CanStartGame = true
			break
		end
	end
	

	if Whitelisted and not CanStartGame then
		TriggerClientEvent('Pug:client:BattleRoyaleNotify', src, 'You are not whitelisted to start a game.')
		return
	end

	if Config.RequireAdminToStartGane and not PlayerHasPermission(src, "admin") then
		TriggerClientEvent('Pug:client:BattleRoyaleNotify', src, 'You need to be an admin to start the game', 'error')
		return
	end

	if #allplayers < Config.MinPlayers then
		TriggerClientEvent('Pug:client:BattleRoyaleNotify', src, 'Needs to be at least '..Config.MinPlayers..' players to start', 'error')
		return
	end

	local allHaveMoney = true
	for _, v in pairs(allplayers) do
		local Player = GetUnifiedPlayer(v)
		local cash = Player and GetPlayerCash(Player) or 0
	
		if cash < cashprize then
			allHaveMoney = false
			TriggerClientEvent('Pug:client:BattleRoyaleNotify', v, 'You do not have enough cash ($' .. cashprize .. ') to join the Royale.', 'error')
		end
	end
	

	if not allHaveMoney then
		TriggerClientEvent('Pug:client:BattleRoyaleNotify', src, 'Not all players have enough money.', 'error')
		return
	end

	dimention = math.random(400,999)
	GulagMap = Config.GulagMap[math.random(1,#Config.GulagMap)]
	local plnspawn = math.random(1, #Config.PlaneSpawns[ChosenMap])
	everyone = #allplayers
	gameStarted = true
	gameHASStarted = true
    
    local AllLootData = GenerateLootData()
    
	for _, v in pairs(allplayers) do
		local Player = GetUnifiedPlayer(v)
		if Player then
			RemoveMoneyFromPlayer(Player, cashprize)
			TriggerClientEvent('Pug:client:BeginRoyaleMatch', v, lives, plnspawn, everyone, os.time(), Config.BeginningTimeInSeconds, CoolDownTime, GulagMap, ChosenMap, AllLootData)

			SetPlayerRoutingBucket(v, dimention)
		end
	end
	
	cashprize = cashprize * #allplayers

	Wait(500)

	TriggerEvent("Pug:server:StarGulagLoop")
	TriggerEvent("Pug:server:BeginRoyaleMatchTimer")
	TriggerClientEvent('Pug:client:RoyalePlaneBegin', src, plnspawn)
end)

RegisterServerEvent('Pug:SV:NotifyLivesLeftRoyale', function(lifeLeft)
	local src = source
	local first, last = GetCharacterInfo(src)
	local name = (first and last) and (first .. " " .. last) or "A player"

	for _, v in pairs(allplayers) do
		TriggerClientEvent('Pug:client:BattleRoyaleNotify', v, name .. ' has ' .. lifeLeft .. ' lives left')
		TriggerClientEvent("Pug:client:PlayerKilledNotificationRoyale", v)
	end
end)


local function round(x)
    return x >= 0 and math.floor(x + 0.5) or math.ceil(x - 0.5)
end

local function CountActiveSquads()
    local activeCount = 0
    local lastSquadId = nil
    for sId, sData in pairs(squads) do
        if sData.aliveCount and sData.aliveCount > 0 then
            activeCount = activeCount + 1
            lastSquadId = sId
        end
    end
    return activeCount, lastSquadId
end


function EliminateSquad(squadId)
    local dyingSquad = squads[squadId]
    if not dyingSquad then return end

    -- Determine placement *before* we remove them
    local activeBefore = 0
    for _, sData in pairs(squads) do
        if sData.aliveCount and sData.aliveCount > 0 then
            activeBefore = activeBefore + 1
        end
    end

    -- Award 3rd / 2nd place cash & XP if applicable
    if activeBefore == 3 then
        AwardSquadPlacement(dyingSquad, 3)
    elseif activeBefore == 2 then
        AwardSquadPlacement(dyingSquad, 2)
    else
        for _, pSrc in ipairs(dyingSquad.members) do
            PlayerLost(pSrc)
        end
    end

    -- Remove members from global player lists
    for _, pSrc in ipairs(dyingSquad.members) do
        -- From allplayers
        for i = #allplayers, 1, -1 do
            if allplayers[i] == pSrc then
                table.remove(allplayers, i)
                everyone = everyone - 1
                break
            end
        end
        -- From spectate list
        for i = #spectateplayers, 1, -1 do
            if spectateplayers[i] == pSrc then
                table.remove(spectateplayers, i)
                break
            end
        end
        -- Clean routing bucket & UI
        TriggerClientEvent('Pug:client:removeFromRoyale', pSrc)
        SetPlayerRoutingBucket(pSrc, 0)
    end

    -- Finally delete the squad
    squads[squadId] = nil

    -- Check if only one squad remains → they win
    local leftCount, lastSquad = CountActiveSquads()
    if leftCount == 1 then
        AwardAndEndMatch(lastSquad)
    elseif leftCount <= 0 then
        FullyResetEverything()
    end
end

function AwardSquadPlacement(squadData, placeNumber)
    if not squadData or not squadData.members then return end

    local placeLabel = (placeNumber == 3 and "3rd")
                   or (placeNumber == 2 and "2nd")
                   or (tostring(placeNumber) .. "th")

    local portion = 0
    if placeNumber == 3 then
        portion = round(cashprize * 0.35)  -- 35% for 3rd
    elseif placeNumber == 2 then
        portion = round(cashprize * 0.50)  -- 50% for 2nd
    end

    local squadSize = #squadData.members
    if squadSize > 0 and portion > 0 then
        local sharePer = math.floor(portion / squadSize)
        for _, src in ipairs(squadData.members) do
            local ply = GetUnifiedPlayer(src)
            if ply then
                FixAddMoneyForOx(ply, sharePer)
                PlayerWon(src)
                AwardPlacementXP(src, placeNumber)

                TriggerClientEvent('Pug:client:BattleRoyaleNotify', src,
                    ("Your squad placed %s and earned $%d!"):format(placeLabel, sharePer),
                    'success', 10000
                )
            end
        end
    else
        for _, src in ipairs(squadData.members) do
            PlayerLost(src)
        end
    end
end

function AwardAndEndMatch(winningSquadId)
    local winningSquad = squads[winningSquadId]
    if not winningSquad or not winningSquad.members then return end

    local squadSize = #winningSquad.members
    if squadSize <= 0 then return end

    local portion = round(cashprize * 0.85)
    local sharePerPlayer = math.floor(portion / squadSize)

    for _, playerSrc in ipairs(winningSquad.members) do
        local Player = GetUnifiedPlayer(playerSrc)
        if Player then
            FixAddMoneyForOx(Player, sharePerPlayer)
            PlayerWon(playerSrc)
            AwardPlacementXP(playerSrc, 1)

            TriggerClientEvent('Pug:client:BattleRoyaleNotify', playerSrc,
                ("Your squad WON 1st place! You earned $%d."):format(sharePerPlayer),
                'success', 10000
            )
        end
    end

    local anyPlayer = winningSquad.members[1]
    if anyPlayer then
        local fName, lName = GetCharacterInfo(anyPlayer)
        local label = (fName and lName) and (fName .. " " .. lName .. "'s Squad") or "A squad"
        for _, p in ipairs(allplayers) do
            TriggerClientEvent('Pug:client:BattleRoyaleNotify', p, (label .. " won the Royale!"))
        end
    end

    for _, p in ipairs(allplayers) do
        TriggerClientEvent('Pug:client:removeFromRoyale', p)
        SetPlayerRoutingBucket(p, 0)
    end

    -- Reset squads, etc.
    squads = {}
    teamMapping = {}
    FullyResetEverything()
end


RegisterServerEvent('Pug:client:RemoveRoyalePlayer', function()
    local src = source
    local Player = GetUnifiedPlayer(src)

    if maxSquadSize == 1 then
        for i = #allplayers, 1, -1 do
            if allplayers[i] == src then
                table.remove(allplayers, i)
                everyone = everyone - 1
                break
            end
        end
        RemovePlayerFromSquad(src)

        for i = #spectateplayers, 1, -1 do
            if spectateplayers[i] == src then
                table.remove(spectateplayers, i)
                break
            end
        end

        TriggerClientEvent('Pug:client:removeFromRoyale', src)
        SetPlayerRoutingBucket(src, 0)

        local fName, lName = GetCharacterInfo(src)
        local label = (fName and lName) and (fName .. " " .. lName) or "A player"
        for _, v in ipairs(allplayers) do
            TriggerClientEvent('Pug:client:BattleRoyaleNotify', v, label .. " has been eliminated!")
            TriggerClientEvent('Pug:UpdatePlayersLeft', v, everyone)
            AwardSurviveXP(v)
        end

        if everyone == 3 and Config.AwaydSecondAndThirdPlace then
            local prize = round(cashprize * 0.35)
            FixAddMoneyForOx(Player, prize)
            -- PlayerWon(src)
            AwardPlacementXP(src, 3)
            TriggerClientEvent('Pug:client:BattleRoyaleNotify', src, 'You placed 3rd and got $'..prize, 'success', 10000)
        elseif everyone == 2  then
            local prize = round(cashprize * 0.50)
            FixAddMoneyForOx(Player, prize)
            -- PlayerWon(src)
            AwardPlacementXP(src, 2)
            TriggerClientEvent('Pug:client:BattleRoyaleNotify', src, 'You placed 2nd and got $'..prize, 'success', 10000)
        elseif everyone == 1 then
            Wait(250)
            local winnerSrc = allplayers[1]
            local finalPly = GetUnifiedPlayer(winnerSrc)
            if finalPly then
                local prize = round(cashprize * 0.85)
                FixAddMoneyForOx(finalPly, prize)
                PlayerWon(winnerSrc)
                AwardPlacementXP(winnerSrc, 1)
                TriggerClientEvent('Pug:client:BattleRoyaleNotify', winnerSrc, 'You won 1st place and earned $'..prize, 'success', 10000)
                TriggerClientEvent('Pug:client:removeFromRoyale', winnerSrc)
                SetPlayerRoutingBucket(winnerSrc, 0)
            end

            GulagPlayers = {}
            syncedArmor = {}
            spectateplayers = {}
            allplayers = {}
            gameStarted = false
            lives = 1
            everyone = 0
            return
        elseif everyone <= 0 then
            GulagPlayers = {}
            syncedArmor = {}
            spectateplayers = {}
            allplayers = {}
            gameStarted = false
            lives = 1
            everyone = 0
            return
        else
            PlayerLost(src)
        end
    else
        OnPlayerEliminated(src)

        for i = #allplayers, 1, -1 do
            if allplayers[i] == src then
                table.remove(allplayers, i)
                everyone = everyone - 1
                break
            end
        end
        print(everyone,"everyone 2")

        for i = #spectateplayers, 1, -1 do
            if spectateplayers[i] == src then
                table.remove(spectateplayers, i)
                break
            end
        end

        TriggerClientEvent('Pug:client:removeFromRoyale', src)
        SetPlayerRoutingBucket(src, 0)

        RemovePlayerFromSquad(src)

        local fName, lName = GetCharacterInfo(src)
        local label = (fName and lName) and (fName .. " " .. lName) or "A player"
        for _, v in ipairs(allplayers) do
            TriggerClientEvent('Pug:client:BattleRoyaleNotify', v, label .. " has been eliminated!")
            TriggerClientEvent('Pug:UpdatePlayersLeft', v, everyone)
        end
    end
end)




Config.FrameworkFunctions.CreateCallback('Pug:GetGulagAvailabilityNumber', function(source, cb)
	local info = {
		allPeople = everyone,
		gulagpeople = (#GulagPlayers),
	}
	cb(info)
end)

Config.FrameworkFunctions.CreateCallback("Pug:Leaderboard:GetData", function(source, cb, filterMode)
    local maxSquadSize = tonumber(filterMode) or 1

    if filterMode == "personal" then
        local src = source
		local _, _, citizenid = GetCharacterInfo(src)
		if not citizenid then return cb({}) end

        MySQL.query([[
            SELECT citizenid, name, kills, deaths, wins, losses, type
            FROM pug_battleroyale_stats
            ORDER BY type ASC, wins DESC, kills DESC
        ]], {}, function(allResults)
            if not allResults or #allResults == 0 then
                print("No leaderboard data found.")
                return cb({})
            end

            local groupedByType = {}
            for _, row in ipairs(allResults) do
                local t = row.type
                groupedByType[t] = groupedByType[t] or {}
                table.insert(groupedByType[t], row)
            end

            MySQL.query([[
                SELECT name, kills, deaths, wins, losses, type
                FROM pug_battleroyale_stats
                WHERE citizenid = ?
            ]], { citizenid }, function(playerRows)
                if not playerRows or #playerRows == 0 then
                    print("No personal stats found for: " .. citizenid)
                    return cb({})
                end

                local formatted = {}

                for _, row in ipairs(playerRows) do
                    local typeGroup = groupedByType[row.type] or {}
                    local rank = "-"

                    for i, stat in ipairs(typeGroup) do
                        if stat.citizenid == citizenid then
                            rank = i
                            break
                        end
                    end

                    table.insert(formatted, {
                        rank = rank,
                        name = row.name or "Unknown",
                        kills = row.kills or 0,
                        deaths = row.deaths or 0,
                        wins = row.wins or 0,
                        losses = row.losses or 0,
                        type = row.type or 1
                    })
                end

                cb(formatted)
            end)
        end)
        return
    end

	MySQL.query([[
		SELECT name, kills, deaths, wins, losses, type
		FROM pug_battleroyale_stats
		WHERE type = ?
		ORDER BY wins DESC, kills DESC
		LIMIT 15
	]], { maxSquadSize }, function(results)
		local formatted = {}
		for i, row in ipairs(results or {}) do
			table.insert(formatted, {
				rank = i,
				name = row.name or "Unknown",
				kills = row.kills or 0,
				deaths = row.deaths or 0,
				wins = row.wins or 0,
				losses = row.losses or 0,
				type = row.type or maxSquadSize
			})
		end
		cb(formatted)
	end)	
end)



local zonePhases = {}
local function pickNextCenter(oldCenter, oldRadius, newRadius)
    local maxDist = oldRadius - newRadius
    if maxDist < 0 then
        maxDist = 0
    end

    local angle = math.random() * math.pi * 2
    local dist  = math.random() * maxDist

    local offsetX = dist * math.cos(angle)
    local offsetY = dist * math.sin(angle)

    return vector3(oldCenter.x + offsetX, oldCenter.y + offsetY, oldCenter.z)
end

RegisterNetEvent("Pug:server:BeginRoyaleMatchTimer", function()
    zonePhases = {
        { waitTime = Config.BeginningTimeInSeconds, radius = Config.MapsCircleShrinkData[ChosenMap].Circle1.Radius, shrinkRate = 0.0, isCooldown = true },
        { waitTime = CoolDownTime,                 radius = Config.MapsCircleShrinkData[ChosenMap].Circle1.Radius, shrinkRate = 0.0, isCooldown = true },
        { waitTime = Config.MapsCircleShrinkData[ChosenMap].Circle1.ShrinkTime,   radius = Config.MapsCircleShrinkData[ChosenMap].Circle1.Radius, shrinkRate = Config.MapsCircleShrinkData[ChosenMap].Circle1.ShrinkSpeed, isCooldown = false },
        { waitTime = CoolDownTime,                 radius = Config.MapsCircleShrinkData[ChosenMap].Circle2.Radius,  shrinkRate = 0.0, isCooldown = true },
        { waitTime = Config.MapsCircleShrinkData[ChosenMap].Circle2.ShrinkTime,  radius = Config.MapsCircleShrinkData[ChosenMap].Circle2.Radius,  shrinkRate = Config.MapsCircleShrinkData[ChosenMap].Circle2.ShrinkSpeed, isCooldown = false },
        { waitTime = CoolDownTime,                 radius = Config.MapsCircleShrinkData[ChosenMap].Circle3.Radius,  shrinkRate = 0.0, isCooldown = true },
        { waitTime = Config.MapsCircleShrinkData[ChosenMap].Circle3.ShrinkTime,  radius = Config.MapsCircleShrinkData[ChosenMap].Circle3.Radius,  shrinkRate = Config.MapsCircleShrinkData[ChosenMap].Circle3.ShrinkSpeed, isCooldown = false },
        { waitTime = CoolDownTime,                 radius = Config.MapsCircleShrinkData[ChosenMap].Circle4.Radius,  shrinkRate = 0.0, isCooldown = true },
        { waitTime = Config.MapsCircleShrinkData[ChosenMap].Circle4.ShrinkTime,  radius = Config.MapsCircleShrinkData[ChosenMap].Circle4.Radius,  shrinkRate = Config.MapsCircleShrinkData[ChosenMap].Circle4.ShrinkSpeed, isCooldown = false },
        { waitTime = CoolDownTime,                 radius = 35.0,   shrinkRate = 0.0, isCooldown = true },
		{ waitTime = Config.MapsCircleShrinkData[ChosenMap].Circle4.ShrinkTime,  radius = 35.0,   shrinkRate = 1.0, isCooldown = false },
		{ waitTime = CoolDownTime,                 radius = 0.0,    shrinkRate = 0.0, isCooldown = true },
    }

    if not gameStarted then return end

    local rndIndex = math.random(1, #Config.ZoneOptions[ChosenMap])
    local firstPos = Config.ZoneOptions[ChosenMap][rndIndex]

    local waveCenters = {}
    waveCenters[1] = firstPos

    local finalRadiusOfPrevious = nil

    for i = 1, #zonePhases do
        local phase = zonePhases[i]
        if finalRadiusOfPrevious then
            phase.radius = finalRadiusOfPrevious
        end

        if i > 1 then
            local oldCenter      = waveCenters[i-1]
            local oldFinalRadius = zonePhases[i-1].radius
            local newRadius      = phase.radius
            waveCenters[i]       = pickNextCenter(oldCenter, oldFinalRadius, newRadius)
        end

        if not waveCenters[i] then
            waveCenters[i] = firstPos
        end

        local endRadius = phase.radius
        if phase.shrinkRate > 0 then
            local possibleShrink = phase.shrinkRate * phase.waitTime
            endRadius = math.max(0, phase.radius - possibleShrink)
        end
        finalRadiusOfPrevious = endRadius
    end

    CreateThread(function()
        for i, phase in ipairs(zonePhases) do
            if not gameStarted then
                gameHASStarted = false
                break
            end

            local fromCenter = waveCenters[i]
            local fromRadius = phase.radius
            local waveTime   = phase.waitTime

            local toCenter, toRadius
            if i < #zonePhases then
                toCenter = waveCenters[i+1]
                toRadius = zonePhases[i+1].radius
            else
                toCenter = fromCenter
                toRadius = fromRadius
            end

            local futureCenter, futureRadius
            if i < (#zonePhases - 1) then
                futureCenter = waveCenters[i+2]
                futureRadius = zonePhases[i+2].radius
            end

            for _, playerId in ipairs(allplayers) do
                TriggerClientEvent("Pug:client:UpdateTimeLeftRoyale", playerId, os.time(), waveTime)

				if i ~= 1 then
					TriggerClientEvent("Pug:client:SetZoneData", playerId, {
						waveTime      = waveTime,
						fromCenter    = { x=fromCenter.x,   y=fromCenter.y,   z=fromCenter.z },
						fromRadius    = fromRadius,
						toCenter      = { x=toCenter.x,     y=toCenter.y,     z=toCenter.z },
						toRadius      = toRadius,
						isCooldown    = phase.isCooldown,

						futureCenter  = (futureCenter and { x=futureCenter.x, y=futureCenter.y, z=futureCenter.z }) or nil,
						futureRadius  = futureRadius or 0
					})
				end
            end

            Wait(waveTime * 1000)
        end
        gameHASStarted = false
    end)
end)