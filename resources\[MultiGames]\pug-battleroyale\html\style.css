@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@200&display=swap');

body {
    margin: 0;
    padding: 0;
}

:root {
    --main-font-size: 1.4vh;  

    /* Dark, semi‐transparent background */
    --primary-color: rgba(0, 0, 0, 0.3);

    /* Slightly lighter translucent panels */
    --secondary-color: rgba(0, 0, 0, 0.3);

    /* Another mid‐range translucent black */
    --tertiary-color: rgba(0, 0, 0, 0.6);

    /* Background color for overlays, popups, etc. */
    --background-color: rgba(0, 0, 0, 0.4);

    /* Accent color: bright blue for highlight */
    --accent-color: #ffffff79;

    /* Lighter black shade for smaller elements */
    --light-secondary-color: rgba(255, 255, 255, 0.07);

    /* Text color: mostly white */
    --text-color: #ffffff;

    /* Icon color: subtle white-ish */
    --icon-color: rgba(255, 255, 255, 0.4);

    /* Text-area color or subtle background for text boxes */
    --tex-area-color: rgba(255, 255, 255, 0.15);

    /* Icon hover color: a bit brighter */
    --icon-color-hover: rgba(255, 255, 255, 0.6);

    /* Placeholder text color: faint white/gray */
    --placeholder-text-color: rgba(255, 255, 255, 0.4);

    /* Panels or tooltips color: mid-range black */
    --panel-color: rgba(0, 0, 0, 0.5);
}

.weapon-icon {
    width: 72px;
    height: 72px;
    object-fit: contain; /* or 'cover', whichever you prefer */
}

#weapon-list li {
    position: absolute;
    width: 50px;      /* reduce from 75px */
    height: 50px;     /* reduce from 75px */
    line-height: 50px;
    text-align: center;
    transform-origin: center -1px;
    font-family: "Poppins", sans-serif;
    font-size: 12px;  /* or smaller text to match */
    font-weight: bold;
    color: var(--text-color);
    background: var(--secondary-color);
    border: 1px solid var(--icon-color);
    border-radius: 50%;
    transition: transform 0.3s, background 0.3s, color 0.3s;
}



.container2 {
    height: 100vh;
    opacity: 1.0;
}


.blackbg2 {
    display: none;
}

#royale-text2 {
    word-spacing: 0.5vh;
    white-space: nowrap;
    text-align: center;
    position: absolute;
    left: 86.8vh;
    bottom: 9.85vh;
    height: 2vh;
    width: 6.5vh;
    font-family: "Inter", sans-serif;
    font-size: var(--main-font-size);
    font-weight: bold;
    color: white;
    /* text-shadow: 1px 1px 0px #000000; */
    background-color: #00000075;
    /* display: inline-block; */
    right: 10vh;
	display: flex;
	align-items: center;
	justify-content: center;
}
#royale-char2 {
    animation: pulse 2s infinite;
    word-spacing: 0.5vh;
    white-space: nowrap;
    text-align: center;
    position: absolute;
    left: 85.45vh;
    bottom: 10.2vh;
    font-family: sans-serif;
    font-size: 1.1vh;
    font-weight: bold;
    color: rgb(0, 0, 0);
    text-shadow: 1px 1px 0px #00000075;
    right: 10vh;
}

.background8 {
    position: absolute;
    height: 2vh;
    width: 2.0vh;
    left: 84.8vh;
    bottom: 9.85vh;
    background-color: #00000075;
    display: inline-block;
    border-radius: -6vh;
}
.background9 {
    animation: pulse 2s infinite;
    position: absolute;
    height: 1.6vh;
    width: 1.6vh;
    left: 85.0vh;
    bottom: 9.999vh;
    background-color: #ffffffa6;
    border-radius: -8vh;
    display: inline-block;
}

@keyframes pulse {
    0% {
        transform: scale(0.9);
    }

    70% {
        transform: scale(1.0);
    }

    100% {
        transform: scale(0.9);
    }
}
/* PICKUP ITEMS UI */
.blackbg {
    display: none;
}

#royale-text {
    /* word-spacing: 0.5vh; */
    white-space: nowrap;
    text-align: center;
    position: absolute;
    left: 86.8vh;
    bottom: 9.85vh;
    height: 2vh;
    width: 11vh;
    font-family: "Inter", sans-serif;
    font-size: var(--main-font-size);
    font-weight: bold;
    color: white;
    /* text-shadow: 1px 1px 0px #000000; */
    background-color: #00000075;
    /* display: inline-block; */
    right: 10vh;
	display: flex;
	align-items: center;
	justify-content: center;
}
#royale-char {
    animation: pulse 2s infinite;
    word-spacing: 0.5vh;
    white-space: nowrap;
    text-align: center;
    position: absolute;
    left: 85.45vh;
    bottom: 10.2vh;
    font-family: sans-serif;
    font-size: 1.1vh;
    font-weight: bold;
    color: rgb(0, 0, 0);
    text-shadow: 1px 1px 0px #00000075;
    right: 10vh;
}

.background6 {
    position: absolute;
    height: 2vh;
    width: 2.0vh;
    left: 84.8vh;
    bottom: 9.85vh;
    background-color: #00000075;
    display: inline-block;
    border-radius: -6vh;
}
.background7 {
    animation: pulse 2s infinite;
    position: absolute;
    height: 1.6vh;
    width: 1.6vh;
    left: 85.0vh;
    bottom: 9.999vh;
    background-color: #ffffffa6;
    border-radius: -8vh;
    display: inline-block;
}
/* END */

.container {
    height: 100vh;
    opacity: 1.0;
}

.ui {
    position: absolute;
    width: 30vh;
    height: 0vh;
    bottom: 24.0vh;
    border-radius: 0vh;
    left: 4.5vh;
}

.background {
    border: thick double #00000000;
    position: absolute;
    display: none;
    top: -16.0vh;
    bottom: 0.9vh;
    left: 6.3vh;
    right: 6.3vh;
    background-color: #00000000;
}

/* CIRCLE BACKGROUNDS */
.background2 {
    position: absolute;
    top: 12.9vh;
    bottom: 0.0vh;
    height: 2vh;
    width: 2vh;
    left: -2.98vh;
    background-color: #00000069;
    border-radius: 2vh;
    display: inline-block;
}
.background3 {
    position: absolute;
    top: 12.9vh;
    bottom: 0.0vh;
    height: 2vh;
    width: 2vh;
    left: 3.0vh;
    background-color: #00000069;
    border-radius: 2vh;
    display: inline-block;
}
/* END */
/* BACKGROUND OF PLAYERS LEFT 1 */
.background4 {
    position: absolute;
    top: 12.9vh;
    bottom: 0.0vh;
    height: 2vh;
    width: 4.3vh;
    left: 7.0vh;
    background-color: #00000069;
    display: inline-block;
}

/* BACKGROUND OF PLAYERS LEFT 2 */
.background5 {
    position: absolute;
    top: 12.9vh;
    bottom: 0.0vh;
    height: 2vh;
    width: 9.0vh;
    left: 7.0vh;
    background-color: #00000069;
    display: inline-block;
}

/* Royale */
.royale {
    display: none;
}

#royale-royalename {
    text-align: center;
    white-space: nowrap;
    position: absolute;
    left: 0vh;
    top: -15vh;
    font-family: sans-serif;
    font-size: var(--main-font-size);
    font-weight: bold;
    color: white;
    text-shadow: 1px 1px 0px #000000;
    right: 0vh;
}

#royale-firstplace {
    white-space: nowrap;
    text-align: center;
    position: absolute;
    left: 0vh;
    right: 0vh;
    top: -13vh;
    font-family: sans-serif;
    font-size: var(--main-font-size);
    font-weight: bold;
    color: white;
    text-shadow: 1px 1px 0px #000000;
}

#royale-secondplace {
    white-space: nowrap;
    text-align: center;
    position: absolute;
    left: 0vh;
    top: -11vh;
    font-family: sans-serif;
    font-size: var(--main-font-size);
    font-weight: bold;
    color: white;
    text-shadow: 1px 1px 0px #000000;
    right: 0vh;
}

#royale-thirdplace {
    white-space: nowrap;
    text-align: center;
    position: absolute;
    left: 0vh;
    top: -9vh;
    font-family: sans-serif;
    font-size: var(--main-font-size);
    font-weight: bold;
    color: white;
    text-shadow: 1px 1px 0px #000000;
    right: 0vh;
}

#royale-position {
    word-spacing: 0.3vh;
    white-space: nowrap;
    text-align: center;
    position: absolute;
    left: 14.0vh;
    top: -2.5vh;
    font-family: sans-serif;
    font-size: var(--main-font-size);
    font-weight: bold;
    color: white;
    text-shadow: 1px 1px 0px #000000;
    right: 10vh;
}

#royale-points {
    white-space: nowrap;
    text-align: center;
    position: absolute;
    left: 5.2vh;
    top: -2.45vh;
    font-family: sans-serif;
    font-size: var(--main-font-size);
    font-weight: bold;
    color: white;
    text-shadow: 1px 1px 0px #000000;
    right: 10vh;
}

#royale-time {
    word-spacing: 0.5vh;
    white-space: nowrap;
    text-align: center;
    position: absolute;
    left: -7.0vh;
    top: -2.8vh;
    font-family: sans-serif;
    font-size: var(--main-font-size);
    font-weight: bold;
    color: white;
    text-shadow: 1px 1px 0px #000000;
    right: 10vh;
}

#royale-skull {
    white-space: nowrap;
    text-align: center;
    position: absolute;
    left: 1.32vh;
    top: -2.8vh;
    font-family: sans-serif;
    font-size: var(--main-font-size);
    font-weight: bold;
    color: white;
    text-shadow: 1px 1px 0px #000000;
    right: 10vh;
}



#custom-weapon-wheel {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 600px; /* Larger size for proper distribution */
    height: 600px;
    border-radius: 50%;
    background: var(--primary-color);
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    overflow: hidden;
    border: 2px solid var(--accent-color);
}

/* Weapon Items */
#weapon-list {
    position: relative;
    width: 100%;
    height: 100%;
    list-style-type: none;
    padding: 0;
    margin: 0;
}

#weapon-list li {
    position: absolute;
    width: 75px;
    height: 75px;
    line-height: 75px;
    text-align: center;
    transform-origin: center -1px; /* Adjust for larger circle */
    font-family: "Poppins", sans-serif;
    font-size: 14px;
    font-weight: bold;
    color: var(--text-color);
    background: var(--secondary-color);
    border: 1px solid var(--icon-color);
    border-radius: 50%;
    transition: transform 0.3s, background 0.3s, color 0.3s;
}

#weapon-list li:hover {
    background: var(--icon-color-hover);
    color: var(--accent-color);
}

#weapon-list li.selected {
    background: var(--accent-color);
    color: var(--text-color);
    transform: scale(1.2);
    border-color: var(--text-color);
}

/* Central Weapon Display */
#weapon-display {
    position: absolute;
    bottom: 230px;
    font-family: "Poppins", sans-serif;
    font-size: 1.8rem;
    color: var(--text-color);
    text-align: center;
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

/* Optional: Add a glowing effect for the selected weapon */
#weapon-list li.selected {
    box-shadow: 0 0 10px var(--accent-color);
}

/* Tooltip for Weapon Display */
#weapon-display-tooltip {
    position: absolute;
    bottom: 100px;
    font-family: "Poppins", sans-serif;
    font-size: 1.2rem;
    color: var(--placeholder-text-color);
    text-align: center;
    background: var(--panel-color);
    padding: 5px 10px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}