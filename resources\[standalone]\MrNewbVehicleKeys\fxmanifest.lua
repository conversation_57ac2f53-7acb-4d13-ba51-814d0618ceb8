fx_version 'cerulean'
game 'gta5'
lua54 'yes'
name "<PERSON><PERSON><PERSON>bVehicle<PERSON>eys"
author "<PERSON><PERSON><PERSON><PERSON>"
version "2.2.0"

shared_scripts {
	'@ox_lib/init.lua',
	'src/shared/config.lua',
	'src/shared/FobOptions.lua',
	'src/shared/ParkingSpots.lua',
	'src/shared/init.lua',
}

client_scripts {
	'src/client/**/*.lua',
}

server_scripts {
	'@oxmysql/lib/MySQL.lua',
	'src/server/**/*.lua',
}

ui_page 'web/build/index.html'

files {
	'locales/*.json',
	'web/build/index.html',
	'web/build/**/*',
}

dependencies {
    '/onesync',
    'ox_lib',
	'community_bridge',
    'oxmysql',
}

escrow_ignore {
	'src/shared/*.lua',     	-- Config files
	'src/client/open/*.lua',   -- open files
	'src/server/open/*.lua',   -- open files
}

dependency '/assetpacks'