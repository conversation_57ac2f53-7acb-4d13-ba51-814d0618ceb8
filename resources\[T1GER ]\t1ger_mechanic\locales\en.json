{"notification": {"no_mechanic_shops": "No mechanic shops available.", "no_online_players": "No players are currently online.", "input_amount_higher_0": "Enter an amount greater than zero.", "shop_created": "Created Shop: '%s' with job: '%s'", "owner_removed": "You removed the current owner from the shop.", "owner_assigned": "New owner '%s' assigned to shop '%s'.", "updated_shop_account": "Shop's account balance updated to: %s", "shop_deleted": "Shop '%s' [%s] has been deleted.", "min_sale_price": "Minimum sale price is 0.", "marker_created": "Created Marker: '%s' with name: '%s'", "marker_updated": "Updated Marker: '%s' with markerId: '%s'", "marker_deleted": "Deleted Marker: '%s' with markerId: '%s'", "account_withdraw_max": "You cannot withdraw more money than what's in the balance!", "not_enough_money": "You do not have enough money on you.", "not_enough_bank_money": "You do not have enough money in your bank account.", "account_deposited": "You deposited $%s into the account", "account_withdrew": "You withdrew $%s from the account", "no_players_nearby": "No players are currently nearby.", "player_already_hired_shop": "The player is already hired in the shop: '%s'", "employee_recruitment_sent": "You sent a recruitment offer to [%s] %s. Await for their response.", "player_declined_recruitment": "The player '%s' has declined your recruitment offer", "player_accepted_recruitment": "The player '%s' has accepted your recruitment offer", "you_have_been_hired": "You have been hired in the shop: '%s'", "no_hired_employees": "There are currently no hired employees in this shop.", "cannot_fire_owner": "You cannot fire the owner of the shop, dumbass!", "you_fired_employee": "You have fired employee '%s' from the shop.", "cannot_update_owner": "You cannot promote/demote the owner of the shop, idiot!", "you_updated_employee": "You have updated employee '%s' to %s", "must_be_inside_vehicle": "You must be inside a vehicle!", "vehicle_not_owned": "The vehicle plate is not registered anywhere!", "no_owned_vehicles": "You have no owned vehicles", "garage_no_vehicles": "You have no parked vehicles in this garage.", "you_clocked_on_duty": "You clocked on-duty", "you_clocked_off_duty": "You clocked off-duty", "workbench_missing_materials": "Missing Materials:", "you_crafted_item": "You successfully crafted %s pcs. of %s.", "shop_account_insufficient_balance": "Not enough money in the shop account!", "order_delivered_to_storage": "Your order has been delivered to the storage.", "supplier_order_success": "Order confirmation of %s for: %s x %s.", "no_towtruck_nearby": "You must be inside or near a flatbed/towtruck!", "towtruck_ready_to_control": "Head to the back of the towtruck to attach or detach.", "towtruck_park_vehicle_in_marker": "You must ensure the vehicle is parked inside the marker", "towing_vehicle_blacklisted": "The vehicle is blacklisted from towing!", "have_ongoing_mission": "You already have an ongoing mission. Complete or cancel it!", "no_missions_available": "There are no missions available currently. Please try again later!", "breakdown_vehicle_not_attached": "The NPC vehicle must be attached to a flatbed/towtruck!", "breakdown_invalid_towtruck": "Vehicle must be attached to a valid towtruck", "breakdown_passenger_seat_occupied": "The passenger seat in the towtruck is occupied!", "breakdown_head_to_dropoff": "Drop off the vehicle and the NPC at the updated GPS location.", "roadsiderepair_cannot_pay": "I cannot pay you before you've completed your work!", "roadsiderepair_fixed": "Task is done. Collect payment from the NPC", "carscrapping_head_to_dropoff": "Transport the vehicle to the scrapyard for salvaging.", "carscrapping_pickup_vehicle": "Attach the vehicle to your tow truck to begin transport.", "carscrapping_veh_not_in_zone": "Vehicle must be parked in the marker!", "carscrapping_start_inspection": "Interact with NPC to initiate the salvage inspection.", "carscrapping_npc_is_inspecting": "Please hold. The NPC is inspecting the vehicle.", "carscrapping_inspect_complete": "The NPC has completed the inspection. Collect your payout!", "carscrapping_reward": "Here's your materials! Bring me more cars!", "mission_cash_reward": "You received %s as payment for your service!", "you_declined_bill": "You declined a bill of %s from shop: '%s'.", "you_paid_bill": "You paid a bill of %s from shop: '%s'.", "player_not_afford_bill": "%s could not afford to pay your bill of %s.", "player_declined_bill": "%s has declined to pay your bill of %s.", "player_paid_bill": "%s has paid your bill of %s.", "no_vehicle_in_direction": "There's no vehicle in that direction. Maybe move closer?", "move_closer_to_interact": "Move yourself closer to interact...", "cannot_be_inside_vehicle": "You cannot be inside a vehicle to perform this.", "is_carrying_prop_emote": "You are already carrying an object", "part_has_failed": "The condition of your %s has failured entirely.", "part_malfunction": "The %s in the vehicle is in %s condition!", "repair_station_closed": "Too many mechanics are available (%s online). Please visit a mechanic shop.", "no_vehicle_nearby": "There's no vehicle nearby...", "must_be_near_engine_hood": "You must be standing near the engine/hood of the vehicle!", "repairkit_advanced_not_allowed": "You do not have experience to utilize advanced repair kits.", "repairkit_basic_success": "You repaired your vehicle's engine enough to keep driving.", "repairkit_advanced_success": "You fully repaired your vehicle!", "patchkit_success": "You patched up your vehicle — find a mechanic soon!", "must_be_near_wheel": "You must be standing near a wheel", "is_carrying_component": "You are already carrying a component, complete your task!", "component_not_compat": "The used component is not compatible with this vehicle type.", "component_is_new": "The '%s' part is in perfect condition!", "component_replaced": "You successfully replaced the %s.", "component_salvage": "You salvaged materials: %s", "component_no_vehicle": "You must be inside or near a vehicle!", "component_tasklist_added": "You added '%s' to your task list.", "component_tasklist_removed": "You removed '%s' from your task list.", "is_carrying_body_part": "You are already carrying a body part, complete your task!", "no_body_repairs_required": "The vehicle does not require any %s body repairs!", "body_repair_window_check": "You cannot install a window without its corresponding door in place.", "body_repair_move_closer": "Move closer to the missing body part you want to repair.", "body_inspect_start": "Inspect the vehicle body to generate a body damage report.", "body_inspect_cancelled": "You cancelled the inspection of the vehicle body.", "body_repair_no_damage": "The vehicle body does not need any parts repair", "body_inspection_complete": "The body damage report is now ready!", "body_repair_complete": "Vehicle body-repair has been completed successfully!", "carjack_carrying": "You are already carrying a carjack. Please attach it.", "carjack_vehicle_attached": "The vehicle already has a car jack attached...", "carjack_is_busy": "This car jack is currently being used.", "carjack_fully_raised": "Car jack is already fully raised.", "carjack_fully_lowered": "Car jack is already fully lowered.", "carjack_4_wheels_only": "Car jack only works with 4-wheel vehicles!", "is_diagnosing": "You are already diagnosing/inspecting vehicle. Please complete tasks or cancel."}, "advanced_notification": {"breakdown_title": "Breakdown Response", "breakdown_subtitle": "Tow Required", "breakdown_message": "A local driver needs a tow. Load up the car, have the NPC hop in, and complete the transport job.", "roadsiderepair_title": "Roadside Repair", "roadsiderepair_message": "A driver is stranded due to a vehicle issue. Bring the correct tools and get them back on the road.", "carscrapping_title": "Car Scrapping", "carscrapping_subtitle": "Tow Required", "carscrapping_message": "An abandoned vehicle was reported. Tow it to the scrapyard to complete the job and earn materials."}, "menu_title": {"yes": "Yes", "no": "No", "admin_menu": "Admin <PERSON>", "view_shops": "View Shops", "create_shop": "Create Shop", "manage_shop": "Manage %s [%s]", "set_owner": "Set Owner", "set_account_bal": "Set Account Balance", "marker_management": "Marker Management", "delete_shop": "Delete Shop", "list_for_sale": "List for Sale", "cancel_sale": "Cancel Sale", "remove_current_owner": "Remove Current Owner", "create_marker": "Create Marker", "view_markers": "View Markers", "manage_marker": "Manage Marker: %s", "marker_teleport": "Teleport", "marker_edit": "Edit", "marker_delete": "Delete", "boss_main": "<PERSON>", "boss_account": "Account", "boss_account_balance": "Balance: $%s", "boss_account_deposit": "<PERSON><PERSON><PERSON><PERSON>", "boss_account_withdraw": "Withdraw", "boss_employees": "Employees", "boss_employees_recruit": "Recruit Employee", "boss_employees_view": "View Employees", "boss_employee_manage": "Manage Employee", "boss_employee_remove": "Remove Employee", "boss_employee_update": "Update Job Grade", "storage_main": "Storage", "garage_main": "Gara<PERSON>", "garage_get_vehicle": "Get Vehicle", "garage_store_vehicle": "Store Vehicle", "duty_main": "Duty Menu", "duty_state": "On Duty: %s", "duty_clock_in_out": "Clock In/Out", "workbench_main": "Workbench Menu", "supplier_main": "Supplier Menu", "missions_main": "Mechanic Missions", "missions_cancel": "Cancel Mission", "missions_breakdown": "Breakdown", "missions_roadsiderepair": "Roadside Repair", "missions_carscrapping": "Car Scrapping", "billing_main": "Billing", "billing_create": "Create Bill", "billing_view": "View Bills", "billing_overview": "%s | %s", "billing_respond": "Pay Bill: %s", "billing_note": "Note", "action_main": "Mechanic Action Menu", "action_tow_vehicle": "Tow Vehicle", "action_push_vehicle": "Push Vehicle", "action_flip_vehicle": "Flip Vehicle", "action_unlock_vehicle": "Unlock Vehicle", "action_impound_vehicle": "Impound Vehicle", "repair_station_main": "Repair Station", "repair_station_type_full": "Repair Vehicle", "repair_station_type_engine": "Engine Repair", "repair_station_type_body": "Body Repair", "repair_station_type_core_parts": "Core Parts Repair", "repair_station_type_service_parts": "Service Parts Repair", "diagnostic_main": "Vehicle Diagnostic", "diagnostic_system_overview": "System Overview", "diagnostic_core_parts": "Core Parts", "diagnostic_service_parts": "Service Parts", "diagnostic_workflow": "Start Workflow", "carjack_raise": "Rai<PERSON>", "carjack_lower": "Lower", "service_ok": "Service OK", "service_due": "Service Due", "service_overdue": "Service Overdue", "part_optimal": "Optimal", "part_worn": "<PERSON><PERSON>", "part_critical": "Critical", "part_failed": "Failed", "service_book_main": "Service Book", "service_book_log": "%s @ %s %s"}, "menu_description": {"set_owner": "Assign a new owner - or remove the current owner", "set_account_bal": "Current Balance: $%s", "marker_management": "Manage shop markers - create, edit, teleport or delete", "delete_shop": "Delete the shop from the game & database", "list_for_sale": "List the shop for sale for other players to purchase and operate it", "cancel_sale": "Currently for sale at: %s", "remove_current_owner": "Delete the shop from the game & database", "create_marker": "Create a new marker with a specified marker class", "view_markers": "View created markers and manage them", "marker_teleport": "Teleport to the marker", "marker_edit": "Open the marker creation interface to edit all values, except the marker class", "marker_delete": "Delete this marker permanently?", "boss_employee_remove": "Do you want to fire '%s' from the shop?", "boss_employee_update": "Promote or demote the job grade for '%s'", "missions_cancel": "Cancel the current ongoing mission", "missions_breakdown": "NPC vehicle has a breakdown and the vehicle cannot be repaired at scene. Bring a tow-truck and tow the vehicle to it's destination.", "missions_roadsiderepair": "NPC vehicle may have a dead battery, ran out of fuel or a bursted tire. Bring your tools and make sure the NPC can get going again.", "missions_carscrapping": "Pick up a junk car and drop it at the scrapyard to disassemble the vehicle and receive some materials.", "repair_station_type": "Duration %s seconds", "diagnostic_core_parts": "Conduct a diagnostic check of the core components. Assess their condition and identify any parts that may require immediate replacement due to wear or failure.", "diagnostic_service_parts": "Perform a service check on the vehicle. Review the mileage of service parts and replace any that are due or nearing replacement.", "diagnostic_workflow": "Generate a diagnostics report of the selected parts and begin to replace the following parts: \n\n", "diagnostic_workflow2": "• %s - %s (%s%%)\n", "service_book_log": "Performed at %s"}, "input_title": {"shop_creation": "Shop Creation", "set_account_balance": "Set Account Balance", "marker_creation": "Marker Creation", "marker_edit": "Marker Update", "boss_account": "Account", "workbench_craft": "Craft: %s", "supplier_order": "Order %s (Unit Price: %s)", "supplier_confirmation": "Place Order: %sx of %s for: %s", "supplier_storage": "Select Storage", "create_bill": "Create Bill", "list_for_sale": "List Shop For Sale"}, "input_label": {"shop_name": "Shop Name", "job_name": "Job Name", "job_label": "Job Label", "account_start_balance": "Account Start Balance", "sale_price": "Sale Price", "for_sale": "For Sale?", "blip_sprite": "Blip <PERSON>rite", "blip_color": "Blip Color", "create_blip": "Create Blip?", "account_current_balance": "Current Balance", "account_enter_amount": "Enter Account Amount", "marker_class": "Class", "marker_name": "Name", "marker_type": "DrawMarker Type", "marker_color": "DrawMarker Color", "marker_bobupdown": "Bob Up And Down?", "marker_facecamera": "Face Camera?", "marker_createblip": "Create Blip?", "marker_stash_slots": "Stash Slots", "marker_stash_weight": "Stash Weight", "boss_current_account": "Current Account Balance", "boss_deposit_account": "<PERSON><PERSON><PERSON><PERSON> Amou<PERSON>", "boss_withdraw_account": "Withdraw Amount", "workbench_amount": "Amount", "supplier_quantity": "Quantity", "supplier_confirm": "Confirm", "supplier_storage": "Storage", "bill_player": "Player", "bill_amount": "Amount", "bill_note": "Note", "bill_date": "Date", "bill_time": "Time"}, "input_description": {"shop_name": "A name for the created shop", "job_name": "Use an existing job name or create a new job (example: t1mechanic)", "job_label": "Job label for created job (example: T1GER Mechanic)", "account_start_balance": "Should the shop start with some money?", "sale_price": "A price to purchase the shop", "blip_sprite": "Set Custom Blip Sprite", "blip_color": "Set Custom Blip Color", "account_enter_amount": "Enter desired amount to update the account balance with", "marker_class": "Select a marker class(type) to create", "marker_name": "Set a name for the marker, e.g. T1GER's Workbench.", "marker_type": "Marker type that will be drawn in-game when nearby the marker.", "marker_color": "Set RGBA color of the marker to be displayed.", "marker_stash_slots": "Set maximum number of slots available in the stash.", "marker_stash_weight": "Set maximum weight allowed in the stash.", "workbench_amount": "Enter how many %s you want to craft", "supplier_quantity": "Enter amount of %s parts to order.", "supplier_storage": "Select which storage to receive the ordered parts in.", "bill_player": "Select a player to bill", "bill_note": "Enter details or notes for the bill"}, "menu_metadata": {"not_answered": "N/A", "id": "ID", "owner": "Owner", "owner_id": "Owner ID", "job": "Job", "player_id": "Player ID", "player_name": "Name", "player_identifier": "Identifier", "job_grade": "Job Grade", "vehicle_make": "Make", "vehicle_model": "Model", "vehicle_plate": "Plate", "vehicle_engine": "Engine", "vehicle_body": "Body", "vehicle_fuel": "Fuel", "billing_reference": "Reference", "billing_sender": "Sender", "billing_receiver": "Receiver", "billing_amount": "Amount", "billing_date": "Date", "billing_time": "Time", "component_condition": "Condition", "component_health": "Health", "component_associated": "Associated Part(s)", "component_next_service": "Next Service", "component_since_service": "Since Service", "component_service_interval": "Service Interval", "diagnostic_engine_health": "Engine Health", "diagnostic_body_health": "Body Health", "diagnostic_tank_health": "Tank Health", "diagnostic_fuel_level": "Fuel Level", "diagnostic_dirt_level": "Dirt Level", "diagnostic_oil_level": "Oil Level", "diagnostic_engine_temp": "Engine Temperature", "diagnostic_mileage": "Mileage", "diagnostic_plate": "License Plate"}, "alert": {"header": {"delete_shop": "Delete Shop?", "recruit_employee": "Employee Recruitment Offer"}, "content": {"delete_shop": "Are you sure you want to delete the shop: \n\n %s (%s)?", "recruit_employee": "You have been offered a position as employee in the shop: '%s'.\n\n Will you accept/decline the recruitment offer?"}}, "target": {"carlift_up": "Lift Up", "carlift_down": "Lift Down", "carlift_stop": "Lift Stop", "carlift_delete": "Lift Delete", "missions_ped_interact": "Ped Interact", "roadsiderepair_fuel": "Refuel Vehicle", "roadsiderepair_battery": "Jumpstart Battery", "roadsiderepair_tire": "Replace Tire", "prop_emote_remove": "Remove Prop", "prop_emote_attach": "Carry / Push", "component_repair": "Replace Component", "body_repair": "Install %s", "body_inspect": "Inspect Body", "carjack_remove": "Remove Car Jack", "carjack_handle": "Use Car Jack"}, "progressbar": {"crafting": "Crafting: %sx %s", "mission_reward": "Collecting Payment", "roadsiderepair_fuel": "Refuelling Vehicle", "roadsiderepair_battery": "Jumpstarting Battery", "roadsiderepair_tire": "Replacing Tire", "flip_vehicle": "Flipping Vehicle", "unlock_vehicle": "Unlocking Vehicle", "impound_vehicle": "Impounding Vehicle", "repair_station_type_full": "Repairing Vehicle", "repair_station_type_engine": "Repairing Engine", "repair_station_type_body": "Repairing Body", "repair_station_type_core_parts": "Repairing Core Parts", "repair_station_type_service_parts": "Repairing Service Parts", "repair_kit_basic": "Using Repair Kit", "repair_kit_advanced": "Using Advanced Repair Kit", "patch_kit": "Using Patch Kit", "component_repair": "Replacing Component", "component_diagnose": "Diagnosing Vehicle", "body_repair": "Installing %s", "body_inspect": "Inspecting Vehicle Body"}, "textui": {"duty_marker": "[E] Toggle Duty", "boss_marker": "[E] <PERSON>", "garage_marker": "[E] Garage", "storage_marker": "[E] Storage", "supplier_marker": "[E] Supplier", "workbench_marker": "[E] Workbench", "push_vehicle": "Steer vehicle with [A] and [D]", "repair_station": "[E] Repair Station", "body_inspect": "[E] Inspect Body"}, "drawtext": {"tow_attach": "~g~[G]~s~ Attach Vehicle", "tow_detach": "~r~[G]~s~ Detach vehicle", "push_vehicle": "Hold ~g~[LEFT SHIFT]~s~ to Push Vehicle", "stop_push": "~r~[BACKSPACE]~s~ Stop Push", "flip_vehicle": "~r~[E]~s~ Flip Vehicle", "unlock_vehicle": "~r~[E]~s~ Unlock Vehicle", "impound_vehicle": "~r~[E]~s~ Impound Vehicle"}}