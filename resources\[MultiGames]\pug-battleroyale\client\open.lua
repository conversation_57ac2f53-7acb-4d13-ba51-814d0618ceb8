print'Pug BattleRoyale 3.0.3'
local vr = false
local hat = 0
local that = 0

-- Notification function
function BattleRoyaleNotify(msg, msgType, length)
    if length then
        length = tonumber(length)
    end
	if msg then
		if type(msg) == "string" then
			if Framework == "ESX" then
				FWork.ShowNotification(msg)
			elseif Framework == "QBCore" then
				FWork.Functions.Notify(msg, msgType, length)
			end
		end
	end
end

RegisterNetEvent('Pug:client:BattleRoyaleNotify', function(msg, msgType, length)
    BattleRoyaleNotify(msg, msgType, length)
end)

-- Show DrawText function
function DrawTextOptiopnForSpectate(text)
    if GetResourceState('cd_drawtextui') == 'started' then
        TriggerEvent('cd_drawtextui:ShowUI', 'show', text)
    else
        if Framework == "QBCore" then
            exports[Config.CoreName]:DrawText(text, 'left')
        else
            FWork.TextUI(text, "error")
        end
    end
end

-- Hide DrawText function
function HideTextOptiopnForSpectate()
    if GetResourceState('cd_drawtextui') == 'started' then
        TriggerEvent('cd_drawtextui:HideUI')
    else
        if Framework == "QBCore" then
            exports[Config.CoreName]:KeyPressed()
            Wait(500)
            exports[Config.CoreName]:HideText()
        else
            FWork.HideUI()
        end
    end
end
local GivenKeys = {}
function GiveVehicleKeys(veh, Plate, VehicleSelected)
	if GetResourceState("MrNewbVehicleKeys") == 'started' then
        GivenKeys[Plate] = true
		exports.MrNewbVehicleKeys:GiveKeysByPlate(Plate)
	elseif GetResourceState("qs-vehiclekeys") == 'started' then
		exports['qs-vehiclekeys']:GiveKeys(Plate, VehicleSelected, true)
	elseif GetResourceState("ak47_vehiclekeys") == 'started' then
		exports['ak47_vehiclekeys']:GiveKey(Plate)
	else
		TriggerEvent("vehiclekeys:client:SetOwner", Plate)
	end
end

local function LoadModel(model)
    if HasModelLoaded(model) then return end
    RequestModel(model)
    while not HasModelLoaded(model) do
        Wait(0)
    end
end
local function loadAnimDict(dict)
    while (not HasAnimDictLoaded(dict)) do
        RequestAnimDict(dict)
        Wait(5)
    end
end
local function RemoveVrAnimation()
    loadAnimDict('veh@common@fp_helmet@')
    TaskPlayAnim(PlayerPedId(), 'veh@common@fp_helmet@' ,'take_off_helmet_stand' ,8.0, -8.0, -1, 48, 0, false, false, false)
end
local function EquipVrAnimation()
    loadAnimDict('veh@common@fp_helmet@')
    TaskPlayAnim(PlayerPedId(), 'veh@common@fp_helmet@' ,'put_on_helmet' ,8.0, -8.0, -1, 48, 0, false, false, false)
end
function RoyalMatchHasBegun() -- You can add whatever you like as well for when players have started in the match

     TriggerServerEvent("Pug:server:GiveWeaponBypassBattleRoyale", true) -- Unhash this line and then edit the event export name in the sv_open.lua inside of this event if you are using fiveguard anticheat

    TriggerEvent("Pug:Anticheat:FixRemovedGun")
end
function RoyalMatchHasEnded() -- You can add whatever you like as well for when players have Ended in the match

     TriggerServerEvent("Pug:server:GiveWeaponBypassBattleRoyale") -- Unhash this line and then edit the event export name in the sv_open.lua inside of this event if you are using fiveguard anticheat
    if GetResourceState("MrNewbVehicleKeys") == 'started' then
        for plate in pairs(GivenKeys) do
            exports.MrNewbVehicleKeys:RemoveKeysByPlate(plate)
        end
        GivenKeys = {}
    end
    TriggerEvent("Pug:Anticheat:FixRemovedGun")
    SetEntityHealth(PlayerPedId(), 200.0)
end

function PugAddTargetToEntity(entity, data)
    if Config.Target == "ox_target" then
        exports.ox_target:addLocalEntity(entity, data)
    else
        exports[Config.Target]:AddTargetEntity(entity, {
            options = data,
            distance = data.distance or 2.0
        })
    end
end
function PugCreateMenu(menuId, menuTitle, options)
    if Config.Menu == "ox_lib" then
        local oxOptions = {}
        for _, item in ipairs(options) do
            table.insert(oxOptions, {
                title = item.title,
                description = item.description or "",
                icon = item.icon,
                event = item.event,
                image = item.image,
                iconColor = item.iconColor,
                disabled = item.disabled,
                progress = item.progress,
                args = item.args
            })
        end

        lib.registerContext({
            id = menuId,
            title = menuTitle,
            options = oxOptions
        })
        lib.showContext(menuId)
    else
        local qbOptions = {}
        for _, item in ipairs(options) do
            table.insert(qbOptions, {
                header = item.title,
                txt = item.description or "",
                icon = item.icon,
                image = item.image,
                iconColor = item.iconColor,
                disabled = item.disabled,
                progress = item.progress,
                params = {
                    event = item.event,
                    args = item.args
                }
            })
        end

        exports[Config.Menu]:openMenu(qbOptions)
    end
end
function UnifiedProgressBar(id, label, duration, data, onFinish, onCancel)
    local progressLabel = label or "Progress"
    local progressDuration = duration or 5000

    -- Set default fallbacks if `data` is nil or missing fields
    local disables = data and data.disables or {
        disableMovement = true,
        disableCarMovement = true,
        disableMouse = false,
        disableCombat = true,
    }

    local anim = data and data.anim or {}
    local prop = data and data.prop or {}
    local propTwo = data and data.propTwo or {}

    if Framework == "QBCore" then
        FWork.Functions.Progressbar(id, progressLabel, progressDuration, false, true, disables, anim, prop, propTwo, function()
            ClearPedTasksImmediately(PlayerPedId())
            if onFinish then onFinish() end
        end, function()
            ClearPedTasksImmediately(PlayerPedId())
            if onCancel then onCancel() end
        end)

    elseif GetResourceState("17mov_Hud") == 'started' then
        local action = {
            duration = progressDuration,
            label = progressLabel,
            useWhileDead = false,
            canCancel = true,
            controlDisables = disables,
            animation = anim,
            prop = prop,
            propTwo = propTwo,
        }

        exports["17mov_Hud"]:StartProgress(action, function()
            -- onStart
        end, function()
            -- onTick
        end, function(wasCanceled)
            ClearPedTasksImmediately(PlayerPedId())
            if wasCanceled then
                if onCancel then onCancel() end
            else
                if onFinish then onFinish() end
            end
        end)

    elseif GetResourceState("ox_lib") == 'started' then
        if lib.progressBar({
            duration = progressDuration,
            label = progressLabel,
            useWhileDead = false,
            canCancel = true,
            disable = disables,
            anim = anim,
            prop = prop,
        }) then
            ClearPedTasksImmediately(PlayerPedId())
            if onFinish then onFinish() end
        else
            ClearPedTasksImmediately(PlayerPedId())
            if onCancel then onCancel() end
        end

    else
        -- fallback
        FWork.Progressbar(progressLabel, progressDuration, {
            FreezePlayer = true,
            onFinish = function()
                ClearPedTasksImmediately(PlayerPedId())
                if onFinish then onFinish() end
            end,
            onCancel = function()
                ClearPedTasksImmediately(PlayerPedId())
                if onCancel then onCancel() end
            end
        })
    end
end



-- make a DrawText3D function that draws 3D text on the screen at the given coords
local function DrawText3D(x, y, z, text)
    local onScreen, _x, _y = GetScreenCoordFromWorldCoord(x, y, z)
    local p = GetGameplayCamRelativePitch()
    SetTextScale(0.55, 0.55)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextColour(255, 255, 255, 215)
    SetTextEntry("STRING")
    SetTextCentre(1)
    AddTextComponentString(text)
    DrawText(_x, _y)
end

CreateThread(function()
    if not Config.UseVrHeadSet then
        RoyalePed = Config.RoyalePed
        LoadModel(RoyalePed)
        RoyalePedMan = CreatePed(2, RoyalePed, vector4(Config.RoyalePedLoc.x, Config.RoyalePedLoc.y, Config.RoyalePedLoc.z-1, Config.RoyalePedLoc.w), false, false)
        SetPedFleeAttributes(RoyalePedMan, 0, 0)
        SetPedDiesWhenInjured(RoyalePedMan, false)
        SetPedKeepTask(RoyalePedMan, true)
        SetBlockingOfNonTemporaryEvents(RoyalePedMan, true)
        SetEntityInvincible(RoyalePedMan, true)
        FreezeEntityPosition(RoyalePedMan, true)

        local weaponHash = GetHashKey("WEAPON_CARBINERIFLE")
        GiveWeaponToPed(RoyalePedMan, weaponHash, 0, false, true) -- 250 ammo, equipped in hand
        SetCurrentPedWeapon(RoyalePedMan, weaponHash, true)

        if GetResourceState('pug-paintball') == 'started' then
            BattleRoyaleBlip = AddBlipForCoord(Config.RoyalePedLoc.x, Config.RoyalePedLoc.y, Config.RoyalePedLoc.z)
            SetBlipDisplay(BattleRoyaleBlip, 4)
            SetBlipAsShortRange(BattleRoyaleBlip, true)
            BeginTextCommandSetBlipName("STRING")
            SetBlipSprite(BattleRoyaleBlip, Config.IconBlip.Icon)
            SetBlipScale(BattleRoyaleBlip, Config.IconBlip.Size)
            SetBlipColour(BattleRoyaleBlip, Config.IconBlip.Color)
            AddTextComponentString("Arcade")
            EndTextCommandSetBlipName(BattleRoyaleBlip)
        else
            Config.IconBlip = {
                Icon = 94,
                Color = 83,
                Size = 0.7,
            }
            BattleRoyaleBlip = AddBlipForCoord(Config.RoyalePedLoc.x, Config.RoyalePedLoc.y, Config.RoyalePedLoc.z)
            SetBlipDisplay(BattleRoyaleBlip, 4)
            SetBlipAsShortRange(BattleRoyaleBlip, true)
            BeginTextCommandSetBlipName("STRING")
            SetBlipSprite(BattleRoyaleBlip, Config.IconBlip.Icon)
            SetBlipScale(BattleRoyaleBlip, Config.IconBlip.Size)
            SetBlipColour(BattleRoyaleBlip, Config.IconBlip.Color)
            AddTextComponentString("Arcade")
            EndTextCommandSetBlipName(BattleRoyaleBlip)
        end

        RequestAnimDict("rcmjosh4")
        while not HasAnimDictLoaded("rcmjosh4") do Wait(0) end
        TaskPlayAnim(RoyalePedMan, "rcmjosh4", "josh_leadout_cop2", 8.0, -8.0, -1, 2, 1.0, false, false, false)

        
        PugAddTargetToEntity(RoyalePedMan, {
            {
                name = 'BattleRoyalePed',
                event = 'Pug:client:ViewBattleRoyale',
                icon = 'fas fa-clipboard',
                label = "Play Battle Royale",
            }
        })
    
        while true do
            Wait(0)
            local playerPed = PlayerPedId()
            local playerCoords = GetEntityCoords(playerPed)
            local distance = #(playerCoords - vector3(Config.RoyalePedLoc.x, Config.RoyalePedLoc.y, Config.RoyalePedLoc.z))
            if distance < 10.0 then
                DrawText3D(Config.RoyalePedLoc.x, Config.RoyalePedLoc.y, Config.RoyalePedLoc.z+1, "~g~BATTLE ROYALE")
            else
                Wait(1000)
            end
        end
    end
end)

CreateThread(function()
    while GetResourceState('int_arcade') ~= 'started' do Wait(1000) print("^2Drag and drop the 'int_arcade' folder from inside of the pug-battleroyale/[ARCADE-MLO] folder into your resources folder and make sure that it is ensured") end
end)

local function EquipArmorAnim()
    loadAnimDict("clothingshirt")        
    TaskPlayAnim(PlayerPedId(), "clothingshirt", "try_shirt_positive_d", 8.0, 1.0, -1, 49, 0, 0, 0, 0)
end

local function Armorjoint(Bool)
    if ClosedRoyaleInfo().ingame then
        if not UseArmor then
            UseArmor = true
        else
            return
        end
        if not Bool then
            PugSoundPlay("puttingarmoron", 0.05)
        end
        local count = 20
        while count > 0 do
            Wait(350)
            count = count - 1
            if not Bool then
                if not IsEntityPlayingAnim(PlayerPedId(), 'clothingshirt', 'try_shirt_positive_d', 3) then
                    EquipArmorAnim()
                end
            end
            SetPedArmour(PlayerPedId(), GetPedArmour(PlayerPedId()) + 5)
            TriggerServerEvent("Pug:server:SyncMyArmorBR", 5)
            if GetPedArmour(PlayerPedId()) == 100 then
                PugStopPlay()
                count = 0
            end
        end
        UseArmor = false
    end
end
local function loadAnimDict(dict)
    while (not HasAnimDictLoaded(dict)) do
        RequestAnimDict(dict)
        Wait(5)
    end
end

local function AdrenEffect()
    local startStamina = 8
    SetRunSprintMultiplierForPlayer(PlayerId(), 1.29)
    while startStamina > 0 do 
        Wait(1000)
        if math.random(5, 100) < 10 then
            RestorePlayerStamina(PlayerId(), 1.0)
        end
        startStamina = startStamina - 1
        if math.random(5, 100) < 51 then
            RestorePlayerStamina(PlayerId(), 0.9)
        end
    end
    startStamina = 0
    SetRunSprintMultiplierForPlayer(PlayerId(), 1.0)
end
RegisterNetEvent("Pug:client:RoyaleHealBoost", function()
    local healing = false
    if not healing then
        healing = true
    else
        return
    end
    
    local count = 60
    while count > 0 do
        Wait(500)
        count = count - 1
        SetEntityHealth(PlayerPedId(), GetEntityHealth(PlayerPedId()) + 25) 
    end
    healing = false
end)
RegisterNetEvent("Pug:Royale:ReloadSkin")
AddEventHandler("Pug:Royale:ReloadSkin", function()
	for k, v in pairs(GetGamePool('CObject')) do
		if IsEntityAttachedToEntity(PlayerPedId(), v) then
			SetEntityAsMissionEntity(v, true, true)
			DeleteObject(v)
			DeleteEntity(v)
		end
	end
	TriggerEvent("Pug:ReloadGuns:sling")
end)
RegisterNetEvent('Pug:client:GiveArmor', function()
    EquipArmorAnim()
    Armorjoint()
    TriggerServerEvent("hospital:server:SetArmor", GetPedArmour(PlayerPedId()))
    ClearPedTasks(PlayerPedId())
end)


-- JUICE 
local healing = false
RegisterNetEvent('Pug:client:UseRoyaleJuice',function()
    if ClosedRoyaleInfo().ingame then
        local cup = GetHashKey("prop_food_bs_juice01")
        RequestModel(cup)
        while not HasModelLoaded(cup) do
            Wait(1)
        end
        local coords = GetEntityCoords(PlayerPedId())
        cupObject = CreateObject(cup, coords.x, coords.y, coords.z, 1, 1, 0)
        local bone1 = GetPedBoneIndex(PlayerPedId(), 28422)
        AttachEntityToEntity(cupObject, PlayerPedId(), bone1, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0, 0, 2, 1)
        PugSoundPlay("slurpdrink", 0.05)
        UnifiedProgressBar("use-juice", "Slurping...", 4000, {
            disables = {
                disableMovement = false,
                disableCarMovement = false,
                disableMouse = false,
                disableCombat = true,
            },
            anim = {
                animDict = "amb@world_human_drinking@coffee@male@idle_a",
                anim = "idle_c",
                flags = 49,
            }
        }, function() -- Done
            TriggerEvent("Pug:Royale:ReloadSkin")
            TriggerEvent("Pug:Royale:UseHealthJuice")
            Armorjoint(true)
            AdrenEffect()
        end, function() -- Cancel
            ClearPedTasks(PlayerPedId())
            BattleRoyaleNotify("Canceled", "error")
        end)
        
    else
        BattleRoyaleNotify(Translations.error.in_game, "error")
    end
end)
RegisterNetEvent('Pug:Royale:UseHealthJuice', function()
    if not healing then
        healing = true
    else
        return
    end
    TriggerEvent("Pug:Client:StopBleeding")
    local count = 7
    while count > 0 do
        Wait(2000)
        count = count - 1
        SetEntityHealth(PlayerPedId(), GetEntityHealth(PlayerPedId()) + 10) 
    end
    healing = false
end)
-- JUMP SHOES 
local SuperJump = false
RegisterNetEvent('Pug:client:UseJumpShoes',function()
    if ClosedRoyaleInfo().ingame then
        loadAnimDict("random@domestic")
        TaskPlayAnim(PlayerPedId(), 'random@domestic' ,'pickup_low' ,8.0, -8.0, 10000, 16, 0, false, false, false)
        SuperJump = true
        Wait(500)
        TriggerEvent("Pug:client:UseJumpShoesBreakEvent")
        ClearPedTasks(PlayerPedId())
        BattleRoyaleNotify(Translations.success.super_jump, "success")
        TriggerEvent("Pug:client:RoyaleHealBoost")
        while SuperJump do
            Wait(0)
            if SuperJump then
                SetSuperJumpThisFrame(PlayerId())
            else
                break
            end
        end
    else
        BattleRoyaleNotify(Translations.error.in_game, "error")
    end
end)
RegisterNetEvent('Pug:client:UseJumpShoesBreakEvent',function()
    Wait(60000)
    SuperJump = false
end)
-- UAV 
RegisterNetEvent('Pug:client:UseUAV',function()
    if ClosedRoyaleInfo().ingame then
        loadAnimDict("amb@code_human_in_bus_passenger_idles@female@tablet@idle_a")
        TaskPlayAnim(PlayerPedId(), 'amb@code_human_in_bus_passenger_idles@female@tablet@idle_a' ,'idle_a' ,8.0, -8.0, 10000, 16, 0, false, false, false)
        local Tablet = CreateObject(GetHashKey("prop_cs_tablet"), GetEntityCoords(PlayerPedId()))
        local bone = GetPedBoneIndex(PlayerPedId(), 28422)
        AttachEntityToEntity(Tablet, PlayerPedId(), bone, -0.05, 0.0, 0.0, 0.0, 0.0, 0.0, 1, 1, 0, 0, 2, 1)
        Wait(2000)
        TriggerServerEvent("Pug:server:EnemyUAVEffectForAll")
        Wait(1000)
        ClearPedTasks(PlayerPedId())
        TriggerEvent("Pug:Royale:ReloadSkin")
    else
        BattleRoyaleNotify(Translations.error.in_game, "error")
    end
end)
RegisterNetEvent('Pug:client:AcivateUav',function(coords,id)
    local uavblip = {}
    local alpha = 250
    uavblip[id] = AddBlipForRadius(coords.x,coords.y,coords.z, 7.0)
    SetBlipSprite(uavblip[id],9)
    SetBlipColour(uavblip[id],49)
    SetBlipAlpha(uavblip[id],alpha)
    SetBlipAsShortRange(uavblip[id], false)

    while alpha ~= 0 do
        Wait(100)
        alpha = alpha - 5
        SetBlipAlpha(uavblip[id], alpha)
        if alpha == 0 then
            RemoveBlip(uavblip[id])
            uavblip[id] = nil
            return
        end
    end
end)
RegisterNetEvent('Pug-VrHeadSet:toggle', function()
    local player = PlayerPedId()
	local ped = GetPlayerPed(-1)
    if IsPedModel(PlayerPedId(), 'mp_f_freemode_01') then
        if not vr then
            hat = GetPedPropIndex(PlayerPedId(),0) -- hat?
            that = GetPedPropTextureIndex(PlayerPedId(), 0)
            vr = true
            EquipVrAnimation()
            Wait(1500)
            StopAnimTask(PlayerPedId(), "veh@common@fp_helmet@", "take_off_helmet_stand", 8.0, 1.0, -1, 49, 0, 0, 0, 0)
            SetPedPropIndex(ped, 0, Config.VrHeadSetPropFemale, 0, 0)
            TaskStartScenarioInPlace(player, "PROP_HUMAN_PARKING_METER", 0, true)
        elseif vr then
            ClearPedTasks(player)
            vr = false
            RemoveVrAnimation()
            Wait(800)
            StopAnimTask(PlayerPedId(), "veh@common@fp_helmet@", "take_off_helmet_stand", 8.0, 1.0, -1, 49, 0, 0, 0, 0)
            if hat == -1 then
                ClearPedProp(PlayerPedId(), 0)
            else
                SetPedPropIndex(PlayerPedId(), 0, hat, that, 0)
            end
        end
    else
        if not vr then
            hat = GetPedPropIndex(PlayerPedId(),0) -- hat?
            that = GetPedPropTextureIndex(PlayerPedId(), 0)
            vr = true
            EquipVrAnimation()
            Wait(1500)
            StopAnimTask(PlayerPedId(), "veh@common@fp_helmet@", "take_off_helmet_stand", 8.0, 1.0, -1, 49, 0, 0, 0, 0)
            SetPedPropIndex(ped, 0, Config.VrHeadSetPropMale, 0, 0)
            TaskStartScenarioInPlace(player, "PROP_HUMAN_PARKING_METER", 0, true)
        elseif vr then
            ClearPedTasks(player)
            vr = false
            RemoveVrAnimation()
            Wait(800)
            StopAnimTask(PlayerPedId(), "veh@common@fp_helmet@", "take_off_helmet_stand", 8.0, 1.0, -1, 49, 0, 0, 0, 0)
            if hat == -1 then
                ClearPedProp(PlayerPedId(), 0)
            else
                SetPedPropIndex(PlayerPedId(), 0, hat, that, 0)
            end
        end
    end
end)

local IslandPropsToRemove = {
	-1574151574,
	1215477734,
	-1697935936,
    -1439869581,
}

CreateThread(function()
   while true do
        if IsInBattleRoyale() then
            for i=1, #IslandPropsToRemove do
                local coords = GetEntityCoords(PlayerPedId(), false)
                local Gates = GetClosestObjectOfType(coords.x, coords.y, coords.z, 100.0, IslandPropsToRemove[i], 0, 0, 0)
                if IslandPropsToRemove ~= 0 then
                    SetEntityAsMissionEntity(Gates, 1, 1)
                    DeleteObject(Gates)
                    SetEntityAsNoLongerNeeded(Gates)
                end
            end
        end
	   Wait(2500)
   end
end)

function CheckingIfDeadFunction() -- NEVER CHANGE THIS UNLESS I HAVE DIRECTED YOU TO.
    return IsPedFatallyInjured(PlayerPedId())
end

RegisterNetEvent("Pug:client:BattleRoyaleReviveEvent", function()
    TriggerEvent('ak47_qb_ambulancejob:revive')
    TriggerEvent('ak47_qb_ambulancejob:skellyfix')
    TriggerEvent('hospital:client:Revive')
    TriggerEvent('esx_ambulancejob:revive')
    TriggerEvent('wasabi_ambulance:revive')
    TriggerEvent('visn_are:resetHealthBuffer')
end)