-- (I WOULD NOT REALLY CHANGE ANYTHING IN HERE)
Config.GulagTime = 30 -- How many seconds the gulag last until it goes into overtime and shows the flag while players are in the 1v1
Config.GulagCheckTime = 30 -- (in seconds) Time the loop runs for checking to put players into a 1v1 gulag match
Config.GulagPlayersMin = 4 -- Minimum amount of players required for people to be able to enter into the gulag when dying (CANNOT MAKE IT LESS THEN 4)
Config.TimeToTakeFlag = 1 -- (in seconds) time it takes to grab the flag in the gulag
----------
Config.FlagModel = 'prop_golfflag' -- Flag model that is use in the gulag (make this nil if you dont want to have a flag for players to get)
Config.RandomGulagWeapon = { -- Random weapon that players get in there gulag fight. (both players get the same weapon)
    "weapon_microsmg",
    "weapon_smg",
    "weapon_carbinerifle",
    "weapon_pumpshotgun",
    "weapon_advancedrifle",
    "weapon_heavypistol",
}
----------
-- This is the random spawn locations players can spawn to when being put in the gulag spectating area
Config.GulagSpawnsSpectate = {
    vector4(2800.35, -3914.87, 154.9, 357.75),
    vector4(2800.46, -3910.84, 152.01, 358.53),
    vector4(2804.71, -3910.63, 152.01, 0.98),
    vector4(2808.25, -3910.86, 152.01, 349.41),
    vector4(2811.53, -3910.84, 152.01, 356.56),
    vector4(2816.17, -3910.62, 152.01, 0.43),
    vector4(2818.48, -3901.23, 146.01, 347.57),
    vector4(2815.68, -3901.64, 146.01, 337.68),
    vector4(2812.33, -3901.26, 146.01, 25.31),
    vector4(2805.14, -3901.56, 146.01, 4.08),
}
----------
-- Gulag RandomMaps that can randomly be selected each battle royale match that is played
Config.GulagMap = {
    "Set_Dystopian_02", -- Jurassic Park
    "Set_Dystopian_03", -- Wrecking Ball
    "Set_Dystopian_04", -- scrap yard
    "Set_Dystopian_07", -- Ship Wreck
    "Set_Dystopian_09", -- Industrial Whore House
    "Set_Dystopian_10", -- Scrap Yard 2
}
----------
-- This is the gulag flag and player vs player spawns for each map.
Config.MapLocation = {
    ['Set_Dystopian_02'] = { 
        ['FlagSpawn'] = vector3(2793.11, -3796.54, 140.15),
        [1] = vector3(2937.17, -3802.91, 144.25), -- Player 1 Spawn
        [2] = vector3(2664.6, -3793.08, 143.71), -- Player 2 Spawn
    },
    ['Set_Dystopian_03'] = { 
        ['FlagSpawn'] = vector3(2800.2, -3796.79, 137.98),
        [1] = vector3(2945.65, -3796.44, 143.26), -- Player 1 Spawn
        [2] = vector3(2658.59, -3797.45, 143.58), -- Player 2 Spawn
    },
    ['Set_Dystopian_04'] = { 
        ['FlagSpawn'] = vector3(2780.02, -3788.88, 140.03),
        [1] = vector3(2945.2, -3791.37, 140.03), -- Player 1 Spawn
        [2] = vector3(2655.69, -3804.56, 140.03), -- Player 2 Spawn
    },
    ['Set_Dystopian_07'] = { 
        ['FlagSpawn'] = vector3(2802.17, -3806.3, 135.94),
        [1] = vector3(2946.17, -3797.53, 140.1), -- Player 1 Spawn
        [2] = vector3(2650.22, -3778.95, 140.14), -- Player 2 Spawn
    },
    ['Set_Dystopian_09'] = { 
        ['FlagSpawn'] = vector3(2828.07, -3799.42, 128.97),
        [1] = vector3(2947.03, -3800.03, 139.63), -- Player 1 Spawn
        [2] = vector3(2650.93, -3799.97, 139.68), -- Player 2 Spawn
    },
    ['Set_Dystopian_10'] = { 
        ['FlagSpawn'] = vector3(2812.87, -3791.98, 147.39),
        [1] = vector3(2948.42, -3790.24, 139.96), -- Player 1 Spawn
        [2] = vector3(2642.46, -3798.63, 139.97), -- Player 2 Spawn
    },
}
----------
-- THESE ARE INVISIBLE FENCES THAT SPAWN TO LOCK THE PLAYER IN THE GULAG STADIUM
Config.Gulag = {
    [1] = {-- front
        prop = 'prop_facgate_03b_l',
        name = 'fence1',
        vec = vector4(2820.01, -3909.51, 152.01, 359.74),
    },
    [2] = {-- front first + 4.75 -2.19
        prop = 'prop_facgate_03b_l',
        name = 'fence2',
        vec = vector4(2820.12, -3906.23, 149.4, 357.35),
    },
    [3] = {-- front first + 4.75 -2.19
        prop = 'prop_facgate_03b_l',
        name = 'fence13',
        vec = vector4(2820.02, -3901.77, 146.01, 4.17),
    },
    [4] = {-- front first + 4.75 -2.19
        prop = 'prop_facgate_03b_l',
        name = 'fence14',
        vec = vector4(2819.68, -3900.24, 146.01, 270.71),
    },
    [5] = {-- front first + 4.75 -2.19
        prop = 'prop_facgate_03b_l',
        name = 'fence15',
        vec = vector4(2813.98, -3900.31, 146.01, 270.31),
    },
    [6] = {-- front first + 4.75 -2.19
        prop = 'prop_facgate_03b_l',
        name = 'fence16',
        vec = vector4(2808.26, -3900.43, 146.01, 270.4),
    },
    [7] = {-- front first + 4.75 -2.19
        prop = 'prop_facgate_03b_l',
        name = 'fence17',
        vec = vector4(2802.55, -3900.38, 146.01, 270.28),
    },
    [8] = {-- right side -2.24 -4.84
        prop = 'prop_facgate_03b_l',
        name = 'fence8',
        vec = vector4(2798.27, -3909.88, 152.01, 1.54),
    },
    [9] = {-- right side -2.24 -4.84
        prop = 'prop_facgate_03b_l',
        name = 'fence9',
        vec = vector4(2798.24, -3905.81, 149.06, 2.97),
    },
    [10] = {-- right side -2.24 -4.84
        prop = 'prop_facgate_03b_l',
        name = 'fence10',
        vec = vector4(2798.26, -3902.19, 146.01, 3.37),
    },
    [11] = {-- right side + 4.75 -2.19
        prop = 'prop_facgate_03b_l',
        name = 'fence311',
        vec = vector4(2820.0, -3917.03, 156.63, 1.26), 
    },
    [12] = {-- right side + 4.75 -2.19
        prop = 'prop_facgate_03b_l',
        name = 'fence12',
        vec = vector4(2820.02, -3912.56, 153.06, 1.17),
    },
    [13] = {-- right side + 4.75 -2.19
        prop = 'prop_facgate_03b_l',
        name = 'fence13',
        vec = vector4(2798.27, -3917.42, 156.95, 1.91), 
    },
    [14] = {-- right side + 4.75 -2.19
        prop = 'prop_facgate_03b_l',
        name = 'fence14',
        vec = vector4(2798.19, -3912.93, 153.36, 0.93),
    },
    [15] = {-- right side + 4.75 -2.19
        prop = 'prop_facgate_03b_l',
        name = 'fence15',
        vec = vector4(2819.58, -3916.87, 156.51, 269.83),
    },
    [16] = {-- right side + 4.75 -2.19
        prop = 'prop_facgate_03b_l',
        name = 'fence16',
        vec = vector4(2813.73, -3916.73, 156.4, 269.66),
    },
    [17] = {-- right side + 4.75 -2.19
        prop = 'prop_facgate_03b_l',
        name = 'fence17',
        vec = vector4(2808.16, -3916.73, 156.4, 270.11),
    },
    [18] = {-- left side -2.24 -4.84
        prop = 'prop_facgate_03b_l',
        name = 'fence18',
        vec = vector4(2802.45, -3916.63, 156.31, 270.29),
    },
}