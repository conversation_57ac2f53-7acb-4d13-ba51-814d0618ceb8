<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfo__InitDataList>
  <residentTxd>vehshare</residentTxd>
  <residentAnims />
  <InitDatas>
	    <Item>
      <modelName>pd450f</modelName>
      <txdName>pd450f</txdName>
      <handlingId>pd450f</handlingId>
      <gameName>pd450f</gameName>
      <vehicleMakeName>700</vehicleMakeName>
      <expressionDictName>null</expressionDictName>
      <expressionName>null</expressionName>
      <animConvRoofDictName>null</animConvRoofDictName>
      <animConvRoofName>null</animConvRoofName>
      <animConvRoofWindowsAffected />
      <ptfxAssetName>null</ptfxAssetName>
      <audioNameHash>lgcyc00qbike700</audioNameHash>
      <layout>LAYOUT_BIKE_QUAD_VERUS</layout>
      <coverBoundOffsets>BLAZER_COVER_OFFSET_INFO</coverBoundOffsets>
	  <POVTuningInfo>BLAZER_POV_TUNING</POVTuningInfo>
      <explosionInfo>EXPLOSION_INFO_DEFAULT</explosionInfo>
      <scenarioLayout />
      <cameraName>FOLLOW_UPRIGHT_BIKE_CAMERA</cameraName>
      <aimCameraName>BIKE_AIM_CAMERA</aimCameraName>
      <bonnetCameraName>BIKE_BLAZER_POV_CAMERA</bonnetCameraName>
      <povCameraName>BIKE_BLAZER_POV_CAMERA</povCameraName>
      <FirstPersonDriveByIKOffset x="0.000000" y="-0.060000" z="0.000000" />
      <FirstPersonDriveByUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByIKOffset x="0.000000" y="-0.060000" z="0.000000" />
	  <FirstPersonProjectileDriveByPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByRearLeftIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonProjectileDriveByRearRightIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByLeftPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightRearPassengerIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByLeftPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonDriveByRightPassengerUnarmedIKOffset x="0.000000" y="0.000000" z="0.000000" />
	  <FirstPersonMobilePhoneOffset x="0.155000" y="0.315000" z="0.520000" />
      <FirstPersonPassengerMobilePhoneOffset x="0.136000" y="0.223000" z="0.425000" />
      <PovCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovCameraVerticalAdjustmentForRollCage value="0.000000" />
      <PovPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <PovRearPassengerCameraOffset x="0.000000" y="0.000000" z="0.000000" />
      <vfxInfoName>VFXVEHICLEINFO_QUAD_GENERIC</vfxInfoName>
      <shouldUseCinematicViewMode value="true" />
      <shouldCameraTransitionOnClimbUpDown value="false" />
      <shouldCameraIgnoreExiting value="false" />
      <AllowPretendOccupants value="false" />
      <AllowJoyriding value="true" />
      <AllowSundayDriving value="false" />
      <AllowBodyColorMapping value="true" />
      <wheelScale value="0.13" />
      <wheelScaleRear value="0.13" />
      <dirtLevelMin value="0.000000" />
      <dirtLevelMax value="0.850000" />
      <envEffScaleMin value="0.000000" />
      <envEffScaleMax value="1.000000" />
      <envEffScaleMin2 value="0.000000" />
      <envEffScaleMax2 value="1.000000" />
      <damageMapScale value="0.000000" />
      <damageOffsetScale value="1.000000" />
      <diffuseTint value="0x00FFFFFF" />
      <steerWheelMult value="0.300000" />
      <HDTextureDist value="5.000000" />
      <lodDistances content="float_array">
        350.000000
        350.000000
        350.000000
        350.000000
        500.000000
        500.000000
      </lodDistances>
      <minSeatHeight value="10" />
      <identicalModelSpawnDistance value="20" />
      <maxNumOfSameColor value="10" />
      <defaultBodyHealth value="1000.000000" />
      <pretendOccupantsScale value="1.000000" />
      <visibleSpawnDistScale value="1.000000" />
      <trackerPathWidth value="2.000000" />
      <weaponForceMult value="0" />
      <frequency value="10" />
      <swankness>SWANKNESS_1</swankness>
      <maxNum value="1" />
      <flags>FLAG_HAS_LIVERY FLAG_EXTRAS_STRONG FLAG_AVERAGE_CAR FLAG_IS_OFFROAD_VEHICLE FLAG_HEADLIGHTS_USE_ACTUAL_BONE_POS FLAG_DONT_TIMESLICE_WHEELS</flags>
      <type>VEHICLE_TYPE_QUADBIKE</type>
      <plateType>VPT_BACK_PLATES</plateType>
      <vehicleClass>VC_OFF_ROAD</vehicleClass>
      <wheelType>VWT_OFFROAD</wheelType>
      <trailers />
      <additionalTrailers />
      <drivers />
      <extraIncludes />
      <doorsWithCollisionWhenClosed />
      <driveableDoors />
      <bumpersNeedToCollideWithMap value="false" />
      <needsRopeTexture value="false" />
      <requiredExtras />
      <rewards />
      <cinematicPartCamera>
        <Item>WHEEL_FRONT_RIGHT_CAMERA</Item>
        <Item>WHEEL_FRONT_LEFT_CAMERA</Item>
        <Item>WHEEL_REAR_RIGHT_CAMERA</Item>
        <Item>WHEEL_REAR_LEFT_CAMERA</Item>
      </cinematicPartCamera>
      <NmBraceOverrideSet />
      <buoyancySphereOffset x="0.000000" y="0.000000" z="0.000000" />
      <buoyancySphereSizeScale value="1.000000" />
      <pOverrideRagdollThreshold type="NULL" />
	  <firstPersonDrivebyData>
        <Item>BIKE_BLAZER3_FRONT</Item>
        <Item>BIKE_BATI_REAR</Item>
      </firstPersonDrivebyData>
    </Item>
  </InitDatas>

  <txdRelationships>
	<Item>
      <parent>vehicles_biker_shared</parent>
      <child>pd450f</child>
    </Item>
  </txdRelationships>
</CVehicleModelInfo__InitDataList>