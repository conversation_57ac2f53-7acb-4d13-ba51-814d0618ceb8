if not Config.ActionMenu.Enable then 
    return 
end

--- Command to open the mechanic action menu
if Config.ActionMenu.Command.enable then
    RegisterCommand(Config.ActionMenu.Command.name, function()
        MechanicActionMenu()
    end)
end

--- keybind to access the mechanic action menu
if Config.ActionMenu.Keybind.enable then
    RegisterCommand('MechanicActionMenu', function()
        MechanicActionMenu()
    end, false)
    RegisterKeyMapping('MechanicActionMenu', Config.ActionMenu.Keybind.description, 'keyboard', Config.ActionMenu.Keybind.defaultMapping)
end

---Mechanic Action Menu
function MechanicActionMenu()
    if not Config.ActionMenu.Enable then return end

    local isMechanic, shopId = IsPlayerMechanic()
    if not isMechanic then return end

    if Config.Debug then
        print("Hired in mechanic shop with id: "..shopId)
    end

    local menuOptions = {}

    if Config.Shop.Billing.enable and Config.Shop.Billing.actionMenu.enable then 
        menuOptions[#menuOptions+1] = {title = locale("menu_title.billing_main"), icon = Config.Shop.Billing.actionMenu.icon, arrow = true, onSelect = function() BillingMain(true) end}
    end

    if Config.Missions.Enable and Config.Missions.ActionMenu.enable then 
        menuOptions[#menuOptions+1] = {title = locale("menu_title.missions_main"), icon = Config.Missions.ActionMenu.icon, arrow = true, onSelect = function() MissionsMainMenu(true) end}
    end
    
    if Config.FlatbedTowing.Enable and Config.FlatbedTowing.ActionMenu.enable then
        menuOptions[#menuOptions+1] = {title = locale("menu_title.action_tow_vehicle"), icon = Config.FlatbedTowing.ActionMenu.icon, onSelect = function() TowVehicle() end}
    end
    
    if Config.PushVehicle.Enable and Config.PushVehicle.ActionMenu.enable then
        menuOptions[#menuOptions+1] = {title = locale("menu_title.action_push_vehicle"), icon = Config.PushVehicle.ActionMenu.icon, onSelect = function() PushVehicle() end}
    end
    
    if Config.FlipVehicle.Enable and Config.FlipVehicle.ActionMenu.enable then
        menuOptions[#menuOptions+1] = {title = locale("menu_title.action_flip_vehicle"), icon = Config.FlipVehicle.ActionMenu.icon, onSelect = function() FlipVehicle() end}
    end
    
    if Config.UnlockVehicle.Enable and Config.UnlockVehicle.ActionMenu.enable then
        menuOptions[#menuOptions+1] = {title = locale("menu_title.action_unlock_vehicle"), icon = Config.UnlockVehicle.ActionMenu.icon, onSelect = function() UnlockVehicle() end}
    end
    
    if Config.ImpoundVehicle.Enable and Config.ImpoundVehicle.ActionMenu.enable then
        menuOptions[#menuOptions+1] = {title = locale("menu_title.action_impound_vehicle"), icon = Config.ImpoundVehicle.ActionMenu.icon, onSelect = function() ImpoundVehicle() end}
    end

    -- return if no menu options
    if #menuOptions <= 0 then 
        return 
    end

    -- register context
    lib.registerContext({
        id = "mechanic_action_menu",
        title = locale("menu_title.action_main"),
        options = menuOptions
    })
    
    -- show context
    lib.showContext("mechanic_action_menu")
end